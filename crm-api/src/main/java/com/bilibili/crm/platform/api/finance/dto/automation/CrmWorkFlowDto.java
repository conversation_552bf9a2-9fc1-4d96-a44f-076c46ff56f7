package com.bilibili.crm.platform.api.finance.dto.automation;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 工作流信息
 *
 * <AUTHOR>
 * @date 2021/1/25 2:26 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrmWorkFlowDto implements Serializable {

    private static final long serialVersionUID = 7742766536963553450L;
    private Integer id;

    /**
     * 上级流程的 id，子流程才有存在
     */
    private Integer pid;

    /**
     * 关联的业务 id
     */
    private Integer refId;

    /**
     * 类型：1-回款流水流程，2-认款流程，3-代打款证明流程 {@link com.bilibili.crm.platform.biz.common.WorkFlowTypeEnum}
     */
    private Integer type;

    /**
     * 状态，由业务流程自己定义
     */
    private Integer status;

    /**
     * 申请人
     */
    private String applicant_name;

    private Timestamp applicantTime;

    /**
     * 审批人
     */
    private String approve_name;

    private Timestamp approveTime;

}
