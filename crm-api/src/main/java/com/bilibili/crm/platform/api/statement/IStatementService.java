package com.bilibili.crm.platform.api.statement;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import com.bilibili.crm.platform.api.statement.dto.*;
import com.bilibili.crm.platform.soa.dto.statement.CrmStatementDetailBatchSaveDto;

import java.util.List;

/**
 * 结算单 soa 服务
 */
public interface IStatementService {

    /**
     * 创建结算单
     * 要求：这些结算单明细必须是同一个账号下的，并且是这个账号下的全量的
     *
     * @param statementDetailBatchSaveDto
     */
    Integer createStatement(CrmStatementDetailBatchSaveDto statementDetailBatchSaveDto);

    Integer createSparkStatement(CrmStatementDetailBatchSaveDto statementDetailBatchSaveDto);

    Integer createCluePassStatement(CrmStatementDetailBatchSaveDto statementDetailBatchSaveDto);

    /**
     * 结算单列表分页查询
     *
     * @param currentPage
     * @param size
     * @param statementQueryDto
     * @return
     */
    PageResult<StatementDto> queryList(Integer currentPage, Integer size, StatementQueryDto statementQueryDto);


    /**
     * 结算单列表全量查询
     *
     * @param statementQueryDto
     * @return
     */
    List<StatementDto> queryList(StatementQueryDto statementQueryDto);

    /**
     * 获取结算单信息
     *
     * @param id
     * @return
     */
    StatementDto queryStatementInfo(Integer id);

    /**
     * 确认结算单
     *
     * @return
     */
    Integer confirmStatement(StatementConfirmSaveDto confirmSaveDto);

    /**
     * 审核结算单
     *
     * @param statementAuditDto
     * @return
     */
    Integer auditStatement(StatementAuditDto statementAuditDto);

    /**
     * 获取结算单模板
     *
     * @return
     */
    AttachmentResultDto queryStatementTemplate();

    /**
     * 获取结算单明细结果信息
     *
     * @param queryDto
     * @return
     */
    StatementDetailResultDto queryStatementDetailResult(StatementDetailQueryDto queryDto);

    /**
     * 获取结算单明细结果信息并上传
     *
     * @param queryDto
     * @return
     */
    StatementDetailResultDto queryStatementDetailResultThenUpload(StatementDetailQueryDto queryDto);

    /**
     * 保存快递单号
     *
     * @param statementExpressSaveDto
     * @return
     */
    Integer saveExpress(StatementExpressSaveDto statementExpressSaveDto);

    /**
     * excel 批量上传快递单号
     *
     * @param returnApplySaveDto
     * @return
     */
    Integer batchUploadExpress(StatementExpressBatchSaveExcelDto returnApplySaveDto);

    /**
     * 查询快递单号
     *
     * @param statementId
     * @return
     */
    StatementExpressDto queryExpressInfo(Integer statementId);
}
