package com.bilibili.crm.platform.api.customer.dto.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 17:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerReportFlowValidateDto {

    /**
     * 校验通过报备IDs
     */
    private List<Long> validReportIds;

    /**
     * 校验未通过信息
     */
    private List<InvalidateInfo> invalidReportInfos;

    @Data
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class InvalidateInfo {
        /**
         * 报备ID
         */
        private Long reportId;

        /**
         * 校验未通过类型
         */
        private Integer invalidType;

        /**
         * 提示信息
         */
        private String tips;
        /**
         * 提示详情
         */
        private String tipsDetail;
    }
}
