package com.bilibili.crm.platform.api.achievement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2019/10/11
 * 业绩指标实体类
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchievementQuotaDto {

    // 总金额（daily_package_amount）
    private BigDecimal totalAmount;

    // 已确认金额(is_checking = 1)
    private BigDecimal checkedAmount;

    // 未确认金额(is_checking = 0)
    private BigDecimal unCheckedAmount;
}
