package com.bilibili.crm.platform.api.contract.dto;

import lombok.Data;

import java.io.Serializable;

@Data
public class BillManualSplitDetailDto implements Serializable {

    private static final long serialVersionUID = 4222773123701218270L;

    private Integer billId;

    private Integer orderId;

    private String orderName;

    private Long billAmount;

    private Integer directSaleId;

    private String directSaleName;

    private Integer channelSaleId;

    private String channelSaleName;
}
