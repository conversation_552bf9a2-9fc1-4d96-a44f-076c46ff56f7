package com.bilibili.crm.platform.api.account.dto;

import com.bilibili.adp.common.bean.Operator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountLabelBaseDto implements Serializable {

    private static final long serialVersionUID = 3243266293741997401L;
    /**
     * 标签id
     */
    private Integer id;

    /**
     * 标签名称
     */
    private String labelName;

    /**
     * 标签用途
     */
    private String labelPurpose;

    /**
     * 业务方联系人
     */
    private String contacts;

    /**
     * 软删除 0 否 1是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 规则模型
     */
    private AccountLabelRuleDto accountLabelRuleDto;

    /**
     * 操作人
     */
    private Operator operator;

    /**
     * 标签更新模式 1对所有存量账号进行更新,不区分自动/手动打标 2不对手动打标账号进行更新,手动打标账号仍保留该标签,其余账号根据新规则更新
     */
    private Integer updateMappingMode;

    private Integer totalAccount;
}
