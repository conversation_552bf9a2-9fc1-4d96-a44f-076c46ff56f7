package com.bilibili.crm.platform.api.persona.service;

import com.bilibili.crm.platform.api.consume.dto.PersonaExportBean;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.api.persona.dto.QueryPersona;

import java.util.List;

public interface IPersonaReportService {
    /**
     * 导出明细
     * @param queryPersona
     * @return
     * @throws IncomeConditionInvalidException
     */
    List<PersonaExportBean> getPersonaReport(QueryPersona queryPersona) throws IncomeConditionInvalidException;
}
