/**
 * bilibili.com Inc.
 * Copyright (c) 2004-2021 All Rights Reserved.
 */
package com.bilibili.crm.platform.api.customer.dto;

import java.io.Serializable;
import java.util.List;

import org.apache.commons.lang.builder.ToStringBuilder;
import org.apache.commons.lang.builder.ToStringStyle;

/**
 * Customer industry qualification domain model.
 * 
 * 这个模型方便对客户端提交过来的多个资质进行分组，避免一堆资质还要stream => map的再分类操作。
 * 
 * <AUTHOR>
 * @version $Id: CustomerIndustryQualificationDto.java, v 0.1 2021-11-12 3:34 PM <PERSON> Exp $$
 */
public class CustomerIndustryQualificationDto implements Serializable {

    private static final long                                serialVersionUID = 7346198461065740303L;

    /** 二级行业id - 高亮显示(虽然下面字段里也有) */
    private Integer                                          commerceCategorySecondId;

    /** 原来挂靠在dto下的所有additional qualification表里客户提交的资质 */
    private List<CustomerAdditionalIndustryQualificationDto> additionalQualifications;

    /**
     * Constructor for reflect.
     */
    public CustomerIndustryQualificationDto() {
    }

    /**
     * Constructor with arguments.
     * 
     * @param commerceCategorySecondId 
     * @param additionalQualifications
     */
    public CustomerIndustryQualificationDto(Integer commerceCategorySecondId,
                                            List<CustomerAdditionalIndustryQualificationDto> additionalQualifications) {
        this.commerceCategorySecondId = commerceCategorySecondId;
        this.additionalQualifications = additionalQualifications;
    }

    /**
     * Getter method for property commerceCategorySecondId.
     *
     * @return property value of commerceCategorySecondId
     */
    public Integer getCommerceCategorySecondId() {
        return commerceCategorySecondId;
    }

    /**
     * Setter method for property commerceCategorySecondId.
     *
     * @param commerceCategorySecondId value to be assigned to property commerceCategorySecondId
     */
    public void setCommerceCategorySecondId(Integer commerceCategorySecondId) {
        this.commerceCategorySecondId = commerceCategorySecondId;
    }

    /**
     * Getter method for property additionalQualifications.
     *
     * @return property value of additionalQualifications
     */
    public List<CustomerAdditionalIndustryQualificationDto> getAdditionalQualifications() {
        return additionalQualifications;
    }

    /**
     * Setter method for property additionalQualifications.
     *
     * @param additionalQualifications value to be assigned to property additionalQualifications
     */
    public void setAdditionalQualifications(List<CustomerAdditionalIndustryQualificationDto> additionalQualifications) {
        this.additionalQualifications = additionalQualifications;
    }

    @Override
    public String toString() {
        return ToStringBuilder.reflectionToString(this, ToStringStyle.SHORT_PREFIX_STYLE);
    }

}