package com.bilibili.crm.platform.api.contract.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.platform.api.contract.dto.*;
import com.bilibili.crm.platform.api.policy.dto.ContractBindPolicyFlowDto;
import com.bilibili.crm.platform.api.rbac.dto.DataStrategy;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/11/8
 * 合同查询类
 **/
public interface IContractQueryService {

    // 查询合同基本对象
    ContractBaseDto getContractBaseDto(Integer contractId);

    List<Integer> querySettleContractIdsFromReadDb(List<Integer> contractIdsWhiteList);

    // 获取合同摘要信息
    ContractSummaryDto getContractSummary(Integer contractId);

    List<ContractDto> queryContractList(QueryContractParam queryContractParam);

    Map<Integer, List<SaleDto>> getContractSaleDtosMap(List<Integer> crmContractIds);

    DataStrategy getDataStrategy(Operator operator);

    List<ContractBaseDto> getContractBaseDtoByQueryParam(QueryContractParam queryParam);

    Map<Integer, ContractDto> queryContractMapInIds(List<Integer> crmContractIds);

    PageResult<ContractDto> queryContractPageResult(QueryContractParam queryContractParam, Integer page, Integer size);

    List<Integer> getContractIdsByAllAuthSale(List<Integer> saleIds);

    List<Integer> getContractIdsBySaleIds(List<Integer> saleIds);

    List<Integer> getContractIdsByBizSaleIds(List<Integer> saleIds);

    List<Integer> getContractIdsBySubstituteSaleIds(List<Integer> saleIds);

    List<Integer> getHasBindNormalSaleContractIds();

    // 支出项专用，仅实现了部分查询条件（其他业务勿用）
    List<ContractBaseDto> queryContractBaseListOptimization(QueryContractParam queryContractParam);

    //only部门权限控制
    DataStrategy getDataStrategyByDepartment(Operator operator);

    //根据部门查询合同id
    List<Integer> getContractIdsByDepartmentIds(List<Integer> depIds);

    List<Integer> getContractIdsThroughExtend(QueryContractParam queryParam);

    List<Integer> getContractIdByDepartmentIdExcludePersonalFly(List<Integer> departmentIds);

    /**
     * 合同绑定的政策流程
     *
     * @param contractId
     * @return
     */
    List<ContractBindPolicyFlowDto> queryPolicyListByContractId(Integer contractId);

    /**
     * 每天18：00查询符合条件的合同，然后发送邮件
     */
    void queryContractAndSendMail();

    Map<Integer, List<CrmContractBillSaleMappingDTO>> queryContractBillSaleMapByCondition(QueryContractBillSaleParam query);

    List<CrmContractBillSaleMappingDTO> queryContractBillSaleListByCondition(QueryContractBillSaleParam query);
}
