package com.bilibili.crm.platform.api.wallet.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.platform.api.pickup.dto.CluePassQueryDto;
import com.bilibili.crm.platform.api.pickup.dto.CrmLaunchTradeRecordDto;
import com.bilibili.crm.platform.soa.dto.statement.CrmStatementDetailBatchSaveDto;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * @author: brady
 * @time: 2022/10/8 3:34 下午
 */
public interface IXstLaunchIncomeQueryService {
    List<CrmStatementDetailBatchSaveDto> queryXstStatementList(Timestamp statementMonth);

    PageResult<CrmLaunchTradeRecordDto> queryCluePassRealConsumePage(CluePassQueryDto queryDto);

    List<CrmLaunchTradeRecordDto> queryCluePassRealConsume(CluePassQueryDto queryDto);

    void addMasAccountInfo(Long id, Integer cluePassAccountId);

    void refreshSaleInfo(Timestamp start, Timestamp end);

    List<Long> queryBySaleId(List<Integer> saleIds);

    Map<Long, List<Integer>> querySaleByIds(List<Long> recordIds);
}
