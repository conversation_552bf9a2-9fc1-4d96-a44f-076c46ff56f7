package com.bilibili.crm.platform.api.income.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.EnumSet;

@AllArgsConstructor
public enum  ResourceIncomeComposition {
    INFORMATION_FLOW(1, "信息流"),
    PLAY_PAGE(2, "播放页推荐位"),

    RESOURCE_HARD_OTHER(0, "其他硬广"),
    RESOURCE_HARD_TOTAL(7,"全部硬广"),

    RESOURCE_INVITE(10,"招商资源"),
    RESOURCE_INVITE_TOTAL(11,"全部资源提供方"),
    RESOURCE_INVITE_OTHER(12,"其他资源提供方"),

    PICK_UP_CD(21,"商单(超电)"),
    PICK_UP_NON_CD(22,"商单(非超电)"),
    NON_STANDARD_INVITE(23,"商单(邀约)"),

    //硬广-广告团队
    INFORMATION_FLOW_AD(31,"信息流"),
    PLAY_PAGE_AD(33,"播放页推荐位"),
    SSA(5, "移动-闪屏"),
    HOME_FOCUS(6, "移动-首焦"),
    TOP_VIEW(43, "TopView"),
    HARD_OTHER_AD(34,"其他硬广"),
    DYNAMIC_INFO_FLOW(46,"动态信息流"),
    UP_CO_OPTED(47,"up主互选"),
    HARD_TOTAL_AD(35,"全部硬广"),

    //硬广-主商团队
    INFORMATION_FLOW_BSI(36,"信息流"),
    PLAY_PAGE_BSI(37,"播放页推荐位"),
    HARD_OTHER_BSI(38,"其他硬广"),
    HARD_TOTAL_BSI(39,"全部硬广"),


    //广告团队-直播商单
    UP_AD_LIVE_PICK_UP(40,"直播商单"),
    UP_TOTAL_AD(41,"UP主 广告团队全部"),

    //主商团队-主商商单
    UP_BSI_PICK_UP(42,"主商商单"),
    UP_TOTAL_BSI(44,"UP主 主商团队全部"),
    UP_BSI_INVITE(45,"邀约"),

    UP_TOTAL(24,"全部商单"),
    ;



    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static ResourceIncomeComposition getByCode(int code) {
        for (ResourceIncomeComposition bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }

    /**
     * 是否其他
     * @param type
     * @return
     */
    public static boolean isOther(ResourceIncomeComposition type) {
        return EnumSet.of(RESOURCE_HARD_OTHER, RESOURCE_INVITE_OTHER).contains(type);
    }
}
