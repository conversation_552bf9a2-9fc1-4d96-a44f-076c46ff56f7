package com.bilibili.crm.platform.api.statusmachine.customer.enums.report;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 新客报备操作类型
 *
 * <AUTHOR>
 * @date 2023/2/20 14:08
 */
@AllArgsConstructor
public enum NewCustomerReportOperationEnum {
    /**
     * 新客报备操作类型
     */
    CREATE_NEW_REPORT(1, "创建报备申请", "%s创建了当前报备申请"),
    UPDATE_REPORT(2, "编辑报备申请", "%s编辑了当前报备申请"),
    SUBMIT_REPORT(3, "提交报备申请", "%s提交了当前报备申请"),
    REJECT_REPORT(4, "驳回申请", "%s驳回了当前报备申请，驳回原因：%s"),
    AUDIT_PASS(5, "审批通过", "%s通过了当前报备申请"),
    ABANDON(6, "作废", "%s作废了当前报备申请"),
    REPORT_VALID(7, "报备生效", "当前报备申请生效"),
    REPORT_AUTO_INVALID(8, "自动失效", "当前报备30天无跟进,自动失效"),
    REPORT_AUTO_INVALID_2(8, "自动失效", "当前报备90天未开户,自动失效"),
    REPORT_SET_INVALID(9, "设为失效", "%s将当前报备申请设置为失效"),
    REPORT_HAVE_EXIST_CUSTOMER_SET_INVALID(9, "设为失效", "检测到当前报备已开户设置为失效"),
    DELETE_REPORT(10, "删除", "%s将当前报备申请删除"),
    REPORT_SUBMIT_CHECK_FILE(11, "提交判断材料", "%s提交判断材料"),

    ;
    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    @Getter
    private final String tpl;

    public static NewCustomerReportOperationEnum getByCode(Integer code) {
        for (NewCustomerReportOperationEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }

        return null;
    }
}
