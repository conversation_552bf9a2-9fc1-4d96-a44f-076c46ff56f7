package com.bilibili.crm.platform.api.policy.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: brady
 * @time: 2025/1/24 16:13
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyFlowReviewAdoptActionReq {

    @ApiModelProperty("流程实例 id(必传)")
    private String process_instance_id;

    @ApiModelProperty("评论内容(核价意见)")
    private String content;

    @ApiModelProperty("项目拆分list")
    private List<ProjectSplitView> project_splits;

}
