package com.bilibili.crm.platform.api.settle;

import com.bilibili.crm.platform.api.enums.CheckingStateEnum;
import com.bilibili.crm.platform.api.finance.enums.RebateRadixTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/10/18
 * 账单详细信息
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillDetailReport implements Serializable {

    private static final long serialVersionUID = 457828911755844782L;

    // 账单id
    private Integer billId;

    /**
     * 计收日期
     */
    private Timestamp receiptDate;

    /**
     * 账单日期 / 执行日期
     */
    private Timestamp billDate;

    // 订单信息

    // 订单ID
    private Integer orderId;

    // 订单名称
    private String orderName;

    // 订单创建时间
    private Timestamp orderCreateTime;

    private Timestamp orderStartTime;

    private Timestamp orderEndTime;


    // 订单类型
    private Integer orderType;

    // 订单类型
    private String orderTypeDesc;

    // 订单状态
    private Integer orderStatus;

    // 订单状态
    private String orderStatusDesc;

    // 订单业务状态
    private Integer orderBusStatus;

    //
    private String orderBusStatusDesc;

    // 审核状态
    private Integer orderAuditStatus;

    private String orderAuditStatusDesc;

    // 订单资源类型
    private Integer orderResourceType;

    // 订单资源类型
    private String orderResourceTypeDesc;

    // 订单金额
    private Long orderAmount;

    // 订单折扣
    private BigDecimal discount;

    // 非标一级产品
    private String firstCategoryProduct;

    // 非标二级产品
    private String secondCategoryProduct;

    private Integer firstCategoryProductId;

    // 非标二级产品
    private Integer secondCategoryProductId;

    // 项目/节目类型
    private String projectItemName;

    /**
     * 主站一级分区
     */
    private String mainCategoryStr;

    /**
     * 业绩归属方
     */
    private String achieveBelongTo;

    /**
     * 项目立项方
     */
    private String projectDepartmentName;

    // 资源提供方(订单中的资源提供方)
    private String departmentName;

    // 资源提供方2(订单中的资源提供方2)
    private String secondDepartmentName;

    // 合同信息

    // 合同id
    private Integer contractId;

    // 合同号
    private Long contractNumber;

    // 合同名称
    private String contractName;

    // 合同创建时间
    private Timestamp contractCreateTime;

    // 合同开始时间
    private Timestamp beginTime;

    // 合同结束时间
    private Timestamp endTime;

    // 合同类型
    private Integer contractType;

    // 合同类型
    private String contractTypeDesc;

    // 合同状态
    private Integer contractStatus;

    // 合同状态
    private String contractStatusDesc;

    //合同业务状态
    private String contractBusStatusDesc;

    //合同审核状态
    private String contractAuditStatusDesc;

    // 合同打包价
    private Long contractAmount;

    // 合同实收金额
    private BigDecimal contractActualAmount;

    // 项目名称
    private String projectName;

    // 存档状态
    private Integer archiveStatus;

    // 存档状态
    private String archiveStatusDesc;

    // 集团名称
    private String groupName;

    private Integer groupId;

    //代理商集团ID
    private Integer agentGroupId;

    //代理商集团名称
    private String agentGroupName;

    // 产品线名称
    private String productLineName;

    // 产品名称
    private String productName;

    private Integer productId;

    // 客户信息

    // 客户ID
    private Integer accountId;

    // 客户名称
    private String customerName;

    // 客户名称
    private String companyName;

    private Integer firstCategoryId;

    private Integer secondCategoryId;

    // 一级行业分类
    private String firstCategoryName;

    // 二级行业分类
    private String secondCategoryName;

    /**
     * 业务行业一级分类id
     */
    private String bizIndustryCategoryFirstName;

    /**
     * 业务行业二级分类id
     */
    private String bizIndustryCategorySecondName;

    // 代理商id
    private Integer agentId;

    /**
     * 商机代理ID
     */
    private Integer optAgentId;

    /**
     * 商机代理名称
     */
    private String optAgentName;

    // 代理商账号id
    private Integer agentAccountId;

    // 代理商名称
    private String agentName;

    private Integer agentCustomerId;

    private String agentCustomerName;

    private String agentCustomerCategory;

    /**
     * 商单所属任务Id
     */
    private Long pickUpOrderSubTaskNo;

    private Integer pickUpOrderSubTaskId;

    /**
     * 商单所属任务名称
     */
    private String pickUpOrderSubTaskName;

    /**
     * 花火订单编号
     */
    private Long pickupOrderNo;

    /**
     * 花火订单ID
     */
    private Integer pickupId;

    /**
     * 花火订单状态
     */
    private String pickupOrderStatusDesc;

    /**
     * 所属合作类型
     */
    private String cooperationTypeDesc;

    /**
     * 花火订单创建日期
     */
    private String pickupCreateDate;

    /**
     * 视频期望上线时间
     */
    private String onlineDate;

    /**
     * 视频实际上线时间
     */
    private String actualOnlineDate;

    /**
     * 花火订单完成时间
     */
    private String completeDate;

    /**
     * 退款时间
     */
    private String refundDate;
    /**
     * 稿件avId
     */
    private Long avid;

    /**
     * up主昵称
     */
    private String upName;

    // 销售信息

    // 销售id
    private List<Integer> saleId;

    // 销售员
    private String saleName;

    private String directSalesName;

    private String channelSalesName;


    private String straightNameGroup;

    private String straightNameSecondGroup;

    private String channelNameGroup;

    private String channelNameSecondGroup;

    // 合同创建人
    private String creator;

    // 项目执行
    private String executeName;

    // 记收信息

    // 拆分打包价
    private BigDecimal dailyPackageAmount;

    // 是否已关帐
    private Integer isClosed;

    // 是否记收
    private Integer isChecking;

    // 是否记收
    private String isCheckingDesc;

    // 关账月份
    private Timestamp closingDate;

    //合同客户所属部门
    private String accountDepartmentName;

    // 预估金额
    private BigDecimal forecastAmount;

    // 抹零金额
    private BigDecimal removeFractionAmount;

    //实结金额
    private BigDecimal actualAmount;

    private RebateRadixTypeEnum radixType;

    //账单来源
    private Integer billSource;
    /**
     * 账单实结类型 1系统 0人工
     */
    private Integer billCloseType;

    /**
     * 主站账号id
     */
    private Long mid;

    /**
     * up主昵称
     */
    private String bilibiliUserName;


    //公司组
    private String companyGroupName;

    private Integer customerId;

    //客户id
    private Integer accountCustomerId;

    //客户名称
    private String accountCustomerName;

    /**
     * 客户种类 0-个人 1-机构
     */
    private Integer accountCustomerCategory;

    private BigDecimal packageAmount;

    private BigDecimal pdAmount;

    private BigDecimal totalAmount;

    private BigDecimal recordAmount;

    /**
     * 未录入金额总计
     */
    private BigDecimal unRecordAmount;

    /**
     * 商单未录入金额 负数转为0
     */
    private BigDecimal pickUpUnRecordAmount;
    /**
     * 商单未录入金额 实际计算值可为负数
     */
    @Deprecated
    private BigDecimal pickUpUnRecordAmountReal;
    /**
     * 品牌未录入金额
     */
    private BigDecimal brandUnRecordAmount;

    private Timestamp periodStart;

    private Timestamp periodEnd;

    /**
     * up主总支出项
     */
    private BigDecimal upTotalOutcome;

    /**
     * 信息技术费
     */
    private BigDecimal informationTechFee;

    /**
     * 信息技术费比例
     */
    private BigDecimal informationTechFeeProp;

    private CheckingStateEnum checkingStateEnum;
    /**
     * 订单一级产品类型
     */
    private Integer orderFirstCategoryId;
    /**
     * 站外电商标签
     */
    private Long out_task_cm;
    /**
     * 站外电商标签
     */
    private String out_task_cm_name;
    /**
     * 站外ID
     */
    private String out_task_no;
    /**
     * 稿件tag
     */
    private String tag;
    /**
     * bvid
     */
    private String bvid;

    /**
     * 业绩销售是否是手动修改
     */
    private Boolean isDirectBizSaleManual;
    private Boolean isChannelBizSaleManual;

    /**
     * 新客完成时间:商单完成且有上线时间的，取视频上线时间；没有上线时间的，取完成时间
     */
    private String newCustomerCompleteTime;

    private String accountUnitedFirstIndustryName;

    private String accountUnitedSecondIndustryName;

    private String accountUnitedThirdIndustryName;

    private String customerUnitedFirstIndustryName;

    private String customerUnitedSecondIndustryName;

    private String customerUnitedThirdIndustryName;
}
