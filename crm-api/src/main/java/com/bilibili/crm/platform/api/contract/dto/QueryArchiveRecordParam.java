package com.bilibili.crm.platform.api.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryArchiveRecordParam implements Serializable {

    private List<Integer> contractIds;
    private Integer status;

}
