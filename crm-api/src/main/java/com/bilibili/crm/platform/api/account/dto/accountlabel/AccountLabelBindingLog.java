package com.bilibili.crm.platform.api.account.dto.accountlabel;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountLabelBindingLog {

    /**
     * 新自动打标的标签ids
     */
    private List<Integer> newAutoLabelIds;

    /**
     * 旧自动打标的标签ids
     */
    private List<Integer> oldAutoLabelIds;

    /**
     * 新手动打标的标签ids
     */
    private List<Integer> newManualLabelIds;

    /**
     * 旧手动打标的标签ids
     */
    private List<Integer> oldManualLabelIds;
}
