package com.bilibili.crm.platform.api.account.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum PickupAccountStatusType {
    /**
     * 花火自动开户状态
     */
    NOT_AUDIT(0, "未审核"),

    TO_BE_AUDIT(1, "待审核"),

    APPROVE(2, "审核通过"),

    REFUSE(3, "驳回"),

    EXPIRED(4, "已过期"),

    WILLEXPIRED(5, "即将过期"),

    APPROVE_FOR_SUPPLEMENT(6, "审核通过（资质待补充）")

    ;



    @Getter
    private final Integer code;
    @Getter
    private final String desc;

    public static String getByCode(Integer code) {
        for (PickupAccountStatusType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean.desc;
            }
        }
        return "";
    }
}
