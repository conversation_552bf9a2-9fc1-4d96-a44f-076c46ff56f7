/**
 * <AUTHOR>
 * @date 2018年9月14日
 */

package com.bilibili.crm.platform.soa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.account.dto.SimpleAccountDto;
import com.bilibili.crm.platform.api.company.dto.*;

import java.util.List;
import java.util.Map;

/**
 * 集团/产品线/产品 接口
 */
public interface ISoaCompanyGroupService {

    List<Integer> getAccountIdsByCompanyGroupName(String name);

    List<Integer> getAccountIdsInCompanyGroupIds(List<Integer> cgIds);

    /**
     * 根据账户 ids 获取公司组 map
     *
     * @param accountIds
     * @return
     */
    Map<Integer, CompanyGroupBaseDto> queryCompanyGroupMapByAccountIds(List<Integer> accountIds);

    /**
     * 查询公司组账户的 mapping 关系
     *
     * @param queryCompanyParam
     * @return
     */
    List<CompanyAccountMappingDto> queryCompanyAccountMappingList(QueryCompanyParam queryCompanyParam);

    /**
     * 批量查询公司组列表
     *
     * @param companyGroupIds
     * @return
     */
    List<CompanyGroupDto> queryCompanyGroupList(List<Integer> companyGroupIds);

    /**
     * 批量查询公司组 Map
     *
     * @param companyGroupIds
     * @return
     */
    Map<Integer, CompanyGroupDto> queryCompanyGroupMap(List<Integer> companyGroupIds);


    /**
     * 根据集团名模糊查询集团
     *
     * @param groupNameLike
     * @return
     */
    List<CorporationGroupDto> queryCorporationGroupByLikeName(String groupNameLike);

    /**
     * 根据产品名称和集团 id 模糊查询产品
     *
     * @param groupId
     * @param productLineNameLike
     * @return
     */
    List<ProductLineDto> queryProductLineByLikeName(Integer groupId, String productLineNameLike);

    /**
     * 根据产品名称和产品线 id 模糊查询产品
     *
     * @param productLineId
     * @param productNameLike
     * @return
     */
    List<ProductDto> queryProductByLikeName(Integer productLineId, String productNameLike);

    /**
     * 获取集团信息
     *
     * @param groupId
     * @return
     */
    CorporationGroupDto getGroupsById(Integer groupId);

    /**
     * 获取产品线信息
     *
     * @param productLineId
     * @return
     */
    ProductLineDto getProductLineById(Integer productLineId);

    /**
     * 获取产品信息
     *
     * @param productId
     * @return
     */
    ProductDto getProductById(Integer productId);

    /**
     * 批量获取产品信息
     *
     * @param productIds
     * @return
     */
    Map<Integer, ProductDto> queryProductInIds(List<Integer> productIds);

    Map<Integer, CorporationGroupDto> queryGroupsInIds(List<Integer> groupIds);

    // ================================= 以下接口目前提供给 DMP SOA 使用的 =================================

    /**
     * 创建产品
     * 要求：name必须唯一
     *
     * @param productName
     * @param operator
     * @return 创建的产品 id
     */
    Integer createProduct(String productName, Operator operator);

    /**
     * 修改产品名称
     * 要求：name必须唯一
     *
     * @param originProductName
     * @param newProductName
     * @param operator          操作人
     * @return
     */
    Integer updateProductName(String originProductName, String newProductName, Operator operator);

    /**
     * 删除产品
     *
     * @param originProductName
     * @param operator          操作人
     * @return
     */
    Integer deleteProduct(String originProductName, Operator operator);

    /**
     * 启用产品
     *
     * @param productName
     * @param operator
     * @return
     */
    Integer enableProduct(String productName, Operator operator);

    /**
     * 禁用产品
     *
     * @param productName
     * @param operator
     * @return
     */
    Integer disableProduct(String productName, Operator operator);

    /**
     * 产品名称模糊查询
     *
     * @param productIds
     * @param nameLike
     * @param size
     * @return
     */
    List<ProductDto> queryProductByNameLike(List<Integer> productIds, String nameLike, Integer size);

    /**
     * 产品名称模糊查询
     * @param nameLike
     * @return
     */
    List<ProductDto> queryProductByNameLike(String nameLike);


    /**
     * 查询集团账户列表
     *
     * @return
     */
    List<SimpleAccountDto> querySameGroupAndBrandAccounts(CorpAccountQueryDto queryDto);
}
