package com.bilibili.crm.platform.api.external_trading.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/6/19 11:13
 * 帐号钱包对外交易结果对象
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AccountWalletTradingResultBean implements Serializable {

    private static final long serialVersionUID = -4632876691875295557L;
    /**
     * 交易流水号
     */
    private String serialNumber;

    /**
     * 系统业务标识: systemType + businessId
     */
    private String systemBusinessId;

    /**
     * 交易类型，见枚举类: WalletTradingOperateType
     */
    private Integer operateType;

    /**
     * 帐号ID
     */
    private Integer accountId;

    /**
     * 金额（单位：分）
     */
    private BigDecimal amount;

    /**
     * 交易创建时间
     */
    private Timestamp ctime;
}
