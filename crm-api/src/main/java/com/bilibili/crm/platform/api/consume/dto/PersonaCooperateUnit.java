package com.bilibili.crm.platform.api.consume.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * @author: brady
 * @time: 2021/3/31 4:00 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonaCooperateUnit {
    private PersonaCooperateSummary summary;
    private Map<Integer, List<PersonaCooperateTrendDate>> trendMap;
    private Map<Integer, PersonaCooperateProduct> productMap  ;

    public static PersonaCooperateUnit init(){
        return PersonaCooperateUnit.builder()
                .summary(PersonaCooperateSummary.init())
                .trendMap(Collections.emptyMap())
                .productMap(Collections.emptyMap())
                .build();
    }
}
