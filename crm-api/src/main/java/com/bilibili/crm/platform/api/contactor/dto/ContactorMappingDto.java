package com.bilibili.crm.platform.api.contactor.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 */
@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ContactorMappingDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 联系人id
     */
    private Long contactorId;

    /**
     * 联系人来源 0 广告主 1 代理商
     */
    private ContactorSourceEnum contactorSource;

    /**
     * 需要关联联系人的业务id
     */
    private Long bizId;

    /**
     * 需要关联联系人的业务类型 0 商机 1 跟进记录
     */
    private ContactorMappingBizTypeEnum bizType;

    private Integer version;
}
