package com.bilibili.crm.platform.api.return_online.dto;

import com.bilibili.crm.platform.api.return_online.enums.RelationOperatorEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 行业保护规则
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class IndustryProtection implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 行业分类
     */
    private List<Integer> secondIndustryIds;

    /**
     * 值类型
     *
     * @see RelationOperatorEnum
     */
    private Integer operatorCode;

    /**
     * 环比达标公式
     */
    private String express;

    /**
     * 环比达标比例
     */
    private List<BigDecimal> chainRatioAmounts;

    /**
     * 档位描述
     */
    private String gearDesc;

    /**
     * 日均规则明细
     */
    private List<RelationDetailItem> details;
}
