package com.bilibili.crm.platform.api.bsiopportunity.dto;

import java.io.Serializable;

public class BsiOpportunityAttachmentDto implements Serializable {
    private static final long serialVersionUID = 1978176325484836722L;
    /**
     * 文件名
     */
    private String fileName;

    /**
     * 文件oss key
     */
    private String ossKey;

    public String url;

    public BsiOpportunityAttachmentDto() {

    }

    public BsiOpportunityAttachmentDto(String fileName, String ossKey) {
        super();
        this.fileName = fileName;
        this.ossKey = ossKey;
    }

    public String getFileName() {
        return fileName;
    }

    public void setFileName(String fileName) {
        this.fileName = fileName;
    }

    public String getOssKey() {
        return ossKey;
    }

    public void setOssKey(String ossKey) {
        this.ossKey = ossKey;
    }

    public String getUrl() {
        return url;
    }

    public void setUrl(String url) {
        this.url = url;
    }
}
