package com.bilibili.crm.platform.api.customer.dto.sea;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/30 14:12
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryCustomerSeaDto {

    /**
     * 客户公海ID
     */
    private Long id;

    /**
     * 客户公海IDs
     */
    private List<Long> ids;

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 客户IDs
     */
    private List<Integer> customerIds;

    /**
     * 是否代理商
     */
    private Integer isAgent;

    /**
     * 是否内部用户
     */
    private Integer isInner;

    /**
     * 客户类型
     */
    private Integer customerCategory;

    /**
     * 公海状态
     */
    private Integer seaState;

    /**
     * 公海状态
     */
    private List<Integer> seaStates;

    /**
     * 公海进入时间 开始
     */
    private Timestamp enterSeaStartTime;

    /**
     * 公海进入时间 结束
     */
    private Timestamp enterSeaEndTime;

    /**
     * 业务一级行业ID
     */
    private List<Integer> bizIndustryFirstIds;

    /**
     * 业务二级行业ID
     */
    private List<Integer> bizIndustrySecondIds;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;

    private List<Integer> unitedFirstIndustryIds;

    private List<Integer> unitedSecondIndustryIds;

    private List<Integer> unitedThirdIndustryIds;

}
