package com.bilibili.crm.platform.api.policy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: brady
 * @time: 2021/4/30 2:32 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyRelatedSubjectDto {
    /**
     * "政策关联主体类型 1 账号 2 客户 3 集团"
     */
    private Integer policyRelatedType;

    /**
     * 政策关联主体清单
     */
    private List<Integer> policyRelatedIds;

    public static PolicyRelatedSubjectDto init() {
        return PolicyRelatedSubjectDto.builder()
                .build();
    }
}
