package com.bilibili.crm.platform.api.company.dto;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProductLineDto implements Serializable {
	private static final long serialVersionUID = -2530430277650664785L;
	/**
	 * 产品线id
	 */
	private Integer id;

	/**
	 * 集团id
	 */
	private Integer groupId;

	/**
	 * 状态
	 */
	private Integer status;

	/**
	 * 产品线名称
	 */
	private String name;

	/**
	 * 产品线标签
	 */
	private String tag;

	/**
	 * 软删除0未删除1 删除
	 */
	private Integer isDeleted;

	/**
	 * 添加时间
	 */
	private Timestamp ctime;

	/**
	 * 更新时间
	 */
	private Timestamp mtime;

	/**
	 * 创建人
	 */
	private String createUser;

	/**
	 * 更新人
	 */
	private String updateUser;

	/**
	 * 公私海名单
	 */
	private Map<SaleSeaProductCategory, Map<BiliUserType, List<Integer>>> productSaleMap;
}
