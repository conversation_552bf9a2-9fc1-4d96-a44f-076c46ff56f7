package com.bilibili.crm.platform.soa.dto.wxapp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-02-07 20:48:15
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerTopIncomeDto implements Serializable {

    /**
     * 日期
     */
    private Timestamp date;


    /**
     * 品牌名称
     */
    private String productName;

    /**
     * 品牌id
     */
    private Integer productId;

    /**
     * 季度累计收入
     */
    private BigDecimal quarterIncome;

    /**
     * 季度累计收入环比
     */
    private BigDecimal quarterIncomeRatio;

    /**
     * 季度累计收入同比
     */
    private BigDecimal quarterIncomeYoyRatio;

    /**
     * 近30日收入趋势
     */
    private List<BigDecimal> historyIncome;

    /**
     * 昨日收入
     */
    private BigDecimal income;

    /**
     * 日环比
     */
    private BigDecimal dayCompareRate;

    /**
     * 上周日收入同比
     */
    private BigDecimal weekIncomeYoyRatio;
}
