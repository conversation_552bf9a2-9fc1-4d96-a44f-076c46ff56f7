package com.bilibili.crm.platform.api.qichacha.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QichachaCompanyRoundDto implements Serializable {

    private static final long serialVersionUID = 3570946777551963977L;

    /**
     * 融资轮次
     */
    private String Round;

    /**
     * 融资次数
     */
    private String FinancingCount;
}
