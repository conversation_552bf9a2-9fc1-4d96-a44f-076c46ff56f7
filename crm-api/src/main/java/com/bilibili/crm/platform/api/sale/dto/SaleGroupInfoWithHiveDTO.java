package com.bilibili.crm.platform.api.sale.dto;

import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Lists;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;

import java.beans.Transient;
import java.io.Serializable;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 销售组查询hive的结果DTO
 *
 * <AUTHOR>
 * date 2024/5/16 18:51.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@Slf4j
public class SaleGroupInfoWithHiveDTO implements Serializable {

    private static final long serialVersionUID = 138823260937385675L;
    private String sale_group_id;
    private String sale_group_name;
    private String sale_group_leader_id;
    private String sale_group_parent_id;
    private String sale_group_parent_name;
    private String sale_group_level;
    private String sale_group_status;
    private String cz_sale_s_id_list;
    private String cz_sale_s_biz_name_list;
    private String fcz_sale_s_id_list;
    private String fcz_sale_s_biz_name_list;
    private String log_date;


    private String sale_group_leader_id_list;
    private String sale_group_leader_name_list;

    @Transient
    public List<String> getLeaderIds() {
        if (StringUtils.isNotBlank(sale_group_leader_id_list)) {
            return Arrays.stream(sale_group_leader_id_list.split(","))
                    .filter(StringUtils::isNotBlank)
                    .filter(StringUtils::isNumeric)
                    .filter(i -> Integer.parseInt(i) > 0)
                    .collect(Collectors.toList());
        } else {
            if (StringUtils.isNotBlank(sale_group_leader_id) && StringUtils.isNumeric(sale_group_leader_id) && Integer.parseInt(sale_group_leader_id) > 0) {
                return Lists.newArrayList(sale_group_leader_id);
            } else {
                log.info("SaleGroupNoLeaders|{}|{}", sale_group_leader_id_list, sale_group_leader_id);
                return Lists.newArrayList();
            }
        }
    }

    @Transient
    public Integer getId() {
        return strToInteger(sale_group_id);
    }

    @Transient
    public Integer getParentId() {
        return strToInteger(getSale_group_parent_id());
    }

    @Transient
    public Integer getStatus() {
        return strToInteger(getSale_group_status());
    }

    @Transient
    public String getName() {
        return sale_group_name;
    }

    @Transient
    private Integer strToInteger(String a) {
        if (StringUtils.isEmpty(a)) {
            return null;
        }
        return Integer.valueOf(a);
    }
}

