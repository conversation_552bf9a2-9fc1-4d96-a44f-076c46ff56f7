package com.bilibili.crm.platform.api.break_tape.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-07-17 19:57:48
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EffectBreakTapeDto implements Serializable {

    /**
     * 主键id
     */
    private Long breakTapeId;

    /**
     * 账户id
     */
    private Integer accountId;

    /**
     * 撞线时间账户日预算
     */
    private Long accountBudget;

    /**
     * 撞线时间账户日消耗
     */
    private Long accountCost;

    /**
     * 撞线撞线时间账户余额
     */
    private Long accountBalance;

    /**
     * 撞线类型
     */
    private Integer breakLineType;

    /**
     * 撞线时间
     */
    private Timestamp breakLineTime;

    /**
     * 回执类型
     */
    private Integer receiptType;

    /**
     * 具体描述
     */
    private String desc;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

}
