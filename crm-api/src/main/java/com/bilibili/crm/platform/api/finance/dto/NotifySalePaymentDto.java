package com.bilibili.crm.platform.api.finance.dto;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NotifySalePaymentDto {

    @ExcelResources(title = "收款日期")
    private String date;

    @ExcelResources(title = "流水ID")
    private Integer flowId;

    @ExcelResources(title = "客户名称")
    private String customerName;

    @ExcelResources(title = "打款金额")
    private BigDecimal payAmount;

    @ExcelResources(title = "已经认款总金额")
    private BigDecimal alreadyProcessAmount;

    @ExcelResources(title = "待认款总金额")
    private BigDecimal toBeProcessAmount;
}