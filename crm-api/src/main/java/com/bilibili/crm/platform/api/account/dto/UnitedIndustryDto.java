package com.bilibili.crm.platform.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/6/12
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UnitedIndustryDto implements Serializable {

    private static final long serialVersionUID = 3093575125485346764L;

    private Integer id;

    private String name;

    private Integer level;

    private Integer pId;

    private String prompt;

    private String remark;

    /**
     * 1:正常 2:封禁
     */
    private Integer status;


    private String parentName;

    private Integer riskLevel;

    private Integer isVertical;

    private List<Integer> achieveBelongSaleGroupIds;

    private List<String> achieveBelongSaleGroupNames;

    private Integer firstId;

    /**
     * 一级行业名称
     */
    private String firstName;

    /**
     * 二级行业id
     */
    private Integer secondId;

    /**
     * 二级行业名称
     */
    private String secondName;

    /**
     * 三级行业id
     */
    private Integer thirdId;

    /**
     * 三级行业名称
     */
    private String thirdName;

    /**
     * 二级行业风险等级 1-1黑产,2-1高危,2-2中危,2-3低危,3-1高负反馈,3-2低相关度,4-1直接竞品,4-2间接竞品,5-1普通行业
     */
    private Integer secondRiskLevel;

    /**
     * 三级行业风险等级 1-1黑产,2-1高危,2-2中危,2-3低危,3-1高负反馈,3-2低相关度,4-1直接竞品,4-2间接竞品,5-1普通行业
     */
    private Integer thirdRiskLevel;

    /**
     * 一级行业备注
     */
    private String firstRemark;

    /**
     * 二级行业备注
     */
    private String secondRemark;

    /**
     * 三级行业备注
     */
    private String thirdRemark;

}
