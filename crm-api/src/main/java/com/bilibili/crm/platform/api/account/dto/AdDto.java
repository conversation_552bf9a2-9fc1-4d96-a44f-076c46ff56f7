package com.bilibili.crm.platform.api.account.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdDto {
    private Integer isAgent;
    private Integer agentType;
    private Integer dependencyAgentId;
    private Integer adStatus;
    private Integer gdStatus;
    private Integer isSupportSeller;
    private Integer isSupportGame;
    private Integer isSupportDpa;
    /**是否支持内容起飞投放 0否 1是**/
    private Integer isSupportContent;
    /**是否支持UP主商单投放 0否 1是**/
    private Integer isSupportPickup;
    /**是否支持互选广告投放 0否 1是**/
    private Integer isSupportMas;
    /**是否支持商业起飞 0否 1是**/
    private Integer isSupportFly;
    /**是否支持线索通 0否 1是**/
    private Integer isSupportCluePass;
}
