package com.bilibili.crm.platform.api.project.enums;

import lombok.Getter;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-11-23 20:51:38
 * @description:
 **/

public enum YearConfigType {

    PROJECT(0, "招商项目目标"),

    TOTAL(1, "品牌&招商收入总目标");

    @Getter
    private final Integer code;

    @Getter
    private final String desc;

    YearConfigType(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }
}
