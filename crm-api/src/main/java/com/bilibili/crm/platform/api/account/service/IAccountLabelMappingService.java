package com.bilibili.crm.platform.api.account.service;

import com.bilibili.crm.platform.api.account.dto.AccountLabelMappingDto;
import com.bilibili.crm.platform.api.account.dto.LabelBindingDto;
import com.bilibili.crm.platform.api.account.dto.QueryLabelMappingDto;
import com.bilibili.crm.platform.api.account.dto.accountlabel.AccountLabelBindingDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/5/7
 * 帐号-标签的映射
 **/
public interface IAccountLabelMappingService {

    List<AccountLabelMappingDto> queryMapping (QueryLabelMappingDto query);

    List<AccountLabelMappingDto> queryMappingFromSlave(QueryLabelMappingDto query);

    List<AccountLabelMappingDto> queryMappingByLabelIds (List<Integer> labelIds);

    void saveMapping (LabelBindingDto binding);

    /**
     * 保存账号与标签的绑定关系
     * 1. 先删除账户下所有的标签(目的为了处理简单)
     * 2. 然后重新插入新的自动标和手动标
     *
     * @param bindingDto 绑定关系
     */
    void saveAccountLabelMapping(AccountLabelBindingDto bindingDto);

    /**
     * 根据标签id获取标签绑定的账号数
     * @param labelId 标签id
     * @return 该标签绑定的账号数
     */
    Integer mappingCount(Integer labelId);
}
