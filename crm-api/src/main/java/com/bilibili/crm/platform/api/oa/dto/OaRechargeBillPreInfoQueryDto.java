package com.bilibili.crm.platform.api.oa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * OA 开票的前置信息
 *
 * <AUTHOR>
 * @date 2021/2/20 3:13 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OaRechargeBillPreInfoQueryDto implements Serializable {

    private static final long serialVersionUID = -6794737504490689581L;
    private OaBillPreSystemInfoDto oaBillPreInfoQueryDto;

    private List<Integer> rechargeIds;
}
