package com.bilibili.crm.platform.api.coupon.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/4/17
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CouponQueryDto implements Serializable {

    private static final long serialVersionUID = 5951220596039736847L;

    private List<Integer> accountIds;

    private List<Integer> agentAccountIds;

    private List<Long> ids;

    private Timestamp begin;

    private Timestamp end;

    private Timestamp ctimeBegin;

    private Timestamp ctimeEnd;

    private List<Integer> projectItemIds;

    private List<Integer> status;

    private Integer page;

    private Integer size;

    private String bizSource;

    private List<Integer> couponTypeList;
}
