package com.bilibili.crm.platform.api.company.dto;

import com.bilibili.crm.platform.common.CrmConstant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 集团账号查询 dto
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CorpAccountQueryDto implements Serializable {
    private static final long serialVersionUID = 8836209495275468499L;

    private Integer accountId;

    /**
     * 查询类型 1-同集团同品牌
     * @see CrmConstant#ACCOUNT_QUERY_TYPE_SAME_GROUP_PRODUCT
     */
    private Integer queryType;

    /**
     * 搜索的账号
     */
    private Integer searchAccountId;

    /**
     * 搜索的用户名
     */
    private String searchUsername;
}
