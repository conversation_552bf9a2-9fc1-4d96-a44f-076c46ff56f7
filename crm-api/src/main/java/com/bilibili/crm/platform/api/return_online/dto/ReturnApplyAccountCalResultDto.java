package com.bilibili.crm.platform.api.return_online.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ReturnApplyAccountCalResultDto implements Serializable {

    /**
     * 合并组标记
     */
    private String mergeGroup;

    /**
     * 集团id(android游戏用)
     */
    private Integer groupId;

    /**
     * 产品id(android游戏用)
     */
    private Integer productId;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 广告主账户id
     */
    private Integer accountId;

    /**
     * 依赖的代理商id
     */
    private Integer depenAgentAccountId;

    /**
     * 上传的级别(代理商定级)(评分季度返货没有)
     */
    private String pointLevel;

    /**
     * 执行状态 0-待执行，1-执行中，2-暂停，3-执行完成，-1-执行失败
     */
    private Integer calStatus;

    /**
     * 计算出来的级别(评分季度返货没有)
     */
    private String calLevel;

    /**
     * 评分等级(评分季度返货才有)
     */
    private String gradeLevel;

    /**
     * 签署日期(非游戏用)
     */
    private Timestamp signDate;

    /**
     * 日均消耗要求(单位:分)(非游戏用)
     */
    private Long dailyCostRequest;

    /**
     * 框架金额(单位:分)(非游戏用)
     */
    private Long frameAmount;


    /**
     * 实际日均消耗(单位:分)(非游戏用)
     */
    private Long calRealDailyCost;

    /**
     * 任务值(单位:分)，代理商月度返货才有
     */
    private Long taskValue;

    /**
     * 完成率(代理商月度)
     */
    private BigDecimal complateRatio;

    /**
     * 本周期复核消耗(单位:分)
     */
    private Long calCurPeriodComplexCost;

    /**
     * 本周期总消耗(单位:分)(代理商月度)
     */
    private Long calCurPeriodTotalCost;

    /**
     * 本周期现金消耗(单位:分)(代理商月度)
     */
    private Long calCurPeriodCashCost;

    /**
     * 上个周期复核消耗(单位:分)
     */
    private Long calPrePeriodComplexCost;

    /**
     * 完成档位(非游戏用)
     */
    private String calCompleteGear;

    /**
     * 返点比例
     */
    private BigDecimal calReturnRatio;

    /**
     * android总消耗占比(android游戏用)
     */
    private BigDecimal androidCostRatio;

    /**
     * 返货数(单位:分)
     */
    private Long calReturnAmount;

    /**
     * 产品类型(多个逗号分隔)
     */
    private String productTypes;

    /**
     * 完成时间
     */
    private Timestamp completeTime;

    /**
     * 备注
     */
    private String remark;

    private static final long serialVersionUID = 1L;
}
