package com.bilibili.crm.platform.api.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/4/13 2:23 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractRelationInfo {

    private Integer contractId;
    private Long contractNumber;

    /**
     * 合同基本信息
     */
    private ContractBaseInfo contractBaseInfo;

    /**
     * 合同的用户信息(广告主/代理商)
     */
    private ContractUserInfo contractUserInfo;

    /**
     * 合同 oa 信息
     */
    private ContractOaInfo contractOaInfo;
}
