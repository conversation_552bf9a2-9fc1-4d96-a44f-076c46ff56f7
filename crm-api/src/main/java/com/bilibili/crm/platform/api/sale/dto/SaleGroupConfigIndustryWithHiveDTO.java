package com.bilibili.crm.platform.api.sale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 客运查询hive的结果DTO
 *
 * <AUTHOR>
 * date 2024/5/16 18:51.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleGroupConfigIndustryWithHiveDTO implements Serializable {

    private static final long serialVersionUID = -3589247208598959068L;
    private Integer customer_united_third_industry_id;
    private String customer_united_third_industry_name;
    private Integer ghyj_zk_sale_group_id_i3;
    private String ghyj_zk_sale_group_name_i3;
}

