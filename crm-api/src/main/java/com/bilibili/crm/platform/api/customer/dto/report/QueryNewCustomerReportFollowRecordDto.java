package com.bilibili.crm.platform.api.customer.dto.report;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 14:39
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class QueryNewCustomerReportFollowRecordDto {

    /**
     * 跟进记录ID
     */
    private Long id;

    /**
     * 报备ID
     */
    private Long reportId;

    /**
     * 报备IDs
     */
    private List<Long> reportIds;

    /**
     * 创建时间
     */
    private Timestamp ctimeStart;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;
}
