package com.bilibili.crm.platform.soa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 之光统计商业收入结构使用
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DssOrderIncomeQueryDto implements Serializable {

    /**
     * 非标订单类型 0所有 1邀约 2商单 3其他非标 4CPT 5GD
     */
    private Integer type;

    /**
     * 开始日期
     */
    private Timestamp startDate;

    /**
     * 结束日期
     */
    private Timestamp endDate;

    /**
     * 订单id
     */
    private List<Integer> orderIds;
}
