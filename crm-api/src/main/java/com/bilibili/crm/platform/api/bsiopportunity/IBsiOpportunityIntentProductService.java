package com.bilibili.crm.platform.api.bsiopportunity;

import com.bilibili.adp.common.bean.DropboxDto;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiIntentProductQueryDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityIntentProductDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityIntentProductMappingDto;


import java.util.List;
import java.util.Map;

public interface IBsiOpportunityIntentProductService {
    List<DropboxDto> getOpportunityIntentProductIdByType(Integer intentProductType);

    Map<Integer, List<Integer>> getOpportunityIntentProductMap(BsiIntentProductQueryDto queryDto);

    Map<Integer, List<BsiOpportunityIntentProductMappingDto>> getOpportunityIntentProductDtoMap(BsiIntentProductQueryDto queryDto);

    List<BsiOpportunityIntentProductMappingDto> getBsiIntentProductMapping(BsiIntentProductQueryDto queryDto);

    List<Integer> getBsiOpportunityIdByProductType(BsiIntentProductQueryDto queryDto);

    void saveBsiOpportunityIntentProductMappingDto(BsiOpportunityDto dto, Operator operator);

    Long getBsiQuarterAmountSellType(List<BsiOpportunityIntentProductDto> intentProductMappingDtoList, List<Integer> salebsiList, Map<Integer, BsiOpportunityDto> bsiDTOMap, Integer isForCoefficient);

    Long getBsiQuarterAmountSellTypeAll(List<BsiOpportunityIntentProductDto> intentProductMappingDtoList, List<Integer> salebsiList, Map<Integer, BsiOpportunityDto> bsiDTOMap, Integer isForCoefficient);

    Long getNewBsiQuarterAmountSellType(List<BsiOpportunityIntentProductDto> intentProductMappingDtoList, List<Integer> salebsiList, Map<Integer, BsiOpportunityDto> bsiDTOMap, Integer isForCoefficient);

    Long getNewBsiQuarterAmountSellTypeAll(List<BsiOpportunityIntentProductDto> intentProductMappingDtoList, List<Integer> salebsiList, Map<Integer, BsiOpportunityDto> bsiDTOMap, Integer isForCoefficient);

}
