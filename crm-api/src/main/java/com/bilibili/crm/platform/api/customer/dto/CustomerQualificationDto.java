package com.bilibili.crm.platform.api.customer.dto;

import com.bilibili.bjcom.util.sensitive.annotation.Sensitive;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerQualificationDto {
    /**
     * 主键ID
     */
    private Integer id;

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 主体资质分类id（机构专用）
     */
    private Integer qualificationTypeId;

    /**
     * 营业执照编码（机构专用）
     */
    private String businessLicenceCode;

    /**
     * 法人姓名（机构专用）
     */
    @Sensitive
    private String legalPersonName;

    /**
     * 个人证件号码（个人专用）
     */
    private String personalIdCardNumber;
}
