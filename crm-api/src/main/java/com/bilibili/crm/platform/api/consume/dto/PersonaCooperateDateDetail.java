package com.bilibili.crm.platform.api.consume.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2021/4/5 3:58 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonaCooperateDateDetail {
    private Integer id;
    private String name;
    private Timestamp date;
    private Long income;
    private Long showCount;
    private Long clickCount;
}
