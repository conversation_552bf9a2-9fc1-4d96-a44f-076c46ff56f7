package com.bilibili.crm.platform.api.effect.dto;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-08-25 16:58:33
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MiddleIndexDto implements Serializable {
    private static final long serialVersionUID = -8903269563982848274L;

    @ApiModelProperty("账号名称")
    @ExcelResources(title = "账号名称", order = -18)
    private String accountName;

    @ApiModelProperty("账号名称")
    @ExcelResources(title = "账号ID", order = -17)
    private Integer accountId;

    @ApiModelProperty("代理商名称")
    @ExcelResources(title = "代理商名称", order = -16)
    private String agentName;

    @ApiModelProperty("所属客户名称")
    @ExcelResources(title = "所属客户名称", order = -15)
    private String customerName;

    @ApiModelProperty("账号状态")
    @ExcelResources(title = "账号状态", order = -14)
    private String accStatus;

    @ApiModelProperty("余额")
    @ExcelResources(title = "余额", order = -13)
    private BigDecimal balance;

    @ApiModelProperty(notes = "单元ID")
    @ExcelResources(title = "单元ID", order = -12)
    private Integer unitId;
    @ApiModelProperty(notes = "单元名称")
    @ExcelResources(title = "单元名称", order = -11)
    private String unitName;
    @ApiModelProperty(notes = "单元状态")
    @ExcelResources(title = "单元状态", order = -10)
    private String status;
    @ApiModelProperty(notes = "单元预算")
    @ExcelResources(title = "单元预算", order = -9)
    private BigDecimal budget;
    @ApiModelProperty(notes = "广告位")
    @ExcelResources(title = "广告位", order = -8)
    private String slotGroup;
    @ApiModelProperty(notes = "设备")
    @ExcelResources(title = "设备", order = -7)
    private String osDesc;
    @ApiModelProperty(notes = "投放日期")
    @ExcelResources(title = "投放日期", order = -6)
    private String date;
    @ApiModelProperty(notes = "投放时间")
    @ExcelResources(title = "投放时间", order = -5)
    private String time;
    @ApiModelProperty(notes = "计划名称")
    @ExcelResources(title = "计划名称", order = -4)
    private String campaignName;
    @ApiModelProperty(notes = "计划ID")
    @ExcelResources(title = "计划ID", order = -3)
    private Integer campaignId;
    private Integer salesMode;
    @ApiModelProperty(notes = "出价方式")
    @ExcelResources(title = "出价方式", order = -2)
    private String salesModeDesc;
    @ApiModelProperty(notes = "出价(元)")
    @ExcelResources(title = "出价(元)", order = -1)
    private BigDecimal costPrice;
    private Integer ocpxStage;

    private String ocpxStageDesc;
    private String ocpxTargetDesc;

    @ApiModelProperty(notes = "oCPX深度转化目标")
    @ExcelResources(title = "oCPX深度转化目标", order = -0)
    private String ocpxTargetTwoStageDesc;
    @ApiModelProperty("目标转化出价")
    @ExcelResources(title = "目标转化出价", order = 1)
    private BigDecimal ocpxTargetOneBid;
    @ApiModelProperty("深度转化出价")
    @ExcelResources(title = "深度转化出价", order = 2)
    private BigDecimal ocpxTargetTwoBid;
    @ApiModelProperty("oCPX转化目标")
    @ExcelResources(title = "oCPX转化目标", order = 3)
    private String ocpx_state_desc;
    @ApiModelProperty("oCPX赔付状态")
    @ExcelResources(title = "oCPX赔付状态", order = 4)
    private String ocpxAutoPayFinalStatus;

    @ApiModelProperty(notes = "总花费")
    @ExcelResources(title = "总花费", order = 5)
    private BigDecimal cost;
    @ApiModelProperty(notes = "展示量")
    @ExcelResources(title = "展示量", order = 6)
    private Long showCount;
    @ApiModelProperty(notes = "点击量")
    @ExcelResources(title = "点击量", order = 7)
    private Integer clickCount;
    @ApiModelProperty(notes = "点击率")
    @ExcelResources(title = "点击率", order = 8)
    private String clickRate;
    @ApiModelProperty(notes = "千次展示价格")
    @ExcelResources(title = "千次展示价格", order = 9)
    private String averageCostPerThousand;
    @ApiModelProperty(notes = "单次点击价格")
    @ExcelResources(title = "单次点击价格", order = 10)
    private String costPerClick;
    @ExcelResources(title = "在线创意数", order = 11)
    @ApiModelProperty(notes = "在线创意数")
    private Integer onlineCreative;
    @ExcelResources(title = "新增创意数", order = 12)
    @ApiModelProperty(notes = "新增创意数")
    private Integer newCreative;
    @ApiModelProperty(notes = "表单提交数")
    @ExcelResources(title = "表单提交数", order = 20)
    private Integer formSubmitCount;
    @ApiModelProperty(notes = "表单提交率")
    @ExcelResources(title = "表单提交率", order = 21)
    private String formSubmitRate;
    @ApiModelProperty(notes = "表单提交成本")
    @ExcelResources(title = "表单提交成本", order = 22)
    private String formSubmitAverageCost;
    @ApiModelProperty(notes = "表单付费数")
    @ExcelResources(title = "表单付费数", order = 23)
    private Integer formPaidCount;
    @ApiModelProperty(notes = "表单付费率")
    @ExcelResources(title = "表单付费率", order = 24)
    private String formPaidRate;
    @ApiModelProperty(notes = "表单付费成本")
    @ExcelResources(title = "表单付费成本", order = 25)
    private String formPaidCost;
    @ApiModelProperty(notes = "有效线索数")
    @ExcelResources(title = "有效线索数", order = 30)
    private Integer validClueCount;
    @ApiModelProperty(notes = "有效线索率")
    @ExcelResources(title = "有效线索率", order = 31)
    private String validClueRate;
    @ApiModelProperty(notes = "有效线索成本")
    @ExcelResources(title = "有效线索成本", order = 32)
    private String validClueAverageCost;
    @ApiModelProperty(notes = "微信复制数")
    @ExcelResources(title = "微信复制数", order = 33)
    private Integer wxCopyCount;
    /**
     * 【微信复制成本】=总花费/【微信复制数】
     */
    @ApiModelProperty(notes = "微信复制成本")
    @ExcelResources(title = "微信复制成本", order = 34)
    private String wxCopyCost;
    /**
     * 【微信复制率】=【微信复制数】/点击量
     */
    @ApiModelProperty(notes = "微信复制率")
    @ExcelResources(title = "微信复制率", order = 35)
    private String wxCopyRate;


    @ApiModelProperty(notes = "微信加粉数")
    @ExcelResources(title = "微信加粉数", order = 36)
    private Integer wxAddFansCount;
    @ApiModelProperty(notes = "微信加粉率")
    @ExcelResources(title = "微信加粉率", order = 37)
    private String wxAddFansRate;
    @ApiModelProperty(notes = "微信加粉成本")
    @ExcelResources(title = "微信加粉成本", order = 38)
    private String wxAddFansCost;

    @ApiModelProperty(notes = "完件数")
    @ExcelResources(title = "完件数", order = 40)
    private Integer applyCount;
    /**
     * 【完件成本】= 总花费 /【完件数】
     */
    @ApiModelProperty(notes = "完件成本")
    @ExcelResources(title = "完件成本", order = 40)
    private String applyCost;
    /**
     * 【完件率】=【完件数】/ 点击量
     */
    @ApiModelProperty(notes = "完件率")
    @ExcelResources(title = "完件率", order = 42)
    private String applyRate;
    /**
     * 授信数
     */
    @ApiModelProperty(notes = "授信数")
    @ExcelResources(title = "授信数", order = 43)
    private Integer creditCount;
    /**
     * 【授信成本】= 总花费 /【授信数】
     */
    @ApiModelProperty(notes = "授信成本")
    @ExcelResources(title = "授信成本", order = 44)
    private String creditCost;
    /**
     * 【授信率】=【授信数】/ 点击量
     */
    @ApiModelProperty(notes = "授信率")
    @ExcelResources(title = "授信率", order = 45)
    private String creditRate;
    /**
     * 放款数
     */
    @ApiModelProperty(notes = "放款数")
    @ExcelResources(title = "放款数", order = 46)
    private Integer withdrawDepositsCount;
    /**
     * 【放款成本】= 总花费 /【放款数】
     */
    @ApiModelProperty(notes = "放款成本")
    @ExcelResources(title = "放款成本", order = 47)
    private String withdrawDepositsCost;
    /**
     * 【放款率】=【放款数】/ 点击量
     */
    @ApiModelProperty(notes = "放款率")
    @ExcelResources(title = "放款率", order = 47)
    private String withdrawDepositsRate;

    @ApiModelProperty(notes = "首日放款数")
    @ExcelResources(title = "首日放款数", order = 48)
    private Integer firstWithdrawCount;
    @ApiModelProperty(notes = "首日放款率")
    @ExcelResources(title = "首日放款率", order = 49)
    private String firstWithdrawRate;
    @ApiModelProperty(notes = "首日放款成本")
    @ExcelResources(title = "首日放款成本", order = 49)
    private String firstWithdrawCost;

    @ApiModelProperty(notes = "游戏预约数")
    @ExcelResources(title = "游戏预约数", order = 50)
    private Integer gameReserveCount;
    @ApiModelProperty(notes = "游戏预约成本")
    @ExcelResources(title = "游戏预约成本", order = 51)
    private String gameReserveAverageCost;
    @ApiModelProperty(notes = "游戏预约率")
    @ExcelResources(title = "游戏预约率", order = 51)
    private String gameReserveRate;
    @ApiModelProperty(notes = "安卓下载数")
    @ExcelResources(title = "安卓下载数", order = 52)
    private Integer androidDownloadCount;
    @ApiModelProperty(notes = "安卓下载率")
    @ExcelResources(title = "安卓下载率", order = 52)
    private String androidDownloadRate;
    @ApiModelProperty(notes = "安卓下载成本")
    @ExcelResources(title = "安卓下载成本", order = 52)
    private String androidDownloadAverageCost;

    @ApiModelProperty(notes = "安卓安装数")
    @ExcelResources(title = "安卓安装数", order = 53)
    private Integer androidInstallCount;
    @ApiModelProperty(notes = "安卓安装率")
    @ExcelResources(title = "安卓安装率", order = 53)
    private String androidInstallRate;
    @ApiModelProperty(notes = "安卓安装成本")
    @ExcelResources(title = "安卓安装成本", order = 53)
    private String androidInstallAverageCost;

    @ApiModelProperty(notes = "IOS激活数")
    @ExcelResources(title = "IOS激活数", order = 54)
    private Integer iosActivateCount;
    @ApiModelProperty(notes = "Android激活数")
    @ExcelResources(title = "Android激活数", order = 54)
    private Integer androidActivateCount;
    @ApiModelProperty(notes = "应用激活数")
    @ExcelResources(title = "应用激活数", order = 55)
    private Integer activateCount;
    @ApiModelProperty(notes = "应用激活成本")
    @ExcelResources(title = "应用激活成本", order = 56)
    private String appActivateAverageCost;
    @ApiModelProperty(notes = "应用激活率")
    @ExcelResources(title = "应用激活率", order = 57)
    private String appActivateRate;
    @ApiModelProperty(notes = "安卓游戏中心激活数")
    @ExcelResources(title = "安卓游戏中心激活数", order = 58)
    private Integer androidGameCenterActivationCount;
    @ApiModelProperty(notes = "安卓游戏中心成本")
    @ExcelResources(title = "安卓游戏中心成本", order = 59)
    private String androidGameCenterActivationCost;
    @ApiModelProperty(notes = "安卓游戏中心激活率")
    @ExcelResources(title = "安卓游戏中心激活率", order = 60)
    private String androidGameCenterActivationRate;
    @ApiModelProperty(notes = "用户注册数")
    @ExcelResources(title = "用户注册数", order = 70)
    private Integer registerCount;
    @ApiModelProperty(notes = "用户注册成本")
    @ExcelResources(title = "用户注册成本", order = 71)
    private String registerAverageCost;
    @ApiModelProperty(notes = "用户注册率")
    @ExcelResources(title = "用户注册率", order = 72)
    private String registerRate;
    @ApiModelProperty(notes = "应用内付费次数")
    @ExcelResources(title = "应用内付费次数", order = 73)
    private Integer orderPayCount;
    @ApiModelProperty(notes = "应用内累计付费金额")
    @ExcelResources(title = "应用内累计付费金额", order = 74)
    private String orderPayAmount;
    @ApiModelProperty(notes = "应用内首次付费次数")
    @ExcelResources(title = "应用内首次付费次数", order = 75)
    private Integer orderFirstPayCount;
    @ApiModelProperty(notes = "应用内首次付费金额")
    @ExcelResources(title = "应用内首次付费金额", order = 76)
    private String orderFirstPayAmount;
    /**
     * 应用内首次付费消耗 = cost(总花费) / orderFirstPayCount(应用内首次付费次数)
     */
    @ApiModelProperty(notes = "应用内首次付费成本")
    @ExcelResources(title = "应用内首次付费成本", order = 77)
    private String orderFirstPayCost;
    /**
     * 应用内首次付费率 = orderFirstPayCount(应用内首次付费数) / clickCount(点击数)
     */
    @ApiModelProperty(notes = "应用内首次付费率")
    @ExcelResources(title = "应用内首次付费率", order = 78)
    private String orderFirstPayRate;
    @ApiModelProperty(notes = "激活后24小时付费数")
    @ExcelResources(title = "激活后24小时付费数", order = 79)
    private Integer paidIn24hCount;
    @ApiModelProperty(notes = "激活后24小时付费成本")
    @ExcelResources(title = "激活后24小时付费成本", order = 80)
    private String paidIn24hCost;
    @ApiModelProperty(notes = "激活后24小时付费金额")
    @ExcelResources(title = "激活后24小时付费金额", order = 81)
    private String paidIn24hPrice;
    @ApiModelProperty(notes = "激活后24小时付费ROI")
    @ExcelResources(title = "激活后24小时付费ROI", order = 82)
    private String paidIn24hRoi;

    @ApiModelProperty(notes = "激活后7日内付费数")
    @ExcelResources(title = "激活后7日内付费数", order = 83)
    private Integer paidIn7dCount;
    @ApiModelProperty(notes = "激活后7日内付费成本")
    @ExcelResources(title = "激活后7日内付费成本", order = 84)
    private String paidIn7dCost;
    @ApiModelProperty(notes = "激活后7日内付费金额")
    @ExcelResources(title = "激活后7日内付费金额", order = 85)
    private String paidIn7dPrice;
    @ApiModelProperty(notes = "激活后7日内付费ROI")
    @ExcelResources(title = "激活后7日内付费ROI", order = 86)
    private String paidIn7dRoi;

    @ApiModelProperty(notes = "安卓游戏中心应用内付费次数")
    @ExcelResources(title = "安卓游戏中心应用内付费次数", order = 90)
    private Integer androidGameCenterPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内付费金额
     */
    @ApiModelProperty(notes = "安卓游戏中心应用内累计付费金额")
    @ExcelResources(title = "安卓游戏中心应用内累计付费金额", order = 92)
    private String androidGameCenterPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费数
     */
    @ApiModelProperty(notes = "安卓游戏中心应用内首次付费数")
    @ExcelResources(title = "安卓游戏中心应用内首次付费数", order = 93)
    private Integer androidGameCenterFirstPaymentInAppCount;
    /**
     * 安卓游戏中心 应用内首次付费金额
     */
    @ApiModelProperty(notes = "安卓游戏中心应用内首次付费金额")
    @ExcelResources(title = "安卓游戏中心应用内首次付费金额", order = 94)
    private String androidGameCenterFirstPaymentInAppAmount;
    /**
     * 安卓游戏中心 应用内首次付费成本 【安卓游戏中心应用内首次付费成本】=总花费/【安卓游戏中心应用内首次付费数】
     */
    @ApiModelProperty(notes = "安卓游戏中心应用内首次付费成本")
    @ExcelResources(title = "安卓游戏中心应用内首次付费成本", order = 95)
    private String androidGameCenterFirstPaymentInAppCost;
    /**
     * 安卓游戏中心 应用内首次付费率 【安卓游戏中心应用内首次付费率】=【安卓游戏中心应用内首次付费数】/【安卓游戏中心应用激活数】
     */
    @ApiModelProperty(notes = "安卓游戏中心应用内首次付费率")
    @ExcelResources(title = "安卓游戏中心应用内首次付费率", order = 96)
    private String androidGameCenterFirstPaymentInAppRate;
    @ApiModelProperty(notes = "次日留存数")
    @ExcelResources(title = "次日留存数", order = 100)
    private Integer retentionCount;
    @ApiModelProperty(notes = "次日留存成本")
    @ExcelResources(title = "次日留存成本", order = 101)
    private String retentionCost;
    @ApiModelProperty(notes = "次日留存率")
    @ExcelResources(title = "次日留存率", order = 102)
    private String retentionRate;

    @ApiModelProperty(notes = "应用内访问数")
    @ExcelResources(title = "应用内访问数", order = 103)
    private Integer appCallupCount;
    @ApiModelProperty(notes = "应用内访问成本")
    @ExcelResources(title = "应用内访问成本", order = 104)
    private String appCallupCost;
    @ApiModelProperty(notes = "应用内访问率")
    @ExcelResources(title = "应用内访问率", order = 105)
    private String appCallupRate;

    @ApiModelProperty(notes = "应用唤起数")
    @ExcelResources(title = "应用唤起数", order = 106)
    private Integer lpCallUpSuccessCount;
    @ApiModelProperty(notes = "应用唤起成本")
    @ExcelResources(title = "应用唤起成本", order = 107)
    private String lpCallUpSuccessCost;
    @ApiModelProperty(notes = "应用唤起率")
    @ExcelResources(title = "应用唤起率", order = 108)
    private String lpCallUpSuccessRate;
    /**
     * 关键行为数
     */
    @ApiModelProperty(notes = "关键行为数")
    @ExcelResources(title = "关键行为数", order = 110)
    private Integer keyBehaviorCount;
    /**
     * 关键行为成本 【关键行为成本】=总花费/【关键行为数】
     */
    @ApiModelProperty(notes = "关键行为成本")
    @ExcelResources(title = "关键行为成本", order = 111)
    private String keyBehaviorCost;
    /**
     * 关键行为率 【关键行为率】=【关键行为数】/【应用激活】
     */
    @ApiModelProperty(notes = "关键行为率")
    @ExcelResources(title = "关键行为率", order = 112)
    private String keyBehaviorRate;
    @ApiModelProperty(notes = "店铺停留数")
    @ExcelResources(title = "店铺停留数", order = 113)
    private Integer lpCallUpSuccessStayCount;
    @ApiModelProperty(notes = "店铺停留成本")
    @ExcelResources(title = "店铺停留成本", order = 114)
    private String lpCallUpSuccessStayCost;
    @ApiModelProperty(notes = "店铺停留率")
    @ExcelResources(title = "店铺停留率", order = 115)
    private String lpCallUpSuccessStayRate;
    @ApiModelProperty(notes = "订单提交数")
    @ExcelResources(title = "订单提交数", order = 120)
    private Integer orderSubmitCount;
    @ApiModelProperty(notes = "订单提交金额")
    @ExcelResources(title = "订单提交金额", order = 121)
    private BigDecimal orderSubmitAmount;
    @ApiModelProperty(notes = "订单提交成本")
    @ExcelResources(title = "订单提交成本", order = 122)
    private String orderSubmitCost;
    @ApiModelProperty(notes = "订单提交率")
    @ExcelResources(title = "订单提交率", order = 123)
    private String orderSubmitRate;
    @ApiModelProperty(notes = "订单转化率")
    @ExcelResources(title = "订单转化率", order = 124)
    private String goodsConversionRate;
    @ApiModelProperty(notes = "订单ROI")
    @ExcelResources(title = "订单ROI", order = 125)
    private String goodsRoi;
    @ApiModelProperty(notes = "唤起成单率")
    @ExcelResources(title = "唤起成单率", order = 126)
    private String callUpOrderSuccessRate;

    @ApiModelProperty(notes = "首次购买数")
    @ExcelResources(title = "首次购买数", order = 130)
    private Integer firstOrderPlaceCount;
    @ApiModelProperty(notes = "首次购买金额")
    @ExcelResources(title = "首次购买金额", order = 131)
    private String firstOrderPlaceAmount;
    @ApiModelProperty(notes = "首次购买率")
    @ExcelResources(title = "首次购买率", order = 132)
    private String firstOrderPlaceRate;
    @ApiModelProperty(notes = "首次购买成本")
    @ExcelResources(title = "首次购买成本", order = 133)
    private String firstOrderPlaceCost;
    @ApiModelProperty(notes = "播放量")
    @ExcelResources(title = "播放量", order = 140)
    private int playCount;
    @ApiModelProperty(notes = "播放率")
    @ExcelResources(title = "播放率", order = 141)
    private String playRate;
    @ApiModelProperty(notes = "播放成本")
    @ExcelResources(title = "播放成本", order = 142)
    private String costPerPlayCount;
    @ApiModelProperty(notes = "互动量")
    @ExcelResources(title = "互动量", order = 143)
    private Integer videoLikeCount;
    @ApiModelProperty(notes = "互动率")
    @ExcelResources(title = "互动率", order = 144)
    private String videoLikeRate;
    @ApiModelProperty(notes = "互动成本")
    @ExcelResources(title = "互动成本", order = 145)
    private String videoLikeCost;
    @ApiModelProperty(notes = "涨粉数")
    @ExcelResources(title = "涨粉数", order = 150)
    private Integer fansIncreaseCount;
    @ApiModelProperty(notes = "涨粉成本")
    @ExcelResources(title = "涨粉成本", order = 151)
    private String fansIncreaseAverageCost;
    @ApiModelProperty(notes = "涨粉率")
    @ExcelResources(title = "涨粉率", order = 152)
    private String fansIncreaseRate;
    @ApiModelProperty(notes = "播转粉率")
    @ExcelResources(title = "播转粉率", order = 153)
    private String play2FansRate;
    @ApiModelProperty(notes = "账号关注数")
    @ExcelResources(title = "账号关注数", order = 160)
    private Integer accountSubscribeCount;
    @ApiModelProperty(notes = "账号关注成本")
    @ExcelResources(title = "账号关注成本", order = 161)
    private String accountSubscribeCost;
    @ApiModelProperty(notes = "账号关注率")
    @ExcelResources(title = "账号关注率", order = 162)
    private String accountSubscribeRate;
    @ApiModelProperty(notes = "动态详情页浏览数")
    @ExcelResources(title = "动态详情页浏览数", order = 170)
    private Integer dynamicDetailPageBrowseCount;
    @ApiModelProperty(notes = "动态详情页浏览成本")
    @ExcelResources(title = "动态详情页浏览成本", order = 171)
    private String dynamicDetailPageBrowseCost;
    @ApiModelProperty(notes = "动态详情页浏览率")
    @ExcelResources(title = "动态详情页浏览率", order = 172)
    private String dynamicDetailPageBrowseRate;
    @ApiModelProperty(notes = "首条评论复制数")
    @ExcelResources(title = "首条评论复制数", order = 180)
    private int firstCommentCopyCount;
    @ApiModelProperty(notes = "首条评论复制成本")
    @ExcelResources(title = "首条评论复制成本", order = 181)
    private String costPerFirstCommentCopyCount;
    @ApiModelProperty(notes = "首条评论复制率")
    @ExcelResources(title = "首条评论复制率", order = 182)
    private String firstCommentCopyRate;
    @ApiModelProperty(notes = "评论点击数")
    @ExcelResources(title = "评论点击数", order = 183)
    private Integer commentClick;
    /**
     * 评论链接点击成本（消耗/评论链接点击数)
     */
    @ApiModelProperty(notes = "评论点击成本")
    @ExcelResources(title = "评论点击成本", order = 184)
    private String commentClickCost;
    /**
     * 评论链接点击率（评论链接点击数/播放数）
     */
    @ApiModelProperty(notes = "评论点击率")
    @ExcelResources(title = "评论点击率", order = 185)
    private String commentClickRate;
    @ApiModelProperty(notes = "框下链接点击数")
    @ExcelResources(title = "框下链接点击数", order = 190)
    private int underBoxLinkClickCount;
    @ApiModelProperty(notes = "框下链接点击成本")
    @ExcelResources(title = "框下链接点击成本", order = 191)
    private String costPerUnderBoxLinkClickCount;
    @ApiModelProperty(notes = "框下链接点击率")
    @ExcelResources(title = "框下链接点击率", order = 192)
    private String underBoxLinkClickRate;
    @ApiModelProperty(notes = "活动页浮层拉起数")
    @ExcelResources(title = "活动页浮层拉起数", order = 200)
    private Integer activityPagePullUpCount;
    /**
     * 活动页浮层拉起成本(元)
     */
    @ApiModelProperty(notes = "活动页浮层拉起成本")
    @ExcelResources(title = "活动页浮层拉起成本", order = 201)
    private String activityPagePullUpCost;
    /**
     * 活动页浮层拉起率(元)
     */
    @ApiModelProperty(notes = "活动页浮层拉起率")
    @ExcelResources(title = "活动页浮层拉起率", order = 202)
    private String activityPagePullUpRate;
    @ApiModelProperty(notes = "转化组件点击")
    @ExcelResources(title = "转化组件点击", order = 203)
    private Integer yellowCarClick;
    @ApiModelProperty(notes = "转化组件点击率")
    @ExcelResources(title = "转化组件点击率", order = 204)
    private String yellowCarClickRate;
    @ApiModelProperty(notes = "转化组件点击成本")
    @ExcelResources(title = "转化组件点击成本", order = 205)
    private String costPerYellowCarClick;
    @ApiModelProperty(notes = "评论链接曝光数")
    @ExcelResources(title = "评论链接曝光数", order = 206)
    private Integer commentShowCount;

    @ApiModelProperty(notes = "评论链接曝光率")
    @ExcelResources(title = "评论链接曝光率", order = 207)
    private String commentShowRate;

    @ApiModelProperty(notes = "评论链接曝光点击率")
    @ExcelResources(title = "评论链接曝光点击率", order = 208)
    private String commentShowClickRate;

    @ApiModelProperty(notes = "评论唤起数")
    @ExcelResources(title = "评论唤起数", order = 209)
    private Integer commentCallUpCount;

    @ApiModelProperty(notes = "评论唤起率")
    @ExcelResources(title = "评论唤起率", order = 210)
    private String commentCallUpRate;

    @ApiModelProperty(notes = "播放唤起率")
    @ExcelResources(title = "播放唤起率", order = 211)
    private String playCallUpRate;

    @ApiModelProperty(notes = "当日付费数")
    @ExcelResources(title = "当日付费数", order = 220)
    private Integer firstDayPayCount;
    @ApiModelProperty(notes = "当日付费金额")
    @ExcelResources(title = "当日付费金额", order = 221)
    private String firstDayPayAmount;
    @ApiModelProperty(notes = "当日付费率")
    @ExcelResources(title = "当日付费率", order = 222)
    private String firstDayPayRate;
    @ApiModelProperty(notes = "当日付费成本")
    @ExcelResources(title = "当日付费成本", order = 223)
    private String firstDayPayCost;
    @ApiModelProperty(notes = "当日付费ROI")
    @ExcelResources(title = "当日付费ROI", order = 224)
    private String firstDayPayRoi;

    private Integer promotionPurposeType;

    private String unitStatusMtime;

    private String beginDate;
    private String endDate;

    private Integer salesType;

    @ExcelResources(title = "安卓游戏激活后24小时付费数", order = 225)
    private Integer androidGameActivePaidIn24hCount;
    @ExcelResources(title = "安卓游戏激活后24小时付费金额", order = 226)
    private String androidGameActivePaidIn24hAmount;
    @ExcelResources(title = "安卓游戏激活后24小时付费成本", order = 227)
    private String androidGameActivePaidIn24hCost;
    @ExcelResources(title = "安卓游戏激活后24小时付费ROI", order = 228)
    private String androidGameActivePaidIn24hRoi;
    @ExcelResources(title = "安卓游戏激活后7日内付费数", order = 229)
    private Integer androidGameActivePaidIn7dCount;
    @ExcelResources(title = "安卓游戏激活后7日内付费金额", order = 230)
    private String androidGameActivePaidIn7dAmount;
    @ExcelResources(title = "安卓游戏激活后7日内付费成本", order = 231)
    private String androidGameActivePaidIn7dCost;
    @ExcelResources(title = "安卓游戏激活后7日内付费ROI", order = 232)
    private String androidGameActivePaidIn7dRoi;
    @ExcelResources(title = "安卓游戏首日付费数", order = 233)
    private Integer androidGameActivePaidIn1dCount;
    @ExcelResources(title = "安卓游戏首日付费金额", order = 234)
    private String androidGameActivePaidIn1dAmount;
    @ExcelResources(title = "安卓游戏首日付费成本", order = 235)
    private String androidGameActivePaidIn1dCost;
    @ExcelResources(title = "安卓游戏首日付费ROI", order = 236)
    private String androidGameActivePaidIn1dRoi;
    @ExcelResources(title = "安卓游戏首日付费率", order = 237)
    private String androidGameActivePaidIn1dRate;

    @ApiModelProperty(notes = "首日付费数")
    @ExcelResources(title = "首日付费数", order = 233)
    private Integer newFirstDayPayCount;
    @ApiModelProperty(notes = "首日付费金额")
    @ExcelResources(title = "首日付费金额", order = 234)
    private String newFirstDayPayAmount;
    @ApiModelProperty(notes = "首日付费率")
    @ExcelResources(title = "首日付费率", order = 235)
    private String newFirstDayPayRate;
    @ApiModelProperty(notes = "首日付费成本")
    @ExcelResources(title = "首日付费成本", order = 236)
    private String newFirstDayPayCost;
    @ApiModelProperty(notes = "首日付费ROI")
    @ExcelResources(title = "首日付费ROI", order = 237)
    private String newFirstDayPayRoi;

    @ApiModelProperty(notes = "辅助目标")
    @ExcelResources(title = "辅助目标", order = 238)
    private String assistTargetDesc;

    @ApiModelProperty(notes = "辅助出价")
    @ExcelResources(title = "辅助出价", order = 239)
    private BigDecimal assistPrice;

    @ApiModelProperty(notes = "直播间唤起次数")
    @ExcelResources(title = "直播间唤起次数", order = 248)
    private Integer liveCallUpCount;
    @ApiModelProperty(notes = "直播间唤起成本")
    @ExcelResources(title = "直播间唤起成本", order = 249)
    private String liveCallUpCost;
    @ApiModelProperty(notes = "直播间进房唤起率")
    @ExcelResources(title = "直播间进房唤起率", order = 250)
    private String liveCallUpRate;
    @ApiModelProperty(notes = "直播间进人数")
    @ExcelResources(title = "直播间进人数", order = 251)
    private Integer liveEntryCount;
    @ApiModelProperty(notes = "直播间进人成本")
    @ExcelResources(title = "直播间进人成本", order = 252)
    private String liveEntryCost;
    @ApiModelProperty(notes = "直播间进人率")
    @ExcelResources(title = "直播间进人率", order = 253)
    private String liveEntryRate;
    @ApiModelProperty(notes = "直播间预约数")
    @ExcelResources(title = "直播间预约数", order = 254)
    private Integer liveReserveCount;
    @ApiModelProperty(notes = "直播间预约成本")
    @ExcelResources(title = "直播间预约成本", order = 255)
    private String liveReserveCost;
    @ApiModelProperty(notes = "直播间预约率")
    @ExcelResources(title = "直播间预约率", order = 256)
    private String liveReserveRate;


    @ApiModelProperty("激活后24小时变现数")
    @ExcelResources(title = "激活后24小时变现数", order = 260)
    private Long adConv_24hCount;

    @ApiModelProperty("激活后24小时变现成本")
    @ExcelResources(title = "激活后24小时变现成本", order = 261)
    private Double adConv_24hCost;

    @ApiModelProperty("激活后24小时变现金额")
    @ExcelResources(title = "激活后24小时变现金额", order = 262)
    private Double adConv_24hAmount;

    @ApiModelProperty("激活后24小时变现ROI")
    @ExcelResources(title = "激活后24小时变现ROI", order = 263)
    private Double adConv_24hRoi;

    @ApiModelProperty("激活后首自然日变现数")
    @ExcelResources(title = "激活后首自然日变现数", order = 264)
    private Long adConv_1dCount;

    @ApiModelProperty("激活后首自然日变现成本")
    @ExcelResources(title = "激活后首自然日变现成本", order = 265)
    private Double adConv_1dCost;

    @ApiModelProperty("激活后首自然日变现金额")
    @ExcelResources(title = "激活后首自然日变现金额", order = 266)
    private Double adConv_1dAmount;

    @ApiModelProperty("激活后首自然日变现ROI")
    @ExcelResources(title = "激活后首自然日变现ROI", order = 267)
    private Double adConv_1dRoi;

    @ApiModelProperty("激活后7日变现数")
    @ExcelResources(title = "激活后7日变现数", order = 268)
    private Long adConv_7dCount;

    @ApiModelProperty("激活后7日变现成本")
    @ExcelResources(title = "激活后7日变现成本", order = 269)
    private Double adConv_7dCost;

    @ApiModelProperty("激活后7日变现金额")
    @ExcelResources(title = "激活后7日变现金额", order = 270)
    private Double adConv_7dAmount;

    @ApiModelProperty("激活后7日变现ROI")
    @ExcelResources(title = "激活后7日变现ROI", order = 271)
    private Double adConv_7dRoi;

}
