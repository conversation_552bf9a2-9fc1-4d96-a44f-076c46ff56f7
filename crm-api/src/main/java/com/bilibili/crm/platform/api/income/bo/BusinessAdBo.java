package com.bilibili.crm.platform.api.income.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Collections;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/1/12 7:29 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BusinessAdBo implements Serializable {

    private static final long serialVersionUID = 1L;
    /**
     * 广告团队
     */
    private List<IncomeCompositionBo> ad_team;
    /**
     * 主商团队
     */
    private List<IncomeCompositionBo> bsi_team;
    /**
     * 全部团队
     */
    private List<IncomeCompositionBo> all_team;

    public static BusinessAdBo init(){
        return BusinessAdBo.builder()
                .ad_team(Collections.emptyList())
                .bsi_team(Collections.emptyList())
                .all_team(Collections.emptyList())
                .build();
    }
}
