package com.bilibili.crm.platform.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum AsyncOperationTypeEnum {

    CUSTOMER_SALE("CUSTOMER_SALE", "广告主/代理商客户配置销售"),
    CUSTOMER_BRAND_DIRECT_SALE("CUSTOMER_BRAND_DIRECT_SALE", "广告主客户-品牌配置直客销售"),
    ACCOUNT_SALE("ACCOUNT_SALE", "广告主/代理商账户配置销售"),
    BATCH_CUSTOMER_DIRECT_SALE("BATCH_CUSTOMER_DIRECT_SALE", "广告主客户批量配置直客销售"),
    BATCH_ACCOUNT_DIRECT_SALE("BATCH_ACCOUNT_DIRECT_SALE", "广告主账户批量配置直客销售"),
    BATCH_AGENT_CUSTOMER_CHANNEL_SALE("BATCH_AGENT_CUSTOMER_CHANNEL_SALE", "代理商客户批量配置渠道销售"),
    BATCH_AGENT_ACCOUNT_CHANNEL_SALE("BATCH_AGENT_ACCOUNT_CHANNEL_SALE", "代理商帐户批量配置渠道销售"),
    BATCH_CONTRACT_BIZ_SALE("BATCH_CONTRACT_BIZ_SALE", "合同批量配置业绩销售"),
    CONTRACT_BILL_SYSTEM_SPLIT("CONTRACT_BILL_SYSTEM_SPLIT", "合同账单拆分_重跑业绩拆分"),
    CONTRACT_BILL_MANUAL_SPLIT("CONTRACT_BILL_MANUAL_SPLIT", "合同账单拆分_手动业绩拆分"),
    CONTRACT_BILL_SPLIT_CONFIRM("CONTRACT_BILL_SPLIT_CONFIRM", "合同账单拆分_业绩拆分确认"),
    ;

    private final String code;
    private final String desc;
}
