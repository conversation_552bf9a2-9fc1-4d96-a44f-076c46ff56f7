package com.bilibili.crm.platform.api.finance.dto;

import com.bilibili.adp.common.enums.SystemType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * Created by user on 2017/8/10.
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountRechargeBillDto implements Serializable {

    private static final long serialVersionUID = 3560185997758575176L;

    /**
     * 充值 id
     */
    private Integer id;

    // 提交人
    private String creator;

    /**
     * 系统来源
     *
     * @see SystemType
     */
    private Integer invoiceSource;
    private String invoiceSourceDesc;

    /**
     * 开票业务类型：1-合同开票，1-充值开票，2-花火订单开票
     */
    private Integer invoiceBizType;
    private String invoiceBizTypeDesc;

    private Integer flowId;
    private BigDecimal amount;
    private Timestamp rechargeDate;
    private Integer status;
    private Integer mode;

    /**
     * 充值新建日期
     */
    private Timestamp ctime;

    /**
     * 充值到账日期
     */
    private Timestamp moneyArriveTime;

    /**
     * 开票 id
     */
    private Integer billId;

    /**
     * oa 流程编号
     */
    private String oaFlowNo;

    /**
     * 开票日期
     */
    private Timestamp billDate;

    /**
     * 充值类流水自动充值的开票状态
     */
    private Integer billStatus;
    private String billStatusDesc;

    private String billTitle;

    /**
     * 是否可以开票
     */
    private Boolean canBill;

}
