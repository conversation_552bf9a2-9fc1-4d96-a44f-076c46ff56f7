package com.bilibili.crm.platform.api.achievement.dto;

import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import lombok.Data;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2024/3/12
 */
@Data
public class AchieveSaleGroupSupportDto {

    private Map<Integer, List<Integer>> industrySaleGroupMap;

    private Map<Integer, CategoryDto> industryMap;

    private Map<Integer, List<Integer>> saleParentGroupMap;

    private Map<Integer, Boolean> salesInfoMap;

    private Map<Integer, Integer> accountCustomerMap;

    private Map<Integer, List<Integer>> saleGroupCategoryMap;
}
