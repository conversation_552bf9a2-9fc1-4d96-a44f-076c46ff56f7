package com.bilibili.crm.platform.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/4.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewGdOrderDto implements Serializable {
    private static final long serialVersionUID = -1802983260323546613L;
    /**
     * 合同id
     */
    private Integer crmContractId;

    /**
     * 订单说明
     */
    private String explanation;

    /**
     * 0其他 1内部、2售卖、3配送、4补量
     */
    private Integer resourceType;

    /**
     * 0其他 4-GD 15-FLY_GD  16-SSA_CPM
     */
    private Integer type;


}
