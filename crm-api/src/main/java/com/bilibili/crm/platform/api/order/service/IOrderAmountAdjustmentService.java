package com.bilibili.crm.platform.api.order.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.crm.platform.api.order.dto.AdjustOrderAmountDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2018/7/19 15:13
 */
public interface IOrderAmountAdjustmentService {

    void adjustOrderAmount(Operator operator, AdjustOrderAmountDto orderAmountDto);

    void updateAdjustOrderAmount(Operator operator, AdjustOrderAmountDto orderAmountDto);

    void auditPassAdjustOrderAmount(Operator operator, Integer id);

    void rejectAdjustOrderAmount(Operator operator, Integer id);

    void forbiddenAdjustOrderAmount(Operator operator, Integer id);

    Map<Integer, List<AdjustOrderAmountDto>> adjustOrderList(List<Integer> contractIds);

    PageResult<AdjustOrderAmountDto> adjustOrderList(Integer contractId, Integer page, Integer size);

}
