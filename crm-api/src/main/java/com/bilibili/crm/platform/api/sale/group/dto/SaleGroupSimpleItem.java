package com.bilibili.crm.platform.api.sale.group.dto;

import lombok.Data;

import java.io.Serializable;
import java.util.ArrayList;
import java.util.List;

@Data
public class SaleGroupSimpleItem implements Serializable {
    private static final long serialVersionUID = -29551316391085425L;


    public static final SaleGroupSimpleItem UNKNOWN = new SaleGroupSimpleItem();

    static {
        UNKNOWN.setName("未知");
        UNKNOWN.setParents(new ArrayList<>());
    }

    private int id;

    private String name;

    private List<SaleGroupSimpleItem> parents;
}
