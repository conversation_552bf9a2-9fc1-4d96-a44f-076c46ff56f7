package com.bilibili.crm.platform.api.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

@Getter
@AllArgsConstructor
public enum BillSplitTypeEnum {

    SYSTEM_SPLIT(0, "系统拆分"),
    MANUAL_SPLIT(1, "手动拆分");

    private final Integer code;
    private final String desc;

    /**
     * 根据code获取枚举
     *
     * @param code
     * @return
     */
    public static BillSplitTypeEnum getByCode(Integer code) {
        for (BillSplitTypeEnum value : values()) {
            if (value.getCode().equals(code)) {
                return value;
            }
        }
        return null;
    }

    /**
     * 是否是是手动拆分
     */
    public static boolean isManualSplit(Integer code) {
        return MANUAL_SPLIT.getCode().equals(code);
    }

    /**
     * 是否是系统拆分
     */
    public static boolean isSystemSplit(Integer code) {
        return SYSTEM_SPLIT.getCode().equals(code);
    }
}
