package com.bilibili.crm.platform.api.oa.dto;

import com.fasterxml.jackson.annotation.JsonInclude;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: brady
 * @time: 2025/3/25 17:38
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class OaOrderDetailReq implements Serializable {
    private static final long serialVersionUID = -1L;
    /**
     * 审批单据id
     */
    private String orderId;
    private String operator;
}
