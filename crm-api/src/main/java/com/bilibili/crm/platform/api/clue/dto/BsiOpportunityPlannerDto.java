package com.bilibili.crm.platform.api.clue.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-07-24 21:51:28
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BsiOpportunityPlannerDto {

    /**
     * 主键id
     */
    private Long planId;

    /**
     * 商机id
     */
    private Integer bsiOpportunityId;

    /**
     * 商机创建者email前缀
     */
    private String bsiOpportunityCreator;

    /**
     * 策划组长姓名
     */
    private String plannerLeaderName;

    /**
     * 策划组长email
     */
    private String plannerLeaderEmail;

    /**
     * 策划专员姓名
     */
    private String plannerSpecialistName;

    /**
     * 策划专员email
     */
    private String plannerSpecialistEmail;

    /**
     * 策划状态
     */
    private Integer plannerStatus;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 操作人
     */
    private String operatorName;

    /**
     * 是否删除 0 未删除 1 已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

}
