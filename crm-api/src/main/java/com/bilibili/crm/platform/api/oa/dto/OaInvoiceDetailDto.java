package com.bilibili.crm.platform.api.oa.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/2/25
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class OaInvoiceDetailDto {
    private String oa_flow_no;
    private String oa_flow_type;
//    private String business_line;
    private String request_company;
//    private String invoice_operator;
    private String creator;
    private String invoice_target_type;
    private String invoice_type;
    private String customer_name;
    private String taxpayer_id;
    private String customer_address;
    private String customer_bank_account;
    private String amount;
    private String status;
    private String oa_url;
}
