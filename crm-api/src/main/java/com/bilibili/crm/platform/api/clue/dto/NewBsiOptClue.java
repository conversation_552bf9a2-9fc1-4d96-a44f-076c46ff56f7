package com.bilibili.crm.platform.api.clue.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2021/12/16 3:31 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewBsiOptClue {
    /**
     * id
     */
    private Long id;

    /**
     * 来源唯一标志
     */
    private String identifyId;

    /**
     * 线索级别
     */
    private Integer clueLevel;

    /**
     * 线索状态
     */
    private Integer clueStatus;

    /**
     * 线索来源
     */
    private Integer clueSource;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 商业行业一级分类id(新版)
     */
    private Integer categoryFirstId;

    /**
     * 商业行业二级分类id(新版)
     */
    private Integer categorySecondId;

    /**
     * 联系人
     */
    private String contactName;

    /**
     * 联系电话
     */
    private String contactNumber;

    /**
     * 联系邮箱
     */
    private String contactEmail;

    /**
     * 需求描述
     */
    private String description;

    /**
     * bid
     */
    private Long bid;

    /**
     * 推广品牌
     */
    private String brandName;


    /**
     * 广告主/代理商
     */
    private String accountTypeDesc;

    /**
     * 线索时间
     */
    private Timestamp clueTime;

    /**
     * 销售执行id
     */
    private Integer saleId;


    /**
     * 销售执行昵称
     */
    private String saleNickName;

    /**
     * 销售责任人id
     */
    private Integer saleLeaderId;

    /**
     * 销售责任人昵称
     */
    private String saleLeaderNickName;

    /**
     * 销售邮箱前缀
     */
    private String saleEmail;

    /**
     * 销售执行邮箱前缀
     */
    private String saleLeaderEmail;

}
