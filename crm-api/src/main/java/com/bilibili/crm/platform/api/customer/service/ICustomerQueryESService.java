package com.bilibili.crm.platform.api.customer.service;

import com.bilibili.crm.platform.api.customer.dto.CustomerBaseESDTO;
import com.bilibili.crm.platform.api.customer.dto.CustomerEsQueryDTO;

import java.util.List;

/**
 * 通过一些条件查询先从es查询 es开关关闭的情况下查询db，查询条件加的时候需要评估返回数据量是否大于20000
 *
 * <AUTHOR>
 * date 2023/11/16 16:36.
 * Contact: <EMAIL>.
 */
public interface ICustomerQueryESService {
    /**
     * 注意返回的字段是否满足你需要的
     */
    List<CustomerBaseESDTO> queryCustomerListByEsCondition(CustomerEsQueryDTO queryDTO);

    /**
     * 注意返回的字段是否满足你需要的
     */
    List<CustomerBaseESDTO> queryCustomerListByEsUserName(String userName, Integer isAgent, Integer isInner, List<Integer> customerCategorieList, List<Integer> customerStatusList);
}
