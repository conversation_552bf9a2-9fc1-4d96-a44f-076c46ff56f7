package com.bilibili.crm.platform.api.wallet.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/9/4 5:07 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountWalletTradeQueryDto implements Serializable {

    private static final long serialVersionUID = 1L;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 代理商ID
     */
    private Integer agentId;

    /**
     * 账号ids
     */
    private List<Integer> accountIds;

    /**
     * 账户类型
     */
    private List<Integer> walletAccountTypes;
    /**
     * 交易类型
     */
    private List<Integer> walletTradeTypes;
    /**
     * 余额变动开始时间
     */
    private Timestamp changeBeginTime;
    /**
     * 余额变动结束时间
     */
    private Timestamp changeEndTime;
    /**
     * 消耗归属开始时间
     */
    private Timestamp belongBeginTime;
    /**
     * 消耗归属结束时间
     */
    private Timestamp belongEndTime;

    /**
     * 页码
     */
    private Integer page;

    /**
     * 页大小
     */
    private Integer size;
}
