package com.bilibili.crm.platform.soa;

import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.CrmDepartmentDto;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @time 2017/11/2 15:50
 */
public interface ISoaDepartmentDervice {

    List<CrmDepartmentDto> queryAllDepartmentList();

    List<CrmDepartmentDto> queryValidDepartmentList();

    Map<Integer, CrmDepartmentDto> queryDepartmentMap();

    CrmDepartmentDto queryDepartmentById(Integer departmentId);

    CrmDepartmentDto queryDepartmentByCode(String code);

    Map<Integer, CrmDepartmentDto> queryDepartmentMapByIds(List<Integer> departmentIds);

    Map<Integer, String> getDepartmentMap(List<AccountBaseDto> accounts);
}
