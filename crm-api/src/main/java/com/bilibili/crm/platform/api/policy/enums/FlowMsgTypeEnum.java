package com.bilibili.crm.platform.api.policy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 通知消息类型
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:52
 */
@AllArgsConstructor
public enum FlowMsgTypeEnum {

    TODO_MSG(1, "代办消息"),
    NOTICE_MSG(2, "通知消息"),

    UNKNOWN(-1, "未知"),
    ;

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static FlowMsgTypeEnum getByCode(Integer code) {
        for (FlowMsgTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }
}
