package com.bilibili.crm.platform.api.statement.dto;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatementExpressDto implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * 结算单ID
     */
    private Integer statementId;

    private Integer accountId;

    /**
     * 快递类型
     * @see com.bilibili.crm.platform.common.statement.ExpressTypeEnum
     */
    private Integer expressType;

    /**
     * 快递单号
     */
    private String expressNo;

    private Operator operator;

    /**
     * 快递图标
     */
    private AttachmentResultDto iconFileDto;
}
