package com.bilibili.crm.platform.api.cost_management.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BTPAuditingStatisticsDto implements Serializable {
    private String prCode;
    private List<PrLineAuditingStatisticsDto> prLines;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class PrLineAuditingStatisticsDto implements Serializable {
        private String code;
        private Long crmOrderId;
        private String crmOrderName;
        private Long crmContractId;
        private String crmContractName;
        private Long crmProjectId;
        private String crmProjectName;
        private BigDecimal expenditureRate;
    }
}