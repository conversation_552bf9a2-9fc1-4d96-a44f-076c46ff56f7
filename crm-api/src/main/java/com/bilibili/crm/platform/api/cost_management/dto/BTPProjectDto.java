package com.bilibili.crm.platform.api.cost_management.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.time.ZonedDateTime;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BTPProjectDto {
    /**
     * BTP 项目号
     */
    private String projectCode;

    //主站项目的数据

    /**
     * 主站项目号
     */
    private String businessCode;
    /**
     * 主站项目名称，BTP项目名称，共用的
     */
    private String projectName;
    /**
     * crm项目id
     */
    private String crmProjectId;
    /**
     * 主站项目申请人的部门code
     */
    private String deptCode;
    /**
     * 主站项目申请人的部门名称
     */
    private String deptName;
    /**
     * 主站项目申请人ad
     */
    private String projectApplicant;
    /**
     * 主站项目申请人工号
     */
    private String projectApplicantWorkCode;
    /**
     * 主站项目的负责人
     */
    private List<ProjectLeader> projectLeaderList;
    /**
     * 主站项目的状态
     * DRAFT(1, "草稿"),
     * AUDITING(2, "审批中"),
     * ONGOING(3, "进行中"),
     * ADJUSTING(4, "调整审批中"),
     * ABANDON(5, "已废弃"),
     * CLOSE(6, "终止"),
     * TO_BE_SUBMITTED(7, "待提交")
     */
    private String projectStatus;
    /**
     * 主站项目的目标收入金额中，整合营销部分的金额 单位元
     */
    private String expectedRevenue;
    /**
     * 主站项目的预算金额中，整合营销部分的金额 单位元
     */
    private String budgetAmount;
    /**
     * 主站的项目协作人
     */
    private List<ProjectStaffPerson> projectStaffPersons;

    // BTP项目的数据

    /**
     * BTP项目来源
     */
    private String projectSource;

    /**
     * 是否整合营销，0否，1是
     */
    private Integer integratedMarketingBudget;

    /**
     * 是否纯商业项目，0否，1是
     */
    private Integer pureCommercialProject;

    // 虚拟金

    /**
     * 虚拟金种类
     */
    private String currencyType;
    /**
     * 虚拟金计提金额 单位 分
     */
    private String virtualGoldAccruedAmount;
    /**
     * 虚拟金计提时间
     */
    private ZonedDateTime virtualGoldAccruedTime;
    /**
     * 虚拟金核销金额
     */
    private String virtualGoldWrittenOffAmount;
    /**
     * 虚拟金核销时间
     */
    private ZonedDateTime virtualGoldWrittenOffTime;

    /**
     * 预算行
     */
    private List<BTPBudgetDto> budgetLineList;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ProjectLeader {
        private String adAccount;

        private String deptCode;

        private String deptName;

        private String deptCodeSeq;

        private String deptNameSeq;
    }

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    static class ProjectStaffPerson {
        /**
         * ad账号或部门code
         */
        @JsonProperty("key")
        private String code;
    }

    @Data
    //@Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class BTPBudgetDto {
        /**
         * 预算单号
         */
        @JsonProperty("budgetBillCode")
        private String billCode;
        /**
         * 行code
         */
        private String lineCode;

        /**
         * 采购品类
         */
        private String categoryName;

        /**
         * 用途
         */
        private String budgetPurposeName;
        /**
         * 预算行金额
         */
        private String amount;
        /**
         * 品类
         */
        public String categoryCode;
    }

    @Getter
    public enum ProjectStatus {
        DRAFT(1, "草稿"),
        AUDITING(2, "审批中"),
        ONGOING(3, "进行中"),
        ADJUSTING(4, "调整审批中"),
        ABORT(5, "已废弃"),
        CLOSED(6, "终止"),
        WAITING_FOR_COMMIT(7, "待提交");

        private final int code;
        private final String description;

        ProjectStatus(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static ProjectStatus getByCode(int code) {
            for (ProjectStatus p : ProjectStatus.values()) {
                if (p.getCode() == code) {
                    return p;
                }
            }

            return null;
        }
    }
}