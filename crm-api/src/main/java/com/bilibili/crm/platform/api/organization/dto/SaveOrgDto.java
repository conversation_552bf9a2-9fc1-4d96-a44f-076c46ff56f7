package com.bilibili.crm.platform.api.organization.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/23
 **/

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class SaveOrgDto implements Serializable {

    private static final long serialVersionUID = 1L;

    private Long id;
    /**
     * 组织所属集团id
     */
    private Integer groupId;

    /**
     * 组织名称
     */
    private String orgName;

    /**
     * 上级组织id
     */
    private Long superiorOrgId;

    /**
     * 组织层级
     */
    private Integer orgLevel;

    /**
     * 组织类别为客户的客户id
     */
    private Integer customerId;
}
