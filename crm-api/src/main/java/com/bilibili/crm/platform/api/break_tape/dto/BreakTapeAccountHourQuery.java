package com.bilibili.crm.platform.api.break_tape.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-05-20 21:48
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BreakTapeAccountHourQuery implements Serializable {
    private static final long serialVersionUID = 6843081010048083387L;

    /**
     * 账户id
     */
    private List<Integer> accountIds;

    /**
     * 撞线类型
     */
    private List<Integer> breakLineTypes;

    /**
     * 撞线子类型
     */
    private List<Integer> breakLineSubTypes;

    /**
     * 撞线开始时间
     */
    private Timestamp breakLineBeginTime;


    /**
     * 撞线结束时间
     */
    private Timestamp breakLineEndTime;

    private String fieldSort;

    private SortOrder sortOrder;
}
