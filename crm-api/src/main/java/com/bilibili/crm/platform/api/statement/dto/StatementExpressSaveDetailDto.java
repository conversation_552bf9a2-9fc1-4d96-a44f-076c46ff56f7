package com.bilibili.crm.platform.api.statement.dto;

import com.bilibili.adp.common.bean.Operator;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class StatementExpressSaveDetailDto implements Serializable {
    private static final long serialVersionUID = 1L;

    private Integer row;

    /**
     * 结算单ID
     */
    private Integer statementId;

    /**
     * 快递类型
     *
     * @see com.bilibili.crm.platform.common.statement.ExpressTypeEnum
     */
    private Integer expressType;
    private String expressTypeDesc;

    /**
     * 快递单号
     */
    private String expressNo;

    private Operator operator;

    /**
     * 是否批量上传
     */
    private Boolean isBatchUpload;
}
