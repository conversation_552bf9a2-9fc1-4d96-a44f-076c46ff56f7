package com.bilibili.crm.platform.api.qichacha.service;

import com.bilibili.crm.platform.api.qichacha.dto.QichachaCompanyDto;
import com.bilibili.crm.platform.api.qichacha.dto.QichachaCompanyExtDto;

import java.util.List;

public interface IQichachaService {

    List<QichachaCompanyDto> getByNameLike(String companyNameLike);

    QichachaCompanyDto getDetailByName(String companyNameLike);

    /**
     *
     * @param searchKey 公司名 营业执照编码
     * @return
     */
    QichachaCompanyExtDto getExtDetail(String searchKey);
}
