package com.bilibili.crm.platform.api.bsiopportunity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @description:
 * @author: brady
 * @time: 2020/12/3 8:12 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BsiSnapshotQueryDto {
    private Timestamp snapshotDate;

    private List<Integer> saleIds;

    private List<Integer> followStages;


    private Timestamp ctimeEnd;
    private Timestamp ctimeBegin;

    private List<Integer> bsiIds;

    private List<Integer> newFollowStages;

}
