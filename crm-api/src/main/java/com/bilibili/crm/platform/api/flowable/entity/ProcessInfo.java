package com.bilibili.crm.platform.api.flowable.entity;

import com.bilibili.crm.platform.api.policy.enums.FlowTypeEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/3/8 16:03
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ProcessInfo {

    /**
     * 流程ID
     */
    private String procInstId;

    /**
     * 流程类型
     */
    private FlowTypeEnum flowTypeEnum;

    /**
     * 是否完成
     */
    private boolean isFinished;

    /**
     * 当前审批节点
     */
    private NodeInfo currentNode;

    /**
     * 审批流节点信息
     */
    private List<NodeInfo> nodeInfos;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
