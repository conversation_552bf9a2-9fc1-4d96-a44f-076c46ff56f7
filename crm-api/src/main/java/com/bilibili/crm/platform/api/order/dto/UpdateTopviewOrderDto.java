package com.bilibili.crm.platform.api.order.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateTopviewOrderDto implements Serializable {

    private static final long serialVersionUID = -5625968515140522866L;

    /**
     * 自增id
     */
    private Integer id;
    /**
     * 订单说明
     */
    private String explanation;
    /**
     * 订单金额
     */
    private Long amount;
    /**
     * 创建时间
     */
    private Timestamp beginTime;

    /**
     * 创建时间
     */
    private Timestamp endTime;

    private Integer resourceType;

    private Integer status;
}
