package com.bilibili.crm.platform.api.achievement.dto;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.enums.PickupAchieveTypeEnum;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationBucketType;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.rbac.dto.DataStrategy;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @description:
 * @author: brady
 * @time: 2020/10/22 5:26 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryAchieveDto {

    private String teamDataType;
    /**
     * 公共参数   手机日报里的团队业绩数据
     */
    private Integer team;

    private List<Integer> saleGroupIds;

    private List<Integer> saleIds;

    private Timestamp dateBegin;

    private Timestamp dateEnd;

    private Timestamp base;
    /**
     * 关帐月份开始时间
     */
    private Timestamp closeBillDateBegin;

    /**
     * 关帐月份结束时间
     */
    private Timestamp closeBillDateEnd;

    private Integer isClosed;

    /**
     * 合约广告参数
     */

    // 订单类型
    private Integer orderType;

    // 订单类型
    private List<Integer> orderTypes;

    private List<Integer> orderIds;

    // 广告主id
    private Integer accountId;

    // 代理商帐号id
    private Integer agentId;

    //合同id
    private Integer contractId;

    /**
     * 消耗参数
     */
    private List<Integer> agentIds;

    private List<Integer> customerIds;

    private List<Integer> accountIds;

    private List<Integer> companyIds;

    private List<Integer> categoryFirstIds;

    private List<Integer> categorySecondIds;

    private List<Integer> bizIndustryCategoryFirstIds;

    private List<Integer> bizIndustryCategorySecondIds;

    private List<Integer> bizIndustryCategoryFirstIdsOr;

    private List<Integer> bizIndustryCategorySecondIdsOr;

    private List<Integer> platformIds;

    private List<Integer> resourceIds;

    /**
     * 代理商客户idList
     */
    private List<Integer> agentCustomerIds;


    private Integer calSort;

    private Integer sortOrder;

    private Integer aggregateType;

    /**
     * 合约广告已确认收入是否只查询计收部分
     */
    private boolean isContractCheck;

    private boolean isContractClosed;
    /**
     * 合同号
     */
    private Long contractNumber;

    /**
     * 收入组成枚举
     */
    private IncomeComposition incomeComposition;

    private Operator operator;

    /**
     * 用户的角色，销售还是非销售
     */
    private DataStrategy dataStrategy;

    /**
     * 花火订单编号Nos
     */
    private List<Long> pickUpNos;

    /**
     * 花火订单类型
     */
    private List<Integer> pickUpOrderTypes;

    /**
     * 花火订单业绩看板类型
     */
    private PickupAchieveTypeEnum pickAchieveTypeEnum;


    /**
     * 排除的产品类型
     */
    private List<Integer> excludeProductTypes;

    /**
     * 日报专用，合约广告 ，是否昨日
     */
    private boolean isYesterday;


    private List<Integer> productIds;

    private List<Integer> groupIds;

    /**
     * 客户画像查询 详细条件参见 https://www.tapd.bilibili.co/20065741/prong/stories/view/1120065741002525347
     */
    private boolean agentPortrait;

    private List<Integer> rtbNoBelongAgentIds;

    private List<Integer> pickUpNoBelongAgentIds;

    private List<Integer> orAgentIds;

    private String teamKpiAggField;

    private boolean excludeContractPickup;

    /**
     * 时间聚合类型
     */
    private AggregationBucketType TimeAggType;

    private Long cluePassObjId;

    private Integer isQuerySale;

    private Integer businessType;

    private List<Integer> accUnitedFirstIndustryIds;

    private List<Integer> accUnitedSecondIndustryIds;

    private List<Integer> accUnitedThirdIndustryIds;

    private List<Integer> customerUnitedFirstIndustryIds;

    private List<Integer> customerUnitedSecondIndustryIds;

    private List<Integer> customerUnitedThirdIndustryIds;

    private String migrateBizScene;

    private Integer queryDetailListType;

}
