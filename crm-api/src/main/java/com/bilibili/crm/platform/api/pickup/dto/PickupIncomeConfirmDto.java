package com.bilibili.crm.platform.api.pickup.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/7/31 19:24
 * 花火账单收入确认
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PickupIncomeConfirmDto {

    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 月份
     */
    private Timestamp month;

    /**
     * 销售确认状态 0:未确认 1:已确认
     */
    private Integer saleConfirmStatus;

    /**
     * 财务确认状态 0:未确认 1:已确认
     */
    private Integer financeConfirmStatus;
}
