package com.bilibili.crm.platform.api.return_online.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 返货政策状态 0-草稿 1-启用 2-禁用
 *
 * <AUTHOR>
 * @date 2021/4/22 11:32 下午
 */
@AllArgsConstructor
public enum ReturnPolicyStatusEnum {

    /**
     * 草稿状态才可以编辑，默认草稿状态，非草稿纸状态回不到草稿状态了
     */
    DRAFT(0, "草稿"),

    /**
     * 启用禁用状态都不可以编辑
     */
    ENABLE(1, "启用"),
    DISABLE(2, "禁用"),

    UNKNOWN(-1, "未知"),
    ;

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static ReturnPolicyStatusEnum getByCode(Integer code) {
        for (ReturnPolicyStatusEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }

}
