package com.bilibili.crm.platform.api.bsiopportunity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BsiOpportunityContractDto {
    private Long id;

    /**
     * 商机id
     */
    private Integer bsiOpportunityId;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 操作人
     */
    private String operator;

    private Timestamp ctime;
}
