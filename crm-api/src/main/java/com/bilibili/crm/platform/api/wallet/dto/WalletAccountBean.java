package com.bilibili.crm.platform.api.wallet.dto;

import com.bilibili.crm.platform.common.wallet.WalletAccountType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: brady
 * @time: 2021/8/30 10:54 上午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WalletAccountBean {

    /**
     * 金额
     */
    private Long amount;

    /**
     * 资金池类型
     */
    private WalletAccountType WalletAccountType;

}
