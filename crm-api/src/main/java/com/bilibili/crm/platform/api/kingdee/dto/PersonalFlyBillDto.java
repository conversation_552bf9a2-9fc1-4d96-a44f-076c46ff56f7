package com.bilibili.crm.platform.api.kingdee.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonalFlyBillDto {
    private String sourceNumber; // 唯一识别码
    private String transactionDate; // 投放期间
    private Integer AdvertiserId; // 广告主ID
    private String AdvertiserName; // 广告主名称
    private String adminOrgUnitCode; // 所属部门编码
    private String adminOrgUnitName; // 所属部门名称
    private String paymentMethodCode; // 支付方式代码
    private String paymentMethodName; // 支付方式名称
    private String isHosting; // 是否托管
    private BigDecimal totalConsumption; // 整体总消耗(元)
    private BigDecimal cashConsumption; // 扶持前现金消耗(元)
    private BigDecimal goodsConsumption; // 扶持返货现金消耗(元)
    private BigDecimal couponConsumption; // 券消耗(元)
    private BigDecimal totalRecharge; // 总充值(元)
    private BigDecimal cashRecharge; // 现金充值(元)
    private BigDecimal couponRecharge; // 券充值(元)
    private BigDecimal totalRefund; // 总退款(元)
    private BigDecimal cashRefund; // 现金退款(元)
    private BigDecimal couponRefund; // 券退款(元)
}
