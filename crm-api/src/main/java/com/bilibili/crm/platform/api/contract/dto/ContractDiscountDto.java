package com.bilibili.crm.platform.api.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2018/3/26 18:14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractDiscountDto {

    private Integer contractId;
    private Integer distribution;
    private Boolean init;
    private BigDecimal discount;
    private BigDecimal amount;
    private List<OrderDiscountDto> orderDiscounts;
}
