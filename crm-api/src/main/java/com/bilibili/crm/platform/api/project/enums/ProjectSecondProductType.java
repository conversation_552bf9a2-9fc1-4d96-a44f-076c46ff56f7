package com.bilibili.crm.platform.api.project.enums;

import com.bilibili.crm.platform.common.income.IncomeTeamEnum;
import com.google.common.collect.Lists;

import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-12-01 16:43:09
 * @description:
 **/

public enum ProjectSecondProductType {

    BRAND_HARD(1, "硬广", IncomeTeamEnum.BRAND),
    NON_STANDARD(2, "非标", IncomeTeamEnum.BRAND),
    BRAND_OTHER(3, "品牌-其他", IncomeTeamEnum.BRAND),
    EFFECT(4, "效果", IncomeTeamEnum.RTB),
    PICKUP_ORDER(5, "花火商单", IncomeTeamEnum.UP),
    PICKUP_LIVING(6, "直播商单", IncomeTeamEnum.UP),
    PICKUP_OTHER(7, "花火-其他", IncomeTeamEnum.UNKNOWN);

    private final int code;
    private final String name;

    private final IncomeTeamEnum incomeTeamEnum;

    ProjectSecondProductType(int code, String name, IncomeTeamEnum incomeTeamEnum) {
        this.code = code;
        this.name = name;
        this.incomeTeamEnum = incomeTeamEnum;
    }

    public static ProjectSecondProductType getByCode(int code) {
        ProjectSecondProductType[] var1 = values();
        int var2 = var1.length;

        for (int var3 = 0; var3 < var2; ++var3) {
            ProjectSecondProductType bean = var1[var3];
            if (bean.getCode() == code) {
                return bean;
            }
        }

        throw new IllegalArgumentException("unknown code.");
    }

    public int getCode() {
        return this.code;
    }

    public String getName() {
        return this.name;
    }

    public IncomeTeamEnum getIncomeTeamEnum() {
        return this.incomeTeamEnum;
    }

    public static List<ProjectSecondProductType> getBrandType() {
        return Lists.newArrayList(BRAND_HARD, NON_STANDARD);
    }

    public static List<ProjectSecondProductType> getUpType() {
        return Lists.newArrayList(PICKUP_ORDER, PICKUP_LIVING);
    }


}
