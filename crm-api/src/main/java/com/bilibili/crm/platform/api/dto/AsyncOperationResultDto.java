package com.bilibili.crm.platform.api.dto;

import com.bilibili.crm.platform.api.enums.ProcessStatusEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AsyncOperationResultDto<T> {

    private Long processCount;

    private T resultData;

    private long processId;

    private ProcessStatusEnum processStatus;

    private int errorCode;

    private String errorMsg;

    public boolean isSucc() {
        return processStatus == ProcessStatusEnum.SUCCESS;
    }

    public boolean isFail() {
        return processStatus == ProcessStatusEnum.FAILED;
    }
}
