package com.bilibili.crm.platform.api.sale.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;


/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleGroupCategoryMappingDto implements Serializable {

    private static final long serialVersionUID = 4217217783146974012L;

    private Integer id;

    private Integer saleGroupId;

    private Integer commerceCategoryId;

    private Timestamp ctime;

    private Timestamp mtime;

    private Integer isDeleted;

    /**
     * 统一一级行业标签
     */
    private Integer unitedIndustryId;
}
