package com.bilibili.crm.platform.api.customer.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/12/15 21:04
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerSignInfoDto {

    /**
     * 客户ID
     */
    private Integer customerId;

    /**
     * 企业名称
     */
    private String companyName;

    /**
     * 联系地址
     */
    private String address;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 联系人
     */
    private String contactor;

    /**
     * 联系邮箱
     */
    private String email;

    /**
     * 开票信息-企业名称
     */
    private String invoiceCompanyName;

    /**
     * 开票信息-纳税人识别号
     */
    private String invoiceTaxpayerNumber;

    /**
     * 开票信息-联系地址
     */
    private String invoiceAddress;

    /**
     * 开票信息-联系电话
     */
    private String invoicePhone;

    /**
     * 开票信息-开户行
     */
    private String invoiceBankName;

    /**
     * 开票信息-开户行账号
     */
    private String invoiceBankAccount;

    /**
     * 账户名称
     */
    private String accountName;

    /**
     * 银行账号
     */
    private String bankAccount;

    /**
     * 开户行
     */
    private String bankName;

    /**
     * 开户行地址
     */
    private String bankAddress;
}
