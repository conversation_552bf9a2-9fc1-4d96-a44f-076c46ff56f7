package com.bilibili.crm.platform.api.contract.dto;

import com.bilibili.crm.platform.api.order.dto.OrderDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2018/3/26 18:14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractQuotaDto {

    //"资金金额列表")
    private List<OrderDto> allOrders;

    //@ApiModelProperty("应付金额列表")
    private List<OrderDto> upRequirementOrders;

    //@ApiModelProperty("售卖金额")
    private Long saleAmount;

    //@ApiModelProperty("配送金额")
    private Long distributionAmount;

    //@ApiModelProperty("配送比例")
    private Integer distributionRate;

    //@ApiModelProperty("补量金额")
    private Long replenishmentAmount;

    //@ApiModelProperty("内部金额")
    private Long internalAmount;

    //@ApiModelProperty("其他金额")
    private Long otherAmount;

    //@ApiModelProperty("应收金额总计")
    private Long receivableAmount;

    private Long distributionInternalControlAmount;

    //@ApiModelProperty("应付金额总计")
    private Long payableAmount;

    //@ApiModelProperty("内控抹零金额")
    private Long wipeZero;

    private Long totalInternalControlAmount;   //看下计算逻辑，决定是否需要删除

    //@ApiModelProperty("内控金额列表")
    private List<InternalControlAmountDto> internalControlAmounts;


}
