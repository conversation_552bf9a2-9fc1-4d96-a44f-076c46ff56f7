package com.bilibili.crm.platform.api.pickup.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: brady
 * @time: 2025/3/14 15:04
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractSelectPreCmOrderView {
    @ExcelProperty(value = "花火订单编号")
    private String cmOrderNo;                 // 花火订单编号
    @ExcelProperty(value = "花火订单状态")
    private String cmOrderStatus;             // 花火订单状态
    @ExcelProperty(value = "实付总金额")
    private BigDecimal actualPayAmount;             // 实付总金额
    @ExcelProperty(value = "合作类型")
    private String cooperationType;             // 合作类型
    @ExcelProperty(value = "任务编号")
    private String taskNo;                      // 任务编号
    @ExcelProperty(value = "任务名称")
    private String taskName;                    // 任务名称
    @ExcelProperty(value = "MCN机构名称")
    private String mcnName;                  // MCN机构名称
    @ExcelProperty(value = "UP主UID")
    private String upUid;                         // UP主UID
    @ExcelProperty(value = "UP主昵称")
    private String upNickname;                  // UP主昵称
    @ExcelProperty(value = "平台总支出")
    private BigDecimal platformTotalExpense;        // 平台总支出
    @ExcelProperty(value = "广告主账户ID")
    private Integer advertiserAccountId;           // 广告主账户ID
    @ExcelProperty(value = "广告主账户名称")
    private String advertiserAccountName;       // 广告主账户名称
    @ExcelProperty(value = "代理商账户ID")
    private Integer agentAccountId;                // 代理商账户ID
    @ExcelProperty(value = "代理商账户名称")
    private String agentAccountName;            // 代理商账户名称
    @ExcelProperty(value = "下单品牌名称")
    private String orderBrandName;              // 下单品牌名称
    @ExcelProperty(value = "订单创建日期")
    private String orderCreateTime;             // 订单创建日期
    @ExcelProperty(value = "视频实际上线时间")
    private String videoOnlineTime;             // 视频实际上线时间
    @ExcelProperty(value = "订单完成时间")
    private String orderCompleteTime;           // 订单完成时间
    @ExcelProperty(value = "话题ID")
    private String topicId;                       // 话题id
    @ExcelProperty(value = "话题tag")
    private String topicName;                    // 话题tag
    @ExcelProperty(value = "业绩是否计算给整合营销")
    private Boolean isCalculateForMarketing;    // 业绩是否计算给整合营销
    @ExcelProperty(value = "业绩计收金额")
    private BigDecimal settledAmount;           // 业绩计收金额
    @ExcelProperty(value = "已关联合同号")
    private String relatedContractNo;           // 已关联合同号
}
