package com.bilibili.crm.platform.api.follow_manage.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-10-31 15:34:14
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class RecordPlannerSaveDto {


    /**
     * 记录id
     */
    private Long recordId;


    /**
     * 状态 4 驳回 5 分配策划
     */
    private Integer toStatus;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 策划专员
      */
    private List<FollowManagePlannerDto> plannerSpecialistList;
}
