package com.bilibili.crm.platform.api.account.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.crm.platform.api.account.dto.*;
import com.bilibili.crm.platform.api.account.dto.accountlabel.*;
import com.bilibili.crm.platform.api.account.dto.task.UploadTaskDto;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import com.bilibili.crm.platform.common.ModifyType;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/5/7
 * 帐号标签service
 **/
public interface IAccountLabelService {
    /**
     * 校验账号是否存在该账号标签
     *
     * @param accountId
     * @param labelId
     * @return
     */
    Boolean checkAccountWithLabel(Integer accountId, Integer labelId);

    /**
     * 查询帐号列表（附带标签信息） 限制单次查询accountid数量
     *
     * @param queryParam
     * @return
     */
    List<Integer> getAccountIdListWithLabel(List<Integer> accountIdList, Integer labelId);

    // 查询帐号（附带标签信息）
    PageResult<AccountLabelViewDto> queryAccount(QueryAccountLabelDto queryParam);

    List<LabelBindingSimpleDto> querySimpleAccountLabelBinding(List<Integer> accountIds);

    /**
     * 查询一批账号的账号标签
     *
     * @param accountIds
     * @return
     */
    Map<Integer, List<Integer>> queryAccountLabelBindingMap(List<Integer> accountIds);

    LabelBindingDto getBindingInfoByAccountId(Integer accountId);

    /**
     * 根据账号id获取绑定的标签信息（区分自动打标，手动打标）
     *
     * @param accountId 账号id
     * @return 绑定的标签信息
     */
    AccountLabelBindingDto getLabelBindingInfoByAccountId(Integer accountId);

    Integer createAccountLabel(AccountLabelBaseDto dto);

    Map<Integer, Integer> queryAllLabel();

    // 查询label
    List<AccountLabelBaseDto> queryLabels(QueryAccountLabelBaseDto queryParam);
    List<AccountLabelBaseDto> queryLabelsForSlave(QueryAccountLabelBaseDto queryParam);

    // 查询所有有效的label
    List<AccountLabelBaseDto> getAllLabels();

    // 帐号绑定标签,更新autoUpdate
    void bindAccountLabel(Operator operator, LabelBindingDto binding, ModifyType modifyType);

    /**
     * 账号绑定标签（区分自动打标，手动打标）
     *
     * @param operator   操作人
     * @param binding    绑定信息
     * @param modifyType 账号操作类型
     */
    void bind(Operator operator, AccountLabelBindingDto binding, ModifyType modifyType);

    /**
     * 批量上传手动账号标签
     *
     * @return
     */
    String batchUploadAccountLabels(AccountLabelBatchUploadBindDto batchUploadBindDto);

    Integer updateAccountLabel(AccountLabelBaseDto dto);

    /**
     * 标签列表
     *
     * @param queryParam
     * @return
     */
    PageResult<AccountLabelDto> queryAccountLabel(QueryAccountLabelDto queryParam);

    List<AccountLabelBaseDto> getMatchedLabelByAccount(AccountDto dto);

    AccountLabelMatchingDto getMatchedLabelByAccountId(Integer accountId);

    /**
     * 根据账号id获取账号和标签的绑定关系
     *
     * @param accountId 账号id
     * @return
     */
    AccountLabelBindDto getBindByAccountId(Integer accountId);

    /**
     * 根据工单id获取账号和标签的绑定关系
     *
     * @param workOrderId 工单id
     * @return
     */
    AccountLabelBindDto getMatchedLabelByWorkOrderId(Integer workOrderId);

    AccountLabelBaseDto getAccountLabelInfoById(Integer labelId);

    Integer countAccByLabelId(Integer labelId);

    // 根据帐号ID，查询帐号绑定标签的日志
    PageResult<QueryAccountLabelLogDto> queryLog(Integer accountId, Integer page, Integer size) throws ServiceException;

    PageResult<AccountLabelLogDto> queryAccountLabelOpLog(Integer objId, Integer page, Integer size) throws ServiceException;

    /**
     * 获取批量下载模板
     *
     * @return
     */
    AttachmentResultDto queryAccountLabelBatchTemplate();

    List<Integer> queryUpdatingLabel(Timestamp mtime, List<Integer> status);

    AccountLabelAccreditUpdateResp updateAccredit(AccountLabelAccreditUpdateReq req);

    AccountLabelBatchUploadBindResp batchUploadLabels(AccountLabelBatchUploadBindDto batchUploadBindDto);

    UploadTaskDto queryUploadTaskInfo(String taskId);

    List<Integer> queryAccountIdsByLabel(Integer labelId);
}
