package com.bilibili.crm.platform.api.account.dto.bizaccount;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/10/3 下午4:59
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CascadeBizAccountDto {


    /**
     * 账号id, bid
     */
    private Integer bid;

    /**
     * 账号类型
     */
    private String bidAccountType;

    /**
     * 绑定手机号
     */
    private String phoneNum;

    /**
     * 创建日期
     */
    private String ctime;

    /**
     * 对公验证是否完成
     */
    private String isPublicAuthPass;
}
