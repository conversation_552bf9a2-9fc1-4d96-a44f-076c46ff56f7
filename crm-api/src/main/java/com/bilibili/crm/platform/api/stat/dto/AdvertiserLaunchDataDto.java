package com.bilibili.crm.platform.api.stat.dto;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-09-24
 **/
@Data
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
public class AdvertiserLaunchDataDto implements Serializable {

    private static final long serialVersionUID = 7469836594241538902L;

    /**
     * 日期
     */
    private Timestamp date;

    /**
     * 日期范围 字符串
     */
    @ExcelProperty("日期")
    private String dateStr;
    /**
     * 广告主ID
     */
    @ExcelProperty("广告主账号ID")
    private Integer accountId;

    /**
     * 依赖的代理商 id
     */
    private Integer agentId;

    /**
     * 公司组id
     */
    private Integer companyGroupId;

    /**
     * 公司组名称
     */
    private String companyGroupName;

    /**
     * 售卖类型
     */
    private Integer salesType;

    /**
     * 产品大类
     */
    private Integer productType;
    private String productTypeDesc;

    /**
     * 现金总消耗
     */
    private BigDecimal totalCashConsume;

    /**
     * 返货总消耗
     */
    private BigDecimal totalRedPacketConsume;

    /**
     * 专项返货总消耗
     */
    private BigDecimal totalSpecialRedPacketConsume;

    /**
     * 授信总消耗
     */
    private BigDecimal totalCreditConsume;

    /**
     * 个人起飞返货总消耗
     */
    private BigDecimal totalFlySupportConsume;

    /**
     * 总体消耗
     */
    private BigDecimal totalConsume;

    /**
     * 三连投放总消耗，单位（元）
     */
    private BigDecimal sanLianLaunchTotalConsume;

    /**
     * 曝光数
     */
    private Long showCount;

    /**
     * 点击量
     */
    private Long clickCount;

    private BigDecimal ctr;

    private BigDecimal cpc;

    private BigDecimal ecpm;
    /**
     * 现金消耗(元)
     */
    private BigDecimal cashConsume;
    /**
     * 返货消耗(元)
     */
    private BigDecimal redPacketConsume;

    /**
     * 专项返货消耗(元)
     */
    private BigDecimal specialRedPacketConsume;

    /**
     * 线索通实扣
     */
    private BigDecimal cluePassDeduct;

    /**
     * 授信消耗（元）
     */
    private BigDecimal creditConsume;

    /**
     * 个人起飞返货消耗（元）
     */
    private BigDecimal flySupportConsume;

    /**
     * 粉丝关注增长数
     */
    private Integer fanFollowCount;
    /**
     * 涨粉成本（元）
     */
    private BigDecimal fansIncreaseCost;

    private Integer onlineCreative;

    private Integer newCreative;

    /**
     * 判断该对象是否空对象, 在比较空对象的时候不允许属性为null
     *
     * @return 空对象  true, otherwise false
     */
    public boolean isEmptyObject() {
        if (totalCashConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (totalRedPacketConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (totalSpecialRedPacketConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (totalConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (!showCount.equals(0L)) {
            return false;
        }
        if (!clickCount.equals(0L)) {
            return false;
        }
        if (ctr.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (cpc.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (ecpm.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (cashConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (redPacketConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (specialRedPacketConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (cluePassDeduct.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (creditConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        if (totalCreditConsume.compareTo(BigDecimal.ZERO) != 0) {
            return false;
        }
        return true;
    }

}
