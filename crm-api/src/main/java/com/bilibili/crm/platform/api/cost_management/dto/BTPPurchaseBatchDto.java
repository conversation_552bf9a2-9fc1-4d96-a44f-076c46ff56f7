package com.bilibili.crm.platform.api.cost_management.dto;

import com.fasterxml.jackson.annotation.JsonProperty;
import lombok.*;

import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BTPPurchaseBatchDto {
    /**
     * 主站项目号
     */
    private String businessCode;
    /**
     * btp项目号
     */
    private String projectCode;
    /**
     * btp项目名（共用主站项目名）
     */
    private String projectName;
    /**
     * 采购申请单code
     */
    private String code;
    /**
     * 采购申请人ad
     */
    private String creator;
    /**
     * 采购申请人部门
     */
    private String creatorDeptName;
    /**
     * 采购申请版本号
     */
    private String prVersion;
    /**
     * BTP采购审批号
     */
    private String flowOrderId;
    /**
     * 采购申请状态 0：待提交，1：审批中，2：执行中，3：废弃，4：关闭
     */
    private Integer status;
    /**
     * 采购申请单是否在审批中，添加调整审批中状态（0：没有审批，1：审批中，2：变更审批中）
     */
    private Integer prInFlow;
    /**
     * 采购申请的链接
     */
    private String flowOrderUrl;
    /**
     * 供应商
     */
    private List<SupplierDto> suppliers;

    /**
     * 采购明细行
     */
    @JsonProperty("details")
    private List<BTPPurchaseDetailDto> btpPurchaseDetails;

    private String approvedTime;

    @Data
    @Builder
    @AllArgsConstructor
    @NoArgsConstructor
    public static class SupplierDto {
        private String code;

        private String name;
    }

    @Getter
    public enum Status {
        WAITING_FOR_COMMIT(0, "待提交"),
        AUDITING(1, "审批中"),
        EXECUTING(2, "执行中"),
        ABORTED(3, "废弃"),
        CLOSED(4, "关闭");

        private final int code;
        private final String description;

        Status(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static Status getByCode(int code) {
            for (Status s : Status.values()) {
                if (s.getCode() == code) {
                    return s;
                }
            }

            return null;
        }
    }

    @Getter
    public enum PrInFlow {
        NONE_AUDIT(0, "没有审批"),
        AUDITING(1, "审批中"),
        EDITING_AUDITING(2, "变更审批中");

        private final int code;
        private final String description;

        PrInFlow(int code, String description) {
            this.code = code;
            this.description = description;
        }

        public static PrInFlow getByCode(int code) {
            for (PrInFlow p : PrInFlow.values()) {
                if (p.code == code) {
                    return p;
                }
            }

            return null;
        }
    }
}