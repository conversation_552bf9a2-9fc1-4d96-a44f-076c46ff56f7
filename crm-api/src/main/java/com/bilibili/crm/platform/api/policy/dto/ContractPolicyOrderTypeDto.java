package com.bilibili.crm.platform.api.policy.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * 政策合同下单方式
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractPolicyOrderTypeDto {

    /**
     * 政策id
     */
    private Integer policyId;

    /**
     * 流程实例id
     */
    private String processInstanceId;

    /**
     * 是否直签(0-代理，1-直签)
     */
    private Integer isDirectSign;

    /**
     * 代理商账号ids
     */
    private List<Integer> agentAccountIds;
    /**
     * 代理商名字拼接(英文逗号)
     */
    private String agentsName;

    /**
     * 直客销售ids(应为逗号分隔)
     */
    private List<Integer> directSaleIds;
    private String directSaleNames;

    /**
     * 渠道销售ids(英文逗号分隔)
     */
    private List<Integer> channelSaleIds;
    private String channelSaleNames;

    /**
     * 执行媒介
     */
    private List<String> execMediumList;
    /**
     * 执行媒介(名称按逗号分隔了)
     */
    private String execMediums;

    /**
     * 硬广收入
     */
    private BigDecimal hardAdIncome;

    /**
     * 非标收入 （原非标/直播硬广收入 ,直播硬广单独拆为liveHardIncome）
     */
    private BigDecimal notStandIncome;

    /**
     * 直播商单收入
     */
    private BigDecimal liveBroadcastCommercialIncome;

    /**
     * 花火商单收入
     */
    private BigDecimal pickupCommercialIncome;

    /**
     * 直播硬广收入
     */
    private BigDecimal liveHardIncome;

    /**
     * 预付费商单收入
     */
    private BigDecimal prePickupIncome;
}
