package com.bilibili.crm.platform.soa.dto.wxapp;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-02-27 17:05:32
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SellGoodResourceRatioDto implements Serializable {

    /**
     * 商品来源名称
     */
    private String goodResourceName;

    /**
     * 商品来源日收入
     */
    private BigDecimal resourceDayIncome;

    /**
     * 商品来源日收入占比
     */
    private BigDecimal resourceDayIncomeRatio;
}
