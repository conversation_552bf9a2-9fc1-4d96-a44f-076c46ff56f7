package com.bilibili.crm.platform.api.achievement.dto;

import com.bilibili.crm.platform.api.settle.BillDetailReport;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/9
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchieveContractSupportDto {

    private List<BillDetailReport> contractAllBrandUnChecked;
    private List<BillDetailReport> contractAllPickUpUnChecked;
    private List<BillDetailReport> contractAllBrandRecord;
    private List<BillDetailReport> contractAllPickUpRecord;
    private List<BillDetailReport> contractAllUnRecord;
}
