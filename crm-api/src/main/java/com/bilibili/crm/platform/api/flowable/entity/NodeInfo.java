package com.bilibili.crm.platform.api.flowable.entity;

import com.bilibili.crm.platform.api.flowable.enums.NodeStateEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * 审批流节点信息
 *
 * <AUTHOR>
 * @date 2023/3/8 16:09
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NodeInfo {

    /**
     * 流程实例ID
     */
    private String procInstId;

    /**
     * flowable 任务ID
     */
    private String taskId;

    /**
     * 任务名称，用于业务判断节点类型
     */
    private String taskName;

    /**
     * 审批人
     */
    private String assignee;

    /**
     * 审批候选人
     */
    private List<String> candidates;

    /**
     * 节点评价
     */
    private String comment;

    /**
     * 节点状态
     */
    private NodeStateEnum state;

    /**
     * 是否是发起节点
     */
    private boolean isStarter;

    /**
     * 开始时间
     */
    private LocalDateTime startTime;

    /**
     * 结束时间
     */
    private LocalDateTime endTime;
}
