package com.bilibili.crm.platform.api.achievement.dto;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum RtbComposition {
    RTB(0, "必选"),
    BSI_FLY(1, "商业起飞"),
    ADX(2, "ADX"),
    PICK_UP(3, "合约广告"),
    DPA(4, "DPA"),
    CPT(5, "CPT"),
    GD(6, "GD"),
    SSA(8, "闪屏"),
    NON_STANDARD(9, "非标"),
    SEARCH_CPT(10, "搜索品专"),
    OTT_TOP_VIEW(11, "OTT"),
    LIVE(12, "直播类订单"),
    PICK_UP_CONFIRM(13, "花火商单(已确认)"),
    LIVE_PICK_UP(14, "直播商单"),
    BUSINESS_FLY_POST_PAY(15, "商业起飞(后付费)"),
    BUSINESS_FLY_ADVANCE_PAY(16, "商业起飞(预付费)"),
    CONTENT_FLY(17, "内容起飞"),
    PERSON_FLY(18, "个人起飞"),
    CPC(19,"CPC"),
    CPM(20,"竞价CPM"),
    TOP_VIEW(21, "TopView"),

    UNKNOWN(-1, "未知"),
    ;

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static RtbComposition getByCode(int code) {
        for (RtbComposition bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }
}
