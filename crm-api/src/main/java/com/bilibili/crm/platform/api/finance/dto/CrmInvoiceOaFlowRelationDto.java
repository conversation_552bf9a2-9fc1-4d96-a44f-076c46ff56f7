package com.bilibili.crm.platform.api.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2021/5/13
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrmInvoiceOaFlowRelationDto {
    /**
     * 开票业务类型：2-充值开票，3-花火订单开票 0-未知
     */
    private Integer invoiceBizType;

    /**
     * crm oa流程id
     */
    private Integer crmOaFlowId;

    /**
     * oa 开票 id
     */
    private Integer oaInvoiceId;

    /**
     * 关联id：充值id，花火订单id
     */
    private Integer relId;

    private Long amount;

}
