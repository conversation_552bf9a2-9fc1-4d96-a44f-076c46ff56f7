package com.bilibili.crm.platform.api.order.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.order.dto.NewOrderBean;
import com.bilibili.crm.platform.api.order.dto.OrderDto;
import com.bilibili.crm.platform.api.order.dto.UpdateOrderBean;
import com.bilibili.crm.platform.common.CrmOrderType;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2019/11/13
 * 订单动作、行为的接口定义
 **/
public interface IOrderActionService {

    Integer createOrder(NewOrderBean newOrderDto, Operator operator);

    // 更新订单
    void updateOrder (Operator operator, UpdateOrderBean updateOrder, CrmOrderType crmOrderType);

    // 根据合同编号查找crm订单id
    List<Integer> getOrderIdByContractNum(Long contractNumber);

    // 废弃订单
    void deleteOrder (Operator operator, Integer orderId);

    // 批量废弃订单
    void deleteOrder(Operator operator, List<Integer> orderIds, List<OrderDto> cannotDeleteOrders);

    // 推审
    void toAudit (Operator operator, Integer orderId);

    // 批量推审
    void toAudit (Operator operator, List<Integer> orderIds);

    // 审核撤销
    void auditRevoke (Operator operator, Integer orderId);

    // 批量审核撤销
    void auditRevoke (Operator operator, List<Integer> orderIds);

    // 审核通过
    void auditSuccess (Operator operator, Integer orderId);

    // 批量审核通过
    void auditSuccess (Operator operator, List<Integer> orderIds);

    // 审核拒绝
    void auditReject (Operator operator, Integer orderId, String remark);

    // 批量审核拒绝
    void auditReject (Operator operator, List<Integer> orderIds, String remark);

    // 更新折扣信息
    void updateDiscount (Operator operator, Integer orderId, BigDecimal discount);

    void batchDeleteOrder(Operator operator, List<Integer> orderIds);
}
