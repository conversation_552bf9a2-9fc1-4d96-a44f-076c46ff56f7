package com.bilibili.crm.platform.api.policy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 流程节点状态: 1-未处理 2-处理中 3-已处理
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:52
 */
@AllArgsConstructor
public enum FlowNodeStatusEnum {

    WAIT(1, "未处理"),
    DOING(2, "处理中"),
    DONE(3, "已处理"),

    UNKNOWN(0, "未知"),
    ;

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static FlowNodeStatusEnum getByCode(Integer code) {
        for (FlowNodeStatusEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }
}
