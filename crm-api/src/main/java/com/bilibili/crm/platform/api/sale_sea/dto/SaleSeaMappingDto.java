package com.bilibili.crm.platform.api.sale_sea.dto;

import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaMappingType;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2020/6/2 15:11
 * 创建销售公私海绑定关系的实体
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleSeaMappingDto {

    /**
     * 公私海 产品-销售名单
     */
    private Map<SaleSeaProductCategory, Map<BiliUserType, List<Integer>>> productSaleMap;

    /**
     * 绑定类型
     */
    private SaleSeaMappingType mappingType;

    /**
     * 绑定ID
     */
    private Integer mappingId;

    public static SaleSeaMappingDto empty () {
        return SaleSeaMappingDto.builder().build();
    }

}
