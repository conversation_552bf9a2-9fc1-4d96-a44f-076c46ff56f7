package com.bilibili.crm.platform.api.achievement.dto;

import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-04-17 19:57:30
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class WxAppSaleTimeDto implements Serializable {

    /**
     * 日报 昨天 / 实时 当天
     */
    private Timestamp baseTime;

    private Boolean isRealTime;

    private Timestamp monthBegin;

    List<Timestamp> monthEachDays;

    private Timestamp quarterFirstDate;

    private long lastQuarterBegin;

    private long yoyQuarterBegin;

    private long quarterGap;

    private SaleDto sale;

    private Integer isSaleGroup;

    private Integer saleGroupId;

    private Timestamp weekStart;

    private Timestamp lastWeekStart;

    private Timestamp lastWeekEnd;

    private WxAppSaleDataDto wxAppSaleDataDto;

}
