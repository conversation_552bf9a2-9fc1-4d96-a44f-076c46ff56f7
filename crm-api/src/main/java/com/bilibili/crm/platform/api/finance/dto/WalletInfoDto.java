package com.bilibili.crm.platform.api.finance.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by fanwen<PERSON> on 16/9/30.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WalletInfoDto {
    private BigDecimal cash;
    private BigDecimal totalCashRecharge;
    private List<WalletLogDto> walletLogDtoList;
}
