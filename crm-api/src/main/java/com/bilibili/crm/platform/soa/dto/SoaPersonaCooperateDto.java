package com.bilibili.crm.platform.soa.dto;

import com.bilibili.crm.platform.api.consume.dto.*;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/4/2 4:25 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SoaPersonaCooperateDto implements Serializable {

//    @ApiModelProperty("收入趋势")
//    private List<PersonaCooperateTrend> trend;
//    @ApiModelProperty("收入概览")
//    private PersonaCooperateSummary summary;
//    @ApiModelProperty("合作详情")
    List<ConsumeExtendDto> cooperateProduct;
}
