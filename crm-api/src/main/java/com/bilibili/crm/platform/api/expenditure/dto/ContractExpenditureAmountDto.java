package com.bilibili.crm.platform.api.expenditure.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractExpenditureAmountDto {

    private BigDecimal expenditureAmount;

    private BigDecimal settleAmount;

    private String rate;
}
