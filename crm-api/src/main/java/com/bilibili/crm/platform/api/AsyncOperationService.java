package com.bilibili.crm.platform.api;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.enums.AsyncOperationTypeEnum;
import com.bilibili.crm.platform.api.dto.AsyncOperationResultDto;

import java.util.function.Consumer;
import java.util.function.Supplier;

public interface AsyncOperationService {

    AsyncOperationResultDto<Void> handleAsyncExec(Operator operator, AsyncOperationTypeEnum asyncOperationTypeEnum,
                                                  String bizNo, Consumer<Void> checkConsumer,
                                                  Supplier<Integer> asyncSupplier);

    <T> AsyncOperationResultDto<T> handleSingleExec(Operator operator, AsyncOperationTypeEnum asyncOperationTypeEnum,
                                                    String bizNo, Supplier<T> supplier);

    AsyncOperationResultDto<Void> queryAsyncExecResult(long logId);
}
