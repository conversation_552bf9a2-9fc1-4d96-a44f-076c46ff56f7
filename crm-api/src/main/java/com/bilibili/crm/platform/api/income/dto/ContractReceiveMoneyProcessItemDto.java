package com.bilibili.crm.platform.api.income.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/7/1 6:04 下午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ContractReceiveMoneyProcessItemDto implements Serializable {

    private static final long serialVersionUID = 3286939833666096407L;
    /**
     * 该年的所有应收款(分)
     */
    private BigDecimal allReceivableAmount;

    /**
     * 该年的所有实际收款
     */
    private BigDecimal allRealReceiveAmount;

    /**
     * 该年的已开票的合同的所有应收款
     */
    private BigDecimal hasBillReceivableAmount;

    /**
     * 该年的已开票的合同的实际应收款
     */
    private BigDecimal hasBillRealReceiveAmount;

    /**
     * 该年的返点率
     */
    private BigDecimal rebateRate;
}
