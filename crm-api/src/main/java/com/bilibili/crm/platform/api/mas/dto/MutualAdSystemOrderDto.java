package com.bilibili.crm.platform.api.mas.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-10-17
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class MutualAdSystemOrderDto {
    /**
     * 合同ID
     */
    private Integer crmContractId;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 订单名称
     */
    private String orderName;

    /**
     * 订单金额（分）
     */
    private Long amount;
    /**
     * 开始时间
     */
    private Timestamp beginTime;

    /**
     * 结束时间
     */
    private Timestamp endTime;

    /**
     * 项目/节目名称
     */
    private String projectItemName;

    /**
     *资源类型 0其他 1内部、2售卖、3配送、4补量
     */
    private Integer resourceType;

    /**
     * 备注信息
     */
    private String remark;

    /**
     * 项目/节目资源包id
     */
    private Integer projectItemId;
    /**
     *招商类型
     */
    private Integer attractInvestmentType;

    // 订单标签
    private Integer orderTag;
}
