package com.bilibili.crm.platform.api.achievement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RtbReportAchievement {

    private String date;

    private Timestamp date_time;

    private Integer account_id;

    private String account_name;

    private String agent_name;

    private Integer agent_account_id;

    private String category_first_name;

    private String category_second_name;

    private String biz_industry_category_first_name;

    private String biz_industry_category_second_name;

    private String product_type;

    private String straight_name;


    /**
     * 直客销售
     */
    private List<Integer> straightSaleId;

    /**
     * 渠道销售
     */
    private List<Integer> channelSaleId;

    private String straight_name_1;

    private String straight_name_2;

    private String straight_name_3;

    private String straight_name_4;

    private String channel_name;

    private String channel_name_1;

    private String channel_name_2;

    private String channel_name_3;

    private String channel_name_4;


    private String straight_name_group;

    private String straight_name_second_group;

    private String channel_name_group;

    private String channel_name_second_group;

    private String operate_name;

    private BigDecimal total_consume;

    private BigDecimal cash;

    private BigDecimal credit;

    private BigDecimal red_packet;

    private BigDecimal special_red_packet;

    private BigDecimal show_amount;

    private BigDecimal click_amount;

    private BigDecimal ctr;

    private BigDecimal cpc;

    private BigDecimal ecpm;
    /**
     * 个人起飞扶持返货消耗(元)4位小数
     */
    private BigDecimal fly_support_amount;
    //资源平台
    private String platform;
    //资源位
    private String resource;

    private String product_line_name;

    private String account_department_name;

    private Integer agent_id;

    private Integer online_creative;

    private Integer new_creative;


    /**
     * 品牌ID
     */

    private Integer product_id;
    /**
     * 品牌
     */
    private String product_name;

    /**
     * 集团ID
     */
    private Integer group_id;
    /**
     * 集团
     */
    private String group_name;

    /**
     * 广告主客户ID
     */
    private Integer account_customer_id;
    /**
     * 广告主客户ID
     */
    private String account_customer_name;

    /**
     * 代理商客户ID
     */
    private Integer agent_customer_id;
    /**
     * 代理商客户名称
     */
    private String agent_customer_name;

    /**
     * 代理商集团ID
     */
    private Integer agent_account_group_id;
    /**
     * 代理商集团名称
     */
    private String agent_account_group_name;


    /**
     * 是否带货
     */
    private String is_take_goods;

    private Long clue_pass_task_id;

    private String clue_pass_task_start_time;

    private String clue_pass_task_end_time;

    private Integer is_support_pick_up;

    private String accountUnitedFirstIndustryName;

    private String accountUnitedSecondIndustryName;

    private String accountUnitedThirdIndustryName;

    private String customerUnitedFirstIndustryName;

    private String customerUnitedSecondIndustryName;

    private String customerUnitedThirdIndustryName;
}
