package com.bilibili.crm.platform.api.non.standard.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-11-23 10:52:58
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectSeatDto {


    /**
     * 席位类型 0 其他 1 特约赞助 2 行业赞助 3 指定合作
     */
    private Integer seatType;

    /**
     * 核价金额
     */
    private BigDecimal seatMoney;
}
