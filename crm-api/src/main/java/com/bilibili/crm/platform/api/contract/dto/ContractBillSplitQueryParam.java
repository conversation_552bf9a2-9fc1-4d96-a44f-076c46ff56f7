package com.bilibili.crm.platform.api.contract.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractBillSplitQueryParam implements Serializable {

    private static final long serialVersionUID = 2332821639330645723L;

    private Timestamp processDate;

    private String contractName;

    private Long contractNumber;

    private String orderName;

    private Integer orderId;

    /**
     * 账单来源，枚举值：全部-0；账单调整-1；合同实结-2
     */
    private Integer billSource;

    /**
     * 拆分类型，0-系统拆分；1-手动拆分；
     * {@link com.bilibili.crm.platform.api.enums.BillSplitTypeEnum}
     */
    private Integer bizType;
}
