package com.bilibili.crm.platform.api.policy;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.platform.api.policy.dto.BrandCustomerPolicyDto;
import com.bilibili.crm.platform.api.policy.dto.BrandCustomerPolicyQueryDto;

import java.util.List;

/**
 * 品牌直客政策
 */
public interface IBrandPolicyService {
    PageResult<BrandCustomerPolicyDto> queryPolicyPage(BrandCustomerPolicyQueryDto queryDto);

    List<BrandCustomerPolicyDto> queryPolicy(BrandCustomerPolicyQueryDto queryDto);

    BrandCustomerPolicyDto getPolicyById(Integer policyId);

    void updatePolicyStatus(Integer policyId, Integer status, Operator operator);

    void createPolicy(BrandCustomerPolicyDto brandCustomerPolicyDto, Operator operator);

    void editPolicy(BrandCustomerPolicyDto brandCustomerPolicyDto, Operator operator);
}
