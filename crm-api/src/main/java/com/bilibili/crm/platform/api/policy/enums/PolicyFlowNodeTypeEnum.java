package com.bilibili.crm.platform.api.policy.enums;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 节点业务类型: 1-提交申请，2-预审审核，3-加签审核
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:52
 */
@AllArgsConstructor
public enum PolicyFlowNodeTypeEnum {

    /**
     * BpmnXMLConstants.ELEMENT_EVENT_START
     */
    START(0, "startEvent", "开始节点"),

    APPLY(1, "policyCreate", "提交申请"),
    PRE_EXAMINE(2, "preExample", "预审审核"),
    ADD_SIGN_EXAMINE(3, "addSign", "加签审核"),
    TO_ARCHIVED(4, "toArchived", "等待归档"),
    OPERATION_REVIEW(6, "operationReview", "运营初审"),

    /**
     * BpmnXMLConstants.ELEMENT_EVENT_END
     */
    END(-2, "endEvent", "结束节点"),

    UNKNOWN(-1, "unknown", "未知"),

    //品牌返货 补充
    BRAND_RETURN_REGULAR_ONE(5, "regular1", "固定加签1"),
    BRAND_RETURN_REGULAR_TWO(5, "regular2", "固定加签2"),
    BRAND_RETURN_REGULAR(5, "regular", "固定加签"),
    ;

    @Getter
    private Integer code;
    @Getter
    private String name;
    @Getter
    private String desc;

    public static PolicyFlowNodeTypeEnum getByCode(Integer code) {
        for (PolicyFlowNodeTypeEnum bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }

    public static PolicyFlowNodeTypeEnum getByName(String nodeName) {
        for (PolicyFlowNodeTypeEnum bean : values()) {
            if (bean.getName().equals(nodeName)) {
                return bean;
            }
        }
        return UNKNOWN;
    }
}
