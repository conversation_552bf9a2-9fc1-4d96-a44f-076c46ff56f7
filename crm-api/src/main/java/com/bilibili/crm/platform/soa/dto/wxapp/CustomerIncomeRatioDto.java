package com.bilibili.crm.platform.soa.dto.wxapp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-02-07 20:31:52
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomerIncomeRatioDto implements Serializable {

    /**
     * 日期
     */
    private Timestamp date;

    /**
     * 一级行业id
     */
    private Integer categoryFirstId;


    /**
     * 行业名称
     */
    private String categoryName;

    /**
     * 行业收入
     */
    private BigDecimal categoryIncome;

    /**
     * 占比
     */
    private BigDecimal categoryIncomeRatio;
}
