package com.bilibili.crm.platform.api.effect.dto;

import com.bilibili.crm.platform.api.contract.dto.ContractFileOssDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-12-19 20:31:49
 * @description:
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class EffectBillDto {
    /**
     * 应收账单表主键Id
     */
    private Long effectBillId;

    /**
     * 账单月份
     */
    private Timestamp billStat;

    /**
     * 账号id
     */
    private Integer accountId;

    /**
     * 账号名称
     */
    private String accountName;

    /**
     * 广告主客户ID
     */
    private Integer customerId;

    /**
     * 广告主客户名称
     */
    private String customerName;

    /**
     * 代理商账户ID
     */
    private Integer agentId;

    /**
     * 代理商账户名称
     */
    private String agentName;

    /**
     * 代理商账户ID
     */
    private Integer agentAccountId;

    /**
     * 代理商账户名称
     */
    private String agentAccountName;

    /**
     * 代理商客户ID
     */
    private Integer agentCustomerId;

    /**
     * 代理商客户名称
     */
    private String agentCustomerName;

    /**
     * 应收账单来源类型 0 ADX 1 DPA 2 效果
     */
    private Integer incomeType;

    /**
     * ADX产品的DSP名称
     */
    private String dspNames;

    /**
     * ADX产品的DSP ID
     */
    private String bidders;

    /**
     * 广告主账户的所属品牌ID
     */
    private Integer productId;

    /**
     * 广告主账户的所属品牌名称
     */
    private String productName;

    /**
     * 广告主账户的所属集团ID
     */
    private Integer companyGroupId;

    /**
     * 广告主账户的所属集团名称
     */
    private String companyGroupName;

    /**
     * 广告主账户的所属一级行业
     */
    private String categoryFirstName;

    /**
     * 广告主账户的所属二级行业
     */
    private String categorySecondName;

    /**
     * 广告主账户的效果业绩归属销售
     */
    private String directBelongSales;


    /**
     * 代理商账户的效果业绩归属销售 名称
     */
    private String agentDirectBelongSales;

    /**
     * 代理商账户的广告运营 名称
     */
    private String agentOperatorBelongSales;


    /**
     * 账单状态 0 初试态 1 开票中 2 待回款 3 回款中 4 回款完成
     */
    private Integer billStatus;

    /**
     * 账单金额
     */
    private BigDecimal billAmount;

    /**
     * 实结金额
     */
    private BigDecimal closeAmount;

    /**
     * 账单已抵扣金额（单位 元）已抵扣金额等于该账单在应收抵扣明细里的抵扣金额之和
     */
    private BigDecimal hasDeductAmount;

    /**
     * 账单待抵扣金额（单位 元）待抵扣金额 = 账单金额-已抵扣金额
     */
    private BigDecimal toDeductAmount;

    private Timestamp closeTime;

    private Timestamp closeEnterTime;

    /**
     * 整体总消耗（单位 元）
     */
    private BigDecimal consumeAmount;

    /**
     * 现金总消耗（单位 元）
     */
    private BigDecimal cashConsume;

    /**
     * 授信总消耗（单位 分）
     */
    private BigDecimal creditConsume;

    /**
     * 返货总消耗（单位 元）
     */
    private BigDecimal redPacketConsume;

    /**
     * 专项返货总消耗（单位 元）
     */
    private BigDecimal specialRedPacketConsume;

    /**
     * 开票总金额（单位 元）
     */
    private BigDecimal invoiceAmount;

    /**
     * 折扣金额（单位 元）
     */
    private BigDecimal discountAmount;

    /**
     * 线上回款金额（单位 分）
     */
    private BigDecimal collectMoneyOnline;

    /**
     * 线下回款金额（单位 分）
     */
    private BigDecimal collectMoneyOffline;


    /**
     * 该账单关联的最新一个oa流程编号
     */
    private String oaFlowNo;

    /**
     * 该账单关联的最新一个oa流程标题
     */
    private String oaFlowTitle;

    /**
     * 该账单关联的最新一个oa流程编号的状态
     */
    private Integer oaFlowStatus;

    /**
     * 协议是否签署完成
     */
    private Integer dealSigned;

    private Integer relateAccountId;

    private String relateAccountName;

    private List<ContractFileOssDto> attachments;

    private String unitedFirstIndustryName;

    private String unitedSecondIndustryName;

    private String unitedThirdIndustryName;

}
