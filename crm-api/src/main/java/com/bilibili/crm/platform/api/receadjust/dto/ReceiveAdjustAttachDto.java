package com.bilibili.crm.platform.api.receadjust.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceiveAdjustAttachDto {
	/**
     * id
     */
    private Integer id;

    /**
     * 应收调整记录id
     */
    private Integer adjustRecordId;

    /**
     * 文件oss key
     */
    private String ossKey;

    /**
     * 文件名
     */
    private String fileName;
}
