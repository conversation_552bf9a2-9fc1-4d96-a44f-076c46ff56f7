package com.bilibili.crm.platform.app.biz.service;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.achievement.IAchievementContractService;
import com.bilibili.crm.platform.api.achievement.IAchievementPickUpService;
import com.bilibili.crm.platform.api.achievement.dto.AchievementContractUnCheckQuotaDto;
import com.bilibili.crm.platform.api.achievement.dto.AchievementDto;
import com.bilibili.crm.platform.api.achievement.dto.QueryAchieveDto;
import com.bilibili.crm.platform.api.dss.dto.DssCreativeCtrCompareDto;
import com.bilibili.crm.platform.api.dss.service.IDssAdOverviewService;
import com.bilibili.crm.platform.api.enums.PickupAchieveTypeEnum;
import com.bilibili.crm.platform.api.income.bo.customer.CategoryCustomerStatBo;
import com.bilibili.crm.platform.api.income.dto.*;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.api.income.exception.IncomeDataException;
import com.bilibili.crm.platform.api.income.service.IIncomeQueryService;
import com.bilibili.crm.platform.api.income.service.IWxAgentQueryService;
import com.bilibili.crm.platform.api.income.service.IWxCustomerQueryService;
import com.bilibili.crm.platform.api.income.service.IWxResourceQueryService;
import com.bilibili.crm.platform.api.white.IWxWriteListService;
import com.bilibili.crm.platform.api.white.dto.QueryWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteType;
import com.bilibili.crm.platform.app.api.EnterpriseWeChatAgentType;
import com.bilibili.crm.platform.app.api.client.IWxAppClient;
import com.bilibili.crm.platform.app.api.client.dto.response.WxAppMessageResponse;
import com.bilibili.crm.platform.app.api.client.dto.response.WxAppUserInfo;
import com.bilibili.crm.platform.app.api.service.IWxAppPaperService;
import com.bilibili.crm.platform.app.cache.WxAppCacheLoader;
import com.bilibili.crm.platform.app.cache.sale.WxAppSaleCacheWriterV2;
import com.bilibili.crm.platform.app.exception.WxAppCrmExceptionCode;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.achievement.config.beta.DepAchieveConfigBeta;
import com.bilibili.crm.platform.biz.service.income.handler.BrandIncomeCompositionQueryHandler;
import com.bilibili.crm.platform.biz.service.income.handler.RtbIncomeCompositionQueryHandler;
import com.bilibili.crm.platform.biz.service.income.service.WxProductServiceImpl;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.income_sale.manager.WxAppSaleHelper;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.income.IncomeType;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/4/2
 * 企业微信日报功能
 **/
@Slf4j
@Service
public class WxAppPaperServiceImpl implements IWxAppPaperService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IWxAppClient wxAppClient;
    @Autowired
    private WxAppCacheLoader cacheLoader;
    @Autowired
    private IIncomeQueryService incomeQueryService;
    @Autowired
    private IncomeUtil incomeUtil;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private IDssAdOverviewService dssAdOverviewService;
    @Autowired
    private RtbIncomeCompositionQueryHandler rtbIncomeCompositionQueryHandler;
    @Autowired
    private BrandIncomeCompositionQueryHandler brandIncomeCompositionQueryHandler;
    @Autowired
    private WxProductServiceImpl wxProductServiceImpl;
    @Autowired
    private IWxResourceQueryService wxResourceService;
    @Autowired
    private IWxCustomerQueryService wxCustomerQueryService;
    @Autowired
    private IWxAgentQueryService wxAgentQueryService;
    @Autowired
    private IAchievementPickUpService achievementPickUpService;
    @Autowired
    private IAchievementContractService achievementContractService;

    @Resource
    private WxAppSaleCacheWriterV2 wxAppSaleCacheWriterV2;

    private final static BigDecimal MILLION = new BigDecimal(1000000);

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    @Autowired
    private IWxWriteListService wxWriteListService;
    @Autowired
    private WxAppSaleHelper wxAppSaleHelper;
    @Override
    public void dailyPush(List<String> userList, String paperDate) {
        if (CollectionUtils.isEmpty(userList)) {
            userList = wxWriteListService.queryWhiteUserId(QueryWxAppWhiteDto.builder()
                    .whiteType(WxAppWhiteType.SUMMARY.getCode())
                    .isPush(IsValid.TRUE.getCode())
                    .whiteStatus(0)
                    .build());
        }
        if (StringUtils.isEmpty(paperDate)) {
            paperDate = Utils.getTimestamp2String(Utils.getYesteday());
        }
        String finalPaperDate = paperDate;
        try {
            String title = "商业中心通知";
            String welcome = "<div class=\"normal\">" + "您好</div>";
            String content = "<div class=\"highlight\">商业中心手机日报（" + finalPaperDate + "）已生成</div>";
            String description = welcome + content;
            String url = "https://cm.bilibili.com/crm-h5/paper/dss?paper_date=" + Utils.getBeginOfDay(Utils.getYesteday()).getTime();
            WxAppMessageResponse response = wxAppClient.sendTextCard(userList, title, description, url);
            String invalidUserList = response.getInvalidUser();
            if (!Strings.isNullOrEmpty(invalidUserList)) {
                log.info("WxPush invalid ,response {}", JSON.toJSONString(response));
            }
        } catch (WxAppException e) {
            log.error("WxAppPaperServiceImpl_dailyPush error userId is", e);
        }
    }

    @Override
    public void dailySalePush(List<String> userList, Timestamp day) {
        if (Objects.isNull(day)) {
            day = Utils.getYesteday();
        }
        if (CollectionUtils.isEmpty(userList)) {
            List<WxAppWhiteDto> wxAppWhiteDtos = wxWriteListService.queryWhite(QueryWxAppWhiteDto.builder()
                    .whiteType(WxAppWhiteType.SALE.getCode())
                    .isPush(IsValid.TRUE.getCode())
                    .ctimeEnd(Utils.getEndOfDay(day))
                    .whiteStatus(0)
                    .build());
            //正常销售
            List<String> normalSale = wxAppSaleHelper.getNormalSaleEmail();
            //过滤推送人员，管理员推，销售状态正常推
            List<WxAppWhiteDto> pushList = wxAppWhiteDtos.stream().filter(item -> {
                if (IsValid.TRUE.getCode().equals(item.getIsManager())) {
                    return true;
                }
                if (normalSale.contains(item.getUserName())) {
                    return true;
                }
                return false;
            }).collect(Collectors.toList());
            userList = pushList.stream().map(WxAppWhiteDto::getWorkNum).distinct().collect(Collectors.toList());
        }
        String finalPaperDate = Utils.getTimestamp2String(day);
        userList.forEach(userId -> {
            try {
                WxAppUserInfo userInfo = wxAppClient.getUserInfo(userId);
                String alias = userInfo.getAlias();
                String title = "商业中心通知";
                String welcome = "<div class=\"normal\">" + alias + " 您好</div>";
                String content = "<div class=\"highlight\">商业中心手机日报销售版（" + finalPaperDate + "）已生成</div>";
                String description = welcome + content;
                String url = "https://cm.bilibili.com/crm-h5/paper/sale";
                WxAppMessageResponse response = wxAppClient.sendTextCard(Lists.newArrayList(userId), title, description, url);
                log.info("WxAppPaperServiceImpl_dailySalePush, user {} ,response {}", alias, JSON.toJSONString(response));
                String invalidUserList = response.getInvalidUser();
                if (!Strings.isNullOrEmpty(invalidUserList)) {
                    log.info("WxPush invalid user {} ,response {}", alias, JSON.toJSONString(response));
                }
                try {
                    Thread.sleep(200L);
                } catch (InterruptedException e) {
                    log.info("WxPush InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }
            } catch (WxAppException e) {
                log.error("WxAppPaperServiceImpl_dailySalePush error userId is" + userId, e);
            }
        });
    }

    @Override
    public void dailySellGoodsPush(List<String> userList) throws WxAppException {

        if (!cacheLoader.isSellGoodsPaperStateAllReadyV2(Utils.getYesteday())) {
            log.error("dailySellGoodsPush isSellGoodsPaperStateAllReadyV2 delay ! attention please ");
            throw new WxAppException(WxAppCrmExceptionCode.PAPER_STATE_ERROR);
        }

        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        userList.forEach(userId -> {
            try {
                WxAppUserInfo userInfo = wxAppClient.getUserInfo(userId);
                String paperDate = Utils.getTimestamp2String(Utils.getYesteday());

                String alias = userInfo.getAlias();
                String title = "商业中心通知";
                String welcome = "<div class=\"normal\">" + alias + " 您好</div>";
                String content = "<div class=\"highlight\">带货日报（" + paperDate + "）已生成</div>";
                String description = welcome + content;

                String url = "http://cm.bilibili.com/ldad/daily-paper/daily.html#/goods" + "?tenant_code=CRM&paper_date=" + Utils.getYesteday().getTime();

                WxAppMessageResponse response = wxAppClient.sendTextCard(Lists.newArrayList(userId), title, description, url);

                log.info("WxPush user {} ,response {}", alias, JSON.toJSONString(response));

                String invalidUserList = response.getInvalidUser();
                if (!Strings.isNullOrEmpty(invalidUserList)) {
                    log.info("WxPush invalid user {} ,response {}", alias, JSON.toJSONString(response));
                    Cat.logEvent("WX_APP_SELL_GOODS_PUSH_FAIL", "dailyPush", "failed", invalidUserList);
                }

                try {
                    Thread.sleep(500L);
                } catch (InterruptedException e) {
                    log.info("WxPush InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }

            } catch (WxAppException e) {
                Cat.logError("WxAppPaperServiceImp.dailyPush error userId is" + userId, e);
            }
        });
    }

    @Override
    public void dailyInvestmentPush(List<String> userList) throws WxAppException {
/*
        if (!cacheLoader.isInvestmentStateAllReady(Utils.getYesteday())) {
            log.error("dailyInvestmentPush isInvestmentStateAllReady delay ! attention please ");
            throw new WxAppException(WxAppCrmExceptionCode.PAPER_STATE_ERROR);
        }
*/
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        userList.forEach(userId -> {
            try {
                WxAppUserInfo userInfo = wxAppClient.getUserInfo(userId);
                String paperDate = Utils.getTimestamp2String(Utils.getYesteday());

                String alias = userInfo.getAlias();
                String title = "商业中心通知";
                String welcome = "<div class=\"normal\">" + alias + " 您好</div>";
                String content = "<div class=\"highlight\">招商项目看板（" + paperDate + "）已生成</div>";
                String description = welcome + content;

                String url = "https://cm.bilibili.com/ldad/opportunity/main.html#/investment";

                WxAppMessageResponse response = wxAppClient.sendTextCard(EnterpriseWeChatAgentType.CRM_DATA_AGENT.getCode(), Lists.newArrayList(userId), title, description, url);

                log.info("dailyInvestmentPush user {} ,response {}", alias, JSON.toJSONString(response));

                String invalidUserList = response.getInvalidUser();
                if (!Strings.isNullOrEmpty(invalidUserList)) {
                    log.info("WxPush invalid user {} ,response {}", alias, JSON.toJSONString(response));
                    Cat.logEvent("WX_APP_INVESTMENT_PUSH_FAIL", "dailyPush", "failed", invalidUserList);
                }

                try {
                    Thread.sleep(500L);
                } catch (InterruptedException e) {
                    log.info("dailyInvestmentPush InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }

            } catch (WxAppException e) {
                Cat.logError("WxAppPaperServiceImp.dailyInvestmentPush error userId is" + userId, e);
            }
        });
    }

    @Override
    public BigDecimal getYesterdayIncome(TeamType team, Timestamp begin, Timestamp end) {
        List<AdIncome> incomeList = incomeQueryService.query(IncomeQueryParam.builder()
                .team(team)
                .dateBegin(begin)
                .dateEnd(end)
                .isYesterday(true)
                .build());

        return incomeList.stream().map(AdIncome::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }


    @Override
    public BigDecimal getIncome(TeamType team, Timestamp begin, Timestamp end) {

        List<AdIncome> incomeList = incomeQueryService.query(IncomeQueryParam.builder()
                .team(team)
                .dateBegin(begin)
                .dateEnd(end)
                .build());
        List<AdIncome> personalFlyList = incomeList.stream().filter(a -> Objects.nonNull(a.getIncomeComposition()) && a.getIncomeComposition().equals(IncomeComposition.PERSON_FLY)).collect(Collectors.toList());
        BigDecimal personalFlyIncome = personalFlyList.stream().map(AdIncome::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal otherIncome = incomeList.stream().filter(a -> Objects.isNull(a.getIncomeComposition()) || !a.getIncomeComposition().equals(IncomeComposition.PERSON_FLY)).map(AdIncome::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        log.info("getIncome:personalFlyIncome:{}, otherIncome:{}", personalFlyIncome, otherIncome);
        personalFlyIncome = wxAppSaleCacheWriterV2.calPersonal(personalFlyIncome, end);
        return personalFlyIncome.add(otherIncome);
    }

    @Override
    public BigDecimal getIncomeComposition(TeamType team, IncomeComposition composition, Timestamp date) {

        List<AdIncome> incomeList = incomeQueryService.query(IncomeQueryParam.builder()
                .team(team)
                .dateBegin(Utils.getBeginOfDay(date))
                .dateEnd(Utils.getEndOfDay(date))
                .composition(Objects.isNull(composition) ? Collections.emptyList() : Lists.newArrayList(composition))
                .build());

        return incomeList.stream().map(AdIncome::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @Override
    public BigDecimal getBrandBsiOpportunityQuarterAmount() {

        return brandIncomeCompositionQueryHandler.getBrandBsiOpportunityQuarterAmount();
    }

    @Override
    public RtbDayIncome getRtbRealTimeIncome() {

        return rtbIncomeCompositionQueryHandler.getRtbRealTimeIncome();
    }

    @Override
    public RtbTrend getRtbTrendIncome(Integer type, Timestamp base) {
        RtbTrend rtbTrend = rtbIncomeCompositionQueryHandler.getRtbTrend(IncomeQueryParam.builder()
                .team(TeamType.RTB)
                .dateBegin(incomeTimeUtil.getYesterdayBegin(base))
                .dateEnd(incomeTimeUtil.getYesterdayEnd(base))
                .trendType(type)
                .build());
        return rtbTrend;
    }

    /**
     * 核心广告资源位使用率
     */
    @Override
    public BigDecimal getLocationComposition(SourceLocation location, LocationComposition composition, Timestamp date) throws WxAppException {
        BigDecimal adCoreComposition = dssAdOverviewService.getLocationComposition(location, composition, date);
        BigDecimal pv = incomeUtil.divide(adCoreComposition, MILLION, 2);
        return pv;
    }

    /**
     * 产品库存
     */
    @Override
    public BigDecimal getProductPv(Timestamp begin, Timestamp end, ProductIncomeComposition composition) throws WxAppException {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();

        try {
            List<AdIncome> incomes = wxProductServiceImpl.queryAdPv(composition, param);
            long sum = incomes.stream().mapToLong(AdIncome::getShowCount).sum();
            LOGGER.info("Product getProductPv {}  date: {} income:{}", composition.name(), end, BigDecimal.valueOf(sum));
            return BigDecimal.valueOf(sum);

        } catch (IncomeDataException e) {
            throw new WxAppException(WxAppCrmExceptionCode.PAPER_REPORT_STOCK_DATA_ZERO);
        }
    }

    /**
     * 产品ECPM,CTR
     */
    @Override
    public AdIncome getProductQuota(Timestamp begin, Timestamp end, ProductIncomeComposition composition) throws IncomeConditionInvalidException {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        AdIncome adIncome = wxProductServiceImpl.queryAdQuota(composition, param);

        AdIncome result = null != adIncome ? adIncome : new AdIncome(begin, BigDecimal.ZERO, 0L, 0L);
        LOGGER.info("Product queryAdQuota  {}  date: {} income:{}", composition.name(), end.toString(), adIncome.toString());
        return result;
    }

    /**
     * 产品算法ctr
     */
    @Override
    public DssCreativeCtrCompareDto getProductCreativeCtr(Timestamp begin, Timestamp end) {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();

        return wxProductServiceImpl.queryAdCreativeCtr(param);
    }

    /**
     * 资源-招商
     */
    @Override
    public List<DepIncome> getResourceInvite(Timestamp begin, Timestamp end) {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        return wxResourceService.queryResourceInviteDepList(param);
    }

    /**
     * 资源-UP主,硬广
     */
    @Override
    public BigDecimal queryResourceIncome(Timestamp begin, Timestamp end, ResourceIncomeComposition composition) throws IncomeConditionInvalidException {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        return wxResourceService.queryResourceIncome(composition, param);
    }

    /**
     * 品牌ECPM,CTR
     */
    @Override
    public AdIncome getResourceBrandQuota(Timestamp begin, Timestamp end, ResourceIncomeComposition composition) throws IncomeConditionInvalidException {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        AdIncome adIncome = wxResourceService.queryResourceBrandQuota(composition, param);
        LOGGER.info("Resource getResourceBrandQuota {} date:{},adIncome:{}", composition.name(), param.getDateEnd(), adIncome.toString());
        AdIncome result = null != adIncome ? adIncome : new AdIncome(begin, BigDecimal.ZERO, 0L, 0L);
        return result;
    }

    /**
     * 效果ECPM
     */
    @Override
    public AdIncome getResourceRtbQuota(Timestamp begin, Timestamp end, ResourceIncomeComposition composition) throws IncomeConditionInvalidException {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        AdIncome adIncome = wxResourceService.queryResourceRtbQuota(composition, param);
        LOGGER.info("Resource getResourceRtbQuota {} date:{},adIncome:{}" + composition.name(), param.getDateEnd(), adIncome.toString());

        AdIncome result = null != adIncome ? adIncome : new AdIncome(begin, BigDecimal.ZERO, 0L, 0L);
        return result;
    }

    /**
     * 资源 ctr
     */
    @Override
    public AdIncome getResourceQuotaCtr(Timestamp begin, Timestamp end, ResourceIncomeComposition composition) throws IncomeConditionInvalidException {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        AdIncome adIncome = wxResourceService.queryResourceCtrQuota(composition, param);

        AdIncome result = null != adIncome ? adIncome : new AdIncome(begin, BigDecimal.ZERO, 0L, 0L);
        return result;
    }

//    /**
//     * 客户 分行业收入
//     */
//    @Override
//    public List<ClassifyIncome> getCustomerCategoryIncome(TeamType team, Timestamp begin, Timestamp end) {
//        List<ClassifyIncome> classifyIncomes = wxCustomerQueryService.queryCategoryIncome(IncomeQueryParam.builder()
//                .team(team)
//                .dateBegin(begin)
//                .dateEnd(end)
//                .build());
//        return classifyIncomes;
//    }

//    /**
//     * 客户 收入
//     */
//    @Override
//    public List<ClassifyIncome> getCustomerIncome(TeamType team, Timestamp begin, Timestamp end) {
//        List<ClassifyIncome> classifyIncomes = wxCustomerQueryService.queryCustomerIncome(IncomeQueryParam.builder()
//                .team(team)
//                .dateBegin(begin)
//                .dateEnd(end)
//                .build());
//        return classifyIncomes;
//    }

//    /**
//     * 单一客户 收入
//     */
//    @Override
//    public List<ClassifyIncome> getCustomerIncomeByGroupId(TeamType team, Timestamp begin, Timestamp end, Integer customerId,Integer companyGroupId) {
//        List<ClassifyIncome> classifyIncomes = wxCustomerQueryService.queryCustomerIncome(IncomeQueryParam.builder()
//                .team(team)
//                .dateBegin(begin)
//                .dateEnd(end)
//                .customerId(customerId)
//                .companyGroupId(companyGroupId)
//                .build());
//        return classifyIncomes;
//    }

//    /**
//     * 分行业客户运营
//     */
//    @Override
//    public List<CategoryCustomerStatBo> getCustomerCategoryStat(TeamType team, IncomeType type, Timestamp begin, Timestamp end) {
//        List<CategoryCustomerStatBo> classifyIncomes = wxCustomerQueryService.queryCategoryCustomerStat(IncomeQueryParam.builder()
//                .team(team)
//                .incomeType(type)
//                .dateBegin(begin)
//                .dateEnd(end)
//                .build());
//        return classifyIncomes;
//    }

    /**
     * 代理商收入 ,单一代理商
     */
    @Override
    public List<ClassifyIncome> getAgentIncome(Timestamp begin, Timestamp end, AgentIncomeComposition composition, Integer customerId) throws IncomeConditionInvalidException {
        IncomeQueryParam param = IncomeQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .customerId(customerId)
                .build();
        List<ClassifyIncome> classifyIncomes = wxAgentQueryService.queryAgentIncome(composition, param);
        return classifyIncomes;
    }

    @Override
    public DepContractIncome getRtbDepQuotaIncome(Timestamp base) {
        DepContractIncome depQuota = rtbIncomeCompositionQueryHandler.getBusinessDepQuotaIncome(base);
        return depQuota;
    }

    @Override
    public DepContractIncome getBrandDepQuotaIncome(Timestamp base) {

        DepContractIncome depQuota = brandIncomeCompositionQueryHandler.getBusinessDepQuotaIncome(base);
        return depQuota;
    }

    @Override
    public BigDecimal getRtbRepeatQuotaIncome(TeamType team, Timestamp begin, Timestamp end) {
        IncomeQueryParam param = IncomeQueryParam.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .team(team)
                .build();
        return rtbIncomeCompositionQueryHandler.getRtbRepeatIncome(param);
    }

    @Override
    public boolean isV2Push() {
        return "1".equals(stringRedisTemplate.opsForValue().get("crm_mobile_push_v2"));
    }

    @Override
    public void upBuild(Timestamp day, DepContractIncome businessDepIncome, Operator operator) {

        List<AchievementDto> unDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .pickAchieveTypeEnum(PickupAchieveTypeEnum.UNDONE)
                        .build(),
                IncomeComposition.PICK_UP_UNCONFIRMED);
        BigDecimal unDoneIncome = unDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);


        List<AchievementDto> boostingUnDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .build(),
                IncomeComposition.BOOSTING_UNDONE);
        BigDecimal boostingUnDoneIncome = boostingUnDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnAuditRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .build(),
                IncomeComposition.BOOSTING_UNAUDIT);
        BigDecimal boostingUnAuditIncome = boostingUnAuditRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> unPaidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .pickAchieveTypeEnum(PickupAchieveTypeEnum.UNPAID)
                        .build(), IncomeComposition.PICK_UP_UNPAID);
        BigDecimal unPaidIncome = unPaidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        //加上合约广告已录点未过审 （花火 直播数据数据 ）
        List<ContractAdIncome> bills = achievementContractService.getContractForecast(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                .build());

        BigDecimal otherPickupAmount = bills.stream().filter(r -> DepAchieveConfigBeta.CONTRACT_ORDER_PICKUP_FIRST_CATE.equals(r.getOrderFirstCategoryId())
                || DepAchieveConfigBeta.OTHER_PICKUP_ORDER_TYPE.contains(r.getOrderType())).map(ContractAdIncome::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        BigDecimal livePickupAmount = bills.stream().filter(r -> DepAchieveConfigBeta.CONTRACT_ORDER_LIVE_ANCHOR_PICKUP_FIRST_CATE.equals(r.getOrderFirstCategoryId()))
                .map(ContractAdIncome::getAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        // 合约广告待执行待交付中（花火 直播数据 ）
        AchievementContractUnCheckQuotaDto quotaDto = achievementContractService.getUnCheckedBillQuotaDto(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                .build());

        BigDecimal contractPickupAmount = Utils.fromFenToYuan(otherPickupAmount.add(livePickupAmount).add(quotaDto.getOtherPickUpAmount()).add(quotaDto.getLivePickUpAmount()));
        /**
         * 待执行 + 助推商单待执行 + 助推商单待审核 + 待支付 + 合约广告待执行
         */
        BigDecimal incomeTotal = unDoneIncome.add(boostingUnDoneIncome).add(boostingUnAuditIncome).add(unPaidIncome).add(contractPickupAmount);
        businessDepIncome.setUpOrderUnExecuted(BigDecimal.valueOf(Utils.fromYuanToFen(incomeTotal)));

        List<AchievementDto> unDoneBizRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .pickAchieveTypeEnum(PickupAchieveTypeEnum.PICK_UP_PAYED_UNDONE)
                        .build(),
                IncomeComposition.PICK_UP_PAYED_UNDONE);
        BigDecimal unDoneBizIncome = unDoneBizRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> unPaidBizRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .pickAchieveTypeEnum(PickupAchieveTypeEnum.PICK_UP_UNPAY_UNORDER)
                        .build(), IncomeComposition.PICK_UP_UNPAY_UNORDER);
        BigDecimal unPaidBizIncome = unPaidBizRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> auditRejectBizRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator,
                QueryAchieveDto.builder()
                        .dateBegin(incomeTimeUtil.getQuarterFirstDate(day))
                        .dateEnd(incomeTimeUtil.getQuarterEndDate(day))
                        .base(day)
                        .pickAchieveTypeEnum(PickupAchieveTypeEnum.PICK_UP_AUDIT_REJECT)
                        .build(), IncomeComposition.PICK_UP_AUDIT_REJECT);
        BigDecimal auditRejectBizIncome = auditRejectBizRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        /**
         * 已支付待完成 + 待支付待接单 + 审核驳回 + 合约广告待执行 + 助推商单待执行 + 助推商单待审核
         */
        BigDecimal historyUpOrderForecastIncome = unDoneBizIncome.add(unPaidBizIncome).add(auditRejectBizIncome).add(contractPickupAmount).add(boostingUnDoneIncome).add(boostingUnAuditIncome);
        businessDepIncome.setHistoryUpOrderForecast(Utils.fromYuanToBigDecimalFen(historyUpOrderForecastIncome));
    }
}
