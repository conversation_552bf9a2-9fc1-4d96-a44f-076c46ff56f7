package com.bilibili.crm.platform.app.api;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 企业微信租户类型 1-商业数据服务 2-crm数据服务
 *
 * <AUTHOR>
 * @date 2021/06/17
 */
@AllArgsConstructor
public enum EnterpriseWeChatAgentType {
    BUSINESS_DATA_AGENT(1, "商业数据服务"),
    CRM_DATA_AGENT(2, "crm数据服务"),
    UNKNOWN(99, "未知");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static EnterpriseWeChatAgentType getByCode(Integer code) {
        for (EnterpriseWeChatAgentType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return EnterpriseWeChatAgentType.UNKNOWN;
    }

}
