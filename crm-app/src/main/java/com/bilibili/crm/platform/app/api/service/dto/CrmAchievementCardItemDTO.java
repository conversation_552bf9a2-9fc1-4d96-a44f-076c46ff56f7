package com.bilibili.crm.platform.app.api.service.dto;

import com.bilibili.crm.platform.biz.po.addata.CrmAchievementDayByTypePo;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 含有kpi目标和完成率的DTO
 *
 * <AUTHOR>
 * date 2024/6/3 16:31.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrmAchievementCardItemDTO implements Serializable {

    private static final long serialVersionUID = -7498772049094683805L;

    /**
     * 自增ID
     */
    private Long id;

    /**
     * 业务名称:销售名称或者小组名称
     */
    private String bizName;

    /**
     * 业务id:销售id或者小组id
     */
    private Integer bizId;

    /**
     * 业务类型：详细见SaleKpiTypeEnum，不是全部都有用哦
     */
    private Integer bizType;

    /**
     * 销售邮箱前缀
     */
    private String saleEmail;

    /**
     * 是否是销售有权限的数据 1是0否
     */
    private Integer saleAuthData;

    /**
     * 销售或者小组属于直客的还是渠道的 1直客 2渠道 为0谁也不是
     */
    private Integer directOrChannel;

    /**
     * 聚合生成的开始日期
     */
    private Timestamp aggTimeStart;

    /**
     * 聚合生成的结束日期
     */
    private Timestamp aggTimeEnd;

    /**
     * 检测日期
     */
    private Timestamp aggLogTime;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期所有业绩已完成总金额（分）
     */
    private Long allCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行已交付已完成总金额（分）
     */
    private Long brandCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行已交付未关账总金额（分）
     */
    private Long brandCompletedUnCloseAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行已交付已关账总金额（分）
     */
    private Long brandCompletedClosedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行已交付直播商单总金额（分）
     */
    private Long brandCompletedLivePickAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行已交付其他商单总金额（分）
     */
    private Long brandCompletedOtherPickAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行已交付品牌产品总金额（分）
     */
    private Long brandCompletedProductAmount;

    /**
     * 合约广告历史季度已交付未关账总金额（分）不受聚合时间影响
     */
    private Long brandOldQuarterAmount;

    /**
     * 合约广告历史季度已交付未关账直播商单金额（分））不受聚合时间影响
     */
    private Long brandOldQuarterLivePickAmount;

    /**
     * 合约广告历史季度已交付未关账其他商单金额（分））不受聚合时间影响
     */
    private Long brandOldQuarterOtherPickAmount;

    /**
     * 合约广告历史季度已交付未关账品牌产品总金额（分））不受聚合时间影响
     */
    private Long brandOldQuarterProductAmount;

    /**
     * 合约广告历史季度已执行待交付金额（分））不受聚合时间影响
     */
    private Long brandOldQuarterCompletedWaitDeliveryAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期花火商单已执行总金额（分）
     */
    private Long pickupCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期cpc_cpm已执行总金额（分）
     */
    private Long cpcCpmCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期cpc_cpm已执行现金总金额（分）
     */
    private Long cpcCpmCompletedCashAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期cpc_cpm已执行所有返货总金额（分）
     */
    private Long cpcCpmCompletedRedPacketAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期dpa已执行总金额（分）
     */
    private Long dpaCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期adx已执行总金额（分）
     */
    private Long adxCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期adx已执行现金总金额（分）
     */
    private Long adxCompletedCashAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期adx已执行所有返货总金额（分）
     */
    private Long adxCompletedRedPacketAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期business_fly已执行总金额（分）
     */
    private Long businessFlyCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期business_fly已执行现金总金额（分）
     */
    private Long businessFlyCompletedCashAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期business_fly已执行所有返货总金额（分）
     */
    private Long businessFlyCompletedRedPacketAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期content_fly已执行总金额（分）
     */
    private Long contentFlyCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期content_fly已执行现金总金额（分）
     */
    private Long contentFlyCompletedCashAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期content_fly已执行所有返货总金额（分）
     */
    private Long contentFlyCompletedRedPacketAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期personFly已执行总金额（分）
     */
    private Long personFlyCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期clue_pass已执行总金额（分）
     */
    private Long cluePassCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待执行待交付已完成总金额（分）
     */
    private Long brandUnCompleteUnDeliveryAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已执行待交付总金额（分）
     */
    private Long brandCompleteUnDeliveryAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待执行总金额（分）
     */
    private Long brandUnCompleteAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待执行待交付直播商单总金额（分）
     */
    private Long brandUnCompleteUnDeliveryLivePickAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待执行待交付其他商单总金额（分）
     */
    private Long brandUnCompleteUnDeliveryOtherPickAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待执行待交付品牌产品总金额（分）
     */
    private Long brandUnCompleteUnDeliveryProductAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已录点位未审核总金额（分）
     */
    private Long brandRecordUnAuditAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待审核总金额（分）
     */
    private Long brandWaitAuditAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告待提审总金额（分）
     */
    private Long brandWaitArraignAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告直播商单总金额（分）
     */
    private Long brandRecordUnAuditLivePickAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告其他商单总金额（分）
     */
    private Long brandRecordUnAuditOtherPickAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告品牌产品总金额（分）
     */
    private Long brandRecordUnAuditProductAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告已下单未录入总金额（分）
     */
    private Long brandOrderedUnRecordAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告品牌未录入总金额（分）
     */
    private Long brandProductUnRecordAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期合约广告商单未录入总金额（分）
     */
    private Long brandPickUnRecordAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期花火商单待执行总金额（分）
     */
    private Long pickupWaitExecuteAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期花火商单待执行(销售口径)总金额（分）
     */
    private Long pickupWaitExecuteWithSaleAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期花火商单待执行(销售口径)已支付待完成总金额（分）
     */
    private Long pickupWaitExecuteWithSalePayedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期花火商单待执行(销售口径)待支付待接单总金额（分）
     */
    private Long pickupWaitExecuteWithSaleUnPayAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期花火商单待执行(销售口径)审核驳回总金额（分）
     */
    private Long pickupWaitExecuteWithSaleRejectAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期 三连其他 已执行总金额（分）
     */
    private Long sanLianOtherCompletedAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期 三连其他 已执行现金总金额（分）
     */
    private Long sanLianOtherCompletedCashAmount;

    /**
     * 聚合生成的开始日期到聚合生成的结束日期 三连其他 已执行所有返货总金额（分）
     */
    private Long sanLianOtherCompletedRedPacketAmount;

    /**
     * 数据环境隔离标识 prd pre
     */
    private String dataEnv;

}

