package com.bilibili.crm.platform.app.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/12/3
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class TeamIncomeVo implements Serializable {

    private static final long serialVersionUID = 2085980809253177706L;

    @ApiModelProperty("团队昨日收入")
    private BigDecimal yesterday_income;

    @ApiModelProperty("季度已确认收入")
    private BigDecimal quarter_confirm_income;

    @ApiModelProperty("季度kpi")
    private BigDecimal quarter_kpi;

    @ApiModelProperty("季度完成进度")
    private BigDecimal quarter_kpi_process;

    @ApiModelProperty("较昨日收入比例")
    private BigDecimal yesterday_ratio;

    @ApiModelProperty("团队分产品收入")
    private List<TeamProductIncomeCompositionVo> team_product_income;

    @ApiModelProperty("已执行待交付")
    private BigDecimal  executed_un_pay;

    @ApiModelProperty("已下单待执行")
    private BigDecimal   ordered_un_execute;

    @ApiModelProperty("已预订待审核")
    private BigDecimal   booked_un_audit;

    @ApiModelProperty("已预订待提审")
    private BigDecimal   booked_un_deliver;

    @ApiModelProperty("已录点位未过审")
    private BigDecimal recorded_un_audit;

    @ApiModelProperty("已下单未录点位")
    private BigDecimal ordered_un_record;

    @ApiModelProperty("下单预估")
    private BigDecimal order_forecast;

    @ApiModelProperty("up主产品-下单待执行")
    private BigDecimal up_order_un_executed;

    @ApiModelProperty("up主产品已下单预估")
    private BigDecimal up_order_forecast;
}
