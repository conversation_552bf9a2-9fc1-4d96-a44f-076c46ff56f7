package com.bilibili.crm.platform.app.biz.monitor;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.crm.platform.api.income.dto.*;
import com.bilibili.crm.platform.app.biz.service.WxAppPaperServiceImpl;
import com.bilibili.crm.platform.app.cache.WxAppCacheLoader;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.dianping.cat.Cat;
import org.mockito.internal.verification.Times;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Component
public class WxAppDataCheckService {
    @Autowired
    private WxAppCacheLoader cacheLoader;
    @Autowired
    private WxAppPaperServiceImpl wxAppPaperService;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;

    @Autowired
    private IMailService mailService;
    @Value("#{'${wx.app.monitor.data.check.mail:<EMAIL>;<EMAIL>}'.split(';')}")
    private List<String> mailList;

    /**
     * 1. 检查各个模块数据是否准备好
     * 2. 检查数仓数据(es:dss_ad_overview)是否准备好
     * 3. 检查依赖(es:report_day_stock) 是否准备好
     * 4. 检查账单数据是否准备好
     * 5. 检查消耗数据数据(es:wallet_log)是否准备好
     * 6. 检查消耗数据数据(es:ad_stat_source_day)是否准备好
     *
     * @param
     * @return
     * @throws Exception
     */
    public void dataCheck() {
        Timestamp date = Utils.getYesteday();

        boolean allState = allModuleStatCheck(date);
        boolean daData = daDataCheck(date);
        boolean dayStock = dayStockCheck(date);
        boolean bill = billCheck(date);

        String head = "企业微信商业日报是否ready: " + allState + "<br>";
        String teamState = "团队模块是否ready: " + moduleStatCheck(date, WxModuleType.TEAM) + "<br>";
        String resourceState = "资源模块是否ready: " + moduleStatCheck(date, WxModuleType.RESOURCE) + "<br>";
        String productState = "产品模块是否ready: " + moduleStatCheck(date, WxModuleType.PRODUCT) + "<br>";
        String customerState = "客户模块是否ready: " + moduleStatCheck(date, WxModuleType.CUSTOMER) + "<br>";
        String agentState = "代理商模块是否ready: " + moduleStatCheck(date, WxModuleType.AGENT) + "<br>";
        String daState = "日报数仓数据是否ready: " + daData + "<br>";
        String dayStockState = "日报report_day_stock数据是否ready: " + dayStock + "<br>";
        String billState = "日报账单数据是否ready: " + bill + "<br>";

        String title = Utils.getTimestamp2String(Utils.getToday()) + " crm企业微信日报数据监控";
        String content = head + teamState + resourceState + productState + daState + dayStockState + billState + customerState + agentState;

        if (!allState && (bill && dayStock && daData)) {
            content = content + "依赖数据刚刚准备好，日报正在生成中，会在30分钟后发出" + "<br>";
        }

        //状态位异常，发送邮件
        if (!allState) {
            sendMail(title, content);
        }

    }

    /**
     * 模块状态位
     */
    private boolean moduleStatCheck(Timestamp date, WxModuleType module) {
        return cacheLoader.isModulePaperStateReady(date, module);
    }

    /**
     * 所有模块状态位
     */
    private boolean allModuleStatCheck(Timestamp date) {
        return cacheLoader.isPaperStateAllReady(date);
    }

    /**
     * 数仓数据 dss_ad_overview
     */
    private boolean daDataCheck(Timestamp date) {
        if (!dssDataCheckByState(date)) {
            return false;
        }
        try {
            wxAppPaperService.getLocationComposition(SourceLocation.INFORMATION_FLOW, LocationComposition.TOTAL, date);
            return true;
        } catch (WxAppException e) {
            return false;
        }
    }

    /**
     * report_day_stock 产品总库存数据
     */
    private boolean dayStockCheck(Timestamp date) {
        try {
            wxAppPaperService.getProductPv(Utils.getBeginOfDay(date), Utils.getEndOfDay(date), ProductIncomeComposition.PRODUCT_PV_ALL);
            return true;
        } catch (WxAppException e) {
            return false;
        }
    }

    /**
     * 账单数据
     */
    private boolean billCheck(Timestamp date) {
        BigDecimal income = wxAppPaperService.getIncomeComposition(TeamType.BRAND, IncomeComposition.CONTRACT_AD, date);
        if (BigDecimal.ZERO.equals(income)) {
            return false;
        }
        return true;
    }


    public void sendMail(String title, String content) {

        MailMessage mailMessage = new MailMessage();
        mailMessage.setUseHtml(true);
        mailMessage.setHasFile(false);
        mailMessage.setSubject(title);
        mailMessage.setText(content);
        mailMessage.setTos(mailList);

        try {
            mailService.send(mailMessage);
        } catch (ServiceException e) {
            Cat.logError("WxAppDataCheckService.sendMonitorMail", e);
            throw new RuntimeException(e);
        }
    }


    public boolean dssDataCheckByState(Timestamp date) {
        String dssKey = generateDssMainKey(date);
        String state = wxAppCacheManager.getForValue(dssKey);
        String stateAdp = wxAppCacheManager.getForValueADP(dssKey);
        if (null == state && null == stateAdp) {
            return false;
        }
        if (null != state && (Integer.valueOf(1).equals(Integer.valueOf(state)))
                || (null != stateAdp && Integer.valueOf(1).equals(Integer.valueOf(stateAdp)))) {
            return true;
        }
        return false;
    }

    private String generateDssMainKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "DSS_MAIN_" + yyyyMMdd + "_FINISH";
    }


    public boolean mobileV2DataCheckByState(Timestamp date) {
        String ckDataReadyKey = generateMobileV2CkKey(date);
        String state = wxAppCacheManager.getForValue(ckDataReadyKey);
        String stateAdp = wxAppCacheManager.getForValueADP(ckDataReadyKey);
        if (null == state && null == stateAdp) {
            return false;
        }
        if (null != state && (Integer.valueOf(1).equals(Integer.valueOf(state)))
                || (null != stateAdp && Integer.valueOf(1).equals(Integer.valueOf(stateAdp)))) {
            return true;
        }
        return false;
    }

    // ads_dss_crm_product_report_1d_d   ads_dss_up_resource_crm_product_report_l_1d_d  ck表是否完整
    private String generateMobileV2CkKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "CRM_PRODUCT_REPORT_" + yyyyMMdd + "_FINISH";
    }


    public boolean mobileDataCheckByState(Timestamp date, String dataReadyKey) {
        String state = wxAppCacheManager.getForValue(dataReadyKey);
        String stateAdp = wxAppCacheManager.getForValueADP(dataReadyKey);
        if (null == state && null == stateAdp) {
            return false;
        }
        if (null != state && (Integer.valueOf(1).equals(Integer.valueOf(state)))
                || (null != stateAdp && Integer.valueOf(1).equals(Integer.valueOf(stateAdp)))) {
            return true;
        }
        return false;
    }

    public String generateSellGoodFlyCkKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "CRM_FLY_V2_" + yyyyMMdd + "_FINISH";
    }

    public String generateSellGoodRtbCkKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "CRM_RTB_V1_" + yyyyMMdd + "_FINISH";
    }

    public String generateEffectResourceCkKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "CRM_EFFECT_RESOURCE_" + yyyyMMdd + "_FINISH";
    }

    public String generateSellGoodGmvTbCkKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "CRM_TAKE_GOOD_TB_" + yyyyMMdd + "_FINISH";
    }

    public String generateSellGoodGmvJdCkKey(Timestamp date) {
        String yyyyMMdd = Utils.getTimestamp2String(date, "yyyyMMdd");
        return "CRM_TAKE_GOOD_JD_" + yyyyMMdd + "_FINISH";
    }

}
