package com.bilibili.crm.platform.app.cache.sale;

import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.account.dto.*;
import com.bilibili.crm.platform.api.account.service.IAccountBiliUserService;
import com.bilibili.crm.platform.api.achievement.IAchievementPickUpService;
import com.bilibili.crm.platform.api.achievement.IAchievementRtbService;
import com.bilibili.crm.platform.api.achievement.dto.*;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromCluePassService;
import com.bilibili.crm.platform.api.contract.dto.ContractBaseDto;
import com.bilibili.crm.platform.api.contract.dto.QueryContractParam;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.dto.*;
import com.bilibili.crm.platform.api.enums.WxSaleAggType;
import com.bilibili.crm.platform.api.enums.WxSaleProductType;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationBucketType;
import com.bilibili.crm.platform.api.finance.enums.SaleTypeEnum;
import com.bilibili.crm.platform.api.income.dto.AdIncomeQueryParam;
import com.bilibili.crm.platform.api.income.dto.ContractAdIncome;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.sale.dto.QuerySaleDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaBelongingService;
import com.bilibili.crm.platform.api.sale_sea.dto.QuerySaleSeaBelongingDto;
import com.bilibili.crm.platform.api.sale_sea.dto.SaleSeaBelonging;
import com.bilibili.crm.platform.api.stat.IPerformanceAdService;
import com.bilibili.crm.platform.api.stat.dto.PerformanceAdQueryDto;
import com.bilibili.crm.platform.api.wallet.enums.AggLevelEnum;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.biz.annotation.ExecuteTime;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.po.AccCompanyGroupPo;
import com.bilibili.crm.platform.biz.po.AccProductPo;
import com.bilibili.crm.platform.biz.po.CrmAgentPo;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.repo.AccCompanyGroupRepo;
import com.bilibili.crm.platform.biz.repo.AccProductRepo;
import com.bilibili.crm.platform.biz.repo.CrmAgentRepo;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.service.account.FastQueryAccountServiceImpl;
import com.bilibili.crm.platform.biz.service.achievement.AchievementContractServiceImpl;
import com.bilibili.crm.platform.biz.service.achievement.AchievementRtbServiceImpl;
import com.bilibili.crm.platform.biz.service.achievement.config.beta.DepAchieveConfigBeta;
import com.bilibili.crm.platform.biz.service.achievement.query.AchieveFromWalletService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.order.OrderUtils;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.income.IncomeTeamEnum;
import com.bilibili.crm.platform.common.pickup.PickupOrderCooperationType;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import com.bilibili.crm.platform.utils.AdUtils;
import com.bilibili.crm.platform.utils.MathUtils;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import joptsimple.internal.Strings;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-04-21 21:19:25
 * @description:
 **/

@Slf4j
@Component
public class WxAppSaleDataHelper {

    @Autowired
    private AchievementContractServiceImpl achievementContractService;
    @Autowired
    private IAchievementRtbService rtbService;
    @Autowired
    private AchieveFromWalletService achieveFromWalletService;
    @Autowired
    private IAchievementPickUpService achievementPickUpService;
    @Resource
    private CrmAgentRepo crmAgentRepo;
    @Resource
    private FastQueryAccountServiceImpl fastQueryAccountService;

    @Autowired
    private ISaleService saleService;

    @Resource
    private ISaleSeaBelongingService iSaleSeaBelongingService;

    @Resource
    private CustomerRepo customerRepo;

    @Resource
    private AccProductRepo accProductRepo;

    @Resource
    private AccCompanyGroupRepo accCompanyGroupRepo;

    @Resource
    private IPerformanceAdService performanceAdService;

    @Resource
    private AchievementRtbServiceImpl achievementRtbService;

    @Resource
    private IContractQueryService contractQueryService;

    @Resource
    private ISaleSeaBelongingService saleSeaBelongingService;

    @Autowired
    private IAccountBiliUserService accountBiliUserService;

    @Autowired
    private CrmCacheManager cacheManager;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    @Resource
    private IAchieveFromCluePassService achieveFromCluePassService;

    public static final Integer TOP_SIZE = 50;

    public static final Integer THIRD_LEVE_TOP_SIZE = 100;

    public static final List<Integer> UP_VIDEO_COOPERATION_TYPE = Arrays.asList(
            PickupOrderCooperationType.CONTENT_CUSTOMIZED.getCode(),
            PickupOrderCooperationType.BRAND_EMBEDDING.getCode()
    );
    public static final List<Integer> UP_LIVE_COOPERATION_TYPE = Arrays.asList(
            PickupOrderCooperationType.ONLINE_LIVE_BROADCAST.getCode(),
            PickupOrderCooperationType.OFFLINE_LIVE_BROADCAST.getCode()
    );

    public List<WxSaleCompositionDto> queryIncomeInfo(Timestamp begin, Timestamp end, List<Integer> agentIds, Timestamp base, boolean historyQuery) {
        //合约广告 --已完成
        AchievementContractCheckQuotaDto checkedBillQuotaDto = achievementContractService.getCheckedBillQuotaDto(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), historyQuery);
        List<WxSaleCompositionDto> contractDtoList = contractToCompositionDto(checkedBillQuotaDto);
        List<WxSaleCompositionDto> result = new ArrayList<>(contractDtoList);

        // 效果
        //cpc
        List<AchievementRtbData> cpc = rtbService.getConsumeDataByFunctionV2(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), IncomeComposition.CPC, achieveFromWalletService::getWalletAggByAccountAgentByWideES);
        result.addAll(rtbToCompositionDto(cpc, WxSaleProductType.CPC_CPM, base));
        AlarmHelper.log("RunCpc", begin, end, cpc);
        //cpm
        List<AchievementRtbData> cpm = rtbService.getConsumeDataByFunctionV2(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), IncomeComposition.CPM, achieveFromWalletService::getWalletAggByAccountAgentByWideES);
        result.addAll(rtbToCompositionDto(cpm, WxSaleProductType.CPC_CPM, base));
        AlarmHelper.log("RunCpm", begin, end, cpm);

        //商业起飞
        List<AchievementRtbData> businessAdvancePay = rtbService.getConsumeDataByFunctionV2(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByAccountAgentByWideES);
        result.addAll(rtbToCompositionDto(businessAdvancePay, WxSaleProductType.BUSINESS_FLY, base));


        //dpa
        List<AchievementRtbData> dpa = rtbService.getDpaSumDetailWithClose(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build());
        result.addAll(rtbToCompositionDto(dpa, WxSaleProductType.DPA, base));
        //ADX
        List<AchievementRtbData> adx = rtbService.getAdxDaySourceDataWithClose(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build());
        result.addAll(rtbToCompositionDto(adx, WxSaleProductType.ADX, base));
        // 必选-起飞 实结
        List<AchievementRtbData> effectClose = rtbService.getConsumeDataByFunctionV2(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), IncomeComposition.RTB_FLY_CLOSE, achieveFromWalletService::getWalletAggByAccountAgentByWideES);
        result.addAll(rtbToCompositionDto(effectClose, WxSaleProductType.RTB_CLOSE, base));
        //带货收入
        List<AchievementRtbData> daySellGoodsDataList = achieveFromWalletService.getWalletAggByAccountAgentByWideES(AdIncomeQueryParam.builder()
                .agentId(agentIds)
                .TimeAggType(AggregationBucketType.DATE_HISTOGRAM_YEAR)
                .dateBegin(begin)
                .dateEnd(end)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeDepIds(DepAchieveConfigBeta.FINICAL_EXCLUDE_DEPIDS)
                .excludeAccountIds(DepAchieveConfigBeta.CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .build());
        result.addAll(rtbToCompositionDto(daySellGoodsDataList, WxSaleProductType.SELL_GOOD, base));

        //花火 -- 已完成
        List<AchievePickUpDto> confirmedPickUp = achievementPickUpService.getPickUpBillExPortByWideEs(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), IncomeComposition.PICK_UP_CONFIRMED);

        List<AchievePickUpDto> boostingPickUp = achievementPickUpService.getPickUpBillExPortByWideEs(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build(), IncomeComposition.BOOSTING_CONFIRMED);

        // 合伙人-线索通
        List<AchievementRtbData> cluePass = rtbService.getConsumeDataByFunction(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .isQuerySale(IsValid.TRUE.getCode())
                .build(), IncomeComposition.CLUE_PASS, achieveFromCluePassService::queryCluePass);

        List<AchievePickUpDto> pickUpList = Stream.of(confirmedPickUp, boostingPickUp).flatMap(Collection::stream).collect(Collectors.toList());
        result.addAll(pickUpToCompositionDto(pickUpList));
        result.addAll(cluePassToCompositionDto(cluePass));
        return result;
    }

    private List<WxSaleCompositionDto> cluePassToCompositionDto(List<AchievementRtbData> cluePass) {
        List<WxSaleCompositionDto> result = new ArrayList<>();
        List<Integer> accountIds = cluePass.stream().map(AchievementRtbData::getAccountId).distinct().collect(Collectors.toList());
        List<AccountBaseDto> accountBaseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds)
                .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId);
        List<Integer> agentIds = accountBaseDtoList.stream().map(AccountBaseDto::getDependencyAgentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmAgentPo> crmAgentPoMap = Maps.newHashMap();
        Map<Integer, CrmAgentPo> finalCrmAgentPoMap = crmAgentPoMap;
        if (!CollectionUtils.isEmpty(agentIds)) {
            crmAgentPoMap = crmAgentRepo.queryAgentDtoMapByAgentIds(agentIds);
            List<Integer> agentAccountIds = crmAgentPoMap.values().stream().map(CrmAgentPo::getSysAgentId).distinct().collect(Collectors.toList());
            List<AccountBaseDto> agentAccountList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(agentAccountIds)
                    .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId);
            if (!CollectionUtils.isEmpty(accountBaseDtoList)) {
                accountBaseDtoList.addAll(Optional.ofNullable(agentAccountList).orElse(new ArrayList<>()));
            }
        }
        Map<Integer, AccountBaseDto> accountMap = accountBaseDtoList.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, a -> a, (a, b) -> a));
        cluePass.forEach(pickUpDto -> {
            WxSaleCompositionDto dto = new WxSaleCompositionDto();
            AccountBaseDto accountBaseDto = accountMap.get(pickUpDto.getAccountId());
            CrmAgentPo crmAgentPo = finalCrmAgentPoMap.getOrDefault(accountBaseDto.getDependencyAgentId(), new CrmAgentPo());
            AccountBaseDto agentAccount = accountMap.getOrDefault(Optional.ofNullable(crmAgentPo.getSysAgentId()).orElse(-1), new AccountBaseDto());
            dto.setAccountId(pickUpDto.getAccountId());
            dto.setSaleIds(pickUpDto.getSaleIds());
            dto.setProductId(accountBaseDto.getProductId());
            dto.setAgentId(accountBaseDto.getDependencyAgentId());
            dto.setCustomerId(accountBaseDto.getCustomerId());
            dto.setCompanyGroupId(accountBaseDto.getGroupId());
            dto.setDate(pickUpDto.getDate());
            dto.setAgentCustomerId(Optional.ofNullable(agentAccount.getCustomerId()).orElse(0));
            dto.setWxSaleProductType(WxSaleProductType.PICK_UP_CLUE_PASS);
            dto.setIncomeTeamEnum(IncomeTeamEnum.UP);
            dto.setIncome(pickUpDto.getTotalConsume());
            dto.setSaleIds(pickUpDto.getSaleIds());
            result.add(dto);
        });
        return result;
    }

    public WxSaleAggRatioDto buildAggRatioDto(List<List<WxSaleCompositionDto>> monthList, List<WxSaleCompositionDto> quarterList, List<WxSaleCompositionDto> lastQuarter, List<WxSaleCompositionDto> yoyQuarter, Boolean isSellGood) {
        if (!isSellGood) {
            monthList = monthList.stream()
                    .map(dayList -> dayList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList()))
                    .collect(Collectors.toList());
            quarterList = quarterList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
            lastQuarter = lastQuarter.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
            yoyQuarter = yoyQuarter.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
        }
        WxSaleAggRatioDto ratioDto = new WxSaleAggRatioDto();
        ratioDto.setMonthAmountList(monthList.stream()
                .map(dayList -> dayList.stream()
                        .map(WxSaleCompositionDto::getIncome)
                        .filter(Objects::nonNull)
                        .reduce(BigDecimal.ZERO, BigDecimal::add))
                .collect(Collectors.toList()));
        ratioDto.setDayIncomeAmount(ratioDto.getMonthAmountList().get(ratioDto.getMonthAmountList().size() - 1));
        ratioDto.setBeforeYesAmount(ratioDto.getMonthAmountList().get(ratioDto.getMonthAmountList().size() - 2));
        ratioDto.setWeekAgoAmount(ratioDto.getMonthAmountList().get(ratioDto.getMonthAmountList().size() - 8));
        ratioDto.setDayRatio(MathUtils.genRate(ratioDto.getDayIncomeAmount().subtract(ratioDto.getBeforeYesAmount()), ratioDto.getBeforeYesAmount()));
        ratioDto.setWeekAgoRatio(MathUtils.genRate(ratioDto.getDayIncomeAmount().subtract(ratioDto.getWeekAgoAmount()), ratioDto.getWeekAgoAmount()));
        ratioDto.setQuarterIncomeAmount(quarterList.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        ratioDto.setBeforeQuarterAmount(lastQuarter.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        ratioDto.setYoyQuarterAmount(yoyQuarter.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        ratioDto.setQuarterRatio(MathUtils.genRate(ratioDto.getQuarterIncomeAmount().subtract(ratioDto.getBeforeQuarterAmount()), ratioDto.getBeforeQuarterAmount()));
        ratioDto.setYoyQuarterRatio(MathUtils.genRate(ratioDto.getQuarterIncomeAmount().subtract(ratioDto.getYoyQuarterAmount()), ratioDto.getYoyQuarterAmount()));
        return ratioDto;
    }

    public void filterSaleData(List<Integer> saleIds, WxAppSaleDataDto wxAppSaleDataDto) {
        wxAppSaleDataDto.setMonthList(wxAppSaleDataDto.getMonthList().stream().map(dalList ->
                        dalList.stream().filter(dto -> isValid(saleIds, dto)).collect(Collectors.toList()))
                .collect(Collectors.toList()));
        wxAppSaleDataDto.setQuarterList(wxAppSaleDataDto.getQuarterList().stream().filter(dto -> isValid(saleIds, dto)).collect(Collectors.toList()));
        wxAppSaleDataDto.setLastQuarter(wxAppSaleDataDto.getLastQuarter().stream().filter(dto -> isValid(saleIds, dto)).collect(Collectors.toList()));
        wxAppSaleDataDto.setYoyQuarter(wxAppSaleDataDto.getYoyQuarter().stream().filter(dto -> isValid(saleIds, dto)).collect(Collectors.toList()));
    }

    public List<List<WxSaleCompositionDto>> filterSaleData(List<Integer> saleIds, List<List<WxSaleCompositionDto>> weekList) {
        return weekList.stream()
                .map(dtoList -> dtoList.stream().filter(dto -> isValid(saleIds, dto)).collect(Collectors.toList()))
                .collect(Collectors.toList());
    }

    public boolean isValid(List<Integer> saleIds, WxSaleCompositionDto wxSaleCompositionDto) {
        if (CollectionUtils.isEmpty(saleIds) || CollectionUtils.isEmpty(wxSaleCompositionDto.getSaleIds())) {
            return false;
        }
        for (Integer saleId : saleIds) {
            if (wxSaleCompositionDto.getSaleIds().contains(saleId)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }


    @ExecuteTime
    public List<WxSaleAggRatioDto> buildSecondAggRatioDto(SecondAggQueryDto secondAggQueryDto) {
        SaleQueryDto saleQueryDto = SaleQueryDto.builder().isBuildSaleGroup(false).build();
        if (secondAggQueryDto.getTopSize() == null) {
            secondAggQueryDto.setTopSize(TOP_SIZE);
        }
        WxSaleAggType wxSaleAggType = secondAggQueryDto.getWxSaleAggType();
        List<List<WxSaleCompositionDto>> weekList = secondAggQueryDto.getWeekList();
        WxAppSaleTimeDto timeDto = secondAggQueryDto.getTimeDto();
        List<Integer> topIds = new ArrayList<>();
        List<WxSaleAggRatioDto> result = new ArrayList<>();
        List<WxSaleCompositionDto> yesList = weekList.get(weekList.size() - 1);
        List<WxSaleCompositionDto> beforeList = weekList.get(weekList.size() - 2);
        List<WxSaleCompositionDto> weekAgoList = weekList.get(weekList.size() - 8);
        Map<Integer, String> nameMap = new HashMap<>();
        if (CollectionUtils.isEmpty(weekList)) {
            return new ArrayList<>();
        }
        Function<WxSaleCompositionDto, Integer> getAggId = WxSaleCompositionDto::getCustomerId;
        if (wxSaleAggType == WxSaleAggType.CUSTOMER) {
            if (timeDto.getIsRealTime() != null && timeDto.getIsRealTime()) {
                topIds = yesList.stream()
                        .filter(Objects::nonNull)
                        .filter(dto -> dto.getCustomerId() != null && dto.getCustomerId() > 0)
                        .map(WxSaleCompositionDto::getCustomerId).distinct()
                        .collect(Collectors.toList());
            } else {
                topIds = queryTopCustomerIds(yesList, secondAggQueryDto.getIsSellGood(), secondAggQueryDto.getTopSize());
            }
            if (CollectionUtils.isEmpty(topIds)) {
                return new ArrayList<>();
            }
            Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(topIds);
            nameMap = customerPoMap.values().stream().filter(Objects::nonNull).filter(a -> a.getId() != null && a.getId() > 0).collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getUsername));
        }
        if (wxSaleAggType == WxSaleAggType.BRAND) {
            getAggId = WxSaleCompositionDto::getProductId;
            if (timeDto.getIsRealTime() != null && timeDto.getIsRealTime()) {
                topIds = yesList.stream()
                        .filter(Objects::nonNull)
                        .filter(dto -> dto.getProductId() != null && dto.getProductId() > 0)
                        .map(WxSaleCompositionDto::getProductId).distinct()
                        .collect(Collectors.toList());
            } else {
                topIds = queryTopBrandIds(yesList, secondAggQueryDto.getIsSellGood(), secondAggQueryDto.getTopSize());
            }
            if (CollectionUtils.isEmpty(topIds)) {
                return new ArrayList<>();
            }
            Map<Integer, AccProductPo> productMap = accProductRepo.queryProductMapByIds(topIds);
            nameMap = productMap.values().stream().filter(Objects::nonNull).filter(a -> a.getId() != null && a.getId() > 0).collect(Collectors.toMap(AccProductPo::getId, AccProductPo::getName));
        }
        if (wxSaleAggType == WxSaleAggType.GROUP) {
            getAggId = WxSaleCompositionDto::getCompanyGroupId;
            if (timeDto.getIsRealTime() != null && timeDto.getIsRealTime()) {
                topIds = yesList.stream()
                        .filter(Objects::nonNull)
                        .filter(dto -> dto.getCompanyGroupId() != null && dto.getCompanyGroupId() > 0)
                        .map(WxSaleCompositionDto::getCompanyGroupId).distinct()
                        .collect(Collectors.toList());
            } else {
                topIds = queryTopGroupIds(yesList, secondAggQueryDto.getIsSellGood(), secondAggQueryDto.getTopSize());
            }
            if (CollectionUtils.isEmpty(topIds)) {
                return new ArrayList<>();
            }
            List<AccCompanyGroupPo> groupPoList = accCompanyGroupRepo.queryListByIds(topIds);
            nameMap = groupPoList.stream().filter(Objects::nonNull).filter(a -> a.getId() != null && a.getId() > 0).collect(Collectors.toMap(AccCompanyGroupPo::getId, AccCompanyGroupPo::getName));
        }
        if (wxSaleAggType == WxSaleAggType.AGENT_CUSTOMER) {
            getAggId = WxSaleCompositionDto::getAgentCustomerId;
            if (timeDto.getIsRealTime() != null && timeDto.getIsRealTime()) {
                topIds = yesList.stream()
                        .filter(Objects::nonNull)
                        .filter(dto -> dto.getAgentCustomerId() != null && dto.getAgentCustomerId() > 0)
                        .map(WxSaleCompositionDto::getAgentCustomerId).distinct()
                        .collect(Collectors.toList());
            } else {
                topIds = queryTopAgentCustomerIds(yesList, secondAggQueryDto.getIsSellGood(), secondAggQueryDto.getTopSize());
            }
            if (CollectionUtils.isEmpty(topIds)) {
                return new ArrayList<>();
            }
            Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(topIds);
            nameMap = customerPoMap.values().stream().filter(Objects::nonNull).filter(a -> a.getId() != null && a.getId() > 0).collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getUsername));
        }
        Function<WxSaleCompositionDto, Integer> finalGetAggId = getAggId;
        Map<Integer, List<WxSaleCompositionDto>> yesMap = yesList.stream()
                .filter(r -> null != finalGetAggId.apply(r)).collect(Collectors.groupingBy(getAggId));
        Map<Integer, List<WxSaleCompositionDto>> beforeMap = beforeList.stream()
                .filter(r -> null != finalGetAggId.apply(r)).collect(Collectors.groupingBy(getAggId));
        Map<Integer, List<WxSaleCompositionDto>> weekAgoMap = weekAgoList.stream()
                .filter(r -> null != finalGetAggId.apply(r)).collect(Collectors.groupingBy(getAggId));
        for (Integer topId : topIds) {
            List<WxSaleCompositionDto> theYesList = yesMap.getOrDefault(topId, new ArrayList<>());
            List<WxSaleCompositionDto> theBeforeList = beforeMap.getOrDefault(topId, new ArrayList<>());
            List<WxSaleCompositionDto> theWeekAgoList = weekAgoMap.getOrDefault(topId, new ArrayList<>());
            WxSaleAggRatioDto dto = buildSecondRatioDto(theYesList, theBeforeList, theWeekAgoList, secondAggQueryDto.getIsSellGood());
            dto.setIncomeProportion(MathUtils.genRate(dto.getDayIncomeAmount(), yesList.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add)));
            dto.setAggLevel(secondAggQueryDto.getAggLevel());
            dto.setAggId(topId);
            dto.setWxSaleAggType(wxSaleAggType);
            if (secondAggQueryDto.getIsResourceDetail() != null && secondAggQueryDto.getIsResourceDetail()) {
                dto.setWxSaleResourceDetailDto(buildResourceDetail(theYesList, theBeforeList, theWeekAgoList));
            }
            if (secondAggQueryDto.getIsNeedNextLevel() != null && secondAggQueryDto.getIsNeedNextLevel()) {
                List<WxSaleAggRatioDto> aggRatioDtoList = buildNextLevel(theYesList, theBeforeList, theWeekAgoList, secondAggQueryDto, topId, wxSaleAggType);
                Map<WxSaleAggType, List<WxSaleAggRatioDto>> nextMap = Maps.newHashMap();
                if (WxSaleAggType.agentCustomerAggType.contains(wxSaleAggType)) {
                    nextMap.put(WxSaleAggType.AGENT_CUSTOMER, aggRatioDtoList);
                } else {
                    nextMap.put(WxSaleAggType.BRAND, aggRatioDtoList);
                }
                dto.setSecondAggMap(nextMap);
            }
            if (secondAggQueryDto.getIsNeedSale() != null && secondAggQueryDto.getIsNeedSale()) {
                saleQueryDto.setAggId(topId);
                saleQueryDto.setWxSaleAggType(wxSaleAggType);
                dto.setSaleDetailDto(querySaleByAccount(theYesList, saleQueryDto));
            }
            dto.setAggName(nameMap.getOrDefault(topId, ""));
            if (secondAggQueryDto.getCreativeAgg() != null && secondAggQueryDto.getCreativeAgg()) {
                dto.setTodayOnlineCreatives(theYesList.stream().filter(dto1 -> dto1.getTodayOnlineCreatives() != null).mapToInt(WxSaleCompositionDto::getTodayOnlineCreatives).sum());
                dto.setTodayNewCreatives(theYesList.stream().filter(dto1 -> dto1.getTodayNewCreatives() != null).mapToInt(WxSaleCompositionDto::getTodayNewCreatives).sum());
            }
            result.add(dto);
        }
        if (secondAggQueryDto.getIsNeedTotal() != null && !secondAggQueryDto.getIsNeedTotal()) {
            return result;
        }
        WxSaleAggRatioDto total = buildSecondRatioDto(yesList, beforeList, weekAgoList, secondAggQueryDto.getIsSellGood());
        total.setAggLevel(secondAggQueryDto.getAggLevel());
        total.setWxSaleAggType(wxSaleAggType);
        total.setAggName("总计");
        if (secondAggQueryDto.getIsResourceDetail() != null && secondAggQueryDto.getIsResourceDetail()) {
            total.setWxSaleResourceDetailDto(buildResourceDetail(yesList, beforeList, weekAgoList));
        }
        if (secondAggQueryDto.getCreativeAgg() != null && secondAggQueryDto.getCreativeAgg()) {
            total.setTodayNewCreatives(result.stream().mapToInt(WxSaleAggRatioDto::getTodayNewCreatives).sum());
            total.setTodayOnlineCreatives(result.stream().mapToInt(WxSaleAggRatioDto::getTodayOnlineCreatives).sum());
        }
        if (secondAggQueryDto.getIsNeedSale() != null && secondAggQueryDto.getIsNeedSale() && secondAggQueryDto.getUpLevelId() != null && secondAggQueryDto.getUpLevelType() != null) {
            saleQueryDto.setAggId(secondAggQueryDto.getUpLevelId());
            saleQueryDto.setWxSaleAggType(secondAggQueryDto.getUpLevelType());
            SaleDetailDto saleDetailDto = querySaleByAccount(yesList, saleQueryDto);
            saleDetailDto.setOperator(null);
            total.setSaleDetailDto(saleDetailDto);
        }
        result.add(0, total);
        return result;
    }

    private List<WxSaleAggRatioDto> buildNextLevel(List<WxSaleCompositionDto> theYesList, List<WxSaleCompositionDto> theBeforeList, List<WxSaleCompositionDto> theWeekAgoList, SecondAggQueryDto secondAggQueryDto, Integer upLevelId, WxSaleAggType upLevelType) {
        List<WxSaleAggRatioDto> ratioDtoList = new ArrayList<>();
        List<List<WxSaleCompositionDto>> weekList = new ArrayList<>(8);
        weekList.add(theWeekAgoList);
        for (int i = 0; i < 5; i++) {
            weekList.add(Lists.newArrayList());
        }
        weekList.add(theBeforeList);
        weekList.add(theYesList);
        if (WxSaleAggType.agentCustomerAggType.contains(secondAggQueryDto.getWxSaleAggType())) {
            ratioDtoList = buildSecondAggRatioDto(SecondAggQueryDto.builder()
                    .isNeedSale(true)
                    .timeDto(secondAggQueryDto.getTimeDto())
                    .weekList(weekList)
                    .upLevelId(upLevelId)
                    .upLevelType(upLevelType)
                    .aggLevel(AggLevelEnum.GROUP_BY.getCode())
                    .isSellGood(secondAggQueryDto.getIsSellGood())
                    .wxSaleAggType(WxSaleAggType.AGENT_CUSTOMER)
                    .creativeAgg(true)
                    .topSize(THIRD_LEVE_TOP_SIZE)
                    .isNeedTotal(false)
                    .build());
        } else {
            ratioDtoList = buildSecondAggRatioDto(SecondAggQueryDto.builder()
                    .isNeedSale(true)
                    .timeDto(secondAggQueryDto.getTimeDto())
                    .weekList(weekList)
                    .upLevelId(upLevelId)
                    .upLevelType(upLevelType)
                    .aggLevel(AggLevelEnum.GROUP_BY.getCode())
                    .isSellGood(secondAggQueryDto.getIsSellGood())
                    .wxSaleAggType(WxSaleAggType.BRAND)
                    .creativeAgg(true)
                    .topSize(THIRD_LEVE_TOP_SIZE)
                    .isNeedTotal(false)
                    .build());
        }
        WxSaleAggRatioDto other = WxSaleAggRatioDto.builder()
                .aggLevel(AggLevelEnum.GROUP_BY.getCode())
                .aggName("其他")
                .build();
        other.setDayIncomeAmount(theYesList.stream().map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getDayIncomeAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setBeforeYesAmount(theBeforeList.stream().map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getBeforeYesAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setWeekAgoAmount(theWeekAgoList.stream().map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getWeekAgoAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setDayRatio(MathUtils.genRate(other.getDayIncomeAmount().subtract(other.getBeforeYesAmount()), other.getBeforeYesAmount()));
        other.setWeekAgoRatio(MathUtils.genRate(other.getDayIncomeAmount().subtract(other.getWeekAgoAmount()), other.getWeekAgoAmount()));
        other.setCashConsume(theYesList.stream().map(WxSaleCompositionDto::getCashConsume).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getCashConsume).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setRedPacketAmount(theYesList.stream().map(WxSaleCompositionDto::getRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getRedPacketAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setSpecialRedPacketAmount(theYesList.stream().map(WxSaleCompositionDto::getSpecialRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getSpecialRedPacketAmount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setShowCount(theYesList.stream().map(WxSaleCompositionDto::getShowCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getShowCount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setClickCount(theYesList.stream().map(WxSaleCompositionDto::getClickCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add).subtract(ratioDtoList.stream().map(WxSaleAggRatioDto::getClickCount).reduce(BigDecimal.ZERO, BigDecimal::add)));
        other.setCpc(AdUtils.getCpc(other.getDayIncomeAmount(), other.getClickCount()));
        other.setCtr(AdUtils.getCtr(other.getClickCount(), other.getShowCount()));
        other.setEcpm(AdUtils.getEcpm(other.getDayIncomeAmount(), other.getShowCount()));
        if (other.getDayIncomeAmount().compareTo(new BigDecimal(0)) > 0) {
            ratioDtoList.add(other);
        }
        return ratioDtoList;
    }

    private WxSaleResourceDetailDto buildResourceDetail(List<WxSaleCompositionDto> yesList, List<WxSaleCompositionDto> beforeList, List<WxSaleCompositionDto> weekAgoList) {
        WxSaleResourceDetailDto detailDto = new WxSaleResourceDetailDto();
        detailDto.setBrandIncome(yesList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeBrandIncome = beforeList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekBrandIncome = weekAgoList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setBrandRatio(MathUtils.genRate(detailDto.getBrandIncome().subtract(beforeBrandIncome), beforeBrandIncome));
        detailDto.setBrandWeekRatio(MathUtils.genRate(detailDto.getBrandIncome().subtract(weekBrandIncome), weekBrandIncome));

        detailDto.setEffectIncome(yesList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.RTB) && !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeEffectIncome = beforeList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.RTB) && !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekEffectIncome = weekAgoList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.RTB) && !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setEffectRatio(MathUtils.genRate(detailDto.getEffectIncome().subtract(beforeEffectIncome), beforeEffectIncome));
        detailDto.setEffectWeekRatio(MathUtils.genRate(detailDto.getEffectIncome().subtract(weekEffectIncome), weekEffectIncome));

        detailDto.setUpIncome(yesList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeUpIncome = beforeList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekUpIncome = weekAgoList.stream().filter(dto -> dto.getIncomeTeamEnum().equals(IncomeTeamEnum.UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setUpRatio(MathUtils.genRate(detailDto.getUpIncome().subtract(beforeUpIncome), beforeUpIncome));
        detailDto.setUpWeekRatio(MathUtils.genRate(detailDto.getUpIncome().subtract(weekUpIncome), weekUpIncome));

        detailDto.setBrandHardIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.HARD_BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeBrandHardIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.HARD_BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekBrandHardIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.HARD_BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setBrandHardRatio(MathUtils.genRate(detailDto.getBrandHardIncome().subtract(beforeBrandHardIncome), beforeBrandHardIncome));
        detailDto.setBrandHardWeekRatio(MathUtils.genRate(detailDto.getBrandHardIncome().subtract(weekBrandHardIncome), weekBrandHardIncome));

        detailDto.setNonIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.NON_STANDARD_BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeBrandNonIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.NON_STANDARD_BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekBrandNonIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.NON_STANDARD_BRAND)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setNonRatio(MathUtils.genRate(detailDto.getNonIncome().subtract(beforeBrandNonIncome), beforeBrandNonIncome));
        detailDto.setNonWeekRatio(MathUtils.genRate(detailDto.getNonIncome().subtract(weekBrandNonIncome), weekBrandNonIncome));

        detailDto.setCpcCpmIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.CPC_CPM)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeCpcCpmIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.CPC_CPM)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekCpcCpmIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.CPC_CPM)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setCpcCpmRatio(MathUtils.genRate(detailDto.getCpcCpmIncome().subtract(beforeCpcCpmIncome), beforeCpcCpmIncome));
        detailDto.setCpcCpmWeekRatio(MathUtils.genRate(detailDto.getCpcCpmIncome().subtract(weekCpcCpmIncome), weekCpcCpmIncome));

        detailDto.setBusFlyIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.BUSINESS_FLY)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeBusFlyIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.BUSINESS_FLY)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekBusFlyIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.VIDEO_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setBusFlyRatio(MathUtils.genRate(detailDto.getBusFlyIncome().subtract(beforeBusFlyIncome), beforeBusFlyIncome));
        detailDto.setBusFlyWeekRatio(MathUtils.genRate(detailDto.getBusFlyIncome().subtract(weekBusFlyIncome), weekBusFlyIncome));

        detailDto.setDpaIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.DPA)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeDpaIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.DPA)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekDpaIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.DPA)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setDpaRatio(MathUtils.genRate(detailDto.getDpaIncome().subtract(beforeDpaIncome), beforeDpaIncome));
        detailDto.setDpaWeekRatio(MathUtils.genRate(detailDto.getDpaIncome().subtract(weekDpaIncome), weekDpaIncome));

        detailDto.setAdxIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.ADX)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeAdxIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.ADX)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekAdxIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.ADX)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setAdxRatio(MathUtils.genRate(detailDto.getAdxIncome().subtract(beforeAdxIncome), beforeAdxIncome));
        detailDto.setAdxWeekRatio(MathUtils.genRate(detailDto.getAdxIncome().subtract(weekAdxIncome), weekAdxIncome));

        detailDto.setSellGoodIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeSellGoodIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekSellGoodIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setSellGoodRatio(MathUtils.genRate(detailDto.getSellGoodIncome().subtract(beforeSellGoodIncome), beforeSellGoodIncome));
        detailDto.setSellGoodWeekRatio(MathUtils.genRate(detailDto.getSellGoodIncome().subtract(weekSellGoodIncome), weekSellGoodIncome));

        detailDto.setVideoIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.VIDEO_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeVideoIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.VIDEO_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekVideoIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.VIDEO_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setVideoRatio(MathUtils.genRate(detailDto.getVideoIncome().subtract(beforeVideoIncome), beforeVideoIncome));
        detailDto.setVideoWeekRatio(MathUtils.genRate(detailDto.getVideoIncome().subtract(weekVideoIncome), weekVideoIncome));

        detailDto.setLiveIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.LIVING_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeLiveIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.LIVING_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekLiveIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.LIVING_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setLiveRatio(MathUtils.genRate(detailDto.getLiveIncome().subtract(beforeLiveIncome), beforeLiveIncome));
        detailDto.setLiveWeekRatio(MathUtils.genRate(detailDto.getLiveIncome().subtract(weekLiveIncome), weekLiveIncome));

        detailDto.setUpNonIncome(yesList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.NON_STANDARD_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
        BigDecimal beforeUpNonIncome = beforeList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.NON_STANDARD_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal weekUpNonIncome = weekAgoList.stream().filter(dto -> dto.getWxSaleProductType().equals(WxSaleProductType.NON_STANDARD_PICK_UP)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
        detailDto.setUpNonRatio(MathUtils.genRate(detailDto.getUpNonIncome().subtract(beforeUpNonIncome), beforeUpNonIncome));
        detailDto.setUpNonWeekRatio(MathUtils.genRate(detailDto.getUpNonIncome().subtract(weekUpNonIncome), weekUpNonIncome));

        return detailDto;

    }

    private WxSaleAggRatioDto buildSecondRatioDto(List<WxSaleCompositionDto> yesList, List<WxSaleCompositionDto> beforeList, List<WxSaleCompositionDto> weekAgoList, Boolean isSellGood) {
        if (isSellGood == null || !isSellGood) {
            yesList = yesList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
            beforeList = beforeList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
            weekAgoList = weekAgoList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
        }
        WxSaleAggRatioDto dto = new WxSaleAggRatioDto();
        dto.setAggLevel(AggLevelEnum.GROUP_BY_TOTAL.getCode());
        dto.setDayIncomeAmount(yesList.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setBeforeYesAmount(beforeList.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setWeekAgoAmount(weekAgoList.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setDayRatio(MathUtils.genRate(dto.getDayIncomeAmount().subtract(dto.getBeforeYesAmount()), dto.getBeforeYesAmount()));
        dto.setWeekAgoRatio(MathUtils.genRate(dto.getDayIncomeAmount().subtract(dto.getWeekAgoAmount()), dto.getWeekAgoAmount()));
        dto.setCashConsume(yesList.stream().map(WxSaleCompositionDto::getCashConsume).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setRedPacketAmount(yesList.stream().map(WxSaleCompositionDto::getRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setSpecialRedPacketAmount(yesList.stream().map(WxSaleCompositionDto::getSpecialRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setShowCount(yesList.stream().map(WxSaleCompositionDto::getShowCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setClickCount(yesList.stream().map(WxSaleCompositionDto::getClickCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add));
        dto.setCpc(AdUtils.getCpc(dto.getDayIncomeAmount(), dto.getClickCount()));
        dto.setCtr(AdUtils.getCtr(dto.getClickCount(), dto.getShowCount()));
        dto.setEcpm(AdUtils.getEcpm(dto.getDayIncomeAmount(), dto.getShowCount()));
        return dto;
    }

    public List<Integer> queryTopAgentCustomerIds(List<WxSaleCompositionDto> yesList, Boolean isSellGood, int topSize) {
        if (CollectionUtils.isEmpty(yesList)) {
            return new ArrayList<>();
        }
        if (!isSellGood) {
            yesList = yesList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
        }
        Map<Integer, BigDecimal> agentMap = yesList.stream()
                .filter(a -> a.getAgentCustomerId() != null && a.getAgentCustomerId() > 0)
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getAgentCustomerId,
                        Collectors.mapping(WxSaleCompositionDto::getIncome, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        return agentMap.entrySet().stream()
                .map(entry -> WxSaleCompositionDto.builder().agentCustomerId(entry.getKey()).income(entry.getValue()).build())
                .sorted(Comparator.comparing(WxSaleCompositionDto::getIncome).reversed())
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getAgentCustomerId() > 0)
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getIncome().compareTo(new BigDecimal(0)) > 0)
                .limit(topSize)
                .map(WxSaleCompositionDto::getAgentCustomerId)
                .collect(Collectors.toList());
    }

    public List<Integer> queryTopBrandIds(List<WxSaleCompositionDto> yesList, Boolean isSellGood, Integer topSize) {
        if (CollectionUtils.isEmpty(yesList)) {
            return new ArrayList<>();
        }
        if (!isSellGood) {
            yesList = yesList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
        }
        Map<Integer, BigDecimal> brandMap = yesList.stream()
                .filter(dto -> dto.getProductId() != null && dto.getProductId() > 0)
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getProductId,
                        Collectors.mapping(WxSaleCompositionDto::getIncome, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        return brandMap.entrySet().stream()
                .map(entry -> WxSaleCompositionDto.builder().productId(entry.getKey()).income(entry.getValue()).build())
                .sorted(Comparator.comparing(WxSaleCompositionDto::getIncome).reversed())
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getProductId() > 0)
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getIncome().compareTo(new BigDecimal(0)) > 0)
                .limit(topSize)
                .map(WxSaleCompositionDto::getProductId)
                .collect(Collectors.toList());
    }

    public List<Integer> queryTopCustomerIds(List<WxSaleCompositionDto> yesList, Boolean isSellGood, Integer topSize) {
        if (CollectionUtils.isEmpty(yesList)) {
            return new ArrayList<>();
        }
        if (!isSellGood) {
            yesList = yesList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
        }
        Map<Integer, BigDecimal> customerMap = yesList.stream()
                .filter(dto -> dto.getCustomerId() != null && dto.getCustomerId() > 0)
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getCustomerId,
                        Collectors.mapping(WxSaleCompositionDto::getIncome, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        return customerMap.entrySet().stream()
                .map(entry -> WxSaleCompositionDto.builder().customerId(entry.getKey()).income(entry.getValue()).build())
                .sorted(Comparator.comparing(WxSaleCompositionDto::getIncome).reversed())
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getCustomerId() > 0)
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getIncome().compareTo(new BigDecimal(0)) > 0)
                .limit(TOP_SIZE)
                .map(WxSaleCompositionDto::getCustomerId)
                .collect(Collectors.toList());
    }

    public List<Integer> queryTopGroupIds(List<WxSaleCompositionDto> yesList, Boolean isSellGood, Integer topSize) {
        if (CollectionUtils.isEmpty(yesList)) {
            return new ArrayList<>();
        }
        if (!isSellGood) {
            yesList = yesList.stream().filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD)).collect(Collectors.toList());
        }
        Map<Integer, BigDecimal> groupMap = yesList.stream()
                .filter(dto -> dto.getCompanyGroupId() != null && dto.getCompanyGroupId() > 0)
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getCompanyGroupId,
                        Collectors.mapping(WxSaleCompositionDto::getIncome, Collectors.reducing(BigDecimal.ZERO, BigDecimal::add))));
        return groupMap.entrySet().stream()
                .map(entry -> WxSaleCompositionDto.builder().companyGroupId(entry.getKey()).income(entry.getValue()).build())
                .sorted(Comparator.comparing(WxSaleCompositionDto::getIncome).reversed())
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getCompanyGroupId() > 0)
                .filter(wxSaleCompositionDto -> wxSaleCompositionDto.getIncome().compareTo(new BigDecimal(0)) > 0)
                .limit(topSize)
                .map(WxSaleCompositionDto::getCompanyGroupId)
                .collect(Collectors.toList());
    }

    public List<WxSaleAggRatioDto> buildNextResourceInfo(List<List<WxSaleCompositionDto>> monthList) {
        List<WxSaleCompositionDto> yesList = monthList.get(monthList.size() - 1);
        List<WxSaleCompositionDto> beforeList = monthList.get(monthList.size() - 2);
        List<WxSaleCompositionDto> weekAgoList = monthList.get(monthList.size() - 8);

        List<WxSaleAggRatioDto> typeDtoList = new ArrayList<>();
        BigDecimal total = yesList.stream()
                .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                .map(a -> a.getIncome()).reduce(BigDecimal.ZERO, BigDecimal::add);
        WxSaleAggRatioDto totalDto = buildSecondRatioDto(yesList, beforeList, weekAgoList, false);
        totalDto.setDayIncomeAmount(total);
        totalDto.setAggLevel(AggLevelEnum.GROUP_BY_TOTAL.getCode());
        totalDto.setWxSaleAggType(WxSaleAggType.RESOURCE_PRODUCT);
        totalDto.setAggId(0);
        totalDto.setAggName("总计");
        totalDto.setIncomeProportion(new BigDecimal(1));
        typeDtoList.add(totalDto);
        for (WxSaleProductType wxSaleProductType : WxSaleProductType.queryNormalType()) {
            WxSaleAggRatioDto typeDto = new WxSaleAggRatioDto();
            typeDto.setAggName(wxSaleProductType.getName());
            typeDto.setWxSaleAggType(WxSaleAggType.RESOURCE_PRODUCT);
            typeDto.setAggId(wxSaleProductType.getCode());
            typeDto.setAggLevel(AggLevelEnum.GROUP_BY_TOTAL.getCode());
            typeDto.setDayIncomeAmount(yesList.stream().filter(a -> a.getWxSaleProductType().equals(wxSaleProductType)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            typeDto.setIncomeProportion(MathUtils.genRate(typeDto.getDayIncomeAmount(), total));
            BigDecimal beforeYesIncome = beforeList.stream().filter(a -> a.getWxSaleProductType().equals(wxSaleProductType)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal weekAgoIncome = weekAgoList.stream().filter(a -> a.getWxSaleProductType().equals(wxSaleProductType)).map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
            typeDto.setDayRatio(MathUtils.genRate(typeDto.getDayIncomeAmount().subtract(beforeYesIncome), beforeYesIncome));
            typeDto.setWeekAgoRatio(MathUtils.genRate(typeDto.getDayIncomeAmount().subtract(weekAgoIncome), weekAgoIncome));
            typeDtoList.add(typeDto);
        }

        for (IncomeTeamEnum incomeTeamEnum : IncomeTeamEnum.normalIncomeTeamEnum()) {
            WxSaleAggRatioDto typeDto = new WxSaleAggRatioDto();
            typeDto.setAggName(incomeTeamEnum.getDesc());
            typeDto.setAggId(incomeTeamEnum.getCode() + 100);
            typeDto.setAggLevel(AggLevelEnum.GROUP_BY_TOTAL.getCode());
            typeDto.setWxSaleAggType(WxSaleAggType.RESOURCE_PRODUCT);
            typeDto.setDayIncomeAmount(yesList.stream()
                    .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                    .filter(a -> a.getIncomeTeamEnum().equals(incomeTeamEnum))
                    .map(WxSaleCompositionDto::getIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add));
            typeDto.setIncomeProportion(MathUtils.genRate(typeDto.getDayIncomeAmount(), total));
            BigDecimal beforeYesIncome = beforeList.stream()
                    .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                    .filter(a -> a.getIncomeTeamEnum().equals(incomeTeamEnum))
                    .map(WxSaleCompositionDto::getIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            BigDecimal weekAgoIncome = weekAgoList.stream()
                    .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                    .filter(a -> a.getIncomeTeamEnum().equals(incomeTeamEnum))
                    .map(WxSaleCompositionDto::getIncome)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            typeDto.setBeforeYesAmount(beforeYesIncome);
            typeDto.setWeekAgoAmount(weekAgoIncome);
            typeDto.setDayRatio(MathUtils.genRate(typeDto.getDayIncomeAmount().subtract(beforeYesIncome), beforeYesIncome));
            typeDto.setWeekAgoRatio(MathUtils.genRate(typeDto.getDayIncomeAmount().subtract(weekAgoIncome), weekAgoIncome));
            if (incomeTeamEnum.equals(IncomeTeamEnum.BRAND)) {
                typeDtoList.add(1, typeDto);
            } else if (incomeTeamEnum.equals(IncomeTeamEnum.RTB)) {
                typeDtoList.add(4, typeDto);
            } else if (incomeTeamEnum.equals(IncomeTeamEnum.UP)) {
                typeDtoList.add(10, typeDto);
            }
        }
        return typeDtoList;
    }

    public List<WxSaleAggRatioDto> buildSecondAgentCustomerInfo(List<List<WxSaleCompositionDto>> monthList, WxSaleAggType aggType, Integer aggId) {
        List<WxSaleCompositionDto> yesList = monthList.get(monthList.size() - 1);
        List<WxSaleCompositionDto> beforeList = monthList.get(monthList.size() - 2);
        List<WxSaleCompositionDto> weekAgoList = monthList.get(monthList.size() - 8);
        List<WxSaleAggRatioDto> detailDtoList = Lists.newArrayList();
        WxSaleAggRatioDto total = buildSecondRatioDto(yesList, beforeList, weekAgoList, false);
        total.setAggId(0);
        total.setAggLevel(AggLevelEnum.GROUP_BY_TOTAL.getCode());
        total.setWxSaleAggType(WxSaleAggType.AGENT_CUSTOMER);
        total.setAggName("总计");
        if (aggType != null && aggId != null) {
            SaleDetailDto saleDetailDto = querySaleByAccount(yesList, SaleQueryDto.builder()
                    .isBuildSaleGroup(false)
                    .wxSaleAggType(aggType)
                    .aggId(aggId)
                    .build());
            saleDetailDto.setOperator(null);
            total.setSaleDetailDto(saleDetailDto);
        }
        detailDtoList.add(total);
        List<Integer> topAgentCustomerIds = queryTopAgentCustomerIds(yesList, false, TOP_SIZE);
        Map<Integer, List<WxSaleCompositionDto>> yesAgentMap = yesList.stream()
                .filter(dto -> dto.getAgentCustomerId() != null && dto.getAgentCustomerId() > 0)
                .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getAgentCustomerId));
        Map<Integer, List<WxSaleCompositionDto>> beforeAgentMap = beforeList.stream()
                .filter(dto -> dto.getAgentCustomerId() != null && dto.getAgentCustomerId() > 0)
                .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getAgentCustomerId));
        Map<Integer, List<WxSaleCompositionDto>> weekAgoAgentMap = weekAgoList.stream()
                .filter(dto -> dto.getAgentCustomerId() != null && dto.getAgentCustomerId() > 0)
                .filter(dto -> !dto.getWxSaleProductType().equals(WxSaleProductType.SELL_GOOD))
                .collect(Collectors.groupingBy(WxSaleCompositionDto::getAgentCustomerId));
        Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(new ArrayList<>(yesAgentMap.keySet()));
        for (Integer agentCustomerId : topAgentCustomerIds) {
            WxSaleAggRatioDto detailDto = new WxSaleAggRatioDto();
            detailDto.setAggId(agentCustomerId);
            detailDto.setAggLevel(AggLevelEnum.GROUP_BY_TOTAL.getCode());
            detailDto.setWxSaleAggType(WxSaleAggType.AGENT_CUSTOMER);
            detailDto.setAggName(customerPoMap.getOrDefault(agentCustomerId, new CustomerPo()).getUsername());
            detailDto.setDayIncomeAmount(yesAgentMap.getOrDefault(agentCustomerId, new ArrayList<>()).stream().map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            detailDto.setBeforeYesAmount(beforeAgentMap.getOrDefault(agentCustomerId, new ArrayList<>()).stream().map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            detailDto.setWeekAgoAmount(weekAgoAgentMap.getOrDefault(agentCustomerId, new ArrayList<>()).stream().map(WxSaleCompositionDto::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add));
            detailDto.setDayRatio(MathUtils.genRate(detailDto.getDayIncomeAmount().subtract(detailDto.getBeforeYesAmount()), detailDto.getBeforeYesAmount()));
            detailDto.setWeekAgoRatio(MathUtils.genRate(detailDto.getDayIncomeAmount().subtract(detailDto.getWeekAgoAmount()), detailDto.getWeekAgoAmount()));
            detailDto.setIncomeProportion(MathUtils.genRate(detailDto.getDayIncomeAmount(), total.getDayIncomeAmount()));
            detailDto.setSaleDetailDto(querySaleByAccount(yesAgentMap.getOrDefault(agentCustomerId, new ArrayList<>()), SaleQueryDto.builder()
                    .isBuildSaleGroup(false)
                    .aggId(agentCustomerId)
                    .wxSaleAggType(WxSaleAggType.AGENT_CUSTOMER)
                    .build()));
            detailDtoList.add(detailDto);
        }
        return detailDtoList;
    }


    public SaleDetailDto querySaleByAccount(List<WxSaleCompositionDto> dtoList, SaleQueryDto saleQueryDto) {
        if (!WxSaleAggType.needSaleAggType.contains(saleQueryDto.getWxSaleAggType())) {
            return SaleDetailDto.builder().build();
        }
        List<Integer> saleIds = dtoList.stream().map(WxSaleCompositionDto::getSaleIds).filter(Objects::nonNull).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleIds)) {
            return SaleDetailDto.builder().build();
        }
        SaleDetailDto saleDetailDto = new SaleDetailDto();
        Map<Integer, SaleDto> saleMapInIds = saleService.getAllSaleMapWithCache();
        if (saleQueryDto.getWxSaleAggType().equals(WxSaleAggType.AGENT_CUSTOMER)) {
            saleDetailDto.setChannelSaleList(saleIds.stream()
                    .filter(saleId -> saleMapInIds.containsKey(saleId) && SaleTypeEnum.channelSaleTypes.contains(saleMapInIds.get(saleId).getType()))
                    .map(saleId -> saleMapInIds.get(saleId).getId())
                    .collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(saleDetailDto.getChannelSaleList())) {
                saleDetailDto.setChannelSale(saleDetailDto.getChannelSaleList().stream()
                        .map(saleId -> saleMapInIds.get(saleId).getName())
                        .collect(Collectors.joining(",")));
            }
        } else {
            saleDetailDto.setDirectSaleList(saleIds.stream()
                    .filter(saleId -> saleMapInIds.containsKey(saleId) && SaleTypeEnum.directSaleTypes.contains(saleMapInIds.get(saleId).getType()))
                    .map(saleId -> saleMapInIds.get(saleId).getId())
                    .collect(Collectors.toList()));
            if (!CollectionUtils.isEmpty(saleDetailDto.getDirectSaleList())) {
                saleDetailDto.setDirectSale(saleDetailDto.getDirectSaleList().stream()
                        .map(saleId -> saleMapInIds.get(saleId).getName())
                        .collect(Collectors.joining(",")));
            }
        }
        saleDetailDto.setOperatorList(saleIds.stream()
                .filter(saleId -> saleMapInIds.containsKey(saleId) && SaleTypeEnum.EFFECT_OPERATE.getCode().equals(saleMapInIds.get(saleId).getType()))
                .map(saleId -> saleMapInIds.get(saleId).getId())
                .collect(Collectors.toList()));
        if (!CollectionUtils.isEmpty(saleDetailDto.getOperatorList())) {
            saleDetailDto.setOperator(saleDetailDto.getOperatorList().stream()
                    .map(saleId -> saleMapInIds.get(saleId).getName())
                    .collect(Collectors.joining(",")));
        }
        return saleDetailDto;
    }


    public SaleDetailDto getSalesName(SaleQueryDto saleQueryDto) {
        if (saleQueryDto.getAggId() == null || saleQueryDto.getWxSaleAggType() == null) {
            return SaleDetailDto.builder().build();
        }
        String key = wxAppCacheManager.genCacheHashKey("SALE_NAME_DETAIL", saleQueryDto.getWxSaleAggType().name(), String.valueOf(saleQueryDto.getAggId()), String.valueOf(saleQueryDto.getIsBuildSaleGroup()));
        SaleDetailDto result = (SaleDetailDto) cacheManager.getValue(key);
        if (result != null) {
            return result;
        }
        try {
            List<Integer> agentIds;
            List<Integer> agentAccountIds;
            List<Integer> accountIds = new ArrayList<>();
            String operator = Strings.EMPTY;
            QueryAccountParam accountParam = QueryAccountParam.builder().build();
            accountParam = buildAccountParam(accountParam, saleQueryDto);
            List<AccountBaseDto> agentAccountList = fastQueryAccountService.fetch(accountParam, AccountFieldMapping.accountId);
            agentAccountIds = agentAccountList.stream().map(AccountBaseDto::getAccountId).distinct().collect(Collectors.toList());
            List<CrmAgentPo> crmAgentPoList = crmAgentRepo.queryAgentDtosByAgentAccountIds(agentAccountIds);
            agentIds = crmAgentPoList.stream().map(CrmAgentPo::getId).distinct().collect(Collectors.toList());
            List<Integer> operatorIds = Lists.newArrayList();
            accountIds.addAll(agentAccountIds);
            if (!CollectionUtils.isEmpty(agentIds)) {
                List<AccAccountBiliUserDto> accountBiliUser = accountBiliUserService.getAccountBiliUserIdByParam(QueryAccountBiliUserParam.builder()
                        .confTypes(Lists.newArrayList(BiliUserType.OPERATOR_MANAGER.getCode()))
                        .accountIds(agentAccountIds)
                        .build());
                List<String> biliUsername = accountBiliUser.stream().map(AccAccountBiliUserDto::getBiliUsername).distinct().collect(Collectors.toList());
                List<SaleDto> sales = saleService.getListByQueryDto(QuerySaleDto.builder().saleTypes(Lists.newArrayList(SaleTypeEnum.EFFECT_OPERATE.getCode())).emails(biliUsername).build());
                operator = sales.stream().map(SaleDto::getName).filter(Objects::nonNull).filter(a -> !a.contains("测试")).map(t -> t + "").collect(Collectors.joining(","));
                operatorIds = sales.stream().map(SaleDto::getId).distinct().collect(Collectors.toList());
                QueryAccountParam param = QueryAccountParam.builder().dependencyAgentIds(agentIds).build();
                List<AccountBaseDto> accountList = fastQueryAccountService.fetch(param, AccountFieldMapping.accountId);
                accountIds.addAll(accountList.stream().map(AccountBaseDto::getAccountId).distinct().collect(Collectors.toList()));
            }
            Map<Integer, Map<BiliUserType, List<Integer>>> accountSaleSeaBelongingMap = iSaleSeaBelongingService.queryAccountUserMap(accountIds);
            List<Integer> saleIds = Optional.of(accountSaleSeaBelongingMap.values()).orElse(new ArrayList<>())
                    .stream().map(a -> new ArrayList<>(Optional.of(a.values()).orElse(new ArrayList<>())))
                    .flatMap(Collection::stream)
                    .flatMap(Collection::stream)
                    .distinct()
                    .collect(Collectors.toList());
            Map<Integer, SaleDto> saleMapInIds = saleService.getSaleMapInIds(saleIds);
            SaleDetailDto saleDetailDto = getSalesNameStr(saleMapInIds, accountSaleSeaBelongingMap, accountIds);
            saleDetailDto.setOperator(operator);
            saleDetailDto.setOperatorList(operatorIds);
            if (saleQueryDto.getIsBuildSaleGroup()) {
                List<Integer> channelSaleList = saleDetailDto.getChannelSaleList();
                Integer level = -1;
                String saleGroupName = StringUtils.EMPTY;
                for (Integer saleId : channelSaleList) {
                    SaleDto saleDto = saleMapInIds.getOrDefault(saleId, new SaleDto());
                    if (level < Optional.ofNullable(saleDto.getLevel()).orElse(-1)) {
                        level = saleDto.getLevel();
                        saleGroupName = saleDto.getGroupName();
                    }
                }
                saleDetailDto.setSaleGroupName(saleGroupName);
            }
            cacheManager.setValueWithTime(key, saleDetailDto, TimeUnit.HOURS, 2);
            return saleDetailDto;
        } catch (Exception e) {
            log.error("getSalesNameByAgentCustomerError, saleQueryDto:{}", GsonUtils.getGson().toJson(saleQueryDto), e);
        }
        return SaleDetailDto.builder().build();
    }

    private QueryAccountParam buildAccountParam(QueryAccountParam param, SaleQueryDto saleQueryDto) {
        if (saleQueryDto.getWxSaleAggType() != null && saleQueryDto.getAggId() != null && saleQueryDto.getAggId() > 0) {
            switch (saleQueryDto.getWxSaleAggType()) {
                case CUSTOMER:
                case AGENT_CUSTOMER:
                    param.setCustomerId(saleQueryDto.getAggId());
                    break;
                case BRAND:
                    param.setProductId(saleQueryDto.getAggId());
                    break;
                case GROUP:
                    param.setGroupId(saleQueryDto.getAggId());
                    break;
            }
        }
        return param;
    }

    private SaleDetailDto getSalesNameStr(Map<Integer, SaleDto> saleMapInIds, Map<Integer, Map<BiliUserType, List<Integer>>> accountSaleSeaBelongingMap, List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return SaleDetailDto.builder()
                    .ChannelSale(StringUtils.EMPTY)
                    .operator(StringUtils.EMPTY)
                    .channelSaleList(Lists.newArrayList())
                    .build();
        }
        List<String> result = new ArrayList<>();
        List<String> directSaleNames = new ArrayList<>();
        List<String> belongSaleNames = new ArrayList<>();
        List<Integer> channelSaleIds = new ArrayList<>();
        List<Integer> directSaleList = new ArrayList<>();
        accountIds = accountIds.stream().distinct().collect(Collectors.toList());
        for (Integer accountId : accountIds) {
            Map<BiliUserType, List<Integer>> userTypeListMap = accountSaleSeaBelongingMap.getOrDefault(accountId, Maps.newHashMap());
            List<Integer> directSales = userTypeListMap.getOrDefault(BiliUserType.STRAIGHT_MANAGER, new ArrayList<>()).stream().distinct().collect(Collectors.toList());
            List<Integer> belongSales = userTypeListMap.getOrDefault(BiliUserType.CHANNEL_MANAGER, new ArrayList<>()).stream().distinct().collect(Collectors.toList());
            channelSaleIds.addAll(belongSales);
            directSaleList.addAll(directSales);
            for (Integer belongSale : belongSales) {
                belongSaleNames.add(saleMapInIds.getOrDefault(belongSale, new SaleDto()).getName());
            }
            for (Integer directSale : directSales) {
                directSaleNames.add(saleMapInIds.getOrDefault(directSale, new SaleDto()).getName());
            }
        }
        directSaleList = directSaleList.stream().distinct().collect(Collectors.toList());
        channelSaleIds = channelSaleIds.stream().distinct().collect(Collectors.toList());
        result.add(directSaleNames.stream().distinct().filter(Objects::nonNull).filter(a -> !a.contains("测试")).map(t -> t + "").collect(Collectors.joining(",")));
        result.add(belongSaleNames.stream().distinct().filter(Objects::nonNull).filter(a -> !a.contains("测试")).map(t -> t + "").collect(Collectors.joining(",")));
        return SaleDetailDto.builder()
                .directSale(result.get(0))
                .ChannelSale(result.get(1))
                .channelSaleList(channelSaleIds)
                .directSaleList(directSaleList)
                .build();
    }


    private List<WxSaleCompositionDto> contractToCompositionDto(AchievementContractCheckQuotaDto checkedBillQuotaDto) {
        List<ContractAdIncome> adIncomeList = new ArrayList<>();
        adIncomeList.addAll(Optional.ofNullable(checkedBillQuotaDto.getClosedBill()).orElse(new ArrayList<>()));
        adIncomeList.addAll(Optional.ofNullable(checkedBillQuotaDto.getUnClosedBill()).orElse(new ArrayList<>()));
        List<Integer> contractIds = adIncomeList.stream().map(ContractAdIncome::getContractId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<ContractBaseDto> contracts = contractQueryService.getContractBaseDtoByQueryParam(QueryContractParam.builder()
                .contractIds(contractIds)
                .build());
        Map<Integer, ContractBaseDto> contractMap = contracts.stream().collect(Collectors.toMap(ContractBaseDto::getId, Function.identity()));
        Map<Integer, List<SaleDto>> contractSalesMap = contractQueryService.getContractSaleDtosMap(contractIds);
        if (CollectionUtils.isEmpty(adIncomeList)) {
            return new ArrayList<>();
        }
        List<Integer> accountIds = contracts.stream().map(ContractBaseDto::getAccountId).filter(a -> a != null && a > 0).distinct().collect(Collectors.toList());
        List<Integer> agentIds = contracts.stream().map(ContractBaseDto::getAgentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmAgentPo> crmAgentPoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(agentIds)) {
            crmAgentPoMap = crmAgentRepo.queryAgentDtoMapByAgentIds(agentIds);
            List<Integer> agentAccountIds = crmAgentPoMap.values().stream().map(CrmAgentPo::getSysAgentId).distinct().collect(Collectors.toList());
            accountIds.addAll(Optional.ofNullable(agentAccountIds).orElse(new ArrayList<>()));
        }
        List<AccountBaseDto> accountBaseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds)
                .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId);
        Map<Integer, AccountBaseDto> accountMap = accountBaseDtoList.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, a -> a, (a, b) -> a));
        List<WxSaleCompositionDto> result = new ArrayList<>();
        for (ContractAdIncome adIncome : adIncomeList) {
            ContractBaseDto contract = contractMap.getOrDefault(adIncome.getContractId(), ContractBaseDto.builder().build());
            AccountBaseDto accountBaseDto = accountMap.getOrDefault(contract.getAccountId(), new AccountBaseDto());
            CrmAgentPo crmAgentPo = crmAgentPoMap.getOrDefault(contract.getAgentId(), new CrmAgentPo());
            AccountBaseDto agentAccount = accountMap.getOrDefault(Optional.ofNullable(crmAgentPo.getSysAgentId()).orElse(-1), new AccountBaseDto());
            WxSaleCompositionDto compositionDto = new WxSaleCompositionDto();
            compositionDto.setIncomeTeamEnum(IncomeTeamEnum.BRAND);
            compositionDto.setDate(adIncome.getClosingDate() != null ? adIncome.getClosingDate() : adIncome.getLaunchDate());
            compositionDto.setAccountId(contract.getAccountId());
            compositionDto.setAgentId(contract.getAgentId());
            List<SaleDto> sales = contractSalesMap.getOrDefault(adIncome.getContractId(), Collections.emptyList());
            compositionDto.setSaleIds(Optional.ofNullable(sales).orElse(new ArrayList<>()).stream().map(SaleDto::getId).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            compositionDto.setCustomerId(accountBaseDto.getCustomerId());
            compositionDto.setCompanyGroupId(contract.getGroupId());
            compositionDto.setProductId(contract.getProductId());
            compositionDto.setAgentCustomerId(Optional.ofNullable(agentAccount.getCustomerId()).orElse(0));
            compositionDto.setIncome(Utils.fromFenToYuan(adIncome.getAmount()));
            // 花火 -- 直播商单
            if (DepAchieveConfigBeta.CONTRACT_ORDER_LIVE_ANCHOR_PICKUP_FIRST_CATE.equals(adIncome.getOrderFirstCategoryId())) {
                compositionDto.setIncomeTeamEnum(IncomeTeamEnum.UP);
                compositionDto.setWxSaleProductType(WxSaleProductType.CONTRACT_PICK_UP);
            }
            // 花火 -- 其他商单
            else if (DepAchieveConfigBeta.CONTRACT_ORDER_PICKUP_FIRST_CATE.equals(adIncome.getOrderFirstCategoryId())
                    || DepAchieveConfigBeta.OTHER_PICKUP_ORDER_TYPE.contains(adIncome.getOrderType())) {
                compositionDto.setIncomeTeamEnum(IncomeTeamEnum.UP);
                compositionDto.setWxSaleProductType(WxSaleProductType.CONTRACT_PICK_UP);
            } else if (Arrays.asList(OrderUtils.HARD_AD_ORDER_TYPES).contains(adIncome.getOrderType())) {
                compositionDto.setWxSaleProductType(WxSaleProductType.HARD_BRAND);
            } else if (CrmOrderType.NON_STANDARD.getCode().equals(adIncome.getOrderType())) {
                compositionDto.setWxSaleProductType(WxSaleProductType.NON_STANDARD_BRAND);
            } else {
                compositionDto.setWxSaleProductType(WxSaleProductType.UNKNOWN);
            }
            result.add(compositionDto);
        }
        return result;
    }

    public List<WxSaleCompositionDto> rtbToCompositionDto(List<AchievementRtbData> rtbList, WxSaleProductType wxSaleProductType, Timestamp base) {
        if (CollectionUtils.isEmpty(rtbList)) {
            return new ArrayList<>();
        }
        List<Integer> accountIds = rtbList.stream().map(AchievementRtbData::getAccountId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<AccountBaseDto> accountBaseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds)
                .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId);
        List<Integer> agentIds = accountBaseDtoList.stream().map(AccountBaseDto::getDependencyAgentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmAgentPo> crmAgentPoMap = Maps.newHashMap();
        Map<String, Integer> userSaleMap = Maps.newHashMap();
        Map<Integer, List<AccAccountBiliUserDto>> accountOperateMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(agentIds)) {
            crmAgentPoMap = crmAgentRepo.queryAgentDtoMapByAgentIds(agentIds);
            List<Integer> agentAccountIds = crmAgentPoMap.values().stream().map(CrmAgentPo::getSysAgentId).distinct().collect(Collectors.toList());
            accountIds.addAll(agentAccountIds);
            List<AccountBaseDto> agentAccountList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(agentAccountIds)
                    .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId);
            if (!CollectionUtils.isEmpty(accountBaseDtoList)) {
                accountBaseDtoList.addAll(Optional.ofNullable(agentAccountList).orElse(new ArrayList<>()));
            }
            List<AccAccountBiliUserDto> accountBiliUser = accountBiliUserService.getAccountBiliUserIdByParam(QueryAccountBiliUserParam.builder()
                    .confTypes(Lists.newArrayList(BiliUserType.OPERATOR_MANAGER.getCode()))
                    .accountIds(agentAccountIds)
                    .build());
            List<String> biliUsername = accountBiliUser.stream().map(AccAccountBiliUserDto::getBiliUsername).distinct().collect(Collectors.toList());
            List<SaleDto> sales = saleService.getListByQueryDto(QuerySaleDto.builder().emails(biliUsername).build());
            userSaleMap = sales.stream().collect(Collectors.toMap(SaleDto::getEmail, SaleDto::getId));
            accountOperateMap = accountBiliUser.stream().collect(Collectors.groupingBy(AccAccountBiliUserDto::getAccountId));
        }
        List<SaleSeaBelonging> saleSeaBelongings = saleSeaBelongingService.queryAccountBelonging(QuerySaleSeaBelongingDto.builder()
                .productCategory(SaleSeaProductCategory.RTB.getCode())
                .accountIds(accountIds)
                .bindDate(Utils.getBeginOfDay(base))
                .build());

        Map<Integer, SaleSeaBelonging> saleSeaBelongingMap = saleSeaBelongings.stream().collect(Collectors.toMap(SaleSeaBelonging::getAccountId, Function.identity()));
        Map<Integer, AccountBaseDto> accountMap = accountBaseDtoList.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, a -> a, (a, b) -> a));
        List<WxSaleCompositionDto> result = new ArrayList<>();
        Map<Integer, List<AchievementRtbData>> aggMap = rtbList.stream().filter(a -> a.getAccountId() != null).collect(Collectors.groupingBy(AchievementRtbData::getAccountId));
        List<AchievementRtbData> aggList = new ArrayList<>();
        for (Integer accountId : aggMap.keySet()) {
            List<AchievementRtbData> tempList = aggMap.getOrDefault(accountId, new ArrayList<>());
            if (CollectionUtils.isEmpty(tempList)) {
                continue;
            }
            AchievementRtbData rtbData = AchievementRtbData.builder()
                    .accountId(tempList.get(0).getAccountId())
                    .totalConsume(tempList.stream().map(AchievementRtbData::getTotalConsume).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .cashAmount(tempList.stream().map(AchievementRtbData::getCashAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .redPacketAmount(tempList.stream().map(AchievementRtbData::getRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .specialRedPacketAmount(tempList.stream().map(AchievementRtbData::getSpecialRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .showCount(tempList.stream().map(AchievementRtbData::getShowCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .clickCount(tempList.stream().map(AchievementRtbData::getClickCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .build();
            aggList.add(rtbData);
        }

        for (AchievementRtbData rtbData : aggList) {
            WxSaleCompositionDto dto = new WxSaleCompositionDto();
            AccountBaseDto accountBaseDto = accountMap.getOrDefault(Optional.ofNullable(rtbData.getAccountId()).orElse(0), AccountBaseDto.builder()
                    .dependencyAgentId(0)
                    .customerId(0)
                    .productId(0)
                    .groupId(0)
                    .build());
            SaleSeaBelonging saleSeaBelonging = saleSeaBelongingMap.getOrDefault(rtbData.getAccountId(), SaleSeaBelonging.empty());
            CrmAgentPo crmAgentPo = crmAgentPoMap.getOrDefault(Optional.ofNullable(accountBaseDto.getDependencyAgentId()).orElse(0), CrmAgentPo.builder().sysAgentId(0).build());
            AccountBaseDto agentAccount = accountMap.getOrDefault(Optional.ofNullable(crmAgentPo.getSysAgentId()).orElse(-1), new AccountBaseDto());
            dto.setAccountId(Optional.ofNullable(rtbData.getAccountId()).orElse(0));
            try {
                dto.setSaleIds(Optional.ofNullable(saleSeaBelonging.getProductSaleMap()).orElse(new HashMap<>()).values().stream()
                        .map(a -> Optional.ofNullable(a).orElse(new HashMap<>()).values()).flatMap(Collection::stream)
                        .filter(Objects::nonNull).flatMap(Collection::stream).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            } catch (Exception e) {
                log.error("rtbToCompositionDtoSaleError", e);
                log.info("rtbToCompositionDtoSale,accountId:{}, saleSeaBelonging:{}", rtbData.getAccountId(), GsonUtils.toJson(saleSeaBelonging));
                dto.setSaleIds(new ArrayList<>());
            }
            dto.setAgentId(accountBaseDto.getDependencyAgentId());
            dto.setCustomerId(accountBaseDto.getCustomerId());
            dto.setProductId(accountBaseDto.getProductId());
            dto.setCompanyGroupId(accountBaseDto.getGroupId());
            dto.setAgentCustomerId(Optional.ofNullable(agentAccount.getCustomerId()).orElse(0));
            dto.setWxSaleProductType(wxSaleProductType);
            dto.setIncomeTeamEnum(IncomeTeamEnum.RTB);
            dto.setIncome(rtbData.getTotalConsume());
            dto.setCashConsume(rtbData.getCashAmount());
            dto.setRedPacketAmount(rtbData.getRedPacketAmount());
            dto.setSpecialRedPacketAmount(rtbData.getSpecialRedPacketAmount());
            dto.setShowCount(rtbData.getShowCount());
            dto.setClickCount(rtbData.getClickCount());
            enhanceSaleIds(dto, crmAgentPoMap, userSaleMap, accountOperateMap, saleSeaBelongingMap);
            result.add(dto);
        }
        return result;
    }

    public void enhanceSaleIds(WxSaleCompositionDto dto, Map<Integer, CrmAgentPo> crmAgentPoMap,
                               Map<String, Integer> userSaleMap,
                               Map<Integer, List<AccAccountBiliUserDto>> accountOperateMap,
                               Map<Integer, SaleSeaBelonging> saleSeaBelongingMap) {
        List<AccAccountBiliUserDto> operator = accountOperateMap.getOrDefault(crmAgentPoMap.getOrDefault(dto.getAgentId(), CrmAgentPo.builder().sysAgentId(0).build()).getSysAgentId(), Collections.emptyList());
        List<Integer> operatorSaleIds = operator.stream().map(user -> {
            return userSaleMap.get(user.getBiliUsername());
        }).filter(saleId -> Utils.isPositive(saleId)).distinct().collect(Collectors.toList());
        //直客销售
        List<Integer> directSaleIds = Collections.emptyList();
        SaleSeaBelonging saleSeaBelongingDirect = saleSeaBelongingMap.getOrDefault(dto.getAccountId(), SaleSeaBelonging.builder().build());
        if (!org.springframework.util.CollectionUtils.isEmpty(saleSeaBelongingDirect.getProductSaleMap())) {
            Map<BiliUserType, List<Integer>> userTypeMap = saleSeaBelongingDirect.getProductSaleMap().getOrDefault(SaleSeaProductCategory.RTB, Collections.emptyMap());
            directSaleIds = userTypeMap.getOrDefault(BiliUserType.STRAIGHT_MANAGER, Collections.emptyList())
                    .stream().filter(saleId -> Utils.isPositive(saleId)).distinct().collect(Collectors.toList());
        }
        //渠道销售
        List<Integer> channelSaleIds = Collections.emptyList();
        SaleSeaBelonging accSaleSeaBelongingChannel = saleSeaBelongingMap.getOrDefault(dto.getAccountId(), SaleSeaBelonging.builder().accountId(0).productSaleMap(new HashMap<>()).build());
        Map<BiliUserType, List<Integer>> userTypeMap = accSaleSeaBelongingChannel.getProductSaleMap().getOrDefault(SaleSeaProductCategory.RTB, Collections.emptyMap());
        List<Integer> configChannelSaleIds = userTypeMap.getOrDefault(BiliUserType.CHANNEL_MANAGER, Collections.emptyList())
                .stream().filter(saleId -> Utils.isPositive(saleId)).distinct().collect(Collectors.toList());
        //先取对应客户配置的渠道经理  取不到 在取代理商对应的渠道经理
        if (!org.springframework.util.CollectionUtils.isEmpty(configChannelSaleIds)) {
            channelSaleIds = configChannelSaleIds;
        } else {
            SaleSeaBelonging saleSeaBelongingChannel = saleSeaBelongingMap.getOrDefault(crmAgentPoMap.getOrDefault(dto.getAgentId(), CrmAgentPo.builder().sysAgentId(0).build()).getSysAgentId(), SaleSeaBelonging.builder().accountId(0).build());
            if (!org.springframework.util.CollectionUtils.isEmpty(saleSeaBelongingChannel.getProductSaleMap())) {
                Map<BiliUserType, List<Integer>> agentUserTypeMap = saleSeaBelongingChannel.getProductSaleMap().getOrDefault(SaleSeaProductCategory.RTB, Collections.emptyMap());
                channelSaleIds = agentUserTypeMap.getOrDefault(BiliUserType.CHANNEL_MANAGER, Collections.emptyList())
                        .stream().filter(saleId -> Utils.isPositive(saleId)).distinct().collect(Collectors.toList());
            }
        }
        //所有销售
        List<Integer> sales = Stream.of(channelSaleIds, operatorSaleIds, directSaleIds).flatMap(Collection::stream)
                .filter(saleId -> Utils.isPositive(saleId)).distinct().collect(Collectors.toList());
        dto.setSaleIds(sales);
    }

    public List<WxSaleCompositionDto> aggByAccount(List<WxSaleCompositionDto> rtbList) {
        Map<Integer, List<WxSaleCompositionDto>> aggMap = rtbList.stream().collect(Collectors.groupingBy(WxSaleCompositionDto::getAccountId));
        List<WxSaleCompositionDto> aggList = new ArrayList<>();
        for (Integer accountId : aggMap.keySet()) {
            List<WxSaleCompositionDto> tempList = aggMap.getOrDefault(accountId, new ArrayList<>());
            if (CollectionUtils.isEmpty(tempList)) {
                continue;
            }
            WxSaleCompositionDto rtbData = WxSaleCompositionDto.builder()
                    .accountId(tempList.get(0).getAccountId())
                    .income(tempList.stream().map(WxSaleCompositionDto::getIncome).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .cashConsume(tempList.stream().map(WxSaleCompositionDto::getCashConsume).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .redPacketAmount(tempList.stream().map(WxSaleCompositionDto::getRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .specialRedPacketAmount(tempList.stream().map(WxSaleCompositionDto::getSpecialRedPacketAmount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .showCount(tempList.stream().map(WxSaleCompositionDto::getShowCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .clickCount(tempList.stream().map(WxSaleCompositionDto::getClickCount).filter(Objects::nonNull).reduce(BigDecimal.ZERO, BigDecimal::add))
                    .agentId(tempList.get(0).getAgentId())
                    .customerId(tempList.get(0).getCustomerId())
                    .agentCustomerId(tempList.get(0).getAgentCustomerId())
                    .productId(tempList.get(0).getProductId())
                    .companyGroupId(tempList.get(0).getCompanyGroupId())
                    .saleIds(tempList.get(0).getSaleIds())
                    .build();
            aggList.add(rtbData);
        }
        return aggList;
    }

    public void buildCreativeInfo(QueryAchieveDto queryAchieveDto, List<WxSaleCompositionDto> dtoList) {
        Timestamp fromTime = queryAchieveDto.getDateBegin();
        Timestamp toTime = queryAchieveDto.getDateEnd();
        PerformanceAdQueryDto performanceAdQueryDto = PerformanceAdQueryDto.builder()
                .accountIds(queryAchieveDto.getAccountIds()).fromTime(fromTime).toTime(toTime).build();

        // 1天内有曝光的创意（这些创意可能是历史新增，也可能是这1天新增的）
        Map<Integer, List<Integer>> activateCreative = performanceAdService.buildActivateCreative(performanceAdQueryDto);

        // 1天内新增的创意
        // FIXME newCreatives switch crm
        Map<Integer, List<Integer>> newCreatives = performanceAdService.buildNewCreatives(performanceAdQueryDto);

        // 筛选出newCreatives中所有也存在于activateCreative的条目，换句话说，筛选出了1天内新增且有曝光的创意，即有效的新增创意
        newCreatives = achievementRtbService.retain(activateCreative, newCreatives);
        Map<Integer, List<Integer>> finalNewCreatives = newCreatives;

        dtoList.parallelStream().forEach(item -> {
            Integer accountId = item.getAccountId();
            if (toTime.toLocalDateTime().minusDays(1).isBefore(fromTime.toLocalDateTime())) {
                item.setTodayNewCreatives(finalNewCreatives.get(accountId) == null ? 0 : finalNewCreatives.get(accountId).size());
                item.setTodayOnlineCreatives(activateCreative.get(accountId) == null ? 0 : activateCreative.get(accountId).size());
            } else {
                item.setTodayNewCreatives(0);
                item.setTodayOnlineCreatives(0);
            }
        });
    }

    private List<WxSaleCompositionDto> pickUpToCompositionDto(List<AchievePickUpDto> pickUpDtoList) {
        if (CollectionUtils.isEmpty(pickUpDtoList)) {
            return new ArrayList<>();
        }
        List<Integer> accountIds = pickUpDtoList.stream().map(AchievePickUpDto::getAccountId).distinct().collect(Collectors.toList());
        List<AccountBaseDto> accountBaseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds)
                .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId);
        List<Integer> agentIds = accountBaseDtoList.stream().map(AccountBaseDto::getDependencyAgentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmAgentPo> crmAgentPoMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(agentIds)) {
            crmAgentPoMap = crmAgentRepo.queryAgentDtoMapByAgentIds(agentIds);
            List<Integer> agentAccountIds = crmAgentPoMap.values().stream().map(CrmAgentPo::getSysAgentId).distinct().collect(Collectors.toList());
            List<AccountBaseDto> agentAccountList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(agentAccountIds)
                    .build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.productId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId);
            if (!CollectionUtils.isEmpty(accountBaseDtoList)) {
                accountBaseDtoList.addAll(Optional.ofNullable(agentAccountList).orElse(new ArrayList<>()));
            }
        }
        Map<Integer, AccountBaseDto> accountMap = accountBaseDtoList.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, a -> a, (a, b) -> a));
        List<WxSaleCompositionDto> result = new ArrayList<>();
        Map<Integer, CrmAgentPo> finalCrmAgentPoMap = crmAgentPoMap;
        pickUpDtoList.forEach(pickUpDto -> {
            WxSaleCompositionDto dto = new WxSaleCompositionDto();
            AccountBaseDto accountBaseDto = accountMap.get(pickUpDto.getAccountId());
            CrmAgentPo crmAgentPo = finalCrmAgentPoMap.getOrDefault(accountBaseDto.getDependencyAgentId(), new CrmAgentPo());
            AccountBaseDto agentAccount = accountMap.getOrDefault(Optional.ofNullable(crmAgentPo.getSysAgentId()).orElse(-1), new AccountBaseDto());
            dto.setAccountId(pickUpDto.getAccountId());
            dto.setSaleIds(pickUpDto.getSaleIds());
            dto.setProductId(accountBaseDto.getProductId());
            dto.setAgentId(accountBaseDto.getDependencyAgentId());
            dto.setCustomerId(accountBaseDto.getCustomerId());
            dto.setCompanyGroupId(accountBaseDto.getGroupId());
            dto.setDate(pickUpDto.getCheckingDate());
            dto.setAgentCustomerId(Optional.ofNullable(agentAccount.getCustomerId()).orElse(0));
            if (UP_VIDEO_COOPERATION_TYPE.contains(pickUpDto.getPickupCooperationType())) {
                dto.setWxSaleProductType(WxSaleProductType.VIDEO_PICK_UP);
            } else if (UP_LIVE_COOPERATION_TYPE.contains(pickUpDto.getPickupCooperationType())) {
                dto.setWxSaleProductType(WxSaleProductType.LIVING_PICK_UP);
            } else {
                dto.setWxSaleProductType(WxSaleProductType.NON_STANDARD_PICK_UP);
            }
            dto.setIncomeTeamEnum(IncomeTeamEnum.UP);
            dto.setIncome(pickUpDto.getAmount());
            result.add(dto);
        });
        return result;
    }

}
