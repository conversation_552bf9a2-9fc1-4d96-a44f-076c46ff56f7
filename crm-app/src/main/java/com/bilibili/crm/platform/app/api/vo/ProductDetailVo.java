package com.bilibili.crm.platform.app.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/3/29
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ToString
public class ProductDetailVo implements Serializable {

    private static final long serialVersionUID = 8440105171957513908L;

    @ApiModelProperty("名称")
    private String name;

    @ApiModelProperty("收入(元)")
    private BigDecimal income;

    @ApiModelProperty("占比")
    private BigDecimal ratio;

    @ApiModelProperty("环比")
    private BigDecimal link_ratio;

    @ApiModelProperty("环比金额")
    private BigDecimal link_ratio_amount;

    @ApiModelProperty("同比")
    private BigDecimal yoy_ratio;

    @ApiModelProperty("同比")
    private BigDecimal yoy_ratio_amount;

}
