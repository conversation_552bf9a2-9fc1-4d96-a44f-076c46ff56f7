package com.bilibili.crm.platform.app.biz.service.writer;

import com.bilibili.ad.util.string.StringUtils;
import com.bilibili.crm.platform.api.achievement.dto.AchievementRtbData;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.agent.dto.QueryAgentDto;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationBucketType;
import com.bilibili.crm.platform.biz.clickhouse.param.AdsDssOcpcParam;
import com.bilibili.crm.platform.biz.clickhouse.param.DwsAdInfoParam;
import com.bilibili.crm.platform.biz.clickhouse.param.DwsFlyCrmParam;
import com.bilibili.crm.platform.biz.po.clickhouse.DwsFlyCrmPo;
import com.bilibili.crm.platform.biz.service.AgentService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.ZoneId;
import java.time.temporal.WeekFields;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import java.util.regex.Matcher;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-02-08 10:31:06
 * @description:
 **/

@Slf4j
@Component
public class SellGoodWriteHelper {

    @Resource
    private AgentService agentService;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    // 19（测试部）、160（mcn激励）278（资源合作部）, 110（媒介资源预占）
    public static final List<Long> EXCLUDE_DEP_IDS = Arrays.asList(160L, 19L, 278L, 110L);

    public static final List<Integer> EXCLUDE_DEP_IDS_INT = Arrays.asList(160, 19, 278, 110);

    public static final List<String> EXCLUDE_DEP_NAMES = Arrays.asList("测试部", "mcn激励", "资源合作部");

    public static final List<Long> EXCLUDE_ACCOUNT_IDS = Arrays.asList(29L);

    public static final List<Integer> EXCLUDE_ACCOUNT_IDS_INT = Arrays.asList(29);

    // 交易产品类型-必选+DPA+商业起飞+ADX+个人起飞+其他起飞+内容起飞
    public static final List<Long> EFFECT_SALES_CATEGORY_IDS = Lists.newArrayList(5L, 6L, 7L, 9L);


    //计费类型-竞价CPC+竞价CPM
    public static final List<Long> EFFECT_SALES_MODEL_LIST = Lists.newArrayList(3L, 5L);

    // 起飞类型: 1 个人 2 内容 3 商业 4 签约金个人起飞 5 签约金个人起飞托管
    public static final List<Integer> FLY_TYPES = Arrays.asList(1, 2, 3);


    public List<Integer> querySellGoodAgent() {
        List<AgentDto> agentDtoList = agentService.queryAgentList(QueryAgentDto.builder().agentNameLike("带货").type(-1).build());
        return agentDtoList.stream().map(AgentDto::getId).collect(Collectors.toList());
    }

    public List<AchievementRtbData> crmPoToRtbData(List<DwsFlyCrmPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Lists.newArrayList();
        }
        List<AchievementRtbData> rtbDataList = new ArrayList<>();
        for (DwsFlyCrmPo po : pos) {
            AchievementRtbData rtbData = new AchievementRtbData();
            if (po.getAccountId() != null) {
                rtbData.setAccountId(po.getAccountId().intValue());
            }
            if (po.getCustomerId() != null) {
                rtbData.setCustomerId(po.getCustomerId().intValue());
            }
            if (po.getCostMilli() != null) {
                rtbData.setTotalConsume(fromMilliFen2Yuan(po.getCostMilli()));
            }
            if (po.getAgentId() != null) {
                rtbData.setAgentId(po.getAgentId().intValue());
            }
            if (!StringUtils.isEmpty(po.getLogDate())) {
                rtbData.setDate(strDayToTimeStamp(po.getLogDate()));
            }
            if (po.getCategoryFirstId() != null) {
                rtbData.setCategoryFirstId(po.getCategoryFirstId());
            }
            if (po.getProductId() != null) {
                rtbData.setProductId(po.getProductId().intValue());
            }
            rtbDataList.add(rtbData);
        }
        return rtbDataList;
    }

    public BigDecimal fromMilliFen2Yuan(double milli) {
        return BigDecimal.valueOf(milli).divide(BigDecimal.valueOf(100000), 2, BigDecimal.ROUND_HALF_UP);
    }

    public String timeStampToStrDay(Timestamp timestamp) {
        if (timestamp == null) {
            return "000000";
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        return sdf.format(timestamp);
    }

    public String timeStampToStr(Timestamp timestamp, String pattern) {
        if (timestamp == null) {
            return Strings.EMPTY;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        return sdf.format(timestamp);
    }

    public Timestamp strDayToTimeStamp(String timeStr) {
        if (StringUtils.isEmpty(timeStr)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        try {
            return new Timestamp(sdf.parse(timeStr).getTime());
        } catch (Exception e) {
            log.error("strToTimeStampError, time,{}", timeStr, e);
            return null;
        }
    }

    public Timestamp strDayToTimeStamp(String timeStr, String pattern) {
        if (StringUtils.isEmpty(timeStr)) {
            return null;
        }
        SimpleDateFormat sdf = new SimpleDateFormat(pattern);
        try {
            return new Timestamp(sdf.parse(timeStr).getTime());
        } catch (Exception e) {
            log.error("strToTimeStampError, time,{}", timeStr, e);
            return null;
        }
    }



        public DwsFlyCrmParam buildDefaultParam() {
        return DwsFlyCrmParam.builder()
                .isTakeGoods(1)
                .excludeDepartmentIds(Lists.newArrayList(EXCLUDE_DEP_IDS))
                .excludeAccountIds(EXCLUDE_ACCOUNT_IDS)
                .flyTypes(FLY_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .agentNameNotLike("%带货%")
                .build();
    }

    public DwsFlyCrmParam buildDefaultParam(String startDate, String endDate) {
        return DwsFlyCrmParam.builder()
                // 注意宽表日期格式不一样
                .startDate(startDate)
                .endDate(endDate)
                .isTakeGoods(1)
                .excludeDepartmentIds(Lists.newArrayList(EXCLUDE_DEP_IDS))
                .excludeAccountIds(EXCLUDE_ACCOUNT_IDS)
                .flyTypes(FLY_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .agentNameNotLike("%带货%")
                .build();
    }

    public DwsFlyCrmParam buildSellGoodFlyParam() {
        return DwsFlyCrmParam.builder()
                .excludeDepartmentIds(Lists.newArrayList(EXCLUDE_DEP_IDS))
                .excludeAccountIds(EXCLUDE_ACCOUNT_IDS)
                .isInner(IsInnerEnum.OUTER.getCode())
                .build();
    }

    public AdsDssOcpcParam buildAdsSellGoodParam() {
        return AdsDssOcpcParam.builder()
                .excludeDepartmentIds(Lists.newArrayList(EXCLUDE_DEP_IDS_INT))
                .excludeAccountIds(EXCLUDE_ACCOUNT_IDS_INT)
                .isInner(IsInnerEnum.OUTER.getCode())
                .build();
    }

    public DwsAdInfoParam buildAdsEffectParam() {
        return DwsAdInfoParam.builder()
                .excludeDepartments(Lists.newArrayList(EXCLUDE_DEP_NAMES))
                .excludeAccountIds(Lists.newArrayList("29"))
                .salesCategoryIds(EFFECT_SALES_CATEGORY_IDS)
                .salesModelList(EFFECT_SALES_MODEL_LIST)
                .isInner(IsInnerEnum.OUTER.getCode())
                .advertisementType(2L)
                .build();
    }

    public String formatDateByBucketType(Timestamp timestamp, AggregationBucketType type) {
        if (timestamp == null) {
            return "0000-00-00";
        }
        try {
            Date date = new Date(timestamp.getTime());
            switch (type) {
                case DATE_HISTOGRAM_YEAR:
                    SimpleDateFormat sdfYear = new SimpleDateFormat("yyyy");
                    return sdfYear.format(date);
                case DATE_HISTOGRAM_QUARTER:
                    return incomeTimeUtil.getQuarter(timestamp);
                case DATE_HISTOGRAM_MONTH:
                    SimpleDateFormat sdfMouth = new SimpleDateFormat("yyyy-MM");
                    return sdfMouth.format(date);
                case DATE_HISTOGRAM_WEEK:
                    LocalDate localDate = new Date(timestamp.getTime()).toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
                    int weekOfYear = localDate.get(WeekFields.ISO.weekOfYear());
                    int year = localDate.getYear();
                    return String.format("%d-W%d", year, weekOfYear);
                case DATE_HISTOGRAM_DAY:
                    SimpleDateFormat sdfDay = new SimpleDateFormat("yyyy-MM-dd");
                    return sdfDay.format(date);
                case DATE_HISTOGRAM_HOUR:
                    SimpleDateFormat sdfHour = new SimpleDateFormat("yyyy-MM-dd HH");
                    return sdfHour.format(date);
                default:
                    return "0000-00-00";
            }
        } catch (Exception e) {
            log.error("formatDateByBucketTypeError, timestamp:{}", timestamp, e);
        }
        return "0000-00-00";
    }

    public String unicodeToString(String str) {
        if (StringUtils.isEmpty(str)) {
            return "";
        }
        try {
            Pattern pattern = Pattern.compile("(\\\\u(\\p{XDigit}{4}))");
            Matcher matcher = pattern.matcher(str);
            char ch;
            while (matcher.find()) {
                ch = (char) Integer.parseInt(matcher.group(2), 16);
                str = str.replace(matcher.group(1), ch + "");
            }
            str = str.replace("\"", "");
            str = str.replace("\\", "");
            str = str.replace(" ", "");
        } catch (Exception e) {
            log.info("unicodeToStringError:{}", str, e);
        }
        return str;
    }

}


