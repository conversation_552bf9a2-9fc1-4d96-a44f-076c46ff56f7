package com.bilibili.crm.platform.app.api.client.dto.response;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-12-19 16:58:47
 * @description:
 **/

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class WechatJsSignatureRes {

    /**
     * 随机字符串
     */
    private String noncestr;

    /**
     * 时间戳（单位：秒）
     */
    private String timestamp;

    /**
     * 签名
     */
    private String signature;

    private String jsapiTicket;
}
