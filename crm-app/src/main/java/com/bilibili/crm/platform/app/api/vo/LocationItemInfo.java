package com.bilibili.crm.platform.app.api.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.*;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/12/16
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@ToString
public class LocationItemInfo implements Serializable {
    private static final long serialVersionUID = -3031494009358471768L;

    @ApiModelProperty("资源位名称")
    private String resource_name;

    @ApiModelProperty("使用效率")
    private BigDecimal pv_ratio;

    @ApiModelProperty("库存")
    private BigDecimal pv;

    @ApiModelProperty("曝光")
    private BigDecimal show_pv;
}
