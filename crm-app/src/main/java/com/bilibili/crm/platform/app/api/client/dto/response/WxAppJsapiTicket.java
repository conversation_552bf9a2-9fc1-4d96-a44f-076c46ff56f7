package com.bilibili.crm.platform.app.api.client.dto.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/3/30
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WxAppJsapiTicket extends WxAppResponse {

    @SerializedName("ticket")
    private String ticket;

    @SerializedName("expires_in")
    private Integer expiresIn;
}
