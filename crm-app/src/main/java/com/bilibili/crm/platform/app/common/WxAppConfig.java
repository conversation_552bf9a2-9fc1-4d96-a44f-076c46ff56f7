package com.bilibili.crm.platform.app.common;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/3/30
 **/
@Component
@Data
public class WxAppConfig {

    // 企业微信-业务后端服务的token
    public static final String WX_APP_TOKEN_NAME = "WX-APP-TOKEN";

    /**
     * 租户code
     */
    public static final String CRM_TENANT_CODE = "CRM";
    public static final String DSS_TENANT_CODE = "DSS";

    @Value("${wx.app.crop.id:wx0833ac9926284fa5}")
    private String corpId;

    /**
     * 商业数据服务的 app secret
     */
    @Value("${wx.app.secret:jvUDokYWPBGfzc9ae-mqhPlW-Ch3Wa7dbJgE963-RpM}")
    private String businessDataServiceAppSecret;

    /**
     * crm 数据服务的 app secret
     */
    @Value("${wx.app.secret.crm:hv991grwRlcoR9rhP3zaVzkup2tQH5GZl-8tMdachVI}")
    private String crmDataServiceAppSecret;

    /**
     * 商业数据服务的 appId
     */
    @Value("${wx.app.agent.id:1000133}")
    private Integer businessDataServiceAppId;

    /**
     * crm 数据服务的 appId
     */
    @Value("${wx.app.agent.id.crm:1000165}")
    private Integer crmDataServiceAppId;

    @Value("${wx.app.base.url:https://qyapi.weixin.qq.com/cgi-bin/}")
    private String baseUrl;

    @Value("${wx.app.access.token.url:gettoken?corpid=%s&corpsecret=%s}")
    private String accessTokenUrl;

    @Value("${wx.app.jsapi.ticket.url:get_jsapi_ticket?access_token=%s}")
    private String jsapiTicketUrl;

    @Value("${wx.app.get.user.id.url:user/getuserinfo?access_token=%s&code=%s}")
    private String getUserIdUrl;

    @Value("${wx.app.get.user.info.url:user/get?access_token=%s&userid=%s}")
    private String getUserInfoUrl;

    @Value("${wx.app.send.message.url:message/send?access_token=%s}")
    private String sendMessageUrl;

    @Value("#{'${wx.app.access.token.error.code:40014,42001}'.split(',')}")
    private List<Integer> wxAppAccessTokenErrorCode;

    @Value("${wx.app.cookie.max.age:86400}")
    private Integer wxAppCookieMaxAge;

    /**
     * crm 企业微信白名单用户列表
     */
    @Value("#{'${wx.app.daily.push.user.list:001600,005110,007289,005636,006657,007080,005555,002110}'.split(',')}")
    private List<String> crmUserList;

    @Value("#{'${wx.app.daily.push.inner.user.list:001600,005110,007289,005636,006657,007080,005555,002110}'.split(',')}")
    private List<String> crmInnerUserList;

    /**
     * dss 企业微信白名单用户列表
     */
    @Value("#{'${dss.wx.app.daily.user.list:001600}'.split(',')}")
    private List<String> dssUserList;
}
