package com.bilibili.crm.platform.app.cache.sale_efficiency;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.sales.bo.CrmSaleBO;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.biz.wallet.bean.SaleAchievementEfficiencyDayEsBean;
import com.bilibili.crm.biz.wallet.bean.SaleAchievementEfficiencyDayQuery;
import com.bilibili.crm.biz.wallet.olap.SaleAchievementEfficiencyDayOlapService;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.biz_common.olap.migrate.enums.MigrateBizScene;
import com.bilibili.crm.biz_common.olap.migrate.util.GenericComparator;
import com.bilibili.crm.biz_common.olap.migrate.util.MigrateUtil;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.achievement.IAchievementContractService;
import com.bilibili.crm.platform.api.achievement.IAchievementPickUpService;
import com.bilibili.crm.platform.api.achievement.IAchievementRtbService;
import com.bilibili.crm.platform.api.achievement.dto.*;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityCKQueryDTO;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityQueryDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.report.NewCustomerReportDto;
import com.bilibili.crm.platform.api.customer.dto.report.QueryNewCustomerReportDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.customer.service.report.INewCustomerReportService;
import com.bilibili.crm.platform.api.dto.AmountDataGroupDto;
import com.bilibili.crm.platform.api.enums.WxDailyDataType;
import com.bilibili.crm.platform.api.flowable.enums.FlowListTypeEnum;
import com.bilibili.crm.platform.api.follow_manage.IFollowManageService;
import com.bilibili.crm.platform.api.follow_manage.dto.FollowManageCustomerDto;
import com.bilibili.crm.platform.api.follow_manage.dto.FollowManageQueryDto;
import com.bilibili.crm.platform.api.follow_manage.enums.CustomerBizType;
import com.bilibili.crm.platform.api.follow_manage.enums.FollowFirstType;
import com.bilibili.crm.platform.api.follow_manage.enums.FollowSecondType;
import com.bilibili.crm.platform.api.follow_manage.enums.FollowStatusType;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.sale.dto.CrmSaleKpiDto;
import com.bilibili.crm.platform.api.sale.dto.QuerySaleDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.dto.SimpleSaleDto;
import com.bilibili.crm.platform.api.sale.group.dto.LeaderDto;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaBelongingService;
import com.bilibili.crm.platform.api.sale_sea.dto.QuerySaleSeaBelongingDto;
import com.bilibili.crm.platform.api.settle.BillDetailReport;
import com.bilibili.crm.platform.api.statusmachine.customer.enums.report.NewCustomerReportStateEnum;
import com.bilibili.crm.platform.app.api.dto.SaleEfficiencyCommonDto;
import com.bilibili.crm.platform.app.api.service.AchievementEstimateHiveCkService;
import com.bilibili.crm.platform.app.api.service.dto.AchievementEstimateHiveCkQueryDTO;
import com.bilibili.crm.platform.app.api.service.dto.CrmAchievementDayItemDTO;
import com.bilibili.crm.platform.app.api.vo.SaleDashboardVo;
import com.bilibili.crm.platform.app.biz.service.WxPaperSalesGoodService;
import com.bilibili.crm.platform.app.cache.estimate.AchievementEstimateService;
import com.bilibili.crm.platform.biz.addata.dao.read.CrmGroupProductKaLabelDao;
import com.bilibili.crm.platform.biz.common.CommonConstant;
import com.bilibili.crm.platform.biz.elasticsearch.live_goods.po.SaleAchievementEfficiencyDayPO;
import com.bilibili.crm.platform.biz.po.CrmSaleSeaMappingPo;
import com.bilibili.crm.platform.biz.po.addata.CrmGroupProductKaLabelPo;
import com.bilibili.crm.platform.biz.po.addata.CrmGroupProductKaLabelPoExample;
import com.bilibili.crm.platform.biz.repo.salesea.SaleSeaMappingRepo;
import com.bilibili.crm.platform.biz.service.AgentService;
import com.bilibili.crm.platform.biz.service.SaleGroupService;
import com.bilibili.crm.platform.biz.service.achievement.AchievementReportService;
import com.bilibili.crm.platform.biz.service.achievement.AchievementRtbReportService;
import com.bilibili.crm.platform.biz.service.achievement.query.AchieveFromWalletService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.trading.AggDateTypeEnum;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.SaleLevelEnum;
import com.bilibili.crm.platform.common.UserType;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.customer.CustomerType;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaMappingType;
import com.google.common.collect.Lists;
import javassist.Loader;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.builder.SearchSourceBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.AliasQuery;
import org.springframework.data.elasticsearch.core.query.IndexQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.crm.platform.biz.constant.EsIndexConstant.INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY;
import static org.elasticsearch.index.query.QueryBuilders.boolQuery;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-02-28 19:21:55
 * @description:
 **/

@Component
@Slf4j
public class SaleEfficiencyService {

    @Autowired
    private ISaleService saleService;

    @Resource
    private SaleGroupService saleGroupService;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private IncomeUtil incomeUtil;

    @Resource
    private IFastQueryAccountService fastQueryAccountService;


    @Resource
    private ISaleSeaBelongingService saleSeaBelongingService;

    @Resource
    private AgentService agentService;

    @Resource
    private WxPaperSalesGoodService wxPaperSalesGoodService;

    @Resource
    private IFollowManageService followManageService;

    @Resource
    private INewCustomerReportService newCustomerReportService;

    @Autowired
    private IBsiOpportunityService bsiOpportunityService;

    @Resource
    private AchievementEstimateService achievementEstimateService;

    @Resource(name = "elasticsearchTemplateLiveGoods")
    private ElasticsearchTemplate elasticsearchTemplateLiveGoods;

    @Autowired
    private IAchievementRtbService rtbService;

    @Resource
    private AchieveFromWalletService achieveFromWalletService;

    @Autowired
    private IAchievementContractService achievementContractService;

    @Autowired
    private IAchievementPickUpService achievementPickUpService;

    @Resource
    private CrmGroupProductKaLabelDao crmGroupProductKaLabelDao;

    @Autowired
    private AchievementReportService reportService;

    @Autowired
    private AchievementRtbReportService rtbReportService;

    @Resource
    private SaleSeaMappingRepo saleSeaMappingRepo;

    @Resource
    private ICustomerQueryService customerQueryService;

    @Autowired
    private AchievementEstimateHiveCkService achievementEstimateHiveCkService;

    @Autowired
    private EsMigrateMessageSender<SaleAchievementEfficiencyDayPO> messageSender;

    @Autowired
    private PaladinConfig paladinConfig;
    @Autowired
    private MigrateUtil migrateUtil;
    @Autowired
    private GenericComparator genericComparator;
    @Autowired
    private SaleAchievementEfficiencyDayOlapService saleAchievementEfficiencyDayOlapService;

    private static final Integer DIRECT_GROUP_ID = 46;
    private static final Integer CHANNEL_GROUP_ID = 85;

    private static final Integer PM_SALE_ID = 140;

    private static final Integer TEST_SALE = 60;

    private static final Integer PM_SALE_GROUP_ID = 38;

    private static final Integer DIRECT_VIRTUAL_TYPE = 18;

    private static final Integer CHANNEL_VIRTUAL_TYPE = 19;

    @Value("${crm.efficiency.quarter.target.time:30}")
    private Integer targetTime;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;

    public List<SaleAchievementEfficiencyDayPO> queryEfficiencyPoList(Timestamp date, List<Integer> saleIds, List<Integer> saleGroupIds, List<Integer> aggTypes, List<Integer> dataTypes) {
        List<SaleAchievementEfficiencyDayPO> result;

        // es集群迁移 sale_achievement_efficiency_day
        if (paladinConfig.switchOn(HotKey.saleAchievementEfficiencyDayEsMigrateSwitch)) {
            result = this.queryEfficiencyPoList4Migrate(date, saleIds, saleGroupIds, aggTypes, dataTypes);
        } else {
            BoolQueryBuilder queryBuilder = boolQuery();

            // 增加一个时间必要条件，否则后面会全量查10000条
            if (null != date) {
                queryBuilder.filter(QueryBuilders.termQuery("agg_time", Utils.getBeginOfDay(date).getTime()));
            }

            if (!CollectionUtils.isEmpty(saleIds)) {
                queryBuilder.should(QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termQuery("agg_time", Utils.getBeginOfDay(date).getTime()))
                        .filter(QueryBuilders.termsQuery("biz_id", saleIds))
                        .filter(QueryBuilders.termsQuery("data_type", dataTypes))
                        .filter(QueryBuilders.termsQuery("agg_type", aggTypes))
                        .filter(QueryBuilders.termQuery("biz_type", SaleKpiTypeEnum.SALE.getBizId())));
            }
            if (!CollectionUtils.isEmpty(saleGroupIds)) {
                queryBuilder.should(QueryBuilders.boolQuery()
                        .filter(QueryBuilders.termQuery("agg_time", Utils.getBeginOfDay(date).getTime()))
                        .filter(QueryBuilders.termsQuery("biz_id", saleGroupIds))
                        .filter(QueryBuilders.termsQuery("data_type", dataTypes))
                        .filter(QueryBuilders.termsQuery("agg_type", aggTypes))
                        .filter(QueryBuilders.termQuery("biz_type", SaleKpiTypeEnum.SALE_TEAM.getBizId())));
            }
            if (!elasticsearchTemplateLiveGoods.indexExists(INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY)) {
                return new ArrayList<>();
            }
            SearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withIndices(INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY)
                    .withQuery(queryBuilder)
                    .withPageable(PageRequest.of(0, 10000))
                    .build();
            SearchSourceBuilder searchSourceBuilder = new SearchSourceBuilder();
            searchSourceBuilder.query(searchQuery.getQuery());
            List<SaleAchievementEfficiencyDayPO> tempResult = new ArrayList<>();
            elasticsearchTemplateLiveGoods.query(searchQuery, response -> {
                for (SearchHit searchHit : response.getHits()) {
                    JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(searchHit.getSourceAsMap()));
                    SaleAchievementEfficiencyDayPO po = JSONObject.parseObject(jsonObject.toJSONString(), SaleAchievementEfficiencyDayPO.class);
                    tempResult.add(po);
                }
                return null;
            });
            result = tempResult;

            if (migrateUtil.canSimulation(HotKey.saleAchievementEfficiencyDayEsMigrateSwitch_simulation, MigrateBizScene.SaleEfficiencyService_queryEfficiencyPoList.getName())) {
                migrateUtil.doSimulation(MigrateBizScene.SaleEfficiencyService_queryEfficiencyPoList.getName(), (n) -> {
                    // 仿真查询
                    List<SaleAchievementEfficiencyDayPO> newResult = this.queryEfficiencyPoList4Migrate(date, saleIds, saleGroupIds, aggTypes, dataTypes);
                    // 仿真比对
                    genericComparator.compareAllPairs(
                            result, newResult,
                            MigrateBizScene.SaleEfficiencyService_queryEfficiencyPoList.getName(),
                            SaleAchievementEfficiencyDayPO::getId,
                            SaleAchievementEfficiencyDayPO::getId,
                            null,null
                    );
                });
            }
        }
        return result;
    }

    private List<SaleAchievementEfficiencyDayPO> queryEfficiencyPoList4Migrate(Timestamp date, List<Integer> saleIds, List<Integer> saleGroupIds, List<Integer> aggTypes, List<Integer> dataTypes) {
        PageResult<SaleAchievementEfficiencyDayEsBean> pageList = saleAchievementEfficiencyDayOlapService.pageSearch(
                SaleAchievementEfficiencyDayQuery.builder()
                        .bizScene(MigrateBizScene.SaleEfficiencyService_queryEfficiencyPoList.getName())
                        .appId(MigrateBizScene.CRM_APP_ID.getName())
                        .aggTime(Utils.getBeginOfDay(date).getTime())
                        .dataTypes(dataTypes)
                        .aggTypes(aggTypes)
                        .saleGroupIds(saleGroupIds)
                        .saleIds(saleIds)
                        .page(1)
                        .size(10000)
                        .build()
        );
        if (Objects.isNull(pageList) || CollectionUtils.isEmpty(pageList.getRecords())) {
            return Collections.emptyList();
        }
        List<SaleAchievementEfficiencyDayEsBean> esBeanList = pageList.getRecords();

        List<SaleAchievementEfficiencyDayPO> saleAchievementEfficiencyDayPOS = new ArrayList<>();
        for (SaleAchievementEfficiencyDayEsBean esBean : esBeanList) {
            SaleAchievementEfficiencyDayPO dto = SaleAchievementEfficiencyDayPO.builder()
                    .id(esBean.getId())
                    .biz_id(esBean.getBiz_id())
                    .biz_type(esBean.getBiz_type())
                    .agg_type(esBean.getAgg_type())
                    .agg_time(esBean.getAgg_time())
                    .data_type(esBean.getData_type())
                    .biz_name(esBean.getBiz_name())
                    .direct_or_channel(esBean.getDirect_or_channel())
                    .total_task_amount(esBean.getTotal_task_amount())
                    .total_complete_amount(esBean.getTotal_complete_amount())
                    .total_complete_link_amount(esBean.getTotal_complete_link_amount())
                    .new_group_amount(esBean.getNew_group_amount())
                    .new_group_link_amount(esBean.getNew_group_link_amount())
                    .on_sale_amount(esBean.getOn_sale_amount())
                    .saleIds(esBean.getSaleIds())
                    .on_sale_link_amount(esBean.getOn_sale_link_amount())
                    .private_sea_group_amount(esBean.getPrivate_sea_group_amount())
                    .private_sea_group_link_amount(esBean.getPrivate_sea_group_link_amount())
                    .private_sea_groups(esBean.getPrivate_sea_groups())
                    .private_sea_agent_amount(esBean.getPrivate_sea_agent_amount())
                    .private_sea_agent_link_amount(esBean.getPrivate_sea_agent_link_amount())
                    .private_sea_agents(esBean.getPrivate_sea_agents())
                    .private_sea_group_alive_amount(esBean.getPrivate_sea_group_alive_amount())
                    .private_sea_alive_groups(esBean.getPrivate_sea_alive_groups())
                    .private_sea_agent_alive_amount(esBean.getPrivate_sea_agent_alive_amount())
                    .private_sea_alive_agents(esBean.getPrivate_sea_alive_agents())
                    .group_average_arpu(esBean.getGroup_average_arpu())
                    .group_average_arpu_link(esBean.getGroup_average_arpu_link())
                    .alive_group_average_arpu(esBean.getAlive_group_average_arpu())
                    .alive_group_average_arpu_link(esBean.getAlive_group_average_arpu_link())
                    .arpu_amount(esBean.getArpu_amount())
                    .agent_average_arpu(esBean.getAgent_average_arpu())
                    .agent_average_arpu_link(esBean.getAgent_average_arpu_link())
                    .alive_agent_average_arpu(esBean.getAlive_agent_average_arpu())
                    .alive_agent_average_arpu_link(esBean.getAlive_agent_average_arpu_link())
                    .visit_group_amount(esBean.getVisit_group_amount())
                    .visit_group_link_amount(esBean.getVisit_group_link_amount())
                    .visit_groups(esBean.getVisit_groups())
                    .visit_agent_amount(esBean.getVisit_agent_amount())
                    .visit_agent_link_amount(esBean.getVisit_agent_link_amount())
                    .visit_agents(esBean.getVisit_agents())
                    .group_new_customer_amount(esBean.getGroup_new_customer_amount())
                    .agent_new_customer_amount(esBean.getAgent_new_customer_amount())
                    .lbs_follow_record_amount(esBean.getLbs_follow_record_amount())
                    .all_follow_record_amount(esBean.getAll_follow_record_amount())
                    .all_follow_record_amount_link(esBean.getAll_follow_record_amount_link())
                    .bsi_amount(esBean.getBsi_amount())
                    .bsi_amount_link(esBean.getBsi_amount_link())
                    .bsi_group_amount(esBean.getBsi_group_amount())
                    .bsi_group_link_amount(esBean.getBsi_group_link_amount())
                    .bsi_agent_amount(esBean.getBsi_agent_amount())
                    .bsi_agent_link_amount(esBean.getBsi_agent_link_amount())
                    .build();
            saleAchievementEfficiencyDayPOS.add(dto);
        }
        return saleAchievementEfficiencyDayPOS;
    }

    public void cacheRequest(Timestamp base, List<Integer> groupIds, List<Integer> saleIds) {
        log.info("SaleEfficiencyServiceStart:{},groupIds:{}, saleIds:{}", base, Optional.ofNullable(groupIds).orElse(new ArrayList<>()), Optional.ofNullable(saleIds).orElse(new ArrayList<>()));
        // 消耗数据， 在这一层查询，不查多次，查总数
        List<SaleGroupDto> allGroupList = saleGroupService.getAllValidGroupList();
        List<Integer> directChildIds = saleGroupService.getAllChildGroupIdListByParentId(DIRECT_GROUP_ID);
        List<Integer> channelChildIds = saleGroupService.getAllChildGroupIdListByParentId(CHANNEL_GROUP_ID);
        if (CollectionUtils.isEmpty(groupIds)) {
            groupIds = allGroupList.stream().filter(e -> directChildIds.contains(e.getId()) || channelChildIds.contains(e.getId())).map(SaleGroupDto::getId).collect(Collectors.toList());
            groupIds.add(DIRECT_GROUP_ID);
            groupIds.add(CHANNEL_GROUP_ID);
            groupIds = groupIds.stream().filter(a -> !Objects.equals(a, PM_SALE_GROUP_ID)).distinct().collect(Collectors.toList());
        }
        List<SaleDto> saleDtos;
        if (CollectionUtils.isEmpty(saleIds)) {
            saleDtos = saleService.getSalesByGroupIds(groupIds);
        } else {
            saleDtos = saleService.getSalesInIds(saleIds);
        }
        saleDtos = filterSale(saleDtos);
        SaleEfficiencyCommonDto commonDto = buildCommonDto(base);
        List<Integer> dLeaderIds = buildDLeaderIds(saleDtos);
        commonDto.setDLeaderIds(dLeaderIds);
        List<SaleAchievementEfficiencyDayPO> result = new ArrayList<>();
        Timestamp quarterBegin = incomeTimeUtil.getQuarterFirstDate(base);
        saleDtos = saleDtos.stream().filter(a -> a.getSaleIsQuit().equals(IsValid.FALSE.getCode()) || a.getSaleQuitTime() == null || a.getSaleQuitTime().compareTo(quarterBegin) >= 0).collect(Collectors.toList());
        saleDtos = saleDtos.stream().sorted(Comparator.comparing(SaleDto::getLevel).reversed()).collect(Collectors.toList());
        Integer remainSize = saleDtos.size();

        // 销售存储
        for (SaleDto saleDto : saleDtos) {
            Integer isDirectOrChannel = 1;
            if (directChildIds.contains(saleDto.getGroupId())) {
                isDirectOrChannel = 1;
            } else {
                isDirectOrChannel = 2;
            }
            try {
                List<SaleAchievementEfficiencyDayPO> poList = saveSaleCommonData(saleDto, commonDto, isDirectOrChannel);
                log.info("saveSaleCommonDataSuccess:{},{}", saleDto.getEmail(), --remainSize);
                result.addAll(poList);
            } catch (Exception e) {
                log.error("buildSaleEfficiencyError:{},{},{}", saleDto.getEmail(), base, --remainSize, e);
                AlarmHelper.log("buildSaleEfficiencyError", e, saleDto.getEmail());
            }
        }
        if (!CollectionUtils.isEmpty(result)) {
            elasticsearchTemplateLiveGoods.bulkIndex(createIndexQuery(result));
            messageSender.sendBatch(result, INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY, EsMigrateCommon.INCR);
            //refresh刷新 Elasticsearch 中指定索引的数据。
            elasticsearchTemplateLiveGoods.refresh(INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY);
        }
        log.info("efficiencyDataSize:{}", result.size());
    }

    private SaleEfficiencyCommonDto buildCommonDto(Timestamp base) {
        SaleEfficiencyCommonDto commonDto = new SaleEfficiencyCommonDto();
        List<AccountBaseDto> orgAccounts = fastQueryAccountService.fetch(QueryAccountParam.builder().userTypes(Lists.newArrayList(UserType.ORG_USER.getCode())).build(), AccountFieldMapping.accountId);
        List<Integer> orgAccountIds = orgAccounts.stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList());
        List<Integer> newGroupIds = queryNewGroupIds(base);

        //查看该季度所有的kpi
        String quarterDesc = incomeTimeUtil.getQuarterDescNow(base);
        List<CrmSaleKpiDto> kpiDtoList = saleService.queryAllKpiByQuarter(quarterDesc);
        commonDto.setBase(base);
        commonDto.setKpiDtoList(kpiDtoList);
        commonDto.setSaleDtoMap(saleService.getAllSaleMapWithDetail());
        commonDto.setOrgAccountIds(orgAccountIds);
        commonDto.setNewGroupIds(newGroupIds);


        // 季度
        Timestamp quarterBegin = incomeTimeUtil.getQuarterFirstDate(base);
        Timestamp quarterEnd = Utils.getEndOfDay(base);
        long lastQuarterBegin = incomeTimeUtil.getStartOrEndDayOfQuarter(base, -1, true);
        long gap = quarterEnd.getTime() - quarterBegin.getTime();
        Timestamp quarterLinkBegin = new Timestamp(lastQuarterBegin);
        Timestamp quarterLinkEnd = new Timestamp(lastQuarterBegin + gap);
        List<Integer> linkNewGroupIds = queryNewGroupIds(quarterLinkEnd);
        commonDto.setLinkGroupIds(linkNewGroupIds);
        commonDto.setQuarterList(buildDto(quarterBegin, quarterEnd, false));
        commonDto.setQuarterLinkList(buildDto(quarterLinkBegin, quarterLinkEnd, true));
        commonDto.setQuarterBegin(quarterBegin);
        commonDto.setQuarterEnd(quarterEnd);
        commonDto.setQuarterLinkBegin(quarterLinkBegin);
        commonDto.setQuarterLinkEnd(quarterLinkEnd);
        log.info("SaleEfficiencyServiceQuarterEnd");
        return commonDto;
    }

    /**
     * 销售数据存储
     */
    public List<SaleAchievementEfficiencyDayPO> saveSaleCommonData(SaleDto saleDto, SaleEfficiencyCommonDto commonDto, Integer isDirectOrChannel) {
        // 哪些需要这一层查询：
        // 季度预估看板 季度数据
        // 在职销售人数、详情
        // 新集团数
        // 私海集团个数
        // 私海代理个数
        // arpu
        // 活跃 集团/代理数
        log.info("saveSaleCommonDataStart:{}", saleDto.getEmail());
        List<SaleAchievementEfficiencyDayPO> result = new ArrayList<>();
        SaleAchievementEfficiencyDayPO po = buildDefault();
        List<SaleDto> saleDtos = saleGroupService.getGroupMembersNew(saleDto, saleDto.getLevel());
        saleDtos.add(saleDto);
        saleDtos = filterSale(saleDtos);

        po.setAgg_time(Utils.getBeginOfDay(commonDto.getBase()).getTime());
        po.setBiz_id(saleDto.getId());
        po.setBiz_type(SaleKpiTypeEnum.SALE.getBizId());
        po.setBiz_name(saleDto.getName());
        po.setDirect_or_channel(isDirectOrChannel);
        if (saleDto.getLevel().equals(SaleLevelEnum.LEADER.getCode()) || commonDto.getDLeaderIds().contains(saleDto.getId())) {
            po.setData_type(1);
        } else {
            po.setData_type(3);
        }
        List<Integer> validSaleIds = saleDtos.stream()
                .filter(a -> a.getSaleIsQuit() == 0 || a.getSaleQuitTime() == null || a.getSaleQuitTime().after(commonDto.getQuarterBegin()))
                .filter(a -> a.getStatus() == 1)
                .map(SaleDto::getId).distinct()
                .collect(Collectors.toList());
        po.setOn_sale_amount(validSaleIds.size());
        po.setSaleIds(validSaleIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
        // 季度目标
        buildPOTaskAmount(po, commonDto.getKpiDtoList(), SaleKpiTypeEnum.SALE, saleDto.getId());
        // 生效账户
        List<Integer> accountIds = queryBelongAccountIds(validSaleIds, commonDto.getBase(), isDirectOrChannel);
        // 构建集团代理信息
        buildGroupInfo(accountIds, isDirectOrChannel, po, commonDto.getNewGroupIds(), validSaleIds, commonDto);
        // 构建arpu信息
        List<SaleAchievementEfficiencyDayPO> linkPos = queryEfficiencyPoList(Utils.getBeginOfDay(commonDto.getQuarterLinkEnd()), Lists.newArrayList(po.getBiz_id()), new ArrayList<>(), Lists.newArrayList(AggDateTypeEnum.QUARTER.getCode()), Lists.newArrayList(po.getData_type()));
        // 环比信息
        if (!CollectionUtils.isEmpty(linkPos)) {
            SaleAchievementEfficiencyDayPO linkPo = linkPos.get(0);
            po.setAgent_average_arpu_link(linkPo.getAgent_average_arpu_link());
            po.setAlive_agent_average_arpu_link(linkPo.getAlive_agent_average_arpu_link());
            po.setGroup_average_arpu_link(linkPo.getGroup_average_arpu_link());
            po.setAlive_group_average_arpu(linkPo.getAlive_group_average_arpu());
        } else {
            List<Integer> linkAccountIds = queryBelongAccountIds(validSaleIds, commonDto.getQuarterLinkEnd(), isDirectOrChannel);
            Long linkAmount = 0L;
            if (isDirectOrChannel == 1) {
                linkAmount = commonDto.getQuarterLinkList().stream()
                        .filter(a -> isValid(a.getSaleIds(), validSaleIds))
                        .filter(a -> a.getAccountId() != null && commonDto.getOrgAccountIds().contains(a.getAccountId()))
                        .mapToLong(AmountDataGroupDto::getAmount).sum();
            } else {
                // 渠道过滤内容起飞
                linkAmount = commonDto.getQuarterLinkList().stream()
                        .filter(a -> a.getIsContentFly() == null || !a.getIsContentFly())
                        .filter(a -> isValid(a.getSaleIds(), validSaleIds))
                        .filter(a -> a.getAccountId() != null && commonDto.getOrgAccountIds().contains(a.getAccountId()))
                        .mapToLong(AmountDataGroupDto::getAmount).sum();
            }
            List<SaleDto> linkSaleDtoList = saleGroupService.getGroupMembersWithTime(saleDto, saleDto.getLevel(), commonDto.getQuarterLinkEnd());
            linkSaleDtoList.add(saleDto);
            linkSaleDtoList = filterSale(linkSaleDtoList);
            List<Integer> linkSaleIds = linkSaleDtoList.stream().map(SaleDto::getId).distinct().collect(Collectors.toList());
            List<List<Integer>> linkProducts = buildProductLinkInfo(linkAccountIds, isDirectOrChannel, linkSaleIds, commonDto);
            log.info("arpuLink:,{},{},{},{}", saleDto.getName(), linkAmount, linkSaleIds, linkProducts.get(1).size());
            if (isDirectOrChannel == 1) {
                po.setGroup_average_arpu_link(calArpu(linkAmount, linkSaleIds.size(), linkProducts.get(1).size()));
                po.setAlive_group_average_arpu_link(calArpu(linkAmount, linkSaleIds.size(), linkProducts.get(2).size()));
            } else {
                po.setAgent_average_arpu_link(calArpu(linkAmount, linkSaleIds.size(), linkProducts.get(1).size()));
                po.setAlive_agent_average_arpu_link(calArpu(linkAmount, linkSaleIds.size(), linkProducts.get(2).size()));
            }
        }
        List<Integer> finalTimeValidSaleIds = validSaleIds;
        // 构建arpu
        if (isDirectOrChannel == 1) {
            Long amount = commonDto.getQuarterList().stream()
                    .filter(a -> isValid(finalTimeValidSaleIds, a.getSaleIds()))
                    .filter(a -> commonDto.getOrgAccountIds().contains(a.getAccountId()))
                    .mapToLong(AmountDataGroupDto::getAmount).sum();
            po.setArpu_amount(amount);
            po.setGroup_average_arpu(calArpu(amount, po.getOn_sale_amount(), po.getPrivate_sea_group_amount()));
            po.setAlive_group_average_arpu(calArpu(amount, po.getOn_sale_amount(), po.getPrivate_sea_group_alive_amount()));
        } else {
            Long amount = commonDto.getQuarterList().stream()
                    .filter(a -> a.getIsContentFly() == null || !a.getIsContentFly())
                    .filter(a -> isValid(finalTimeValidSaleIds, a.getSaleIds()))
                    .filter(a -> a.getAccountId() != null && commonDto.getOrgAccountIds().contains(a.getAccountId()))
                    .mapToLong(AmountDataGroupDto::getAmount).sum();
            po.setArpu_amount(amount);
            po.setAgent_average_arpu(calArpu(amount, po.getOn_sale_amount(), po.getPrivate_sea_agent_amount()));
            po.setAlive_agent_average_arpu(calArpu(amount, po.getOn_sale_amount(), po.getPrivate_sea_agent_alive_amount()));
        }
        try {
            Thread.sleep(300L);
        } catch (Exception e) {
        }
        SaleDashboardVo saleDashboardVo = wxPaperSalesGoodService.queryByTypeBizId(WxDailyDataType.SALE_DASHBOARD_QUARTER, commonDto.getBase(), saleDto.getId(), new SaleDashboardVo());
        // 副组长情况
        if (validSaleIds.size() > 1 && !saleDto.getLevel().equals(SaleLevelEnum.LEADER.getCode())) {
            Long total = buildTotal(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                    .dateBegin(commonDto.getQuarterBegin())
                    .dateEnd(commonDto.getQuarterEnd())
                    .saleIds(validSaleIds)
                    .build(), isDirectOrChannel, false);
            po.setTotal_complete_amount(total);
        } else {
            po.setTotal_complete_amount(Utils.fromYuanToFen(saleDashboardVo.getIncome()));
            po.setTotal_complete_link_amount(Utils.fromYuanToFen(saleDashboardVo.getDay_compare_rate_amount()));
        }
        // 私海
        for (AggDateTypeEnum typeEnum : AggDateTypeEnum.queryNotDay()) {
            log.info("saveSaleCommonDataTime:{}, type:{}", saleDto.getEmail(), typeEnum);
            List<SaleAchievementEfficiencyDayPO> pos = saveSaleTimeData(typeEnum, saleDto, isDirectOrChannel, po, accountIds, commonDto, validSaleIds);
            result.addAll(pos);
        }
        return result;
    }

    /**
     * 集团信息
     */
    public void buildGroupInfo(List<Integer> accountIds, Integer isChannelOrDirect, SaleAchievementEfficiencyDayPO po, List<Integer> newGroupIds, List<Integer> saleIds, SaleEfficiencyCommonDto commonDto) {
        log.info("buildGroupInfo:{},{},{}", po.getBiz_name(), accountIds, saleIds);
        if (CollectionUtils.isEmpty(accountIds)) {
            return;
        }
        List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId, AccountFieldMapping.isAgent, AccountFieldMapping.userType);
        // 过滤机构账户
        List<Integer> customerIds = accountBaseDtos.stream().filter(a -> a.getUserType().equals(UserType.ORG_USER.getCode())).map(AccountBaseDto::getCustomerId).distinct().collect(Collectors.toList());
        log.info("buildGroupInfoCustomer:{},{}", po.getBiz_name(), GsonUtils.toJson(customerIds));
        List<CustomerBaseDto> customerBaseDtos = new ArrayList<>();
        // 取客户所属集团
        if (!CollectionUtils.isEmpty(customerIds)) {
            customerBaseDtos = customerQueryService.getCustomerBaseDtosByIds(customerIds);
        }
        List<Integer> groupIds = customerBaseDtos.stream().map(CustomerBaseDto::getGroupId).distinct().filter(a -> a > 0).collect(Collectors.toList());
        log.info("buildGroupInfoGroup:{},{}", po.getBiz_name(), GsonUtils.toJson(customerIds));
        List<Integer> newPoGroupIds = groupIds.stream().filter(newGroupIds::contains).collect(Collectors.toList());
        log.info("newGroupDetail{},{}", po.getBiz_name(), newPoGroupIds);
        // 新集团数 渠道/直客都需要
        po.setNew_group_amount(newPoGroupIds.size());
        if (isChannelOrDirect == 1) {
            po.setPrivate_sea_group_amount(groupIds.size());
            po.setPrivate_sea_group_alive_amount((int) commonDto.getQuarterList().stream().filter(a -> isValid(a.getSaleIds(), saleIds) && a.getAccountId() != null && commonDto.getOrgAccountIds().contains(a.getAccountId())).map(AmountDataGroupDto::getGroupId).distinct().filter(Objects::nonNull).filter(a -> a > 0).count());
            if (!CollectionUtils.isEmpty(groupIds)) {
                po.setPrivate_sea_groups(groupIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
        } else {
            List<Integer> agentCustomerIds = accountBaseDtos.stream().filter(a -> a.getIsAgent().equals(IsValid.TRUE.getCode())).map(AccountBaseDto::getCustomerId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Integer> agentIds = accountBaseDtos.stream().map(AccountBaseDto::getDependencyAgentId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Integer> agentCustomerIdsForAccount = new ArrayList<>();
            if (!CollectionUtils.isEmpty(agentIds)) {
                Map<Integer, AgentDto> agentDtoMap = agentService.getAgentMapInIds(agentIds);
                List<Integer> agentAccountIds = agentDtoMap.values().stream().map(AgentDto::getSysAgentId).distinct().filter(a -> a > 0).collect(Collectors.toList());
                log.info("buildGroupInfo.agentAccountIds:{},{}", po.getBiz_name(), agentAccountIds);
                if (!CollectionUtils.isEmpty(agentAccountIds)) {
                    List<AccountBaseDto> agentAccountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(agentAccountIds).userTypes(Lists.newArrayList(UserType.ORG_USER.getCode())).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.groupId);
                    agentCustomerIdsForAccount = agentAccountBaseDtos.stream().map(AccountBaseDto::getCustomerId).distinct().collect(Collectors.toList());
                }
            }
            agentCustomerIds.addAll(agentCustomerIdsForAccount);
            List<Integer> customerIdsOfSales = new ArrayList<>();
            // 私海客户直接匹配
            if (!CollectionUtils.isEmpty(saleIds)) {
                List<CrmSaleSeaMappingPo> crmSaleSeaMappingPos = saleSeaMappingRepo.queryMappingsBySaleIds(SaleSeaMappingType.CUSTOMER, saleIds, null, null);
                customerIdsOfSales = crmSaleSeaMappingPos.stream().filter(a -> a.getBiliUserType().equals(BiliUserType.CHANNEL_MANAGER.getCode())).map(t -> t.getMappingId()).distinct().collect(Collectors.toList());
                log.info("customerIdsOfSales:{},{}", po.getBiz_name(), customerIdsOfSales);
            }
            agentCustomerIds.addAll(customerIdsOfSales);
            agentCustomerIds = Optional.ofNullable(agentCustomerIds).orElse(new ArrayList<>()).stream().distinct().filter(a -> a > 0).collect(Collectors.toList());
            log.info("buildGroupInfo.filterAgentCustomerIds:{},{}", po.getBiz_name(), agentCustomerIds);
            po.setPrivate_sea_agent_amount(agentCustomerIds.size());
            // 渠道组 只筛选 代理商账户为机构客户
            List<Integer> finalAgentCustomerIds = agentCustomerIds;
            if (!CollectionUtils.isEmpty(agentCustomerIds)) {
                List<Integer> aliveAgentCustomerIds = commonDto.getQuarterList().stream().filter(a -> isValid(a.getSaleIds(), saleIds) && a.getAccountId() != null && commonDto.getOrgAccountIds().contains(a.getAccountId())).map(AmountDataGroupDto::getAgentCustomerId).distinct().filter(Objects::nonNull).filter(a -> a > 0).collect(Collectors.toList());
                log.info("aliveCustomer:{},{}", po.getBiz_name(), aliveAgentCustomerIds);
                po.setPrivate_sea_agent_alive_amount(aliveAgentCustomerIds.size());
                po.setPrivate_sea_agents(agentCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            }
        }
    }

    /**
     * 集团代理环比
     *
     * @param accountIds
     * @param isChannelOrDirect
     * @return
     */
    public List<List<Integer>> buildProductLinkInfo(List<Integer> accountIds, Integer isChannelOrDirect, List<Integer> saleIds, SaleEfficiencyCommonDto commonDto) {
        log.info("buildProductLinkInfoRequest:{},{}", accountIds, saleIds);
        List<List<Integer>> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList(new ArrayList<>(), new ArrayList<>(), Lists.newArrayList());
        }
        List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.groupId, AccountFieldMapping.dependencyAgentId, AccountFieldMapping.isAgent, AccountFieldMapping.userType);
        // 过滤机构账户
        List<Integer> customerIds = accountBaseDtos.stream().filter(a -> a.getUserType().equals(UserType.ORG_USER.getCode())).map(AccountBaseDto::getCustomerId).distinct().collect(Collectors.toList());
        List<CustomerBaseDto> customerBaseDtos = new ArrayList<>();
        // 取客户所属集团
        if (!CollectionUtils.isEmpty(customerIds)) {
            customerBaseDtos = customerQueryService.getCustomerBaseDtosByIds(customerIds);
        }
        List<Integer> groupIds = customerBaseDtos.stream().map(CustomerBaseDto::getGroupId).distinct().filter(a -> a > 0).collect(Collectors.toList());
        result.add(groupIds);
        if (isChannelOrDirect == 1) {
            result.add(groupIds);
            List<Integer> aliveGroupIds = commonDto.getQuarterLinkList().stream().filter(a -> a.getAccountId() != null && accountIds.contains(a.getAccountId()) && commonDto.getOrgAccountIds().contains(a.getAccountId())).map(AmountDataGroupDto::getGroupId).distinct().filter(Objects::nonNull).filter(a -> a > 0).collect(Collectors.toList());
            result.add(aliveGroupIds);
        } else {
            List<Integer> agentCustomerIds = accountBaseDtos.stream().filter(a -> a.getIsAgent().equals(IsValid.TRUE.getCode())).map(AccountBaseDto::getCustomerId).distinct().collect(Collectors.toList());
            log.info("buildProductLinkInfo.agentAccountIds:,{},{}", saleIds, agentCustomerIds);
            List<Integer> agentIds = accountBaseDtos.stream().map(AccountBaseDto::getDependencyAgentId).filter(a -> a > 0).distinct().collect(Collectors.toList());
            List<Integer> agentCustomerIdsForAccount = new ArrayList<>();
            log.info("buildProductLinkInfo.agentIds:{},{}", saleIds, agentIds);
            if (!CollectionUtils.isEmpty(agentIds)) {
                Map<Integer, AgentDto> agentDtoMap = agentService.getAgentMapInIds(agentIds);
                List<Integer> agentAccountIds = agentDtoMap.values().stream().map(AgentDto::getSysAgentId).distinct().filter(a -> a > 0).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(agentAccountIds)) {
                    List<AccountBaseDto> agentAccountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(agentAccountIds).userTypes(Lists.newArrayList(UserType.ORG_USER.getCode())).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.groupId);
                    agentCustomerIdsForAccount = agentAccountBaseDtos.stream().map(AccountBaseDto::getCustomerId).distinct().collect(Collectors.toList());
                }
            }
            log.info("buildProductLinkInfo.agentCustomerIdsForAccount:{},{}", saleIds, agentCustomerIdsForAccount);
            agentCustomerIds.addAll(agentCustomerIdsForAccount);
            List<Integer> customerIdsOfSales = new ArrayList<>();
            if (!CollectionUtils.isEmpty(saleIds)) {
                // 私海客户直接匹配
                List<CrmSaleSeaMappingPo> crmSaleSeaMappingPos = saleSeaMappingRepo.queryMappingsBySaleIds(SaleSeaMappingType.CUSTOMER, saleIds, null, null);
                customerIdsOfSales = crmSaleSeaMappingPos.stream().filter(a -> a.getBiliUserType().equals(BiliUserType.CHANNEL_MANAGER.getCode())).map(t -> t.getMappingId()).distinct().collect(Collectors.toList());
            }
            log.info("buildProductLinkInfo.customerIdsOfSales:{},{}", saleIds, agentCustomerIdsForAccount);
            agentCustomerIds.addAll(customerIdsOfSales);
            agentCustomerIds = agentCustomerIds.stream().distinct().filter(a -> a > 0).collect(Collectors.toList());
            result.add(agentCustomerIds);
            List<Integer> aliveAgentCustomerIds = new ArrayList<>();
            if (!CollectionUtils.isEmpty(agentCustomerIds)) {
                List<Integer> finalAgentCustomerIds = agentCustomerIds;
                aliveAgentCustomerIds = commonDto.getQuarterLinkList().stream().filter(a -> a.getAgentCustomerId() != null && finalAgentCustomerIds.contains(a.getAgentCustomerId())).map(AmountDataGroupDto::getAgentCustomerId).distinct().filter(Objects::nonNull).filter(a -> a > 0).collect(Collectors.toList());
            }
            result.add(aliveAgentCustomerIds);
        }
        return result;
    }

    public List<SaleAchievementEfficiencyDayPO> saveSaleTimeData(AggDateTypeEnum typeEnum, SaleDto saleDto, Integer isDirectOrChannel, SaleAchievementEfficiencyDayPO originPo, List<Integer> accountIds, SaleEfficiencyCommonDto commonDto, List<Integer> saleIds) {
        // 在职销售环比
        // 新集团个数环比
        // 私海集团数 环比
        // 私海代理商数 环比
        // arpu 相关
        // 拜访相关
        // 新客相关
        // lbs相关
        // 商机相关
        List<SaleAchievementEfficiencyDayPO> pos = new ArrayList<>();
        SaleAchievementEfficiencyDayPO po = buildDefault();
        BeanUtils.copyProperties(originPo, po);
        po.setAgg_type(typeEnum.getCode());
        Timestamp quarterFirstDate = incomeTimeUtil.getQuarterFirstDate(commonDto.getBase());

        Timestamp begin = Utils.getBeginOfDay(commonDto.getBase());
        Timestamp end = Utils.getEndOfDay(commonDto.getBase());
        Timestamp linkBegin = incomeTimeUtil.getYesterdayBegin(commonDto.getBase());
        Timestamp linkEnd = incomeTimeUtil.getYesterdayEnd(commonDto.getBase());
        List<Integer> groupIds = Arrays.stream(Optional.ofNullable(po.getPrivate_sea_groups()).orElse("").split(",")).filter(a -> !StringUtils.isEmpty(a)).map(Integer::parseInt).collect(Collectors.toList());
        List<Integer> agentCustomerIds = Arrays.stream(Optional.ofNullable(po.getPrivate_sea_agents()).orElse("").split(",")).filter(a -> !StringUtils.isEmpty(a)).map(Integer::parseInt).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            groupIds.add(-1);
        }
        if (CollectionUtils.isEmpty(agentCustomerIds)) {
            agentCustomerIds.add(-1);
        }
        switch (typeEnum) {
            case DAY:
                begin = Utils.getBeginOfDay(commonDto.getBase());
                end = Utils.getEndOfDay(commonDto.getBase());
                linkBegin = incomeTimeUtil.getYesterdayBegin(commonDto.getBase());
                linkEnd = incomeTimeUtil.getYesterdayEnd(commonDto.getBase());
                break;
            case WEEK:
                begin = incomeTimeUtil.getStartOrEndDayOfWeek(commonDto.getBase(), true);
                end = Utils.getEndOfDay(commonDto.getBase());
                linkBegin = incomeTimeUtil.getNumDayTimeBefore(7, begin);
                linkEnd = Utils.getEndOfDay(incomeTimeUtil.getNumDayTimeBefore(7, end));
                break;
            case MONTH:
                begin = incomeTimeUtil.getMonthFirstDate(commonDto.getBase());
                end = Utils.getEndOfDay(commonDto.getBase());
                long monthGap = end.getTime() - begin.getTime();
                linkBegin = incomeTimeUtil.getSomeMonthAfterBeginTime(-1, begin);
                linkEnd = new Timestamp(linkBegin.getTime() + monthGap);
                break;
            case QUARTER:
                begin = incomeTimeUtil.getQuarterFirstDate(commonDto.getBase());
                end = Utils.getEndOfDay(commonDto.getBase());
                long lastQuarterBegin = incomeTimeUtil.getStartOrEndDayOfQuarter(commonDto.getBase(), -1, true);
                long gap = end.getTime() - quarterFirstDate.getTime();
                linkBegin = new Timestamp(lastQuarterBegin);
                linkEnd = new Timestamp(lastQuarterBegin + gap);
                break;
        }
        log.info("saveSaleTimeDataStart:{},{},{},{}", po.getBiz_name(), typeEnum, linkBegin, linkEnd);
        List<SaleAchievementEfficiencyDayPO> linkDayPos = queryEfficiencyPoList(linkEnd, Lists.newArrayList(originPo.getBiz_id()), new ArrayList<>(), Lists.newArrayList(typeEnum.getCode()), Lists.newArrayList(1, 3));
        List<Integer> linkSaleIds = new ArrayList<>();
        List<Integer> linkAccountIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(linkDayPos)) {
            SaleAchievementEfficiencyDayPO linkDayPo = linkDayPos.get(0);
            po.setOn_sale_link_amount(linkDayPo.getOn_sale_link_amount());
            po.setNew_group_link_amount(linkDayPo.getNew_group_amount());
            po.setPrivate_sea_group_link_amount(linkDayPo.getPrivate_sea_group_amount());
            po.setPrivate_sea_agent_link_amount(linkDayPo.getPrivate_sea_agent_amount());
            linkSaleIds = Arrays.stream(linkDayPo.getSaleIds().split(",")).filter(a -> !StringUtils.isEmpty(a)).map(Integer::parseInt).collect(Collectors.toList());
        } else {
            List<SaleDto> linkSaleDtoList = saleGroupService.getGroupMembersWithTime(saleDto, saleDto.getLevel(), linkEnd);
            linkSaleDtoList.add(saleDto);
            linkSaleDtoList = filterSale(linkSaleDtoList);
            linkSaleIds = linkSaleDtoList.stream().map(SaleDto::getId).distinct().collect(Collectors.toList());
            linkAccountIds = queryBelongAccountIds(saleIds, linkEnd, isDirectOrChannel);
            List<List<Integer>> result = buildProductLinkInfo(linkAccountIds, isDirectOrChannel, linkSaleIds, commonDto);
            po.setNew_group_link_amount(result.get(0).stream().filter(a -> commonDto.getLinkGroupIds().contains(a)).collect(Collectors.toList()).size());
            if (isDirectOrChannel == 1) {
                po.setPrivate_sea_group_link_amount(result.get(1).size());
            } else {
                po.setPrivate_sea_agent_link_amount(result.get(1).size());
            }
        }
        po.setOn_sale_link_amount(linkSaleIds.size());
        // 跟进拜访信息
        List<FollowManageCustomerDto> followDtoList = queryFollowManage(begin, end, saleIds);
        List<FollowManageCustomerDto> linkFollowDtoList = queryFollowManage(linkBegin, linkEnd, saleIds);
        log.info("saveSaleTimeDataFollowEnd,{},{}", po.getBiz_name(), typeEnum);
        // 新客报备信息
        List<NewCustomerReportDto> reportDtos = queryNewReport(begin, end, saleIds);
        log.info("saveSaleTimeDataNewReportEnd,{},{}", po.getBiz_name(), typeEnum);
        // 商机信息
        List<BsiOpportunityDto> bsiDtoList = queryBsi(begin, end, saleIds);
        List<BsiOpportunityDto> linkBsiDtoList = queryBsi(linkBegin, linkEnd, saleIds);
        log.info("saveSaleTimeDataBsiEnd,{},{}", po.getBiz_name(), typeEnum);
        if (isDirectOrChannel == 1) {
            List<Integer> visitGroupIds = followDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.CUSTOMER.getCode())).map(FollowManageCustomerDto::getGroupId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> visitNewReportIds = followDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.NEW_CUSTOMER.getCode())).map(FollowManageCustomerDto::getCustomerId).distinct().filter(Objects::nonNull).filter(a -> a > 0).collect(Collectors.toList());
            List<Integer> linkVisitGroupIds = linkFollowDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.CUSTOMER.getCode())).map(FollowManageCustomerDto::getGroupId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> linkVisitNewReportIds = linkFollowDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.NEW_CUSTOMER.getCode())).map(FollowManageCustomerDto::getCustomerId).filter(Objects::nonNull).distinct().filter(a -> a > 0).collect(Collectors.toList());
            // 跟进拜访 直客 拜访客户所属集团数 + 新客报备数
            po.setVisit_group_amount(visitGroupIds.size() + visitNewReportIds.size());
            po.setVisit_group_link_amount(linkVisitGroupIds.size() + linkVisitNewReportIds.size());
            po.setVisit_groups(visitGroupIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + "_" + visitNewReportIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            po.setGroup_new_customer_amount(reportDtos.stream().map(NewCustomerReportDto::getReportId).distinct().collect(Collectors.toList()).size());
            List<Integer> bsiGroupIds = bsiDtoList.stream().filter(a -> a.getCustomerId() > 0).map(BsiOpportunityDto::getGroupId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> bsiNewReportIds = bsiDtoList.stream().filter(a -> a.getCustomerId() == 0).map(BsiOpportunityDto::getReportId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Integer> bsiLinkGroupIds = linkBsiDtoList.stream().filter(a -> a.getCustomerId() > 0).map(BsiOpportunityDto::getGroupId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> bsiNewLinkReportIds = linkBsiDtoList.stream().filter(a -> a.getCustomerId() == 0).map(BsiOpportunityDto::getReportId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            po.setBsi_group_amount(bsiGroupIds.size() + bsiNewReportIds.size());
            po.setBsi_group_link_amount(bsiLinkGroupIds.size() + bsiNewLinkReportIds.size());
        } else {
            List<Long> visitCustomerIds = followDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.CUSTOMER.getCode())).map(FollowManageCustomerDto::getCustomerId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> visitLinkCustomerIds = linkFollowDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.CUSTOMER.getCode())).map(FollowManageCustomerDto::getCustomerId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> newVisitCustomerIds = followDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.NEW_CUSTOMER.getCode())).map(FollowManageCustomerDto::getCustomerId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Long> newLinkVisitCustomerIds = linkFollowDtoList.stream().filter(a -> a.getCustomerBizType().equals(CustomerBizType.NEW_CUSTOMER.getCode())).map(FollowManageCustomerDto::getCustomerId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            po.setVisit_agent_amount(visitCustomerIds.size() + newVisitCustomerIds.size());
            po.setVisit_agent_link_amount(visitLinkCustomerIds.size() + newLinkVisitCustomerIds.size());
            po.setVisit_agents(visitCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")) + "_" + newVisitCustomerIds.stream().map(String::valueOf).collect(Collectors.joining(",")));
            po.setAgent_new_customer_amount(reportDtos.stream().filter(a -> a.getCustomerType().equals(CustomerType.AGENT)).map(NewCustomerReportDto::getReportId).collect(Collectors.toList()).size());
            // 代理商
            List<Integer> agentIds = bsiDtoList.stream().map(BsiOpportunityDto::getAgentId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Integer> linkAgentIds = linkBsiDtoList.stream().map(BsiOpportunityDto::getAgentId).distinct().filter(a -> a > 0).collect(Collectors.toList());
            List<Integer> allAgentIds = new ArrayList<>();
            allAgentIds.addAll(linkAgentIds);
            allAgentIds.addAll(agentIds);
            log.info("saveAgentBsiAgentSize:{},{}", po.getBiz_name(), agentIds.size());
            Map<Integer, AgentDto> agentIdMapAgent = agentService.getAgentMapInIds(new ArrayList<>(allAgentIds));
            Map<Integer, Integer> agentAccountMap = agentIdMapAgent.values().stream().collect(Collectors.toMap(AgentDto::getId, AgentDto::getSysAgentId));
            List<Integer> bsiAccountIds = agentAccountMap.values().stream().distinct().filter(a -> a > 0).collect(Collectors.toList());
            log.info("saveAgentBsiAccountSize:{},{}", po.getBiz_name(), bsiAccountIds.size());
            if (CollectionUtils.isEmpty(bsiAccountIds)) {
                po.setBsi_agent_amount(0);
                po.setBsi_agent_link_amount(0);
            } else {
                List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(bsiAccountIds).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId);
                Map<Integer, Integer> accountCustomerMap = accountBaseDtos.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, AccountBaseDto::getCustomerId));
                log.info("saveAgentBsiAccountChangeSize:{},{}", po.getBiz_name(), accountCustomerMap.size());
                po.setBsi_agent_amount(agentIds.stream().map(agentAccountMap::get).filter(Objects::nonNull).map(accountCustomerMap::get).filter(Objects::nonNull).distinct().filter(a -> a > 0).collect(Collectors.toList()).size());
                po.setBsi_agent_link_amount(linkAgentIds.stream().map(agentAccountMap::get).filter(Objects::nonNull).map(accountCustomerMap::get).filter(Objects::nonNull).distinct().filter(a -> a > 0).collect(Collectors.toList()).size());
            }
        }
        po.setLbs_follow_record_amount(followDtoList.stream().map(FollowManageCustomerDto::getRecordDtoList).flatMap(Collection::stream).filter(a -> a.getFollowType().equals(FollowFirstType.OFFLINE.getCode()) && a.getFollowSecondType().equals(FollowSecondType.VISIT.getCode())).collect(Collectors.toList()).size());
        po.setAll_follow_record_amount(followDtoList.stream().map(FollowManageCustomerDto::getRecordDtoList).flatMap(Collection::stream).collect(Collectors.toList()).size());
        po.setAll_follow_record_amount_link(linkFollowDtoList.stream().map(FollowManageCustomerDto::getRecordDtoList).flatMap(Collection::stream).collect(Collectors.toList()).size());
        po.setBsi_amount(bsiDtoList.stream().map(BsiOpportunityDto::getId).distinct().collect(Collectors.toList()).size());
        po.setBsi_amount_link(linkBsiDtoList.stream().map(BsiOpportunityDto::getId).distinct().collect(Collectors.toList()).size());
        if (saleDto.getLevel().equals(SaleLevelEnum.LEADER.getCode()) && saleDto.getGroupIds().size() < 2) {
            SaleAchievementEfficiencyDayPO groupPo = buildDefault();
            BeanUtils.copyProperties(po, groupPo);
            groupPo.setBiz_id(saleDto.getGroupId());
            groupPo.setBiz_name(saleDto.getGroupName());
            groupPo.setBiz_type(SaleKpiTypeEnum.SALE_TEAM.getBizId());
            groupPo.setData_type(3);
            buildPOTaskAmount(groupPo, commonDto.getKpiDtoList(), SaleKpiTypeEnum.SALE_TEAM, groupPo.getBiz_id());
            pos.add(groupPo);
        }
        pos.add(po);
        return pos;
    }

    public void cacheTotalData(Timestamp base, List<Integer> groupIds, List<Integer> saleIds) {
        log.info("cacheTotalData:{},{},{}", base, groupIds, saleIds);
        List<SaleAchievementEfficiencyDayPO> poList = queryEfficiencyPoList(base, saleIds, groupIds, Lists.newArrayList(AggDateTypeEnum.QUARTER.getCode(), AggDateTypeEnum.MONTH.getCode(), AggDateTypeEnum.WEEK.getCode()), Lists.newArrayList(1, 3));
        log.info("cacheTotalDataSize:{}", poList.size());
        AchievementEstimateHiveCkQueryDTO queryDTO = new AchievementEstimateHiveCkQueryDTO();
        queryDTO.setQueryAll(Boolean.TRUE);
        queryDTO.setAggTime(base);
        List<CrmAchievementDayItemDTO> estimateDayPOS = achievementEstimateHiveCkService.queryCrmAchievementDayItemPoList(queryDTO);
        if (CollectionUtils.isEmpty(estimateDayPOS)) {
            return;
        }
        Map<String, CrmAchievementDayItemDTO> estimateDayPOMap = estimateDayPOS.stream().collect(Collectors.toMap(a -> String.format("%s_%s", a.getBizType(), a.getBizId()), Function.identity(), (r, b) -> r));
        for (SaleAchievementEfficiencyDayPO dayPO : poList) {
            dayPO.setTotal_complete_amount(estimateDayPOMap.getOrDefault(String.format("%s_%s", dayPO.getBiz_type(), dayPO.getBiz_id()), CrmAchievementDayItemDTO.builder().completedTaskAmount(0L).build()).getCompletedTaskAmount());
        }
        if (!CollectionUtils.isEmpty(poList)) {
            elasticsearchTemplateLiveGoods.bulkIndex(createIndexQuery(poList));
            messageSender.sendBatch(poList, INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY, EsMigrateCommon.INCR);
            //refresh刷新 Elasticsearch 中指定索引的数据。
            elasticsearchTemplateLiveGoods.refresh(INDEX_SALE_ACHIEVEMENT_EFFICIENCY_DAY);
        }
        log.info("efficiencyCacheTotalDataSize:{}", poList.size());
    }


    // 跟进拜访 lbs 注意
    public List<FollowManageCustomerDto> queryFollowManage(Timestamp start, Timestamp end, List<Integer> saleIds) {
        return followManageService.queryManageList(FollowManageQueryDto.builder()
                .followTimeLeft(start)
                .followTimeRight(end)
                .saleIds(saleIds)
                .followStatusList(Lists.newArrayList(FollowStatusType.HAS_DONE.getCode()))
                .build(), IncomeUtil.buildSuperAdmin());


    }

    // 新客报备
    public List<NewCustomerReportDto> queryNewReport(Timestamp start, Timestamp end, List<Integer> saleIds) {
        QueryNewCustomerReportDto reportDto = QueryNewCustomerReportDto.builder()
                .saleIds(saleIds)
                .applyTimeStart(start)
                .applyTimeEnd(end)
                .reportStates(Lists.newArrayList(NewCustomerReportStateEnum.VALID.getCode(), NewCustomerReportStateEnum.AUDITING.getCode()))
                .build();
        return newCustomerReportService.queryList(IncomeUtil.buildSuperAdmin(), reportDto, FlowListTypeEnum.ALL);
    }


    // 商机
    public List<BsiOpportunityDto> queryBsi(Timestamp start, Timestamp end, List<Integer> saleIds) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return new ArrayList<>();
        }
        List<String> emailByQueryDto = saleService.getEmailByQueryDto(QuerySaleDto.builder().saleIds(saleIds).build());
        PageResult<BsiOpportunityDto> pageResult = bsiOpportunityService.queryBrandBsiOpportunityList(BsiOpportunityQueryDto.builder()
                .ctimeBegin(start)
                .ctimeEnd(end)
                .createUsers(emailByQueryDto)
                .build(), 1, Integer.MAX_VALUE);
        List<Integer> customerIds = pageResult.getRecords().stream().map(BsiOpportunityDto::getCustomerId).filter(Objects::nonNull).filter(a -> a > 0).collect(Collectors.toList());
        List<CustomerBaseDto> customerBaseDtos = customerQueryService.getCustomerBaseDtosByIds(customerIds);
        Map<Integer, CustomerBaseDto> customerBaseDtoMap = customerBaseDtos.stream().collect(Collectors.toMap(CustomerBaseDto::getId, Function.identity()));
        pageResult.getRecords().stream().peek(a -> a.setGroupId(customerBaseDtoMap.getOrDefault(a.getCustomerId(), CustomerBaseDto.builder().groupId(0).build()).getGroupId())).collect(Collectors.toList());
        return pageResult.getRecords();
    }

    public List<BsiOpportunityDto> queryBsiWithCk(Timestamp start, Timestamp end, List<Integer> saleIds) {
        return bsiOpportunityService.queryBsiOpportunityListForCK(BsiOpportunityCKQueryDTO.builder()
                .createMinDate(CrmUtils.getStringByTimeLong(start.getTime()))
                .createMaxDate(CrmUtils.getStringByTimeLong(end.getTime()))
                .logDate(CrmUtils.formatDate(end, CrmUtils.YYYYMMDD))
                .saleIdList(saleIds)
                .build());
    }

    /**
     * 只查机构账户
     */
    public List<Integer> queryBelongAccountIds(List<Integer> saleIds, Timestamp base, Integer isDirectOrChannel) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return new ArrayList<>();
        }
        List<Integer> accountIdsInBelonging = saleSeaBelongingService.querySaleBelongingAccount(QuerySaleSeaBelongingDto.builder()
                .saleIds(saleIds)
                .bindDate(base)
                .build());
        //渠道
        List<Integer> agentAccountIdsInBelonging = saleSeaBelongingService.querySaleBelongingAccount(QuerySaleSeaBelongingDto.builder()
                .saleIds(saleIds)
                .bindDate(base)
                .biliUserType(BiliUserType.CHANNEL_MANAGER)
                .build());
        if (!CollectionUtils.isEmpty(agentAccountIdsInBelonging) && isDirectOrChannel == 2) {
            Map<Integer, Integer> accountId2AgentIdMapInAccountIds = agentService.getAccountId2AgentIdMapInAccountIds(agentAccountIdsInBelonging);
            if (accountId2AgentIdMapInAccountIds != null && !accountId2AgentIdMapInAccountIds.isEmpty()) {
                List<AccountBaseDto> channelAccBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder()
                        .dependencyAgentIds(new ArrayList<>(accountId2AgentIdMapInAccountIds.values()))
                        .userTypes(Lists.newArrayList(UserType.ORG_USER.getCode(), UserType.PERSONAL_USER.getCode())).build(), AccountFieldMapping.accountId);
                accountIdsInBelonging.addAll(channelAccBaseDtos.stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList()));
                accountIdsInBelonging.addAll(agentAccountIdsInBelonging);
            }
        }
        return accountIdsInBelonging.stream().distinct().collect(Collectors.toList());
    }

    public void buildPOTaskAmount(SaleAchievementEfficiencyDayPO po, List<CrmSaleKpiDto> kpiDtoList, SaleKpiTypeEnum bizType, Integer bizId) {
        if (po.getBiz_id() == 0) {
            bizType = SaleKpiTypeEnum.BIZ_PRODUCT_SALE;
        }
        SaleKpiTypeEnum finalBizType = bizType;
        CrmSaleKpiDto itKpi = kpiDtoList.stream()
                .filter(e -> finalBizType.getBizId().equals(e.getBizType()))
                .filter(e -> bizId.equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(0L).build());
        log.info("buildPOTaskAmount,bizType={},bizId={},quarter={},itKpi={}", bizType, bizId, itKpi.getQuarter(), itKpi.getQuarterKpi());
        po.setTotal_task_amount(itKpi.getQuarterKpi());
        po.setBiz_id(bizId);
        po.setBiz_type(bizType.getBizId());
        if (po.getBiz_name() == null) {
            po.setBiz_name(bizType.getDesc());
        }
    }


    public List<IndexQuery> createIndexQuery(List<SaleAchievementEfficiencyDayPO> esResult) {
        return esResult.stream().map(po -> {
            IndexQuery indexQuery = new IndexQuery();
            indexQuery.setId(po.getBiz_id() + "_" + po.getBiz_type() + "_" + po.getAgg_type() + "_" + po.getData_type() + "_" + po.getAgg_time());
            po.setId(indexQuery.getId());
            indexQuery.setObject(po);
            return indexQuery;
        }).collect(Collectors.toList());
    }

    /**
     * 需排除直客-虚拟销售
     * 去除内部下单销售组
     *
     * @param saleDtos
     * @return
     */
    public List<SaleDto> filterSale(List<SaleDto> saleDtos) {
        saleDtos = saleDtos.stream().filter(Objects::nonNull).filter(a -> !Lists.newArrayList(PM_SALE_ID, TEST_SALE).contains(a.getId()) &&
                (a.getType() == null || !Lists.newArrayList(DIRECT_VIRTUAL_TYPE).contains(a.getType()))
        ).collect(Collectors.toList());
        return saleDtos;
    }

    public List<SaleDto> filterSaleWithTime(List<SaleDto> saleDtos, Timestamp quarterBegin, List<Integer> noConditionSaleIds, List<Integer> hasTargetSaleIds, Timestamp base) {
        saleDtos = saleDtos.stream()
                .filter(Objects::nonNull).filter(a -> !Lists.newArrayList(PM_SALE_ID, TEST_SALE).contains(a.getId()) &&
                        (a.getType() == null || !Lists.newArrayList(DIRECT_VIRTUAL_TYPE).contains(a.getType())))
                .filter(a -> a.getSaleIsQuit().equals(IsValid.FALSE.getCode()) || a.getSaleQuitTime() == null || a.getSaleQuitTime().compareTo(quarterBegin) >= 0)
                .filter(a -> noConditionSaleIds.contains(a.getId()))
                .collect(Collectors.toList());
        if (Utils.getEachDays(quarterBegin, base).size() > targetTime) {
            saleDtos = saleDtos.stream().filter(a -> hasTargetSaleIds.contains(a.getId())).collect(Collectors.toList());
        }
        return saleDtos;
    }

    public SaleAchievementEfficiencyDayPO buildDefault() {
        return SaleAchievementEfficiencyDayPO.builder()
                .biz_id(0)
                .biz_type(0)
                .agg_type(0)
                .agg_time(0L)
                .data_type(0)
                .biz_name("")
                .direct_or_channel(1)
                .total_complete_amount(0L)
                .total_complete_link_amount(0L)
                .new_group_amount(0)
                .new_group_link_amount(0)
                .on_sale_amount(0)
                .on_sale_link_amount(0)
                .private_sea_group_amount(0)
                .private_sea_group_link_amount(0)
                .private_sea_agent_amount(0)
                .private_sea_agent_link_amount(0)
                .private_sea_group_alive_amount(0)
                .private_sea_agent_alive_amount(0)
                .group_average_arpu(0L)
                .group_average_arpu_link(0L)
                .alive_group_average_arpu(0L)
                .alive_group_average_arpu_link(0L)
                .agent_average_arpu(0L)
                .agent_average_arpu_link(0L)
                .alive_agent_average_arpu(0L)
                .alive_agent_average_arpu_link(0L)
                .visit_group_amount(0)
                .saleIds("")
                .private_sea_groups("")
                .private_sea_agents("")
                .private_sea_alive_groups("")
                .private_sea_alive_agents("")
                .visit_groups("")
                .visit_agents("")
                .visit_group_link_amount(0)
                .visit_agent_amount(0)
                .visit_agent_link_amount(0)
                .group_new_customer_amount(0)
                .agent_new_customer_amount(0)
                .lbs_follow_record_amount(0)
                .all_follow_record_amount(0)
                .all_follow_record_amount_link(0)
                .bsi_amount(0)
                .bsi_amount_link(0)
                .bsi_group_amount(0)
                .bsi_group_link_amount(0)
                .bsi_agent_amount(0)
                .bsi_agent_link_amount(0)
                .build();

    }

    /**
     * 计算总数
     *
     * @param operator
     * @param queryAchieveDto
     * @param isDirectOrChannel
     * @return
     */
    public Long buildTotal(Operator operator, QueryAchieveDto queryAchieveDto, Integer isDirectOrChannel, Boolean isHistory) {

        //cpc
        List<AchievementRtbData> cpc = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.CPC, achieveFromWalletService::getWalletAggByDayByWideES);
        //cpm
        List<AchievementRtbData> cpm = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.CPM, achieveFromWalletService::getWalletAggByDayByWideES);
        //ADX
        List<AchievementRtbData> adx = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.ADX, achieveFromWalletService::getWalletAggByDayByWideES);
        //dpa
        List<AchievementRtbData> dpa = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.DPA, achieveFromWalletService::getWalletAggByDayByWideES);
        //内容起飞
        List<AchievementRtbData> contentFly = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.CONTENT_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        //个人起飞
        List<AchievementRtbData> personFly = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        //商业起飞（预付费）
        List<AchievementRtbData> businessAdvancePay = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);
        // 效果实结
        List<AchievementRtbData> close = rtbService.getConsumeDataByFunctionV2(operator, queryAchieveDto, IncomeComposition.EFFECT_CLOSE, achieveFromWalletService::getWalletAggByDayByWideES);
        AchievementContractCheckQuotaDto achievementContractCheckQuotaDto = achievementContractService.getCheckedBillQuotaDto(operator, queryAchieveDto, isHistory);
        //花火商单已完成金额
        List<AchievementDto> pickUpBillDetailByWideEs = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_CONFIRMED);
        List<AchievementDto> boostingPickUpBillDetailByWideEs = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_CONFIRMED);
        BigDecimal cpcAmount = achievementEstimateService.amountRtbSum(cpc);
        BigDecimal adxAmount = achievementEstimateService.amountRtbSum(adx);
        BigDecimal dpaAmount = achievementEstimateService.amountRtbSum(dpa);
        BigDecimal cpmAmount = achievementEstimateService.amountRtbSum(cpm);
        BigDecimal closeAmount = achievementEstimateService.amountRtbSum(close);
        BigDecimal personFlyAmount = queryAchieveDto.getDateEnd().compareTo(CommonConstant.FLY_ADJUST_END_TIME) <= 0 && queryAchieveDto.getDateEnd().compareTo(CommonConstant.FLY_ADJUST_START_TIME) >= 0 ?
                achievementEstimateService.amountRtbSum(personFly).multiply(CommonConstant.PERSONAL_FLY_COEFFICIENT) : achievementEstimateService.amountRtbSum(personFly);
        BigDecimal contentFlyAmount = achievementEstimateService.amountRtbSum(contentFly);
        BigDecimal businessAdvancePayAmount = achievementEstimateService.amountRtbSum(businessAdvancePay);
        BigDecimal effectTotal = cpcAmount.add(adxAmount).add(dpaAmount).add(cpmAmount)
                .add(businessAdvancePayAmount).add(closeAmount);
        if (!Objects.equals(isDirectOrChannel, 2)) {
            effectTotal = effectTotal.add(contentFlyAmount);
        }
        if (!Objects.equals(isDirectOrChannel, 1) && !Objects.equals(isDirectOrChannel, 2)) {
            effectTotal = effectTotal.add(personFlyAmount);
        }
        BigDecimal pickUpConfirmed = achievementEstimateService.amountSum(pickUpBillDetailByWideEs);
        BigDecimal boostingConfirmed = achievementEstimateService.amountSum(boostingPickUpBillDetailByWideEs);
        BigDecimal contractCheckedDecimal = achievementContractCheckQuotaDto.getClosedAmount().add(achievementContractCheckQuotaDto.getUnClosedAmount());
        log.info("efficiencyBuildTotal:,queryAchieveDto:{}cpc:{},cpm:{},adx:{},dpa:{},contentFly:{},personFly:{},businessAdvancePay:{},close:{},pickUpConfirmed:{}," +
                "boostingConfirmed:{},contractCheckedDecimal:{}", queryAchieveDto, cpcAmount, cpmAmount, adxAmount, dpaAmount, contentFlyAmount, personFlyAmount, businessAdvancePayAmount, closeAmount, pickUpConfirmed, boostingConfirmed, contractCheckedDecimal);
        return effectTotal.add(pickUpConfirmed).add(boostingConfirmed).add(contractCheckedDecimal).longValue();
    }


    public List<AmountDataGroupDto> buildDto(Timestamp begin, Timestamp end, Boolean isHistory) {
        Operator operator = IncomeUtil.buildSuperAdmin();
        QueryAchieveDto queryAchieveDto = QueryAchieveDto.builder()
                .dateBegin(begin)
                .dateEnd(end)
                .build();
        List<AmountDataGroupDto> result = new ArrayList<>();
        log.info("cacheRequest,开始");
        //合约广告
        List<BillDetailReport> achievementCheckedReport = reportService.getAchievementCheckedReport(operator, queryAchieveDto, isHistory);
        //必选 宽表
        List<RtbReportAchievement> rtbData = rtbReportService.getRtbReportData(operator, queryAchieveDto);
        //商业起飞 宽表
        List<RtbReportAchievement> bsiFlyData = rtbReportService.getBsiFlyReportDataFormWideEs(operator, queryAchieveDto);
        //DPA 宽表
        List<RtbReportAchievement> dpaData = rtbReportService.getDpaReportData(operator, queryAchieveDto);
        //ADX 宽表
        List<AdxReportAchievement> adxReportData = rtbReportService.getAdxReportData(operator, queryAchieveDto);

        // 效果实结数据
        List<RtbReportAchievement> rtbCloseData = rtbReportService.getRtbCloseReportData(operator, queryAchieveDto);
        List<RtbReportAchievement> dpaCloseData = rtbReportService.getDpaCloseReportData(operator, queryAchieveDto);
        List<AdxReportAchievement> adxCloseData = rtbReportService.getAdxCloseReportData(operator, queryAchieveDto);

        //内容起飞 宽表
        List<RtbReportAchievement> contentFlyData = rtbReportService.getContentFlyReportData(operator, queryAchieveDto);
        //花火已确认收入 宽表
        List<BillDetailReport> pickConfirmData = reportService.getPickUpConfirmReportData(operator, queryAchieveDto);
        // 合伙人-线索通
        List<RtbReportAchievement> cluePass = rtbReportService.getCluePassReportData(operator, queryAchieveDto);
        List<AmountDataGroupDto> contractList = contractToGroupDto(achievementCheckedReport);
        List<AmountDataGroupDto> pickUpList = contractToGroupDto(pickConfirmData);
        List<AmountDataGroupDto> rtbList = rtbToGroupDto(rtbData, false);
        List<AmountDataGroupDto> bsiFlyList = rtbToGroupDto(bsiFlyData, false);
        List<AmountDataGroupDto> dpaList = rtbToGroupDto(dpaData, false);
        List<AmountDataGroupDto> adxList = adxToGroupDto(adxReportData);
        List<AmountDataGroupDto> rtbCloseList = rtbToGroupDto(rtbCloseData, false);
        List<AmountDataGroupDto> dpaCloseList = rtbToGroupDto(dpaCloseData, false);
        List<AmountDataGroupDto> adxCloseList = adxToGroupDto(adxCloseData);
        List<AmountDataGroupDto> contentFlyList = rtbToGroupDto(contentFlyData, true);
        List<AmountDataGroupDto> cluePassList = rtbToGroupDto(cluePass, false);
        result.addAll(contractList);
        result.addAll(pickUpList);
        result.addAll(rtbList);
        result.addAll(bsiFlyList);
        result.addAll(dpaList);
        result.addAll(adxList);
        result.addAll(rtbCloseList);
        result.addAll(dpaCloseList);
        result.addAll(adxCloseList);
        result.addAll(contentFlyList);
        result.addAll(cluePassList);
        log.info("efficiencyBuildGroup:queryAchieveDto:{},contractList:{},pickUpList:{},rtbList:{},bsiFlyList:{},dpaList:{},adxList:{},rtbCloseList:{}," +
                        "dpaCloseList:{},adxCloseList:{},contentFlyList:{},cluePassList:{}", queryAchieveDto,
                contractList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                pickUpList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                rtbList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                bsiFlyList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                dpaList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                adxList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                rtbCloseList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                dpaCloseList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                adxCloseList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                contentFlyList.stream().mapToLong(AmountDataGroupDto::getAmount).sum(),
                cluePassList.stream().mapToLong(AmountDataGroupDto::getAmount).sum()
        );
        return result;
    }

    private List<AmountDataGroupDto> contractToGroupDto(List<BillDetailReport> billList) {
        if (CollectionUtils.isEmpty(billList)) {
            return new ArrayList<>();
        }
        List<AmountDataGroupDto> result = new ArrayList<>();
        for (BillDetailReport billDetailReport : billList) {
            AmountDataGroupDto groupDto = new AmountDataGroupDto();
            groupDto.setOrderId(billDetailReport.getBillId());
            groupDto.setAccountId(billDetailReport.getAccountId());
            groupDto.setCustomerId(billDetailReport.getCustomerId());
            groupDto.setProductId(billDetailReport.getProductId());
            groupDto.setAgentAccountId(billDetailReport.getAgentAccountId());
            groupDto.setGroupId(billDetailReport.getGroupId());
            groupDto.setSaleIds(billDetailReport.getSaleId());
            groupDto.setAgentCustomerId(billDetailReport.getAgentCustomerId());
            groupDto.setAmount(billDetailReport.getDailyPackageAmount().longValue());
            result.add(groupDto);
        }
        return result;

    }

    private List<AmountDataGroupDto> adxToGroupDto(List<AdxReportAchievement> rtbList) {
        if (CollectionUtils.isEmpty(rtbList)) {
            return new ArrayList<>();
        }
        Map<Integer, List<AdxReportAchievement>> rtbMap = rtbList.stream().collect(Collectors.groupingBy(AdxReportAchievement::getAccount_id));
        List<AmountDataGroupDto> result = new ArrayList<>();
        for (Integer accountId : rtbMap.keySet()) {
            AmountDataGroupDto groupDto = new AmountDataGroupDto();
            List<AdxReportAchievement> rtbGroupList = rtbMap.get(accountId);
            groupDto.setAccountId(accountId);
            groupDto.setCustomerId(rtbGroupList.get(0).getAccount_customer_id());
            groupDto.setProductId(rtbGroupList.get(0).getProduct_id());
            groupDto.setGroupId(rtbGroupList.get(0).getGroup_id());
            groupDto.setSaleIds(Stream.of(Optional.ofNullable(rtbGroupList.get(0).getStraightSaleId()).orElse(new ArrayList<>()),
                    Optional.ofNullable(rtbGroupList.get(0).getChannelSaleId()).orElse(new ArrayList<>())).flatMap(Collection::stream).collect(Collectors.toList()));
            groupDto.setGroupId(rtbGroupList.get(0).getGroup_id());
            groupDto.setAgentCustomerId(rtbGroupList.get(0).getAgent_customer_id());
            groupDto.setAmount(rtbGroupList.stream().map(AdxReportAchievement::getTotal_consume).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(new BigDecimal(100)).longValue());
            result.add(groupDto);
        }
        return result;
    }

    private List<AmountDataGroupDto> rtbToGroupDto(List<RtbReportAchievement> rtbList, Boolean isContentFly) {
        if (CollectionUtils.isEmpty(rtbList)) {
            return new ArrayList<>();
        }
        List<AmountDataGroupDto> result = new ArrayList<>();
        for (RtbReportAchievement achievement : rtbList) {
            AmountDataGroupDto groupDto = new AmountDataGroupDto();
            groupDto.setAccountId(achievement.getAccount_id());
            groupDto.setCustomerId(achievement.getAccount_customer_id());
            groupDto.setProductId(achievement.getProduct_id());
            groupDto.setGroupId(achievement.getGroup_id());
            groupDto.setSaleIds(Stream.of(Optional.ofNullable(achievement.getStraightSaleId()).orElse(new ArrayList<>()),
                    Optional.ofNullable(achievement.getChannelSaleId()).orElse(new ArrayList<>())).flatMap(Collection::stream).collect(Collectors.toList()));
            groupDto.setGroupId(achievement.getGroup_id());
            groupDto.setAgentCustomerId(achievement.getAgent_customer_id());
            groupDto.setAmount(Lists.newArrayList(achievement).stream().map(RtbReportAchievement::getTotal_consume).reduce(BigDecimal.ZERO, BigDecimal::add).multiply(new BigDecimal(100)).longValue());
            groupDto.setIsContentFly(isContentFly);
            groupDto.setDate(achievement.getDate_time());
            result.add(groupDto);
        }
        return result;
    }

    /**
     * 获取 副组长 + 小组长为多个组的情况
     */
    public List<Integer> buildDLeaderIds(List<SaleDto> dtoList) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return new ArrayList<>();
        }
        List<SaleDto> noLeaderList = dtoList.stream()
                .filter(a -> a.getLevel().equals(SaleLevelEnum.SALE.getCode()))
                .collect(Collectors.toList());
        List<SaleDto> leaderList = dtoList.stream()
                .filter(a -> a.getLevel().equals(SaleLevelEnum.LEADER.getCode()))
                .collect(Collectors.toList());
        List<Integer> manyGroupLeaders = leaderList.stream()
                .filter(a -> Optional.ofNullable(a.getGroupIds()).orElse(new ArrayList<>()).size() > 1)
                .map(SaleDto::getId)
                .collect(Collectors.toList());
        List<Integer> leadIds = new ArrayList<>();
        for (SaleDto saleDto : noLeaderList) {
            List<SaleDto> saleDtos = saleGroupService.getGroupMembersNew(saleDto, saleDto.getLevel());
            if (!saleDtos.isEmpty()) {
                leadIds.add(saleDto.getId());
            }
        }
        leadIds.addAll(manyGroupLeaders);
        return leadIds.stream().distinct().collect(Collectors.toList());
    }

    public List<Integer> queryNewGroupIds(Timestamp base) {
        try {
            CrmGroupProductKaLabelPoExample example = new CrmGroupProductKaLabelPoExample();
            CrmGroupProductKaLabelPoExample.Criteria criteria = example.createCriteria();
            criteria.andIsNewEqualTo(1).andLogDateEqualTo(CrmUtils.formatDate(incomeTimeUtil.getYesterdayBegin(base), CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
            List<CrmGroupProductKaLabelPo> labelPos = crmGroupProductKaLabelDao.selectByExample(example);
            return labelPos.stream().map(a -> a.getGroupId().intValue()).distinct().filter(a -> a > 0).collect(Collectors.toList());
        } catch (Exception e) {
            log.error("queryNewGroupIdsError", e);
            return new ArrayList<>();
        }

    }

    public Long calArpu(Long moneyAmount, Integer saleAmount, Integer privateAmount) {
        if (moneyAmount == 0 || saleAmount == 0 || privateAmount == 0) {
            return 0L;
        }
        return incomeUtil.divide(incomeUtil.divide(moneyAmount, saleAmount, 4), BigDecimal.valueOf(privateAmount), 4).longValue();
    }

    public boolean isValid(List<Integer> saleIds, List<Integer> filterSaleIds) {
        if (org.apache.commons.collections.CollectionUtils.isEmpty(saleIds) || CollectionUtils.isEmpty(filterSaleIds)) {
            return false;
        }
        for (Integer saleId : saleIds) {
            if (filterSaleIds.contains(saleId)) {
                return Boolean.TRUE;
            }
        }
        return Boolean.FALSE;
    }

}
