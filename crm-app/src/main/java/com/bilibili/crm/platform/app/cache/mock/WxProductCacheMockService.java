package com.bilibili.crm.platform.app.cache.mock;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.income.dto.ProductIncomeComposition;
import com.bilibili.crm.platform.api.income.dto.WxQuotaType;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.app.api.service.IWxAppPaperService;
import com.bilibili.crm.platform.app.biz.service.WxAppProductIncomeService;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.WxAppProductCacheWriter;
import com.bilibili.crm.platform.app.cache.config.WxAppRequestKey;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeConfigHelper;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/5/20 2:12 下午
 */
@Slf4j
@Component
public class WxProductCacheMockService {
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private WxAppProductCacheWriter cacheWriter;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private IWxAppPaperService wxAppPaperService;
    @Autowired
    private WxAppProductIncomeService wxAppProductIncomeService;
    @Autowired
    private IncomeConfigHelper incomeConfigHelper;

    /**
     * 初始化 缓存请求
     */
    public void initRequest(Timestamp begin, Timestamp end) {
        // 某天至昨天的所有天
        List<Timestamp> dayList = Utils.getEachDays(begin, end);
        dayList.forEach(d -> {
            initRequestData(d);
        });
        log.info("WxProductCacheMockService.initRequest finish begin {} ,end {}", begin, end);
    }

    /**
     * 初始化 缓存数据
     */
//    public void initYesterdayCalculateData(Timestamp begin, Timestamp end) throws WxAppException {
//        // 某天至昨天的所有天
//        List<Timestamp> dayList = Utils.getEachDays(begin, end);
//        for (Timestamp d : dayList) {
//            initYesterday(d);
//        }
//        log.info("WxProductCacheMockService.initYesterdayCalculateData finish begin {} ,end {}", begin, end);
//    }

    private void initRequestData(Timestamp base) {
        Timestamp day = incomeTimeUtil.getYesterdayBegin(base);
        //产品-UP
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_UP_COMPOSITION_UPDATE, wxAppProductIncomeService.getUpIncomeComposition(base));
    }


//    private void initYesterday(Timestamp base) {
//        //昨日
//        Timestamp begin = incomeTimeUtil.getYesterdayBegin(base);
//        Timestamp end = incomeTimeUtil.getYesterdayEnd(base);
//
//        cacheComeCompositionInTime(begin, end, Lists.newArrayList(ProductIncomeComposition.UP_BSI_PICK_UP,ProductIncomeComposition.UP_BSI_INVITE));
////        //up主-主商团队
////        cacheWriter.cacheInTime(begin, end, incomeConfigHelper.getProductUpBsiComposition(), ProductIncomeComposition.UP_BSI_TOTAL);
////        //up主-所有团队
////        cacheWriter.cacheInTime(begin, end, incomeConfigHelper.getProductUpTotalComposition(), ProductIncomeComposition.UP_TOTAL);
//
//    }

//    private void initQuarter(Timestamp base) {
//        //季度
//        Timestamp begin = incomeTimeUtil.getQuarterFirstDate(incomeTimeUtil.getYesterdayBegin(base));
//        Timestamp end = incomeTimeUtil.getYesterdayEnd(base);
//        //up主-主商团队
//        cacheWriter.cacheInTime(begin, end, incomeConfigHelper.getProductUpBsiComposition(), ProductIncomeComposition.UP_BSI_TOTAL);
//        //up主-所有团队
//        cacheWriter.cacheInTime(begin, end, incomeConfigHelper.getProductUpTotalComposition(), ProductIncomeComposition.UP_TOTAL);
//    }


//    // 缓存 某段范围的收入
//    public void cacheComeCompositionInTime(Timestamp begin, Timestamp end, List<ProductIncomeComposition> compositions) {
//        BigDecimal totalAmount = BigDecimal.ZERO;
//
//        for (ProductIncomeComposition composition : compositions) {
//            try {
//                BigDecimal income = wxAppPaperService.getProductIncome(begin, end, composition);
//                totalAmount = totalAmount.add(income);
//                wxAppCacheManager.cacheCalculateData(begin, end, income, WxModuleType.PRODUCT.name(), WxQuotaType.INCOME.name(), composition.name());
//            } catch (IncomeConditionInvalidException e) {
//                wxAppCacheManager.cacheCalculateData(begin, end, BigDecimal.ZERO, WxModuleType.PRODUCT.name(), WxQuotaType.INCOME.name(), composition.name());
//            }
//
//        }
//    }


    public BigDecimal mockQuota(Timestamp begin, Timestamp end, ProductIncomeComposition composition) throws IncomeConditionInvalidException {

        return wxAppPaperService.getProductQuota(begin, end, composition).getAmount();

    }

//    public BigDecimal mockIncome(Timestamp begin, Timestamp end, ProductIncomeComposition composition) throws IncomeConditionInvalidException {
//        return wxAppPaperService.getProductIncome(begin, end, composition);
//    }

}
