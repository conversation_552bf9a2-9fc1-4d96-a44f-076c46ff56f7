package com.bilibili.crm.platform.app.api.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.achievement.service.AchievementEstimateBizService;
import com.bilibili.crm.biz.dataprepare.service.CrmKpiCkPrepareService;
import com.bilibili.crm.biz.sales.bo.CrmSaleBO;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.ListUtils;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityCKQueryDTO;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityDto;
import com.bilibili.crm.platform.api.sale.dto.*;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.app.api.service.dto.*;
import com.bilibili.crm.platform.biz.addata.dao.write.*;
import com.bilibili.crm.platform.biz.annotation.ObjectParamCache;
import com.bilibili.crm.platform.biz.annotation.StringParamCache;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.clickhouse.dao.AdsCrmCommerceKpiWithSaleAmountDataADDao;
import com.bilibili.crm.platform.biz.clickhouse.param.AchievementEstimateHiveCkDetailQueryParam;
import com.bilibili.crm.platform.biz.clickhouse.param.AdsCrmCommerceKpiWithSaleAmountDataADQueryParam;
import com.bilibili.crm.platform.biz.common.FollowStage;
import com.bilibili.crm.platform.biz.po.addata.*;
import com.bilibili.crm.platform.biz.po.clickhouse.*;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmCommerceKpiWithSaleAmountDataADPO;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmCommerceKpiWithSaleAmountExportDataADPO;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmCommerceKpiWithSaleAmountRealDataADPO;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmCommerceKpiWithSaleInfoExportDataADPO;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.SaleGroupStatus;
import com.bilibili.crm.platform.common.SaleLevelEnum;
import com.bilibili.crm.platform.common.SaleTypeStatus;
import com.bilibili.crm.platform.common.dashboard.DashBoardShowOrderEnum;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.bilibili.crm.platform.utils.MathUtils;
import com.bilibili.crm.platform.utils.ThreadPoolUtils;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 仪表盘-预估看板-业绩季度预估HiveCkService
 *
 * <AUTHOR>
 * date 2024/04/30 17:16.
 * Contact: <EMAIL>.
 */
@Component
@Slf4j
public class AchievementEstimateHiveCkService {
    @Autowired
    private ISaleService iSaleService;
    @Autowired
    private ISaleGroupService iSaleGroupService;
    @Autowired
    private AdsCrmCommerceKpiWithSaleAmountDataADDao adsCrmCommerceKpiWithSaleAmountDataADDao;

    @Autowired
    private CrmKpiCkPrepareService crmKpiCkPrepareService;
    @Resource
    private IBsiOpportunityService bsiOpportunityService;
    @DynamicValue
    @Value("${crm.portal.env:prd}")
    private String env;
//    private String env = "pre";
    @DynamicValue
    @Value("${crm.offset.group:0,46,95,64,90,96,15,83,101,107,84,102,75,77,78,109,100,76,55,103,104,52,60,85,105,14,89}")
    private String needCalGroup;
    @DynamicValue
    @Value("${achievement.log.date.config:{\"20240630\":\"20240702\"}}")
    private String achievementLogDateConfig;
    @DynamicValue
    @Value("${crm.card.agg.start.time:1685548800000}")
    private String cardAggTimeStart;
    @Resource
    private CrmAchievementDayByTypeDao crmAchievementDayByTypeDao;
    @Resource
    private CrmAchievementDayItemDao crmAchievementDayItemDao;
    @Resource
    private CrmAchievementCardDao crmAchievementCardDao;
    @Resource
    private CrmAchievementEveryDayDao crmAchievementEveryDayDao;

    @Autowired
    private AchievementEstimateBizService achievementEstimateBizService;
    @Resource
    private CrmCacheManager cacheManager;
    @Autowired
    private WxRobotService wxRobotService;
    @Resource
    private CrmAchievementEveryDayExtendDao crmAchievementEveryDayExtendDao;
    public static final Integer DIRECT_GROUP_ID = 46;
    public static final Integer CHANNEL_GROUP_ID = 85;
    public static final Integer PM_GROUP_ID = 38;
    public static final String SALE_HAVE_AUTH_TARGER_AMOUNT_CASH_MAP = "SALE_HAVE_AUTH_TARGER_AMOUNT_CASH_MAP";

    public static final String HAVE_SALE_YJ_BIG_CK_TABLE = "HAVE_SALE_YJ_BIG_CK_TABLE";

    public static final Timestamp TIME_20240630 = new Timestamp(1719676800000L);
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;

    public boolean achievementDayDataIsReadyByDate(Timestamp date) {
        CrmAchievementDayItemPoExample poExample = new CrmAchievementDayItemPoExample();
        poExample.createCriteria().andAggTimeEqualTo(date).andDataEnvEqualTo(env);
        return crmAchievementDayItemDao.countByExample(poExample) > 0;
    }

    public boolean achievementCardDataIsReadyByDate(Timestamp date) {
        CrmAchievementCardPoExample poExample = new CrmAchievementCardPoExample();
        poExample.createCriteria().andDataEnvEqualTo(env).andAggLogTimeEqualTo(date).andAggTimeStartEqualTo(new Timestamp(Long.parseLong(cardAggTimeStart)));
        return crmAchievementCardDao.countByExample(poExample) > 0;
    }

    public void buildDtoForItemPoListWithProductDetail(List<CrmAchievementDayItemDTO> dtoList, AchievementEstimateHiveCkQueryDTO queryDTO) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return;
        }
        if (Objects.equals(queryDTO.getQueryProductType(), Boolean.TRUE)) {
            List<Long> itemPoIds = dtoList.stream().map(CrmAchievementDayItemDTO::getId).collect(Collectors.toList());
            CrmAchievementDayByTypePoExample typePoExample = new CrmAchievementDayByTypePoExample();
            typePoExample.createCriteria().andItemIdIn(itemPoIds);
            List<CrmAchievementDayByTypePo> typePoList = crmAchievementDayByTypeDao.selectByExample(typePoExample);
            Map<Long, List<CrmAchievementDayByTypePo>> typePoGroupMap = typePoList.stream().collect(Collectors.groupingBy(CrmAchievementDayByTypePo::getItemId));
            dtoList.forEach(t -> t.setDayByTypePoList(typePoGroupMap.get(t.getId())));
        }
    }

    public List<Integer> queryOffSetGroup() {
        return Arrays.stream(needCalGroup.split(",")).map(Integer::parseInt).collect(Collectors.toList());
    }

    /**
     * 计算销售组长和副组长有权限的目标销售组
     */
    public Map<Integer, List<Integer>> calculateSaleTargetAmountMapToCash(Integer updateFlag) {
        Map<Integer, List<Integer>> result = new HashMap<>();
        Object cash = cacheManager.getValue(SALE_HAVE_AUTH_TARGER_AMOUNT_CASH_MAP);
        if (Objects.nonNull(cash) && !Objects.equals(updateFlag, 1)) {
            result = JSON.parseObject(cash.toString(), new TypeReference<Map<Integer, List<Integer>>>() {
            });
            return result;
        }
        Map<Integer, SaleGroupDto> allSaleGroupMap = iSaleGroupService.getAllGroupMap();
        List<SaleGroupDto> SaleGroupDtoList = new ArrayList<>(allSaleGroupMap.values());
        Map<Integer, SaleBaseDto> allSaleMap = iSaleService.getAllSaleBaseDtoMap();

        Map<Integer, CrmSaleBO> saleRoleMap = saleGroupMappingServiceImpl.querySales(new ArrayList<>(allSaleMap.keySet()));
        List<SaleBaseDto> allSaleList = new ArrayList<>(allSaleMap.values());
        for (SaleBaseDto oneSale : allSaleList) {
            List<Integer> childGroupIds = new ArrayList<>();
            CrmSaleBO saleRole = saleRoleMap.getOrDefault(oneSale.getId(), new CrmSaleBO());
            if (saleRole.isLeader()) {
                childGroupIds.add(oneSale.getGroupId());
                childGroupIds.addAll(SaleGroupDtoList.stream()
                        .map(SaleGroupDto::getId)
                        .filter(saleRole::inGroup)
                        .collect(Collectors.toList()));
            }
            if (!CollectionUtils.isEmpty(childGroupIds)) {
                result.put(oneSale.getId(), childGroupIds.stream().distinct().collect(Collectors.toList()));
            }
        }
        if (!result.isEmpty()) {
            cacheManager.setValueWithTime(SALE_HAVE_AUTH_TARGER_AMOUNT_CASH_MAP, JSON.toJSONString(result), TimeUnit.MINUTES, 10);
        }
        return result;
    }

    public List<CrmAchievementDayItemDTO> buildDtoForItemPoListWithKpiTarget(List<CrmAchievementDayItemPo> poList, AchievementEstimateHiveCkQueryDTO queryDTO) {
        //所有的任务信息
        List<CrmSaleKpiDto> kpiDtoList = iSaleService.queryAllKpiByQuarter(CrmUtils.getQuarterDescByTime(queryDTO.getAggTime(), ""));
        Map<Integer, List<Integer>> saleTargetGroupMap = calculateSaleTargetAmountMapToCash(0);
        Map<String, CrmSaleKpiDto> kpiDtoMap = kpiDtoList.stream().collect(Collectors.toMap(a -> a.getBizType() + "_" + a.getBizId(), Function.identity(), (o, n) -> n));
        List<CrmAchievementDayItemDTO> dtoList = new LinkedList<>();
        poList.forEach(itemPo -> {
            CrmAchievementDayItemDTO itemDTO = new CrmAchievementDayItemDTO();
            BeanUtils.copyProperties(itemPo, itemDTO);
            if (Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.SALE_TEAM.getBizId()) ||
                    Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.BILIBILI.getBizId())) {
                if (Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.BILIBILI.getBizId())) {
                    itemDTO.setTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.BIZ_PRODUCT_SALE.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                } else {
                    itemDTO.setTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_TEAM.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                }
                itemDTO.setBrandTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_GROUP_BRAND.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setEffectTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_GROUP_EFFECT.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setPickupTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_GROUP_UP.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.ESTIMATE_AMOUNT_SALE_GROUP.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_GROUP_ESTIMATE_BRAND.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setEffectConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_GROUP_ESTIMATE_EFFECT.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setPickupConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_GROUP_ESTIMATE_UP.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandConfigToBeCompletedAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.OFFSET_BRAND_QUARTER_TO_BE_CHECKED.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandBsiOffsetEstimate(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.OFFSET_BRAND_BSI_ESTIMATE.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandMarketCenterEstimate(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.OFFSET_BRAND_MARKET_CENTER.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setEffectMarketCenterEstimate(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.OFFSET_EFFECT_BUSINESS_ESTIMATE_CHECKED.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setPickUpMarketCenterEstimate(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.OFFSET_PICKUP_BUSINESS_ESTIMATE_CHECKED.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
            } else if (Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.SALE.getBizId()) || Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.SPECIAL_SALE.getBizId())) {
                itemDTO.setTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_BRAND.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setEffectTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_EFFECT.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setPickupTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_UP.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.ESTIMATE_AMOUNT_SALE.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_ESTIMATE_BRAND.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setEffectConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_ESTIMATE_EFFECT.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setPickupConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_ESTIMATE_UP.getBizId() + "_" + itemDTO.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                if (CollectionUtils.isNotEmpty(saleTargetGroupMap.get(itemDTO.getBizId()))) {
                    Long haveAuthTotalTaskAmount = saleTargetGroupMap.get(itemDTO.getBizId()).stream()
                            .map(u -> kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_TEAM.getBizId() + "_" + u, CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi()).reduce(Long::sum).orElse(0L);
                    itemDTO.setHaveAuthTotalTaskAmount(haveAuthTotalTaskAmount);
                }
            } else if (Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.DIRECT_MERGE_OTHER.getBizId())) {
                itemDTO.setTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.DIRECT_OTHER_TASK.getBizId() + "_" + SaleKpiTypeEnum.DIRECT_MERGE_OTHER.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.DIRECT_OTHER_BIZ_ESTIMATE.getBizId() + "_" + SaleKpiTypeEnum.DIRECT_MERGE_OTHER.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandTotalTaskAmount(0L);
                itemDTO.setEffectTotalTaskAmount(0L);
                itemDTO.setPickupTotalTaskAmount(0L);
                itemDTO.setBrandConfigBusinessEstimateAmount(0L);
                itemDTO.setEffectConfigBusinessEstimateAmount(0L);
                itemDTO.setPickupConfigBusinessEstimateAmount(0L);
            } else if (Objects.equals(itemDTO.getBizType(), SaleKpiTypeEnum.CHANNEL_MERGE_OTHER.getBizId())) {
                itemDTO.setTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.CHANNEL_OTHER_TASK.getBizId() + "_" + SaleKpiTypeEnum.CHANNEL_MERGE_OTHER.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setConfigBusinessEstimateAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.CHANNEL_OTHER_BIZ_ESTIMATE.getBizId() + "_" + SaleKpiTypeEnum.CHANNEL_MERGE_OTHER.getBizId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
                itemDTO.setBrandTotalTaskAmount(0L);
                itemDTO.setEffectTotalTaskAmount(0L);
                itemDTO.setPickupTotalTaskAmount(0L);
                itemDTO.setBrandConfigBusinessEstimateAmount(0L);
                itemDTO.setEffectConfigBusinessEstimateAmount(0L);
                itemDTO.setPickupConfigBusinessEstimateAmount(0L);
            } else {
                itemDTO.setTotalTaskAmount(0L);
                itemDTO.setBrandTotalTaskAmount(0L);
                itemDTO.setEffectTotalTaskAmount(0L);
                itemDTO.setPickupTotalTaskAmount(0L);
                itemDTO.setConfigBusinessEstimateAmount(0L);
                itemDTO.setBrandConfigBusinessEstimateAmount(0L);
                itemDTO.setEffectConfigBusinessEstimateAmount(0L);
                itemDTO.setPickupConfigBusinessEstimateAmount(0L);
            }
            if (!Utils.isPositive(itemDTO.getHaveAuthTotalTaskAmount())) {
                itemDTO.setHaveAuthTotalTaskAmount(itemDTO.getTotalTaskAmount());
            }
            //一些完成率
            itemDTO.setBrandCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getBrandCompletedTaskAmount(), itemDTO.getBrandTotalTaskAmount()));
            itemDTO.setEffectCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getEffectCompletedTaskAmount(), itemDTO.getEffectTotalTaskAmount()));
            itemDTO.setPickupCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getPickupCompletedTaskAmount(), itemDTO.getPickupTotalTaskAmount()));
            itemDTO.setCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getCompletedTaskAmount(), itemDTO.getTotalTaskAmount()));
            itemDTO.setBrandBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getBrandConfigBusinessEstimateAmount(), itemDTO.getBrandTotalTaskAmount()));
            itemDTO.setEffectBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getEffectConfigBusinessEstimateAmount(), itemDTO.getEffectTotalTaskAmount()));
            itemDTO.setPickupBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getPickupConfigBusinessEstimateAmount(), itemDTO.getPickupTotalTaskAmount()));
            itemDTO.setBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getConfigBusinessEstimateAmount(), itemDTO.getTotalTaskAmount()));
            itemDTO.setBrandSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getBrandSevenToQuarterEstimateAmount(), itemDTO.getBrandTotalTaskAmount()));
            itemDTO.setEffectSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getEffectSevenToQuarterEstimateAmount(), itemDTO.getEffectTotalTaskAmount()));
            itemDTO.setPickupSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getPickupSevenToQuarterEstimateAmount(), itemDTO.getPickupTotalTaskAmount()));
            itemDTO.setAllSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getAllSevenToQuarterEstimateAmount(), itemDTO.getTotalTaskAmount()));
            dtoList.add(itemDTO);
        });
        return dtoList;
    }


    public List<CrmAchievementDayItemDTO> queryCrmAchievementDayItemPoList(AchievementEstimateHiveCkQueryDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            return new ArrayList<>();
        }
        CrmAchievementDayItemPoExample poExample = buildQueryItemExample(queryDTO);
        AlarmHelper.log("queryCrmAchievementDayItemPoList", queryDTO);
        List<CrmAchievementDayItemPo> poList = crmAchievementDayItemDao.selectByExample(poExample);
        List<CrmAchievementDayItemDTO> itemDTOList = buildDtoForItemPoListWithKpiTarget(poList, queryDTO);
        buildDtoForItemPoListWithProductDetail(itemDTOList, queryDTO);
        return itemDTOList;
    }

    public void deleteAndInsertItemPoList(List<CrmAchievementDayItemDTO> poList, Timestamp aggDate, String env) {
        AlarmHelper.log("deleteAndInsertItemPoList", poList.size(), aggDate, env);
        CrmAchievementDayItemPoExample dayItemPoExample = new CrmAchievementDayItemPoExample();
        dayItemPoExample.createCriteria().andAggTimeEqualTo(aggDate).andDataEnvEqualTo(env);
        int cnt = crmAchievementDayItemDao.deleteByExample(dayItemPoExample);
        AlarmHelper.log("crmAchievementDayItemDao_del", cnt);
        CrmAchievementDayByTypePoExample dayByTypePoExample = new CrmAchievementDayByTypePoExample();
        dayByTypePoExample.createCriteria().andAggTimeEqualTo(aggDate).andDataEnvEqualTo(env);
        crmAchievementDayByTypeDao.deleteByExample(dayByTypePoExample);
        AlarmHelper.log("crmAchievementDayByTypeDao_del", cnt);
        poList.forEach(dto -> {
            CrmAchievementDayItemPo p1 = new CrmAchievementDayItemPo();
            BeanUtils.copyProperties(dto, p1);
            crmAchievementDayItemDao.insertSelective(p1);
            if (CollectionUtils.isNotEmpty(dto.getDayByTypePoList())) {
                for (CrmAchievementDayByTypePo p2 : dto.getDayByTypePoList()) {
                    p2.setItemId(p1.getId());
                    crmAchievementDayByTypeDao.insertSelective(p2);
                }
            }
        });
        AlarmHelper.log("crmAchievementDayItemDao_insert", cnt);
        AlarmHelper.log("crmAchievementDayByTypeDao_insert", cnt);
    }

    public CrmAchievementDayItemPoExample buildQueryItemExample(AchievementEstimateHiveCkQueryDTO queryDTO) {
        CrmAchievementDayItemPoExample poExample = new CrmAchievementDayItemPoExample();
        if (CollectionUtils.isNotEmpty(queryDTO.getParentIdList())) {
            buildCommonCondition(poExample, queryDTO).andBizParentIdIn(queryDTO.getParentIdList());
        }
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleIdList())) {
            buildCommonCondition(poExample, queryDTO).andBizIdIn(queryDTO.getSaleIdList()).andBizTypeEqualTo(SaleKpiTypeEnum.SALE.getBizId());
        }
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleGroupIdList())) {
            buildCommonCondition(poExample, queryDTO).andBizIdIn(queryDTO.getSaleGroupIdList()).andBizTypeEqualTo(SaleKpiTypeEnum.SALE_TEAM.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryBiliBili(), Boolean.TRUE)) {
            buildCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.BILIBILI.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryBiHuo(), Boolean.TRUE)) {
            buildCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.PERSON_FLY.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryDirectOther(), Boolean.TRUE)) {
            buildCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.DIRECT_MERGE_OTHER.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryChannelOther(), Boolean.TRUE)) {
            buildCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.CHANNEL_MERGE_OTHER.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryChannelNoAssign(), Boolean.TRUE)) {
            buildCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.CHANNEL_NO_ASSIGN.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryAll(), Boolean.TRUE)) {
            buildCommonCondition(poExample, queryDTO);
        }
        if (Objects.nonNull(queryDTO.getSaleIdNeedSummary())) {
            buildCommonCondition(poExample, queryDTO)
                    .andBizTypeEqualTo(SaleKpiTypeEnum.SPECIAL_SALE.getBizId())
                    .andBizIdEqualTo(queryDTO.getSaleIdNeedSummary());
        }
        return poExample;
    }

    public CrmAchievementDayItemPoExample.Criteria buildCommonCondition(CrmAchievementDayItemPoExample poExample, AchievementEstimateHiveCkQueryDTO queryDTO) {
        Timestamp beginAggTime = Utils.getBeginOfDay(queryDTO.getAggTime());
        return poExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAggTimeEqualTo(beginAggTime).andDataEnvEqualTo(env);
    }


    public void deleteAndInsertEveryDayPoList(List<CrmAchievementEveryDayPo> poList, String env) {
        CrmAchievementEveryDayPoExample dayItemPoExample = new CrmAchievementEveryDayPoExample();
        dayItemPoExample.createCriteria().andDataEnvEqualTo(env);
        crmAchievementEveryDayDao.deleteByExample(dayItemPoExample);
        long startTime = System.currentTimeMillis();
        ThreadPoolUtils.asyncExecuteVoid(7, poList, partPoList -> {
            List<List<CrmAchievementEveryDayPo>> partitions = Lists.partition(partPoList, 6000);
            partitions.forEach(one -> {
                one.forEach(p -> {
                            p.setBizName(Optional.ofNullable(p.getBizName()).orElse(""));
                            p.setBizId(Optional.ofNullable(p.getBizId()).orElse(0));
                            p.setDirectOrChannel(Optional.ofNullable(p.getDirectOrChannel()).orElse(0));
                            p.setGroupId(Optional.ofNullable(p.getGroupId()).orElse(0));
                            p.setGroupName(Optional.ofNullable(p.getGroupName()).orElse(""));
                            p.setCompletedTaskAmount(Optional.ofNullable(p.getCompletedTaskAmount()).orElse(0L));
                            p.setBrandCompletedTaskAmount(Optional.ofNullable(p.getBrandCompletedTaskAmount()).orElse(0L));
                            p.setEffectCompletedTaskAmount(Optional.ofNullable(p.getEffectCompletedTaskAmount()).orElse(0L));
                            p.setPickupCompletedTaskAmount(Optional.ofNullable(p.getPickupCompletedTaskAmount()).orElse(0L));
                            p.setAllDirectSaleNameStr(Optional.ofNullable(p.getAllDirectSaleNameStr()).orElse(""));
                            p.setAllChannelSaleNameStr(Optional.ofNullable(p.getAllChannelSaleNameStr()).orElse(""));
                            p.setAllDirectSaleGroupNameStr(Optional.ofNullable(p.getAllDirectSaleGroupNameStr()).orElse(""));
                            p.setAllChannelSaleGroupNameStr(Optional.ofNullable(p.getAllChannelSaleGroupNameStr()).orElse(""));
                            p.setAllDirectSeaGroupNameStr(Optional.ofNullable(p.getAllDirectSeaGroupNameStr()).orElse(""));
                            p.setBrandDirectSaleNameStr(Optional.ofNullable(p.getBrandDirectSaleNameStr()).orElse(""));
                            p.setBrandChannelSaleNameStr(Optional.ofNullable(p.getBrandChannelSaleNameStr()).orElse(""));
                            p.setBrandDirectSaleGroupNameStr(Optional.ofNullable(p.getBrandDirectSaleGroupNameStr()).orElse(""));
                            p.setBrandChannelSaleGroupNameStr(Optional.ofNullable(p.getBrandChannelSaleGroupNameStr()).orElse(""));
                            p.setBrandDirectSeaGroupNameStr(Optional.ofNullable(p.getBrandDirectSeaGroupNameStr()).orElse(""));
                            p.setEffectDirectSaleNameStr(Optional.ofNullable(p.getEffectDirectSaleNameStr()).orElse(""));
                            p.setEffectChannelSaleNameStr(Optional.ofNullable(p.getEffectChannelSaleNameStr()).orElse(""));
                            p.setEffectDirectSaleGroupNameStr(Optional.ofNullable(p.getEffectDirectSaleGroupNameStr()).orElse(""));
                            p.setEffectChannelSaleGroupNameStr(Optional.ofNullable(p.getEffectChannelSaleGroupNameStr()).orElse(""));
                            p.setEffectDirectSeaGroupNameStr(Optional.ofNullable(p.getEffectDirectSeaGroupNameStr()).orElse(""));
                            p.setPickupDirectSaleNameStr(Optional.ofNullable(p.getPickupDirectSaleNameStr()).orElse(""));
                            p.setPickupChannelSaleNameStr(Optional.ofNullable(p.getPickupChannelSaleNameStr()).orElse(""));
                            p.setPickupDirectSaleGroupNameStr(Optional.ofNullable(p.getPickupDirectSaleGroupNameStr()).orElse(""));
                            p.setPickupChannelSaleGroupNameStr(Optional.ofNullable(p.getPickupChannelSaleGroupNameStr()).orElse(""));
                            p.setPickupDirectSeaGroupNameStr(Optional.ofNullable(p.getPickupDirectSeaGroupNameStr()).orElse(""));
                            p.setDataEnv(Optional.ofNullable(p.getDataEnv()).orElse("prd"));
                        }
                );
                crmAchievementEveryDayExtendDao.batchInsertByPoList(one);
            });
        });
    }

    public List<CrmAchievementEveryDayExtendPo> queryCrmAchievementEveryDayPoTenList(AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            return new ArrayList<>();
        }
        Assert.isTrue(Objects.nonNull(queryDTO.getLogDate()), "统计周期不能为空");
        Assert.isTrue(Objects.nonNull(queryDTO.getAnalysisBeginDate()), "分析周期开始时间不能为空");
        Assert.isTrue(Objects.nonNull(queryDTO.getAnalysisEndDate()), "分析周期结束时间不能为空");
        return crmAchievementEveryDayExtendDao.selectListGroupForBizIdAndType(queryDTO.getBizId(),
                queryDTO.getBizType(),
                queryDTO.getCompleteType(),
                queryDTO.getAnalysisBeginDate(),
                queryDTO.getAnalysisEndDate(),
                env
        );
    }


    public List<CrmAchievementEveryDayPo> queryCrmAchievementEveryDayPoAllList(AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            return new ArrayList<>();
        }
        Assert.isTrue(Objects.nonNull(queryDTO.getAnalysisBeginDate()), "分析周期开始时间不能为空");
        Assert.isTrue(Objects.nonNull(queryDTO.getAnalysisEndDate()), "分析周期结束时间不能为空");
        CrmAchievementEveryDayPoExample poExample = buildQueryEveryDayExample(queryDTO);
        return crmAchievementEveryDayDao.selectByExample(poExample);
    }

    public CrmAchievementEveryDayPoExample buildQueryEveryDayExample(AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        CrmAchievementEveryDayPoExample poExample = new CrmAchievementEveryDayPoExample();
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleIdList())) {
            buildEveryDayCommonCondition(poExample, queryDTO).andBizIdIn(queryDTO.getSaleIdList()).andBizTypeEqualTo(SaleKpiTypeEnum.SALE.getBizId());
        }
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleGroupIdList())) {
            buildEveryDayCommonCondition(poExample, queryDTO).andBizIdIn(queryDTO.getSaleGroupIdList()).andBizTypeEqualTo(SaleKpiTypeEnum.SALE_TEAM.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryBiliBili(), Boolean.TRUE)) {
            buildEveryDayCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.BILIBILI.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryDirectOther(), Boolean.TRUE)) {
            buildEveryDayCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.DIRECT_MERGE_OTHER.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryChannelOther(), Boolean.TRUE)) {
            buildEveryDayCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.CHANNEL_MERGE_OTHER.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryChannelNoAssign(), Boolean.TRUE)) {
            buildEveryDayCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.CHANNEL_NO_ASSIGN.getBizId());
        }
        return poExample;
    }

    public CrmAchievementEveryDayPoExample.Criteria buildEveryDayCommonCondition(CrmAchievementEveryDayPoExample poExample, AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        return poExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAggTimeGreaterThanOrEqualTo(queryDTO.getAnalysisBeginDate())
                .andAggTimeLessThanOrEqualTo(queryDTO.getAnalysisEndDate())
                .andGroupIdEqualTo(0).andDataEnvEqualTo(env);
    }

    public Map<String, List<Integer>> queryAllHaveAuthSonSaleIdsBySaleId(Integer saleId) {
        return achievementEstimateBizService.queryRealTimeAllHaveAuthSonSaleIdsBySaleId(saleId);
    }

    public void deleteAndInsertCardDtoList(List<CrmAchievementCardItemDTO> dtoList, Timestamp aggDate, Timestamp aggBeginDate, Timestamp aggEndDate, String env) {
        aggDate = Utils.getBeginOfDay(aggDate);
        CrmAchievementCardPoExample dayItemPoExample = new CrmAchievementCardPoExample();
        CrmAchievementCardPoExample.Criteria criteria = dayItemPoExample.createCriteria().andAggLogTimeEqualTo(aggDate).andDataEnvEqualTo(env);
        if (Objects.nonNull(aggBeginDate)) {
            criteria.andAggTimeStartEqualTo(aggBeginDate);
        }
        if (Objects.nonNull(aggEndDate)) {
            criteria.andAggTimeEndEqualTo(aggEndDate);
        }
        crmAchievementCardDao.deleteByExample(dayItemPoExample);
        ThreadPoolUtils.asyncExecuteVoid(7, dtoList, subPoList -> subPoList.forEach(dto -> {
            CrmAchievementCardPo p = new CrmAchievementCardPo();
            BeanUtils.copyProperties(dto, p);
            p.setDataEnv(env);
            crmAchievementCardDao.insertSelective(p);
        }));
        //除了aggDate的上一天的数据 和季度末的数据 都删除
        List<Timestamp> quarterEndTimeList = CrmUtils.getQuarterEndTimeListAfterSomeDay(TIME_20240630);
        quarterEndTimeList.add(aggDate);
        quarterEndTimeList.add(Utils.getSomeDayAgo(aggDate, 1));
        quarterEndTimeList.add(Utils.getBeginOfDay(Utils.getYesteday()));
        CrmAchievementCardPoExample dayNotEndPoExample = new CrmAchievementCardPoExample();
        dayNotEndPoExample.createCriteria().andAggLogTimeNotIn(quarterEndTimeList).andDataEnvEqualTo(env);
        crmAchievementCardDao.deleteByExample(dayNotEndPoExample);
    }

    @ObjectParamCache(value = 3, timeUnit = TimeUnit.SECONDS)
    public List<CrmAchievementCardPo> queryCrmAchievementCardDtoAllList(AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        if (Objects.isNull(queryDTO)) {
            return new ArrayList<>();
        }
        Assert.isTrue(Objects.nonNull(queryDTO.getAnalysisBeginDate()), "分析周期开始时间不能为空");
        Assert.isTrue(Objects.nonNull(queryDTO.getAnalysisEndDate()), "分析周期结束时间不能为空");
        if (!StringUtils.isEmpty(queryDTO.getAnalysisBeginDate())) {
            int days = CrmUtils.getIntervalDays(queryDTO.getAnalysisBeginDate(), queryDTO.getAnalysisEndDate());
            Assert.isTrue(days <= 92, "计收时间间隔不能大于3个月");
        }
        AlarmHelper.log("QueryCard", queryDTO);
        CrmAchievementCardPoExample poExample = buildQueryCardExample(queryDTO);
        return crmAchievementCardDao.selectByExample(poExample);
    }

    public CrmAchievementCardPoExample buildQueryCardExample(AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        CrmAchievementCardPoExample poExample = new CrmAchievementCardPoExample();
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleIdList())) {
            buildCardCommonCondition(poExample, queryDTO).andBizIdIn(queryDTO.getSaleIdList()).andBizTypeEqualTo(SaleKpiTypeEnum.SALE.getBizId());
        }
        if (CollectionUtils.isNotEmpty(queryDTO.getSaleGroupIdList())) {
            buildCardCommonCondition(poExample, queryDTO).andBizIdIn(queryDTO.getSaleGroupIdList()).andBizTypeEqualTo(SaleKpiTypeEnum.SALE_TEAM.getBizId());
        }
        if (Objects.equals(queryDTO.getQueryBiliBili(), Boolean.TRUE)) {
            buildCardCommonCondition(poExample, queryDTO).andBizTypeEqualTo(SaleKpiTypeEnum.BILIBILI.getBizId());
        }
        return poExample;
    }

    public CrmAchievementCardPoExample.Criteria buildCardCommonCondition(CrmAchievementCardPoExample poExample, AchievementEstimateHiveCkQueryListByGroupDTO queryDTO) {
        return poExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAggTimeStartEqualTo(queryDTO.getAnalysisBeginDate())
                .andAggTimeEndEqualTo(queryDTO.getAnalysisEndDate())
                .andSaleAuthDataEqualTo(queryDTO.getSaleAuthData())
                .andAggLogTimeEqualTo(Utils.getBeginOfDay(queryDTO.getLogDate())).andDataEnvEqualTo(env);
    }

    public List<AdsCrmCommerceKpiWithSaleInfoDetailADPO> queryDetailListForCKByCondition(AchievementEstimateHiveCkDetailQueryParam queryParam) {
        Assert.isTrue(Objects.nonNull(queryParam.getQueryDetailListType()), "查询类型不能为空");
        convertLogDate(queryParam);
        if (!StringUtils.isEmpty(queryParam.getBusiAccountDateStart())) {
            int days = CrmUtils.getIntervalDays(CrmUtils.parseTimestamp(queryParam.getBusiAccountDateStart(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT), CrmUtils.parseTimestamp(queryParam.getBusiAccountDateEnd(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
            Assert.isTrue(days <= 92, "计收时间间隔不能大于3个月");
        }
        if (!StringUtils.isEmpty(queryParam.getAmountDateStart())) {
            int days = CrmUtils.getIntervalDays(CrmUtils.parseTimestamp(queryParam.getAmountDateStart(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT), CrmUtils.parseTimestamp(queryParam.getAmountDateEnd(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
            Assert.isTrue(days <= 92, "执行时间间隔不能大于3个月");
        }
        AlarmHelper.log("queryDetailListForCKByCondition", queryParam);
        return adsCrmCommerceKpiWithSaleAmountDataADDao.queryDetailListDataByCondition(queryParam);
    }


    public List<AdsCrmCommerceKpiWithSaleInfoExportADPO> exportDetailListForCKByCondition(AchievementEstimateHiveCkDetailQueryParam queryParam) {
        convertLogDate(queryParam);
        if (!StringUtils.isEmpty(queryParam.getBusiAccountDateStart())) {
            int days = CrmUtils.getIntervalDays(CrmUtils.parseTimestamp(queryParam.getBusiAccountDateStart(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT), CrmUtils.parseTimestamp(queryParam.getBusiAccountDateEnd(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
            Assert.isTrue(days <= 92, "计收时间间隔不能大于3个月");
        }
        log.info("exportDetailListForCKByCondition,queryParam={}", JSON.toJSONString(queryParam));
        return adsCrmCommerceKpiWithSaleAmountDataADDao.exportDetailListForCKByCondition(queryParam);
    }
    public Map<String, List<Integer>> queryAllHaveAuthSonSaleIdsBySaleIdWithLogDate(Integer saleId, String logDateStr) {
        return achievementEstimateBizService.queryAllHaveAuthSonSaleIdsBySaleIdWithLogDate(saleId, logDateStr);
    }

    public Map<String, List<Integer>> queryAllHaveAuthSonSaleIdsBySaleGroupIdWithLogDate(List<Integer> saleGroupIdList, String logDateStr) {
        return achievementEstimateBizService.queryAllHaveAuthSonSaleIdsBySaleGroupIdWithLogDate(saleGroupIdList, logDateStr);
    }

    /**
     * 获取销售组和他下属组所有的销售id不包括被封禁的 没有包含副组长
     */
    public Map<Integer, List<SaleInfoWithHiveDTO>> getAllValidSaleBaseDtoMapByGroupIdListForLogStr(List<Integer> saleGroupIdList, String logDateStr) {
        //logDateStr = "yyyyMMdd"
        Assert.isTrue(CollectionUtils.isNotEmpty(saleGroupIdList), "销售组id不能为空");
        Assert.isTrue(Objects.nonNull(logDateStr), "logDate不能为空");
        Map<Integer, SaleInfoWithHiveDTO> allSaleMap = iSaleService.getAllSaleMapForHive(logDateStr);
        List<SaleInfoWithHiveDTO> allSaleList = new ArrayList<>(allSaleMap.values());
        Map<Integer, SaleGroupInfoWithHiveDTO> allSaleGroupMap = iSaleGroupService.getAllGroupMapForHive(logDateStr);
        List<SaleGroupInfoWithHiveDTO> SaleGroupDtoList = new ArrayList<>(allSaleGroupMap.values());
        List<Integer> filterSaleGroupList = saleGroupIdList.stream().filter(allSaleGroupMap::containsKey).collect(Collectors.toList());
        Map<Integer, List<SaleInfoWithHiveDTO>> result = new HashMap<>();
        filterSaleGroupList.forEach(groupId -> {
            List<Integer> childGroupIds = filterAllChildGroupIdListByParentIdListForCK(SaleGroupDtoList, Collections.singletonList(String.valueOf(groupId))).stream().map(Integer::valueOf).collect(Collectors.toList());
            childGroupIds.add(groupId);
            List<SaleInfoWithHiveDTO> sonSaleDtos = allSaleList.stream()
                    .filter(t -> Objects.equals(t.getStatus(), SaleTypeStatus.VALID.getCode()))
                    .filter(t -> ListUtils.containsAnyInt(childGroupIds, t.getSaleGroupIdsInt()))
                    .collect(Collectors.toList());
            if (Objects.equals(groupId, DIRECT_GROUP_ID)) {
                List<Integer> pmGroupIds = filterAllChildGroupIdListByParentIdListForCK(SaleGroupDtoList, Collections.singletonList(String.valueOf(PM_GROUP_ID))).stream().map(Integer::valueOf).collect(Collectors.toList());
                pmGroupIds.add(PM_GROUP_ID);
                List<Integer> pmSonSaleIds = allSaleList.stream()
                        .filter(t -> Objects.equals(t.getStatus(), SaleTypeStatus.VALID.getCode()))
                        .filter(t-> ListUtils.containsAnyInt(pmGroupIds, t.getSaleGroupIdsInt()))
                        .map(SaleInfoWithHiveDTO::getId)
                        .collect(Collectors.toList());
                sonSaleDtos = sonSaleDtos.stream().filter(t -> !pmSonSaleIds.contains(t.getId())).collect(Collectors.toList());
            }
            result.put(groupId, sonSaleDtos);
        });
        return result;
    }
    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryAllHaveSaleKpiListByCondition(String logDate, String beginTime, String endTime) {
        AdsCrmCommerceKpiWithSaleAmountDataADQueryParam param = new AdsCrmCommerceKpiWithSaleAmountDataADQueryParam();
        param.setLogDate(logDate);
        param.setBusAccountDateBegin(beginTime);
        param.setBusAccountDateEnd(endTime);
        AlarmHelper.log("QueryAllHaveSaleKpiListByCondition", param);
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectAdsCrmCommerceKpiWithSaleAmountDataADList(param);
        long endQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryAllHaveSaleKpiListByCondition", "耗时",endQueryTime - startQueryTime, "结果集数量", poList.size());
        return poList;
    }

    //品牌 历史已交付未关账数据
    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryBrandNotCurrentQuarterCloseList(String logDate, String beginQuarterTime) {
        AlarmHelper.log("queryBrandNotCurrentQuarterCloseList", "logDate", logDate, "beginQuarterTime", beginQuarterTime);
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectBrandNotCurrentQuarterCloseList(logDate, beginQuarterTime);
        long endQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryBrandNotCurrentQuarterCloseList", "耗时", endQueryTime - startQueryTime, "结果集数量", poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryBrandNotCurrentQuarterCloseListWithProductType(String logDate, String beginQuarterTime) {
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectBrandNotCurrentQuarterCloseListWithProductType(logDate, beginQuarterTime);
        long endQueryTime = System.currentTimeMillis();
        log.info("queryBrandNotCurrentQuarterCloseListWithProductType，查询耗时:{} 单位ms,结果数量:{}", endQueryTime - startQueryTime, poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryBrandLastQuarterCompletedWaitDeliveryList(String logDate, String beginQuarterTime, String endQuarterTime) {
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectBrandLastQuarterCompletedWaitDeliveryList(logDate, beginQuarterTime, endQuarterTime);
        long endQueryTime = System.currentTimeMillis();
        log.info("queryBrandLastQuarterCompletedWaitDeliveryList，查询耗时:{} 单位ms,结果数量:{}", endQueryTime - startQueryTime, poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryBrandQuarterWaitCompleteList(String logDate, String beginTime, String endTime) {
        AlarmHelper.log("queryBrandQuarterWaitCompleteList", "logDate", logDate, "beginTime", beginTime, "endTime", endTime);
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectBrandQuarterWaitCompleteList(logDate, beginTime, endTime);
        long endQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryBrandQuarterWaitCompleteList", "耗时", endQueryTime - startQueryTime, "结果集数量", poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryPickUpQuarterWaitCompleteList(String logDate, String beginTime, String endTime) {
        AlarmHelper.log("queryPickUpQuarterWaitCompleteList", "logDate", logDate, "beginTime", beginTime, "endTime", endTime);
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectPickUpQuarterWaitCompleteList(logDate, beginTime, endTime);
        long endQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryPickUpQuarterWaitCompleteList", "耗时", endQueryTime - startQueryTime, "结果集数量", poList.size());
        return poList;
    }

    //花火 历史已交付未关账数据
    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryPickUpNotCurrentQuarterCloseList(String logDate, String beginQuarterTime) {
        AlarmHelper.log("queryPickUpNotCurrentQuarterCloseList", "logDate", logDate, "beginQuarterTime", beginQuarterTime);
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectPickUpNotCurrentQuarterCloseList(logDate, beginQuarterTime);
        long endQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryPickUpNotCurrentQuarterCloseList", "耗时", endQueryTime - startQueryTime, "结果集数量", poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryCompleteDataWithTime(String logDate, String beginTime, String endTime) {
        long startQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryCompleteDataWithTime", "logDate", logDate, "beginTime", beginTime, "endTime", endTime);
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectCompleteDataWithTime(logDate, beginTime, endTime);
        long endQueryTime = System.currentTimeMillis();
        AlarmHelper.log("queryCompleteDataWithTime", "耗时", endQueryTime - startQueryTime, "结果集数量", poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountRealDataADPO> queryCompleteDataWithTimeForSaleId(String logDate, String beginTime,
                                                                                                String endTime, String directSaleId, String channelSaleId) {
        Assert.isTrue(Objects.isNull(directSaleId) || Objects.isNull(channelSaleId), "销售id只能为一个");
        Assert.isTrue(Objects.nonNull(directSaleId) || Objects.nonNull(channelSaleId), "销售id不能为空");
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountRealDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.queryCompleteDataWithTimeForSaleId(logDate, beginTime,
                endTime, directSaleId, channelSaleId);
        long endQueryTime = System.currentTimeMillis();
        log.info("queryCompleteDataWithTimeForSaleId，查询耗时:{} 单位ms,结果数量:{}", endQueryTime - startQueryTime, poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountRealDataADPO> queryBeforeCompleteDataWithTimeForSaleId(String logDate, String beginTime,
                                                                                                String endTime, String directSaleId, String channelSaleId) {
        Assert.isTrue(Objects.isNull(directSaleId) || Objects.isNull(channelSaleId), "销售id只能为一个");
        Assert.isTrue(Objects.nonNull(directSaleId) || Objects.nonNull(channelSaleId), "销售id不能为空");
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountRealDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.queryBeforeCompleteDataWithTimeForSaleId(logDate, beginTime,
                endTime, directSaleId, channelSaleId);
        long endQueryTime = System.currentTimeMillis();
        log.info("queryBeforeCompleteDataWithTimeForSaleId，查询耗时:{} 单位ms,结果数量:{}", endQueryTime - startQueryTime, poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountDataADPO> queryCompleteDataWithTimeForBrushBack(String logDate, String beginTime, String endTime) {
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.selectCompleteDataWithTimeForBrushBack(logDate, beginTime, endTime);
        long endQueryTime = System.currentTimeMillis();
        log.info("queryCompleteDataWithTimeForBrushBack，查询耗时:{} 单位ms,结果数量:{}", endQueryTime - startQueryTime, poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleInfoExportDataADPO> exportCompleteInfoDataWithTimeAndCondition(AchievementEstimateHiveCkCompleteExportQueryDTO exportQueryDTO) {
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleInfoExportDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.exportCompleteInfoDataWithTimeAndCondition(exportQueryDTO.getLogDate(),
                exportQueryDTO.getBeginTime(),
                exportQueryDTO.getEndTime(),
                exportQueryDTO.getQueryType(),
                exportQueryDTO.getCompleteType(),
                exportQueryDTO.getDirectSaleIdOrList(),
                exportQueryDTO.getDirectSeaGroupIdOrList(),
                exportQueryDTO.getChannelSaleIdOrList(),
                exportQueryDTO.getFlashBack());
        long endQueryTime = System.currentTimeMillis();
        log.info("exportCompleteInfoDataWithTimeAndCondition，查询耗时:{} 单位ms,结果数量:{}", endQueryTime - startQueryTime, poList.size());
        return poList;
    }

    public List<AdsCrmCommerceKpiWithSaleAmountExportDataADPO> exportCompleteAmountDataWithTimeAndCondition(AchievementEstimateHiveCkCompleteExportQueryDTO exportQueryDTO) {
        long startQueryTime = System.currentTimeMillis();
        List<AdsCrmCommerceKpiWithSaleAmountExportDataADPO> poList = adsCrmCommerceKpiWithSaleAmountDataADDao.exportCompleteAmountDataWithTimeAndCondition(exportQueryDTO.getLogDate(),
                exportQueryDTO.getBeginTime(),
                exportQueryDTO.getEndTime(),
                exportQueryDTO.getQueryType(),
                exportQueryDTO.getCompleteType(),
                exportQueryDTO.getDirectSaleIdOrList(),
                exportQueryDTO.getDirectSeaGroupIdOrList(),
                exportQueryDTO.getChannelSaleIdOrList(),
                exportQueryDTO.getFlashBack());
        AlarmHelper.log("exportCompleteAmountDataWithTimeAndCondition", exportQueryDTO, System.currentTimeMillis() - startQueryTime, poList.size());
        return poList;
    }


    public AdsCrmCommerceKpiTotalInfoPO calculateKpiAmountInfoForOperationIdList(AchievementEstimateHiveCkDetailQueryParam queryParam) {
        return adsCrmCommerceKpiWithSaleAmountDataADDao.calculateKpiAmountInfoForOperationIdList(queryParam);
    }

    public boolean ckTodayDataIsReady(Timestamp logDate) {
        Boolean result = crmKpiCkPrepareService.judgePrepareDataInfoForDB(logDate, "sycpb.ads_crm_commerce_kpi_amount_data_with_sales_a_d");
        if (Objects.equals(env, "prd")) {
            Object cash = cacheManager.getValue(HAVE_SALE_YJ_BIG_CK_TABLE);
            Timestamp nowBeginTime = Utils.getBeginOfDay(Utils.getNow());
            Timestamp yesterdayBeginTime = Utils.getBeginOfDay(Utils.getYesteday());
            LocalDateTime now = LocalDateTime.now();
            LocalDateTime todayEightAM = LocalDateTime.of(now.toLocalDate(), LocalTime.of(8, 0));
            if (Objects.isNull(cash) && !result && logDate.before(nowBeginTime) && (logDate.after(yesterdayBeginTime) || logDate.equals(yesterdayBeginTime)) && now.isAfter(todayEightAM)) {
                cacheManager.setValueWithTime(HAVE_SALE_YJ_BIG_CK_TABLE, 1, TimeUnit.DAYS, 1);
                WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                        .msgtype(MsgType.TEXT.getType())
                        .text(new Text("工程端检测到含有销售的业绩宽表logDate=" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD_WITHOUT_SPLIT) + "的CK表延迟产出"))
                        .build();
                wxRobotService.sendWeChatWork(wxRobotMsg, "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=080d5444-224a-4539-bc32-e63e7cea2ef2");
            }
        }
        return result;
    }

    public List<BsiOpportunityDto> queryBsiListForCKWithQuarter(String logDate, String beginQuarterTime, String endQuarterTime) {
        BsiOpportunityCKQueryDTO queryDTO = new BsiOpportunityCKQueryDTO();
        queryDTO.setLogDate(logDate);
        queryDTO.setFollowStageList(FollowStage.getTenAndNinetyStatus());
        queryDTO.setExpectLaunchMinDate(beginQuarterTime);
        queryDTO.setExpectLaunchMaxDate(endQuarterTime);
        AlarmHelper.log("queryBsiListForCKWithQuarter", queryDTO);
        long start = System.currentTimeMillis();
        List<BsiOpportunityDto> bsiDtoList = bsiOpportunityService.queryBsiOpportunityListForCK(queryDTO);
        AlarmHelper.log("queryBsiListForCKWithQuarter", "耗时", System.currentTimeMillis() - start, "结果数量", bsiDtoList.size());
        return bsiDtoList;
    }


    public List<Integer> filterAllChildGroupIdListByParentIdList(List<SaleGroupInfoWithHiveDTO> SaleGroupDtoList, List<Integer> groupIds) {
        List<Integer> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return result;
        }
        List<Integer> nextGroupIds = filterNextChildGroupIdListByParentIdList(SaleGroupDtoList, groupIds);
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            result.addAll(nextGroupIds);
            nextGroupIds = filterNextChildGroupIdListByParentIdList(SaleGroupDtoList, nextGroupIds);
        }
        return result;
    }


    public List<Integer> filterNextChildGroupIdListByParentIdList(List<SaleGroupInfoWithHiveDTO> SaleGroupDtoList, List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        return SaleGroupDtoList.stream()
                .filter(e -> groupIds.contains(e.getParentId()))
                .filter(e -> Objects.equals(e.getStatus(), SaleGroupStatus.VALID.getCode()))
                .map(SaleGroupInfoWithHiveDTO::getId)
                .collect(Collectors.toList());
    }

    public List<String> filterAllChildGroupIdListByParentIdListForCK(List<SaleGroupInfoWithHiveDTO> SaleGroupDtoList, List<String> groupIds) {
        List<String> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return result;
        }
        List<String> nextGroupIds = filterNextChildGroupIdListByParentIdListForCK(SaleGroupDtoList, groupIds);
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            result.addAll(nextGroupIds);
            nextGroupIds = filterNextChildGroupIdListByParentIdListForCK(SaleGroupDtoList, nextGroupIds);
        }
        return result;
    }


    public List<String> filterNextChildGroupIdListByParentIdListForCK(List<SaleGroupInfoWithHiveDTO> SaleGroupDtoList, List<String> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        return SaleGroupDtoList.stream().filter(e -> groupIds.contains(e.getSale_group_parent_id())).filter(e -> Objects.equals(e.getSale_group_status(), SaleGroupStatus.VALID.getCode().toString())).map(SaleGroupInfoWithHiveDTO::getSale_group_id).collect(Collectors.toList());
    }

    public Integer judgeDirectOrChannelForSaleGroup(Integer groupId, Map<Integer, SaleGroupInfoWithHiveDTO> allSaleGroupMap) {
        if (groupId == null || groupId == 0) {
            return 0;
        }
        List<SaleGroupInfoWithHiveDTO> salesGroupIdList = new ArrayList<>();
        Integer tempGroupId = groupId;
        while (Utils.isPositive(tempGroupId)) {
            SaleGroupInfoWithHiveDTO tempGroupDto = allSaleGroupMap.getOrDefault(tempGroupId, new SaleGroupInfoWithHiveDTO());
            salesGroupIdList.add(0, tempGroupDto);
            tempGroupId = tempGroupDto.getParentId();
        }
        Integer parentId = salesGroupIdList.size() > 0 ? salesGroupIdList.get(0).getId() : -1;
        return Objects.equals(parentId, DashBoardShowOrderEnum.DIRECT_SALE_GROUP.getId()) ? 1 : 2;
    }

    public void convertLogDate(AchievementEstimateHiveCkDetailQueryParam queryParam) {
        if (!StringUtils.isEmpty(achievementLogDateConfig)) {
            Map<String, String> logDateConfigMap = JSON.parseObject(achievementLogDateConfig, new TypeReference<Map<String, String>>() {
            });
            if (logDateConfigMap.containsKey(queryParam.getLogDate())) {
                queryParam.setLogDate(logDateConfigMap.get(queryParam.getLogDate()));
                queryParam.setCloseStatus(1);
            }
        }
    }

    public String putAchievementLogDateConfig() {
        return achievementLogDateConfig;
    }


}

