package com.bilibili.crm.platform.app.aop.aspect;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.white.IWxWriteListService;
import com.bilibili.crm.platform.api.white.dto.QueryWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteType;
import com.bilibili.crm.platform.app.exception.WxAppCrmExceptionCode;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.income_sale.manager.WxAppSaleHelper;
import com.bilibili.crm.platform.common.AccountStatus;
import com.bilibili.crm.platform.common.IsValid;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * @author: brady
 * @time: 2020/12/8 7:32 下午
 */
@Component
@Aspect
public class WxSaleRequestAspect {
    @Autowired
    private IWxWriteListService wxWriteListService;

    private final String WX_SALE_REQUEST = "execution(public * com.bilibili.crm.platform.portal.webapi.wxappsale.service.WebWxAppSaleIncomeService.*(..))";

    @Pointcut(value = WX_SALE_REQUEST)
    public void requestSalePointcut() {
    }

    /**
     * 销售切面
     *
     * @throws Throwable
     */
    @Before("requestSalePointcut()")
    public void requestMethodInvoke(JoinPoint pjp) throws Throwable {
        //强转待优化
        Object[] args = pjp.getArgs();
        Timestamp date = (Timestamp) args[0];
        Operator operator = (Operator) args[1];

        String username = operator.getOperatorName();
        //判断是否在白名单内，判断是否为销售管理员
        List<WxAppWhiteDto> wxAppWhiteDtos = wxWriteListService.queryWhite(QueryWxAppWhiteDto.builder()
                .whiteType(WxAppWhiteType.SALE.getCode())
                .userName(username)
                .build());
        if (CollectionUtils.isEmpty(wxAppWhiteDtos)) {
            throw new WxAppException(WxAppCrmExceptionCode.NO_DAILY_PAPER_AUTH);
        }
        if (IsValid.TRUE.getCode().equals(wxAppWhiteDtos.get(0).getIsManager()) && AccountStatus.ON.getCode().equals(wxAppWhiteDtos.get(0).getWhiteStatus())) {
            //operator.setOperatorName("manager");
            operator.setOperatorName(WxAppSaleHelper.SALE_MANAGER);
        }
    }

}
