package com.bilibili.crm.platform.app.api.service.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * 业绩监控查询DTO
 *
 * <AUTHOR>
 * date 2024/5/6 17:03.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchievementEstimateHiveCkQueryListByGroupDTO implements Serializable {

    private static final long serialVersionUID = -139508720153970234L;
    private Integer bizId;
    private Integer bizType;//SaleKpiTypeEnum
    private Timestamp logDate;
    private Integer saleAuthData;
    private Timestamp analysisBeginDate;
    private Timestamp analysisEndDate;
    private Integer completeType;
    private List<Integer> saleIdList;
    private List<Integer> saleGroupIdList;
    private Boolean queryBiliBili;
    private Boolean queryDirectOther;
    private Boolean queryChannelOther;
    private Boolean queryChannelNoAssign;
}

