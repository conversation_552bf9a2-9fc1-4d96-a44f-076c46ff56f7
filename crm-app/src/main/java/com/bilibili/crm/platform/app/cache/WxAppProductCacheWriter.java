package com.bilibili.crm.platform.app.cache;

import com.bilibili.crm.platform.api.dss.dto.DssCreativeCtrCompareDto;
import com.bilibili.crm.platform.api.income.dto.AdIncome;
import com.bilibili.crm.platform.api.income.dto.CtrType;
import com.bilibili.crm.platform.api.income.dto.ProductIncomeComposition;
import com.bilibili.crm.platform.api.income.dto.WxQuotaType;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.app.api.service.IWxAppPaperService;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.WxAppProductIncomeService;
import com.bilibili.crm.platform.app.cache.config.WxAppRequestKey;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.app.exception.WxAppCrmExceptionCode;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.income.config.product.ProductPvConfig;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeConfigHelper;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.dianping.cat.Cat;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

@Service
public class WxAppProductCacheWriter {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IWxAppPaperService wxAppPaperService;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private IncomeUtil incomeUtil;
    @Autowired
    private WxAppProductIncomeService wxAppProductIncomeService;
    @Autowired
    private IncomeConfigHelper incomeConfigHelper;
    @Autowired
    private ProductPvConfig productPvConfig;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private WxAppDataCheckService dataCheckService;

    private final static BigDecimal MILLION = new BigDecimal(1000000);


    public void cacheRequest(Timestamp base) {
        Timestamp day = incomeTimeUtil.getYesterdayBegin(base);

        wxAppCacheManager.invalidModuleRequestStatus(day, WxModuleType.PRODUCT);
        //产品-品牌
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_BRAND_COMPOSITION, wxAppProductIncomeService.getBrandIncomeComposition(base));
        //产品-效果
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_RTB_COMPOSITION, wxAppProductIncomeService.getRtbIncomeComposition(base));
        //产品-UP
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_UP_COMPOSITION_UPDATE, wxAppProductIncomeService.getUpIncomeComposition(base));

        //产品库存使用情况
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_PV, wxAppProductIncomeService.getLocationDayBo(base));

        //ECPM
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_QUOTA_ECPM, wxAppProductIncomeService.getCompositionEcpm(base));

        //CTR
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_QUOTA_CTR, wxAppProductIncomeService.getCompositionCtr(base));

        //算法提升
        wxAppCacheManager.cacheDayRequest(day, WxAppRequestKey.PRODUCT_CREATIVE_CTR, wxAppProductIncomeService.getProductCreativeCtrData(base));
        wxAppCacheManager.validModuleRequestStatus(day, WxModuleType.PRODUCT);

    }

//    // 缓存计算数据
//    public void cacheCalculateData(Timestamp base) throws WxAppException {
//        Timestamp day = incomeTimeUtil.getYesterdayBegin(base);
//        //dss_ad_overview 数据源数据是否准备好监控
//        if (!dataCheckService.dssDataCheckByState(day)) {
//            throw new WxAppException(WxAppCrmExceptionCode.PAPER_DA_DATA_ZERO);
//        }
//
//        wxAppCacheManager.invalidModuleCalculateStatus(day, WxModuleType.PRODUCT);
//        //产品收入
//        cacheCompositionYesterday(base);
//        cacheCompositionQuarter(base);
//
//        wxAppCacheManager.validModuleCalculateStatus(day, WxModuleType.PRODUCT);
//    }


//    // 缓存计算数据 查询数仓表
//    public void cacheCalculateDataV2(Timestamp base) throws WxAppException {
//        Timestamp day = incomeTimeUtil.getYesterdayBegin(base);
//        //dss_ad_overview 数据源数据是否准备好监控
//        if (!dataCheckService.dssDataCheckByState(day)) {
//            throw new WxAppException(WxAppCrmExceptionCode.PAPER_DA_DATA_ZERO);
//        }
//
//        wxAppCacheManager.invalidModuleCalculateStatus(day, WxModuleType.PRODUCT);
//        //产品收入
//        cacheCompositionYesterday(base);
//        cacheCompositionQuarter(base);
//
//        wxAppCacheManager.validModuleCalculateStatus(day, WxModuleType.PRODUCT);
//    }

//    // 缓存 组成部分的某天的收入
//    public void cacheCompositionYesterday(Timestamp base) throws WxAppException {
//        Cat.logEvent("PRODUCT_YESTERDAY_CACHE_START", base.toString());
//        LOGGER.info("PRODUCT_YESTERDAY_CACHE_START " + base.toString());
//
//        Timestamp begin = incomeTimeUtil.getYesterdayBegin(base);
//        Timestamp end = incomeTimeUtil.getYesterdayEnd(base);
//
//        cacheInTime(begin, end, incomeConfigHelper.getProductBrandComposition(), ProductIncomeComposition.INCOME_BRAND_TOTAL);
//
//        cacheInTime(begin, end, incomeConfigHelper.getProductRtbComposition(), ProductIncomeComposition.INCOME_RTB_TOTAL);
//
//        //up主-广告团队
//        //cacheInTime(begin, end, incomeConfigHelper.getProductUpAdComposition(), ProductIncomeComposition.UP_AD_TOTAL);
//        //up主-主商团队  up主产品
//        //cacheInTime(begin, end, incomeConfigHelper.getProductUpBsiComposition(), ProductIncomeComposition.UP_BSI_TOTAL);
//        //up主-所有团队
//        cacheInTime(begin, end, incomeConfigHelper.getProductUpTotalComposition(), ProductIncomeComposition.UP_TOTAL);
//        //PV库存
//        cachePvInTime(begin, end, incomeConfigHelper.getProductPvComposition());
//        //quota
//        cacheQuotaInTime(begin, end, productPvConfig.getProductQuotaComposition());
//
//        cacheCreativeInTime(begin, end);
//
//        Cat.logEvent("PRODUCT_YESTERDAY_CACHE_FINISH", base.toString());
//        LOGGER.info("PRODUCT_YESTERDAY_CACHE_FINISH " + base.toString());
//    }

//    // 缓存 组成部分季度
//    public void cacheCompositionQuarter(Timestamp base) {
//        Cat.logEvent("PRODUCT_QUARTER_CACHE_START", base.toString());
//        Timestamp begin = incomeTimeUtil.getQuarterFirstDate(incomeTimeUtil.getYesterdayBegin(base));
//        Timestamp end = incomeTimeUtil.getYesterdayEnd(base);
//        cacheInTime(begin, end, incomeConfigHelper.getProductBrandComposition(), ProductIncomeComposition.INCOME_BRAND_TOTAL);
//        cacheInTime(begin, end, incomeConfigHelper.getProductRtbComposition(), ProductIncomeComposition.INCOME_RTB_TOTAL);
//
//        //up主-广告团队
//        //cacheInTime(begin, end, incomeConfigHelper.getProductUpAdComposition(), ProductIncomeComposition.UP_AD_TOTAL);
//        //up主-主商团队
//        //cacheInTime(begin, end, incomeConfigHelper.getProductUpBsiComposition(), ProductIncomeComposition.UP_BSI_TOTAL);
//        //up主-所有团队
//        cacheInTime(begin, end, incomeConfigHelper.getProductUpTotalComposition(), ProductIncomeComposition.UP_TOTAL);
//        Cat.logEvent("PRODUCT_QUARTER_CACHE_FINISH", base.toString());
//    }

//    // 缓存 某段范围的收入
//    public void cacheInTime(Timestamp begin, Timestamp end, List<ProductIncomeComposition> compositions, ProductIncomeComposition total) {
//        BigDecimal totalAmount = BigDecimal.ZERO;
//
//        for (ProductIncomeComposition composition : compositions) {
//            try {
//                BigDecimal income = wxAppPaperService.getProductIncome(begin, end, composition);
//                totalAmount = totalAmount.add(income);
//                wxAppCacheManager.cacheCalculateData(begin, end, income, WxModuleType.PRODUCT.name(), WxQuotaType.INCOME.name(), composition.name());
//            } catch (IncomeConditionInvalidException e) {
//                wxAppCacheManager.cacheCalculateData(begin, end, BigDecimal.ZERO, WxModuleType.PRODUCT.name(), WxQuotaType.INCOME.name(), composition.name());
//            }
//
//        }
//        //缓存全部
//        wxAppCacheManager.cacheCalculateData(begin, end, totalAmount, WxModuleType.PRODUCT.name(), WxQuotaType.INCOME.name(), total.name());
//    }

    /**
     * 缓存产品pv收入
     */
    private void cachePvInTime(Timestamp begin, Timestamp end, List<ProductIncomeComposition> compositions) throws WxAppException {
        //总库存
        BigDecimal totalPv = BigDecimal.ZERO;
        //库存累加
        BigDecimal amount = BigDecimal.ZERO;
        for (ProductIncomeComposition composition : compositions) {

            BigDecimal income = wxAppPaperService.getProductPv(begin, end, composition);
            BigDecimal pv = incomeUtil.divide(income, MILLION, 3);
            wxAppCacheManager.cacheCalculateData(begin, end, pv, WxModuleType.PRODUCT.name(), WxQuotaType.PV.name(), composition.name());

            if (ProductIncomeComposition.PRODUCT_PV_ALL.equals(composition)) {
                totalPv = pv;
            }
            if (!(ProductIncomeComposition.PRODUCT_PV_ALL.equals(composition) || ProductIncomeComposition.PRODUCT_PV_OTHER.equals(composition))) {
                amount = amount.add(pv);
            }
        }
        //缓存其他
        wxAppCacheManager.cacheCalculateData(begin, end, totalPv.subtract(amount), WxModuleType.PRODUCT.name(), WxQuotaType.PV.name(), ProductIncomeComposition.PRODUCT_PV_OTHER.name());
    }

    /**
     * 缓存ecpm,ctr
     */
    private void cacheQuotaInTime(Timestamp begin, Timestamp end, List<ProductIncomeComposition> compositions) {
        compositions.forEach(composition -> {
            try {
                AdIncome productQuota = wxAppPaperService.getProductQuota(begin, end, composition);
                wxAppCacheManager.cacheCalculateObjectData(begin, end, productQuota, WxModuleType.PRODUCT.name(), WxQuotaType.QUOTA.name(), composition.name());
            } catch (IncomeConditionInvalidException e) {
                AdIncome adIncome = new AdIncome(begin, BigDecimal.ZERO, 0L, 0L);
                wxAppCacheManager.cacheCalculateObjectData(begin, end, adIncome, WxModuleType.PRODUCT.name(), WxQuotaType.QUOTA.name(), composition.name());
            }
        });
    }

    /**
     * 缓存算法ctr
     */
    private void cacheCreativeInTime(Timestamp begin, Timestamp end) {
        DssCreativeCtrCompareDto creativeCtr = wxAppPaperService.getProductCreativeCtr(begin, end);
        //基准ctr
        BigDecimal randomCtr = incomeUtil.divide(creativeCtr.getRandomClick(), creativeCtr.getRandomShow(), 5);
        //算法ctr
        BigDecimal leftCtr = incomeUtil.divide(creativeCtr.getLeftClick(), creativeCtr.getLeftShow(), 5);
        wxAppCacheManager.cacheCalculateObjectData(begin, end, randomCtr, WxModuleType.PRODUCT.name(), WxQuotaType.QUOTA.name(), CtrType.RANDOM_CTR.name());
        wxAppCacheManager.cacheCalculateObjectData(begin, end, leftCtr, WxModuleType.PRODUCT.name(), WxQuotaType.QUOTA.name(), CtrType.LEFT_CTR.name());
    }

}
