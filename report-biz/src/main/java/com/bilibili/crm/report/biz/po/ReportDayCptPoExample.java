package com.bilibili.crm.report.biz.po;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

public class ReportDayCptPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public ReportDayCptPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andDateIsNull() {
            addCriterion("date is null");
            return (Criteria) this;
        }

        public Criteria andDateIsNotNull() {
            addCriterion("date is not null");
            return (Criteria) this;
        }

        public Criteria andDateEqualTo(Timestamp value) {
            addCriterion("date =", value, "date");
            return (Criteria) this;
        }

        public Criteria andDateNotEqualTo(Timestamp value) {
            addCriterion("date <>", value, "date");
            return (Criteria) this;
        }

        public Criteria andDateGreaterThan(Timestamp value) {
            addCriterion("date >", value, "date");
            return (Criteria) this;
        }

        public Criteria andDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("date >=", value, "date");
            return (Criteria) this;
        }

        public Criteria andDateLessThan(Timestamp value) {
            addCriterion("date <", value, "date");
            return (Criteria) this;
        }

        public Criteria andDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("date <=", value, "date");
            return (Criteria) this;
        }

        public Criteria andDateIn(List<Timestamp> values) {
            addCriterion("date in", values, "date");
            return (Criteria) this;
        }

        public Criteria andDateNotIn(List<Timestamp> values) {
            addCriterion("date not in", values, "date");
            return (Criteria) this;
        }

        public Criteria andDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("date between", value1, value2, "date");
            return (Criteria) this;
        }

        public Criteria andDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("date not between", value1, value2, "date");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNull() {
            addCriterion("order_id is null");
            return (Criteria) this;
        }

        public Criteria andOrderIdIsNotNull() {
            addCriterion("order_id is not null");
            return (Criteria) this;
        }

        public Criteria andOrderIdEqualTo(Integer value) {
            addCriterion("order_id =", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotEqualTo(Integer value) {
            addCriterion("order_id <>", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThan(Integer value) {
            addCriterion("order_id >", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_id >=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThan(Integer value) {
            addCriterion("order_id <", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("order_id <=", value, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdIn(List<Integer> values) {
            addCriterion("order_id in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotIn(List<Integer> values) {
            addCriterion("order_id not in", values, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("order_id between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("order_id not between", value1, value2, "orderId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdIsNull() {
            addCriterion("applied_id is null");
            return (Criteria) this;
        }

        public Criteria andAppliedIdIsNotNull() {
            addCriterion("applied_id is not null");
            return (Criteria) this;
        }

        public Criteria andAppliedIdEqualTo(Integer value) {
            addCriterion("applied_id =", value, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdNotEqualTo(Integer value) {
            addCriterion("applied_id <>", value, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdGreaterThan(Integer value) {
            addCriterion("applied_id >", value, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("applied_id >=", value, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdLessThan(Integer value) {
            addCriterion("applied_id <", value, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdLessThanOrEqualTo(Integer value) {
            addCriterion("applied_id <=", value, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdIn(List<Integer> values) {
            addCriterion("applied_id in", values, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdNotIn(List<Integer> values) {
            addCriterion("applied_id not in", values, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdBetween(Integer value1, Integer value2) {
            addCriterion("applied_id between", value1, value2, "appliedId");
            return (Criteria) this;
        }

        public Criteria andAppliedIdNotBetween(Integer value1, Integer value2) {
            addCriterion("applied_id not between", value1, value2, "appliedId");
            return (Criteria) this;
        }

        public Criteria andSrcIdIsNull() {
            addCriterion("src_id is null");
            return (Criteria) this;
        }

        public Criteria andSrcIdIsNotNull() {
            addCriterion("src_id is not null");
            return (Criteria) this;
        }

        public Criteria andSrcIdEqualTo(Integer value) {
            addCriterion("src_id =", value, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdNotEqualTo(Integer value) {
            addCriterion("src_id <>", value, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdGreaterThan(Integer value) {
            addCriterion("src_id >", value, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("src_id >=", value, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdLessThan(Integer value) {
            addCriterion("src_id <", value, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdLessThanOrEqualTo(Integer value) {
            addCriterion("src_id <=", value, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdIn(List<Integer> values) {
            addCriterion("src_id in", values, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdNotIn(List<Integer> values) {
            addCriterion("src_id not in", values, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdBetween(Integer value1, Integer value2) {
            addCriterion("src_id between", value1, value2, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcIdNotBetween(Integer value1, Integer value2) {
            addCriterion("src_id not between", value1, value2, "srcId");
            return (Criteria) this;
        }

        public Criteria andSrcLevelIsNull() {
            addCriterion("src_level is null");
            return (Criteria) this;
        }

        public Criteria andSrcLevelIsNotNull() {
            addCriterion("src_level is not null");
            return (Criteria) this;
        }

        public Criteria andSrcLevelEqualTo(String value) {
            addCriterion("src_level =", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelNotEqualTo(String value) {
            addCriterion("src_level <>", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelGreaterThan(String value) {
            addCriterion("src_level >", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelGreaterThanOrEqualTo(String value) {
            addCriterion("src_level >=", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelLessThan(String value) {
            addCriterion("src_level <", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelLessThanOrEqualTo(String value) {
            addCriterion("src_level <=", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelLike(String value) {
            addCriterion("src_level like", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelNotLike(String value) {
            addCriterion("src_level not like", value, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelIn(List<String> values) {
            addCriterion("src_level in", values, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelNotIn(List<String> values) {
            addCriterion("src_level not in", values, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelBetween(String value1, String value2) {
            addCriterion("src_level between", value1, value2, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andSrcLevelNotBetween(String value1, String value2) {
            addCriterion("src_level not between", value1, value2, "srcLevel");
            return (Criteria) this;
        }

        public Criteria andRoleIdIsNull() {
            addCriterion("role_id is null");
            return (Criteria) this;
        }

        public Criteria andRoleIdIsNotNull() {
            addCriterion("role_id is not null");
            return (Criteria) this;
        }

        public Criteria andRoleIdEqualTo(Integer value) {
            addCriterion("role_id =", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotEqualTo(Integer value) {
            addCriterion("role_id <>", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdGreaterThan(Integer value) {
            addCriterion("role_id >", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("role_id >=", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdLessThan(Integer value) {
            addCriterion("role_id <", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdLessThanOrEqualTo(Integer value) {
            addCriterion("role_id <=", value, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdIn(List<Integer> values) {
            addCriterion("role_id in", values, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotIn(List<Integer> values) {
            addCriterion("role_id not in", values, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdBetween(Integer value1, Integer value2) {
            addCriterion("role_id between", value1, value2, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("role_id not between", value1, value2, "roleId");
            return (Criteria) this;
        }

        public Criteria andRoleNameIsNull() {
            addCriterion("role_name is null");
            return (Criteria) this;
        }

        public Criteria andRoleNameIsNotNull() {
            addCriterion("role_name is not null");
            return (Criteria) this;
        }

        public Criteria andRoleNameEqualTo(String value) {
            addCriterion("role_name =", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotEqualTo(String value) {
            addCriterion("role_name <>", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameGreaterThan(String value) {
            addCriterion("role_name >", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameGreaterThanOrEqualTo(String value) {
            addCriterion("role_name >=", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLessThan(String value) {
            addCriterion("role_name <", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLessThanOrEqualTo(String value) {
            addCriterion("role_name <=", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameLike(String value) {
            addCriterion("role_name like", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotLike(String value) {
            addCriterion("role_name not like", value, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameIn(List<String> values) {
            addCriterion("role_name in", values, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotIn(List<String> values) {
            addCriterion("role_name not in", values, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameBetween(String value1, String value2) {
            addCriterion("role_name between", value1, value2, "roleName");
            return (Criteria) this;
        }

        public Criteria andRoleNameNotBetween(String value1, String value2) {
            addCriterion("role_name not between", value1, value2, "roleName");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNull() {
            addCriterion("material_id is null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIsNotNull() {
            addCriterion("material_id is not null");
            return (Criteria) this;
        }

        public Criteria andMaterialIdEqualTo(Integer value) {
            addCriterion("material_id =", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotEqualTo(Integer value) {
            addCriterion("material_id <>", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThan(Integer value) {
            addCriterion("material_id >", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("material_id >=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThan(Integer value) {
            addCriterion("material_id <", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdLessThanOrEqualTo(Integer value) {
            addCriterion("material_id <=", value, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdIn(List<Integer> values) {
            addCriterion("material_id in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotIn(List<Integer> values) {
            addCriterion("material_id not in", values, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdBetween(Integer value1, Integer value2) {
            addCriterion("material_id between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andMaterialIdNotBetween(Integer value1, Integer value2) {
            addCriterion("material_id not between", value1, value2, "materialId");
            return (Criteria) this;
        }

        public Criteria andTitleIsNull() {
            addCriterion("title is null");
            return (Criteria) this;
        }

        public Criteria andTitleIsNotNull() {
            addCriterion("title is not null");
            return (Criteria) this;
        }

        public Criteria andTitleEqualTo(String value) {
            addCriterion("title =", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotEqualTo(String value) {
            addCriterion("title <>", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThan(String value) {
            addCriterion("title >", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleGreaterThanOrEqualTo(String value) {
            addCriterion("title >=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThan(String value) {
            addCriterion("title <", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLessThanOrEqualTo(String value) {
            addCriterion("title <=", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleLike(String value) {
            addCriterion("title like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotLike(String value) {
            addCriterion("title not like", value, "title");
            return (Criteria) this;
        }

        public Criteria andTitleIn(List<String> values) {
            addCriterion("title in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotIn(List<String> values) {
            addCriterion("title not in", values, "title");
            return (Criteria) this;
        }

        public Criteria andTitleBetween(String value1, String value2) {
            addCriterion("title between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andTitleNotBetween(String value1, String value2) {
            addCriterion("title not between", value1, value2, "title");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNull() {
            addCriterion("platform is null");
            return (Criteria) this;
        }

        public Criteria andPlatformIsNotNull() {
            addCriterion("platform is not null");
            return (Criteria) this;
        }

        public Criteria andPlatformEqualTo(Integer value) {
            addCriterion("platform =", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotEqualTo(Integer value) {
            addCriterion("platform <>", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThan(Integer value) {
            addCriterion("platform >", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformGreaterThanOrEqualTo(Integer value) {
            addCriterion("platform >=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThan(Integer value) {
            addCriterion("platform <", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformLessThanOrEqualTo(Integer value) {
            addCriterion("platform <=", value, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformIn(List<Integer> values) {
            addCriterion("platform in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotIn(List<Integer> values) {
            addCriterion("platform not in", values, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformBetween(Integer value1, Integer value2) {
            addCriterion("platform between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPlatformNotBetween(Integer value1, Integer value2) {
            addCriterion("platform not between", value1, value2, "platform");
            return (Criteria) this;
        }

        public Criteria andPageIsNull() {
            addCriterion("page is null");
            return (Criteria) this;
        }

        public Criteria andPageIsNotNull() {
            addCriterion("page is not null");
            return (Criteria) this;
        }

        public Criteria andPageEqualTo(String value) {
            addCriterion("page =", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageNotEqualTo(String value) {
            addCriterion("page <>", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageGreaterThan(String value) {
            addCriterion("page >", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageGreaterThanOrEqualTo(String value) {
            addCriterion("page >=", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageLessThan(String value) {
            addCriterion("page <", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageLessThanOrEqualTo(String value) {
            addCriterion("page <=", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageLike(String value) {
            addCriterion("page like", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageNotLike(String value) {
            addCriterion("page not like", value, "page");
            return (Criteria) this;
        }

        public Criteria andPageIn(List<String> values) {
            addCriterion("page in", values, "page");
            return (Criteria) this;
        }

        public Criteria andPageNotIn(List<String> values) {
            addCriterion("page not in", values, "page");
            return (Criteria) this;
        }

        public Criteria andPageBetween(String value1, String value2) {
            addCriterion("page between", value1, value2, "page");
            return (Criteria) this;
        }

        public Criteria andPageNotBetween(String value1, String value2) {
            addCriterion("page not between", value1, value2, "page");
            return (Criteria) this;
        }

        public Criteria andPositionIsNull() {
            addCriterion("position is null");
            return (Criteria) this;
        }

        public Criteria andPositionIsNotNull() {
            addCriterion("position is not null");
            return (Criteria) this;
        }

        public Criteria andPositionEqualTo(String value) {
            addCriterion("position =", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionNotEqualTo(String value) {
            addCriterion("position <>", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionGreaterThan(String value) {
            addCriterion("position >", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionGreaterThanOrEqualTo(String value) {
            addCriterion("position >=", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionLessThan(String value) {
            addCriterion("position <", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionLessThanOrEqualTo(String value) {
            addCriterion("position <=", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionLike(String value) {
            addCriterion("position like", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionNotLike(String value) {
            addCriterion("position not like", value, "position");
            return (Criteria) this;
        }

        public Criteria andPositionIn(List<String> values) {
            addCriterion("position in", values, "position");
            return (Criteria) this;
        }

        public Criteria andPositionNotIn(List<String> values) {
            addCriterion("position not in", values, "position");
            return (Criteria) this;
        }

        public Criteria andPositionBetween(String value1, String value2) {
            addCriterion("position between", value1, value2, "position");
            return (Criteria) this;
        }

        public Criteria andPositionNotBetween(String value1, String value2) {
            addCriterion("position not between", value1, value2, "position");
            return (Criteria) this;
        }

        public Criteria andOffsetIsNull() {
            addCriterion("offset is null");
            return (Criteria) this;
        }

        public Criteria andOffsetIsNotNull() {
            addCriterion("offset is not null");
            return (Criteria) this;
        }

        public Criteria andOffsetEqualTo(Integer value) {
            addCriterion("offset =", value, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetNotEqualTo(Integer value) {
            addCriterion("offset <>", value, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetGreaterThan(Integer value) {
            addCriterion("offset >", value, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetGreaterThanOrEqualTo(Integer value) {
            addCriterion("offset >=", value, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetLessThan(Integer value) {
            addCriterion("offset <", value, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetLessThanOrEqualTo(Integer value) {
            addCriterion("offset <=", value, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetIn(List<Integer> values) {
            addCriterion("offset in", values, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetNotIn(List<Integer> values) {
            addCriterion("offset not in", values, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetBetween(Integer value1, Integer value2) {
            addCriterion("offset between", value1, value2, "offset");
            return (Criteria) this;
        }

        public Criteria andOffsetNotBetween(Integer value1, Integer value2) {
            addCriterion("offset not between", value1, value2, "offset");
            return (Criteria) this;
        }

        public Criteria andPvIsNull() {
            addCriterion("pv is null");
            return (Criteria) this;
        }

        public Criteria andPvIsNotNull() {
            addCriterion("pv is not null");
            return (Criteria) this;
        }

        public Criteria andPvEqualTo(Integer value) {
            addCriterion("pv =", value, "pv");
            return (Criteria) this;
        }

        public Criteria andPvNotEqualTo(Integer value) {
            addCriterion("pv <>", value, "pv");
            return (Criteria) this;
        }

        public Criteria andPvGreaterThan(Integer value) {
            addCriterion("pv >", value, "pv");
            return (Criteria) this;
        }

        public Criteria andPvGreaterThanOrEqualTo(Integer value) {
            addCriterion("pv >=", value, "pv");
            return (Criteria) this;
        }

        public Criteria andPvLessThan(Integer value) {
            addCriterion("pv <", value, "pv");
            return (Criteria) this;
        }

        public Criteria andPvLessThanOrEqualTo(Integer value) {
            addCriterion("pv <=", value, "pv");
            return (Criteria) this;
        }

        public Criteria andPvIn(List<Integer> values) {
            addCriterion("pv in", values, "pv");
            return (Criteria) this;
        }

        public Criteria andPvNotIn(List<Integer> values) {
            addCriterion("pv not in", values, "pv");
            return (Criteria) this;
        }

        public Criteria andPvBetween(Integer value1, Integer value2) {
            addCriterion("pv between", value1, value2, "pv");
            return (Criteria) this;
        }

        public Criteria andPvNotBetween(Integer value1, Integer value2) {
            addCriterion("pv not between", value1, value2, "pv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvIsNull() {
            addCriterion("visible_pv is null");
            return (Criteria) this;
        }

        public Criteria andVisiblePvIsNotNull() {
            addCriterion("visible_pv is not null");
            return (Criteria) this;
        }

        public Criteria andVisiblePvEqualTo(Integer value) {
            addCriterion("visible_pv =", value, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvNotEqualTo(Integer value) {
            addCriterion("visible_pv <>", value, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvGreaterThan(Integer value) {
            addCriterion("visible_pv >", value, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvGreaterThanOrEqualTo(Integer value) {
            addCriterion("visible_pv >=", value, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvLessThan(Integer value) {
            addCriterion("visible_pv <", value, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvLessThanOrEqualTo(Integer value) {
            addCriterion("visible_pv <=", value, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvIn(List<Integer> values) {
            addCriterion("visible_pv in", values, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvNotIn(List<Integer> values) {
            addCriterion("visible_pv not in", values, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvBetween(Integer value1, Integer value2) {
            addCriterion("visible_pv between", value1, value2, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andVisiblePvNotBetween(Integer value1, Integer value2) {
            addCriterion("visible_pv not between", value1, value2, "visiblePv");
            return (Criteria) this;
        }

        public Criteria andClickIsNull() {
            addCriterion("click is null");
            return (Criteria) this;
        }

        public Criteria andClickIsNotNull() {
            addCriterion("click is not null");
            return (Criteria) this;
        }

        public Criteria andClickEqualTo(Integer value) {
            addCriterion("click =", value, "click");
            return (Criteria) this;
        }

        public Criteria andClickNotEqualTo(Integer value) {
            addCriterion("click <>", value, "click");
            return (Criteria) this;
        }

        public Criteria andClickGreaterThan(Integer value) {
            addCriterion("click >", value, "click");
            return (Criteria) this;
        }

        public Criteria andClickGreaterThanOrEqualTo(Integer value) {
            addCriterion("click >=", value, "click");
            return (Criteria) this;
        }

        public Criteria andClickLessThan(Integer value) {
            addCriterion("click <", value, "click");
            return (Criteria) this;
        }

        public Criteria andClickLessThanOrEqualTo(Integer value) {
            addCriterion("click <=", value, "click");
            return (Criteria) this;
        }

        public Criteria andClickIn(List<Integer> values) {
            addCriterion("click in", values, "click");
            return (Criteria) this;
        }

        public Criteria andClickNotIn(List<Integer> values) {
            addCriterion("click not in", values, "click");
            return (Criteria) this;
        }

        public Criteria andClickBetween(Integer value1, Integer value2) {
            addCriterion("click between", value1, value2, "click");
            return (Criteria) this;
        }

        public Criteria andClickNotBetween(Integer value1, Integer value2) {
            addCriterion("click not between", value1, value2, "click");
            return (Criteria) this;
        }

        public Criteria andCtr1IsNull() {
            addCriterion("ctr1 is null");
            return (Criteria) this;
        }

        public Criteria andCtr1IsNotNull() {
            addCriterion("ctr1 is not null");
            return (Criteria) this;
        }

        public Criteria andCtr1EqualTo(BigDecimal value) {
            addCriterion("ctr1 =", value, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1NotEqualTo(BigDecimal value) {
            addCriterion("ctr1 <>", value, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1GreaterThan(BigDecimal value) {
            addCriterion("ctr1 >", value, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ctr1 >=", value, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1LessThan(BigDecimal value) {
            addCriterion("ctr1 <", value, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ctr1 <=", value, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1In(List<BigDecimal> values) {
            addCriterion("ctr1 in", values, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1NotIn(List<BigDecimal> values) {
            addCriterion("ctr1 not in", values, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ctr1 between", value1, value2, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr1NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ctr1 not between", value1, value2, "ctr1");
            return (Criteria) this;
        }

        public Criteria andCtr2IsNull() {
            addCriterion("ctr2 is null");
            return (Criteria) this;
        }

        public Criteria andCtr2IsNotNull() {
            addCriterion("ctr2 is not null");
            return (Criteria) this;
        }

        public Criteria andCtr2EqualTo(BigDecimal value) {
            addCriterion("ctr2 =", value, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2NotEqualTo(BigDecimal value) {
            addCriterion("ctr2 <>", value, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2GreaterThan(BigDecimal value) {
            addCriterion("ctr2 >", value, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2GreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("ctr2 >=", value, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2LessThan(BigDecimal value) {
            addCriterion("ctr2 <", value, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2LessThanOrEqualTo(BigDecimal value) {
            addCriterion("ctr2 <=", value, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2In(List<BigDecimal> values) {
            addCriterion("ctr2 in", values, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2NotIn(List<BigDecimal> values) {
            addCriterion("ctr2 not in", values, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2Between(BigDecimal value1, BigDecimal value2) {
            addCriterion("ctr2 between", value1, value2, "ctr2");
            return (Criteria) this;
        }

        public Criteria andCtr2NotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("ctr2 not between", value1, value2, "ctr2");
            return (Criteria) this;
        }

        public Criteria andUvIsNull() {
            addCriterion("uv is null");
            return (Criteria) this;
        }

        public Criteria andUvIsNotNull() {
            addCriterion("uv is not null");
            return (Criteria) this;
        }

        public Criteria andUvEqualTo(Integer value) {
            addCriterion("uv =", value, "uv");
            return (Criteria) this;
        }

        public Criteria andUvNotEqualTo(Integer value) {
            addCriterion("uv <>", value, "uv");
            return (Criteria) this;
        }

        public Criteria andUvGreaterThan(Integer value) {
            addCriterion("uv >", value, "uv");
            return (Criteria) this;
        }

        public Criteria andUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("uv >=", value, "uv");
            return (Criteria) this;
        }

        public Criteria andUvLessThan(Integer value) {
            addCriterion("uv <", value, "uv");
            return (Criteria) this;
        }

        public Criteria andUvLessThanOrEqualTo(Integer value) {
            addCriterion("uv <=", value, "uv");
            return (Criteria) this;
        }

        public Criteria andUvIn(List<Integer> values) {
            addCriterion("uv in", values, "uv");
            return (Criteria) this;
        }

        public Criteria andUvNotIn(List<Integer> values) {
            addCriterion("uv not in", values, "uv");
            return (Criteria) this;
        }

        public Criteria andUvBetween(Integer value1, Integer value2) {
            addCriterion("uv between", value1, value2, "uv");
            return (Criteria) this;
        }

        public Criteria andUvNotBetween(Integer value1, Integer value2) {
            addCriterion("uv not between", value1, value2, "uv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvIsNull() {
            addCriterion("visible_uv is null");
            return (Criteria) this;
        }

        public Criteria andVisibleUvIsNotNull() {
            addCriterion("visible_uv is not null");
            return (Criteria) this;
        }

        public Criteria andVisibleUvEqualTo(Integer value) {
            addCriterion("visible_uv =", value, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvNotEqualTo(Integer value) {
            addCriterion("visible_uv <>", value, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvGreaterThan(Integer value) {
            addCriterion("visible_uv >", value, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvGreaterThanOrEqualTo(Integer value) {
            addCriterion("visible_uv >=", value, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvLessThan(Integer value) {
            addCriterion("visible_uv <", value, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvLessThanOrEqualTo(Integer value) {
            addCriterion("visible_uv <=", value, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvIn(List<Integer> values) {
            addCriterion("visible_uv in", values, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvNotIn(List<Integer> values) {
            addCriterion("visible_uv not in", values, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvBetween(Integer value1, Integer value2) {
            addCriterion("visible_uv between", value1, value2, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andVisibleUvNotBetween(Integer value1, Integer value2) {
            addCriterion("visible_uv not between", value1, value2, "visibleUv");
            return (Criteria) this;
        }

        public Criteria andUseQuotaIsNull() {
            addCriterion("use_quota is null");
            return (Criteria) this;
        }

        public Criteria andUseQuotaIsNotNull() {
            addCriterion("use_quota is not null");
            return (Criteria) this;
        }

        public Criteria andUseQuotaEqualTo(Integer value) {
            addCriterion("use_quota =", value, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaNotEqualTo(Integer value) {
            addCriterion("use_quota <>", value, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaGreaterThan(Integer value) {
            addCriterion("use_quota >", value, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaGreaterThanOrEqualTo(Integer value) {
            addCriterion("use_quota >=", value, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaLessThan(Integer value) {
            addCriterion("use_quota <", value, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaLessThanOrEqualTo(Integer value) {
            addCriterion("use_quota <=", value, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaIn(List<Integer> values) {
            addCriterion("use_quota in", values, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaNotIn(List<Integer> values) {
            addCriterion("use_quota not in", values, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaBetween(Integer value1, Integer value2) {
            addCriterion("use_quota between", value1, value2, "useQuota");
            return (Criteria) this;
        }

        public Criteria andUseQuotaNotBetween(Integer value1, Integer value2) {
            addCriterion("use_quota not between", value1, value2, "useQuota");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andResourceTypeIsNull() {
            addCriterion("resource_type is null");
            return (Criteria) this;
        }

        public Criteria andResourceTypeIsNotNull() {
            addCriterion("resource_type is not null");
            return (Criteria) this;
        }

        public Criteria andResourceTypeEqualTo(Integer value) {
            addCriterion("resource_type =", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotEqualTo(Integer value) {
            addCriterion("resource_type <>", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeGreaterThan(Integer value) {
            addCriterion("resource_type >", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("resource_type >=", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeLessThan(Integer value) {
            addCriterion("resource_type <", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("resource_type <=", value, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeIn(List<Integer> values) {
            addCriterion("resource_type in", values, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotIn(List<Integer> values) {
            addCriterion("resource_type not in", values, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeBetween(Integer value1, Integer value2) {
            addCriterion("resource_type between", value1, value2, "resourceType");
            return (Criteria) this;
        }

        public Criteria andResourceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("resource_type not between", value1, value2, "resourceType");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIsNull() {
            addCriterion("external_price is null");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIsNotNull() {
            addCriterion("external_price is not null");
            return (Criteria) this;
        }

        public Criteria andExternalPriceEqualTo(Integer value) {
            addCriterion("external_price =", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotEqualTo(Integer value) {
            addCriterion("external_price <>", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceGreaterThan(Integer value) {
            addCriterion("external_price >", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceGreaterThanOrEqualTo(Integer value) {
            addCriterion("external_price >=", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceLessThan(Integer value) {
            addCriterion("external_price <", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceLessThanOrEqualTo(Integer value) {
            addCriterion("external_price <=", value, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceIn(List<Integer> values) {
            addCriterion("external_price in", values, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotIn(List<Integer> values) {
            addCriterion("external_price not in", values, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceBetween(Integer value1, Integer value2) {
            addCriterion("external_price between", value1, value2, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andExternalPriceNotBetween(Integer value1, Integer value2) {
            addCriterion("external_price not between", value1, value2, "externalPrice");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeIsNull() {
            addCriterion("creative_type is null");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeIsNotNull() {
            addCriterion("creative_type is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeEqualTo(String value) {
            addCriterion("creative_type =", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotEqualTo(String value) {
            addCriterion("creative_type <>", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeGreaterThan(String value) {
            addCriterion("creative_type >", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeGreaterThanOrEqualTo(String value) {
            addCriterion("creative_type >=", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeLessThan(String value) {
            addCriterion("creative_type <", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeLessThanOrEqualTo(String value) {
            addCriterion("creative_type <=", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeLike(String value) {
            addCriterion("creative_type like", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotLike(String value) {
            addCriterion("creative_type not like", value, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeIn(List<String> values) {
            addCriterion("creative_type in", values, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotIn(List<String> values) {
            addCriterion("creative_type not in", values, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeBetween(String value1, String value2) {
            addCriterion("creative_type between", value1, value2, "creativeType");
            return (Criteria) this;
        }

        public Criteria andCreativeTypeNotBetween(String value1, String value2) {
            addCriterion("creative_type not between", value1, value2, "creativeType");
            return (Criteria) this;
        }

        public Criteria andOrderNameIsNull() {
            addCriterion("order_name is null");
            return (Criteria) this;
        }

        public Criteria andOrderNameIsNotNull() {
            addCriterion("order_name is not null");
            return (Criteria) this;
        }

        public Criteria andOrderNameEqualTo(String value) {
            addCriterion("order_name =", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameNotEqualTo(String value) {
            addCriterion("order_name <>", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameGreaterThan(String value) {
            addCriterion("order_name >", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameGreaterThanOrEqualTo(String value) {
            addCriterion("order_name >=", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameLessThan(String value) {
            addCriterion("order_name <", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameLessThanOrEqualTo(String value) {
            addCriterion("order_name <=", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameLike(String value) {
            addCriterion("order_name like", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameNotLike(String value) {
            addCriterion("order_name not like", value, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameIn(List<String> values) {
            addCriterion("order_name in", values, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameNotIn(List<String> values) {
            addCriterion("order_name not in", values, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameBetween(String value1, String value2) {
            addCriterion("order_name between", value1, value2, "orderName");
            return (Criteria) this;
        }

        public Criteria andOrderNameNotBetween(String value1, String value2) {
            addCriterion("order_name not between", value1, value2, "orderName");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdIsNull() {
            addCriterion("crm_account_id is null");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdIsNotNull() {
            addCriterion("crm_account_id is not null");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdEqualTo(Integer value) {
            addCriterion("crm_account_id =", value, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdNotEqualTo(Integer value) {
            addCriterion("crm_account_id <>", value, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdGreaterThan(Integer value) {
            addCriterion("crm_account_id >", value, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_account_id >=", value, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdLessThan(Integer value) {
            addCriterion("crm_account_id <", value, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("crm_account_id <=", value, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdIn(List<Integer> values) {
            addCriterion("crm_account_id in", values, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdNotIn(List<Integer> values) {
            addCriterion("crm_account_id not in", values, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("crm_account_id between", value1, value2, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_account_id not between", value1, value2, "crmAccountId");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameIsNull() {
            addCriterion("crm_account_name is null");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameIsNotNull() {
            addCriterion("crm_account_name is not null");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameEqualTo(String value) {
            addCriterion("crm_account_name =", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameNotEqualTo(String value) {
            addCriterion("crm_account_name <>", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameGreaterThan(String value) {
            addCriterion("crm_account_name >", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameGreaterThanOrEqualTo(String value) {
            addCriterion("crm_account_name >=", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameLessThan(String value) {
            addCriterion("crm_account_name <", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameLessThanOrEqualTo(String value) {
            addCriterion("crm_account_name <=", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameLike(String value) {
            addCriterion("crm_account_name like", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameNotLike(String value) {
            addCriterion("crm_account_name not like", value, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameIn(List<String> values) {
            addCriterion("crm_account_name in", values, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameNotIn(List<String> values) {
            addCriterion("crm_account_name not in", values, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameBetween(String value1, String value2) {
            addCriterion("crm_account_name between", value1, value2, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmAccountNameNotBetween(String value1, String value2) {
            addCriterion("crm_account_name not between", value1, value2, "crmAccountName");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdIsNull() {
            addCriterion("crm_contract_id is null");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdIsNotNull() {
            addCriterion("crm_contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdEqualTo(Integer value) {
            addCriterion("crm_contract_id =", value, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdNotEqualTo(Integer value) {
            addCriterion("crm_contract_id <>", value, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdGreaterThan(Integer value) {
            addCriterion("crm_contract_id >", value, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_contract_id >=", value, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdLessThan(Integer value) {
            addCriterion("crm_contract_id <", value, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdLessThanOrEqualTo(Integer value) {
            addCriterion("crm_contract_id <=", value, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdIn(List<Integer> values) {
            addCriterion("crm_contract_id in", values, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdNotIn(List<Integer> values) {
            addCriterion("crm_contract_id not in", values, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdBetween(Integer value1, Integer value2) {
            addCriterion("crm_contract_id between", value1, value2, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractIdNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_contract_id not between", value1, value2, "crmContractId");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameIsNull() {
            addCriterion("crm_contract_name is null");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameIsNotNull() {
            addCriterion("crm_contract_name is not null");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameEqualTo(String value) {
            addCriterion("crm_contract_name =", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameNotEqualTo(String value) {
            addCriterion("crm_contract_name <>", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameGreaterThan(String value) {
            addCriterion("crm_contract_name >", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameGreaterThanOrEqualTo(String value) {
            addCriterion("crm_contract_name >=", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameLessThan(String value) {
            addCriterion("crm_contract_name <", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameLessThanOrEqualTo(String value) {
            addCriterion("crm_contract_name <=", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameLike(String value) {
            addCriterion("crm_contract_name like", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameNotLike(String value) {
            addCriterion("crm_contract_name not like", value, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameIn(List<String> values) {
            addCriterion("crm_contract_name in", values, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameNotIn(List<String> values) {
            addCriterion("crm_contract_name not in", values, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameBetween(String value1, String value2) {
            addCriterion("crm_contract_name between", value1, value2, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmContractNameNotBetween(String value1, String value2) {
            addCriterion("crm_contract_name not between", value1, value2, "crmContractName");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdIsNull() {
            addCriterion("crm_order_id is null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdIsNotNull() {
            addCriterion("crm_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdEqualTo(Integer value) {
            addCriterion("crm_order_id =", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdNotEqualTo(Integer value) {
            addCriterion("crm_order_id <>", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdGreaterThan(Integer value) {
            addCriterion("crm_order_id >", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_order_id >=", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdLessThan(Integer value) {
            addCriterion("crm_order_id <", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("crm_order_id <=", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdIn(List<Integer> values) {
            addCriterion("crm_order_id in", values, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdNotIn(List<Integer> values) {
            addCriterion("crm_order_id not in", values, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_id between", value1, value2, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_id not between", value1, value2, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeIsNull() {
            addCriterion("crm_order_resource_type is null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeIsNotNull() {
            addCriterion("crm_order_resource_type is not null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeEqualTo(Integer value) {
            addCriterion("crm_order_resource_type =", value, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeNotEqualTo(Integer value) {
            addCriterion("crm_order_resource_type <>", value, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeGreaterThan(Integer value) {
            addCriterion("crm_order_resource_type >", value, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_order_resource_type >=", value, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeLessThan(Integer value) {
            addCriterion("crm_order_resource_type <", value, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("crm_order_resource_type <=", value, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeIn(List<Integer> values) {
            addCriterion("crm_order_resource_type in", values, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeNotIn(List<Integer> values) {
            addCriterion("crm_order_resource_type not in", values, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_resource_type between", value1, value2, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderResourceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_resource_type not between", value1, value2, "crmOrderResourceType");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountIsNull() {
            addCriterion("crm_order_discount is null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountIsNotNull() {
            addCriterion("crm_order_discount is not null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountEqualTo(Integer value) {
            addCriterion("crm_order_discount =", value, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountNotEqualTo(Integer value) {
            addCriterion("crm_order_discount <>", value, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountGreaterThan(Integer value) {
            addCriterion("crm_order_discount >", value, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_order_discount >=", value, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountLessThan(Integer value) {
            addCriterion("crm_order_discount <", value, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountLessThanOrEqualTo(Integer value) {
            addCriterion("crm_order_discount <=", value, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountIn(List<Integer> values) {
            addCriterion("crm_order_discount in", values, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountNotIn(List<Integer> values) {
            addCriterion("crm_order_discount not in", values, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_discount between", value1, value2, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderDiscountNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_discount not between", value1, value2, "crmOrderDiscount");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusIsNull() {
            addCriterion("crm_order_status is null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusIsNotNull() {
            addCriterion("crm_order_status is not null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusEqualTo(Integer value) {
            addCriterion("crm_order_status =", value, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusNotEqualTo(Integer value) {
            addCriterion("crm_order_status <>", value, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusGreaterThan(Integer value) {
            addCriterion("crm_order_status >", value, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_order_status >=", value, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusLessThan(Integer value) {
            addCriterion("crm_order_status <", value, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusLessThanOrEqualTo(Integer value) {
            addCriterion("crm_order_status <=", value, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusIn(List<Integer> values) {
            addCriterion("crm_order_status in", values, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusNotIn(List<Integer> values) {
            addCriterion("crm_order_status not in", values, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_status between", value1, value2, "crmOrderStatus");
            return (Criteria) this;
        }

        public Criteria andCrmOrderStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_status not between", value1, value2, "crmOrderStatus");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}