<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.report.biz.dao.ReportDayGdDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.report.biz.po.ReportDayGdPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="date" jdbcType="DATE" property="date" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="account_name" jdbcType="VARCHAR" property="accountName" />
    <result column="order_id" jdbcType="INTEGER" property="orderId" />
    <result column="order_name" jdbcType="VARCHAR" property="orderName" />
    <result column="schedule_id" jdbcType="INTEGER" property="scheduleId" />
    <result column="schedule_name" jdbcType="VARCHAR" property="scheduleName" />
    <result column="total_impression" jdbcType="INTEGER" property="totalImpression" />
    <result column="expected_visible_pv" jdbcType="INTEGER" property="expectedVisiblePv" />
    <result column="slot_group_id" jdbcType="INTEGER" property="slotGroupId" />
    <result column="slot_group_name" jdbcType="VARCHAR" property="slotGroupName" />
    <result column="card_type_names" jdbcType="VARCHAR" property="cardTypeNames" />
    <result column="visible_pv" jdbcType="INTEGER" property="visiblePv" />
    <result column="visible_uv" jdbcType="INTEGER" property="visibleUv" />
    <result column="click" jdbcType="INTEGER" property="click" />
    <result column="cost" jdbcType="DECIMAL" property="cost" />
    <result column="cpm" jdbcType="DECIMAL" property="cpm" />
    <result column="unit_id" jdbcType="INTEGER" property="unitId" />
    <result column="creative_number" jdbcType="INTEGER" property="creativeNumber" />
    <result column="campaign_id" jdbcType="INTEGER" property="campaignId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ctr" jdbcType="DECIMAL" property="ctr" />
    <result column="cpc" jdbcType="DECIMAL" property="cpc" />
    <result column="ecpm" jdbcType="DECIMAL" property="ecpm" />
    <result column="schedule_consumption_progress" jdbcType="DECIMAL" property="scheduleConsumptionProgress" />
    <result column="src_id" jdbcType="INTEGER" property="srcId" />
    <result column="creative_id" jdbcType="INTEGER" property="creativeId" />
    <result column="crm_account_id" jdbcType="INTEGER" property="crmAccountId" />
    <result column="crm_account_name" jdbcType="VARCHAR" property="crmAccountName" />
    <result column="crm_contract_id" jdbcType="INTEGER" property="crmContractId" />
    <result column="crm_contract_name" jdbcType="VARCHAR" property="crmContractName" />
    <result column="crm_order_id" jdbcType="INTEGER" property="crmOrderId" />
    <result column="crm_order_resource_type" jdbcType="TINYINT" property="crmOrderResourceType" />
    <result column="crm_order_discount" jdbcType="TINYINT" property="crmOrderDiscount" />
    <result column="crm_order_status" jdbcType="TINYINT" property="crmOrderStatus" />
    <result column="expected_cost" jdbcType="DECIMAL" property="expectedCost" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, date, account_id, account_name, order_id, order_name, schedule_id, schedule_name, 
    total_impression, expected_visible_pv, slot_group_id, slot_group_name, card_type_names, 
    visible_pv, visible_uv, click, cost, cpm, unit_id, creative_number, campaign_id, 
    ctime, mtime, ctr, cpc, ecpm, schedule_consumption_progress, src_id, creative_id, 
    crm_account_id, crm_account_name, crm_contract_id, crm_contract_name, crm_order_id, 
    crm_order_resource_type, crm_order_discount, crm_order_status, expected_cost
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from report_day_gd
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from report_day_gd
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from report_day_gd
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPoExample">
    delete from report_day_gd
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into report_day_gd (date, account_id, account_name, 
      order_id, order_name, schedule_id, 
      schedule_name, total_impression, expected_visible_pv, 
      slot_group_id, slot_group_name, card_type_names, 
      visible_pv, visible_uv, click, 
      cost, cpm, unit_id, 
      creative_number, campaign_id, ctime, 
      mtime, ctr, cpc, 
      ecpm, schedule_consumption_progress, src_id, 
      creative_id, crm_account_id, crm_account_name, 
      crm_contract_id, crm_contract_name, crm_order_id, 
      crm_order_resource_type, crm_order_discount, 
      crm_order_status, expected_cost)
    values (#{date,jdbcType=DATE}, #{accountId,jdbcType=INTEGER}, #{accountName,jdbcType=VARCHAR}, 
      #{orderId,jdbcType=INTEGER}, #{orderName,jdbcType=VARCHAR}, #{scheduleId,jdbcType=INTEGER}, 
      #{scheduleName,jdbcType=VARCHAR}, #{totalImpression,jdbcType=INTEGER}, #{expectedVisiblePv,jdbcType=INTEGER}, 
      #{slotGroupId,jdbcType=INTEGER}, #{slotGroupName,jdbcType=VARCHAR}, #{cardTypeNames,jdbcType=VARCHAR}, 
      #{visiblePv,jdbcType=INTEGER}, #{visibleUv,jdbcType=INTEGER}, #{click,jdbcType=INTEGER}, 
      #{cost,jdbcType=DECIMAL}, #{cpm,jdbcType=DECIMAL}, #{unitId,jdbcType=INTEGER}, 
      #{creativeNumber,jdbcType=INTEGER}, #{campaignId,jdbcType=INTEGER}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{ctr,jdbcType=DECIMAL}, #{cpc,jdbcType=DECIMAL}, 
      #{ecpm,jdbcType=DECIMAL}, #{scheduleConsumptionProgress,jdbcType=DECIMAL}, #{srcId,jdbcType=INTEGER}, 
      #{creativeId,jdbcType=INTEGER}, #{crmAccountId,jdbcType=INTEGER}, #{crmAccountName,jdbcType=VARCHAR}, 
      #{crmContractId,jdbcType=INTEGER}, #{crmContractName,jdbcType=VARCHAR}, #{crmOrderId,jdbcType=INTEGER}, 
      #{crmOrderResourceType,jdbcType=TINYINT}, #{crmOrderDiscount,jdbcType=TINYINT}, 
      #{crmOrderStatus,jdbcType=TINYINT}, #{expectedCost,jdbcType=DECIMAL})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into report_day_gd
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="date != null">
        date,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="accountName != null">
        account_name,
      </if>
      <if test="orderId != null">
        order_id,
      </if>
      <if test="orderName != null">
        order_name,
      </if>
      <if test="scheduleId != null">
        schedule_id,
      </if>
      <if test="scheduleName != null">
        schedule_name,
      </if>
      <if test="totalImpression != null">
        total_impression,
      </if>
      <if test="expectedVisiblePv != null">
        expected_visible_pv,
      </if>
      <if test="slotGroupId != null">
        slot_group_id,
      </if>
      <if test="slotGroupName != null">
        slot_group_name,
      </if>
      <if test="cardTypeNames != null">
        card_type_names,
      </if>
      <if test="visiblePv != null">
        visible_pv,
      </if>
      <if test="visibleUv != null">
        visible_uv,
      </if>
      <if test="click != null">
        click,
      </if>
      <if test="cost != null">
        cost,
      </if>
      <if test="cpm != null">
        cpm,
      </if>
      <if test="unitId != null">
        unit_id,
      </if>
      <if test="creativeNumber != null">
        creative_number,
      </if>
      <if test="campaignId != null">
        campaign_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="ctr != null">
        ctr,
      </if>
      <if test="cpc != null">
        cpc,
      </if>
      <if test="ecpm != null">
        ecpm,
      </if>
      <if test="scheduleConsumptionProgress != null">
        schedule_consumption_progress,
      </if>
      <if test="srcId != null">
        src_id,
      </if>
      <if test="creativeId != null">
        creative_id,
      </if>
      <if test="crmAccountId != null">
        crm_account_id,
      </if>
      <if test="crmAccountName != null">
        crm_account_name,
      </if>
      <if test="crmContractId != null">
        crm_contract_id,
      </if>
      <if test="crmContractName != null">
        crm_contract_name,
      </if>
      <if test="crmOrderId != null">
        crm_order_id,
      </if>
      <if test="crmOrderResourceType != null">
        crm_order_resource_type,
      </if>
      <if test="crmOrderDiscount != null">
        crm_order_discount,
      </if>
      <if test="crmOrderStatus != null">
        crm_order_status,
      </if>
      <if test="expectedCost != null">
        expected_cost,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="date != null">
        #{date,jdbcType=DATE},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderName != null">
        #{orderName,jdbcType=VARCHAR},
      </if>
      <if test="scheduleId != null">
        #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="scheduleName != null">
        #{scheduleName,jdbcType=VARCHAR},
      </if>
      <if test="totalImpression != null">
        #{totalImpression,jdbcType=INTEGER},
      </if>
      <if test="expectedVisiblePv != null">
        #{expectedVisiblePv,jdbcType=INTEGER},
      </if>
      <if test="slotGroupId != null">
        #{slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="slotGroupName != null">
        #{slotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeNames != null">
        #{cardTypeNames,jdbcType=VARCHAR},
      </if>
      <if test="visiblePv != null">
        #{visiblePv,jdbcType=INTEGER},
      </if>
      <if test="visibleUv != null">
        #{visibleUv,jdbcType=INTEGER},
      </if>
      <if test="click != null">
        #{click,jdbcType=INTEGER},
      </if>
      <if test="cost != null">
        #{cost,jdbcType=DECIMAL},
      </if>
      <if test="cpm != null">
        #{cpm,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeNumber != null">
        #{creativeNumber,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctr != null">
        #{ctr,jdbcType=DECIMAL},
      </if>
      <if test="cpc != null">
        #{cpc,jdbcType=DECIMAL},
      </if>
      <if test="ecpm != null">
        #{ecpm,jdbcType=DECIMAL},
      </if>
      <if test="scheduleConsumptionProgress != null">
        #{scheduleConsumptionProgress,jdbcType=DECIMAL},
      </if>
      <if test="srcId != null">
        #{srcId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="crmAccountId != null">
        #{crmAccountId,jdbcType=INTEGER},
      </if>
      <if test="crmAccountName != null">
        #{crmAccountName,jdbcType=VARCHAR},
      </if>
      <if test="crmContractId != null">
        #{crmContractId,jdbcType=INTEGER},
      </if>
      <if test="crmContractName != null">
        #{crmContractName,jdbcType=VARCHAR},
      </if>
      <if test="crmOrderId != null">
        #{crmOrderId,jdbcType=INTEGER},
      </if>
      <if test="crmOrderResourceType != null">
        #{crmOrderResourceType,jdbcType=TINYINT},
      </if>
      <if test="crmOrderDiscount != null">
        #{crmOrderDiscount,jdbcType=TINYINT},
      </if>
      <if test="crmOrderStatus != null">
        #{crmOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="expectedCost != null">
        #{expectedCost,jdbcType=DECIMAL},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPoExample" resultType="java.lang.Long">
    select count(*) from report_day_gd
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update report_day_gd
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.date != null">
        date = #{record.date,jdbcType=DATE},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.accountName != null">
        account_name = #{record.accountName,jdbcType=VARCHAR},
      </if>
      <if test="record.orderId != null">
        order_id = #{record.orderId,jdbcType=INTEGER},
      </if>
      <if test="record.orderName != null">
        order_name = #{record.orderName,jdbcType=VARCHAR},
      </if>
      <if test="record.scheduleId != null">
        schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      </if>
      <if test="record.scheduleName != null">
        schedule_name = #{record.scheduleName,jdbcType=VARCHAR},
      </if>
      <if test="record.totalImpression != null">
        total_impression = #{record.totalImpression,jdbcType=INTEGER},
      </if>
      <if test="record.expectedVisiblePv != null">
        expected_visible_pv = #{record.expectedVisiblePv,jdbcType=INTEGER},
      </if>
      <if test="record.slotGroupId != null">
        slot_group_id = #{record.slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.slotGroupName != null">
        slot_group_name = #{record.slotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.cardTypeNames != null">
        card_type_names = #{record.cardTypeNames,jdbcType=VARCHAR},
      </if>
      <if test="record.visiblePv != null">
        visible_pv = #{record.visiblePv,jdbcType=INTEGER},
      </if>
      <if test="record.visibleUv != null">
        visible_uv = #{record.visibleUv,jdbcType=INTEGER},
      </if>
      <if test="record.click != null">
        click = #{record.click,jdbcType=INTEGER},
      </if>
      <if test="record.cost != null">
        cost = #{record.cost,jdbcType=DECIMAL},
      </if>
      <if test="record.cpm != null">
        cpm = #{record.cpm,jdbcType=DECIMAL},
      </if>
      <if test="record.unitId != null">
        unit_id = #{record.unitId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeNumber != null">
        creative_number = #{record.creativeNumber,jdbcType=INTEGER},
      </if>
      <if test="record.campaignId != null">
        campaign_id = #{record.campaignId,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.ctr != null">
        ctr = #{record.ctr,jdbcType=DECIMAL},
      </if>
      <if test="record.cpc != null">
        cpc = #{record.cpc,jdbcType=DECIMAL},
      </if>
      <if test="record.ecpm != null">
        ecpm = #{record.ecpm,jdbcType=DECIMAL},
      </if>
      <if test="record.scheduleConsumptionProgress != null">
        schedule_consumption_progress = #{record.scheduleConsumptionProgress,jdbcType=DECIMAL},
      </if>
      <if test="record.srcId != null">
        src_id = #{record.srcId,jdbcType=INTEGER},
      </if>
      <if test="record.creativeId != null">
        creative_id = #{record.creativeId,jdbcType=INTEGER},
      </if>
      <if test="record.crmAccountId != null">
        crm_account_id = #{record.crmAccountId,jdbcType=INTEGER},
      </if>
      <if test="record.crmAccountName != null">
        crm_account_name = #{record.crmAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.crmContractId != null">
        crm_contract_id = #{record.crmContractId,jdbcType=INTEGER},
      </if>
      <if test="record.crmContractName != null">
        crm_contract_name = #{record.crmContractName,jdbcType=VARCHAR},
      </if>
      <if test="record.crmOrderId != null">
        crm_order_id = #{record.crmOrderId,jdbcType=INTEGER},
      </if>
      <if test="record.crmOrderResourceType != null">
        crm_order_resource_type = #{record.crmOrderResourceType,jdbcType=TINYINT},
      </if>
      <if test="record.crmOrderDiscount != null">
        crm_order_discount = #{record.crmOrderDiscount,jdbcType=TINYINT},
      </if>
      <if test="record.crmOrderStatus != null">
        crm_order_status = #{record.crmOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="record.expectedCost != null">
        expected_cost = #{record.expectedCost,jdbcType=DECIMAL},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update report_day_gd
    set id = #{record.id,jdbcType=INTEGER},
      date = #{record.date,jdbcType=DATE},
      account_id = #{record.accountId,jdbcType=INTEGER},
      account_name = #{record.accountName,jdbcType=VARCHAR},
      order_id = #{record.orderId,jdbcType=INTEGER},
      order_name = #{record.orderName,jdbcType=VARCHAR},
      schedule_id = #{record.scheduleId,jdbcType=INTEGER},
      schedule_name = #{record.scheduleName,jdbcType=VARCHAR},
      total_impression = #{record.totalImpression,jdbcType=INTEGER},
      expected_visible_pv = #{record.expectedVisiblePv,jdbcType=INTEGER},
      slot_group_id = #{record.slotGroupId,jdbcType=INTEGER},
      slot_group_name = #{record.slotGroupName,jdbcType=VARCHAR},
      card_type_names = #{record.cardTypeNames,jdbcType=VARCHAR},
      visible_pv = #{record.visiblePv,jdbcType=INTEGER},
      visible_uv = #{record.visibleUv,jdbcType=INTEGER},
      click = #{record.click,jdbcType=INTEGER},
      cost = #{record.cost,jdbcType=DECIMAL},
      cpm = #{record.cpm,jdbcType=DECIMAL},
      unit_id = #{record.unitId,jdbcType=INTEGER},
      creative_number = #{record.creativeNumber,jdbcType=INTEGER},
      campaign_id = #{record.campaignId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      ctr = #{record.ctr,jdbcType=DECIMAL},
      cpc = #{record.cpc,jdbcType=DECIMAL},
      ecpm = #{record.ecpm,jdbcType=DECIMAL},
      schedule_consumption_progress = #{record.scheduleConsumptionProgress,jdbcType=DECIMAL},
      src_id = #{record.srcId,jdbcType=INTEGER},
      creative_id = #{record.creativeId,jdbcType=INTEGER},
      crm_account_id = #{record.crmAccountId,jdbcType=INTEGER},
      crm_account_name = #{record.crmAccountName,jdbcType=VARCHAR},
      crm_contract_id = #{record.crmContractId,jdbcType=INTEGER},
      crm_contract_name = #{record.crmContractName,jdbcType=VARCHAR},
      crm_order_id = #{record.crmOrderId,jdbcType=INTEGER},
      crm_order_resource_type = #{record.crmOrderResourceType,jdbcType=TINYINT},
      crm_order_discount = #{record.crmOrderDiscount,jdbcType=TINYINT},
      crm_order_status = #{record.crmOrderStatus,jdbcType=TINYINT},
      expected_cost = #{record.expectedCost,jdbcType=DECIMAL}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPo">
    update report_day_gd
    <set>
      <if test="date != null">
        date = #{date,jdbcType=DATE},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="accountName != null">
        account_name = #{accountName,jdbcType=VARCHAR},
      </if>
      <if test="orderId != null">
        order_id = #{orderId,jdbcType=INTEGER},
      </if>
      <if test="orderName != null">
        order_name = #{orderName,jdbcType=VARCHAR},
      </if>
      <if test="scheduleId != null">
        schedule_id = #{scheduleId,jdbcType=INTEGER},
      </if>
      <if test="scheduleName != null">
        schedule_name = #{scheduleName,jdbcType=VARCHAR},
      </if>
      <if test="totalImpression != null">
        total_impression = #{totalImpression,jdbcType=INTEGER},
      </if>
      <if test="expectedVisiblePv != null">
        expected_visible_pv = #{expectedVisiblePv,jdbcType=INTEGER},
      </if>
      <if test="slotGroupId != null">
        slot_group_id = #{slotGroupId,jdbcType=INTEGER},
      </if>
      <if test="slotGroupName != null">
        slot_group_name = #{slotGroupName,jdbcType=VARCHAR},
      </if>
      <if test="cardTypeNames != null">
        card_type_names = #{cardTypeNames,jdbcType=VARCHAR},
      </if>
      <if test="visiblePv != null">
        visible_pv = #{visiblePv,jdbcType=INTEGER},
      </if>
      <if test="visibleUv != null">
        visible_uv = #{visibleUv,jdbcType=INTEGER},
      </if>
      <if test="click != null">
        click = #{click,jdbcType=INTEGER},
      </if>
      <if test="cost != null">
        cost = #{cost,jdbcType=DECIMAL},
      </if>
      <if test="cpm != null">
        cpm = #{cpm,jdbcType=DECIMAL},
      </if>
      <if test="unitId != null">
        unit_id = #{unitId,jdbcType=INTEGER},
      </if>
      <if test="creativeNumber != null">
        creative_number = #{creativeNumber,jdbcType=INTEGER},
      </if>
      <if test="campaignId != null">
        campaign_id = #{campaignId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="ctr != null">
        ctr = #{ctr,jdbcType=DECIMAL},
      </if>
      <if test="cpc != null">
        cpc = #{cpc,jdbcType=DECIMAL},
      </if>
      <if test="ecpm != null">
        ecpm = #{ecpm,jdbcType=DECIMAL},
      </if>
      <if test="scheduleConsumptionProgress != null">
        schedule_consumption_progress = #{scheduleConsumptionProgress,jdbcType=DECIMAL},
      </if>
      <if test="srcId != null">
        src_id = #{srcId,jdbcType=INTEGER},
      </if>
      <if test="creativeId != null">
        creative_id = #{creativeId,jdbcType=INTEGER},
      </if>
      <if test="crmAccountId != null">
        crm_account_id = #{crmAccountId,jdbcType=INTEGER},
      </if>
      <if test="crmAccountName != null">
        crm_account_name = #{crmAccountName,jdbcType=VARCHAR},
      </if>
      <if test="crmContractId != null">
        crm_contract_id = #{crmContractId,jdbcType=INTEGER},
      </if>
      <if test="crmContractName != null">
        crm_contract_name = #{crmContractName,jdbcType=VARCHAR},
      </if>
      <if test="crmOrderId != null">
        crm_order_id = #{crmOrderId,jdbcType=INTEGER},
      </if>
      <if test="crmOrderResourceType != null">
        crm_order_resource_type = #{crmOrderResourceType,jdbcType=TINYINT},
      </if>
      <if test="crmOrderDiscount != null">
        crm_order_discount = #{crmOrderDiscount,jdbcType=TINYINT},
      </if>
      <if test="crmOrderStatus != null">
        crm_order_status = #{crmOrderStatus,jdbcType=TINYINT},
      </if>
      <if test="expectedCost != null">
        expected_cost = #{expectedCost,jdbcType=DECIMAL},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.report.biz.po.ReportDayGdPo">
    update report_day_gd
    set date = #{date,jdbcType=DATE},
      account_id = #{accountId,jdbcType=INTEGER},
      account_name = #{accountName,jdbcType=VARCHAR},
      order_id = #{orderId,jdbcType=INTEGER},
      order_name = #{orderName,jdbcType=VARCHAR},
      schedule_id = #{scheduleId,jdbcType=INTEGER},
      schedule_name = #{scheduleName,jdbcType=VARCHAR},
      total_impression = #{totalImpression,jdbcType=INTEGER},
      expected_visible_pv = #{expectedVisiblePv,jdbcType=INTEGER},
      slot_group_id = #{slotGroupId,jdbcType=INTEGER},
      slot_group_name = #{slotGroupName,jdbcType=VARCHAR},
      card_type_names = #{cardTypeNames,jdbcType=VARCHAR},
      visible_pv = #{visiblePv,jdbcType=INTEGER},
      visible_uv = #{visibleUv,jdbcType=INTEGER},
      click = #{click,jdbcType=INTEGER},
      cost = #{cost,jdbcType=DECIMAL},
      cpm = #{cpm,jdbcType=DECIMAL},
      unit_id = #{unitId,jdbcType=INTEGER},
      creative_number = #{creativeNumber,jdbcType=INTEGER},
      campaign_id = #{campaignId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      ctr = #{ctr,jdbcType=DECIMAL},
      cpc = #{cpc,jdbcType=DECIMAL},
      ecpm = #{ecpm,jdbcType=DECIMAL},
      schedule_consumption_progress = #{scheduleConsumptionProgress,jdbcType=DECIMAL},
      src_id = #{srcId,jdbcType=INTEGER},
      creative_id = #{creativeId,jdbcType=INTEGER},
      crm_account_id = #{crmAccountId,jdbcType=INTEGER},
      crm_account_name = #{crmAccountName,jdbcType=VARCHAR},
      crm_contract_id = #{crmContractId,jdbcType=INTEGER},
      crm_contract_name = #{crmContractName,jdbcType=VARCHAR},
      crm_order_id = #{crmOrderId,jdbcType=INTEGER},
      crm_order_resource_type = #{crmOrderResourceType,jdbcType=TINYINT},
      crm_order_discount = #{crmOrderDiscount,jdbcType=TINYINT},
      crm_order_status = #{crmOrderStatus,jdbcType=TINYINT},
      expected_cost = #{expectedCost,jdbcType=DECIMAL}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>