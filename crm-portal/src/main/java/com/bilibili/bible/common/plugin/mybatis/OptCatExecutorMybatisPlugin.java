package com.bilibili.bible.common.plugin.mybatis;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.util.ValUtil;
import com.bilibili.crm.platform.portal.config.database.MybatisConfProp;
import com.bilibili.crm.platform.support.DbDataSourceHolder;
import com.bilibili.crm.platform.support.metrics.Metrics;
import com.bilibili.crm.platform.support.metrics.MetricsNames;
import com.bilibili.sycpb.plugin.sycpb.accesslog.enums.AccessLogFileld;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import org.apache.ibatis.executor.Executor;
import org.apache.ibatis.mapping.*;
import org.apache.ibatis.plugin.*;
import org.apache.ibatis.reflection.MetaObject;
import org.apache.ibatis.session.Configuration;
import org.apache.ibatis.session.ResultHandler;
import org.apache.ibatis.session.RowBounds;
import org.apache.ibatis.type.TypeHandlerRegistry;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import java.sql.DatabaseMetaData;
import java.sql.SQLException;
import java.text.DateFormat;
import java.util.*;
import java.util.regex.Matcher;

/**
 * 原基础上，把sql和耗时打印到日志
 */
@Intercepts({@Signature(
        method = "query",
        type = Executor.class,
        args = {MappedStatement.class, Object.class, RowBounds.class, ResultHandler.class}
), @Signature(
        method = "update",
        type = Executor.class,
        args = {MappedStatement.class, Object.class}
)})
public class OptCatExecutorMybatisPlugin implements Interceptor {
    private static final Logger log = LoggerFactory.getLogger(OptCatExecutorMybatisPlugin.class);
    private ISpliceSqlParams spliceSqlParamsByReplace;
    private ISpliceSqlParams spliceSqlParamsBySplice;
    public static final int SQL_FORMAT_SIZE = 400;

    @Autowired
    private MybatisConfProp prop;
    @Autowired
    private OpenTelemetry openTelemetry;
    @Autowired
    private PaladinConfig paladinConfig;
    @Autowired
    private SqlHelper sqlHelper;

    public OptCatExecutorMybatisPlugin() {
        log.info("=====> OptCatExecutorMybatisPlugin construct ...");
        this.spliceSqlParamsByReplace = new SpliceSqlParamsByReplace();
        this.spliceSqlParamsBySplice = new SpliceSqlParamsBySplice();
    }

    public Object intercept(Invocation invocation) throws Throwable {
        Executor target = (Executor) invocation.getTarget();
        MappedStatement mappedStatement = (MappedStatement) invocation.getArgs()[0];
        String[] strArr = mappedStatement.getId().split("\\.");
        String methodName = strArr[strArr.length - 2] + "." + strArr[strArr.length - 1];

        Transaction t = Cat.newTransaction("SQL", methodName);
        t.addData(AccessLogFileld.req_trace_id.name(), MDC.get(AccessLogFileld.req_trace_id.name()));

        Object parameter = null;
        if (invocation.getArgs().length > 1) {
            parameter = invocation.getArgs()[1];
        }

        BoundSql boundSql = mappedStatement.getBoundSql(parameter);

        // 影子链路，替换表名
        if (DbDataSourceHolder.isShadowTableEnabled()
                || paladinConfig.switchOn(HotKey.enableGlobalShadowTable)) {

            String table = sqlHelper.getTableName(boundSql.getSql(), false, true);
            if (! paladinConfig.getListString(HotKey.shadowTableBlackList).contains(table)) {

                boundSql = sqlHelper.buildShadowBoundSql(boundSql, mappedStatement);
                mappedStatement = sqlHelper.buildShadowMappedStatement(boundSql, mappedStatement);
                invocation.getArgs()[0] = mappedStatement;
            } else {

                // 命中白名单，需要操作线上表，校验是否是写操作
                if (sqlHelper.isWriteOperation(boundSql.getSql())) {
                    throw new RuntimeException(String.format("影子链路不支持写入线上表: %s", table));
                }
            }
        }

        Configuration configuration = mappedStatement.getConfiguration();
        String sql = this.showSql(configuration, boundSql);
        SqlCommandType sqlCommandType = mappedStatement.getSqlCommandType();
        Cat.logEvent("SQL.Method", sqlCommandType.name().toLowerCase(), "0", sql);
        DatabaseMetaData metaData = this.getDatabaseUrl(target);
        String jdbcUrl = "";
        String userName = "";
        if (metaData != null) {
            jdbcUrl = metaData.getURL();
            userName = metaData.getUserName();
            try {
                userName = userName.split("@")[0];
            } catch (Exception e) {
                userName = metaData.getUserName();
            }
        }
        Cat.logEvent("SQL.Database", jdbcUrl);
        Object returnObj = null;

        this.forbiddenWrite(sql);

        // 绘制db耗时graph
        String reqTraceId = ValUtil.getVal(MDC.get(AccessLogFileld.req_trace_id.name()), Utils.getNow().getTime() + "");
        Tracer tracer = openTelemetry.getTracer(methodName);
        Span span = tracer.spanBuilder(methodName)
                // 可添加自定义键值对
                .setAttribute("sql", sql)
                .setAttribute("table", getTable(sql))
                .setAttribute("username", userName)
                .setAttribute("req_trace_id", reqTraceId)
                .setAttribute("jdbc_url", jdbcUrl)
                .startSpan();

        try (Scope ignored = span.makeCurrent()) {
            long start = System.currentTimeMillis();
            returnObj = invocation.proceed();
            long cost = System.currentTimeMillis() - start;
            String table = getTable(sql);
            long objLen = getObjLen(returnObj);
            //复用accesslog的字段
            MDC.put(AccessLogFileld.req_cost.name(), Long.toString(cost));
            MDC.put("jdbc_url", jdbcUrl);
            MDC.put("table", table);
            MDC.put("username", userName);
            MDC.put("res_len", Long.toString(objLen));
            MDC.put("cat", Cat.getCurrentMessageId());
            span.setAttribute("res_len", Long.toString(objLen));
            if (objLen < 0) {
                AlarmHelper.log("UnKnowObj", returnObj == null ? "null" : returnObj.getClass());
            }
            if (objLen > prop.getDbRetLen()) {
                AlarmHelper.log("returnBig", sql);
            }
            metricLog(jdbcUrl, table, cost, objLen, userName);
            if (cost > prop.getLogIfCostMillGt()) {
                AlarmHelper.log(String.format("slow_sql='%s", sql));
            }
            t.setStatus("0");
        } catch (Exception e) {
            span.setStatus(StatusCode.ERROR).recordException(e);
            Cat.logError(e);
            throw new RuntimeException(e);
        } finally {
            span.end();
            MDC.remove(AccessLogFileld.req_cost.name());
            MDC.remove("jdbc_url");
            MDC.remove("table");
            MDC.remove("username");
            MDC.remove("res_len");
            MDC.remove("cat");
            t.complete();
        }

        return returnObj;
    }

    private void metricLog(String jdbcUrl, String table, long cost, long objLen, String userName) {
        try {
            AlarmHelper.log("DbQuery");
            Metrics.qpsAndTime(MetricsNames.DB, "sql", jdbcUrl, cost, "table", table);
            Metrics.gauge(MetricsNames.DB_LEN, "db_query", objLen, "table", table, "jdbc_url", jdbcUrl);

            // mysql IO量
            Metrics.many(MetricsNames.DB_IO, "res_len", objLen, "table", table, "user_name", userName);

            // show_sql数量
            if (cost > prop.getLogIfCostMillGt()) {
                Metrics.one(MetricsNames.DB_SHOW_SQL, "show_sql", "table", table, "user_name", userName);
            }
        } catch (Exception e) {
            AlarmHelper.alarmEx("MetricException", e);
        }
    }

    private String getTable(String sql) {
        try {
            String lowCaseSql = sql.toLowerCase();
            String prefix = "";
            String table = "unKnow";
            if (lowCaseSql.contains("select ")) {
                int fromIndex = lowCaseSql.indexOf(" from ");
                int endIndex = lowCaseSql.indexOf(" ", fromIndex + 6);
                prefix = "select_";
                if (endIndex < 0) {
                    table = sql.substring(fromIndex + 6);
                } else {
                    table = sql.substring(fromIndex + 6, endIndex).trim();
                }
            } else if (lowCaseSql.contains("insert ")) {
                int fromIndex = lowCaseSql.indexOf(" into ");
                int endIndex = lowCaseSql.indexOf(" ", fromIndex + 6);
                prefix = "insert_";
                table = sql.substring(fromIndex + 6, endIndex).trim();
            } else if (lowCaseSql.contains("update ")) {
                int fromIndex = lowCaseSql.indexOf("update ");
                int endIndex = lowCaseSql.indexOf(" ", fromIndex + 7);
                prefix = "update_";
                table = sql.substring(fromIndex + 6, endIndex).trim();
            } else if (lowCaseSql.contains("delete ")) {
                int fromIndex = lowCaseSql.indexOf(" from ");
                int endIndex = lowCaseSql.indexOf(" ", fromIndex + 6);
                table = sql.substring(fromIndex + 6, endIndex).trim();
                prefix = "delete_";
            }
            return prefix + table;
        } catch (Exception e) {
            AlarmHelper.alarmEx("LogTableException", e, sql);
            return "unKnow";
        }
    }

    private void forbiddenWrite(String sql) throws Exception {
        if (! paladinConfig.switchOn(HotKey.forbiddenWriteTableSwitch)) {
            return;
        }

        String table = sqlHelper.getTableName(sql, true, false);
        if (paladinConfig.getListString(HotKey.forbiddenWriteTable).contains(table)) {
            log.warn("触发禁写: {}", table);
            throw new RuntimeException(String.format("触发禁写: %s", table));
        }

    }

    @SuppressWarnings("all")
    private long getObjLen(Object res) {
        if (res == null) {
            return -1;
        }
        if (res instanceof Collection) {
            return ((Collection) res).size();
        } else if (res instanceof Integer) {
            return (Integer) res;
        }
        return -1;
    }

    private DatabaseMetaData getDatabaseUrl(Executor target) {
        org.apache.ibatis.transaction.Transaction transaction = target.getTransaction();
        if (transaction == null) {
            log.error("Could not find transaction on target [{}]", target);
            return null;
        } else {
            try {
                return transaction.getConnection().getMetaData();
            } catch (SQLException var4) {
                log.error("Could not get database metadata on target [{}]", target);
                return null;
            }
        }
    }

    public String showSql(Configuration configuration, BoundSql boundSql) {
        Object parameterObject = boundSql.getParameterObject();
        List<ParameterMapping> parameterMappings = boundSql.getParameterMappings();
        String sql = boundSql.getSql().replaceAll("[\\s]+", " ");
        if (parameterMappings.size() > 0 && parameterObject != null) {
            TypeHandlerRegistry typeHandlerRegistry = configuration.getTypeHandlerRegistry();
            if (typeHandlerRegistry.hasTypeHandler(parameterObject.getClass())) {
                sql = sql.replaceFirst("\\?", Matcher.quoteReplacement(getParameterValue(parameterObject)));
            } else {
                MetaObject metaObject = configuration.newMetaObject(parameterObject);
                if (parameterMappings.size() > 400) {
                    sql = this.spliceSqlParamsBySplice.spliceParamsAndBoundSql(boundSql, parameterMappings, sql, metaObject);
                } else {
                    sql = this.spliceSqlParamsByReplace.spliceParamsAndBoundSql(boundSql, parameterMappings, sql, metaObject);
                }
            }
        }

        return sql;
    }

    public static String getParameterValue(Object obj) {
        String value = null;
        if (obj instanceof String) {
            value = "'" + obj.toString() + "'";
        } else if (obj instanceof Date) {
            DateFormat formatter = DateFormat.getDateTimeInstance(2, 2, Locale.CHINA);
            value = "'" + formatter.format((Date) obj) + "'";
        } else if (obj != null) {
            value = obj.toString();
        } else {
            value = "";
        }

        return value;
    }

    public Object plugin(Object target) {
        return target instanceof Executor ? Plugin.wrap(target, this) : target;
    }

    public void setProperties(Properties properties) {
    }

}
