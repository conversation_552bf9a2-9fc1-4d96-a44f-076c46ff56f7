package com.bilibili.bible.common.plugin.mybatis;

import lombok.extern.slf4j.Slf4j;
import net.sf.jsqlparser.parser.CCJSqlParserManager;
import net.sf.jsqlparser.schema.Table;
import net.sf.jsqlparser.statement.Statement;
import net.sf.jsqlparser.statement.delete.Delete;
import net.sf.jsqlparser.statement.insert.Insert;
import net.sf.jsqlparser.statement.select.*;
import net.sf.jsqlparser.statement.update.Update;
import org.apache.ibatis.mapping.BoundSql;
import org.apache.ibatis.mapping.MappedStatement;
import org.apache.ibatis.mapping.SqlSource;
import org.springframework.stereotype.Component;

import java.io.StringReader;
import java.lang.reflect.Field;
import java.util.Map;

@Component
@Slf4j
public class SqlHelper {

    private static final String SHADOW_TABLE_PREFIX = "bili_shadow_";
    private final CCJSqlParserManager parserManager = new CCJSqlParserManager();

    /**
     * 获取SQL中的表名
     *
     * @param sql 原始查询sql
     * @param onlyWrite 是否只获取写操作的表名
     * @param isThrowExp 解析失败是否抛出异常
     *                   弱依赖，如禁写，有dba禁写兜底，解析失败不应该抛异常
     *                   强依赖，如影子链路，解析失败会污染线上数据，应该抛异常
     * @return
     * @throws Exception
     */
    public String getTableName(String sql, boolean onlyWrite, boolean isThrowExp) throws Exception {
        String lowCaseSql = sql.toLowerCase();
        String table = "unKnow";
        try {
            Statement stmt = parserManager.parse(new StringReader(lowCaseSql));
            if (!onlyWrite && stmt instanceof Select) {
                SelectBody selectBody = ((Select) stmt).getSelectBody();
                if (selectBody instanceof PlainSelect) {
                    FromItem fromItem = ((PlainSelect) selectBody).getFromItem();
                    if (fromItem instanceof Table) {
                        table = ((Table) fromItem).getName();
                    }
                }
            } else if (stmt instanceof Insert) {
                Insert insertStatement = (Insert) stmt;
                table = insertStatement.getTable().getName();
            } else if (stmt instanceof Update) {
                Update updateStatement = (Update) stmt;
                table = updateStatement.getTable().getName();
            } else if (stmt instanceof Delete) {
                Delete deleteStatement = (Delete) stmt;
                table = deleteStatement.getTable().getName();
            }
        } catch (Exception e) {
            log.error("getTableName 解析SQL失败", e);
            if (isThrowExp) {
                throw e;
            }
        }

        return table;
    }

    private String parseSubSelectTable(FromItem fromItem) {
        if (fromItem == null) return "unKnow";

        SubSelect subSelect = (SubSelect) fromItem;
        if (subSelect.getSelectBody() == null) return "unKnow";

        SelectBody subSelectBody = subSelect.getSelectBody();
        if (subSelectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) subSelectBody;
            FromItem subFromItem = plainSelect.getFromItem();

            if (subFromItem instanceof Table) {
                return ((Table) subFromItem).getName();
            } else if (subFromItem instanceof SubSelect) {
                // 递归处理嵌套的子查询
                return parseSubSelectTable(subFromItem);
            }
        }

        throw new RuntimeException("不支持的 SubSelectBody 类型：" + subSelectBody.getClass().getName());
    }

    public boolean isWriteOperation(String sql) throws Exception {
        String lowCaseSql = sql.toLowerCase();
        try {
            Statement stmt = parserManager.parse(new StringReader(lowCaseSql));
            return stmt instanceof Insert || stmt instanceof Update || stmt instanceof Delete;
        } catch (Exception e) {
            log.error("getTableName 解析SQL失败", e);
            throw e;
        }
    }

    public BoundSql buildShadowBoundSql(BoundSql boundSql, MappedStatement mappedStatement) {
        String originalSql = boundSql.getSql();

        // 动态替换表名
        String modifiedSql = this.replaceTableNames(originalSql);

        // 创建新的 BoundSql
        BoundSql newBoundSql = new BoundSql(
                mappedStatement.getConfiguration(),
                modifiedSql,
                boundSql.getParameterMappings(),
                boundSql.getParameterObject());

        // 使用反射复制 parameters
        this.copyParameters(boundSql, newBoundSql);

        return newBoundSql;
    }

    public MappedStatement buildShadowMappedStatement(BoundSql newBoundSql,
                                                      MappedStatement mappedStatement) {
        // 创建新的 MappedStatement
        return this.copyFromMappedStatement(mappedStatement, new ModifiedSqlSource(newBoundSql));
    }

    private String replaceTableNames(String sql) {
        try {
            Statement stmt = parserManager.parse(new StringReader(sql));

            if (stmt instanceof Select) {
                replaceTablesInSelectBody(((Select) stmt).getSelectBody());
            } else if (stmt instanceof Update) {
                Update updateStatement = (Update) stmt;
                updateStatement.getTable().setName(SHADOW_TABLE_PREFIX + updateStatement.getTable().getName());
            } else if (stmt instanceof Insert) {
                Insert insertStatement = (Insert) stmt;
                insertStatement.getTable().setName(SHADOW_TABLE_PREFIX + insertStatement.getTable().getName());
            } else if (stmt instanceof Delete) {
                Delete deleteStatement = (Delete) stmt;
                deleteStatement.getTable().setName(SHADOW_TABLE_PREFIX + deleteStatement.getTable().getName());
            }

            return stmt.toString();
        } catch (Exception e) {
            log.error("replaceTableNames 解析SQL失败, SQL: {}", sql, e);
            throw new RuntimeException("SQL解析异常: " + e.getMessage());
        }
    }

    private void replaceTablesInSelectBody(SelectBody selectBody) {
        if (selectBody instanceof PlainSelect) {
            PlainSelect plainSelect = (PlainSelect) selectBody;

            // 简单替换
            replaceTableNameInFromItem(plainSelect.getFromItem());

            // 带join语句的替换
            if (plainSelect.getJoins() != null) {
                for (Join join : plainSelect.getJoins()) {
                    replaceTableNameInFromItem(join.getRightItem());
                }
            }
        } else {
            throw new RuntimeException("不支持的 SelectBody 类型：" + selectBody.getClass().getName());
        }
    }

    private void replaceTableNameInFromItem(FromItem fromItem) {
        if (fromItem == null) return;

        if (fromItem instanceof Table) {
            Table table = (Table) fromItem;
            table.setName(SHADOW_TABLE_PREFIX + table.getName());
        } else if (fromItem instanceof SubSelect) {
            SubSelect subSelect = (SubSelect) fromItem;
            if (subSelect.getSelectBody() != null) {
                replaceTablesInSelectBody(subSelect.getSelectBody());
            }
        } else {
            throw new RuntimeException("replaceTableNameInFromItem 不支持的 FromItem 类型：" + fromItem.getClass().getName());
        }
    }

    private MappedStatement copyFromMappedStatement(MappedStatement ms, SqlSource newSqlSource) {
        MappedStatement.Builder builder = new MappedStatement.Builder(
                ms.getConfiguration(),
                ms.getId(),
                newSqlSource,
                ms.getSqlCommandType()
        );
        // 显式覆盖构造函数默认值
        builder.statementType(ms.getStatementType())
                .resultSetType(ms.getResultSetType())
                .parameterMap(ms.getParameterMap())
                .resultMaps(ms.getResultMaps());
        // 其他属性
        builder.resource(ms.getResource())
                .fetchSize(ms.getFetchSize())
                .keyGenerator(ms.getKeyGenerator())
                .keyProperty(ms.getKeyProperties() != null ?
                        String.join(",", ms.getKeyProperties()) : null) // 3.5+ 需要数组转字符串
                .keyColumn(ms.getKeyColumns() != null ?
                        String.join(",", ms.getKeyColumns()) : null)
                .timeout(ms.getTimeout())
                .cache(ms.getCache())
                .flushCacheRequired(ms.isFlushCacheRequired())
                .useCache(ms.isUseCache())
                .resultOrdered(ms.isResultOrdered())
                .lang(ms.getLang())
                .databaseId(ms.getDatabaseId())
                .resultSets(ms.getResultSets() != null ?
                        String.join(",", ms.getResultSets()) : null) // 处理 resultSets
        ;

        return builder.build();
    }

    private void copyParameters(BoundSql originalBoundSql, BoundSql newBoundSql) {

        // 复制additionalParameters
        try {
            Field field = BoundSql.class.getDeclaredField("additionalParameters");
            field.setAccessible(true);
            Map<String, Object> additionalParameters = (Map<String, Object>) field.get(originalBoundSql);

            for (Map.Entry<String, Object> entry : additionalParameters.entrySet()) {
                newBoundSql.setAdditionalParameter(entry.getKey(), entry.getValue());
            }
        } catch (Exception e) {
            log.warn("复制额外参数失败", e);
        }

        // 复制 metaParameters
        try {
            Field metaParamsField = BoundSql.class.getDeclaredField("metaParameters");
            metaParamsField.setAccessible(true);
            Object metaParams = metaParamsField.get(originalBoundSql);
            if (metaParams != null) {
                metaParamsField.set(newBoundSql, metaParams);
            }
        } catch (Exception e) {
            log.warn("复制元数据参数失败", e);
        }
    }

    // 辅助类：用于提供新的 SqlSource
    public static class ModifiedSqlSource implements SqlSource {
        private final BoundSql boundSql;

        public ModifiedSqlSource(BoundSql boundSql) {
            this.boundSql = boundSql;
        }

        @Override
        public BoundSql getBoundSql(Object parameterObject) {
            return boundSql;
        }
    }

}
