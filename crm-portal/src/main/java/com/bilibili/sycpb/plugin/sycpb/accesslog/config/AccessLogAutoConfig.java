package com.bilibili.sycpb.plugin.sycpb.accesslog.config;

import com.bilibili.crm.platform.support.CrmAccessLogFilter;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.AccessLogFilter;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.AccessLogMethodAspect;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.WarpDatabusSubAspect;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.extend.AccesLogIJobHandlerAspect;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.fieldsAspect.AccessLogCodeAspect;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.fieldsAspect.AccessLogExceptionHandlerAspect;
import com.bilibili.sycpb.plugin.sycpb.accesslog.aop.fieldsAspect.AccessLogModuleAspect;
import com.bilibili.sycpb.plugin.sycpb.accesslog.config.trace.AccessLogOkHttpClientConfig;
import com.bilibili.sycpb.plugin.sycpb.accesslog.config.trace.AccessLogTraceSoaExporterConfig;
import com.bilibili.sycpb.plugin.sycpb.accesslog.config.trace.AccessLogTraceSoaInvokerConfig;
import com.bilibili.sycpb.plugin.sycpb.accesslog.core.AccessLogGlobalPolicy;
import com.bilibili.sycpb.plugin.sycpb.accesslog.core.policy.DefaultAccessLogGlobalPolicy;
import com.bilibili.sycpb.plugin.sycpb.accesslog.util.AccessLogContextUtil;
import okhttp3.OkHttpClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.autoconfigure.condition.ConditionalOnBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnClass;
import org.springframework.boot.autoconfigure.condition.ConditionalOnMissingBean;
import org.springframework.boot.autoconfigure.condition.ConditionalOnProperty;
import org.springframework.boot.web.servlet.FilterRegistrationBean;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Import;
import org.springframework.remoting.httpinvoker.HttpInvokerProxyFactoryBean;
import org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter;

@Import({AccessLogGrpcClientConfig.class, AccessLogGrpcServerConfig.class})
public class AccessLogAutoConfig {
    private static final Logger log = LoggerFactory.getLogger(AccessLogAutoConfig.class);

    public AccessLogAutoConfig() {
    }

    @Bean
    public AccessLogProperties accessLogProperties() {
        return new AccessLogProperties();
    }

    @Bean
    public AccessLogStaticProperties accessLogStaticProperties() {
        return new AccessLogStaticProperties();
    }

    @Bean
    @ConditionalOnMissingBean({AccessLogContextUtil.class})
    public AccessLogContextUtil accessLogGlobalConfig() {
        return new AccessLogContextUtil();
    }

    @Bean
    @ConditionalOnMissingBean({AccessLogGlobalPolicy.class})
    public AccessLogGlobalPolicy accessLogGlobalPolicy() {
        return new DefaultAccessLogGlobalPolicy();
    }

    @Bean
    @ConditionalOnMissingBean({AccessLogModuleAspect.class})
    public AccessLogModuleAspect logModuleAspect() {
        return new AccessLogModuleAspect();
    }

    @Bean
    @ConditionalOnMissingBean({AccessLogCodeAspect.class})
    public AccessLogCodeAspect accessLogCodeAspect() {
        return new AccessLogCodeAspect();
    }

    @Bean
    @ConditionalOnMissingBean({AccessLogMethodAspect.class})
    public AccessLogMethodAspect accessLogMethodAspect() {
        return new AccessLogMethodAspect();
    }

    // 已经在FilterConfig配置过了，这里注释掉
//    @Bean
//    @ConditionalOnMissingBean({CrmAccessLogFilter.class})
//    public CrmAccessLogFilter accessLogFilter() {
//        return new CrmAccessLogFilter();
//    }

    @Bean
    @ConditionalOnMissingBean({AccessLogExceptionHandlerAspect.class})
    public AccessLogExceptionHandlerAspect accessLogExceptionHandlerAspect() {
        return new AccessLogExceptionHandlerAspect();
    }

    // 已经在FilterConfig配置过了，这里注释掉
//    @Bean
//    @ConditionalOnBean({AccessLogFilter.class})
//    public FilterRegistrationBean<AccessLogFilter> accessLogFilterFilterRegistrationBean(AccessLogFilter filter) {
//        FilterRegistrationBean<AccessLogFilter> registrationBean = new FilterRegistrationBean();
//        registrationBean.setFilter(filter);
//        registrationBean.setOrder(2147483637);
//        registrationBean.addUrlPatterns(new String[]{"/*"});
//        return registrationBean;
//    }

    @Bean
    @ConditionalOnClass(
            name = {"com.xxl.job.core.handler.IJobHandler"}
    )
    @ConditionalOnProperty(
            name = {"accesslog.accesLogIJobHandlerAspect.enable"},
            matchIfMissing = true,
            havingValue = "true"
    )
    @ConditionalOnMissingBean({AccesLogIJobHandlerAspect.class})
    public AccesLogIJobHandlerAspect accesLogIJobHandlerAspect() {
        log.info("accessLogAutoConfig accesLogIJobHandlerAspect init");
        return new AccesLogIJobHandlerAspect();
    }

    @Bean
    @ConditionalOnClass(
            name = {"com.bilibili.warp.databus.MessageListener"}
    )
    @ConditionalOnProperty(
            name = {"accesslog.warpDatabusSubAspect.enable"},
            matchIfMissing = true,
            havingValue = "true"
    )
    @ConditionalOnMissingBean({WarpDatabusSubAspect.class})
    public WarpDatabusSubAspect warpDatabusSubAspect() {
        log.info("accessLogAutoConfig warpDatabusSubAspect init");
        return new WarpDatabusSubAspect();
    }

    @Bean
    @ConditionalOnBean({OkHttpClient.class})
    public AccessLogOkHttpClientConfig accessLogOkHttpClientConfig() {
        return new AccessLogOkHttpClientConfig();
    }

    @Bean
//    @ConditionalOnBean({HttpInvokerServiceExporter.class})
    public AccessLogTraceSoaExporterConfig accessLogTraceSoaExporterConfig() {
        return new AccessLogTraceSoaExporterConfig();
    }

    @Bean
//    @ConditionalOnBean({HttpInvokerProxyFactoryBean.class})
    public AccessLogTraceSoaInvokerConfig accessLogTraceSoaInvokerConfig() {
        return new AccessLogTraceSoaInvokerConfig();
    }
}
