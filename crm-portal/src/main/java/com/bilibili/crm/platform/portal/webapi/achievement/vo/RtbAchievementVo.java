package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RtbAchievementVo {

    @ApiModelProperty(notes = "代理商id")
    private Integer agent_id;

    @ApiModelProperty(notes = "代理商账号id")
    private Integer agent_account_id;

    @ApiModelProperty(notes = "代理商名称")
    private String agent_name;

//    @ApiModelProperty(notes = "公司组id")
//    private Integer company_id;
//
//    @ApiModelProperty(notes = "公司组名称")
//    private String company_name;


    @ApiModelProperty(notes = "品牌id")
    private Integer product_id;

    @ApiModelProperty(notes = "品牌名称名称")
    private String product_name;



    @ApiModelProperty(notes = "广告主id")
    private Integer account_id;

    @ApiModelProperty(notes = "广告主名称")
    private String account_name;

    @ApiModelProperty(notes = "直客")
    private String straight_name;

    @ApiModelProperty(notes = "渠道")
    private String channel_name;

    @ApiModelProperty(notes = "运营")
    private String operate_name;

    @ApiModelProperty(notes = "集团")
    private String group_name;

    @ApiModelProperty(notes = "整体总消耗")
    private BigDecimal total_consume;

    @ApiModelProperty(notes = "现金总消耗")
    private BigDecimal cash;

    @ApiModelProperty(notes = "返货总消耗")
    private BigDecimal red_packet;

    @ApiModelProperty(notes = "专项返货总消耗")
    private BigDecimal special_red_packet;

    @ApiModelProperty(notes = "同比(元)")
    private BigDecimal weekday_compare;

    @ApiModelProperty(notes = "同比%")
    private BigDecimal weekday_compare_rate;

    @ApiModelProperty(notes = "环比(元)")
    private BigDecimal day_compare;

    @ApiModelProperty(notes = "环比%")
    private BigDecimal day_compare_rate;

    @ApiModelProperty("在线创意数")
    private Integer online_creative;

    @ApiModelProperty("新增创意数")
    private Integer new_creative;

    @ApiModelProperty(notes = "展示")
    private BigDecimal show_amount;

    @ApiModelProperty(notes = "点击")
    private BigDecimal click_amount;

    @ApiModelProperty(notes = "CTR")
    private BigDecimal CTR;

    @ApiModelProperty(notes = "CPC")
    private BigDecimal CPC;

    @ApiModelProperty(notes = "eCPM")
    private BigDecimal eCPM;


    @ApiModelProperty(notes = "是否表头")
    private Integer is_collect;


}
