package com.bilibili.crm.platform.portal.webapi.wxapp.vo.effect;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/10/19 7:54 下午
 */
@Data
public class EffectRealtimePanelCashVo {
    /**
     * 单位
     */
    @ApiModelProperty("cpc 单位")
    private String cpc_unit_desc;

    /**
     * cpc
     */
    @ApiModelProperty("cpc")
    private BigDecimal cpc;

    /**
     * 昨天cpc
     */
    @ApiModelProperty("昨天cpc")
    private BigDecimal pre_cpc;

    /**
     * 上周cpc
     */
    @ApiModelProperty("上周cpc")
    private BigDecimal last_cpc;

    /**
     * 环比
     */
    @ApiModelProperty("cpc 环比")
    private BigDecimal pre_period_ratio_cpc;

    /**
     * 同比
     */
    @ApiModelProperty("cpc 同比")
    private BigDecimal last_same_period_ratio_cpc;

    @ApiModelProperty("ecpm 单位")
    private String ecpm_unit_desc;

    /**
     * ecpm
     */
    @ApiModelProperty("ecpm")
    private BigDecimal ecpm;

    @ApiModelProperty("昨天ecpm")
    private BigDecimal pre_ecpm;

    @ApiModelProperty("上周ecpm")
    private BigDecimal last_ecpm;

    /**
     * 环比
     */
    @ApiModelProperty("ecpm 环比")
    private BigDecimal pre_period_ratio_ecpm;

    /**
     * 同比
     */
    @ApiModelProperty("ecpm 同比")
    private BigDecimal last_same_period_ratio_ecpm;

    /**
     * ctr
     */
    @ApiModelProperty("ctr")
    private BigDecimal ctr;

    @ApiModelProperty("pre_ctr")
    private BigDecimal pre_ctr;

    @ApiModelProperty("last_ctr")
    private BigDecimal last_ctr;

    /**
     * 环比
     */
    @ApiModelProperty("ctr 环比")
    private BigDecimal pre_period_ratio_ctr;

    /**
     * 同比
     */
    @ApiModelProperty("ctr 同比")
    private BigDecimal last_same_period_ratio_ctr;

    public static EffectRealtimePanelCashVo init() {
        EffectRealtimePanelCashVo incomeVo = new EffectRealtimePanelCashVo();
        incomeVo.setCpc_unit_desc("元");
        incomeVo.setCpc(BigDecimal.ZERO);
        incomeVo.setPre_period_ratio_cpc(BigDecimal.ZERO);
        incomeVo.setLast_same_period_ratio_cpc(BigDecimal.ZERO);
        incomeVo.setCtr(BigDecimal.ZERO);
        incomeVo.setPre_period_ratio_ctr(BigDecimal.ZERO);
        incomeVo.setLast_same_period_ratio_ctr(BigDecimal.ZERO);
        incomeVo.setEcpm(BigDecimal.ZERO);
        incomeVo.setPre_period_ratio_ecpm(BigDecimal.ZERO);
        incomeVo.setLast_same_period_ratio_ecpm(BigDecimal.ZERO);
        return incomeVo;
    }
}
