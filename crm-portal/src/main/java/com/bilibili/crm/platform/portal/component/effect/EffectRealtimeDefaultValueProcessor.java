package com.bilibili.crm.platform.portal.component.effect;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.dss.api.dict.EffectRealtimeQueryDataType;
import com.bilibili.dss.api.dto.effect.EffectRealtimeBaseQueryDto;
import com.bilibili.dss.api.dto.effect.dimension.EffectRealtimeDimensionQueryDto;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * 效果实时默认值处理器
 *
 * <AUTHOR>
 * @date 2020/10/20 5:46 下午
 */
@Component
public class EffectRealtimeDefaultValueProcessor {

    /**
     * 设置默认值
     *
     * @param queryDto
     */
    public void setDefaultValues(EffectRealtimeBaseQueryDto queryDto) {
        if (queryDto.getDate() != null) {
            queryDto.setDateTimestamp(new Timestamp(queryDto.getDate()));
        } else {
            queryDto.setDateTimestamp(Utils.getNow());
        }
        queryDto.setDate(queryDto.getDateTimestamp().getTime());
        queryDto.setFormatQueryDate(CrmUtils.formatDateWithoutSplit(queryDto.getDateTimestamp()));

        if (queryDto instanceof EffectRealtimeDimensionQueryDto) {
            queryDto.setQueryDataTypeEnum(EffectRealtimeQueryDataType.DIMENSION);
        } else {
            queryDto.setQueryDataTypeEnum(EffectRealtimeQueryDataType.HOUR);
        }
    }
}
