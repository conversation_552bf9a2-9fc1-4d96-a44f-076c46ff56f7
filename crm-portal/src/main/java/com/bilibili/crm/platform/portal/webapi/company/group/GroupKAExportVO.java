package com.bilibili.crm.platform.portal.webapi.company.group;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * ka集团的导出
 *
 * <AUTHOR>
 * date 2023/10/27 17:17.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupKAExportVO implements Serializable {

    private static final long serialVersionUID = 6265180553373344387L;

    @ExcelResources(title = "集团id")
    @ApiModelProperty(notes = "集团id")
    private Integer group_id;

    @ExcelResources(title = "集团名称")
    @ApiModelProperty(notes = "集团名称")
    private String group_name;

    @ExcelResources(title = "集团状态")
    @ApiModelProperty(notes = "集团状态")
    private String group_status;

    @ExcelResources(title = "当前层级标签")
    @ApiModelProperty(notes = "当前层级标签")
    private String now_ka_label;

    @ExcelResources(title = "标签类型")
    @ApiModelProperty(notes = "标签类型")
    private String all_ka_label;

    @ExcelResources(title = "子标签")
    @ApiModelProperty(notes = "子标签")
    private String ska_sub_tag;

    @ExcelResources(title = "KA标签生效开始时间")
    @ApiModelProperty(notes = "KA标签生效开始时间")
    private String ka_begin_time;


    @ExcelResources(title = "KA标签生效结束时间")
    @ApiModelProperty(notes = "KA标签生效结束时间")
    private String ka_end_time;
}

