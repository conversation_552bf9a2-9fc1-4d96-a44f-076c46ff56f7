package com.bilibili.crm.platform.portal.webapi.oa;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.contract.dto.ContractBillDto;
import com.bilibili.crm.platform.api.finance.dto.*;
import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentBatchUploadDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentUploadDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CrmOaReceiptSaveDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CrmOaReceiptsDto;
import com.bilibili.crm.platform.api.finance.enums.InvoiceBizTypeEnum;
import com.bilibili.crm.platform.api.oa.dto.*;
import com.bilibili.crm.platform.api.oa.service.IOaService;
import com.bilibili.crm.platform.biz.service.oa.component.IContractBillInfoQuerier;
import com.bilibili.crm.platform.biz.util.ExcelReadUtils;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.portal.common.OssAttachmentVo;
import com.bilibili.crm.platform.portal.convertor.ContractVoConvertor;
import com.bilibili.crm.platform.portal.convertor.oa.OaVoConvertor;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.contract.vo.ContractBillInfoVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.*;
import com.bilibili.crm.platform.portal.webapi.oa.vo.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.io.Files;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.io.FileUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.support.DefaultMessageSourceResolvable;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;
import java.io.File;
import java.io.IOException;
import java.io.OutputStream;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * OA 相关 controller
 *
 * <AUTHOR>
 * @date 2021/2/20 2:06 下午
 */
@Slf4j
@Controller
@RequestMapping("/web_api/v1/oa")
@Api(value = "oa", description = "oa相关")
public class OaController extends BaseRestfulController {

    public static final String INVOICE_TARGET_TYPE_ENUM_KEY = "invoice_target_type_enum";
    public static final String INVOICE_TYPE_ENUM_KEY = "invoice_type_enum";
    public static final String COMMA = ",";
    public static final String CONTENT_DISPOSITION_KEY = "Content-Disposition";
    public static final String DOWNLOAD_KEY = "attachment";
    public static final String INLINE_KEY = "inline";

    private static final Integer LIMIT_SIZE = 301;

    @Autowired
    private IOaService oaService;

    @Resource
    private IContractBillInfoQuerier iContractBillInfoQuerier;

    @ResponseBody
    @ApiOperation(value = "检查多个合同是否可以进行开票")
    @RequestMapping(value = "/check_contracts_bill_info/contract", method = RequestMethod.GET)
    public Response<OaBillSituationInfoVo> checkContractsBillInfo(@ApiIgnore Context context, OaContractBillPreInfoQueryVo oaBillPreInfoQueryVo)
            throws ServiceException {
        OaContractBillPreInfoQueryDto oaBillPreInfoQueryDto = OaVoConvertor.convertContractPreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setDomainAccountName(context.getUsername());
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceSource(SystemType.CRM.getCode());
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceBizType(InvoiceBizTypeEnum.CONTRACT.getCode());
        OaBillSituationInfoDto oaBillSituationInfoDto = oaService.checkContractsBillInfo(oaBillPreInfoQueryDto);
        OaBillSituationInfoVo billSituationInfoVo =
                OaVoConvertor.convertBillSituationInfoDto2Vo(oaBillSituationInfoDto);
        return Response.SUCCESS(billSituationInfoVo);
    }

    @Deprecated // @远书 确认前端没有调用
    @ResponseBody
    @ApiOperation(value = "检查多个合同的返点核算开票信息【1777608】")
    @RequestMapping(value = "/check_contracts_bill_info/rebate_check", method = RequestMethod.POST)
    public Response<OaBillSituationInfoVo> checkRebateCheckContractsBillInfo(@ApiIgnore Context context,
                                                                             @RequestBody OaRebateCheckContractBillVo oaRebateCheckContractBillVo)
            throws ServiceException {
        OaRebateCheckContractBillDto oaRebateCheckContractBillDto = OaVoConvertor.convertOaRebateCheckContractBillVo2Dto(oaRebateCheckContractBillVo);
        OaBillSituationInfoDto oaBillSituationInfoDto = oaService.checkRebateCheckContractsBillInfo(oaRebateCheckContractBillDto);
        OaBillSituationInfoVo billSituationInfoVo =
                OaVoConvertor.convertBillSituationInfoDto2Vo(oaBillSituationInfoDto);
        return Response.SUCCESS(billSituationInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "查询合同开票的预置信息")
    @RequestMapping(value = "/bill_pre_info/contract", method = RequestMethod.GET)
    public Response<OaContractBillPreInfoVo> queryContractOaBillPreInfo(@ApiIgnore Context context, OaContractBillPreInfoQueryVo oaBillPreInfoQueryVo)
            throws ServiceException {
        OaContractBillPreInfoQueryDto oaBillPreInfoQueryDto = OaVoConvertor.convertContractPreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setDomainAccountName(context.getUsername());
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceSource(SystemType.CRM.getCode());
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceBizType(InvoiceBizTypeEnum.CONTRACT.getCode());
        OaContractBillPreInfoDto oaBillPreInfoDto = oaService.queryOaContractBillPreInfo(oaBillPreInfoQueryDto);
        OaContractBillPreInfoVo oaBillPreInfoVo = OaVoConvertor.converContractBillPreInfoDto2Vo(oaBillPreInfoDto);
        return Response.SUCCESS(oaBillPreInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "查询充值开票的预置信息")
    @RequestMapping(value = "/bill_pre_info/recharge", method = RequestMethod.GET)
    public Response<OaRechargeBillPreInfoVo> queryRechargeOaBillPreInfo(@ApiIgnore Context context,
                                                                        OaRechargeBillPreInfoQueryVo oaBillPreInfoQueryVo)
            throws ServiceException {
        OaRechargeBillPreInfoQueryDto oaRechargeBillPreInfoQueryDto =
                OaVoConvertor.convertRechargePreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setDomainAccountName(context.getUsername());
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceSource(SystemType.CRM.getCode());
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceBizType(InvoiceBizTypeEnum.RECHARGE.getCode());
        OaRechargeBillPreInfoDto oaRechargeBillPreInfoDto = oaService.queryOaRechargeBillPreInfo(oaRechargeBillPreInfoQueryDto);
        OaRechargeBillPreInfoVo oaRechargeBillPreInfoVo = OaVoConvertor.convertRechargeBillPreInfoDto2Vo(oaRechargeBillPreInfoDto);
        return Response.SUCCESS(oaRechargeBillPreInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "查询授信充值开票的预置信息")
    @RequestMapping(value = "/bill_pre_info/credit_recharge", method = RequestMethod.GET)
    public Response<OaRechargeBillPreInfoVo> queryCreditRechargeOaBillPreInfo(@ApiIgnore Context context,
                                                                              OaRechargeBillPreInfoQueryVo oaBillPreInfoQueryVo) {
        OaRechargeBillPreInfoQueryDto oaRechargeBillPreInfoQueryDto =
                OaVoConvertor.convertRechargePreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setDomainAccountName(context.getUsername());
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceBizType(InvoiceBizTypeEnum.CREDIT_RECHARGE.getCode());
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceSource(SystemType.CRM.getCode());
        OaRechargeBillPreInfoDto oaRechargeBillPreInfoDto = oaService.queryOaCreditRechargeBillPreInfo(oaRechargeBillPreInfoQueryDto);
        OaRechargeBillPreInfoVo oaRechargeBillPreInfoVo = OaVoConvertor.convertRechargeBillPreInfoDto2Vo(oaRechargeBillPreInfoDto);
        return Response.SUCCESS(oaRechargeBillPreInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "批量新增授信充值开票")
    @RequestMapping(value = "/batch/invoice/credit_recharge", method = RequestMethod.POST)
    public Response createBatchCreditRechargeInvoice(@ApiIgnore Context context,
                                                     @ApiParam("批量新增授信开票参数") @Valid @RequestBody RechargeBatchInvoiceCreateVo batchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        Operator operator = super.getOperator(context);
        RechargeBatchInvoiceCreateDto batchInvoiceCreateDto = OaVoConvertor.convertRechargeBatchInvoiceCreateVo2Dto(batchInvoiceCreateVo);
        batchInvoiceCreateDto.setInvoiceBizType(InvoiceBizTypeEnum.CREDIT_RECHARGE.getCode());
        batchInvoiceCreateDto.setInvoice_source(SystemType.CRM.getCode());
        oaService.createCreditRechargeOaInvoiceBatch(batchInvoiceCreateDto, operator);
        return Response.SUCCESS(null);
    }


    @ResponseBody
    @ApiOperation(value = "查询花火订单开票的预置信息")
    @RequestMapping(value = "/bill_pre_info/pickup", method = RequestMethod.GET)
    public Response<OaPickupBillPreInfoVo> queryPickupOaBillPreInfo(@ApiIgnore Context context,
                                                                    OaPickupBillPreInfoQueryVo oaBillPreInfoQueryVo)
            throws ServiceException {
        OaPickupBillPreInfoQueryDto oaRechargeBillPreInfoQueryDto =
                OaVoConvertor.convertPickupPreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setDomainAccountName(context.getUsername());
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceSource(SystemType.CRM.getCode());
        oaRechargeBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceBizType(InvoiceBizTypeEnum.PICK_UP_ORDER.getCode());

        OaPickupBillPreInfoDto oaRechargeBillPreInfoDto =
                oaService.queryOaPickupBillPreInfo(oaRechargeBillPreInfoQueryDto);
        OaPickupBillPreInfoVo oaRechargeBillPreInfoVo =
                OaVoConvertor.convertPickupBillPreInfoDto2Vo(oaRechargeBillPreInfoDto);
        return Response.SUCCESS(oaRechargeBillPreInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "查询结算单开票的预置信息")
    @RequestMapping(value = "/bill_pre_info/statement", method = RequestMethod.GET)
    public Response<OaStatementBillPreInfoVo> queryStatementOaBillPreInfo(@ApiIgnore Context context,
                                                                          OaStatementBillPreInfoQueryVo oaBillPreInfoQueryVo) throws ServiceException {
        OaStatementBillPreInfoQueryDto queryDto = OaVoConvertor.convertStatementPreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        queryDto.setSystemType(SystemType.CRM.getCode());
        queryDto.setDomainAccountName(context.getUsername());

        OaPickupStatementBillPreInfoDto preInfoDto = oaService.queryOaStatementBillPreInfo(queryDto);
        OaStatementBillPreInfoVo oaRechargeBillPreInfoVo = OaVoConvertor.convertStatementBillPreInfoDto2Vo(preInfoDto);
        return Response.SUCCESS(oaRechargeBillPreInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "批量新增OA结算单开票")
    @RequestMapping(value = "/batch/invoice/statement", method = RequestMethod.POST)
    public Response createBatchStatementInvoice(@ApiIgnore Context context,
                                                @ApiParam("批量新增开票参数") @Valid @RequestBody StatementBatchInvoiceCreateVo batchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        StatementBatchInvoiceCreateDto batchInvoiceCreateDto = OaVoConvertor.convertStatementBatchInvoiceCreateVo2Dto(batchInvoiceCreateVo);
        batchInvoiceCreateDto.setInvoice_source(SystemType.CRM.getCode());
        oaService.createStatementOaInvoiceBatch(batchInvoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "批量新增OA返现开票")
    @RequestMapping(value = "/batch/invoice/return_money", method = RequestMethod.POST)
    public Response createBatchReturnMoneyInvoice(@ApiIgnore Context context,
                                                  @ApiParam("批量新增开票参数") @Valid @RequestBody ReturnMoneyBatchInvoiceCreateVo batchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(DefaultMessageSourceResolvable::getDefaultMessage).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        ReturnMoneyBatchInvoiceCreateDto batchInvoiceCreateDto = OaVoConvertor.convertReturnMoneyBatchInvoiceCreateVo2Dto(batchInvoiceCreateVo);
        batchInvoiceCreateDto.setInvoice_source(SystemType.CRM.getCode());
        oaService.createReturnMoneyOaInvoiceBatch(batchInvoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "获取返点开票的预置信息【1777608】")
    @RequestMapping(value = "/bill_pre_info/contract_rebate_check", method = RequestMethod.POST)
    public Response<OaContractRebateCheckBillPreInfoVo> queryContractRebateCheckOaBillPreInfo(@ApiIgnore Context context,
                                                                                              @RequestBody OaContractRebateCheckBillPreInfoQueryVo oaBillPreInfoQueryVo)
            throws ServiceException {
        OaContractRebateCheckBillPreInfoQueryDto oaBillPreInfoQueryDto =
                OaVoConvertor.convertContractRebateCheckPreInfoQueryVo2Dto(oaBillPreInfoQueryVo);
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setDomainAccountName(context.getUsername());
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceSource(SystemType.CRM.getCode());
        oaBillPreInfoQueryDto.getOaBillPreInfoQueryDto().setInvoiceBizType(InvoiceBizTypeEnum.REBATE_CHECK.getCode());

        OaRebateCheckBillPreInfoDto oaBillPreInfoDto = oaService.queryContractRebateCheckOaBillPreInfo(oaBillPreInfoQueryDto);
        List<Integer> signProjectIds = Optional.ofNullable(oaBillPreInfoDto.getRebateCheckInfoForOaDtos()).orElse(new ArrayList<>()).stream().map(ContractBillDto::getSignProjectId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Assert.isTrue(signProjectIds.size() <= 1, "您提交的合同属于多个我方主体，请分主体提交");
        OaContractRebateCheckBillPreInfoVo oaBillPreInfoVo = OaVoConvertor.converRebateCheckBillPreInfoDto2Vo(oaBillPreInfoDto);
        return Response.SUCCESS(oaBillPreInfoVo);
    }

    //    @Security("OaController_bindOaContract")
    @ResponseBody
    @ApiOperation(value = "绑定OA合同/结算单")
    @RequestMapping(value = "/contract/bind", method = RequestMethod.POST)
    public Response bindOaContract(@ApiIgnore Context context, @ApiParam("绑定OA合同/结算单参数") @Valid @RequestBody ContractBindingVo contractBindingVo) {
        ContractBindingDto contractBindingDto =
                ContractBindingDto.builder()
                        .crmContractId(contractBindingVo.getCrm_contract_id())
                        .oaFlowNo(contractBindingVo.getOa_flow_no())
                        .oaContractNo(contractBindingVo.getOa_contract_no())
                        .build();
        oaService.bindOaContract(contractBindingDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "绑定OA合同预览")
    @RequestMapping(value = "/contract/bind/preview", method = RequestMethod.GET)
    public Response<OaContractPreviewVo> bindOaContractView(@ApiParam("OA流程编号") @RequestParam(value = "oa_flow_no", required = false) String oaFlowNo
            , @ApiParam("OA合同编号") @RequestParam(value = "oa_contract_no", required = false) String oaContractNo) {
        OaContractPreviewDto oaContractPreviewDto = oaService.queryOaContractPreview(oaFlowNo, oaContractNo);
        OaContractPreviewVo result = new OaContractPreviewVo();
        result.setOa_contract_no(oaContractPreviewDto.getOa_contract_no());
        result.setParty_a(oaContractPreviewDto.getJf().getXm());
        result.setParty_b(oaContractPreviewDto.getYf().getXm());
        return Response.SUCCESS(result);
    }

    //    @Security("OaController_queryOaContractDetail")
    @ResponseBody
    @ApiOperation(value = "获取OA合同明细")
    @RequestMapping(value = "/contract/detail", method = RequestMethod.GET)
    public Response<List<OaContractDetailVo>> queryOaContractDetail(@ApiParam("CRM合同id") @RequestParam(value = "crm_contract_id", required = false) Integer crmContractId) {
        List<OaContractDetailDto> oaContractDetailDtoList = oaService.queryOaContractDetail(crmContractId);
        List<OaContractDetailVo> oaContractDetailVoList = OaVoConvertor.convertOaContractDetailDto2Vo(oaContractDetailDtoList);
        return Response.SUCCESS(oaContractDetailVoList);
    }

    @ResponseBody
    @ApiOperation(value = "新增合同开票")
    @RequestMapping(value = "/invoice/contract", method = RequestMethod.POST)
    @Deprecated
    public Response createContractOaInvoice(@ApiIgnore Context context,
                                            @ApiParam("新增OA开票参数") @Valid @RequestBody ContractInvoiceCreateVo invoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        ContractInvoiceCreateDto invoiceCreateDto = OaVoConvertor.convertContractInvoiceCreateVo2Dto(invoiceCreateVo);
        oaService.createOaContractInvoice(invoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "批量新增OA合同开票")
    @RequestMapping(value = "/batch/invoice/contract", method = RequestMethod.POST)
    public Response createBatchContractInvoice(@ApiIgnore Context context,
                                               @ApiParam("批量新增开票参数") @Valid @RequestBody ContractBatchInvoiceCreateVo batchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }

        ContractBatchInvoiceCreateDto batchInvoiceCreateDto = OaVoConvertor.convertBatchContractInvoiceCreateVo2Dto(batchInvoiceCreateVo);
        oaService.createContractOaInvoiceBatch(batchInvoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "批量新增OA充值开票")
    @RequestMapping(value = "/batch/invoice/recharge", method = RequestMethod.POST)
    public Response createBatchRechargeInvoice(@ApiIgnore Context context,
                                               @ApiParam("批量新增开票参数") @Valid @RequestBody RechargeBatchInvoiceCreateVo batchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        RechargeBatchInvoiceCreateDto batchInvoiceCreateDto = OaVoConvertor.convertRechargeBatchInvoiceCreateVo2Dto(batchInvoiceCreateVo);
        // 开票来源
        batchInvoiceCreateDto.setInvoice_source(SystemType.CRM.getCode());
        oaService.createRechargeOaInvoiceBatch(batchInvoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "新增 oa 返点核算开票【1777608】")
    @RequestMapping(value = "/batch/invoice/contract_rebate_check", method = RequestMethod.POST)
    public Response createBatchContractRebateCheckInvoice(@ApiIgnore Context context,
                                                          @ApiParam("批量新增开票参数") @Valid @RequestBody ContractRebateCheckBatchInvoiceCreateVo batchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }

        ContractRebateCheckBatchInvoiceCreateDto batchInvoiceCreateDto = OaVoConvertor.convertBatchContractRebateCheckInvoiceCreateVo2Dto(batchInvoiceCreateVo);
        oaService.createContractRebateCheckOaInvoiceBatch(batchInvoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "新增 oa 返点核算开票信息导入")
    @RequestMapping(value = "/batch/invoice/contract_rebate_import", method = RequestMethod.POST)
    public Response<List<ContractBillInfoVo>> importContract(@ApiIgnore Context context, @RequestParam(value = "file", required = false) MultipartFile file) throws Exception {
        List<String> fileResult = ExcelReadUtils.readExcel(file.getOriginalFilename(), file.getBytes(), 0);
        if (CollectionUtils.isEmpty(fileResult)) {
            return Response.SUCCESS(new ArrayList<>());
        }
        Assert.isTrue(fileResult.size() <= LIMIT_SIZE, "至多支持上传300条数据，请删减后上传");
        List<ContractBillInfoVo> invoiceVoList = new ArrayList<>();
        Set<String> billCompanySet = new HashSet<>();
        for (int i = 1; i < fileResult.size(); i++) {
            String rowData = fileResult.get(i);
            Assert.isTrue(!StringUtils.isEmpty(rowData), String.format("第%s行数据为空", i + 1));
            String[] rowDataList = rowData.split(",");
            Assert.isTrue(rowDataList.length >= 2, String.format("第%s行信息填充不全", i + 1));
            Assert.isTrue(StringUtils.isNumeric(rowDataList[0]), String.format("第%s行数据合同号不合法【%s】", i + 1, rowDataList[0]));
            ContractBillDto contractBillDto;
            try {
                contractBillDto = iContractBillInfoQuerier.queryContractBillInfo(Long.parseLong(rowDataList[0]));
            } catch (Exception e) {
                throw new RuntimeException(String.format("第%s行数据合同号不合法【%s】:", i + 1, rowDataList[0]) + e.getMessage());
            }
            ContractBillInfoVo contractBillInfoVo = ContractVoConvertor.convertContractBillDto2Vo(contractBillDto);
            billCompanySet.add(contractBillInfoVo.getBill_company());
            contractBillInfoVo.setThe_to_be_invoice_amount(new BigDecimal(rowDataList[1]));
            invoiceVoList.add(contractBillInfoVo);
        }
        Assert.isTrue(billCompanySet.size() <= 1, "您提交的合同属于多个我方主体，请分主体提交");
        return Response.SUCCESS(invoiceVoList);
    }


    @ResponseBody
    @ApiOperation(value = "批量新增OA花火订单开票")
    @RequestMapping(value = "/batch/invoice/pickUpOrder", method = RequestMethod.POST)
    public Response createBatchPickUpOrderInvoice(@ApiIgnore Context context,
                                                  @ApiParam("批量新增开票参数") @Valid @RequestBody PickUpOrderBatchInvoiceCreateVo pickUpOrderBatchInvoiceCreateVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        PickupBatchInvoiceCreateDto batchInvoiceCreateDto =
                OaVoConvertor.convertPickupBatchInvoiceCreateVo2Dto(pickUpOrderBatchInvoiceCreateVo);
        oaService.createPickupOaInvoiceBatch(batchInvoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "OA附件上传")
    @RequestMapping(value = "/invoice/attachment/upload", method = RequestMethod.POST)
    public Response<OaAttachmentUploadInfoVo> uploadInvoiceAttachment(@ApiIgnore Context context, @RequestParam(value = "file", required = false) MultipartFile file) {
        if (null == file || file.isEmpty()) {
            throw new ServiceRuntimeException("未上传文件");
        }
        File tempDir = Files.createTempDir();
        File tmpFile = new File(tempDir, file.getOriginalFilename());
        try {
            file.transferTo(tmpFile);
        } catch (IOException e) {
            throw new ServiceRuntimeException("文件读取错误");
        }
        OaAttachmentUploadInfoVo result;
        try {
            OaAttachmentUploadInfoDto oaAttachmentUploadInfoDto = oaService.uploadAttachment(tmpFile,
                    super.getOperator(context));
            result = OaVoConvertor.convertOaAttachmentUploadInfoDto2Vo(oaAttachmentUploadInfoDto);
        } finally {
            try {
                FileUtils.forceDelete(tempDir);
                FileUtils.forceDelete(tmpFile);
            } catch (IOException e) {
                log.warn("临时文件删除错误");
            }
        }
        return Response.SUCCESS(result);
    }

    @ResponseBody
    @ApiOperation(value = "OA附件下载")
    @RequestMapping(value = "/invoice/attachment/download", method = RequestMethod.GET)
    public void downloadInvoiceAttachment(@ApiIgnore Context context, @RequestParam(value = "file_id", required = false) String fileId,
                                          HttpServletResponse response) throws Exception {
        Pair<String, byte[]> attachment = oaService.getAttachment(fileId, super.getOperator(context));
        OutputStream out = response.getOutputStream();
        try {
            if (StringUtils.isNotBlank(attachment.getLeft())) {
                response.setHeader(CONTENT_DISPOSITION_KEY, attachment.getLeft());
            }
            out.write(attachment.getRight());
            out.close();
        } catch (Exception e) {
            log.error("OA附件解析失败,fileId={}", fileId, e);
            throw new ServiceRuntimeException("OA附件解析失败");
        } finally {
            out.close();
        }
    }

    @ResponseBody
    @ApiOperation(value = "OA附件预览")
    @RequestMapping(value = "/invoice/attachment/view", method = RequestMethod.GET)
    public void viewInvoiceAttachment(@ApiIgnore Context context, @RequestParam(value = "file_id", required = false) String fileId,
                                      HttpServletResponse response) {
        Pair<String, byte[]> attachment = oaService.getAttachment(fileId, super.getOperator(context));
        log.info("viewInvoiceAttachment fileId:{}, attachment:{}, bytes:{}", fileId, attachment, attachment.getRight());
        try {
            if (StringUtils.isNotBlank(attachment.getLeft())) {
                response.setHeader(CONTENT_DISPOSITION_KEY, attachment.getLeft().replaceAll(DOWNLOAD_KEY, INLINE_KEY));
            } else {
                response.setHeader(CONTENT_DISPOSITION_KEY, INLINE_KEY);
            }
            log.info("viewInvoiceAttachment header:{}", response.getHeader("Content-Disposition"));
            OutputStream out = response.getOutputStream();
            out.write(attachment.getRight());
            out.close();
        } catch (Exception e) {
            log.error("OA附件解析失败,fileId={}", fileId, e);
            throw new ServiceRuntimeException("OA附件解析失败");
        }
    }

    //    @Security("OaController_bindOaInvoice")
    @ResponseBody
    @ApiOperation(value = "绑定开票流程")
    @RequestMapping(value = "/invoice/bind", method = RequestMethod.POST)
    public Response bindOaInvoice(@ApiIgnore Context context, @ApiParam("绑定OA开票参数") @Valid @RequestBody InvoiceBindingVo invoiceBindingVo) {
        InvoiceBindingDto invoiceBindingDto =
                InvoiceBindingDto.builder()
                        .crmContractId(invoiceBindingVo.getCrm_contract_id())
                        .oaFlowNo(invoiceBindingVo.getOa_flow_no())
                        .build();
        oaService.bindOaInvoice(invoiceBindingDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    //    @Security("OaController_queryOaInvoiceDetail")
    @ResponseBody
    @ApiOperation(value = "获取OA开票流程明细")
    @RequestMapping(value = "/invoice/detail", method = RequestMethod.GET)
    public Response<List<OaInvoiceDetailVo>> queryOaInvoiceDetail(@ApiParam("CRM合同id") @RequestParam(value = "crm_contract_id", required = false
    ) Integer crmContractId) {

        List<OaInvoiceDetailDto> oaInvoiceDetailDtoList = oaService.queryOaInvoiceDetail(crmContractId);

        List<OaInvoiceDetailVo> resultList = OaVoConvertor.convertOaInvoiceDetailDto2Vo(oaInvoiceDetailDtoList);
        return Response.SUCCESS(resultList);
    }

    @ResponseBody
    @ApiOperation(value = "绑定OA开票预览")
    @RequestMapping(value = "/invoice/bind/preview", method = RequestMethod.GET)
    public Response<OaInvoiceRedetailVo> queryOaInvoiceRedetail(@ApiParam("OA流程编号") @RequestParam(value = "oa_flow_no", required = false
    ) String oaFlowNo) {
        OaInvoiceRedetailDto oaInvoiceRedetailDto = oaService.queryOaInvoiceRedetail(oaFlowNo);
        OaInvoiceRedetailVo result = OaInvoiceRedetailVo.builder()
                .amount(oaInvoiceRedetailDto.getAmount())
                .customer_name(oaInvoiceRedetailDto.getCustomer_name())
                .build();
        return Response.SUCCESS(result);
    }

    @ResponseBody
    @ApiOperation(value = "开票表单下拉枚举")
    @RequestMapping(value = "/invoice/form/enum", method = RequestMethod.GET)
    public Response<Map<String, List<String>>> queryOaInvoiceFormEnum(@ApiParam("类型 0 正常 1 返点") @RequestParam(value = "is_rebate", required = false, defaultValue = "0")
                                                                      Integer is_rebate) {
        Map<String, List<String>> map = Maps.newHashMap();
        map.put(INVOICE_TARGET_TYPE_ENUM_KEY,
                Stream.of(InvoiceTargetType.values()).map(invoiceTargetType -> invoiceTargetType.getTypeName()).collect(Collectors.toList()));
        if (is_rebate.equals(IsValid.TRUE.getCode())) {
            map.put(INVOICE_TYPE_ENUM_KEY,
                    Stream.of(InvoiceType.values()).map(invoiceTargetType -> invoiceTargetType.getTypeName()).collect(Collectors.toList()));
        } else {
            map.put(INVOICE_TYPE_ENUM_KEY, Lists.newArrayList(InvoiceType.ORDINARY_ELEC.getTypeName(), InvoiceType.PROFORMA.getTypeName(), InvoiceType.SPECIAL_ELECTRONIC.getTypeName()));
        }
        return Response.SUCCESS(map);
    }

    @ResponseBody
    @ApiOperation(value = "查询效果后付费开票的预置信息")
    @RequestMapping(value = "/bill_pre_info/post_pay", method = RequestMethod.GET)
    public Response<PostPayPreInfoVo> queryRtbPostPayPreInfo(@ApiIgnore Context context) {

        OaBillPreSystemInfoDto oaBillPreInfoQueryDto = new OaBillPreSystemInfoDto();

        oaBillPreInfoQueryDto.setDomainAccountName(context.getUsername());
        oaBillPreInfoQueryDto.setInvoiceSource(SystemType.CRM.getCode());

        oaBillPreInfoQueryDto.setInvoiceBizType(InvoiceBizTypeEnum.POST_PAY.getCode());

        OaBillBaseInfoDto oaBillPreInfoDto = oaService.queryOaBillBaseInfoDto(oaBillPreInfoQueryDto);

        PostPayPreInfoVo preInfoVo = OaVoConvertor.convertBillPreInfoDto2Vo(oaBillPreInfoDto);

        return Response.SUCCESS(preInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "新增效果后付费开票")
    @RequestMapping(value = "/invoice/rtb/post_pay", method = RequestMethod.POST)
    public Response createRtbPostPayInvoice(@ApiIgnore Context context,
                                            @ApiParam("新增开票参数") @Valid @RequestBody PostPayInvoiceCreateVo createVo, BindingResult bindingResult) {
        if (bindingResult.hasErrors()) {
            String errMsg =
                    StringUtils.join(bindingResult.getFieldErrors().stream().map(fieldError -> fieldError.getDefaultMessage()).collect(Collectors.toList()), COMMA);
            throw new ServiceRuntimeException(errMsg);
        }
        PostPayInvoiceCreateDto invoiceCreateDto = OaVoConvertor.convertPostPayInvoiceCreateVo2Dto(createVo);
        invoiceCreateDto.setInvoice_source(SystemType.CRM.getCode());

        oaService.createOaPostPayInvoice(invoiceCreateDto, super.getOperator(context));
        return Response.SUCCESS(null);
    }

    @ResponseBody
    @ApiOperation(value = "查看开票的发票信息")
    @RequestMapping(value = "/bill/receipts", method = RequestMethod.GET)
    public Response<OaReceiptsVo> queryOaBillReceiptInfo(@ApiIgnore Context context,
                                                         @ApiParam("crmOa开票id") @RequestParam(value = "crm_oa_flow_id", required = false) Integer crmInvoiceId) throws ServiceException {
        CrmOaReceiptsDto crmOaReceiptsDto = oaService.queryOaReceiptInfos(crmInvoiceId);
        OaReceiptsVo preInfoVo = OaVoConvertor.convertOaReceiptInfosDto2Vo(crmOaReceiptsDto);
        return Response.SUCCESS(preInfoVo);
    }

    @ResponseBody
    @ApiOperation(value = "补充发票号码")
    @RequestMapping(value = "/bill/receipts", method = RequestMethod.POST)
    public Response saveOaBillReceiptInfo(@ApiIgnore Context context,
                                          @ApiParam("保存信息") @RequestBody OaReceiptSaveVo save_vo) throws ServiceException {
        List<CommonAttachmentUploadDto> uploadDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(save_vo.getAttachment())) {
            for (OssAttachmentVo ossAttachmentVo : save_vo.getAttachment()) {
                CommonAttachmentUploadDto commonAttachmentUploadDto = new CommonAttachmentUploadDto();
                commonAttachmentUploadDto.setUploadType(UploadAttachmentType.OSS.getCode());
                commonAttachmentUploadDto.setUrl(ossAttachmentVo.getUrl());
                commonAttachmentUploadDto.setOssKey(ossAttachmentVo.getOss_key());
                commonAttachmentUploadDto.setFileName(ossAttachmentVo.getFile_name());
                uploadDtoList.add(commonAttachmentUploadDto);
            }
        }
        Assert.isTrue(Objects.nonNull(save_vo.getCrm_oa_flow_id()), "crmOa开票id 必填");
        Assert.isTrue(Objects.nonNull(save_vo.getProj_line_no()), "行号必填");
        oaService.saveSuppleInfo(CrmOaReceiptSaveDto.builder()
                .crmOaFlowId(save_vo.getCrm_oa_flow_id())
                .invoiceCode(save_vo.getInvoice_code())
                .invoiceNumber(save_vo.getInvoice_number())
                .projLineNo(save_vo.getProj_line_no())
                .uploadDto(CommonAttachmentBatchUploadDto.builder()
                        .bizModuleType(AttachmentUploadBizModuleEnum.OA_INVOICE.getModuleCode())
                        .subBizType(AttachmentUploadBizModuleEnum.OA_INVOICE.getSubBizCode())
                        .attachmentUploadDtos(uploadDtoList)
                        .build())
                .build(), this.getOperator(context));
        return Response.SUCCESS();
    }
}
