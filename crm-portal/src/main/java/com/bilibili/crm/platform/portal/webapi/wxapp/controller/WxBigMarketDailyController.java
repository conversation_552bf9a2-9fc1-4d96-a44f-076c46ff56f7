package com.bilibili.crm.platform.portal.webapi.wxapp.controller;

import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.sale.dto.CrmSaleKpiDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.app.api.service.AchievementEstimateHiveCkService;
import com.bilibili.crm.platform.app.api.service.dto.AchievementEstimateHiveCkQueryDTO;
import com.bilibili.crm.platform.app.api.service.dto.CrmAchievementDayItemDTO;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.bilibili.crm.platform.portal.webapi.wxapp.WxAppBaseController;
import com.bilibili.crm.platform.portal.webapi.wxapp.helper.WxAppInvestmentConverter;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.daily.SaleDailyCommonQueryVO;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.daily.TheBigMarketOverviewVO;
import com.bilibili.crm.platform.utils.MathUtils;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 大盘日报的Controller
 *
 * <AUTHOR>
 * date 2024/7/15 15:47.
 * Contact: <EMAIL>.
 */
@RestController
@Slf4j
@RequestMapping({"/wx_app/big_market_daily", "/web_api/big_market_daily"})
public class WxBigMarketDailyController extends WxAppBaseController {
    @Autowired
    private AchievementEstimateHiveCkService achievementEstimateHiveCkService;
    @Autowired
    private WxAppInvestmentConverter wxAppInvestmentConverter;
    @Autowired
    private ISaleService iSaleService;
    @ApiOperation(value = "大盘日报制定时间的概述")
    @RequestMapping(value = "/income/overview", method = RequestMethod.GET)
    public Response<TheBigMarketOverviewVO> getTheBigMarketIncomeByDate(@RequestParam(value = "date", required = false) Long date) {
        SaleDailyCommonQueryVO queryVO = new SaleDailyCommonQueryVO();
        queryVO.setDate(new Timestamp(date));
        List<CrmAchievementDayItemDTO> itemDTOList = achievementEstimateHiveCkService.queryCrmAchievementDayItemPoList(AchievementEstimateHiveCkQueryDTO.builder()
                .aggTime(queryVO.getDate())
                .queryProductType(Boolean.TRUE)
                .queryBiliBili(Boolean.TRUE).build());
        if (CollectionUtils.isEmpty(itemDTOList)) {
            return Response.SUCCESS(null);
        }
        List<CrmSaleKpiDto> kpiDtoList = iSaleService.queryAllKpiByQuarter(CrmUtils.getQuarterDescByTime(queryVO.getDate(), ""));
        Map<String, CrmSaleKpiDto> kpiDtoMap = kpiDtoList.stream().collect(Collectors.toMap(a -> a.getBizType() + "_" + a.getBizId(), Function.identity(), (o, n) -> n));
        CrmAchievementDayItemDTO itemDTO = itemDTOList.get(0);
        itemDTO.setTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.SALE_TEAM.getBizId() + "_0", CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
        itemDTO.setHaveAuthTotalTaskAmount(itemDTO.getTotalTaskAmount());
        itemDTO.setBrandTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.BIZ_PRODUCT.getBizId() + "_1", CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
        itemDTO.setEffectTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.BIZ_PRODUCT.getBizId() + "_2", CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
        itemDTO.setPickupTotalTaskAmount(kpiDtoMap.getOrDefault(SaleKpiTypeEnum.BIZ_PRODUCT.getBizId() + "_3", CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi());
        itemDTO.setBrandCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getBrandCompletedTaskAmount(), itemDTO.getBrandTotalTaskAmount()));
        itemDTO.setEffectCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getEffectCompletedTaskAmount(), itemDTO.getEffectTotalTaskAmount()));
        itemDTO.setPickupCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getPickupCompletedTaskAmount(), itemDTO.getPickupTotalTaskAmount()));
        itemDTO.setCompletedTaskAmountRate(MathUtils.calculateRateToString(itemDTO.getCompletedTaskAmount(), itemDTO.getTotalTaskAmount()));
        itemDTO.setBrandBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getBrandConfigBusinessEstimateAmount(), itemDTO.getBrandTotalTaskAmount()));
        itemDTO.setEffectBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getEffectConfigBusinessEstimateAmount(), itemDTO.getEffectTotalTaskAmount()));
        itemDTO.setPickupBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getPickupConfigBusinessEstimateAmount(), itemDTO.getPickupTotalTaskAmount()));
        itemDTO.setBusinessEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getConfigBusinessEstimateAmount(), itemDTO.getTotalTaskAmount()));
        itemDTO.setBrandSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getBrandSevenToQuarterEstimateAmount(), itemDTO.getBrandTotalTaskAmount()));
        itemDTO.setEffectSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getEffectSevenToQuarterEstimateAmount(), itemDTO.getEffectTotalTaskAmount()));
        itemDTO.setPickupSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getPickupSevenToQuarterEstimateAmount(), itemDTO.getPickupTotalTaskAmount()));
        itemDTO.setAllSevenToQuarterEstimateAmountRate(MathUtils.calculateRateToString(itemDTO.getAllSevenToQuarterEstimateAmount(), itemDTO.getTotalTaskAmount()));
        List<TheBigMarketOverviewVO> overviewVOList = wxAppInvestmentConverter.toBuildBigDailyOverviewVO(itemDTOList, queryVO);
        return Response.SUCCESS(overviewVOList.get(0));
    }

}

