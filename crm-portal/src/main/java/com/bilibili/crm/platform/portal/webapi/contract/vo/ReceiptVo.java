package com.bilibili.crm.platform.portal.webapi.contract.vo;

import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.portal.webapi.sale.vo.SaleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * 合同收款纪录
 * Created by fanwenbin on 2017/5/15.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReceiptVo {
    @ApiModelProperty("收款记录id")
    private Integer receipt_id;
    @ApiModelProperty("合同id")
    private Integer crm_contract_id;

    @ApiModelProperty("收款金额(元)")
    private BigDecimal amount;

    @ApiModelProperty("收款方式 0银行转账 1支票 2现金 3其他")
    private Integer type;

    @ApiModelProperty("收款方式-展示")
    private String type_desc;

    @ApiModelProperty("收款日期")
    private String collection_date;

    @ApiModelProperty("对公账户")
    private String public_accounts;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("销售")
    private String sales_desc;

    @ApiModelProperty("销售")
    private List<SaleVo> sales;

    @ApiModelProperty("是否正常 1审核通过 2作废 3-待审核 4-审核驳回")
    private Integer status;
    @ApiModelProperty("是否正常展示值")
    private String status_desc;
    @ApiModelProperty("废除备注")
    private String delete_remark;

    @ApiModelProperty("审批人")
    private String auditor;

    @ApiModelProperty("审批时间")
    private String processing_time;

    @ApiModelProperty("驳回原因")
    private String reason;

    @ApiModelProperty("记录人")
    private String operator;

    @ApiModelProperty("记录时间")
    private Long mtime;
}
