package com.bilibili.crm.platform.portal.component.effect;

import com.bilibili.crm.platform.biz.common.EffectRealtimeHourDataType;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.effect.EffectRealtimePanelVo;
import com.bilibili.dss.api.dict.EffectRealtimeQueryDataType;
import com.bilibili.dss.api.dto.effect.hour.EffectRealtimeHourQueryDto;
import org.springframework.stereotype.Component;

/**
 * 分时数据查询器
 *
 * <AUTHOR>
 * @date 2020/10/21 2:46 下午
 */
@Deprecated
@Component(value = "effectRealtimeIncomeQuerierForByHour")
public class EffectRealtimeIncomeQuerierForByHour extends AbstractEffectRealtimeIncomeQuerier<EffectRealtimeHourQueryDto, EffectRealtimePanelVo> {

    @Override
    protected String generateCacheKey(EffectRealtimeHourQueryDto queryDto) {
        String cacheKey = effectRealtimeCacheKeyQuerier.queryCacheKey(EffectRealtimeQueryDataType.HOUR, EffectRealtimeHourDataType.HOUR_STAT, null, null);
        return cacheKey;
    }

    @Override
    protected EffectRealtimePanelVo initResult() {
        EffectRealtimePanelVo effectRealtimeVo = EffectRealtimePanelVo.init();
        return effectRealtimeVo;
    }
}
