package com.bilibili.crm.platform.portal.export.template.agent;

import com.bilibili.crm.platform.api.effect.enums.ReportColumnBehavior;
import com.bilibili.crm.platform.api.trading.flow.dto.AccountTradingFlowDto;
import com.bilibili.crm.platform.api.trading.flow.dto.QueryTradingFlowParam;
import com.bilibili.crm.platform.portal.export.template.TradingFlowSpringExportTemplate;
import com.bilibili.crm.platform.portal.export.vo.AgentAdvTradingFlowExportVO;
import com.bilibili.crm.platform.portal.webapi.trading.flow.vo.TradingFlowVo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * 代理商后台、客户资金导出
 */
@Service
@Slf4j
public class AgentAdvTradingFlowSpringExportTemplate extends TradingFlowSpringExportTemplate<AgentAdvTradingFlowExportVO> {


    public AgentAdvTradingFlowSpringExportTemplate() {
        super(ReportColumnBehavior.AGENT_ADVERTISER_FUND, AgentAdvTradingFlowExportVO.class);
    }

    @Override
    public List<AgentAdvTradingFlowExportVO> getData(QueryTradingFlowParam param) {
        List<AccountTradingFlowDto> flows = accountAgentWalletTradingCkService.exportAdTradingFlowList(null, param);
        List<TradingFlowVo> tempList = webTradingFlowService.dto2vo(flows);
        return tempList.stream()
                .map(i -> {
                    AgentAdvTradingFlowExportVO vo = new AgentAdvTradingFlowExportVO();
                    BeanUtils.copyProperties(i, vo);
                    return vo;
                })
                .collect(Collectors.toList());
    }
}
