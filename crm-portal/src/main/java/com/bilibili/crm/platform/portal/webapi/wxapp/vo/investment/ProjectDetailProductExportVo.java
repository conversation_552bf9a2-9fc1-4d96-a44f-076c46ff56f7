package com.bilibili.crm.platform.portal.webapi.wxapp.vo.investment;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-12-15 13:48:20
 * @description:
 **/

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class ProjectDetailProductExportVo {

    @ExcelResources(title = "产品类型")
    private String product_type;

    @ExcelResources(title = "产品子类")
    private String product_second_type;

    @ExcelResources(title = "已完成（万）")
    @ApiModelProperty("已完成（万）")
    private BigDecimal has_completed;

    @ExcelResources(title = "已下单（万）")
    @ApiModelProperty("已下单（万）")
    private BigDecimal has_ordered;

    @ExcelResources(title = "目标金额（万）")
    @ApiModelProperty("目标金额（万）")
    private BigDecimal target;

    @ExcelResources(title = "执行完成率")
    @ApiModelProperty("执行完成率")
    private BigDecimal has_completed_rate;

    @ExcelResources(title = "下单进度")
    @ApiModelProperty("下单进度")
    private BigDecimal ordered_rate;

    @ExcelResources(title = "商机预估（万）")
    @ApiModelProperty("商机预估（万）")
    private BigDecimal bsi_estimate;

}
