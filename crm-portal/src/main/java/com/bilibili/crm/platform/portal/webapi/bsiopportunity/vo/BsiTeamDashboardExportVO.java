package com.bilibili.crm.platform.portal.webapi.bsiopportunity.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModel;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 商机团队概述结果行数据导出
 *
 * <AUTHOR>
 * date 2024/3/9 18:11.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ApiModel(value = "商机团队概述结果行数据导出", description = "商机团队概述结果行数据导出")
public class BsiTeamDashboardExportVO implements Serializable {
    private static final long serialVersionUID = 5513105613322615575L;

    @ExcelResources(title = "季度")
    private String quarter;
    @ExcelResources(title = "营销中心")
    private String marketing_center;
    @ExcelResources(title = "直客/渠道组")
    private String direct_channel;
    @ExcelResources(title = "销售小组")
    private String sale_group_name;
    @ExcelResources(title = "销售姓名")
    private String sale_name;
    @ExcelResources(title = "有效商机商机金额*系数（元）")
    private BigDecimal bsi_amount_and_factor;
    @ExcelResources(title = "有效商机金额总计（元）")
    private BigDecimal bsi_amount;

    @ExcelResources(title = "有效商机数量总计")
    private Long bsi_all_count;

    @ExcelResources(title = "100%下单-未关联合同-商机金额（元）")
    private BigDecimal complete_not_related_bsi_amount;
    @ExcelResources(title = "100%下单-未关联合同-商机数量")
    private Long complete_not_related_bsi_count;
    @ExcelResources(title = "100%下单-已关联合同-商机数量")
    private Long complete_have_related_bsi_count;

    @ExcelResources(title = "新30%-商机金额（元）")
    private BigDecimal thirty_bsi_amount;

    @ExcelResources(title = "新30%-商机数量")
    private Long thirty_bsi_count;

    @ExcelResources(title = "新50%-商机金额（元）")
    private BigDecimal fifty_bsi_amount;

    @ExcelResources(title = "新50%-商机数量")
    private Long fifty_bsi_count;

    @ExcelResources(title = "新80%-商机金额（元）")
    private BigDecimal eighty_bsi_amount;

    @ExcelResources(title = "新80%-商机数量")
    private Long eighty_bsi_count;
}

