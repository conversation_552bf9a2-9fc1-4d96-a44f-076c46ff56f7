package com.bilibili.crm.platform.portal.webapi.finance.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @author: brady
 * @time: 2021/11/18 3:56 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PostPayInvoiceQueryVo {

    @ApiModelProperty("开票开始时间")
    private Long bill_time_begin;

    @ApiModelProperty("开票结束时间")
    private Long bill_time_end;

    @ApiModelProperty("客户id")
    private Integer customer_id;

    @ApiModelProperty("账号id")
    private Integer account_id;

    @ApiModelProperty("开票公司")
    private String company;

    @ApiModelProperty("开票状态")
    private List<Integer> bill_status_list;

}
