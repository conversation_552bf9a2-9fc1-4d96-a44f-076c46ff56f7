package com.bilibili.crm.platform.portal.webapi.finance.vo.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * 特殊点位折扣比例
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicySpecialPointDiscountVo {

    @ApiModelProperty("点位类型")
    private Integer type;

    /**
     * 硬广政策-特殊点位折扣比例
     */
    @ApiModelProperty("折扣比例")
    private BigDecimal discountRatio;
}
