package com.bilibili.crm.platform.portal.webapi.customer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpdateCustomerVo {

    // 基本信息

    @ApiModelProperty("客户ID")
    private Integer id;

    @ApiModelProperty("客户别名")
    private String nick_name;

    @ApiModelProperty("客户名称 外部（公司名称） 内部（个人姓名）")
    private String username;

    @ApiModelProperty("推广域名")
    private String domain;

    @ApiModelProperty("区域id（机构专用）")
    private Integer area_id;

    @ApiModelProperty("网站名称（机构专用）")
    private String website_name;

    @ApiModelProperty("微博账号（机构专用）")
    private String weibo;

    @ApiModelProperty("我司对接人（机构专用）")
    private String internal_linkman;

    @ApiModelProperty("联系人手机号（机构专用）")
    private String linkman_phone;

    @ApiModelProperty("联系人邮箱（机构专用）")
    private String linkman_email;

    @ApiModelProperty("联系人地址（机构专用）")
    private String linkman_address;

    @ApiModelProperty("开户行（机构专用）")
    private String bank;

    @ApiModelProperty("是否是代理商 0-否 1-是")
    private Integer is_agent;

    @ApiModelProperty("代理商类型：0-无 1-品牌代理商  2-MCN代理商  3-效果代理商")
    private Integer agent_type;

    @ApiModelProperty("一级行业标签（机构专用）")
    private Integer first_industry_tag_id;

    @ApiModelProperty("二级行业标签（机构专用）")
    private Integer second_industry_tag_id;

    @ApiModelProperty("个人手机号（个人专用）")
    private String personal_phone;

    @ApiModelProperty("联系人地址（个人专用）")
    private String personal_address;

    @ApiModelProperty("备注")
    private String remark;

    // 资质信息
    @ApiModelProperty("主体资质分类id（机构专用）")
    private Integer qualification_type_id;

    @ApiModelProperty("营业执照编码（机构专用）")
    private String business_licence_code;

    @ApiModelProperty(notes = "营业执照照片(机构专用)")
    private List<CustomerAttachVo> business_licence_pics;

    @ApiModelProperty("营业执照是否长期有效 1是 0 否（机构专用）")
    private Integer is_business_licence_indefinite;

    @ApiModelProperty("营业执照到期时间（机构专用）")
    private Long business_licence_expire_date;

    @ApiModelProperty("法人姓名（机构专用）")
    private String legal_person_name;

    @ApiModelProperty("法人身份证照片（机构专用）")
    private List<CustomerAttachVo> legal_person_id_card_pics;

    @ApiModelProperty("法人身份证是否长期有效 1是 0 否（机构专用）")
    private Integer is_legal_person_id_card_indefinite;

    @ApiModelProperty("法人身份证到期时间（机构专用）")
    private Long legal_person_id_card_expire_date;

    @ApiModelProperty("icp备案号（机构专用）")
    private String icp_record_number;

    @ApiModelProperty("icp备案证明照片/icp证（机构专用）")
    private List<CustomerAttachVo> icp_pics;

    @ApiModelProperty("个人证件类型 0-未知 1-身份证（大陆地区） 2-护照（港澳台及海外） （个人专用）")
    private Integer personal_id_card_type;

    @ApiModelProperty("个人证件号码（个人专用）")
    private String personal_id_card_number;

    @ApiModelProperty("个人证件是否长期有效 1是 0 否（个人专用）")
    private Integer is_personal_id_card_indefinite;

    @ApiModelProperty("个人证件到期时间（个人专用）")
    private Long personal_id_card_expire_date;

    @ApiModelProperty("个人证件照片（个人专用）")
    private List<CustomerAttachVo> person_id_card_pics;

    @ApiModelProperty("额外资质信息列表")
    private List<CustomerAdditionalQualificationVo> qualifications;
}
