package com.bilibili.crm.platform.portal.webapi.customer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/3/19
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerWorkOrderVo {

    @ApiModelProperty("工单id")
    private Integer work_order_id;

    @ApiModelProperty("创建时间")
    private Long ctime;

    @ApiModelProperty("来源 CRM用户 代理商")
    private String source;

    @ApiModelProperty("客户名称")
    private String user_name;

    @ApiModelProperty("客户id")
    private Integer customer_id;

    @ApiModelProperty("别名")
    private String nick_name;

    @ApiModelProperty("客户类别 是否代理商客户 0广告主客户 1代理商客户")
    private Integer is_agent;

    @ApiModelProperty("客户类别 是否代理商客户 0广告主客户 1代理商客户")
    private String is_agent_desc;

    @ApiModelProperty("审核状态")
    private Integer status;

    @ApiModelProperty("审核状态描述")
    private String status_desc;

    @ApiModelProperty("复用客户id")
    private Integer reuse_customer_id;

    @ApiModelProperty("复用客户名称")
    private String reuse_customer_name;

    @ApiModelProperty("一级行业分类")
    private Integer category_first_id;

    @ApiModelProperty("一级行业分类")
    private String category_first_name;

    @ApiModelProperty("二级行业分类")
    private Integer category_second_id;

    @ApiModelProperty("二级行业分类")
    private String category_second_name;

    @ApiModelProperty("创建人")
    private String creator_name;

    @ApiModelProperty(notes = "审核备注")
    private String audit_remark;

    @ApiModelProperty("一级行业分类ID")
    private Integer united_first_industry_id;

    @ApiModelProperty("一级行业分类")
    private String united_first_industry_name;

    @ApiModelProperty("二级行业分类ID")
    private Integer united_second_industry_id;

    @ApiModelProperty("二级行业分类")
    private String united_second_industry_name;

    @ApiModelProperty("三级行业分类ID")
    private Integer united_third_industry_id;

    @ApiModelProperty("三级行业分类")
    private String united_third_industry_name;
}
