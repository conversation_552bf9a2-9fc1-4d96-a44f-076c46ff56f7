package com.bilibili.crm.platform.portal.webapi.investment.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-11-29 16:29:07
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TaskQueryVo {

    @ApiModelProperty(notes = "项目Id")
    private List<Integer> project_id_list;

    @ApiModelProperty(notes = "导入时间-左区间")
    private Timestamp import_time_left;

    @ApiModelProperty(notes = "导入时间-右区间")
    private Timestamp import_time_right;

    @ApiModelProperty(notes = "task tag")
    private List<String> task_tag_list;
}
