package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * @description: 合约广告查询参数
 * @author: brady
 * @time: 2020/10/22 2:33 下午
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryAchieveContractVo extends QueryAchieveBoardVo{
    @ApiModelProperty("账户ID")
    private Integer account_id;

    @ApiModelProperty("客户ID")
    private Integer customer_id;

    @ApiModelProperty("代理商ID")
    private Integer agent_id;

    @ApiModelProperty("账户IDs")
    private List<Integer> account_ids;

    @ApiModelProperty("客户IDs")
    private List<Integer> customer_ids;

    @ApiModelProperty("代理商IDs")
    private List<Integer> agent_ids;

    @ApiModelProperty("订单类型")
    private Integer order_type;

    @ApiModelProperty("合同号")
    private Long contract_number;

    @ApiModelProperty("计收开始时间")
    private Long check_date_begin;

    @ApiModelProperty("计收结束时间")
    private Long check_date_end;

    @ApiModelProperty("业务大类：0 全部产品 1品牌 2效果 3商单")
    private Integer business_type;


    @ApiModelProperty("花火订单编号No")
    private Long pick_order_no;

    private Integer query_detail_list_type;

}
