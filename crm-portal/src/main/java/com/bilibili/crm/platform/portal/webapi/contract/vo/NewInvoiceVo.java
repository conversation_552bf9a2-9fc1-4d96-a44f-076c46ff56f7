package com.bilibili.crm.platform.portal.webapi.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/5/15.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewInvoiceVo {

//    @ApiModelProperty("开票公司")
//    @NotNull
//    private String company;

//    @ApiModelProperty("税号信息")
//    @NotNull
//    private String tax_number;

//    @ApiModelProperty("开户银行")
//    @NotNull
//    private String bank;

//    @ApiModelProperty("通讯地址")
//    @NotNull
//    private String address;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("合同id")
    @NotNull
    private Integer crm_contract_id;

//    @ApiModelProperty("票据类型 0增值税专用发票 1增值税普通发票 2其他 3增值税 4普通国税 5普通地税")
//    @NotNull
//    private Integer type;

    @ApiModelProperty("金额")
    @NotNull
    private BigDecimal amount;

    
    @ApiModelProperty("开票日期")
    @NotNull
    private Long billing_date;
    
    @ApiModelProperty("开票相关附件")
    @NotNull
    private List<ContractMailOssVo> invoice_mail_oss;

    @ApiModelProperty("返点抵扣金额")
    private BigDecimal rebate_amount;
}
