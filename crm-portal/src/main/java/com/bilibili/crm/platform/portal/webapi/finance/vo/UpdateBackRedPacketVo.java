package com.bilibili.crm.platform.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by user on 2017/8/10.
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateBackRedPacketVo {

    @ApiModelProperty("返货ID")
    @NotNull(message = "返货ID不可为空")
    private Integer id;

    @ApiModelProperty("返货类型: 1-系统自动返货 2-人工返货 3-特批返货")
    @NotNull(message = "返货类型不可为空")
    private int back_type;

    @ApiModelProperty("充值流水号")
    private String recharge_serial_number;

    @ApiModelProperty("特批邮件列表")
    private List<EmailScreenshotVo> emails;

    @ApiModelProperty("返货金额,单位(元)")
    @NotNull(message = "返货金额不可为空")
    private BigDecimal amount;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("返货归属日期")
    private Long belong_date;

    @ApiModelProperty("适用返货政策")
    //@NotNull(message = "返货类型不可为空")
    private Integer back_policy_type;
}
