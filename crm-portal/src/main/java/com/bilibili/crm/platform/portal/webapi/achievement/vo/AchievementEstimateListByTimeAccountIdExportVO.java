package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预估看板-点击已完成按照天乘账号维度导出
 *
 * <AUTHOR>
 * date 2024/5/23 15:03.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@ExcelIgnoreUnannotated
public class AchievementEstimateListByTimeAccountIdExportVO implements Serializable {

    private static final long serialVersionUID = 5814190575239455898L;
    @ExcelProperty(value = "计收日期")
    private String bus_account_date;
    @ExcelProperty(value = "消耗日期")
    private String amount_date_list_str;
    @ExcelProperty(value = "业务关账日期")
    private String busi_closing_date_list_str;
    @ExcelProperty(value = "广告主账户ID")
    private String account_id;
    @ExcelProperty(value = "广告主账户名称")
    private String account_name;
    @ExcelProperty(value = "广告主客户ID")
    private String customer_id_list_str;
    @ExcelProperty(value = "广告主客户名称")
    private String customer_name_list_str;
    @ExcelProperty(value = "客户业务一级行业ID")
    private String cus_biz_industry_category_first_id_list_str;
    @ExcelProperty(value = "客户业务一级行业名称")
    private String cus_biz_industry_category_first_name_list_str;
    @ExcelProperty(value = "客户业务二级行业ID")
    private String cus_biz_industry_category_second_id_list_str;
    @ExcelProperty(value = "客户业务二级行业名称")
    private String cus_biz_industry_category_second_name_list_str;
    @ExcelProperty(value = "品牌ID")
    private String product_id_list_str;
    @ExcelProperty(value = "品牌名称")
    private String product_name_list_str;
    @ExcelProperty(value = "集团ID")
    private String group_id_list_str;
    @ExcelProperty(value = "集团名称")
    private String group_name_list_str;
    @ExcelProperty(value = "代理商账户ID")
    private String agent_id_list_str;
    @ExcelProperty(value = "代理商账户名称")
    private String agent_name_list_str;
    @ExcelProperty(value = "代理商客户ID")
    private String agent_customer_id_list_str;
    @ExcelProperty(value = "代理商客户名称")
    private String agent_customer_name_list_str;
    @ExcelProperty(value = "代理商集团ID")
    private String agent_group_id_list_str;
    @ExcelProperty(value = "代理商集团名称")
    private String agent_group_name_list_str;
    @ExcelProperty(value = "商机代理客户ID")
    private String opt_agent_id_list_str;
    @ExcelProperty(value = "商机代理客户名称")
    private String opt_agent_name_list_str;
    @ExcelProperty(value = "商机代理集团ID")
    private String opt_agent_group_id_list_str;
    @ExcelProperty(value = "商机代理集团名称")
    private String opt_agent_group_name_list_str;
    @ExcelProperty(value = "已完成总计（元）")
    private BigDecimal total_amount;
    @ExcelProperty(value = "品牌已完成（元）")
    private BigDecimal brand_amount;
    @ExcelProperty(value = "效果已完成（元）")
    private BigDecimal effect_amount;
    @ExcelProperty(value = "花火已完成（元）")
    private BigDecimal pickup_amount;
    @ExcelProperty(value = "业绩归属直客销售")
    private String kpi_zk_sale_name_list_str;
    @ExcelProperty(value = "直客销售所属小组")
    private String kpi_zk_sale_group_name_v1_list_str;
    @ExcelProperty(value = "直客公海小组")
    private String yjgh_zk_sale_group_name_list_str;
    @ExcelProperty(value = "直客公海上级小组")
    private String yjgh_zk_sale_group_parent_name_list_str;
    @ExcelProperty(value = "业绩归属渠道销售")
    private String kpi_qd_sale_name_list_str;
    @ExcelProperty(value = "渠道销售所属小组")
    private String kpi_qd_sale_group_name_v1_list_str;
    @ExcelProperty(value = "渠道公海小组")
    private String yjgh_qd_sale_group_name_list_str;
    @ExcelProperty(value = "账户一级行业标签")
    private String acc_united_first_industry_name_list_str;
    @ExcelProperty(value = "账户二级行业标签")
    private String acc_united_second_industry_name_list_str;
    @ExcelProperty(value = "账户三级行业标签")
    private String acc_united_third_industry_name_list_str;
    @ExcelProperty(value = "客户一级行业标签")
    private String customer_united_first_industry_name_list_str;
    @ExcelProperty(value = "客户二级行业标签")
    private String customer_united_second_industry_name_list_str;
    @ExcelProperty(value = "客户三级行业标签")
    private String customer_united_third_industry_name_list_str;
}

