package com.bilibili.crm.platform.portal.convertor;

import com.bilibili.crm.platform.api.customer.dto.CustomerBillInfoDto;
import com.bilibili.crm.platform.api.finance.dto.FlowableAttachmentDto;
import com.bilibili.crm.platform.portal.webapi.customer.vo.CustomerBillInfoForOaVo;
import com.bilibili.crm.platform.portal.webapi.customer.vo.CustomerBillInfoVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.InvoiceImgVo;

import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2021/2/19 7:05 下午
 */
public class CustomerBillInfoVoConvertor {

    public static CustomerBillInfoVo convertDto2Vo(CustomerBillInfoDto customerBillInfoDto) {
        CustomerBillInfoVo customerBillInfoVo = CustomerBillInfoVo.builder().build();
        if (customerBillInfoDto == null) {
            return customerBillInfoVo;
        }
        customerBillInfoVo.setCustomer_id(customerBillInfoDto.getCustomerId());
        customerBillInfoVo.setTaxpayer_number(customerBillInfoDto.getTaxpayerNumber());
        customerBillInfoVo.setReceiver(customerBillInfoDto.getReceiver());
        customerBillInfoVo.setAddress(customerBillInfoDto.getAddress());
        customerBillInfoVo.setBank_account_no(customerBillInfoDto.getBankAccountNo());
        customerBillInfoVo.setBank_name(customerBillInfoDto.getBankName());
        customerBillInfoVo.setTel(customerBillInfoDto.getTel());
        customerBillInfoVo.setRequest_company_code(customerBillInfoDto.getRequestCompanyCode());
        customerBillInfoVo.setRequest_company(customerBillInfoDto.getRequestCompany());
        customerBillInfoVo.setTaxpayer_type(customerBillInfoDto.getTaxpayerType());
        customerBillInfoVo.setBusiness_licence_code(customerBillInfoDto.getBusinessLicenceCode());

        if(Objects.nonNull(customerBillInfoDto.getTaxpayerQualification())){
            customerBillInfoVo.setTaxpayer_qualification(InvoiceImgVo.builder()
                    .file_id(customerBillInfoDto.getTaxpayerQualification().getFileId())
                    .file_name(customerBillInfoDto.getTaxpayerQualification().getFileName())
                    .file_type(customerBillInfoDto.getTaxpayerQualification().getFileType())
                    .build());
        }
        return customerBillInfoVo;
    }

    public static CustomerBillInfoForOaVo convertDto2ForOaVo(CustomerBillInfoDto customerBillInfoDto) {
        CustomerBillInfoForOaVo customerBillInfoVo = CustomerBillInfoForOaVo.builder().build();
        if (customerBillInfoDto == null) {
            return customerBillInfoVo;
        }
        customerBillInfoVo.setCustomer_id(customerBillInfoDto.getCustomerId());
        customerBillInfoVo.setCustomer_name(customerBillInfoDto.getCustomerName());
        customerBillInfoVo.setTaxpayer_id(customerBillInfoDto.getTaxpayerNumber());
        customerBillInfoVo.setReceiver(customerBillInfoDto.getReceiver());
        customerBillInfoVo.setCustomer_address(customerBillInfoDto.getAddress());
        customerBillInfoVo.setCustomer_account(customerBillInfoDto.getBankAccountNo());
        customerBillInfoVo.setCustomer_bank(customerBillInfoDto.getBankName());
        customerBillInfoVo.setCustomer_phone(customerBillInfoDto.getTel());
        customerBillInfoVo.setRequest_company_code(customerBillInfoDto.getRequestCompanyCode());
        customerBillInfoVo.setRequest_company(customerBillInfoDto.getRequestCompany());
        customerBillInfoVo.setTaxType(customerBillInfoDto.getTaxType());
        customerBillInfoVo.setTaxTypeDesc(customerBillInfoDto.getTaxTypeDesc());
        customerBillInfoVo.setSpark_customer_name(customerBillInfoDto.getSellerCompanyName());
        return customerBillInfoVo;
    }

    public static CustomerBillInfoDto convertVo2Dto(CustomerBillInfoVo customerBillInfoVo) {
        if (customerBillInfoVo == null) {
            return null;
        }
        CustomerBillInfoDto customerBillInfoDto = CustomerBillInfoDto.builder().build();
        customerBillInfoDto.setCustomerId(customerBillInfoVo.getCustomer_id());
        customerBillInfoDto.setTaxpayerNumber(customerBillInfoVo.getTaxpayer_number());
        customerBillInfoDto.setReceiver(customerBillInfoVo.getReceiver());
        customerBillInfoDto.setAddress(customerBillInfoVo.getAddress());
        customerBillInfoDto.setBankAccountNo(customerBillInfoVo.getBank_account_no());
        customerBillInfoDto.setBankName(customerBillInfoVo.getBank_name());
        customerBillInfoDto.setTel(customerBillInfoVo.getTel());
        customerBillInfoDto.setTaxpayerType(customerBillInfoVo.getTaxpayer_type());
        if(Objects.nonNull(customerBillInfoVo.getTaxpayer_qualification())){
            customerBillInfoDto.setTaxpayerQualification(FlowableAttachmentDto.builder()
                    .fileId(customerBillInfoVo.getTaxpayer_qualification().getFile_id())
                    .fileName(customerBillInfoVo.getTaxpayer_qualification().getFile_name())
                    .fileType(customerBillInfoVo.getTaxpayer_qualification().getFile_type())
                    .build());
        }

        return customerBillInfoDto;
    }
}
