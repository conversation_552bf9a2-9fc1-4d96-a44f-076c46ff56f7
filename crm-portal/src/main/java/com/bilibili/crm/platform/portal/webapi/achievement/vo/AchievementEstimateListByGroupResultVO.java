package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * 预估看板-点击已完成按照集团降序的列表
 *
 * <AUTHOR>
 * date 2024/5/23 15:03.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchievementEstimateListByGroupResultVO implements Serializable {

    private static final long serialVersionUID = 7015446206892475517L;

    @ApiModelProperty("集团id")
    private Integer group_id;
    @ApiModelProperty("集团名称")
    private String group_name;
    @ApiModelProperty("已完成总计（元）")
    private BigDecimal completed_task_amount;
    @ApiModelProperty("品牌产品已完成（元）")
    private BigDecimal brand_completed_amount;
    @ApiModelProperty("效果产品已完成（元）")
    private BigDecimal effect_completed_amount;
    @ApiModelProperty("花火已完成（元）")
    private BigDecimal pickup_completed_amount;
    @ApiModelProperty("业绩归属直客销售")
    private String direct_sale_name_str;
    @ApiModelProperty("业绩归属渠道销售")
    private String channel_sale_name_str;
    @ApiModelProperty("业绩归属直客销售小组")
    private String direct_sale_group_name_str;
    @ApiModelProperty("业绩归属渠道销售小组")
    private String channel_sale_group_name_str;
    @ApiModelProperty("业绩归属直客公海小组")
    private String direct_sea_group_name_str;

}

