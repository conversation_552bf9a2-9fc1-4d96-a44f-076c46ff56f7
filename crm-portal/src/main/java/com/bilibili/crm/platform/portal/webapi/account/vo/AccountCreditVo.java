package com.bilibili.crm.platform.portal.webapi.account.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountCreditVo {
    @ApiModelProperty(notes="账户ID")
    private Integer account_id;

    @ApiModelProperty(notes="风险等级 1.高 2.中 3.较低 4.低")
    private Integer risk_level;

    @ApiModelProperty(notes="风险等级描述 1.高 2.中 3.较低 4.低")
    private String risk_level_desc;

    @ApiModelProperty(notes = "报表类目 0.其他、1.游戏、2.电商、3.金融、4.教育培训、5.网络服务、6.美容保健")
    private Integer report_category;

    @ApiModelProperty(notes="报表类目描述")
    private String report_category_desc;

    @ApiModelProperty(notes="账号批注")
    private String annotation;
}
