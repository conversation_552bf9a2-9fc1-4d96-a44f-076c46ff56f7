package com.bilibili.crm.platform.portal.webapi.achievement.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.achievement.IAchievementRtbService;
import com.bilibili.crm.platform.api.achievement.dto.*;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromBillService;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromCluePassService;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromWalletService;
import com.bilibili.crm.platform.api.adx.dto.AdxBidderConfigDto;
import com.bilibili.crm.platform.api.adx.service.IAdxBidderDayService;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.company.dto.CorporationGroupDto;
import com.bilibili.crm.platform.api.company.dto.ProductDto;
import com.bilibili.crm.platform.api.company.service.ICorporationGroupService;
import com.bilibili.crm.platform.api.income.dto.AdIncome;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.biz.common.ClosedType;
import com.bilibili.crm.platform.biz.service.achievement.AchievementRtbReportService;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchievementRtbServiceHelper;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.common.CalculateType;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.SortOrderType;
import com.bilibili.crm.platform.portal.config.ThreadConfig;
import com.bilibili.crm.platform.portal.webapi.achievement.helper.WebAchievementHelper;
import com.bilibili.crm.platform.portal.webapi.achievement.vo.*;
import com.bilibili.crm.platform.utils.AdUtils;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Service
public class WebAchievementRtbService {
    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private IAchievementRtbService rtbService;
    @Autowired
    private IncomeUtil incomeUtil;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private AchievementRtbServiceHelper rtbHelper;
    @Autowired
    private ISaleService saleService;
    @Autowired
    private IAdxBidderDayService adxBidderDayService;
    @Autowired
    private AchievementRtbReportService rtbReportService;
    @Autowired
    private WebAchievementHelper webAchievementHelper;
    @Autowired
    private IAchieveFromWalletService achieveFromWalletService;
    @Autowired
    private IAchieveFromBillService achieveFromBillService;
    @Autowired
    private ICorporationGroupService corporationGroupService;

    @Resource
    private ThreadConfig threadConfig;

    @Resource
    private IAchieveFromCluePassService achieveFromCluePassService;


    /**
     * 商业起飞累计收入导出
     */
    public List<RtbReportAchievementVo> getBsiFlyReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getBsiFlyReportDataFormWideEs(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievementVo> result = rtbReportData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    /**
     * 必选累计收入导出
     */
    public List<RtbReportAchievementVo> getRtbReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getRtbReportData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievementVo> result = rtbReportData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    /**
     * 内容起飞累计收入导出
     */
    public List<RtbReportAchievementVo> getContentReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getContentFlyReportData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievementVo> result = rtbReportData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    public List<RtbReportAchievementVo> getCluePassData(Operator operator, QueryAchieveRtbVo queryVo) {
        // 合伙人-线索通
        List<RtbReportAchievement> cluePassData = rtbReportService.getCluePassReportData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievementVo> result = cluePassData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    public List<AchievementRtbData> getCluePassAggData(Operator operator, QueryAchieveRtbVo queryVo) {
        // 合伙人-线索通
        return rtbReportService.getCluePassAggData(operator, queryVo2Dto(queryVo));
    }

    public List<AchievementRtbData> getCluePassDto(Operator operator, QueryAchieveRtbVo queryVo) {
        QueryAchieveDto queryAchieveDto = queryVo2Dto(queryVo);
        queryAchieveDto.setIsQuerySale(IsValid.TRUE.getCode());
        // 合伙人-线索通
        return rtbService.getConsumeDataByFunction(operator, queryVo2Dto(queryVo), IncomeComposition.CLUE_PASS, achieveFromCluePassService::queryCluePass);
    }

    /**
     * 内容起飞累计收入导出-含资源平台、资源位类型、是否带货
     */
    public List<RtbReportAchievementVo> getContentFlyResourceReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getContentFlyReportBeyondData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievementVo> result = rtbReportData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    /**
     * dpa累计收入导出
     */
    public List<RtbReportAchievementVo> getDpaReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getDpaReportData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievement> rtbCloseReportData = rtbReportService.getDpaCloseReportData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievement> merge = Stream.of(rtbReportData, rtbCloseReportData).flatMap(Collection::stream).collect(Collectors.toList());
        List<RtbReportAchievementVo> result = merge.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    /**
     * 商业起飞累计收入导出-超播
     */
    public List<RtbReportAchievementVo> getBsiFlyResourceReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getBsiFlyReportBeyondData(operator, queryVo2Dto(queryVo));
        List<RtbReportAchievementVo> result = rtbReportData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
        return result;
    }

    /**
     * 必选累计收入导出-超播
     */
    public List<RtbReportAchievementVo> getRtbResourceReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<RtbReportAchievement> rtbReportData = rtbReportService.getRtbReportBeyondData(operator, queryVo2Dto(queryVo));
        return rtbReportData.stream().map(this::rtb2ReportVo).collect(Collectors.toList());
    }

    /**
     * adx累计收入导出
     */
    public List<AdxReportAchievementVo> getAdxReportData(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AdxReportAchievement> adxReportData = rtbReportService.getAdxReportData(operator, queryVo2Dto(queryVo));
        List<AdxReportAchievement> adxCloseReportData = rtbReportService.getAdxCloseReportData(operator, queryVo2Dto(queryVo));
        List<AdxReportAchievementVo> result = adxReportData.stream().map(this::adx2ReportVo).collect(Collectors.toList());
        List<AdxReportAchievementVo> closeResult = adxCloseReportData.stream().map(this::adx2ReportVo).collect(Collectors.toList());
        result.addAll(closeResult);
        return result;
    }

    /**
     * 必选实时收入折线图
     */
    public AchievementConsumePeriodQuotaVo getRtbRealTimeData(Operator operator, QueryAchieveRtbVo queryVo, IncomeComposition composition) {
        //今天
        Timestamp beginOfToday = Utils.getBeginOfDay(new Timestamp(System.currentTimeMillis()));
        Timestamp endOfHour = incomeTimeUtil.getCurrentHourEndTime();
        //昨天
        Timestamp beginOfYesterday = Utils.getYesteday();
        Timestamp endOfYesterday = Utils.getEndOfDay(Utils.getYesteday());
        //上周今天
        Timestamp beginOfLastWeek = Utils.getBeginOfDay(incomeTimeUtil.getLastWeekTime());
        Timestamp endOfLastWeek = Utils.getEndOfDay(incomeTimeUtil.getLastWeekTime());

        CompletableFuture<List<ConsumeHourData>> yesterdayDataFuture = CompletableFuture.supplyAsync(() -> getBeforeConsumeHourData(operator, queryVo, composition, beginOfYesterday, endOfYesterday), threadConfig.getExecutor());
        CompletableFuture<List<ConsumeHourData>> lastWeekDataFuture = CompletableFuture.supplyAsync(() -> getBeforeConsumeHourData(operator, queryVo, composition, beginOfLastWeek, endOfLastWeek), threadConfig.getExecutor());
        CompletableFuture<List<ConsumeHourData>> todayDataFuture = CompletableFuture.supplyAsync(() -> rtbService.getTheDayConsumeHourData(operator, buildQueryParamByTime(queryVo, beginOfToday, endOfHour), composition), threadConfig.getExecutor());

        List<ConsumeHourData> yesterdayData = yesterdayDataFuture.join();
        List<ConsumeHourData> lastWeekData = lastWeekDataFuture.join();
        List<ConsumeHourData> todayData = todayDataFuture.join();

        AchievementConsumePeriodQuotaVo realTimeVo = AchievementConsumePeriodQuotaVo.builder()
                .today_consume_stats(todayData)
                .yesterday_consume_stats(yesterdayData)
                .last_week_consume_stats(lastWeekData)
                .build();
        return realTimeVo;
    }

    private List<ConsumeHourData> getBeforeConsumeHourData(Operator operator, QueryAchieveRtbVo queryVo, IncomeComposition composition, Timestamp begin, Timestamp end) {
        return rtbService.getTheDayConsumeHourDataWithCache(new RealTimeCacheParam(operator, buildQueryParamByTime(queryVo, begin, end), composition));
    }

    /**
     * 必选实时收入汇总表,1
     */
    public List<RtbAchievementVo> getRtbRealTimeDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> rtbRealTimeSum = rtbService.getRtbRealTimeSum(rtbHelper.changeOperationToAdmin(operator), buildTodayQueryParam(queryVo));
        rtbRealTimeSum = rtbHelper.filterRtbRealTimeDataByOperation(rtbRealTimeSum, operator);
        //转化
        return this.enhanceRtbSumData(rtbRealTimeSum, queryVo2Dto(queryVo));
    }


    /**
     * 实时收入汇总表,dpa
     */
    public List<RtbAchievementVo> getDpaRealTimeDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> rtbRealTimeSum = rtbService.getDpaRealTimeSum(rtbHelper.changeOperationToAdmin(operator), buildTodayQueryParam(queryVo));
        rtbRealTimeSum = rtbHelper.filterRtbRealTimeDataByOperation(rtbRealTimeSum, operator);
        //转化
        return this.enhanceRtbSumData(rtbRealTimeSum, queryVo2Dto(queryVo));
    }


    /**
     * 商业起飞实时收入汇总表,1
     */
    public List<RtbAchievementVo> getBsiFlyRealTimeDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> bsiFlyRealTimeSum = rtbService.getBsiFlyRealTimeSum(rtbHelper.changeOperationToAdmin(operator), buildTodayQueryParam(queryVo));
        bsiFlyRealTimeSum = rtbHelper.filterRtbRealTimeDataByOperation(bsiFlyRealTimeSum, operator);
        //转化
        List<RtbAchievementVo> rtbAchievementVos = enhanceRtbSumData(bsiFlyRealTimeSum, queryVo2Dto(queryVo));
        LOGGER.error("WebAchievementRtbService.getBsiFlyRealTimeDetail complete");
        return rtbAchievementVos;
    }

    /**
     * 内容起飞实时收入汇总表,1
     */
    public List<RtbAchievementVo> getContentFlyRealTimeDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> bsiFlyRealTimeSum = rtbService.getContentFlyRealTimeSum(rtbHelper.changeOperationToAdmin(operator), buildTodayQueryParam(queryVo));
        bsiFlyRealTimeSum = rtbHelper.filterRtbRealTimeDataByOperation(bsiFlyRealTimeSum, operator);
        //转化
        return enhanceRtbSumData(bsiFlyRealTimeSum, queryVo2Dto(queryVo));
    }

    /**
     * ADX实时收入汇总表,1
     */
    public List<AdxAchievementVo> getAdxRealTimeDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> adxRealTimeSum = rtbService.getAdxRealTimeSum(rtbHelper.changeOperationToAdmin(operator), buildTodayQueryParam(queryVo));
        adxRealTimeSum = rtbHelper.filterRtbRealTimeDataByOperation(adxRealTimeSum, operator);
        //转化
        return enhanceAdxSumData(adxRealTimeSum, queryVo2Dto(queryVo));

    }

    /**
     * 必选累计收入汇总表
     */
    public List<RtbAchievementVo> getRtbSumDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> sumData = rtbService.getRtbSumDetail(operator, queryVo2Dto(queryVo));
        //翻译累计汇总表
        List<RtbAchievementVo> rtbAchievementVos = this.enhanceRtbSumData(sumData, queryVo2Dto(queryVo));
        buildCreativeVo(queryVo, rtbAchievementVos);
        return rtbAchievementVos;
    }

    /**
     * 必选累计收入汇总表 取自wallet_consume_day
     */
    public List<RtbAchievementVo> getRtbSumDetailV2(Operator operator, QueryAchieveRtbVo queryVo) {
        if (!isWalletES(queryVo)) {
            return getRtbSumDetail(operator, queryVo);
        }
        List<AchievementRtbData> sumData = rtbService.getRtbSumDetailV2(operator, queryVo2Dto(queryVo));
        //翻译累计汇总表
        List<RtbAchievementVo> rtbAchievementVos = this.enhanceRtbSumData(sumData, queryVo2Dto(queryVo));
        buildCreativeVo(queryVo, rtbAchievementVos);
        return rtbAchievementVos;
    }

    private void buildCreativeVo(QueryAchieveRtbVo queryVo, List<RtbAchievementVo> rtbAchievementVos) {
        if (!new Timestamp(queryVo.getDate_end()).toLocalDateTime().minusDays(1).isBefore(new Timestamp(queryVo.getDate_begin()).toLocalDateTime())) {
            rtbAchievementVos.forEach(
                    item -> {
                        item.setOnline_creative(null);
                        item.setNew_creative(null);
                    }
            );
        }
    }

    /**
     * DPA累计收入汇总表
     */
    public List<RtbAchievementVo> getDpaSumDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> sumData = rtbService.getDpaSumDetailWithClose(operator, queryVo2Dto(queryVo));
        //翻译累计汇总表
        List<RtbAchievementVo> rtbAchievementVos = this.enhanceRtbSumData(sumData, queryVo2Dto(queryVo));

        return rtbAchievementVos;
    }

    /**
     * 商业起飞累计收入汇总表
     */
    public List<RtbAchievementVo> getBsiFlySumDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> sumData = rtbService.getBsiFlySumDetail(operator, queryVo2Dto(queryVo), true);
        //翻译累计汇总表
        List<RtbAchievementVo> rtbAchievementVos = enhanceRtbSumData(sumData, queryVo2Dto(queryVo));
        buildCreativeVo(queryVo, rtbAchievementVos);
        return rtbAchievementVos;
    }

//    /**
//     * 个人起飞收入汇总表
//     */
//    public List<AdxAchievementVo> getPersonFlySumDetail(Operator operator, QueryAchieveRtbVo queryVo) {
//        List<AchievementRtbData> sumData = rtbService.getConsumeDataByFunctionV2(operator, queryVo2Dto(queryVo), IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByAccountAgentByWideES);
//        //翻译累计汇总表
//        //转化
//        List<AdxAchievementVo> achievementVos = enhanceAdxSumData(sumData, queryVo2Dto(queryVo));
//        return achievementVos;
//
//    }

    /**
     * 内容起飞收入汇总表
     */
    public List<RtbAchievementVo> getContentFlySumDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> sumData = rtbService.getContentFlySumDetail(operator, queryVo2Dto(queryVo));
        //翻译累计汇总表
        List<RtbAchievementVo> rtbAchievementVos = enhanceRtbSumData(sumData, queryVo2Dto(queryVo));
        buildCreativeVo(queryVo, rtbAchievementVos);
        return rtbAchievementVos;
    }

    /**
     * ADX累计收入汇总表
     */
    public List<AdxAchievementVo> getAdxSumDetail(Operator operator, QueryAchieveRtbVo queryVo) {
        List<AchievementRtbData> sumData = rtbService.getAdxDaySourceDataWithClose(operator, queryVo2Dto(queryVo));
        //翻译累计汇总表
        //转化
        List<AdxAchievementVo> achievementVos = enhanceAdxSumData(sumData, queryVo2Dto(queryVo));
        return achievementVos;

    }


    /**
     * 累计收入折线图
     */
    public List<AchievementPeriodDayVo> getDayChart(Operator operator, QueryAchieveRtbVo queryVo, IncomeComposition composition) {
        //折线图
        List<AchievementPeriodDay> rtbSumChart = rtbService.getDayChart(operator, queryVo2Dto(queryVo), composition);
        return rtbSumChart.stream().map(this::day2PeriodDayVo).collect(Collectors.toList());
    }

    private AchievementPeriodDayVo day2PeriodDayVo(AchievementPeriodDay day) {
        AchievementPeriodDayVo vo = AchievementPeriodDayVo.builder().build();
        BeanUtils.copyProperties(day, vo);
        return vo;
    }

    public QueryAchieveDto queryVo2Dto(QueryAchieveRtbVo vo) {
        Timestamp beginTime = vo.getDate_begin() == null ? null : new Timestamp(vo.getDate_begin());
        Timestamp endTime = vo.getDate_end() == null ? null : new Timestamp(vo.getDate_end());

        webAchievementHelper.validQueryAchieveVo(vo);

        return QueryAchieveDto.builder()
                .dateBegin(beginTime)
                .dateEnd(endTime)
                .saleGroupIds(vo.getSale_group_ids())
                .saleIds(vo.getSale_ids())
                .agentIds(vo.getAgent_ids())
                .accountIds(vo.getAccount_ids())
                .customerIds(vo.getCustomer_ids())
                .categoryFirstIds(vo.getCategory_first_ids())
                .categorySecondIds(vo.getCategory_second_ids())
                .bizIndustryCategoryFirstIds(vo.getBiz_industry_category_first_ids())
                .bizIndustryCategorySecondIds(vo.getBiz_industry_category_second_ids())
                .resourceIds(vo.getResource_ids())
                .platformIds(vo.getPlatform_ids())
                .agentCustomerIds(vo.getAgent_customer_ids())
                .calSort(null != vo.getCal_sort() ? vo.getCal_sort() : CalculateType.TOTAL_CONSUME.getCode())
                .sortOrder(null != vo.getSort_order() ? vo.getSort_order() : SortOrderType.DESC.getCode())
                .aggregateType(vo.getAggregate_type())
                .productIds(vo.getProduct_ids())
                .groupIds(vo.getGroup_ids())
                .teamKpiAggField(vo.getTeam_kpi_agg_field())
                .cluePassObjId(vo.getTask_id())
                .accUnitedFirstIndustryIds(vo.getAcc_united_first_industry_ids())
                .accUnitedSecondIndustryIds(vo.getAcc_united_second_industry_ids())
                .accUnitedThirdIndustryIds(vo.getAcc_united_third_industry_ids())
                .customerUnitedFirstIndustryIds(vo.getCustomer_united_first_industry_ids())
                .customerUnitedSecondIndustryIds(vo.getCustomer_united_second_industry_ids())
                .customerUnitedThirdIndustryIds(vo.getCustomer_united_third_industry_ids())
                .build();
    }

    /**
     * 聚合，翻译adx汇总数据
     */
    private List<AdxAchievementVo> enhanceAdxSumData(List<AchievementRtbData> sumData, QueryAchieveDto query) {
        if (CollectionUtils.isEmpty(sumData)) {
            return Collections.emptyList();
        }
        // 翻译帐号
        List<Integer> accountIds = sumData.stream().map(AchievementRtbData::getAccountId).distinct().collect(Collectors.toList());
        Map<Integer, AccountBaseDto> accountDtoMap = queryAccountService.getAccountBaseDtoMapInIdsFromReadOnly(accountIds);
        //翻译dsp
        List<AdxBidderConfigDto> adxBidderConfigDtos = adxBidderDayService.queryConfigByAccountIds(accountIds);
        Map<Integer, String> accountBidderNameMap = adxBidderConfigDtos.stream().collect(Collectors.toMap(AdxBidderConfigDto::getBidderId, AdxBidderConfigDto::getName));

        //翻译销售
        Map<Integer, SaleDto> saleMap = saleService.getAllSaleMap();
        List<AdxAchievementVo> aggData = Lists.newArrayList();

        //总计
        AchievementRtbData quota = rtbHelper.calculateAchievementRtbData(sumData);
        AdxAchievementVo quotaVo = rtbDto2AdxVo(quota, accountDtoMap, saleMap, accountBidderNameMap);
        quotaVo.setIs_collect(1);
        aggData.add(quotaVo);

        List<AchievementRtbData> sortedList = sumData.stream().sorted(Comparator.comparing(AchievementRtbData::getTotalConsume).reversed()).collect(Collectors.toList());
        sortedList.forEach(item -> {
            AdxAchievementVo vo = rtbDto2AdxVo(item, accountDtoMap, saleMap, accountBidderNameMap);
            aggData.add(vo);
        });

        List<AdxAchievementVo> adxAchievementVos = sortAdxDataDynamic(aggData, query);
        return adxAchievementVos;
    }

    /**
     * 聚合，翻译消耗汇总数据
     */
    public List<RtbAchievementVo> enhanceRtbSumData(List<AchievementRtbData> sumData, QueryAchieveDto query) {
        if (CollectionUtils.isEmpty(sumData)) {
            return Collections.emptyList();
        }
        // 翻译帐号
        List<Integer> accountIds = sumData.stream().map(AchievementRtbData::getAccountId).distinct().collect(Collectors.toList());
        Map<Integer, AccountBaseDto> accountDtoMap = queryAccountService.getAccountBaseDtoMapInIdsFromReadOnly(accountIds);


        //翻译品牌(产品)
        List<Integer> productIds = sumData.stream().map(AchievementRtbData::getProductId).distinct().collect(Collectors.toList());

        //翻译集团
        List<Integer> groupIds = sumData.stream().map(AchievementRtbData::getGroupId).distinct().collect(Collectors.toList());

        Map<Integer, CorporationGroupDto> idMapGroup = corporationGroupService.queryGroupsInIds(groupIds);
        Map<Integer, String> groupNameMap = idMapGroup.values().stream().collect(Collectors.toMap(CorporationGroupDto::getId, CorporationGroupDto::getName));


        Map<Integer, ProductDto> idMapProduct = corporationGroupService.queryProductInIds(productIds);
        Map<Integer, String> productNameMap = idMapProduct.values().stream().collect(Collectors.toMap(ProductDto::getId, ProductDto::getName));


        // 翻译代理商
        List<Integer> agentIds = sumData.stream().map(AchievementRtbData::getAgentId).distinct().collect(Collectors.toList());
        Map<Integer, AgentDto> agentDtoMap = agentService.getAgentMapInIds(agentIds);

        //翻译销售
        Map<Integer, SaleDto> saleMap = saleService.getAllSaleMap();

        return this.aggData(sumData, query, agentDtoMap, accountDtoMap, productNameMap, groupNameMap, saleMap);
    }

    /**
     * 根据反射动态聚合
     */
    public List<RtbAchievementVo> aggData(List<AchievementRtbData> sumData, QueryAchieveDto query, Map<Integer, AgentDto> agentDtoMap,
                                          Map<Integer, AccountBaseDto> accountDtoMap,
                                          Map<Integer, String> productMap, Map<Integer, String> groupMap,
                                          Map<Integer, SaleDto> saleMap) {

        Integer aggregate_type = query.getAggregateType();
        String firstField = "agentId";
        String secondField = "productId";

        Function<AchievementRtbData, Integer> firstGroup = AchievementRtbData::getAgentId;
        Function<AchievementRtbData, Integer> secondGroup = AchievementRtbData::getProductId;

        if (Integer.valueOf(1).equals(aggregate_type)) {
            firstField = "productId";
            secondField = "agentId";
            firstGroup = AchievementRtbData::getProductId;
            secondGroup = AchievementRtbData::getAgentId;
        }

        List<RtbAchievementVo> aggData = Lists.newArrayList();
        //总计
        AchievementRtbData quota = rtbHelper.calculateAchievementRtbData(sumData);
        RtbAchievementVo quotaVo = rtbDto2RtbVo(quota, agentDtoMap, accountDtoMap, productMap, groupMap, saleMap);
        quotaVo.setIs_collect(1);

        aggData.add(quotaVo);

        //嵌套分组
        Map<Integer, Map<Integer, List<AchievementRtbData>>> groupData = sumData.stream().collect(Collectors.groupingBy(firstGroup,
                Collectors.groupingBy(secondGroup)));

        String finalFirstField = firstField;
        String finalSecondField = secondField;
        groupData.forEach((k, v) -> {
            List<AchievementRtbData> dataByCompany = v.values().stream().map(item -> rtbHelper.calculateAchievementRtbData(item)).collect(Collectors.toList());

            AchievementRtbData sumByAgent = rtbHelper.calculateAchievementRtbData(dataByCompany);

            setDataField(finalFirstField, k, sumByAgent);

            RtbAchievementVo sumByAgentVo = rtbDto2RtbVo(sumByAgent, agentDtoMap, accountDtoMap, productMap, groupMap, saleMap);
            sumByAgentVo.setIs_collect(2);
            aggData.add(sumByAgentVo);

            v.forEach((k1, v1) -> {
                AchievementRtbData sumByCompany = rtbHelper.calculateAchievementRtbData(v1);

                setDataField(finalFirstField, k, sumByCompany);
                setDataField(finalSecondField, k1, sumByCompany);

                RtbAchievementVo sumByCompanyVo = rtbDto2RtbVo(sumByCompany, agentDtoMap, accountDtoMap, productMap, groupMap, saleMap);

                //代理商+公司组汇总，is_collect=3
                //代理商+产品汇总，is_collect=3
                sumByCompanyVo.setIs_collect(3);
                aggData.add(sumByCompanyVo);

                //广告主明细
                v1.forEach(item -> {
                    RtbAchievementVo rtbVo = rtbDto2RtbVo(item, agentDtoMap, accountDtoMap, productMap, groupMap, saleMap);
                    aggData.add(rtbVo);
                });
            });
        });
        return this.sortRtbDataDynamic(aggData, query);
    }

    private void setDataField(String finalField, Integer value, AchievementRtbData sumData) {
        Field field;
        try {
            field = sumData.getClass().getDeclaredField(finalField);
            field.setAccessible(true);
            field.set(sumData, value);
        } catch (NoSuchFieldException | IllegalAccessException e) {
            LOGGER.error("WebAchievementRtbService.setDataField failed ", e);
        }
    }

    /**
     * 动态排序rtb
     */
    public List<RtbAchievementVo> sortRtbDataDynamic(List<RtbAchievementVo> aggData, QueryAchieveDto query) {
        final Integer sort_order = query.getSortOrder();
        CalculateType sortCal = CalculateType.getByCode(query.getCalSort());
        Integer aggregate_type = query.getAggregateType();
        String firstField = "agent_id";
        String secondField = "product_id";

        if (Integer.valueOf(1).equals(aggregate_type)) {
            firstField = "product_id";
            secondField = "agent_id";
        }
        Comparator<RtbAchievementVo> rtbComparator = Integer.valueOf(1).equals(sort_order) ? Comparator.comparing(webAchievementHelper.getSortFunction(sortCal))
                : Comparator.comparing(webAchievementHelper.getSortFunction(sortCal)).reversed();

        List<RtbAchievementVo> result = new ArrayList<>(aggData.size());
        //总计
        List<RtbAchievementVo> total = aggData.stream().filter(item -> Integer.valueOf(1).equals(item.getIs_collect())).collect(Collectors.toList());
        result.add(total.get(0));
        //代理商
        List<RtbAchievementVo> aggFirst = aggData.stream().filter(item -> Integer.valueOf(2).equals(item.getIs_collect())).collect(Collectors.toList());
        List<RtbAchievementVo> agentSortedList = aggFirst.stream().sorted(rtbComparator).collect(Collectors.toList());

        String finalFirstField = firstField;
        String finalSecondField = secondField;
        agentSortedList.forEach(item -> {
            result.add(item);
            List<RtbAchievementVo> companyList = aggData.stream().filter(each -> {
                try {
                    Field first = item.getClass().getDeclaredField(finalFirstField);
                    first.setAccessible(true);
                    Field second = each.getClass().getDeclaredField(finalFirstField);
                    second.setAccessible(true);

                    return first.get(item).equals(second.get(each))
                            && Integer.valueOf(3).equals(each.getIs_collect());
                } catch (NoSuchFieldException | IllegalAccessException e) {
                    return false;
                }
            }).collect(Collectors.toList());
            List<RtbAchievementVo> companySortedList = companyList.stream().sorted(rtbComparator).collect(Collectors.toList());

            companySortedList.forEach(item1 -> {
                        result.add(item1);
                        List<RtbAchievementVo> atomList = aggData.stream().filter(atom -> {
                            try {
                                Field first1 = item1.getClass().getDeclaredField(finalFirstField);
                                first1.setAccessible(true);
                                Field first2 = atom.getClass().getDeclaredField(finalFirstField);
                                first2.setAccessible(true);

                                Field second1 = item1.getClass().getDeclaredField(finalSecondField);
                                second1.setAccessible(true);
                                Field second2 = atom.getClass().getDeclaredField(finalSecondField);
                                second2.setAccessible(true);

                                return first1.get(item1).equals(first2.get(atom))
                                        && second2.get(item1).equals(second2.get(atom))
                                        && null == atom.getIs_collect();
                            } catch (NoSuchFieldException | IllegalAccessException e) {
                                return false;
                            }
                        }).collect(Collectors.toList());
                        List<RtbAchievementVo> atomSortedList = atomList.stream().sorted(rtbComparator).collect(Collectors.toList());

                        atomSortedList.forEach(
                                item2 -> {
                                    result.add(item2);
                                }
                        );
                    }
            );
        });
        return result;
    }

    /**
     * 动态排序adx
     */
    public List<AdxAchievementVo> sortAdxDataDynamic(List<AdxAchievementVo> aggData, QueryAchieveDto query) {
        final Integer sort_order = query.getSortOrder();
        CalculateType sortCal = CalculateType.getByCode(query.getCalSort());

        Comparator<AdxAchievementVo> adxComparator = Integer.valueOf(1).equals(sort_order) ? Comparator.comparing(webAchievementHelper.getAdxSortFunction(sortCal))
                : Comparator.comparing(webAchievementHelper.getAdxSortFunction(sortCal)).reversed();

        List<AdxAchievementVo> result = new ArrayList<>(aggData.size());
        //总计
        List<AdxAchievementVo> total = aggData.stream().filter(item -> Integer.valueOf(1).equals(item.getIs_collect())).collect(Collectors.toList());
        result.add(total.get(0));

        List<AdxAchievementVo> aggFirst = aggData.stream().filter(item -> !Integer.valueOf(1).equals(item.getIs_collect())).collect(Collectors.toList());
        List<AdxAchievementVo> aggSortedList = aggFirst.stream().sorted(adxComparator).collect(Collectors.toList());

        aggSortedList.forEach(item -> {
            result.add(item);
        });
        return result;
    }

    private RtbAchievementVo rtbDto2RtbVo(AchievementRtbData item, Map<Integer, AgentDto> agentDtoMap, Map<Integer, AccountBaseDto> accountDtoMap,
                                          Map<Integer, String> productMap, Map<Integer, String> groupMap, Map<Integer, SaleDto> saleMap) {

        AgentDto agentDto = agentDtoMap.getOrDefault(item.getAgentId(), AgentDto.builder().id(0).name("其他").build());
        AccountBaseDto accountBaseDto = accountDtoMap.getOrDefault(item.getAccountId(), AccountBaseDto.builder().build());
        //TODO 填充翻译
        //CompanyGroupDto companyGroupDto = companyGroupMap.getOrDefault(item.getCompanyGroupId(), CompanyGroupDto.builder().id(0).name("其他").build());

        //同比（元）=  今日当前总消耗-上周今日同时总消耗
        BigDecimal weekdayCompare = null != item.getLastWeekDayConsume() ? item.getTotalConsume().subtract(item.getLastWeekDayConsume()) : null;
        //同比（%）=(今日当前总消耗-上周今日同时总消耗)/上周今日同时总消耗*100
        BigDecimal weekdayCompareRate = null != weekdayCompare ? incomeUtil.divide(weekdayCompare.multiply(BigDecimal.valueOf(100)), item.getLastWeekDayConsume(), 2) : null;
        //环比（元）=今日当前总消耗-昨日同时总消耗
        BigDecimal yesterdayCompare = null != item.getYesterdayConsume() ? item.getTotalConsume().subtract(item.getYesterdayConsume()) : null;
        //环比（%）=(今日当前总消耗-昨日同时总消耗)/昨日同时总消耗*100
        BigDecimal yesterdayCompareRate = null != yesterdayCompare ? incomeUtil.divide(yesterdayCompare.multiply(BigDecimal.valueOf(100)), item.getYesterdayConsume(), 2) : null;

        BigDecimal consume = Optional.ofNullable(item.getTotalConsume()).orElse(BigDecimal.ZERO);
        BigDecimal clickCount = Optional.ofNullable(item.getClickCount()).orElse(BigDecimal.ZERO);
        BigDecimal showCount = Optional.ofNullable(item.getShowCount()).orElse(BigDecimal.ZERO);
        //TODO 填充
        return RtbAchievementVo.builder()
                .agent_id(item.getAgentId())
                .agent_account_id(agentDto.getSysAgentId())
                //.company_id(item.getCompanyGroupId())
                .product_id(item.getProductId())
                .product_name(productMap.getOrDefault(item.getProductId(), "其他"))
                .group_name(groupMap.getOrDefault(item.getGroupId(), ""))
                .account_id(item.getAccountId())
                .account_name(accountBaseDto.getUsername())
                .agent_name(agentDto.getName())
                .straight_name(rtbHelper.getSalesName(item.getStraightSaleId(), saleMap))
                .channel_name(rtbHelper.getSalesName(item.getChannelSaleId(), saleMap))
                .operate_name(rtbHelper.getSalesName(item.getOperateSaleId(), saleMap))
                .total_consume(item.getTotalConsume())
                .cash(item.getCashAmount())
                .red_packet(item.getRedPacketAmount())
                .special_red_packet(item.getSpecialRedPacketAmount())
                .click_amount(item.getClickCount())
                .show_amount(item.getShowCount())
                .CPC(AdUtils.getCpc(consume, clickCount))
                .CTR(AdUtils.getCtr(clickCount, showCount))
                .eCPM(AdUtils.getEcpm(consume, showCount))
                .online_creative(item.getOnLineCreative())
                .new_creative(item.getNewCreative())
                .day_compare(yesterdayCompare)
                .day_compare_rate(yesterdayCompareRate)
                .weekday_compare(weekdayCompare)
                .weekday_compare_rate(weekdayCompareRate)
                .build();
    }

    private AdxAchievementVo rtbDto2AdxVo(AchievementRtbData item, Map<Integer, AccountBaseDto> accountDtoMap, Map<Integer, SaleDto> saleMap, Map<Integer, String> accountBidderNameMap) {
        AccountBaseDto accountBaseDto = accountDtoMap.getOrDefault(item.getAccountId(), AccountBaseDto.builder().build());

        //同比（元）=  今日当前总消耗-上周今日同时总消耗
        BigDecimal weekdayCompare = null != item.getLastWeekDayConsume() ? item.getTotalConsume().subtract(item.getLastWeekDayConsume()) : null;
        //同比（%）=(今日当前总消耗-上周今日同时总消耗)/上周今日同时总消耗*100
        BigDecimal weekdayCompareRate = null != weekdayCompare ? incomeUtil.divide(weekdayCompare.multiply(BigDecimal.valueOf(100)), item.getLastWeekDayConsume(), 2) : null;
        //环比（元）=今日当前总消耗-昨日同时总消耗
        BigDecimal yesterdayCompare = null != item.getYesterdayConsume() ? item.getTotalConsume().subtract(item.getYesterdayConsume()) : null;
        //环比（%）=(今日当前总消耗-昨日同时总消耗)/昨日同时总消耗*100
        BigDecimal yesterdayCompareRate = null != yesterdayCompare ? incomeUtil.divide(yesterdayCompare.multiply(BigDecimal.valueOf(100)), item.getYesterdayConsume(), 2) : null;

        BigDecimal consume = Optional.ofNullable(item.getTotalConsume()).orElse(BigDecimal.ZERO);
        BigDecimal clickCount = Optional.ofNullable(item.getClickCount()).orElse(BigDecimal.ZERO);
        BigDecimal showCount = Optional.ofNullable(item.getShowCount()).orElse(BigDecimal.ZERO);

        return AdxAchievementVo.builder()
                .account_id(item.getAccountId())
                .account_name(accountBaseDto.getUsername())
                .dsp_name(accountBidderNameMap.getOrDefault(item.getBidderId(), null))
                .straight_name(rtbHelper.getSalesName(item.getStraightSaleId(), saleMap))
                .channel_name(rtbHelper.getSalesName(item.getChannelSaleId(), saleMap))
                .operate_name(rtbHelper.getSalesName(item.getOperateSaleId(), saleMap))
                .total_consume(item.getTotalConsume())
                .cash(item.getCashAmount())
                .red_packet(item.getRedPacketAmount())
                .click_amount(item.getClickCount())
                .show_amount(item.getShowCount())
                .CPC(AdUtils.getCpc(consume, clickCount))
                .CTR(AdUtils.getCtr(clickCount, showCount))
                .eCPM(AdUtils.getEcpm(consume, showCount))
                .day_compare(yesterdayCompare)
                .day_compare_rate(yesterdayCompareRate)
                .weekday_compare(weekdayCompare)
                .weekday_compare_rate(weekdayCompareRate)
                .build();
    }


    public QueryAchieveDto buildTodayQueryParam(QueryAchieveRtbVo queryVo) {
        //今天
        Timestamp beginOfToday = Utils.getBeginOfDay(new Timestamp(System.currentTimeMillis()));
        Timestamp endOfHour = Timestamp.valueOf(LocalDateTime.now());

        QueryAchieveDto queryDto = queryVo2Dto(queryVo);
        queryDto.setDateBegin(beginOfToday);
        queryDto.setDateEnd(endOfHour);
        queryDto.setQueryDetailListType(queryVo.getQuery_detail_list_type());
        return queryDto;
    }

    private QueryAchieveDto buildQueryParamByTime(QueryAchieveRtbVo queryVo, Timestamp begin, Timestamp end) {

        QueryAchieveDto queryDto = queryVo2Dto(queryVo);
        queryDto.setDateBegin(begin);
        queryDto.setDateEnd(end);

        return queryDto;
    }


    private RtbReportAchievementVo rtb2ReportVo(RtbReportAchievement item) {
        RtbReportAchievementVo vo = RtbReportAchievementVo.builder().build();
        BeanUtils.copyProperties(item, vo);
        vo.setDate_time(item.getDate_time());
        vo.setAgent_account_id(item.getAgent_account_id());
        return vo;
    }

    private AdxReportAchievementVo adx2ReportVo(AdxReportAchievement item) {
        AdxReportAchievementVo vo = AdxReportAchievementVo.builder().build();
        BeanUtils.copyProperties(item, vo);
        vo.setFirst_category_name(item.getCategory_first_name());
        vo.setSecond_category_name(item.getCategory_second_name());
        vo.setDate_time(item.getDate_time());
        vo.setGroup_id(item.getGroup_id());
        vo.setGroup_name(item.getGroup_name());
        return vo;
    }


    public List<RtbIncomeVo> getRtbIncomeTrendForAgentPortrait(Operator operator, QueryAchieveDto queryDto) {
        Map<RtbComposition, List<AchievementRtbData>> data = new HashMap<>();

        QueryAchieveDto rtbQueryDto = QueryAchieveDto.builder().build();
        QueryAchieveDto pickUpQueryDto = QueryAchieveDto.builder().build();
        BeanUtils.copyProperties(queryDto, rtbQueryDto);
        rtbQueryDto.setOrAgentIds(queryDto.getRtbNoBelongAgentIds());
        BeanUtils.copyProperties(queryDto, pickUpQueryDto);
        pickUpQueryDto.setOrAgentIds(queryDto.getPickUpNoBelongAgentIds());
        buildClosedTime(queryDto);

        List<AchievementRtbData> bsiFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);
        data.put(RtbComposition.BUSINESS_FLY_ADVANCE_PAY, bsiFly);
        //ADX
        List<AchievementRtbData> adx = rtbService.getAdxDaySourceData(operator, rtbQueryDto);
        data.put(RtbComposition.ADX, adx);
        List<AchievementRtbData> dpa = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.DPA, achieveFromWalletService::getWalletAggByDayByWideES);
        data.put(RtbComposition.DPA, dpa);
        //商单
        queryDto.setContractCheck(true);

        // 属于合约广告的产品类型：CPT、GD、闪屏、TopView、非标、搜索CPT、OTT、直播类订单、直播商单、商业起飞（后付费）
        //品牌广告   目前包括：CPT、GD、闪屏、TopView、非标、搜索CPT、OTT、直播类订单
        List<AchievementRtbData> cpt = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.CPT, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> gd = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.GD, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> ssa = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.SSA, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> topView = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.TOP_VIEW, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> noStand = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.NON_STANDARD, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> searchCpt = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.SEARCH_CPT, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> ott = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.OTT_TOP_VIEW, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> live = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.LIVE, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> livePickUp = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.LIVE_PICK_UP, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);
        List<AchievementRtbData> businessPostPay = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.BUSINESS_FLY_POST_PAY, achieveFromBillService::getBillAggByDayForIncomeTrendByWideES);

        //效果广告   目前包括：CPC、竞价CPM、DPA、ADX
        List<AchievementRtbData> cpc = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CPC, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> cpm = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CPM, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> contentFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CONTENT_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> personFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        //up主广告   目前包括：花火商单、直播商单、商业起飞（后付费）、商业起飞（预付费）、内容起飞、个人起飞
        List<AchievementRtbData> pickUpOrderConfirm = rtbService.getConsumeDataByFunctionV2(operator, pickUpQueryDto, IncomeComposition.PICK_UP_CONFIRMED, achieveFromBillService::getBillAggByDayByWideES);
        List<AchievementRtbData> boostingPickUpOrderConfirm = rtbService.getConsumeDataByFunctionV2(operator, pickUpQueryDto, IncomeComposition.BOOSTING_CONFIRMED, achieveFromBillService::getBillAggByDayByWideES);
        pickUpOrderConfirm.addAll(boostingPickUpOrderConfirm);
        //预付费来自 wallet  后付费来自bill
        List<AchievementRtbData> businessAdvancePay = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);

        data.put(RtbComposition.CPT, cpt);
        data.put(RtbComposition.GD, gd);
        data.put(RtbComposition.SSA, ssa);
        data.put(RtbComposition.TOP_VIEW, topView);
        data.put(RtbComposition.NON_STANDARD, noStand);
        data.put(RtbComposition.SEARCH_CPT, searchCpt);
        data.put(RtbComposition.OTT_TOP_VIEW, ott);
        data.put(RtbComposition.LIVE, live);
        data.put(RtbComposition.LIVE_PICK_UP, livePickUp);
        data.put(RtbComposition.BUSINESS_FLY_POST_PAY, businessPostPay);
        data.put(RtbComposition.CPC, cpc);
        data.put(RtbComposition.CPM, cpm);
        data.put(RtbComposition.CONTENT_FLY, contentFly);
        data.put(RtbComposition.PERSON_FLY, personFly);
        data.put(RtbComposition.PICK_UP_CONFIRM, pickUpOrderConfirm);
        data.put(RtbComposition.BUSINESS_FLY_ADVANCE_PAY, businessAdvancePay);

        List<RtbIncomeVo> rtbIncomeVos = buildTrendDayIncome(data, rtbQueryDto.getDateBegin(), rtbQueryDto.getDateEnd());

        return rtbIncomeVos;
    }

    /**
     * 效果收入结构前五排序
     */
    public static List<RtbCompositionRatioVo> getLimitRtbCompositionRatio(List<RtbCompositionRatioVo> data, Integer limitNum) {
        List<RtbCompositionRatioVo> limitList = data.stream().sorted(Comparator.comparing(RtbCompositionRatioVo::getAmount).reversed()).limit(limitNum).collect(Collectors.toList());
        BigDecimal limitAmount = limitList.stream().map(RtbCompositionRatioVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        BigDecimal totalAmount = data.stream().map(RtbCompositionRatioVo::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
        limitList.add(RtbCompositionRatioVo.builder().type_desc("其他").type(0).amount(totalAmount.subtract(limitAmount)).build());
        return limitList;
    }


    private void buildClosedTime(QueryAchieveDto queryDto) {
        if (queryDto.getCloseBillDateBegin() != null && queryDto.getDateBegin() != null) {
            queryDto.setDateBegin(queryDto.getCloseBillDateBegin().before(queryDto.getDateBegin()) ? queryDto.getDateBegin() : queryDto.getCloseBillDateBegin());
            queryDto.setIsClosed(ClosedType.IS_CLOSED.getCode());
        }
        if (queryDto.getDateEnd() != null && queryDto.getCloseBillDateEnd() != null) {
            queryDto.setDateEnd(queryDto.getCloseBillDateEnd().before(queryDto.getDateEnd()) ? queryDto.getCloseBillDateEnd() : queryDto.getDateEnd());
            queryDto.setIsClosed(ClosedType.IS_CLOSED.getCode());
        }
    }


    private List<RtbIncomeVo> buildTrendDayIncome(Map<RtbComposition, List<AchievementRtbData>> data, Timestamp begin, Timestamp end) {
        List<Timestamp> eachDays = Utils.getEachDays(begin, end);

        List<RtbIncomeVo> resultList = new ArrayList<>();

        for (int i = 0; i < eachDays.size(); i++) {
            Timestamp eachBegin = eachDays.get(i);
            Timestamp eachEnd = Utils.getEndOfDay(eachBegin);

            List<RtbCompositionIncomeVo> composition = data.entrySet().stream().map(
                    item -> {
                        RtbComposition key = item.getKey();
                        List<AchievementRtbData> itemValue = item.getValue();
                        List<AdIncome> rtbComposition = getRtbComposition(itemValue);
                        return RtbCompositionIncomeVo.builder()
                                .type(key.getCode())
                                .type_desc(key.getDesc())
                                .income(getDayIncome(rtbComposition, eachBegin, eachEnd))
                                .build();
                    }
            ).collect(Collectors.toList());
            BigDecimal income = composition.stream().map(RtbCompositionIncomeVo::getIncome).reduce(BigDecimal.ZERO, BigDecimal::add);
            resultList.add(RtbIncomeVo.builder().rtb_composition_income(composition).income(income).date(eachBegin).build());
        }
        return resultList;
    }

    private BigDecimal getDayIncome(List<AdIncome> adIncomes, Timestamp eachBegin, Timestamp eachEnd) {
        List<AdIncome> data = adIncomes.stream()
                .filter(dto -> dto.getDate().getTime() >= eachBegin.getTime() && dto.getDate().getTime() <= eachEnd.getTime())
                .collect(Collectors.toList());
        BigDecimal amount = data.stream().map(AdIncome::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);

        return amount;
    }

    private List<AdIncome> getRtbComposition(List<AchievementRtbData> data) {
        return rtbData2AdIncome(data);
    }

    private List<AdIncome> rtbData2AdIncome(List<AchievementRtbData> rtb) {
        List<AdIncome> incomes = rtb.stream().map(item -> {
            AdIncome ad = new AdIncome();
            ad.setAmount(item.getTotalConsume());
            ad.setDate(item.getDate());
            return ad;
        }).collect(Collectors.toList());
        return incomes;
    }

    public boolean isWalletES(QueryAchieveRtbVo param) {
        //有资源平台和资源位类型，查询超播数据源
        return CollectionUtils.isEmpty(param.getPlatform_ids()) && CollectionUtils.isEmpty(param.getResource_ids());
    }

}
