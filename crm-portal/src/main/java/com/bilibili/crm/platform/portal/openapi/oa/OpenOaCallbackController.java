package com.bilibili.crm.platform.portal.openapi.oa;

import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.portal.openapi.oa.convertor.OaCallbackConvertor;
import com.bilibili.crm.platform.portal.openapi.oa.service.OpenOaCallbackService;
import com.bilibili.crm.platform.portal.openapi.oa.vo.SyncBillFlowInfoVo;
import com.bilibili.crm.platform.portal.openapi.oa.vo.SyncBillFlowStateVo;
import com.bilibili.gateway.filter.bean.ApiResponse;
import com.bilibili.gateway.filter.bean.BaseApi;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Controller;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.validation.Valid;

/**
 * oa 回调相关接口
 *
 * <AUTHOR>
 * @date 2022/8/18 17:40
 */

@Controller
@RequestMapping("/openApi/v1/oa/callback")
@Api(value = "/oa/callback", description = "oa 回调相关接口")
@Slf4j
public class OpenOaCallbackController extends BaseApi {
    @Resource
    private OpenOaCallbackService openOaCallbackService;

    @ApiOperation(value = "OA 结算单流程编号、流程发起日期同步")
    @RequestMapping(value = "/flow/info", method = RequestMethod.POST)
    @ResponseBody
    public ApiResponse<String> syncBillFlowInfo(@RequestBody @Valid SyncBillFlowInfoVo syncBillFlowInfoVo) {

        log.info("syncBillFlowInfo msg {}", JSON.toJSONString(syncBillFlowInfoVo));

        openOaCallbackService.syncOaFlowInfo(OaCallbackConvertor.vo2Dto(syncBillFlowInfoVo));
        return ApiResponse.SUCCESS("success");
    }

    @ApiOperation(value = "OA 流程节点状态同步")
    @RequestMapping(value = "/flow/state", method = RequestMethod.POST)
    @ResponseBody
    public ApiResponse<String> syncBillFlowState(@RequestBody @Valid SyncBillFlowStateVo syncBillFlowStateVo) {

        log.info("syncBillFlowState msg {}", JSON.toJSONString(syncBillFlowStateVo));

        openOaCallbackService.syncOaFlowState(OaCallbackConvertor.vo2Dto(syncBillFlowStateVo));
        return ApiResponse.SUCCESS("success");
    }
}
