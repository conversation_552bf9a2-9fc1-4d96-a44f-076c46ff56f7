package com.bilibili.crm.platform.portal.webapi.mock;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.enums.FinanceType;
import com.bilibili.crm.platform.api.statement.dto.StatementQueryDto;
import com.bilibili.crm.platform.biz.dao.CrmStatementDao;
import com.bilibili.crm.platform.biz.dao.CrmStatementPickupReceivableBillDao;
import com.bilibili.crm.platform.biz.po.CrmStatementPickupReceivableBillPo;
import com.bilibili.crm.platform.biz.po.CrmStatementPickupReceivableBillPoExample;
import com.bilibili.crm.platform.biz.po.CrmStatementPo;
import com.bilibili.crm.platform.biz.po.CrmStatementPoExample;
import com.bilibili.crm.platform.biz.repo.statement.CrmStatementRepo;
import com.bilibili.crm.platform.biz.repo.statement.bill.CrmStatementPickupReceivableBillRepo;
import com.bilibili.crm.platform.biz.repo.statement.bill.param.QueryPickUpBillParam;
import com.bilibili.crm.platform.biz.service.QueryAccountService;
import com.bilibili.crm.platform.biz.service.statement.StatementListQuerier;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.statement.StatementFinanceTypeEnum;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/12/29 14:03
 */

@Slf4j
@RestController
@RequestMapping("/web_api/v1/mock/statement")
public class StatementMockController {

    @Resource
    private StatementListQuerier statementListQuerier;

    @Resource
    private QueryAccountService queryAccountService;

    @Resource
    private CrmStatementRepo crmStatementRepo;

    @Resource
    private CrmStatementPickupReceivableBillRepo crmStatementPickupReceivableBillRepo;

    @Resource
    private CrmStatementDao crmStatementDao;

    @Resource
    private CrmStatementPickupReceivableBillDao crmStatementPickupReceivableBillDao;

    /**
     * 刷历史数据 - 结算单财务类型
     *
     * @return 条数
     */
    @PostMapping("/finance_type/update")
    public Response<Integer> financeTypeUpdate() {
        // 查询所有的执行单
        List<CrmStatementPo> statementPos = statementListQuerier.queryPoList(StatementQueryDto.builder().build());

        if (CollectionUtils.isEmpty(statementPos)) {
            return Response.SUCCESS(0);
        }

        Lists.partition(statementPos, 500).forEach(this::batchUpdate);

        return Response.SUCCESS(statementPos.size());
    }

    /**
     * 分批更新
     *
     * @param statementPos 单批次
     */
    private void batchUpdate(List<CrmStatementPo> statementPos) {
        // 查询账号的财务类型信息
        List<Integer> accountIds = CrmUtils.convert(statementPos, CrmStatementPo::getAccountId);
        List<AccountBaseDto> accountBaseDtos = queryAccountService.queryAccount(accountIds);
        Map<Integer, AccountBaseDto> accountId2BaseMap = accountBaseDtos.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity(), (k1, k2) -> k1));

        // 花火授信执行单
        List<Integer> statementOfCreditOrder = Lists.newArrayList();
        // 普通执行单
        List<Integer> statementOfNormalOrder = Lists.newArrayList();

        statementPos.forEach(statement -> {
            Integer accountId = statement.getAccountId();
            AccountBaseDto accountBaseDto = accountId2BaseMap.get(accountId);
            if (Objects.nonNull(accountBaseDto) && FinanceType.VIRTUAL_MONEY.getCode().equals(accountBaseDto.getFinanceType())) {
                // 花火授信执行单
                statementOfCreditOrder.add(statement.getId());
            } else {
                // 普通执行单
                statementOfNormalOrder.add(statement.getId());
            }
        });

        // 更新花火授信执行单
        if (CollectionUtils.isNotEmpty(statementOfCreditOrder)) {
            crmStatementRepo.updateFinanceType(statementOfCreditOrder, StatementFinanceTypeEnum.PICK_UP_CREDIT_ORDER.getCode());
        }
        // 更新普通执行单
        if (CollectionUtils.isNotEmpty(statementOfNormalOrder)) {
            crmStatementRepo.updateFinanceType(statementOfNormalOrder, StatementFinanceTypeEnum.PICK_UP_NORMAL_ORDER.getCode());
        }
    }


    @PostMapping("/generate/pickup/delete")
    public Response<Integer> deletePickUp() {
        // 查询所有的花火授信执行单
        StatementQueryDto statementQueryDto = StatementQueryDto.builder()
                .financeTypeList(Collections.singletonList(StatementFinanceTypeEnum.PICK_UP_CREDIT_ORDER.getCode()))
                .build();
        List<CrmStatementPo> statementPos = statementListQuerier.queryPoList(statementQueryDto);

        if (CollectionUtils.isEmpty(statementPos)) {
            return Response.SUCCESS(0);
        }

        // 根据 账号 内外部属性过滤
        List<Integer> accountIds = CrmUtils.convertDistinct(statementPos, CrmStatementPo::getAccountId);

        if (CollectionUtils.isEmpty(accountIds)) {
            // 没有筛选到账号
            log.info(">>> 没有筛选到账号");
            return Response.SUCCESS(0);
        }
        List<AccountBaseDto> accountBaseDtos = queryAccountService.queryAccount(accountIds);
        Map<Integer, AccountBaseDto> accountBaseDtoMap = accountBaseDtos.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));
        log.info(">>> 过滤前: {}", statementPos.size());
        // 过滤外部账号
        statementPos = CrmUtils.filter(statementPos, statement -> accountBaseDtoMap.containsKey(statement.getAccountId()) && YesNoEnum.YES.getCode().equals(accountBaseDtoMap.get(statement.getAccountId()).getIsInner()));
        log.info(">>> 过滤后: {}", statementPos.size());

        // 删除内部账号的应收单
        // 循环删除，应该不会很多
        statementPos.forEach(statement -> crmStatementPickupReceivableBillRepo.deleteByStatementId(statement.getId()));

        return Response.SUCCESS(statementPos.size());
    }

    /**
     * 生成历史数据的结算单花火应收账单
     *
     * @return 条数
     */
    @PostMapping("/generate/pickup")
    public Response<Integer> generatePickUp() {
        // 查询所有的花火授信执行单
        StatementQueryDto statementQueryDto = StatementQueryDto.builder()
                .financeTypeList(Collections.singletonList(StatementFinanceTypeEnum.PICK_UP_CREDIT_ORDER.getCode()))
                .build();
        List<CrmStatementPo> statementPos = statementListQuerier.queryPoList(statementQueryDto);

        if (CollectionUtils.isEmpty(statementPos)) {
            return Response.SUCCESS(0);
        }

        // 根据 账号 内外部属性过滤
        List<Integer> accountIds = CrmUtils.convertDistinct(statementPos, CrmStatementPo::getAccountId);

        if (CollectionUtils.isEmpty(accountIds)) {
            // 没有筛选到账号
            log.info(">>> 没有筛选到账号");
            return Response.SUCCESS(0);
        }
        List<AccountBaseDto> accountBaseDtos = queryAccountService.queryAccount(accountIds);
        Map<Integer, AccountBaseDto> accountBaseDtoMap = accountBaseDtos.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));
        log.info(">>> 过滤前: {}", statementPos.size());
        // 过滤内部账号
        statementPos = CrmUtils.filter(statementPos, statement -> accountBaseDtoMap.containsKey(statement.getAccountId()) && YesNoEnum.NO.getCode().equals(accountBaseDtoMap.get(statement.getAccountId()).getIsInner()));
        log.info(">>> 过滤后: {}", statementPos.size());

        // 循环创建，应该不会很多
        statementPos.forEach(statement -> crmStatementPickupReceivableBillRepo.create(statement.getId(), statement.getMonth(), statement.getAmount()));

        return Response.SUCCESS(statementPos.size());
    }

    /**
     * 刷历史数据 - 财务结算单回款状态
     *
     * @return 条数
     */
    @RequestMapping(value = "/receive_state/refresh", method = RequestMethod.GET)
    @ResponseBody
    public Response<Integer> receiveStateUpdate(@RequestParam("statement_id") Integer statementId) {
        log.info("StatementMockController.receiveStateUpdate, statementId:{}", statementId);
        // 查询所有的花火账单
        CrmStatementPickupReceivableBillPoExample statementPickupReceivableBillPoExample = new CrmStatementPickupReceivableBillPoExample();
        if (null != statementId) {
            CrmStatementPickupReceivableBillPoExample.Criteria criteria = statementPickupReceivableBillPoExample.createCriteria();
            criteria.andStatementIdEqualTo(statementId);
        }
        List<CrmStatementPickupReceivableBillPo> pos = crmStatementPickupReceivableBillDao.selectByExample(statementPickupReceivableBillPoExample);

        // 查询花火账单对应的财务结算单
        List<Integer> statementIds = pos.stream().map(CrmStatementPickupReceivableBillPo::getStatementId).collect(Collectors.toList());
        CrmStatementPoExample crmStatementPoExample = new CrmStatementPoExample();
        crmStatementPoExample.createCriteria().andIdIn(statementIds);
        List<CrmStatementPo> statementPos = crmStatementDao.selectByExample(crmStatementPoExample);
        if (CollectionUtils.isEmpty(statementPos)) {
            return Response.SUCCESS(0);
        }

        // 将花火应收账单的回款状态，填充到财务结算单回款状态
        Map<Integer, CrmStatementPickupReceivableBillPo> statementId2PickupMap = pos.stream().collect(Collectors.toMap(CrmStatementPickupReceivableBillPo::getStatementId, Function.identity()));
        statementPos.forEach(statementPo -> {
            CrmStatementPickupReceivableBillPo pickupPo = statementId2PickupMap.get(statementPo.getId());
            statementPo.setReceiveState(pickupPo.getReceiveState());
        });

        // 按回款状态分组
        Map<Integer, List<CrmStatementPo>> receiveState2StatementPosMap = statementPos.stream().collect(Collectors.groupingBy(CrmStatementPo::getReceiveState));

        // 每组内拆批次，分批更新
        // 线上一共893条数据，这里批量更新压力不大
        receiveState2StatementPosMap.forEach((receiveState, crmStatementPos) -> {
            Lists.partition(crmStatementPos, 100).forEach(partStatementPos -> {
                List<Integer> ids = partStatementPos.stream().map(CrmStatementPo::getId).collect(Collectors.toList());
                log.info("=====> updateReceiveState, ids:{}, receiveState:{}, size:{}", ids, receiveState, partStatementPos.size());
                CrmStatementPo statementPo = CrmStatementPo.builder().build();
                statementPo.setReceiveState(receiveState);

                CrmStatementPoExample example = new CrmStatementPoExample();
                example.createCriteria().andIdIn(ids);
                crmStatementDao.updateByExampleSelective(statementPo, example);
            });
        });

        return Response.SUCCESS(statementPos.size());
    }
}
