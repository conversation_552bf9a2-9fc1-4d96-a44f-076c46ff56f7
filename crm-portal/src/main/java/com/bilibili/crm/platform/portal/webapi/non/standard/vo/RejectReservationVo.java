package com.bilibili.crm.platform.portal.webapi.non.standard.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * @time 2018/1/8 12:18
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RejectReservationVo {

    @ApiModelProperty(notes = "ID")
    private Integer id;

    @ApiModelProperty(notes = "原因")
    private String reason;

}
