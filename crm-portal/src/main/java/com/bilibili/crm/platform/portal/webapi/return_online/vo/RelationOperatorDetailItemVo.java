package com.bilibili.crm.platform.portal.webapi.return_online.vo;

import com.bilibili.crm.platform.api.return_online.enums.RelationOperatorEnum;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 关系运算符对象
 *
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class RelationOperatorDetailItemVo implements Serializable {
    private static final long serialVersionUID = 1L;

    @ApiModelProperty("规则内容(前端忽略)")
    private String rule_content;

    /**
     * 操作符
     *
     * @see RelationOperatorEnum
     */
    @ApiModelProperty("运算符code")
    private Integer operator_code;

    @ApiModelProperty("运算符描述")
    private String operator_desc;

    /**
     * 参数列表
     */
    @ApiModelProperty("运算符参数列表")
    private List<Object> params;

    /**
     * 档位(定级用的)
     */
    @ApiModelProperty("档位(定级用的)")
    private String level;

    /**
     * 表达式
     */
    @ApiModelProperty("表达式(后端计算用的)")
    private String express;

    /**
     * 返点比例(指标里用的)
     */
    @ApiModelProperty("返点比例(指标里用的)")
    private BigDecimal return_amount_rate;

    /**
     * 左数字
     */
    @ApiModelProperty("左参数")
    private Double left_num;

    /**
     * 右数字
     */
    @ApiModelProperty("右参数")
    private Double right_num;

    @ApiModelProperty("档位描述")
    private String gear_desc;
}
