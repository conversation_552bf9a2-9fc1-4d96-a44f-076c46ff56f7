package com.bilibili.crm.platform.portal.webapi.customer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.concurrent.atomic.AtomicBoolean;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CustomerInfoVo {

    // 基本信息
    @ApiModelProperty("客户ID")
    private Integer id;

    @ApiModelProperty("是否是内部客户 0-否 1-是")
    private Integer is_inner;

    @ApiModelProperty("是否是内部客户 0-否 1-是")
    private String is_inner_desc;

    @ApiModelProperty("客户类型 0-个人 1-机构")
    private Integer customer_category;

    @ApiModelProperty("客户类型 0-个人 1-机构")
    private String customer_category_desc;

    @ApiModelProperty("客户别名")
    private String nick_name;

    @ApiModelProperty("客户名称 外部（公司名称） 内部（个人姓名）")
    private String user_name;

    @ApiModelProperty("所属集团id")
    private Integer group_id;

    @ApiModelProperty("所属集团")
    private String company_group_name;

    @ApiModelProperty("所属父集团id")
    private Integer parent_group_id;

    @ApiModelProperty("所属父集团名称")
    private String parent_group_name;

    @ApiModelProperty("所属部门id")
    private Integer department_id;

    @ApiModelProperty("所属部门")
    private String department_name;

    @ApiModelProperty("所属部门id")
    private List<Integer> department_ids;

    @ApiModelProperty("所属部门")
    private String department_names;

    @ApiModelProperty("蓝v mid")
    private List<BlueMidVo> blue_mid_vos;

    @ApiModelProperty("客户状态")
    private Integer customer_status;

    @ApiModelProperty("客户状态name")
    private String customer_status_name;

    @ApiModelProperty("推广域名")
    private List<String> domains;

    @ApiModelProperty("区域id（机构专用）")
    private Integer area_id;

    @ApiModelProperty("区域id（机构专用）")
    private String area_id_desc;

    @ApiModelProperty("网站名称（机构专用）")
    private String website_name;

    @ApiModelProperty("微博账号（机构专用）")
    private String weibo;

    @ApiModelProperty("我司对接人（机构专用）")
    private String internal_linkman;

    @ApiModelProperty("联系人手机号（机构专用）")
    private String linkman_phone;

    @ApiModelProperty("联系人邮箱（机构专用）")
    private String linkman_email;

    @ApiModelProperty("联系人地址（机构专用）")
    private String linkman_address;

    @ApiModelProperty("开户行（机构专用）")
    private String bank;

    @ApiModelProperty("是否是代理商 0-否 1-是")
    private Integer is_agent;

    @ApiModelProperty("是否是代理商 0-否 1-是")
    private String is_agent_desc;

    @ApiModelProperty("代理商类型：0-无 1-品牌代理商  2-MCN代理商  3-效果代理商")
    private Integer agent_type;

    @ApiModelProperty("代理商类型：0-无 1-品牌代理商  2-MCN代理商  3-效果代理商")
    private String agent_type_desc;

    @ApiModelProperty("一级行业分类（机构专用）")
    private Integer category_first_id;

    @ApiModelProperty("一级行业分类（机构专用）")
    private String category_first_name;

    @ApiModelProperty("二级行业分类（机构专用）")
    private Integer category_second_id;

    @ApiModelProperty("二级行业分类（机构专用）")
    private String category_second_name;

    @ApiModelProperty("业务一级行业分类")
    private Integer biz_category_first_id;

    @ApiModelProperty("业务一级行业分类")
    private String biz_category_first_name;

    @ApiModelProperty("业务二级行业分类")
    private Integer biz_category_second_id;

    @ApiModelProperty("业务二级行业分类")
    private String biz_category_second_name;

    @ApiModelProperty("个人手机号（个人专用）")
    private String personal_phone;

    @ApiModelProperty("联系人地址（个人专用）")
    private String personal_address;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("下属账号行业信息")
    private List<AccountIndustryCategoryVo> accountIndustryCategoryVos;

    // 资质信息
    @ApiModelProperty("主体资质分类id（机构专用）")
    private Integer qualification_type_id;

    @ApiModelProperty("主体资质分类id（机构专用）")
    private String qualification_type_id_desc;

    @ApiModelProperty("营业执照编码（机构专用）")
    private String business_licence_code;

    @ApiModelProperty(notes = "营业执照照片(机构专用)")
    private List<CustomerAttachVo> business_licence_pics;

    @ApiModelProperty("营业执照是否长期有效 1是 0 否（机构专用）")
    private Integer is_business_licence_indefinite;

    @ApiModelProperty("营业执照是否长期有效 1是 0 否（机构专用）")
    private String is_business_licence_indefinite_desc;

    @ApiModelProperty("营业执照到期时间（机构专用）")
    private Long business_licence_expire_date;

    @ApiModelProperty("法人姓名（机构专用）")
    private String legal_person_name;

    @ApiModelProperty("法人身份证照片（机构专用）")
    private List<CustomerAttachVo> legal_person_id_card_pics;

    @ApiModelProperty("法人身份证是否长期有效 1是 0 否（机构专用）")
    private Integer is_legal_person_id_card_indefinite;

    @ApiModelProperty("法人身份证是否长期有效 1是 0 否（机构专用）")
    private String is_legal_person_id_card_indefinite_desc;

    @ApiModelProperty("法人身份证到期时间（机构专用）")
    private Long legal_person_id_card_expire_date;

    @ApiModelProperty("icp备案号（机构专用）")
    private String icp_record_number;

    @ApiModelProperty("icp备案证明照片/icp证（机构专用）")
    private List<CustomerAttachVo> icp_pics;

    @ApiModelProperty("个人证件类型 0-未知 1-身份证（大陆地区） 2-护照（港澳台及海外） （个人专用）")
    private Integer personal_id_card_type;

    @ApiModelProperty("个人证件类型 0-未知 1-身份证（大陆地区） 2-护照（港澳台及海外） （个人专用）")
    private String personal_id_card_type_desc;

    @ApiModelProperty("个人证件号码（个人专用）")
    private String personal_id_card_number;

    @ApiModelProperty("个人证件是否长期有效 1是 0 否（个人专用）")
    private Integer is_personal_id_card_indefinite;

    @ApiModelProperty("个人证件是否长期有效 1是 0 否（个人专用）")
    private String is_personal_id_card_indefinite_desc;

    @ApiModelProperty("个人证件到期时间（个人专用）")
    private Long personal_id_card_expire_date;

    @ApiModelProperty("个人证件照片（个人专用）")
    private List<CustomerAttachVo> person_id_card_pics;

    @ApiModelProperty("额外资质信息列表")
    private List<CustomerAdditionalQualificationVo> qualifications;

    @ApiModelProperty("开票信息")
    private CustomerBillInfoVo customer_bill_info;

    @ApiModelProperty("创建时间")
    private Timestamp ctime;

    @ApiModelProperty("线下店铺的商品照片(线下店铺)")
    private List<CustomerAttachVo> store_item_pics;

    @ApiModelProperty("线下店铺的实体照片(线下店铺)")
    private List<CustomerAttachVo> store_entity_pics;

    @ApiModelProperty("线下店铺的地址(线下店铺)")
    private String store_address;

    @ApiModelProperty("一级行业分类ID")
    private Integer united_first_industry_id;

    @ApiModelProperty("二级行业分类ID")
    private Integer united_second_industry_id;

    @ApiModelProperty("三级行业分类ID")
    private Integer united_third_industry_id;

    @ApiModelProperty("一级行业分类")
    private String united_first_industry_name;

    @ApiModelProperty("二级行业分类")
    private String united_second_industry_name;

    @ApiModelProperty("三级行业分类")
    private String united_third_industry_name;

    @ApiModelProperty("客户运营标签类型")
    private Byte customer_operate_label_type;

    public boolean qualificationsEqual(CustomerInfoVo customerInfoVo) {

        if (customerInfoVo.getQualifications().size() != this.qualifications.size()) {
            return false;
        }
        AtomicBoolean equal = new AtomicBoolean(false);
        this.qualifications.forEach(
                item -> {
                    equal.set(false);
                    customerInfoVo.getQualifications().forEach(
                            element -> {
                                if (element.getExpire_date().equals(item.getExpire_date())
                                        && element.getUrl().equals(item.getUrl())
                                        && element.getSpecial_info_type_id().equals(item.getSpecial_info_type_id())) {
                                    equal.set(true);
                                }
                            }
                    );
                }
        );
        return equal.get();
    }

    public static boolean stringNullBlankEqual(String s1, String s2) {
        if (StringUtils.isBlank(s1) && StringUtils.isBlank(s2)) {
            return true;
        }

        return !ObjectUtils.notEqual(s1, s2);
    }

}
