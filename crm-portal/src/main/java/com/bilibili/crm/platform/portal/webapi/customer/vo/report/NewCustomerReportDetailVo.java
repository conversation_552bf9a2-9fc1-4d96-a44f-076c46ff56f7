package com.bilibili.crm.platform.portal.webapi.customer.vo.report;

import com.bilibili.crm.platform.portal.webapi.contract.vo.ContractFileOssVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/20 17:05
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerReportDetailVo {

    @ApiModelProperty("报备id")
    private Long report_id;

    @ApiModelProperty("客户类型")
    private Integer customer_type;

    @ApiModelProperty("客户名称")
    private String customer_name;

    @ApiModelProperty("营业执照号码")
    private String business_license_code;

    @ApiModelProperty("客户注册地址")
    private String customer_register_address;

    @ApiModelProperty("所属集团ID")
    private Integer belong_group;

    @ApiModelProperty("所属集团")
    private String belong_group_name;

    @ApiModelProperty("所属集团 其他 手动输入")
    private String belong_group_other;

    @ApiModelProperty("所属bu ID")
    private Integer belong_bu;

    @ApiModelProperty("所属bu")
    private String belong_bu_name;

    @ApiModelProperty("所属bu 其他 手动输入")
    private String belong_bu_other;

    @ApiModelProperty("代理商简称")
    private String agent_brief_name;

    @ApiModelProperty("代理商等级")
    private String agent_level;

    @ApiModelProperty("代理商范围")
    private String agent_range;

    @ApiModelProperty("业务一级行业ID")
    private Integer business_first_industry_id;

    @ApiModelProperty("业务一级行业")
    private String business_first_industry;

    @ApiModelProperty("业务二级行业ID")
    private Integer business_second_industry_id;

    @ApiModelProperty("业务二级行业")
    private String business_second_industry;

    @ApiModelProperty("客户联系人")
    private String contactor;

    @ApiModelProperty("客户联系人职位")
    private String contactor_position;

    @ApiModelProperty("联系人电话")
    private String contactor_mobile_phone;

    @ApiModelProperty("办公地址-省市区")
    private String address_city;

    @ApiModelProperty("办公地址-详细地址")
    private String address_detail;

    @ApiModelProperty("客户联系人地区")
    private String contactor_region;

    @ApiModelProperty("客户联系人座机")
    private String contactor_phone;

    @ApiModelProperty("客户联系人微信")
    private String contactor_wechat;

    @ApiModelProperty("客户联系人邮箱")
    private String contactor_email;

    @ApiModelProperty("报备销售id")
    private Integer sale_id;

    @ApiModelProperty("报备销售名称")
    private String sale_name;

    @ApiModelProperty("报备销售域账号")
    private String sale_domain;

    @ApiModelProperty("报备销售id")
    private Integer sale_group_id;

    @ApiModelProperty("报备销售组名称")
    private String sale_group_name;

    @ApiModelProperty("报备状态ID")
    private Integer report_state;

    @ApiModelProperty("报备状态")
    private String report_state_name;

    @ApiModelProperty("申请时间")
    private String apply_time;

    @ApiModelProperty("报备跟进记录 ID")
    private Long report_follow_id;

    @ApiModelProperty("跟进阶段")
    private Integer report_follow_state;

    @ApiModelProperty("跟进描述")
    private String report_follow_state_desc;

    @ApiModelProperty("跟进备注")
    private String report_follow_remark;

    @ApiModelProperty("附件")
    private List<ContractFileOssVo> attachments;

    /**
     * 角色
     */
    @ApiModelProperty("联系人角色")
    private String contactor_role;

    /**
     * 部门
     */
    @ApiModelProperty("联系人部门")
    private String contactor_department;

    /**
     * 联系人手机区号
     */
    @ApiModelProperty("联系人手机号区号")
    private String contactor_phone_code;

    @ApiModelProperty("新-统一一级行业标签id")
    private Integer united_first_industry_id;

    @ApiModelProperty("新-统一一级行业名")
    private String united_first_industry_name;

    @ApiModelProperty("新-统一二级行业标签id")
    private Integer united_second_industry_id;

    @ApiModelProperty("新-统一二级行业名")
    private String united_second_industry_name;

    @ApiModelProperty("新-统一三级行业标签id")
    private Integer united_third_industry_id;

    @ApiModelProperty("新-统一三级行业名")
    private String united_third_industry_name;

    @ApiModelProperty("新客报备-销售提交的判断材料")
    private List<ContractFileOssVo> sale_check_attachments;

    @ApiModelProperty("新客报备-销售说明材料")
    private String sale_remark;

    @ApiModelProperty("客户标签类型")
    private Byte customer_operate_label_type;

    @ApiModelProperty("标签服务开始时间")
    private Long customer_operate_label_begin_time;

    @ApiModelProperty("客户标签类型")
    private Long customer_operate_label_end_time;

}
