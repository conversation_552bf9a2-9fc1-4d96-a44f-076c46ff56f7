package com.bilibili.crm.platform.portal.webapi.mock.vo;

import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2019/12/16
 * 日志查询类
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryLogMockLoginVo {

    @ApiModelProperty("页号")
    private Integer page;

    @ApiModelProperty("页长")
    private Integer size;
}
