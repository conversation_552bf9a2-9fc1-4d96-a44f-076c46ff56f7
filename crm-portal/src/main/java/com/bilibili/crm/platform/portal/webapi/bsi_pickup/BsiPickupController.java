package com.bilibili.crm.platform.portal.webapi.bsi_pickup;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.bsi_pickup.IBsiPickupService;
import com.bilibili.crm.platform.api.bsi_pickup.dto.BsiPickupDetailDto;
import com.bilibili.crm.platform.api.bsi_pickup.dto.BsiPickupDto;
import com.bilibili.crm.platform.api.bsi_pickup.dto.QueryBsiPickupDto;
import com.bilibili.crm.platform.biz.annotation.Export;
import com.bilibili.crm.platform.portal.common.CrmCommonUtils;
import com.bilibili.crm.platform.portal.webapi.bsi_pickup.service.WebBsiPickupService;
import com.bilibili.crm.platform.portal.webapi.bsi_pickup.vo.BsiPickupDetailVo;
import com.bilibili.crm.platform.portal.webapi.bsi_pickup.vo.BsiPickupVo;
import com.bilibili.crm.platform.portal.webapi.bsi_pickup.vo.QueryBsiPickupVo;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.soa.ISoaQueryAccountService;
import com.bilibili.rbac.filter.annotation.Security;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.util.List;


/**
 * <AUTHOR>
 * @date 2020/6/11 21:19
 * 花火商单相关api
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/bsi_pickup")
@Api(value = "/bsi_pickup", description = "花火商单")
public class BsiPickupController extends BaseRestfulController {

    @Autowired
    private IBsiPickupService bsiPickupService;
    @Autowired
    private WebBsiPickupService webBsiPickupService;
    @Autowired
    private ISoaQueryAccountService soaQueryAccountService;
    @Autowired
    private CrmCommonUtils crmCommonUtils;

    @Security("BsiPickupController_queryBsiPickup")
    @ApiOperation(value = "查询花火商机列表")
    @RequestMapping(value = "/list", method = RequestMethod.GET)
    public Response<Pagination<List<BsiPickupVo>>> queryBsiPickup (@ApiIgnore Context context, QueryBsiPickupVo queryVo) {

        PageResult<BsiPickupDetailDto> result = bsiPickupService.queryBsiPickup(getOperator(context), QueryBsiPickupDto.builder()
                .statusList(queryVo.getStatus())
                .saleId(queryVo.getSale_id())
                .pageInfo(Page.valueOf(
                        queryVo.getPage() == null ? 1 : queryVo.getPage(),
                        queryVo.getSize() == null ? 10 : queryVo.getSize()))
                .build());

        if (CollectionUtils.isEmpty(result.getRecords())) {
            return Response.SUCCESS(Pagination.emptyPagination());
        }
        List<BsiPickupVo> vos = webBsiPickupService.dto2vo(result.getRecords());

        return Response.SUCCESS(new Pagination<>(queryVo.getPage(), result.getTotal(), vos));
    }

    @Security("BsiPickupController_getBsiPickup")
    @ApiOperation(value = "查询花火商机详情")
    @RequestMapping(value = "/detail/{id}", method = RequestMethod.GET)
    public Response<BsiPickupDetailVo> getBsiPickup (@PathVariable("id") Integer id) {

        BsiPickupDetailDto bsiPickup = bsiPickupService.getBsiPickup(id);
        return Response.SUCCESS(webBsiPickupService.dto2vo(bsiPickup));
    }

    @ApiOperation(value = "保存花火商机")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public Response<String> saveBsiPickup(@ApiIgnore Context context, @RequestBody BsiPickupDto dto) {

        bsiPickupService.saveBsiPickup(getOperator(context),dto);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "根据商单ID获取归属销售")
    @RequestMapping(value = "/{id}/sale_list", method = RequestMethod.GET)
    public Response<List<String>> getSalesById(@PathVariable("id") Integer id) {

        return Response.SUCCESS(bsiPickupService.getSalesById(id));
    }

    @Security("BsiPickupController_adopt")
    @ApiOperation(value = "通过花火商机")
    @RequestMapping(value = "/success", method = RequestMethod.POST)
    public Response<String> adopt(@ApiIgnore Context context, Integer id) {

        bsiPickupService.adopt(getOperator(context), id);
        return Response.SUCCESS("");
    }

    @Security("BsiPickupController_reject")
    @ApiOperation(value = "拒绝花火商机")
    @RequestMapping(value = "/reject", method = RequestMethod.POST)
    public Response<String> reject(@ApiIgnore Context context, Integer id, String reject_reason) {

        bsiPickupService.reject(getOperator(context), id, reject_reason);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "判断是否Ka")
    @RequestMapping(value = "/is_pick_ka", method = RequestMethod.GET)
    public Response<Boolean> isPickUpKaAccount(Integer account_id) {

        boolean isKa = soaQueryAccountService.isPickUpKaAccount(account_id);
        return Response.SUCCESS(isKa);
    }

    @ApiOperation(value = "处理超时的花火商机")
    @RequestMapping(value = "/handle_timeout", method = RequestMethod.GET)
    public Response<String> handleTimeout() {

        bsiPickupService.handleTimeout();
        return Response.SUCCESS("");
    }

    @Security("BsiPickupController_queryBsiPickup")
    @Export
    @ApiOperation(value = "导出花火商机列表")
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public Response<String> export (@ApiIgnore Context context, QueryBsiPickupVo queryVo) {

        new Thread(() -> {
            List<BsiPickupDetailDto> result = bsiPickupService.exportBsiPickupList(getOperator(context), QueryBsiPickupDto.builder()
                    .statusList(queryVo.getStatus())
                    .saleId(queryVo.getSale_id())
                    .build());

            List<BsiPickupVo> vos = webBsiPickupService.dto2vo(result);
            try {
                crmCommonUtils.writeAndSendFile(this.getOperator(context), vos, BsiPickupVo.class, "bsi_pickup.xls", "您好，附件是花火商机判定数据报表，请查收");
            } catch (Exception e) {
                log.error("BsiPickupController.export error", e);
            }
        }).start();

        return Response.SUCCESS("已将导出数据文件发送至你的b站邮箱。前往查看已将导出数据文件发送至你的b站邮箱。前往查看");
    }
}
