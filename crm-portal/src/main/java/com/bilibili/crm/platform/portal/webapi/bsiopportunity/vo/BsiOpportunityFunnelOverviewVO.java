package com.bilibili.crm.platform.portal.webapi.bsiopportunity.vo;

import com.bilibili.crm.platform.common.datavo.ChartDataItemVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;

/**
 * 商机项目看板的整体概述
 *
 * <AUTHOR>
 * date 2023/8/11 15:50.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BsiOpportunityFunnelOverviewVO implements Serializable {

    private static final long serialVersionUID = 1416807304220037923L;
    //数据整体概述
    @ApiModelProperty(notes = "本季度商机预计投放金额(元)不乘系数")
    private BigDecimal current_quarter_expect_launch_amount;
    @ApiModelProperty(notes = "其他季度商机预计投放金额(元)不乘系数")
    private BigDecimal other_quarter_expect_launch_amount;
    @ApiModelProperty(notes = "近7日新增商机个数")
    private Long new_bsi_opportunity_in_seven_days_count;
    @ApiModelProperty(notes = "预计30天内成单商机个数")
    private Long expect_order_in_thirty_days_count;
    @ApiModelProperty(notes = "预计7天内自动流转为弃单的商机个数")
    private Long to_abandon_order_in_seven_days_count;
    @ApiModelProperty(notes = "需重新拆季度填写预估金额的商机个数")
    private Long need_unpack_quarter_count;
    @ApiModelProperty(notes = "近7日策划接单状态变更的商机")
    private Long have_planner_state_change_in_seven_days_count;
    @ApiModelProperty(notes = "近7日策划接单的商机")
    private Long planner_confirm_seven_days_count;
    //商机分阶段概述
    @ApiModelProperty(notes = "有效商机总数个数")
    private Long effective_bsi_opportunity_count;
    @ApiModelProperty(notes = "所有商机总数个数")
    private Long all_bsi_opportunity_count;
    @ApiModelProperty(notes = "弃单商机个数")
    private Long abandon_order_bsi_opportunity_count;
    @ApiModelProperty(notes = "弃单商机预计投放金额")
    private BigDecimal abandon_order_bsi_opportunity_expect_amount;

    @ApiModelProperty(notes = "brief（10%）商机个数")
    private Long brief_bsi_opportunity_count;
    @ApiModelProperty(notes = "brief（10%）商机占百分比")
    private BigDecimal brief_bsi_opportunity_rate;
    @ApiModelProperty(notes = "brief（10%）本季度预计投放金额")
    private BigDecimal brief_bsi_opportunity_expect_amount;

    @ApiModelProperty(notes = "提案通过（30%）商机个数")
    private Long preliminary_plan_bsi_opportunity_count;
    @ApiModelProperty(notes = "提案通过（30%）商机占百分比")
    private BigDecimal preliminary_plan_bsi_opportunity_rate;
    @ApiModelProperty(notes = "提案通过（30%）商机转换率")
    private BigDecimal preliminary_plan_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "提案通过（30%）商机转化天数")
    private String preliminary_plan_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "提案通过（30%）本季度预计投放金额")
    private BigDecimal preliminary_plan_bsi_opportunity_conversion_expect_amount;

    @ApiModelProperty(notes = "意向明确沟通预算（50%）商机个数")
    private Long plan_communicate_bsi_opportunity_count;
    @ApiModelProperty(notes = "意向明确沟通预算（50%）商机占百分比")
    private BigDecimal plan_communicate_bsi_opportunity_rate;
    @ApiModelProperty(notes = "意向明确沟通预算（50%）商机占转换率")
    private BigDecimal plan_communicate_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "意向明确沟通预算（50%）商机转化天数")
    private String plan_communicate_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "意向明确沟通预算（50%）本季度预计投放金额")
    private BigDecimal plan_communicate_bsi_opportunity_conversion_expect_amount;


    @ApiModelProperty(notes = "价格谈判与细节沟通（70%）商机个数")
    private Long provide_package_bsi_opportunity_count;
    @ApiModelProperty(notes = "价格谈判与细节沟通（70%）商机占百分比")
    private BigDecimal provide_package_bsi_opportunity_rate;
    @ApiModelProperty(notes = "价格谈判与细节沟通（70%）商机占转换率")
    private BigDecimal provide_package_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "价格谈判与细节沟通（70%）商机转化天数")
    private String provide_package_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "价格谈判与细节沟通（70%）本季度预计投放金额")
    private BigDecimal provide_package_bsi_opportunity_conversion_expect_amount;


    @ApiModelProperty(notes = "合同和审批流程（90%）商机个数")
    private Long provide_schedule_bsi_opportunity_count;
    @ApiModelProperty(notes = "合同和审批流程（90%）商机占百分比")
    private BigDecimal provide_schedule_bsi_opportunity_rate;
    @ApiModelProperty(notes = "合同和审批流程（90%）商机占转换率")
    private BigDecimal provide_schedule_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "合同和审批流程（90%）商机转化天数")
    private String provide_schedule_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "合同和审批流程（90%）本季度预计投放金额")
    private BigDecimal provide_schedule_bsi_opportunity_conversion_expect_amount;

    @ApiModelProperty(notes = "下单（100%）未关联合同 商机个数")
    private Long complete_order_bsi_opportunity_count;
    @ApiModelProperty(notes = "下单（100%）未关联合同 商机占转换率")
    private BigDecimal complete_order_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "下单（100%）未关联合同 商机转化天数")
    private String complete_order_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "下单（100%）未关联合同 本季度预计投放金额")
    private BigDecimal complete_order_bsi_opportunity_conversion_expect_amount;

    @ApiModelProperty(notes = "下单（100%）已关联合同 商机个数")
    private Long complete_order_related_contract_bsi_opportunity_count;
    @ApiModelProperty(notes = "下单（100%）已关联合同 商机占转换率")
    private BigDecimal complete_order_related_contract_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "下单（100%）已关联合同 商机转化天数")
    private String complete_order_related_contract_bsi_opportunity_conversion_days;

    @ApiModelProperty(notes = "商机漏斗明细表总个数")
    private Long bsi_record_list_total;
    @ApiModelProperty(notes = "弃单商机原因分析")
    private List<ChartDataItemVo> bsi_abandon_reason_list;
    @ApiModelProperty(notes = "弃单商机原因分析总数")
    private Long bsi_abandon_reason_list_count;
    @ApiModelProperty(notes = "弃单商机销售组分析")
    private List<ChartDataItemVo> bsi_abandon_sale_group_list;
    @ApiModelProperty(notes = "弃单商机销售组分析总数")
    private Long bsi_abandon_sale_group_list_count;
    @ApiModelProperty(notes = "商机弃单明细表")
    private List<BsiOpportunityFunnelVO> bsi_abandon_record_list;
    @ApiModelProperty(notes = "商机弃单明细表的总数")
    private Long bsi_abandon_record_list_count;
    @ApiModelProperty(notes = "商机明细表")
    private List<BsiOpportunityFunnelVO> bsi_record_list;

    @ApiModelProperty(notes = "新30%商机个数")
    private Long thirty_bsi_opportunity_count;
    @ApiModelProperty(notes = "新30%商机占百分比")
    private BigDecimal thirty_bsi_opportunity_rate;
    @ApiModelProperty(notes = "新30%本季度预计投放金额")
    private BigDecimal thirty_bsi_opportunity_conversion_expect_amount;

    @ApiModelProperty(notes = "新50%商机个数")
    private Long fifty_bsi_opportunity_count;
    @ApiModelProperty(notes = "新50%商机占百分比")
    private BigDecimal fifty_bsi_opportunity_rate;
    @ApiModelProperty(notes = "新50%商机转换率")
    private BigDecimal fifty_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "新50%商机转化天数")
    private String fifty_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "新50%本季度预计投放金额")
    private BigDecimal fifty_bsi_opportunity_conversion_expect_amount;

    @ApiModelProperty(notes = "新80%商机个数")
    private Long eighty_bsi_opportunity_count;
    @ApiModelProperty(notes = "新80%商机占百分比")
    private BigDecimal eighty_bsi_opportunity_rate;
    @ApiModelProperty(notes = "新80%商机转换率")
    private BigDecimal eighty_bsi_opportunity_conversion_rate;
    @ApiModelProperty(notes = "新80%商机转化天数")
    private String eighty_bsi_opportunity_conversion_days;
    @ApiModelProperty(notes = "新80%本季度预计投放金额")
    private BigDecimal eighty_bsi_opportunity_conversion_expect_amount;

    public static BsiOpportunityFunnelOverviewVO returnEmptyVO() {
        return BsiOpportunityFunnelOverviewVO.builder()
                .current_quarter_expect_launch_amount(BigDecimal.ZERO)
                .other_quarter_expect_launch_amount(BigDecimal.ZERO)
                .new_bsi_opportunity_in_seven_days_count(0L)
                .expect_order_in_thirty_days_count(0L)
                .to_abandon_order_in_seven_days_count(0L)
                .need_unpack_quarter_count(0L)
                .have_planner_state_change_in_seven_days_count(0L)
                .effective_bsi_opportunity_count(0L)
                .all_bsi_opportunity_count(0L)
                .abandon_order_bsi_opportunity_count(0L)
                .abandon_order_bsi_opportunity_expect_amount(BigDecimal.ZERO)
                .brief_bsi_opportunity_count(0L)
                .brief_bsi_opportunity_rate(BigDecimal.ZERO)
                .brief_bsi_opportunity_expect_amount(BigDecimal.ZERO)
                .preliminary_plan_bsi_opportunity_count(0L)
                .preliminary_plan_bsi_opportunity_rate(BigDecimal.ZERO)
                .preliminary_plan_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .preliminary_plan_bsi_opportunity_conversion_days("0")
                .preliminary_plan_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)
                .plan_communicate_bsi_opportunity_count(0L)
                .plan_communicate_bsi_opportunity_rate(BigDecimal.ZERO)
                .plan_communicate_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .plan_communicate_bsi_opportunity_conversion_days("0")
                .plan_communicate_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)
                .provide_package_bsi_opportunity_count(0L)
                .provide_package_bsi_opportunity_rate(BigDecimal.ZERO)
                .provide_package_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .provide_package_bsi_opportunity_conversion_days("0")
                .provide_package_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)
                .provide_schedule_bsi_opportunity_count(0L)
                .provide_schedule_bsi_opportunity_rate(BigDecimal.ZERO)
                .provide_schedule_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .provide_schedule_bsi_opportunity_conversion_days("0")
                .provide_schedule_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)
                .complete_order_bsi_opportunity_count(0L)
                .complete_order_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .complete_order_bsi_opportunity_conversion_days("0")
                .complete_order_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)
                .complete_order_related_contract_bsi_opportunity_count(0L)
                .complete_order_related_contract_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .complete_order_related_contract_bsi_opportunity_conversion_days("0")

                .thirty_bsi_opportunity_count(0L)
                .thirty_bsi_opportunity_rate(BigDecimal.ZERO)
                .thirty_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)

                .fifty_bsi_opportunity_count(0L)
                .fifty_bsi_opportunity_rate(BigDecimal.ZERO)
                .fifty_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .fifty_bsi_opportunity_conversion_days("0")
                .fifty_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)

                .eighty_bsi_opportunity_count(0L)
                .eighty_bsi_opportunity_rate(BigDecimal.ZERO)
                .eighty_bsi_opportunity_conversion_rate(BigDecimal.ZERO)
                .eighty_bsi_opportunity_conversion_days("0")
                .eighty_bsi_opportunity_conversion_expect_amount(BigDecimal.ZERO)
                .bsi_record_list_total(0L)
                .bsi_record_list(Collections.emptyList())
                .bsi_abandon_record_list(Collections.emptyList()).build();
    }
}

