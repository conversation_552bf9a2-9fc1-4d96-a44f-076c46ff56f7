package com.bilibili.crm.platform.portal.webapi.sale_sea;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.biz.sale_sea.check.SaleSeaCheckComponent;
import com.bilibili.crm.platform.api.AsyncOperationService;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.dto.AsyncOperationResultDto;
import com.bilibili.crm.platform.api.enums.AsyncOperationTypeEnum;
import com.bilibili.crm.platform.api.exception.code.CrmAppExceptionCode;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale_sea.IQuerySaleSeaService;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaService;
import com.bilibili.crm.platform.api.sale_sea.dto.SaleSeaMappingDto;
import com.bilibili.crm.platform.api.sale_sea.dto.SaleSeaMappingSaveDto;
import com.bilibili.crm.platform.api.sale_sea.excel.SaleSeaAgentCustomerExcelBo;
import com.bilibili.crm.platform.api.sale_sea.excel.SaleSeaCustomerExcelBo;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.repo.AccProductRepo;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.service.return_online.utils.EasyExcelUtils;
import com.bilibili.crm.platform.biz.service.sale.SaleConfig;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.CrmConstant;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaMappingType;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import com.bilibili.crm.platform.portal.common.DropBoxVo;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.sale_sea.service.WebSaleSeaService;
import com.bilibili.crm.platform.portal.webapi.sale_sea.vo.SaleSeaMappingFormVo;
import com.bilibili.rbac.filter.annotation.Security;
import com.google.common.collect.ImmutableMap;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.time.temporal.ChronoUnit;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/6/2 17:33
 * 公私海相关api
 */
@RestController
@RequestMapping("/web_api/v1/sale_sea")
@Api(value = "/sale_sea", description = "公私海相关【2262129】")
public class SaleSeaController extends BaseRestfulController {

    private static final Logger logger = LoggerFactory.getLogger(SaleSeaController.class);

    @Autowired
    private ISaleSeaService saleSeaService;
    @Autowired
    private IQuerySaleSeaService querySaleSeaService;
    @Autowired
    private WebSaleSeaService webSaleSeaService;
    @Autowired
    private ISaleService iSaleService;
    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private SaleConfig saleConfig;
    @Autowired
    private AsyncOperationService asyncOperationService;
    @Resource
    private AccProductRepo accProductRepo;
    @Resource
    private SaleSeaCheckComponent saleSeaCheckComponent;

    @Deprecated
    @ApiOperation(value = "查询客销关系配置执行结果")
    @RequestMapping(value = "/saleConf/result/{id}", method = RequestMethod.GET)
    public Response<Map<String, Object>> querySaleConfResult(@PathVariable("id") Integer id) {
        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.queryAsyncExecResult(id);
        if (Objects.isNull(asyncOperationResultDto)) {
            return Response.FAIL(CrmAppExceptionCode.NO_RESULT);
        }
        return Response.SUCCESS(ImmutableMap.of("status", asyncOperationResultDto.getProcessStatus().getCode(),
                "count", asyncOperationResultDto.getProcessCount(),
                "errorMsg", asyncOperationResultDto.getErrorMsg()));
    }

    //============================== 集团/产品线/产品(绑定的是直客销售) ===================================
    @ApiOperation(value = "集团-绑定公私海绑定关系")
    @Security("SaleSeaController_groupBind")
    @RequestMapping(value = "/group/bind", method = RequestMethod.POST)
    public Response<String> bindGroupSaleSea(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {
        formVo.setBili_user_type(BiliUserType.STRAIGHT_MANAGER.getCode());
        SaleSeaMappingSaveDto saleSeaMappingDto = webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.GROUP);
        saleSeaService.bindSaleSea(getOperator(context), saleSeaMappingDto);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "集团-公私海绑定关系")
    @RequestMapping(value = "/group/bind/{mapping_id}", method = RequestMethod.GET)
    public Response<Map<Integer, Map<Integer, List<DropBoxVo>>>> groupSaleSeaList(@PathVariable("mapping_id") Integer mappingId) {

        SaleSeaMappingDto mapping = querySaleSeaService.getSaleSeaMap(SaleSeaMappingType.GROUP, mappingId);
        return Response.SUCCESS(webSaleSeaService.getSaleMap2(mapping.getProductSaleMap()));
    }

    @ApiOperation(value = "产品线-绑定公私海绑定关系")
    @RequestMapping(value = "/product_line/bind", method = RequestMethod.POST)
    public Response<String> bindProductLineSaleSea(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {
        formVo.setBili_user_type(BiliUserType.STRAIGHT_MANAGER.getCode());
        SaleSeaMappingSaveDto saleSeaMappingDto = webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.PRODUCT_LINE);
        saleSeaService.bindSaleSea(getOperator(context), saleSeaMappingDto);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "产品线-公私海绑定关系")
    @RequestMapping(value = "/product_line/bind/{mapping_id}", method = RequestMethod.GET)
    public Response<Map<Integer, Map<Integer, List<DropBoxVo>>>> productLineSaleSeaList(@PathVariable("mapping_id") Integer mappingId) {

        SaleSeaMappingDto mapping = querySaleSeaService.getSaleSeaMap(SaleSeaMappingType.PRODUCT_LINE, mappingId);
        return Response.SUCCESS(webSaleSeaService.getSaleMap2(mapping.getProductSaleMap()));
    }

    @ApiOperation(value = "产品-绑定公私海绑定关系")
    @RequestMapping(value = "/product/bind", method = RequestMethod.POST)
    public Response<String> bindProductSaleSea(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {
        formVo.setBili_user_type(BiliUserType.STRAIGHT_MANAGER.getCode());
        SaleSeaMappingSaveDto saleSeaMappingDto = webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.PRODUCT);
        saleSeaService.bindSaleSea(getOperator(context), saleSeaMappingDto);
        return Response.SUCCESS("");
    }

    @ApiOperation(value = "产品-公私海绑定关系")
    @RequestMapping(value = "/product/bind/{mapping_id}", method = RequestMethod.GET)
    public Response<Map<Integer, Map<Integer, List<DropBoxVo>>>> productSaleSeaList(@PathVariable("mapping_id") Integer mappingId) {

        SaleSeaMappingDto mapping = querySaleSeaService.getSaleSeaMap(SaleSeaMappingType.PRODUCT, mappingId);
        return Response.SUCCESS(webSaleSeaService.getSaleMap2(mapping.getProductSaleMap()));
    }

    //============================== 客户/账号(绑定直客销售/渠道销售) ===================================

    /**
     * 1、广告主客户绑定直客销售
     * 2、代理商客户绑定渠道销售
     *
     * @param context
     * @param formVo
     * @return
     */
    @ApiOperation(value = "客户-绑定公私海绑定关系")
    @Security("SaleSeaController_customerBind")
    @RequestMapping(value = "/customer/bind", method = RequestMethod.POST)
    public Response<String> bindCustomerSaleSea(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {

        Operator operator = getOperator(context);
        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.handleSingleExec(operator, AsyncOperationTypeEnum.CUSTOMER_SALE, String.valueOf(formVo.getMapping_id()),
                () -> {
                    saleSeaService.bindSaleSea(operator, webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.CUSTOMER));
                    return null;
                });
        return asyncOperationResultDto.isSucc() ? Response.SUCCESS("") : Response.FAIL(asyncOperationResultDto.getErrorCode(), asyncOperationResultDto.getErrorMsg());
    }

    @Deprecated
    @ApiOperation(value = "客户-绑定公私海绑定关系")
    @Security("SaleSeaController_customerBind")
    @RequestMapping(value = "/customer_channel/bind", method = RequestMethod.POST)
    public Response<String> bindCustomerChannelSale(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {
        Assert.isTrue(formVo.getBili_user_type().equals(BiliUserType.CHANNEL_MANAGER.getCode()), "非客户渠道经理设置");
        SaleSeaMappingSaveDto createSaleSea = webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.CUSTOMER);
        createSaleSea.setAccCustomerChannelSaleSeaSetting(Boolean.TRUE);
        saleSeaService.bindCustomerChannelSaleSeaByUpdateCon(getOperator(context), createSaleSea);
        return Response.SUCCESS("");
    }

    /**
     * 广告主客户 & 品牌 绑定销售
     *
     * @param context
     * @param formVo
     * @return
     */
    @ApiOperation(value = "广告主客户 & 品牌-绑定公私海绑定关系")
//    @Security("SaleSeaController_customerBind")
    @RequestMapping(value = "/customer_brand/bind", method = RequestMethod.POST)
    public Response<String> bindCustomerProductSale(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {
        Operator operator = getOperator(context);
        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.handleSingleExec(operator, AsyncOperationTypeEnum.CUSTOMER_BRAND_DIRECT_SALE,
                String.format("%s#%s", formVo.getMapping_id(), formVo.getProduct_id()), () -> {
                    SaleSeaMappingSaveDto createSaleSea = webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.CUSTOMER);
                    createSaleSea.setAccCustomerProductSaleSeaSetting(Boolean.TRUE);
                    createSaleSea.getProductSaleMap().entrySet().forEach(
                            entry -> {
                                if (CollectionUtils.isEmpty(entry.getValue())) {
                                    entry.setValue(Lists.newArrayList(0)); // 设置0，为了实现原客销记录的删除
                                }
                            }
                    );
                    saleSeaService.bindCustomerProductSaleSeaByUpdateCon(operator, createSaleSea);
                    return null;
                });
        return asyncOperationResultDto.isSucc() ? Response.SUCCESS("success") : Response.FAIL(asyncOperationResultDto.getErrorCode(), asyncOperationResultDto.getErrorMsg());
    }

    /**
     * Excel批量配置入口
     * <p>
     * 通过是否指定productId来决策是配置的【广告主客户绑定销售】还是配置的【广告主客户、品牌 绑定销售】
     *
     * @param context
     * @param multipartFile
     * @return
     */
    @ApiOperation(value = "批量设置广告主直客品牌销售")
    @RequestMapping(value = "/customer_brand/batch_update_sale_belong", method = RequestMethod.POST)
    @ResponseBody
    public Response<Long> batchUpdateCustomerBrandSaleBelong(@ApiIgnore Context context,
                                                             @RequestParam(value = "file", required = false) MultipartFile multipartFile) {
        List<SaleSeaCustomerExcelBo> bos = new ArrayList<>();
        try {
            EasyExcelUtils.parseExcel(multipartFile.getBytes(), SaleSeaCustomerExcelBo.class, bos::addAll);
        } catch (Exception e) {
            logger.error("error ", e);
        }

        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.handleAsyncExec(getOperator(context), AsyncOperationTypeEnum.BATCH_CUSTOMER_DIRECT_SALE, Utils.getUUID(),
                (unused) -> validateCustomerBrandExcel(bos),
                () -> {
                    int success = 0;
                    try {
                        for (SaleSeaCustomerExcelBo bo : bos) {

                            Integer customerId = Integer.valueOf(bo.getCustomerId());
                            Integer productId = null;

                            if (StringUtils.isNotBlank(bo.getProductId()) && !"0".equals(bo.getProductId())) {
                                productId = Integer.valueOf(bo.getProductId());
                            }


                            List<Integer> brandSaleIds = null;
                            if (StringUtils.isNotBlank(bo.getBrandSaleIds())) {
                                String[] brandSaleIdArr = bo.getBrandSaleIds().split(";");
                                List<String> brandSaleIdsStr = Arrays.asList(brandSaleIdArr);
                                brandSaleIds = brandSaleIdsStr.stream().map(Integer::parseInt).collect(Collectors.toList());
                            }

                            List<Integer> rtbSaleIds = null;
                            if (StringUtils.isNotBlank(bo.getRtbSaleIds())) {
                                String[] rtbSaleIdArr = bo.getRtbSaleIds().split(";");
                                List<String> rtbSaleIdsStr = Arrays.asList(rtbSaleIdArr);
                                rtbSaleIds = rtbSaleIdsStr.stream().map(Integer::parseInt).collect(Collectors.toList());
                            }

                            List<Integer> contractSaleIds = null;
                            if (StringUtils.isNotBlank(bo.getContractSaleIds())) {
                                String[] contractSaleIdArr = bo.getContractSaleIds().split(";");
                                List<String> contractSaleIdsStr = Arrays.asList(contractSaleIdArr);
                                contractSaleIds = contractSaleIdsStr.stream().map(Integer::parseInt).collect(Collectors.toList());
                            }

                            if (CollectionUtils.isEmpty(brandSaleIds) && CollectionUtils.isEmpty(rtbSaleIds) && CollectionUtils.isEmpty(contractSaleIds) &&
                                    StringUtils.isBlank(bo.getBrandSaleIds()) && StringUtils.isBlank(bo.getRtbSaleIds()) && StringUtils.isBlank(bo.getContractSaleIds())) {
                                continue;
                            }

                            Map<SaleSeaProductCategory, List<Integer>> productSaleMap = new HashMap<>();
                            if (Objects.nonNull(brandSaleIds)) {
                                productSaleMap.put(SaleSeaProductCategory.BRAND, brandSaleIds);
                            }
                            if (Objects.nonNull(rtbSaleIds)) {
                                productSaleMap.put(SaleSeaProductCategory.RTB, rtbSaleIds);
                            }
                            if (Objects.nonNull(contractSaleIds)) {
                                productSaleMap.put(SaleSeaProductCategory.CONTRACT, contractSaleIds);
                            }

                            //商单业务业绩归属开始日期字段
                            Timestamp newBsiOrderBelongBeginDate = null;
                            if (StringUtils.isNotEmpty(bo.getBrandBeginDate())) {
                                newBsiOrderBelongBeginDate = CrmUtils.getString2Timestamp(bo.getBrandBeginDate());
                            }
                            //效果业务业绩归属开始日期字段
                            Timestamp newBelongBeginDate = null;
                            if (StringUtils.isNotEmpty(bo.getRtbBeginDate())) {
                                newBelongBeginDate = CrmUtils.getString2Timestamp(bo.getRtbBeginDate());
                            }
                            //合约业务业绩归属开始日期字段
                            Timestamp newContractBelongBeginDate = null;
                            if (StringUtils.isNotEmpty(bo.getContractBeginDate())) {
                                newContractBelongBeginDate = CrmUtils.getString2Timestamp(bo.getContractBeginDate(), CrmUtils.YYYYMM);
                            }

                            if (null != productId) {
                                saleSeaService.bindCustomerProductSaleSeaByUpdateCon(getOperator(context), SaleSeaMappingSaveDto.builder()
                                        .mappingId(customerId)
                                        .customerAuthProductIds(Lists.newArrayList(productId))
                                        .mappingType(SaleSeaMappingType.CUSTOMER)
                                        .newBelongBeginDate(newBelongBeginDate)
                                        .newBsiOrderBelongBeginDate(newBsiOrderBelongBeginDate)
                                        .newContractBelongBeginDate(newContractBelongBeginDate)
                                        .productSaleMap(productSaleMap)
                                        .biliUserType(BiliUserType.STRAIGHT_MANAGER)
                                        .accCustomerProductSaleSeaSetting(true)
                                        .build());
                            } else {
                                saleSeaService.bindSaleSeaByUpdateCon(getOperator(context), SaleSeaMappingSaveDto.builder()
                                        .mappingId(customerId)
                                        .mappingType(SaleSeaMappingType.CUSTOMER)
                                        .newBelongBeginDate(newBelongBeginDate)
                                        .newBsiOrderBelongBeginDate(newBsiOrderBelongBeginDate)
                                        .newContractBelongBeginDate(newContractBelongBeginDate)
                                        .productSaleMap(productSaleMap)
                                        .biliUserType(BiliUserType.STRAIGHT_MANAGER)
                                        .build());
                            }
                            success++;
                            logger.info("batchUpdateCustomerBrandSaleBelong success {}", JSON.toJSONString(bo));
                        }

                    } catch (Exception e) {
                        logger.error("error ", e);
                    }
                    return success;
                });

        return asyncOperationResultDto.isFail() ? Response.FAIL(asyncOperationResultDto.getErrorCode(), asyncOperationResultDto.getErrorMsg()) : Response.SUCCESS(asyncOperationResultDto.getProcessId());
    }

    private void validateCustomerBrandExcel(List<SaleSeaCustomerExcelBo> excels) {

        Assert.isTrue(excels.size() <= 2000, "不能超过2000条");
        Map<Integer, SaleDto> saleMap = iSaleService.getAllSaleMap();

        // <客户ID, 索引>
        Map<Integer, Integer> customerIdMap = Maps.newHashMap();
        List<Integer> productIds = new ArrayList<>();
        for (int i = 0; i < excels.size(); i++) {
            List<SaleDto> saleDtoList = Lists.newArrayList();
            SaleSeaCustomerExcelBo bo = excels.get(i);

            int currentRow = i + 2;
            try {
                Integer customerId = Integer.valueOf(bo.getCustomerId());
                customerIdMap.putIfAbsent(customerId, i);
            } catch (Exception e) {
                Assert.isTrue(false, "第" + currentRow + "行数据广告主客户ID格式有误，请重新上传");
            }

            try {
                if (StringUtils.isNotBlank(bo.getProductId())) {
                    productIds.add(Integer.valueOf(bo.getProductId()));
                }
            } catch (Exception e) {
                Assert.isTrue(false, "第" + currentRow + "行数据品牌ID格式有误，请重新上传");
            }

            if (StringUtils.isNotBlank(bo.getBrandSaleIds()) && !"0".equals(bo.getBrandSaleIds())) {
                saleSeaCheckComponent.checkSale(bo.getBrandSaleIds(), currentRow, saleDtoList, saleMap);
            }

            if (StringUtils.isNotBlank(bo.getRtbSaleIds()) && !"0".equals(bo.getRtbSaleIds())) {
                saleSeaCheckComponent.checkSale(bo.getBrandSaleIds(), currentRow, saleDtoList, saleMap);
            }

            if (StringUtils.isNotBlank(bo.getContractSaleIds()) && !"0".equals(bo.getContractSaleIds())) {
                saleSeaCheckComponent.checkSale(bo.getBrandSaleIds(), currentRow, saleDtoList, saleMap);
            }

            //商单归属开始日期
            //效果归属开始日期
            //合约归属开始日期
            try {
                Timestamp forbiddenBeginDate = Utils.getSomeDayAfter(Utils.getNow(), -122);
                if (StringUtils.isNotEmpty(bo.getBrandBeginDate())) {
                    Timestamp newBelongBeginDate = CrmUtils.getString2Timestamp(bo.getBrandBeginDate());
                    Assert.isTrue(newBelongBeginDate.after(forbiddenBeginDate), "第" + (i + 2) + "行效果业绩归属开始日期格式有误，请重新上传");
                }
                if (StringUtils.isNotEmpty(bo.getRtbBeginDate())) {
                    Timestamp newBsiOrderBelongBeginDate = CrmUtils.getString2Timestamp(bo.getRtbBeginDate());
                    Assert.isTrue(newBsiOrderBelongBeginDate.after(forbiddenBeginDate), "第" + (i + 2) + "行商单业绩归属开始日期格式有误，请重新上传");
                }

                if (StringUtils.isNotBlank(bo.getContractSaleIds()) && !"0".equals(bo.getContractSaleIds())) {
                    Assert.isTrue(StringUtils.isNotEmpty(bo.getContractBeginDate()), "第" + (i + 2) + "行合约业绩归属开始日期格式有误，请重新上传");
                }
                if (StringUtils.isNotEmpty(bo.getContractBeginDate())) {
                    CrmUtils.isValidDateFormat(bo.getContractBeginDate(), CrmConstant.YYYY_MM_REG, "第" + (i + 2) + "行合约业绩归属开始日期格式有误，请重新上传");
                    Timestamp newContractBelongBeginDate = CrmUtils.getString2Timestamp(bo.getContractBeginDate(), CrmUtils.YYYYMM, false);

                    Assert.isTrue(newContractBelongBeginDate.after(CrmConstant.SALE_EFFECT_BEGIN_DATE), "第" + (i + 2) + "行合约业绩归属开始日期格式有误，请重新上传");

                    long numOfMonthsBetween = ChronoUnit.MONTHS.between(newContractBelongBeginDate.toLocalDateTime().toLocalDate(), Utils.getBeginDayOfMonth(Utils.getBeginOfDay(Utils.getNow())).toLocalDateTime().toLocalDate());
                    Assert.isTrue(numOfMonthsBetween <= 3 && numOfMonthsBetween >= 0, "第" + (i + 2) + "行合约业绩归属开始日期格式有误，请重新上传");
                }
            } catch (Exception e) {
                Assert.isTrue(false, "第" + (i + 2) + "行业绩归属开始日期格式有误，请重新上传");
            }

            List<Integer> channelSaleIds =
                    saleDtoList.stream()
                            .filter(crmSalePo -> !saleConfig.getDirectSaleTypes().contains(crmSalePo.getType()) && !saleConfig.getOperateSaleTypes().contains(crmSalePo.getType()))
                            .map(SaleDto::getId).collect(Collectors.toList());
            Assert.isTrue(CollectionUtils.isEmpty(channelSaleIds), "第" + (i + 2) + "行直客销售ID有误");
        }

        List<CustomerPo> customerPos = customerRepo.queryListByCustomerIds(Lists.newArrayList(customerIdMap.keySet()));
        List<Integer> agentCustomerIds = customerPos.stream().filter(customerPo -> customerPo.getIsAgent() == 1).map(CustomerPo::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(agentCustomerIds)) {
            List<String> rows = Lists.newArrayList();
            agentCustomerIds.stream().map(agentCustomerId -> String.valueOf(customerIdMap.get(agentCustomerId) + 2)).forEach(rows::add);
            Assert.isTrue(false, "第" + String.join(",", rows) + "行广告主客户ID有误");
        }

        // 校验客户、品牌ID名称匹配
        Map<Integer, String> customerIdNameMap = customerPos.stream().collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getUsername));
        Map<Integer, String> productIdNameMap = accProductRepo.queryProductNameMapByIds(productIds);
        for (int i = 0; i < excels.size(); i++) {
            SaleSeaCustomerExcelBo bo = excels.get(i);
            int currentRow = i + 2;
            String customerName = customerIdNameMap.get(Integer.valueOf(bo.getCustomerId()));
            Assert.isTrue(StringUtils.equals(customerName, bo.getCustomerName()), "第" + currentRow + "行广告主客户ID名称不匹配");

            if (StringUtils.isNotBlank(bo.getProductId()) && !"0".equals(bo.getProductId())) {
                String productName = productIdNameMap.get(Integer.valueOf(bo.getProductId()));
                Assert.isTrue(StringUtils.equals(productName, bo.getProductName()), "第" + currentRow + "行品牌ID名称不匹配");
            }
        }
    }

    @ApiOperation(value = "批量设置代理商渠道品牌销售")
    @RequestMapping(value = "/agent/batch_update_sale_belong", method = RequestMethod.POST)
    @ResponseBody
    public Response<Long> batchUpdateAgentChannel(@ApiIgnore Context context,
                                                  @RequestParam(value = "file", required = false) MultipartFile multipartFile) {

        List<SaleSeaAgentCustomerExcelBo> bos = new ArrayList<>();
        try {
            EasyExcelUtils.parseExcel(multipartFile.getBytes(), SaleSeaAgentCustomerExcelBo.class, bos::addAll);
        } catch (Exception e) {
            logger.error("error ", e);
        }

        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.handleAsyncExec(getOperator(context), AsyncOperationTypeEnum.BATCH_AGENT_ACCOUNT_CHANNEL_SALE, Utils.getUUID(),
                (unused) -> validateAgentCustomerExcel(bos),
                () -> {
                    int success = 0;
                    try {
                        for (SaleSeaAgentCustomerExcelBo bo : bos) {
                            Integer agentCustomerId = Integer.parseInt(bo.getCustomerId());
                            List<Integer> brandSaleIds = null;
                            if (StringUtils.isNotEmpty(bo.getBrandSaleIds())) {
                                String[] brandSaleIdArr = bo.getBrandSaleIds().split(";");
                                List<String> brandSaleIdsStr = Arrays.asList(brandSaleIdArr);
                                brandSaleIds = brandSaleIdsStr.stream().map(Integer::parseInt).collect(Collectors.toList());
                            }

                            List<Integer> rtbSaleIds = null;
                            if (StringUtils.isNotEmpty(bo.getRtbSaleIds())) {
                                String[] rtbSaleIdArr = bo.getRtbSaleIds().split(";");
                                List<String> rtbSaleIdsStr = Arrays.asList(rtbSaleIdArr);
                                rtbSaleIds = rtbSaleIdsStr.stream().map(Integer::parseInt).collect(Collectors.toList());
                            }

                            List<Integer> contractSaleIds = null;
                            if (StringUtils.isNotEmpty(bo.getContractSaleIds())) {
                                String[] contractSaleIdArr = bo.getContractSaleIds().split(";");
                                List<String> contractSaleIdsStr = Arrays.asList(contractSaleIdArr);
                                contractSaleIds = contractSaleIdsStr.stream().map(Integer::parseInt).collect(Collectors.toList());
                            }

                            if (CollectionUtils.isEmpty(brandSaleIds) && CollectionUtils.isEmpty(rtbSaleIds) && CollectionUtils.isEmpty(contractSaleIds)) {
                                continue;
                            }

                            Map<SaleSeaProductCategory, List<Integer>> productSaleMap = new HashMap<>();
                            if (Objects.nonNull(brandSaleIds)) {
                                productSaleMap.put(SaleSeaProductCategory.BRAND, brandSaleIds);
                            }
                            if (Objects.nonNull(rtbSaleIds)) {
                                productSaleMap.put(SaleSeaProductCategory.RTB, rtbSaleIds);
                            }
                            if (Objects.nonNull(contractSaleIds)) {
                                productSaleMap.put(SaleSeaProductCategory.CONTRACT, contractSaleIds);
                            }

                            // 效果归属业绩
                            Timestamp newBelongBeginDate = null;
                            if (StringUtils.isNotEmpty(bo.getRtbBeginDate())) {
                                newBelongBeginDate = CrmUtils.getString2Timestamp(bo.getRtbBeginDate());
                            }

                            // 商单归属业绩
                            Timestamp newBsiOrderBelongBeginDate = null;
                            if (StringUtils.isNotEmpty(bo.getBrandBeginDate())) {
                                newBsiOrderBelongBeginDate = CrmUtils.getString2Timestamp(bo.getBrandBeginDate());
                            }

                            // 合约归属业绩
                            Timestamp newContractBelongBeginDate = null;
                            if (StringUtils.isNotEmpty(bo.getContractBeginDate())) {
                                newContractBelongBeginDate = CrmUtils.getString2Timestamp(bo.getContractBeginDate(), CrmUtils.YYYYMM);
                            }

                            saleSeaService.bindSaleSeaByUpdateCon(getOperator(context), SaleSeaMappingSaveDto.builder()
                                    .mappingId(agentCustomerId)
                                    .mappingType(SaleSeaMappingType.CUSTOMER)
                                    .newBelongBeginDate(newBelongBeginDate)
                                    .newBsiOrderBelongBeginDate(newBsiOrderBelongBeginDate)
                                    .newContractBelongBeginDate(newContractBelongBeginDate)
                                    .productSaleMap(productSaleMap)
                                    .biliUserType(BiliUserType.CHANNEL_MANAGER)
                                    .build());
                            success++;
                            logger.info("batchUpdateAgentChannel success {}", JSON.toJSONString(bo));
                        }
                    } catch (Exception e) {
                        logger.error("error ", e);
                    }
                    return success;
                });

        return asyncOperationResultDto.isFail() ? Response.FAIL(asyncOperationResultDto.getErrorCode(), asyncOperationResultDto.getErrorMsg()) : Response.SUCCESS(asyncOperationResultDto.getProcessId());
    }

    private void validateAgentCustomerExcel(List<SaleSeaAgentCustomerExcelBo> bos) {

        Assert.isTrue(bos.size() <= 1000, "不能超过1000条");
        Map<Integer, SaleDto> saleMap = iSaleService.getAllSaleMap();

        Map<Integer, Integer> customerIdMap = Maps.newHashMap();
        for (int i = 0; i < bos.size(); i++) {

            SaleSeaAgentCustomerExcelBo bo = bos.get(i);
            List<SaleDto> saleDtoList = Lists.newArrayList();
            int currentRow = i + 2;
            try {
                Integer customerId = Integer.parseInt(bo.getCustomerId());
                customerIdMap.putIfAbsent(customerId, i);
            } catch (Exception e) {
                Assert.isTrue(false, "第" + currentRow + "行数据代理商客户ID格式有误，请重新上传");
            }

            if (StringUtils.isNotBlank(bo.getBrandSaleIds()) && !"0".equals(bo.getBrandSaleIds())) {
                saleSeaCheckComponent.checkSale(bo.getBrandSaleIds(), currentRow, saleDtoList, saleMap);
            }

            if (StringUtils.isNotBlank(bo.getRtbSaleIds()) && !"0".equals(bo.getRtbSaleIds())) {
                saleSeaCheckComponent.checkSale(bo.getRtbSaleIds(), currentRow, saleDtoList, saleMap);
            }

            if (StringUtils.isNotBlank(bo.getContractSaleIds()) && !"0".equals(bo.getContractSaleIds())) {
                saleSeaCheckComponent.checkSale(bo.getContractSaleIds(), currentRow, saleDtoList, saleMap);
            }

            //商单归属开始日期
            //效果归属开始日期
            //合约归属开始日期
            try {
                Timestamp forbiddenBeginDate = Utils.getSomeDayAfter(Utils.getNow(), -122);
                if (StringUtils.isNotEmpty(bo.getBrandBeginDate())) {
                    Timestamp newBelongBeginDate = CrmUtils.getString2Timestamp(bo.getBrandBeginDate());
                    Assert.isTrue(newBelongBeginDate.after(forbiddenBeginDate), "第" + currentRow + "行效果业绩归属开始日期格式有误，请重新上传");
                }
                if (StringUtils.isNotEmpty(bo.getRtbBeginDate())) {
                    Timestamp newBsiOrderBelongBeginDate = CrmUtils.getString2Timestamp(bo.getRtbBeginDate());
                    Assert.isTrue(newBsiOrderBelongBeginDate.after(forbiddenBeginDate), "第" + currentRow + "行商单业绩归属开始日期格式有误，请重新上传");
                }
                if (StringUtils.isNotBlank(bo.getContractBeginDate()) && !"0".equals(bo.getContractBeginDate())) {
                    Assert.isTrue(StringUtils.isNotEmpty(bo.getContractBeginDate()), "第" + currentRow + "行合约业绩归属开始日期格式有误，请重新上传");
                }
                if (StringUtils.isNotEmpty(bo.getContractBeginDate())) {
                    CrmUtils.isValidDateFormat(bo.getContractBeginDate(), CrmConstant.YYYY_MM_REG, "第" + currentRow + "行合约业绩归属开始日期格式有误，请重新上传");
                    Timestamp newContractBelongBeginDate = CrmUtils.getString2Timestamp(bo.getContractBeginDate(), CrmUtils.YYYYMM, false);
                    Assert.isTrue(newContractBelongBeginDate.after(CrmConstant.SALE_EFFECT_BEGIN_DATE), "第" + currentRow + "行合约业绩归属开始日期格式有误，请重新上传");
                    long numOfMonthsBetween = ChronoUnit.MONTHS.between(newContractBelongBeginDate.toLocalDateTime().toLocalDate(), Utils.getBeginDayOfMonth(Utils.getBeginOfDay(Utils.getNow())).toLocalDateTime().toLocalDate());
                    Assert.isTrue(numOfMonthsBetween <= 3 && numOfMonthsBetween >= 0, "第" + currentRow + "行合约业绩归属开始日期格式有误，请重新上传");
                }
            } catch (Exception e) {
                Assert.isTrue(false, "第" + currentRow + "行业绩归属开始日期格式有误，请重新上传");
            }

            List<Integer> directSaleIds =
                    saleDtoList.stream()
                            .filter(crmSalePo -> !saleConfig.getChannelSaleTypes().contains(crmSalePo.getType()) && !saleConfig.getOperateSaleTypes().contains(crmSalePo.getType()))
                            .map(SaleDto::getId).collect(Collectors.toList());
            Assert.isTrue(CollectionUtils.isEmpty(directSaleIds), "第" + currentRow + "行渠道销售ID有误");
        }

        List<CustomerPo> customerPos = customerRepo.queryListByCustomerIds(Lists.newArrayList(customerIdMap.keySet()));
        List<Integer> adCustomerIds = customerPos.stream().filter(customerPo -> customerPo.getIsAgent() == 0).map(CustomerPo::getId).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(adCustomerIds)) {
            List<String> rows = Lists.newArrayList();
            adCustomerIds.stream().map(adCustomerId -> String.valueOf(customerIdMap.get(adCustomerId) + 2)).forEach(rows::add);
            Assert.isTrue(false, "第" + String.join(",", rows) + "行代理商客户ID有误");
        }

        Map<Integer, String> customerIdNameMap = customerPos.stream().collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getUsername));
        for (int i = 0; i < bos.size(); i++) {
            SaleSeaAgentCustomerExcelBo bo = bos.get(i);
            String customerName = customerIdNameMap.get(Integer.valueOf(bo.getCustomerId()));
            Assert.isTrue(StringUtils.equals(customerName, bo.getCustomerName()), "第" + (i + 2) + "行代理商客户ID名称不匹配");
        }
    }

    @ApiOperation(value = "客户-公私海绑定关系")
    @RequestMapping(value = "/customer/bind/{mapping_id}", method = RequestMethod.GET)
    public Response<Map<Integer, Map<Integer, List<DropBoxVo>>>> customerSaleSeaList(@PathVariable("mapping_id") Integer mappingId) {

        SaleSeaMappingDto mapping = querySaleSeaService.getSaleSeaMap(SaleSeaMappingType.CUSTOMER, mappingId);
        return Response.SUCCESS(webSaleSeaService.getSaleMap2(mapping.getProductSaleMap()));
    }

    /**
     * 1、广告主帐户配置直客销售
     * 2、代理商帐户配置渠道销售
     *
     * @param context
     * @param formVo
     * @return
     */
    @ApiOperation(value = "帐号-绑定公私海绑定关系")
    @RequestMapping(value = "/account/bind", method = RequestMethod.POST)
    public Response<String> bindAccountSaleSea(@ApiIgnore Context context, @ApiParam("公私海绑定关系") @RequestBody SaleSeaMappingFormVo formVo) {
        Operator operator = getOperator(context);
        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.handleSingleExec(operator, AsyncOperationTypeEnum.ACCOUNT_SALE, String.valueOf(formVo.getMapping_id()),
                () -> {
                    saleSeaService.bindSaleSea(operator, webSaleSeaService.vo2dto(formVo, SaleSeaMappingType.ACCOUNT));
                    return null;
                });

        return asyncOperationResultDto.isSucc() ? Response.SUCCESS("") : Response.FAIL(asyncOperationResultDto.getErrorCode(), asyncOperationResultDto.getErrorMsg());
    }

    @ApiOperation(value = "帐号-公私海绑定关系")
    @RequestMapping(value = "/account/bind/{mapping_id}", method = RequestMethod.GET)
    public Response<Map<Integer, Map<Integer, List<DropBoxVo>>>> accountSaleSeaList(@PathVariable("mapping_id") Integer mappingId) {

        SaleSeaMappingDto mapping = querySaleSeaService.getSaleSeaMap(SaleSeaMappingType.ACCOUNT, mappingId);
        return Response.SUCCESS(webSaleSeaService.getSaleMap2(mapping.getProductSaleMap()));
    }

}
