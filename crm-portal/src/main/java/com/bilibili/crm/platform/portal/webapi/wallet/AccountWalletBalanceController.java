package com.bilibili.crm.platform.portal.webapi.wallet;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.effect.enums.ExportType;
import com.bilibili.crm.platform.api.effect.enums.ReportColumnBehavior;
import com.bilibili.crm.platform.api.rbac.IDomainUserService;
import com.bilibili.crm.platform.api.rbac.common.DataStrategyScope;
import com.bilibili.crm.platform.api.rbac.dto.DataStrategy;
import com.bilibili.crm.platform.api.wallet.dto.AccountWalletDetailBean;
import com.bilibili.crm.platform.api.wallet.dto.QueryWalletParam;
import com.bilibili.crm.platform.api.wallet.service.IQueryWalletService;
import com.bilibili.crm.platform.biz.annotation.Export;
import com.bilibili.crm.platform.biz.elasticsearch.elasticsearch_cloud.ElasticsearchCloudBaseConfig;
import com.bilibili.crm.platform.portal.common.ExportUtils;
import com.bilibili.crm.platform.portal.common.UploadCenterUtil;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.download.coverter.DownloadUtil;
import com.bilibili.crm.platform.portal.webapi.wallet.service.WebAccountWalletService;
import com.bilibili.crm.platform.portal.webapi.wallet.vo.AccountWalletVo;
import com.bilibili.crm.platform.portal.webapi.wallet.vo.AgentWalletVo;
import com.bilibili.crm.platform.portal.webapi.wallet.vo.QueryWalletVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * @author: brady
 * @time: 2021/9/8 5:19 下午
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/wallet")
@Api(value = "/wallet", description = "帐号钱包余额")
public class AccountWalletBalanceController extends BaseRestfulController {
    @Autowired
    private IQueryWalletService queryWalletService;
    @Autowired
    private WebAccountWalletService webAccountWalletService;
    @Autowired
    private ExportUtils exportUtils;
    @Autowired
    private ElasticsearchCloudBaseConfig elasticsearchCloudBaseConfig;
    @Resource
    private IDomainUserService domainUserService;

    @Autowired
    private IQueryAccountService queryAccountService;
    @Resource
    private UploadCenterUtil uploadCenterUtil;

    @ApiOperation(value = "查询广告主余额")
    @RequestMapping(value = "/list/ad", method = RequestMethod.GET)
    public Response<Pagination<List<AccountWalletVo>>> queryAccountWallet(@ApiIgnore Context context, QueryWalletVo queryVo) {
        QueryWalletParam param = webAccountWalletService.adVo2dto(queryVo);
        if (Objects.equals(elasticsearchCloudBaseConfig.getAccountEnable(), true)) {
            PageResult<AccountBaseDto> accountBaseDtoPageResult = getAccountIdsForWalletQuery(getOperator(context), param);
            if (CollectionUtils.isEmpty(accountBaseDtoPageResult.getRecords())) {
                return Response.SUCCESS(new Pagination<>(queryVo.getPage(), 0, Collections.emptyList()));
            } else {
                List<AccountWalletDetailBean> beanList = queryWalletService.queryWalletByAccountIds(accountBaseDtoPageResult.getRecords().stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList()));
                List<AccountWalletVo> vos = webAccountWalletService.dto2vo(beanList);
                return Response.SUCCESS(new Pagination<>(queryVo.getPage(), accountBaseDtoPageResult.getTotal(), vos));
            }
        }
        PageResult<AccountWalletDetailBean> result = queryWalletService.queryWalletDetailPageable(getOperator(context), param);
        List<AccountWalletVo> vos = webAccountWalletService.dto2vo(result);
        return Response.SUCCESS(new Pagination<>(queryVo.getPage(), result.getTotal(), vos));
    }

    @ApiOperation(value = "查询代理商余额")
    @RequestMapping(value = "/list/agent", method = RequestMethod.GET)
    public Response<Pagination<List<AgentWalletVo>>> queryAgentWallet(@ApiIgnore Context context, QueryWalletVo queryVo) {
        QueryWalletParam param = webAccountWalletService.agentVo2dto(queryVo);
        if (Objects.equals(elasticsearchCloudBaseConfig.getAccountEnable(), true)) {
            PageResult<AccountBaseDto> accountBaseDtoPageResult = getAccountIdsForWalletQuery(getOperator(context), param);
            if (CollectionUtils.isEmpty(accountBaseDtoPageResult.getRecords())) {
                return Response.SUCCESS(new Pagination<>(queryVo.getPage(), 0, Collections.emptyList()));
            } else {
                List<AccountWalletDetailBean> beanList = queryWalletService.queryWalletByAccountIds(accountBaseDtoPageResult.getRecords().stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList()));
                List<AgentWalletVo> vos = webAccountWalletService.agentDto2Vo(beanList, super.getOperator(context));
                return Response.SUCCESS(new Pagination<>(queryVo.getPage(), accountBaseDtoPageResult.getTotal(), vos));
            }
        }
        PageResult<AccountWalletDetailBean> result = queryWalletService.queryWalletDetailPageable(getOperator(context), webAccountWalletService.agentVo2dto(queryVo));
        List<AgentWalletVo> vos = webAccountWalletService.agentDto2Vo(result.getRecords(), super.getOperator(context));

        return Response.SUCCESS(new Pagination<>(queryVo.getPage(), result.getTotal(), vos));
    }

    @ApiOperation(value = "导出广告主余额")
    @Export
    @RequestMapping(value = "/export/ad", method = RequestMethod.GET)
    public Response<String> exportAccountWallet(@ApiIgnore Context context, QueryWalletVo queryVo,
                                                @ApiParam("导出类型：1-导出到邮箱 2-导出到下载中心") @RequestParam(value = "export_type", required = false, defaultValue = "1") Integer exportType) {
        queryVo.setPage(null);
        queryVo.setSize(null);
        if (queryVo.getDate() > Utils.getToday().getTime()) {
            //实时导出
            Supplier<List<AccountWalletVo>> supplier = () -> {
                List<AccountWalletDetailBean> result = queryWalletService.queryWalletDetail(getOperator(context), webAccountWalletService.adVo2dto(queryVo));
                return webAccountWalletService.dto2vo(result);
            };

            String fileName = DownloadUtil.generateFileName(null, null, ReportColumnBehavior.ACCOUNT_BALANCE_ADV);
            uploadCenterUtil.uploadToCenter(getOperator(context), supplier, fileName, ReportColumnBehavior.ACCOUNT_BALANCE_ADV);
            return Response.SUCCESS(DownloadUtil.return_str);
        } else {
            //历史截止日期导出
            QueryWalletParam queryWalletParam = webAccountWalletService.adVo2dto(queryVo);
            AlarmHelper.log("WalletParam", queryWalletParam);
            uploadCenterUtil.asyncBatchExportForCrm(getOperator(context).getOperatorName(), ReportColumnBehavior.ACCOUNT_BALANCE, queryWalletParam);
        }
        return Response.SUCCESS("导出任务提交成功");
    }

    @ApiOperation(value = "导出代理商余额")
    @Export
    @RequestMapping(value = "/export/agent", method = RequestMethod.GET)
    public Response<String> exportAgentWallet(@ApiIgnore Context context, QueryWalletVo queryVo,
                                              @ApiParam("导出类型：1-导出到邮箱 2-导出到下载中心") @RequestParam(value = "export_type", required = false, defaultValue = "1") Integer exportType) {
        queryVo.setPage(null);
        queryVo.setSize(null);

        if (queryVo.getDate() > Utils.getToday().getTime()) {
            //实时导出
            Supplier<List<AgentWalletVo>> supplier = () -> {
                List<AccountWalletDetailBean> result = queryWalletService.queryWalletDetail(getOperator(context), webAccountWalletService.agentVo2dto(queryVo));
                List<AgentWalletVo> vos = webAccountWalletService.agentDto2Vo(result, super.getOperator(context));
                return vos;
            };
            if (Objects.equals(exportType, ExportType.EMAIL.getCode())) {
                String response = exportUtils.asyncExportToEmail(getOperator(context), supplier, "代理商账号实时余额", true);
                return Response.SUCCESS(response);
            } else {
                String fileName = DownloadUtil.generateFileName(null, null, ReportColumnBehavior.ACCOUNT_BALANCE_AGENT);
                uploadCenterUtil.uploadToCenter(getOperator(context), supplier, fileName, ReportColumnBehavior.ACCOUNT_BALANCE_AGENT);
                return Response.SUCCESS(DownloadUtil.return_str);
            }
        } else {
            //历史截止日期导出
            QueryWalletParam queryWalletParam = webAccountWalletService.agentVo2dto(queryVo);
            uploadCenterUtil.asyncBatchExportForCrm(getOperator(context).getOperatorName(), ReportColumnBehavior.AGENT_ACCOUNT_BALANCE, queryWalletParam);
        }
        return Response.SUCCESS("导出任任务正在执行。请稍后前往b站邮箱查看导出数据。");
    }


    public PageResult<AccountBaseDto> getAccountIdsForWalletQuery(Operator operator, QueryWalletParam param) {
        QueryAccountParam queryAccountParam = new QueryAccountParam();
        DataStrategy dataStrategy = domainUserService.getDataStrategy(operator.getOperatorName());
        if (dataStrategy.getScope().equals(DataStrategyScope.EMPTY)) {
            return PageResult.emptyPageResult();
        } else if (dataStrategy.getScope().equals(DataStrategyScope.LIMIT)) {
            List<Integer> departmentIds = dataStrategy.getCondition();
            if (!CollectionUtils.isEmpty(param.getDepartmentIds())) {
                departmentIds.retainAll(param.getDepartmentIds());
            }
            if (CollectionUtils.isEmpty(departmentIds)) {
                return PageResult.emptyPageResult();
            }
            queryAccountParam.setDepartmentIds(param.getDepartmentIds());
        }
        queryAccountParam.setAccountIds(param.getAccountIds());
        queryAccountParam.setDependencyAgentIds(param.getDependenceAgentIds());
        queryAccountParam.setDepartmentIds(param.getDepartmentIds());
        queryAccountParam.setIsAgent(param.getIsAgent());
        queryAccountParam.setUserTypes(param.getUserTypes());
        return queryAccountService.queryAccountWithEs(queryAccountParam, param.getPage() - 1, param.getSize(), operator);
    }
}
