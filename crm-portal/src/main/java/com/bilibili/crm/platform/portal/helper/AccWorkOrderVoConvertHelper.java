package com.bilibili.crm.platform.portal.helper;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.NewOldCategoryMappingDto;
import com.bilibili.crm.platform.api.account.dto.UnitedIndustryDto;
import com.bilibili.crm.platform.api.account.service.ICommerceCenterCategoryService;
import com.bilibili.crm.platform.api.account.service.INewOldCategoryMappingService;
import com.bilibili.crm.platform.api.wo.dto.AccountWorkOrderDto;
import com.bilibili.crm.platform.biz.industry.service.UnitedIndustryService;
import com.bilibili.crm.platform.common.AdStatus;
import com.bilibili.crm.platform.common.WorkOrderStatus;
import com.bilibili.crm.platform.common.WorkOrderUserType;
import com.bilibili.crm.platform.portal.webapi.account.vo.AccWorkOrderVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Objects;

/**
 * Created by fanwenbin on 2017/8/21.
 */
@Component
public class AccWorkOrderVoConvertHelper implements BeanConvertHelper<AccWorkOrderVo, AccountWorkOrderDto> {

    @Autowired
    private ICommerceCenterCategoryService commerceCenterCategoryService;
    @Autowired
    private INewOldCategoryMappingService newOldCategoryMappingService;

    @Resource
    private UnitedIndustryService unitedIndustryService;

    /**
     * 涉及到旧行业的 都删除掉 todo
     */
    @Override
    public AccWorkOrderVo convertDtoToVo(AccountWorkOrderDto accountWorkOrderDto) {

        Integer categoryFirstId = null, categorySecondId = null;
        if (Utils.isPositive(accountWorkOrderDto.getCommerceCategoryFirstId()) && Utils.isPositive(accountWorkOrderDto.getCommerceCategorySecondId())) {
            categoryFirstId = accountWorkOrderDto.getCommerceCategoryFirstId();
            categorySecondId = accountWorkOrderDto.getCommerceCategorySecondId();
        } else if (Utils.isPositive(accountWorkOrderDto.getCategoryFirstId()) && Utils.isPositive(accountWorkOrderDto.getCategorySecondId())) {
            Integer oldCategorySecondId = accountWorkOrderDto.getCategorySecondId();
            NewOldCategoryMappingDto newCategoryByOld = newOldCategoryMappingService.getNewCategoryByOld(oldCategorySecondId);
            categoryFirstId = newCategoryByOld == null ? null : newCategoryByOld.getNewCategoryFirstId();
            categorySecondId = newCategoryByOld == null ? null : newCategoryByOld.getNewCategorySecondId();
        }
        CategoryDto firstCategoryDto = Utils.isPositive(categoryFirstId) ? commerceCenterCategoryService.getCategoryDtoById(categoryFirstId) : null;
        CategoryDto secondCategoryDto = Utils.isPositive(categorySecondId) ? commerceCenterCategoryService.getCategoryDtoById(categorySecondId) : null;

        Integer unitedFirstIndustryId = null, unitedSecondIndustryId = null, unitedThirdIndustryId = null;
        if (Utils.isPositive(accountWorkOrderDto.getUnitedFirstIndustryId())) {
            unitedFirstIndustryId = accountWorkOrderDto.getUnitedFirstIndustryId();
            unitedSecondIndustryId = accountWorkOrderDto.getUnitedSecondIndustryId();
            unitedThirdIndustryId = accountWorkOrderDto.getUnitedThirdIndustryId();
        }
        UnitedIndustryDto unitedFirstIndustryDto = Utils.isPositive(unitedFirstIndustryId) ? unitedIndustryService.queryIndustryById(accountWorkOrderDto.getUnitedFirstIndustryId()) : null;
        UnitedIndustryDto unitedSecondIndustryDto = Utils.isPositive(unitedSecondIndustryId) ? unitedIndustryService.queryIndustryById(accountWorkOrderDto.getUnitedSecondIndustryId()) : null;
        UnitedIndustryDto unitedThirdIndustryDto = Utils.isPositive(unitedThirdIndustryId) ? unitedIndustryService.queryIndustryById(accountWorkOrderDto.getUnitedThirdIndustryId()) : null;

        return AccWorkOrderVo.builder()
                .work_order_id(accountWorkOrderDto.getWorkOrderId())
                .customer_id(accountWorkOrderDto.getCustomerId())
                .ctime(accountWorkOrderDto.getCtime())
                .creator_type(accountWorkOrderDto.getCreatorType())
                .creator_type_desc(WorkOrderUserType.getByCode(accountWorkOrderDto.getCreatorType()))
                .user_name(accountWorkOrderDto.getUsername())
                .account_id(accountWorkOrderDto.getAccountId())
                .is_agent(accountWorkOrderDto.getIsAgent())
                .status(accountWorkOrderDto.getStatus())
                .status_desc(WorkOrderStatus.getByCode(accountWorkOrderDto.getStatus()).getDesc())
                .ad_status(accountWorkOrderDto.getAdStatus())
                .ad_status_desc(Objects.isNull(accountWorkOrderDto.getAdStatus()) ? "" : AdStatus.getByCode(accountWorkOrderDto.getAdStatus()).getDesc())
                .category_first_id(categoryFirstId)
                .category_first_name(null == firstCategoryDto ? "" : firstCategoryDto.getName())
                .category_second_id(categorySecondId)
                .category_second_name(null == secondCategoryDto ? "" : secondCategoryDto.getName())
                .creator(accountWorkOrderDto.getCreatorName())
                .audit_remark(accountWorkOrderDto.getAuditRemark())
                .united_first_industry_id(unitedFirstIndustryId)
                .united_first_industry_name(null == unitedFirstIndustryDto ? "" : unitedFirstIndustryDto.getName())
                .united_second_industry_id(unitedSecondIndustryId)
                .united_second_industry_name(null == unitedSecondIndustryDto ? "" : unitedSecondIndustryDto.getName())
                .united_third_industry_id(unitedThirdIndustryId)
                .united_third_industry_name(null == unitedThirdIndustryDto ? "" : unitedThirdIndustryDto.getName())
                .build();
    }
}
