package com.bilibili.crm.platform.portal.webapi.effect.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-12-23 14:53:18
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EffectBillClaimDetailVo implements Serializable {

    /**
     * 应收账单表主键Id
     */
    @ApiModelProperty("应收账单表主键Id")
    private Long effect_bill_id;

    @ApiModelProperty("认领金额")
    private BigDecimal claim_amount;
}
