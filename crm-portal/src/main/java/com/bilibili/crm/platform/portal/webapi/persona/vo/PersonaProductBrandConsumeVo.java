package com.bilibili.crm.platform.portal.webapi.persona.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

/**
 * @author: brady
 * @time: 2021/3/22 2:32 下午
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonaProductBrandConsumeVo extends PersonaProductConsumeVo {

    @ApiModelProperty("硬广首次消费时间")
    private String hard_first_consume_date;
    @ApiModelProperty("硬广最新消费时间")
    private String hard_latest_consume_date;
    @ApiModelProperty("硬广最新消费时间距今时间")
    private Integer hard_interval_date;
}
