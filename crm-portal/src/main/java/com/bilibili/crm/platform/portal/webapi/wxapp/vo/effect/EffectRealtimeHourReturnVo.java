package com.bilibili.crm.platform.portal.webapi.wxapp.vo.effect;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2020/10/19 7:56 下午
 */
@Data
public class EffectRealtimeHourReturnVo {

    /**
     * 最新更新时间
     */
    @ApiModelProperty("最新更新时间")
    private String latest_update_time;

    /**
     * 预估开始小时
     */
    @ApiModelProperty("预估开始小时")
    private Integer estimate_start_hour;

    /**
     * 上一个完成加载的小时 = 预估开始的小时 - 1
     */
    @ApiModelProperty("上一个完成加载的小时")
    private Integer lastCompletedLoadHour;

    /**
     * 分时数据
     */
    @ApiModelProperty("分时数据")
    private EffectRealtimeHourTypeVo by_hour_data;

    /**
     * 累计数据
     */
    @ApiModelProperty("累计数据")
    private EffectRealtimeHourTypeVo sum_hour_data;

    public static EffectRealtimeHourReturnVo init() {
        EffectRealtimeHourReturnVo effectRealtimeHourReturnVo = new EffectRealtimeHourReturnVo();
        effectRealtimeHourReturnVo.setLatest_update_time(CrmUtils.formatDate(Utils.getToday(), CrmUtils.YYYYMMDDHHMMSS));
        effectRealtimeHourReturnVo.setEstimate_start_hour(0);
        effectRealtimeHourReturnVo.setLastCompletedLoadHour(0);

        EffectRealtimeHourTypeVo by_hour_data = initOneGroupHoursData();
        effectRealtimeHourReturnVo.setBy_hour_data(by_hour_data);

        EffectRealtimeHourTypeVo sum_hour_data = initOneGroupHoursData();
        effectRealtimeHourReturnVo.setSum_hour_data(sum_hour_data);
        return effectRealtimeHourReturnVo;
    }

    private static EffectRealtimeHourTypeVo initOneGroupHoursData() {
        EffectRealtimeHourTypeVo by_hour_data = new EffectRealtimeHourTypeVo();
        by_hour_data.setToday_data(EffectRealtimeHourOneDayVo.init());
        by_hour_data.setLast_week_data(EffectRealtimeHourOneDayVo.init());
        by_hour_data.setYesterday_data(EffectRealtimeHourOneDayVo.init());
        return by_hour_data;
    }
}
