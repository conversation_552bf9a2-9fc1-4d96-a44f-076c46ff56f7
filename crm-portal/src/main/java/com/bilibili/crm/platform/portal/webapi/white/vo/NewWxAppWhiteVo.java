package com.bilibili.crm.platform.portal.webapi.white.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * @description:
 * @author: brady
 * @time: 2020/11/16 5:13 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewWxAppWhiteVo {

    @ApiModelProperty("域账号")
    private String user_name;

    @ApiModelProperty("昵称")
    private String nick_name;

    @ApiModelProperty("企业工号")
    private String work_num;

    @ApiModelProperty("白名单类型 1-大盘 2-之光 3-销售 4 带货")
    private Integer white_type;

    @ApiModelProperty("是否推送 0-否 1-是")
    private Integer is_push;

    @ApiModelProperty("是否管理员 0-否 1-是")
    private Integer is_manager;

}
