package com.bilibili.crm.platform.portal.aop;

import com.bilibili.crm.platform.api.exception.CrmQueryException;
import com.bilibili.crm.platform.api.exception.code.QueryExceptionCode;
import org.aspectj.lang.JoinPoint;
import org.aspectj.lang.annotation.Aspect;
import org.aspectj.lang.annotation.Before;
import org.aspectj.lang.annotation.Pointcut;
import org.springframework.stereotype.Component;

@Component
@Aspect
public class CrmControllerAspect {

    private final String CRM_RTB_ACHIEVEMENT = "execution(public * com.bilibili.crm.platform.portal.webapi.achievement.AchievementRtbController.*(..))";

    @Pointcut(value = CRM_RTB_ACHIEVEMENT)
    public void requestRtbPointcut() {
    }

    /**
     * 效果业绩看板
     *
     * @throws Throwable
     */
    @Before("requestRtbPointcut()")
    public void requestMethodInvoke(JoinPoint pjp) throws Throwable {

        //20200915 0-6点暂停服务
        long date = System.currentTimeMillis();
        if (date >= 1600099200000L && date <= 1600120800000L) {
            throw new CrmQueryException(QueryExceptionCode.NO_RESULT);
        }

    }
}
