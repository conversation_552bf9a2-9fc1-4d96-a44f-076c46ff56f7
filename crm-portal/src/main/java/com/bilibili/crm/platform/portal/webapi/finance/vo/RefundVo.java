package com.bilibili.crm.platform.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by user on 2017/8/10.
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RefundVo {

    @ApiModelProperty("ID")
    private Integer id;

    @ApiModelProperty("账号ID")
    private Integer account_id;

    @ApiModelProperty("账号ID")
    private Long serial_number;

    @ApiModelProperty("退款金额,单位(元)")
    private BigDecimal amount;

    @ApiModelProperty("退款类型: 27-现金退款 28-返货退款 29-专项返货退款")
    private Integer type;

    @ApiModelProperty("退款类型描述: 27-现金退款 28-返货退款 29-专项返货退款")
    private String type_desc;

    @ApiModelProperty("退款附件")
    private List<RefundAttachmentVo> refund_attachment_vo;

    @ApiModelProperty("状态")
    private Integer status;

    @ApiModelProperty("状态描述: 1-审核通过 2-作废 3-待审核 4-审核驳回")
    private String status_desc;

    @ApiModelProperty("驳回理由")
    private String reason;

    @ApiModelProperty("备注：如实银行转账,记录流水号")
    private String remark;

    @ApiModelProperty("审批时间")
    private String processing_time;

    @ApiModelProperty("资金池")
    private String wallet_type;

}
