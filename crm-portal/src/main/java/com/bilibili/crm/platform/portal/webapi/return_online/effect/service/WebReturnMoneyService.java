package com.bilibili.crm.platform.portal.webapi.return_online.effect.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerInfoDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.oa.dto.contract.BusinessInfo;
import com.bilibili.crm.platform.api.return_online.effect.IReturnMoneyService;
import com.bilibili.crm.platform.api.return_online.effect.dto.ReturnMoneyDto;
import com.bilibili.crm.platform.api.return_online.effect.dto.param.QueryReturnMoneyParam;
import com.bilibili.crm.platform.biz.po.CustomerQualificationInfoPo;
import com.bilibili.crm.platform.biz.repo.CustomerQualificationInfoRepo;
import com.bilibili.crm.platform.biz.service.oa.component.OaSoaManager;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.portal.common.ExportUtils;
import com.bilibili.crm.platform.portal.webapi.return_online.effect.convertor.ReturnMoneyConvertor;
import com.bilibili.crm.platform.portal.webapi.return_online.effect.vo.*;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/1/28 15:22
 */
@Slf4j
@Service
public class WebReturnMoneyService {

    @Resource
    private IReturnMoneyService returnMoneyService;

    @Resource
    private ICustomerQueryService customerQueryService;

    @Resource
    private ExportUtils exportUtils;

    @Resource
    private CustomerQualificationInfoRepo customerQualificationInfoRepo;

    @Resource
    private OaSoaManager oaSoaManager;

    public Pagination<List<ReturnMoneyVo>> pageReturnMoney(QueryReturnMoneyVo vo) {
        // 参数转换
        QueryReturnMoneyParam param = ReturnMoneyConvertor.vo2Param(vo);

        // 分页查询
        PageResult<ReturnMoneyDto> result = returnMoneyService.pageReturnMoney(param);

        return new Pagination<>(param.getPageNum(), result.getTotal(), CrmUtils.convert(result.getRecords(), ReturnMoneyConvertor::dto2Vo));
    }


    public String export(Operator operator, QueryReturnMoneyVo vo) {
        // 参数转换
        QueryReturnMoneyParam param = ReturnMoneyConvertor.vo2Param(vo);

        List<ReturnMoneyDto> dtos = returnMoneyService.listReturnMoney(param);

        if (CollectionUtils.isEmpty(dtos)) {
            return "当前条件下无数据导出";
        }

        List<ReturnMoneyExportVo> returnMoneyExportVos = CrmUtils.convert(dtos, ReturnMoneyConvertor::dto2ExportVo);

        // 导出邮件
        exportUtils.asyncExportToEmail(operator, () -> returnMoneyExportVos, "您好，效果-返货返现列表导出数据，请查收", true);

        return "已将导出数据文件发送至你的b站邮箱。前往查看已将导出数据文件发送至你的b站邮箱。前往查看";
    }

    public List<ReturnMoneyAgentCustomerVo> listAgentCustomer(Integer customerId, String customerNameLike) {
        List<ReturnMoneyAgentCustomerVo> vos = Lists.newArrayList();
        if (Utils.isPositive(customerId)) {
            // 根据 客户ID 查询
            CustomerInfoDto customerInfoDto = customerQueryService.getCustomerDto(customerId);

            if (Objects.nonNull(customerInfoDto)) {
                vos.add(
                        ReturnMoneyAgentCustomerVo.builder()
                                .agent_customer_id(customerId)
                                .agent_customer_name(customerInfoDto.getUsername())
                                .agent_business_licence_code(customerInfoDto.getBusinessLicenceCode())
                                .build()
                );
            }
        }

        if (StringUtils.isNotEmpty(customerNameLike)) {
            // 根据 客户名称 查询
            List<CustomerBaseDto> customerBaseDtos = customerQueryService.queryCustomerListByNameLike(customerNameLike);
            if (CollectionUtils.isNotEmpty(customerBaseDtos)) {
                List<Integer> customerIds = CrmUtils.convert(customerBaseDtos, CustomerBaseDto::getId);
                // 营业执照信息
                List<CustomerQualificationInfoPo> qualificationInfoPos = customerQualificationInfoRepo.queryQualificationInfosByCustomerIds(customerIds);
                Map<Integer, String> businessLicenceCodeMap = CrmUtils.toMap(qualificationInfoPos, CustomerQualificationInfoPo::getCustomerId, CustomerQualificationInfoPo::getBusinessLicenceCode);

                customerBaseDtos.forEach(dto -> {
                    ReturnMoneyAgentCustomerVo customerVo = ReturnMoneyAgentCustomerVo.builder()
                            .agent_customer_id(dto.getId())
                            .agent_customer_name(dto.getUsername())
                            .agent_business_licence_code(businessLicenceCodeMap.getOrDefault(dto.getId(), "--"))
                            .build();

                    vos.add(customerVo);
                });
            }
        }

        return vos;
    }

    public List<ReturnMoneyOaContractVo> listOaContract(String customerName, String oaContractNoLike, String oaContractNameLike) {
        List<ReturnMoneyOaContractVo> vos = Lists.newArrayList();
        // OA侧查询合同信息
        List<BusinessInfo> businessInfos = oaSoaManager.getContractBusinessInfo(customerName);

        // 根绝合同号和合同名称模糊匹配
        businessInfos = CrmUtils.filter(businessInfos, businessInfo -> {
            // 模糊匹配合同号
            String contractCode = businessInfo.getBasicInfo().getContractCode();
            if (contractCode.contains(oaContractNoLike)) {
                return true;
            }

            // 模糊匹配合同名称
            String contractTitle = businessInfo.getBasicInfo().getContractTitle();
            return contractTitle.contains(oaContractNameLike);
        });

        // 按照创建时间倒叙
        businessInfos.sort((c1, c2) -> {
            String timeC1Str = c1.getPromptInfo().getCreateDate();
            if (StringUtils.isEmpty(timeC1Str)) {
                return -1;
            }
            String timeC2Str = c2.getPromptInfo().getCreateDate();
            if (StringUtils.isEmpty(timeC2Str)) {
                return 1;
            }
            Timestamp timeC1 = CrmUtils.parseTimestamp(c1.getPromptInfo().getCreateDate());
            Timestamp timeC2 = CrmUtils.parseTimestamp(c2.getPromptInfo().getCreateDate());

            return timeC1.compareTo(timeC2);
        });

        return vos;
    }
}
