package com.bilibili.crm.platform.portal.webapi;

import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.AsyncOperationService;
import com.bilibili.crm.platform.api.dto.AsyncOperationResultDto;
import com.bilibili.crm.platform.api.exception.code.CrmAppExceptionCode;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.google.common.collect.ImmutableMap;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import java.util.Map;
import java.util.Objects;

@Slf4j
@RestController
@RequestMapping("/web_api/v1/async/operations")
@Api(value = "/async/operations", description = "异步操作相关")
public class AsyncOperationController extends BaseRestfulController {

    @Autowired
    private AsyncOperationService asyncOperationService;

    @ApiOperation(value = "查询异步操作执行结果")
    @RequestMapping(value = "result/{id}", method = RequestMethod.GET)
    public Response<Map<String, Object>> asyncResult(@PathVariable("id") Integer id) {
        AsyncOperationResultDto<Void> asyncOperationResultDto = asyncOperationService.queryAsyncExecResult(id);
        if (Objects.isNull(asyncOperationResultDto)) {
            return Response.FAIL(CrmAppExceptionCode.NO_RESULT);
        }
        return Response.SUCCESS(ImmutableMap.of("status", asyncOperationResultDto.getProcessStatus().getCode(),
                "count", asyncOperationResultDto.getProcessCount(),
                "errorMsg", asyncOperationResultDto.getErrorMsg()));
    }
}
