package com.bilibili.crm.platform.portal.common;

import com.alibaba.excel.EasyExcel;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.LocalDateUtils;
import com.bilibili.crm.common.SafeStopWatch;
import com.bilibili.crm.platform.api.effect.dto.BossAttachmentsDto;
import com.bilibili.crm.platform.api.effect.dto.MiddleExportDto;
import com.bilibili.crm.platform.api.effect.enums.BossTaskStatus;
import com.bilibili.crm.platform.api.effect.enums.ReportColumnBehavior;
import com.bilibili.crm.platform.api.effect.enums.ReportColumnBizType;
import com.bilibili.crm.platform.api.effect.service.IRtbConsumeService;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.repo.effect.BossAttachmentsRepo;
import com.bilibili.crm.platform.biz.service.contract.component.ContractBiiPeriodAlertComponent;
import com.bilibili.crm.platform.portal.export.SpringExportTemplate;
import com.bilibili.rbac.api.service.IUserService;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.MutablePair;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.logging.log4j.util.Strings;

import java.io.File;
import java.io.OutputStream;
import java.nio.file.Files;
import java.nio.file.Paths;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

@Slf4j
public class UploadCenterInstance<PARAM, EXCEL> {

    private final PaladinConfig paladinConfig;
    private final BossAttachmentsRepo bossAttachmentsRepo;

    private final IRtbConsumeService rtbConsumeService;

    private final List<EXCEL> data = new ArrayList<>();

    private final List<Pair<MiddleExportDto, Boolean>> taskQueue = new ArrayList<>();

    private Class<EXCEL> tCls;
    private final Integer tenantId;
    private final IUserService userService;
    private final ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent;
    private final RedisLock redisLock;


    public UploadCenterInstance(PaladinConfig paladinConfig,
                                BossAttachmentsRepo bossAttachmentsRepo,
                                IRtbConsumeService rtbConsumeService,
                                Integer tenantId, IUserService userService,
                                ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent,
                                RedisLock redisLock) {
        this.paladinConfig = paladinConfig;
        this.bossAttachmentsRepo = bossAttachmentsRepo;
        this.rtbConsumeService = rtbConsumeService;
        this.tenantId = tenantId;
        this.userService = userService;
        this.contractBiiPeriodAlertComponent = contractBiiPeriodAlertComponent;
        this.redisLock = redisLock;
    }

    @SuppressWarnings("unchecked")
    public void export(Long agentBid, String operatorName, PARAM param, SpringExportTemplate<PARAM, EXCEL> exportTemplate) {
        AlarmHelper.log("StartExport", Thread.currentThread().getName(), agentBid, operatorName, exportTemplate.getScene(), param);
        int maxSingleFileRecordCount = paladinConfig.getIntegerOrDefault("export.center.single.file.max.record.count." + exportTemplate.getScene().name(), 200_000);
        ReportColumnBehavior scene = exportTemplate.getScene();
        SafeStopWatch watch = new SafeStopWatch("Download_" + scene.name());
        tCls = exportTemplate.getResClass();
        String exportBatch;
        try {
            exportBatch = exportTemplate.getExportBatch(param);
        } catch (Exception e) {
            AlarmHelper.alarmEx("ExportBatchErr", e, param);
            exportBatch = exportTemplate.getScene().getDesc();
        }

        long bizId;
        if (StringUtils.isNotBlank(operatorName)) {
            bizId = userService.getUserByUserName(tenantId, operatorName).getUserBaseDto().getId().longValue();
            contractBiiPeriodAlertComponent.sendQiwe(operatorName, buildContent(scene, BossTaskStatus.DOWNLOADING, exportBatch));
        } else {
            bizId = agentBid;
        }
        try {
            List<PARAM> ps = exportTemplate.getPartition(param);
            AlarmHelper.log("TemplatePartitionParameter", ps);

            boolean newTask = false;
            int totalCnt = 0;

            for (int i = 0; i < ps.size(); i++) {
                PARAM p = ps.get(i);

                if (CollectionUtils.isEmpty(taskQueue)) {
                    watch.pushTask("NewTask_" + i);
                    newDownloadTask(bizId, operatorName, exportBatch, exportTemplate.getFileName(exportBatch, p), scene);
                }

                watch.pushTask("GenData_" + i);
                AlarmHelper.log("StartProcess", i, p);
                List<EXCEL> batchData = exportTemplate.getData(p);
                batchData = batchData == null ? Collections.emptyList() : batchData;
                AlarmHelper.log("ProcessParameterDone", i, p, batchData.size(), data.size());
                totalCnt += batchData.size();

                watch.pushTask("AddAll_" + i);
                data.addAll(batchData);

                // 超过单文件批次，则分文件
                List<EXCEL> lastBatch = new ArrayList<>();
                for (List<EXCEL> subList : Lists.partition(data, maxSingleFileRecordCount)) {
                    if (newTask) {
                        newTask = false;
                        watch.pushTask("NewTask_" + i);
                        newDownloadTask(bizId, operatorName, exportBatch, exportTemplate.getFileName(exportBatch, p), scene);
                        watch.pushTask("MarkPrevSuccess_" + i);
                    }
                    List<EXCEL> subListNewList = new ArrayList<>(subList);
                    if (subList.size() < maxSingleFileRecordCount) {
                        lastBatch.addAll(subListNewList);
                        break;
                    }
                    watch.pushTask("Download_" + i);
                    downloadData(subListNewList, exportTemplate.getIgnoreCol(param), scene);
                    newTask = true;
                }

                data.clear();
                data.addAll(lastBatch);
                AlarmHelper.log("ProcessData", i, batchData.size(), data.size());
            }

            if (totalCnt > 0) {
                if (!data.isEmpty()) {
                    AlarmHelper.log("UploadFinalData", data.size());
                    watch.pushTask("UploadFinalData");
                    downloadData(data, exportTemplate.getIgnoreCol(param), scene);
                }
                watch.pushTask("DoneMarkSuccess");
                markSuccess();
                watch.pushTask("SendQiWei");
                String msgContent = taskQueue.size() > 1 ? buildContent(scene, BossTaskStatus.DOWNLOADED, exportBatch) : String.format("【下载任务成功】：%s_%s<br/>" +
                                "· 任务类型：%s明细数据<br/>" +
                                "· 任务状态：任务成功<br/>" +
                                "· 共有%d个文件<br/>" +
                                "请前往PC端CRM-下载中心查看"
                        , scene.getDesc(), exportBatch
                        , scene.getDesc(), taskQueue.size()
                );
                contractBiiPeriodAlertComponent.sendQiwe(operatorName, msgContent);
            } else {
                if (CollectionUtils.isEmpty(taskQueue)) {
                    newDownloadTask(bizId, operatorName, exportBatch, exportTemplate.getFileName(exportBatch, param), scene);
                }
                watch.pushTask("DoneMarkNull");
                markNull();
                watch.pushTask("SendQiWei");
                contractBiiPeriodAlertComponent.sendQiwe(operatorName, buildContent(scene, BossTaskStatus.NULL, exportBatch));
            }
        } catch (Exception e) {
            AlarmHelper.log("DownloadException", e);
            markFailed();
            contractBiiPeriodAlertComponent.sendQiwe(operatorName, buildContent(scene, BossTaskStatus.FAIL, exportBatch));
        }
        watch.log();
    }

    private void markNull() {
        Pair<MiddleExportDto, Boolean> task = taskQueue.get(taskQueue.size() - 1);
        AlarmHelper.log("MarkNull", task);
        bossAttachmentsRepo.updateDownloadNullStatus(task.getKey().getBossId(), getFinalFileName(task.getKey().getFileName()));
        task.setValue(true);
    }

    private String getFinalFileName(String fileName) {
        return fileName + "_PartFinal.xlsx";
    }

    private String getFileName(Integer index, String fileName) {
        if (index == null) {
            return fileName + ".xlsx";
        }
        return String.format("%s_Part%d.xlsx", fileName, index);
    }

    private void downloadData(List<EXCEL> batchData, List<String> excludeColumnFiledNames, ReportColumnBehavior scene) throws Exception {
        AlarmHelper.log("DownloadData", scene, batchData.size());
        Pair<MiddleExportDto, Boolean> task = taskQueue.get(taskQueue.size() - 1);

        File dir = new File("/tmp");
        if (!dir.exists()) {
            boolean createSuccess = dir.mkdirs();
            AlarmHelper.log("Mkdir", scene, createSuccess);
        }
        String yyyyMMdd = LocalDateUtils.format(LocalDate.now(), LocalDateUtils.YYYYMMDD);
        dir = new File("/tmp/" + yyyyMMdd);

        if (!dir.exists()) {
            boolean createSuccess = dir.mkdirs();
            AlarmHelper.log("Mkdir", scene, createSuccess);
        }

        String fileName = String.format("/tmp/%s/%d_%s.xlsx",
                yyyyMMdd,
                System.currentTimeMillis(),
                UUID.randomUUID().toString().replaceAll("-", ""));

        SafeStopWatch watch = new SafeStopWatch("Download_" + taskQueue.size());
        File file = new File(fileName);
        try (OutputStream out = Files.newOutputStream(Paths.get(fileName))) {
            watch.pushTask("GenExcel");
            EasyExcel.write(out, tCls)
                    .sheet("sheet1")
                    .excludeColumnFiledNames(excludeColumnFiledNames)
                    .doWrite(batchData);
        }
        watch.pushTask("SaveBoss");
        rtbConsumeService.saveFileToBoss(task.getKey(), file, scene);
        watch.log();
    }

    private void markSuccess() {
        Pair<MiddleExportDto, Boolean> task = taskQueue.get(taskQueue.size() - 1);
        if (taskQueue.size() == 1) {
            bossAttachmentsRepo.updateDownloadStatus(task.getKey().getBossId(), getFileName(null, task.getKey().getFileName()));
        } else {
            bossAttachmentsRepo.updateDownloadStatus(task.getKey().getBossId(), getFinalFileName(task.getKey().getFileName()));
        }
    }

    private void markFailed() {
        for (int i = 0; i < taskQueue.size(); i++) {
            Pair<MiddleExportDto, Boolean> task = taskQueue.get(i);
            AlarmHelper.log("MarkFailed", i, taskQueue.size(), task);
            if (i == taskQueue.size() - 1) {
                bossAttachmentsRepo.updateDownloadFailStatus(task.getKey().getBossId(), getFinalFileName(task.getKey().getFileName()));
            } else {
                if (!task.getValue()) {
                    task.setValue(true);
                    bossAttachmentsRepo.updateDownloadFailStatus(task.getKey().getBossId(), getFileName(i, task.getKey().getFileName()));
                }
            }
        }
    }

    private void newDownloadTask(Long bizId, String email, String exportBatch, String fileName, ReportColumnBehavior scene) {
        if (!taskQueue.isEmpty()) {
            Pair<MiddleExportDto, Boolean> prevTask = taskQueue.get(taskQueue.size() - 1);
            AlarmHelper.log("MarkPrevSuccess", taskQueue.size(), prevTask);
            prevTask.setValue(true);
            bossAttachmentsRepo.updateDownloadStatus(prevTask.getKey().getBossId(), getFileName(taskQueue.size(), prevTask.getKey().getFileName()));
        }

        ReportColumnBizType bizType = StringUtils.isBlank(email) ? ReportColumnBizType.AGENT : ReportColumnBizType.CRM;
        String key = "crm_file_boss" + "_" + bizType.getCode() + "_" + bizId + "_" + scene.getCode();
        Long bossId;
        String bossKey = UUID.randomUUID() + ".xlsx";
        try {
            redisLock.getLock(key, 60);
            bossId = bossAttachmentsRepo.insert(BossAttachmentsDto.builder()
                    .bizId(bizId)
                    .bizType(bizType.getCode())
                    .behaviorId(scene.getCode())
                    .fileName(fileName)
                    .bossKey(bossKey)
                    .build());
        } finally {
            redisLock.releaseLock(key);
        }
        MiddleExportDto exportDto = MiddleExportDto.builder()
                .fileName(fileName)
                .email(email)
                .bizType(StringUtils.isBlank(email) ? ReportColumnBizType.AGENT : ReportColumnBizType.CRM)
                .bizId(bizId)
                .build();
        exportDto.setBossId(bossId);
        exportDto.setBossKey(bossKey);
        contractBiiPeriodAlertComponent.sendQiwe(exportDto.getEmail(), buildContent(scene, BossTaskStatus.DOWNLOADING, exportBatch));
        AlarmHelper.log("NewTask", bizId, fileName, scene, exportDto, taskQueue.size());
        taskQueue.add(MutablePair.of(exportDto, false));
    }

    public static String buildContent(ReportColumnBehavior behavior, BossTaskStatus bossTaskStatus, String exportBatch) {
        if (bossTaskStatus.equals(BossTaskStatus.DOWNLOADING)) {
            return String.format("【有新增的下载任务】：%s_%s<br/>" +
                            "· 任务类型：%s明细数据<br/>" +
                            "· 任务状态：创建成功<br/>" +
                            "请前往PC端CRM-下载中心查看"
                    , behavior.getDesc(), exportBatch, behavior.getDesc()
            );
        } else if (bossTaskStatus.equals(BossTaskStatus.DOWNLOADED)) {
            return String.format("【下载任务成功】：%s_%s<br/>" +
                            "· 任务类型：%s明细数据<br/>" +
                            "· 任务状态：任务成功<br/>" +
                            "请前往PC端CRM-下载中心查看"
                    , behavior.getDesc(), exportBatch, behavior.getDesc()
            );
        } else if (bossTaskStatus.equals(BossTaskStatus.FAIL)) {
            return String.format("【下载任务失败】：%s_%s<br/>" +
                            "· 任务类型：%s明细数据<br/>" +
                            "· 任务状态：任务失败<br/>" +
                            "请前往PC端CRM-下载中心查看"
                    , behavior.getDesc(), exportBatch, behavior.getDesc()
            );
        } else if (bossTaskStatus.equals(BossTaskStatus.NULL)) {
            return String.format("【下载任务结束】：%s_%s<br/>" +
                            "· 任务类型：%s明细数据<br/>" +
                            "· 任务状态：数据为空<br/>" +
                            "请前往PC端CRM-下载中心查看"
                    , behavior.getDesc(), exportBatch, behavior.getDesc()
            );
        }
        return Strings.EMPTY;
    }
}
