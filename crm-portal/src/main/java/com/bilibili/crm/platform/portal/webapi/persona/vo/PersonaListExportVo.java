package com.bilibili.crm.platform.portal.webapi.persona.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-03-20 10:45:27
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonaListExportVo implements Serializable {

    @ExcelResources(title = "id")
    private Integer id;

    @ExcelResources(title = "名称")
    private String name;

    @ExcelResources(title = "业绩归属部门")
    private String department;

    @ExcelResources(title = "业绩归属代理商")
    private String agent;

    @ExcelResources(title = "业绩归属销售")
    private String sale;

    @ExcelResources(title = "总收入")
    private BigDecimal income;

    @ExcelResources(title = "品牌产品收入")
    private BigDecimal brand_income;

    @ExcelResources(title = "效果产品收入")
    private BigDecimal effect_income;

    @ExcelResources(title = "UP主产品收入")
    private BigDecimal up_income;

}
