package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UpPeriodDayVo {
    @ApiModelProperty(notes = "日期")
    private Timestamp date;
    @ApiModelProperty(notes = "数据")
    private BigDecimal data;

    @ApiModelProperty("比例")
    private BigDecimal ratio;

    @ApiModelProperty("均值")
    private BigDecimal avg;

    @ApiModelProperty("均值比例")
    private BigDecimal avg_ratio;
}
