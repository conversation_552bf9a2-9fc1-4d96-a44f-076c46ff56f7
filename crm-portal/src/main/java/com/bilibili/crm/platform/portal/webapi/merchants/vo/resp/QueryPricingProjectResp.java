package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
public class QueryPricingProjectResp {

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目ID")
    private Integer projectId;

    @ApiModelProperty("项目流水ID")
    private Integer flowId;

    @ApiModelProperty("核价状态: 1-待审批 2-审批通过 3-审批拒绝 4-废弃")
    private Byte pricingStatus;

    @ApiModelProperty("资源库: 0-待创建 1-待审批 2-审批通过 3-审批拒绝 4-废弃")
    private Byte libraryStatus;

    @ApiModelProperty("资源库非标总净价")
    private Long netPrice;

    @ApiModelProperty("资源库非标总刊例价")
    private Long publishPrice;

    @ApiModelProperty("招商等级:值有S_ADD,S,A,B,TBD")
    private String investmentLevelType;

    @ApiModelProperty("项目属性名称")
    private String projectPropertyName;

    @ApiModelProperty("项目类型名称")
    private String projectTypeName;

    @ApiModelProperty("一级类目名称")
    private String projectFirstCategoryName;

    @ApiModelProperty("二级类目名称")
    private String projectSecondCategoryName;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("项目状态: 0 - 初始化, 1 - 编辑中, 2 - 审批中 3 - 审批通过 4 - 审批拒绝 5 - 审批撤回 6 - 已生效 7 - 已失效 8 - 业务失效 9 - 废弃")
    private Byte merchantsStatus;

    @ApiModelProperty("资源库工单id")
    private Long libraryWorkOrderId;

}
