package com.bilibili.crm.platform.portal.webapi.persona.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * @author: brady
 * @time: 2021/3/24 4:10 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PersonaCooperateTrendDateVo {
    @ApiModelProperty("日期")
    private String date;

    @ApiModelProperty("品牌收入")
    public BigDecimal brand_income;
    @ApiModelProperty("效果收入")
    public BigDecimal rtb_income;
    @ApiModelProperty("UP主收入")
    public BigDecimal up_income;
}
