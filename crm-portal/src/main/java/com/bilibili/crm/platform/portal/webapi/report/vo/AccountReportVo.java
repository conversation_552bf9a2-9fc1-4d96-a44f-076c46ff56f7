package com.bilibili.crm.platform.portal.webapi.report.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountReportVo {

    @ExcelResources(title = "账号id")
    private Integer account_id;

    @ExcelResources(title = "账号名称")
    private String user_name;

    @ExcelResources(title = "账户类型")
    private String account_type_desc;

    @ExcelResources(title = "所属客户类型")
    private String customer_category_desc;

    @ExcelResources(title = "所属客户id")
    private Integer customer_id;

    @ExcelResources(title = "所属客户名称")
    private String customer_name;

    @ExcelResources(title = "客户别名")
    private String customer_nick_name;

    @ExcelResources(title = "账号所属集团id")
    private Integer group_id;

    @ExcelResources(title = "账号所属集团")
    private String group_name;

    @ExcelResources(title = "账号所属产品线id")
    private Integer product_line_id;

    @ExcelResources(title = "账号所属产品线")
    private String product_line;

    @ExcelResources(title = "账号所属品牌id")
    private Integer product_id;

    @ExcelResources(title = "账号所属品牌")
    private String product;

    @ExcelResources(title = "主站ID")
    private Long mid;

    @ExcelResources(title = "蓝V MID")
    private Long blueMid;

    @ExcelResources(title = "账号状态")
    private String status_desc;

    /**
     * 账号权限
     */
    @ExcelResources(title = "效果广告状态展示")
    private String ad_status_desc;
    @ExcelResources(title = "gd广告系统状态展示")
    private String gd_status_desc;
    @ExcelResources(title = "游戏开放平台投放权限")
    private String is_support_game_desc;
    @ExcelResources(title = "内容推广投放权限")
    private String is_support_content_desc;

    @ExcelResources(title = "商业起飞投放权限")
    private String is_support_fly_desc;
    @ExcelResources(title = "UP主商单投放权限")
    private String is_support_pickup_desc;

    @ExcelResources(title = "电商开放平台投放权限")
    private String is_support_seller_desc;
    @ExcelResources(title = "DPA投放权限")
    private String is_support_dpa_desc;
    @ExcelResources(title = "互选投放权限")
    private String is_support_mas_desc;

    /**
     * 代理商账号类型
     */
    @ExcelResources(title = "代理商类型")
    private String agent_account_type_desc;

    /**
     * 代理商信息
     */
    @ExcelResources(title = "效果广告所属代理商")
    private String dependency_agent_name;

    @ExcelResources(title = "所属代理商账号id")
    private Integer dependency_agent_account_id;
    @ExcelResources(title = "所属代理商账号状态")
    private String dependency_agent_account_status_desc;
    @ExcelResources(title = "所属代理商客户id")
    private Integer dependency_agent_customer_id;
    @ExcelResources(title = "所属代理商客户名称")
    private String dependency_agent_customer_name;
    @ExcelResources(title = "所属代理商客户状态")
    private String dependency_agent_customer_status_desc;

    @ExcelResources(title = "所属部门")
    private String department_name;

    @ExcelResources(title = "是否内部账号")
    private String is_inner_desc;

    @ExcelResources(title = "一级行业分类")
    private String category_first_name;

    @ExcelResources(title = "二级行业分类")
    private String category_second_name;

    @ExcelResources(title = "一级行业分类")
    private String united_first_industry_name;

    @ExcelResources(title = "二级行业分类")
    private String united_second_industry_name;

    @ExcelResources(title = "三级行业分类")
    private String united_third_industry_name;

    @ExcelResources(title = "风险等级")
    private String risk_level_desc;

    @ExcelResources(title = "创建时间")
    private String ctime;

    @ExcelResources(title = "效果业绩归属渠道销售")
    @ApiModelProperty(notes = "效果业绩归属渠道销售")
    private String rtb_channel_manager_list;
    @ExcelResources(title = "商单业绩归属渠道销售")
    @ApiModelProperty(notes = "商单业绩归属渠道销售")
    private String brand_channel_manager_list;
    @ExcelResources(title = "合约业绩归属渠道销售")
    @ApiModelProperty(notes = "合约业绩归属渠道销售")
    private String contract_channel_manager_list;

    @ExcelResources(title = "客户域名")
    @ApiModelProperty(notes = "客户域名")
    private String customer_domain;

    @ExcelResources(title = "账号域名")
    @ApiModelProperty(notes = "账号域名")
    private String account_domain;

    @ExcelResources(title = "一级分类")
    private String first_industry_tag;

    @ExcelResources(title = "二级分类")
    private String second_industry_tag;

    @ExcelResources(title = "三级分类")
    private String third_industry_tag;
    @ExcelResources(title = "看数归属运营")
    private String authOperationNameList;
    @ExcelResources(title = "配置运营")
    private String configOperationNameList;
}
