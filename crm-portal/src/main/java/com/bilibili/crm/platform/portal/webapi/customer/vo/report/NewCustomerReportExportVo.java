package com.bilibili.crm.platform.portal.webapi.customer.vo.report;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/2/20 14:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class NewCustomerReportExportVo {

    @ExcelResources(title = "报备ID")
    private Long report_id;

    @ExcelResources(title = "客户类型")
    private String customer_type_desc;

    @ExcelResources(title = "客户主体名称")
    private String customer_name;

    @ExcelResources(title = "营业执照号码")
    private String business_license_code;

    @ExcelResources(title = "客户注册地址")
    private String customer_register_address;

    @ExcelResources(title = "代理商简称")
    private String agent_brief_name;

    @ExcelResources(title = "所属集团")
    private String belong_group_name;

    @ExcelResources(title = "所属bu")
    private String belong_bu_name;

    @ExcelResources(title = "所属一级业务行业")
    private String business_industry_first_name;

    @ExcelResources(title = "所属二级业务行业")
    private String business_industry_second_name;

    @ExcelResources(title = "一级行业名")
    private String united_first_industry_name;

    @ExcelResources(title = "二级行业名")
    private String united_second_industry_name;

    @ExcelResources(title = "三级行业名")
    private String united_third_industry_name;

    @ExcelResources(title = "代理商等级")
    private String agent_level;

    @ExcelResources(title = "代理商范围")
    private String agent_range;

    @ExcelResources(title = "客户联系人")
    private String contactor;

    @ExcelResources(title = "客户联系人职位")
    private String contactor_position;

    @ExcelResources(title = "客户联系人角色")
    private String contactor_role;

    @ExcelResources(title = "客户联系人部门")
    private String contactor_department;

    @ExcelResources(title = "客户联系人手机号区号")
    private String contactor_phone_code;

    @ExcelResources(title = "联系人电话")
    private String contactor_mobile_phone;

    @ExcelResources(title = "客户联系人座机")
    private String contactor_phone;

    @ExcelResources(title = "客户联系人微信")
    private String contactor_wechat;

    @ExcelResources(title = "客户联系人邮箱")
    private String contactor_email;

    @ExcelResources(title = "办公地址-省市区")
    private String address_city;

    @ExcelResources(title = "办公地址-详细地址")
    private String address_detail;

    @ExcelResources(title = "报备销售")
    private String sale_name;

    @ExcelResources(title = "报备销售组")
    private String sale_group_name;

    @ExcelResources(title = "跟进描述")
    private String report_follow_state_desc;

    @ExcelResources(title = "创建时间")
    private String create_time;

    @ExcelResources(title = "最新报备申请时间")
    private String apply_time;

    @ExcelResources(title = "审批通过时间")
    private String adopt_time;

    @ExcelResources(title = "报备生效时间")
    private String valid_time;

    @ExcelResources(title = "报备失效时间")
    private String invalid_time;

    @ExcelResources(title = "跟进记录")
    private String follow_record;
}
