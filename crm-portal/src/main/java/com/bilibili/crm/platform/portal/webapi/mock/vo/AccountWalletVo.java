package com.bilibili.crm.platform.portal.webapi.mock.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2020/6/18 14:32
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountWalletVo {

    /**
     * 帐号ID
     */
    private Integer account_id;

    /**
     * SystemType
     */
    private Integer system_type;

    /**
     * 业务唯一标识
     */
    private String business_id;

    /**
     * 充值金额（单位: 元）
     */
    private BigDecimal amount;
}
