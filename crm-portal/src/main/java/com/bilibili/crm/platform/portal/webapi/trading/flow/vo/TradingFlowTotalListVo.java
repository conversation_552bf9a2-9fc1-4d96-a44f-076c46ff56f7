package com.bilibili.crm.platform.portal.webapi.trading.flow.vo;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.platform.api.trading.flow.dto.AccountTradingFlowTotalDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/3/30
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class TradingFlowTotalListVo {
    private AccountTradingFlowTotalDto total;
    private Pagination<List<TradingFlowVo>> page_data;
}
