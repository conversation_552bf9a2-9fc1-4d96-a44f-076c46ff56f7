package com.bilibili.crm.platform.portal.webapi.industry.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2022/11/24 20:21
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DimensionDetailVo {

    @ApiModelProperty("品牌id")
    private String product_id;

    @ApiModelProperty("品牌名称")
    private String product_name;

    @ApiModelProperty("品牌线id")
    private String product_line_id;

    @ApiModelProperty("品牌线名称")
    private String product_line_name;

    @ApiModelProperty("预算")
    private String budget;

    @ApiModelProperty("消耗")
    private String consume;

    @ApiModelProperty("消耗占比")
    private String consume_prop;

    @ApiModelProperty("责任销售id")
    private String sale_id;

    @ApiModelProperty("责任销售")
    private String sale_name;

    @ApiModelProperty("集团id")
    private String group_id;

    @ApiModelProperty("集团名称")
    private String group_name;
}
