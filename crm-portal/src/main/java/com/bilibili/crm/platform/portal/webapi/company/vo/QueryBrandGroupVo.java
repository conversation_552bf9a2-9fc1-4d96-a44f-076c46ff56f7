package com.bilibili.crm.platform.portal.webapi.company.vo;

import com.bilibili.crm.platform.api.ka_belonging.enums.KaBelongingConfigLevelEnum;
import io.swagger.annotations.ApiModelProperty;
import io.swagger.annotations.ApiParam;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryBrandGroupVo implements Serializable {

    private static final long serialVersionUID = -1328829991386564487L;
    @ApiModelProperty("集团id")
    private Integer group_id;

    @ApiModelProperty("集团名称")
    private String group_name;

    @ApiModelProperty("品牌id")
    private List<Integer> brand_id;

    @ApiModelProperty("品牌名称")
    private String brand_name;

    @ApiModelProperty("配置层级(1:SKA, 2:KA，3:腰尾部_100w, 4:腰尾部_100w以下, 0:无标签 为空不关心")
    private Integer ka_level;

    @ApiModelProperty("KA生效开始时间")
    private Long ka_begin_time;

    @ApiModelProperty("KA生效结束时间")
    private Long ka_end_time;

    @ApiModelProperty(notes = "一级产品类目id")
    private Integer commerce_category_first_id;

    @ApiModelProperty(notes = "二级产品类目id")
    private Integer commerce_category_second_id;

    @ApiModelProperty("页码")
    private Integer page;

    @ApiModelProperty("页大小")
    private Integer size;

}
