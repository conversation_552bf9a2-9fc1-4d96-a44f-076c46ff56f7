package com.bilibili.crm.platform.portal.webapi.finance.vo.flow;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class PolicyFlowExportVo {

    @ExcelResources(title = "政策ID")
    private Integer policy_id;

    @ExcelResources(title = "流程ID")
    private String process_instance_id;

    @ExcelResources(title = "流程状态")
    private String status_name;

    @ExcelResources(title = "已关联合同号")
    private String contract_nos;

    @ExcelResources(title = "广告主账号ID")
    private Integer account_id;

    @ExcelResources(title = "广告主账号名称")
    private String account_name;

    @ExcelResources(title = "合同执行时间")
    private String cooperate_exec_time;
    @ExcelResources(title = "所属客户ID")
    private Integer customer_id;
    @ExcelResources(title = "所属客户")
    private String customer_name;
    @ExcelResources(title = "所属集团ID")
    private Integer group_id;
    @ExcelResources(title = "所属集团")
    private String group_name;
    @ExcelResources(title = "所属产品ID")
    private Integer product_id;
    @ExcelResources(title = "所属产品")
    private String product_name;

    @ExcelResources(title = "下单方式")
    private String is_direct_sign_desc_list;

    @ExcelResources(title = "代理商账户名称1")
    private String agent_names1;
    @ExcelResources(title = "直客销售1")
    private String direct_sale_names1;
    @ExcelResources(title = "渠道销售1")
    private String channel_sale_names1;
    @ExcelResources(title = "执行媒介1")
    private String exec_mediums1;
    @ExcelResources(title = "硬广收入1(元)")
    private BigDecimal hard_ad_income1;
    @ExcelResources(title = "非标收入1(元)")
    private BigDecimal not_stand_income1;
    @ExcelResources(title ="直播硬广收入1(元)")
    private BigDecimal live_hard_income1;
    @ExcelResources(title = "花火商单收入1(元)")
    private BigDecimal pickup_commercial_income1;
    @ExcelResources(title = "直播商单收入1(元)")
    private BigDecimal live_broadcast_commercial_income1;
    @ExcelResources(title ="预付费商单收入1(元)")
    private BigDecimal pre_pickup_income1;

    @ExcelResources(title = "代理商账户名称2")
    private String agent_names2;
    @ExcelResources(title = "直客销售2")
    private String direct_sale_names2;
    @ExcelResources(title = "渠道销售2")
    private String channel_sale_names2;
    @ExcelResources(title = "执行媒介2")
    private String exec_mediums2;
    @ExcelResources(title = "硬广收入2(元)")
    private BigDecimal hard_ad_income2;
    @ExcelResources(title = "非标收入2(元)")
    private BigDecimal not_stand_income2;
    @ExcelResources(title ="直播硬广收入2(元)")
    private BigDecimal live_hard_income2;
    @ExcelResources(title = "花火商单收入2(元)")
    private BigDecimal pickup_commercial_income2;
    @ExcelResources(title = "直播商单收入2(元)")
    private BigDecimal live_broadcast_commercial_income2;
    @ExcelResources(title ="预付费商单收入2(元)")
    private BigDecimal pre_pickup_income2;

    @ExcelResources(title = "直客销售")
    private String direct_sale_names3;
    @ExcelResources(title = "执行媒介")
    private String exec_mediums3;
    @ExcelResources(title = "硬广收入(元)")
    private BigDecimal hard_ad_income3;
    @ExcelResources(title = "非标收入(元)")
    private BigDecimal not_stand_income3;
    @ExcelResources(title ="直播硬广收入(元)")
    private BigDecimal live_hard_income3;
    @ExcelResources(title = "花火商单收入(元)")
    private BigDecimal pickup_commercial_income3;
    @ExcelResources(title = "直播商单收入(元)")
    private BigDecimal live_broadcast_commercial_income3;
    @ExcelResources(title ="预付费商单收入(元)")
    private BigDecimal pre_pickup_income3;

    @ExcelResources(title = "合同总收入(元)")
    private BigDecimal contract_total_income;

    @ExcelResources(title = "IP招商合作项目")
    private String ip_invest_project;

    @ExcelResources(title = "硬广政策-常规点位折扣比例")
    private BigDecimal general_point_discount_ratio;

    @ApiModelProperty("硬广政策特殊点位折扣")
    @ExcelResources(title = "硬广政策特殊点位折扣")
    private String special_point_discounts;

    @ApiModelProperty("硬广政策-配送比例")
    @ExcelResources(title = "硬广政策-配送比例")
    private BigDecimal distribution_ratio;

//    @ExcelResources(title = "当前处理人")
//    private String assignee;
    @ExcelResources(title = "提交人")
    private String creator;
    @ExcelResources(title = "提交时间")
    private String ctime;
    @ExcelResources(title = "备注")
    private String remark;
    @ExcelResources(title = "流程结束时间")
    private String flowEndTime;
    @ExcelResources(title = "促销政策")
    private String promotion_policy_types;

}
