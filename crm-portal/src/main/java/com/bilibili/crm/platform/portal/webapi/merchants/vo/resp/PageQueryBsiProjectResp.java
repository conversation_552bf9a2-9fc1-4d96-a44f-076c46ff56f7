package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.bilibili.crm.common.json.serializer.LocalDate2LongSerializer;
import com.bilibili.crm.common.json.serializer.LocalDateTime2LongSerializer;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/12/15
 */
@ExcelIgnoreUnannotated
@Data
public class PageQueryBsiProjectResp {

    @ApiModelProperty("项目id")
    private Integer id;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目属性ID")
    private Integer projectPropertyId;

    @ApiModelProperty("项目类型ID")
    private Integer projectTypeId;

    @ApiModelProperty("一级类目ID")
    private Integer projectFirstCategoryId;

    @ApiModelProperty("二级类目ID")
    private Integer projectSecondCategoryId;

    @ApiModelProperty("项目属性名称")
    private String projectPropertyName;

    @ApiModelProperty("项目类型名称")
    private String projectTypeName;

    @ApiModelProperty("一级类目名称")
    private String projectFirstCategoryName;

    @ApiModelProperty("二级类目名称")
    private String projectSecondCategoryName;

    @ApiModelProperty("常规/定制:1-常规, 2-定制")
    private Byte projectGeneralType;

    @ApiModelProperty("营销团队")
    private Byte merchantMarketingTeamId;

    @ApiModelProperty("创作类型:1-非共创, 2-共创")
    private Byte projectCreateType;

    @ApiModelProperty("归属一级分区ID")
    private Integer projectFirstAreaId;

    @ApiModelProperty("归属二级分区ID")
    private Integer projectSecondAreaId;

    @ApiModelProperty("归属一级分区名称")
    private String projectFirstAreaName;

    @ApiModelProperty("归属二级分区名称")
    private String projectSecondAreaName;

    @ApiModelProperty("招商周期是否长期有效: 0-否, 1-是")
    private Integer investmentLongTimeFlag;

    @ApiModelProperty("招商周期开始日期")
    @JsonSerialize(using = LocalDateTime2LongSerializer.class)
    private LocalDateTime investmentBeginTime;

    @ApiModelProperty("招商周期结束日期")
    @JsonSerialize(using = LocalDateTime2LongSerializer.class)
    private LocalDateTime investmentEndTime;

    @ApiModelProperty("上线周期是否长期有效: 0-否, 1-是")
    private Integer validLongTimeFlag;

    @ApiModelProperty("执行周期开始日期")
    @JsonSerialize(using = LocalDate2LongSerializer.class)
    private LocalDate validBeginDay;

    @ApiModelProperty("执行周期结束日期")
    @JsonSerialize(using = LocalDate2LongSerializer.class)
    private LocalDate validEndDay;

    @ApiModelProperty("商业对接人")
    private String businessContact;

    @ApiModelProperty("主站对接人")
    private String mainContact;

    @ApiModelProperty("提报商机总数")
    private Integer bsiCount;

    @ApiModelProperty("提报商机总金额")
    private Long bsiAmount;

    @ApiModelProperty("已创建合同的商机总数")
    private Integer contractBsiCount;

    @ApiModelProperty("已创建合同的商机总额")
    private Long contractBsiAmount;

    @ApiModelProperty("已计收的商机总数")
    private Integer completedBsiCount;

    @ApiModelProperty("已计收的商机总金额")
    private Long completedBsiAmount;
}
