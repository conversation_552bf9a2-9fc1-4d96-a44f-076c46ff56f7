package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchievementConsumeQuotaVo {
    // 总消耗
    private BigDecimal total_consume;

    // 现金消耗
    private BigDecimal cash_consume;

    // 返货消耗
    private BigDecimal red_packet_consumer;

    // 专项返货消耗
    private BigDecimal special_red_packet_consumer;

    // 所有返货消耗（普通返货+专项返货）
    private BigDecimal all_red_packet_consumer;
    // 个人起飞扶持消耗
    private BigDecimal fly_support_consume;

    @ApiModelProperty(name = "花火待完成")
    private BigDecimal pick_up_undone;

    @ApiModelProperty(name = "花火已支付&待审核")
    private BigDecimal pick_up_done_unaudit;

    @ApiModelProperty(name = "花火待支付")
    private BigDecimal pick_ip_unpaid;

    @ApiModelProperty(notes = "占比")
    private BigDecimal ratio;
}
