package com.bilibili.crm.platform.portal.webapi.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;

/**
 * 附件通用上传 vo
 *
 * <AUTHOR>
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CommonAttachmentUploadResultVo {

    @ApiModelProperty(notes = "上传方式: 1-bfs 2-oss 3-flowable 流程上传文件")
    private Integer upload_type;

    @ApiModelProperty(notes = "flowable 流程上传方式提供")
    private String flow_attachment_id;

    @ApiModelProperty(notes = "oss key，oss方式提供")
    private String oss_key;

    @ApiModelProperty(notes = "文件名，oss方式提供")
    private String file_name;

    @ApiModelProperty(notes = "url")
    private String url;

    @ApiModelProperty(notes = "fullUrl(如果是bfs自动拼接了token)")
    private String full_url;

    @ApiModelProperty(notes = "token，bfs方式提供")
    private String token;

    @ApiModelProperty(notes = "url，bfs方式提供")
    private String hash;

    @ApiModelProperty("修改时间")
    private Timestamp mtime;

    @ApiModelProperty("附件ID")
    private Integer id;

}
