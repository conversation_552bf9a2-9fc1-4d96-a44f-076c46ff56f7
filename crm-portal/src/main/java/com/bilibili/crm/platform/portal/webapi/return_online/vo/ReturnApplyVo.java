package com.bilibili.crm.platform.portal.webapi.return_online.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnApplyVo {

    /**
     * id
     */
    @ApiModelProperty("返货申请id")
    private Integer id;

    /**
     * 名称
     */
    @ApiModelProperty("返货申请名称")
    private String name;

    /**
     * 流程实例id
     */
    @ApiModelProperty("流程实例id")
    private String process_instance_id;

    /**
     * 政策流程状态: 0-政策申请节点建好，1-审核驳回(表单填写中，打回来重新填)，2-预审审核中，3-加签审核中，4-审核完成，5-已归档，-1-已作废
     */
    @ApiModelProperty("政策流程状态")
    private Integer flow_status;
    @ApiModelProperty("政策流程状态")
    private String flow_status_desc;

    /**
     * 执行状态 0-待执行，1-执行中，2-执行完成，-1-执行失败
     */
    @ApiModelProperty("执行状态")
    private Integer exe_status;
    @ApiModelProperty("执行状态")
    private String exe_status_desc;

    /**
     * 返货归属时间
     */
    @ApiModelProperty("返货归属时间")
    private Timestamp belong_date;

    /**
     * 归属日期类型 1-月度，2-季度
     */
    @ApiModelProperty("归属日期类型 1-月度，2-季度")
    private Integer belong_date_type;

    /**
     * 任务完成时间
     */
    @ApiModelProperty("任务完成时间")
    private Timestamp task_complete_time;


    /**
     * 备注
     */
    @ApiModelProperty("备注")
    private String remark;

    /**
     * 创建时间
     */
    @ApiModelProperty("创建时间")
    private Timestamp ctime;

    /**
     * creator
     */
    @ApiModelProperty("创建人")
    private String creator;

    /**
     * 提交审核人
     */
    @ApiModelProperty("提交审核人")
    private String submit_flow_user;

    /**
     * 提交审核时间
     */
    @ApiModelProperty("提交审核时间")
    private Timestamp submit_flow_time;

    /**
     * 人工校准返货额度(单位:元)
     */
    @ApiModelProperty("人工校准返货额度(单位:元)")
    private BigDecimal manual_correct_amount;

    /**
     * 系统计算返货额度(单位:元)
     */
    @ApiModelProperty("系统计算返货额度(单位:元)")
    private BigDecimal system_cal_return_amount;

    @ApiModelProperty("当前节点处理人")
    private String assignee;

    private static final long serialVersionUID = 1L;
}
