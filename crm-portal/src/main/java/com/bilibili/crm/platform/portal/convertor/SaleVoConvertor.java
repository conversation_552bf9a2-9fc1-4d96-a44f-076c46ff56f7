package com.bilibili.crm.platform.portal.convertor;

import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.portal.webapi.sale.vo.SaleDropBoxVo;

/**
 * <AUTHOR>
 * @date 2021/8/27 下午12:01
 */
public class SaleVoConvertor {

    public static SaleDropBoxVo saleDto2DropBox(SaleDto dto) {
        return SaleDropBoxVo.builder()
                .sale_id(dto.getId())
                .sale_name(dto.getName())
                .group(dto.getGroupName())
                .leader(dto.getLeaderName())
                .nickname(dto.getNickName())
                .build();
    }
}
