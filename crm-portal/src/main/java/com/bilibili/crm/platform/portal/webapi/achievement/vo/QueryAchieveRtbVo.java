package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;
import lombok.experimental.SuperBuilder;

import java.util.List;

/**
 * @description:
 * @author: brady
 * @time: 2020/10/22 2:31 下午
 */
@SuperBuilder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryAchieveRtbVo extends QueryAchieveBoardVo {

    @ApiModelProperty("代理商")
    private List<Integer> agent_ids;

    @ApiModelProperty("代理商客户ids")
    private List<Integer> agent_customer_ids;

    @ApiModelProperty("广告主")
    private List<Integer> account_ids;

    @ApiModelProperty("集团ids")
    private List<Integer> group_ids;

    @ApiModelProperty("品牌/产品ids")
    private List<Integer> product_ids;

    @ApiModelProperty("一级行业分类")
    private List<Integer> category_first_ids;

    @ApiModelProperty("二级行业分类")
    private List<Integer> category_second_ids;

    @ApiModelProperty("业务一级行业分类")
    private List<Integer> biz_industry_category_first_ids;

    @ApiModelProperty("业务二级行业分类")
    private List<Integer> biz_industry_category_second_ids;

    @ApiModelProperty("资源平台")
    private List<Integer> platform_ids;

    @ApiModelProperty("资源位类型")
    private List<Integer> resource_ids;

    @ApiModelProperty("排序字段")
    private Integer cal_sort;

    @ApiModelProperty("排序 0 降序, 1 升序")
    private Integer sort_order;

    @ApiModelProperty("维度 0 代理商, 1 品牌")
    private Integer aggregate_type;

    @ApiModelProperty("其实是花火订单编号No")
    private Long pick_order_id;

    @ApiModelProperty("合伙人任务Id")
    private Long task_id;

    @ApiModelProperty("客户ID")
    private List<Integer> customer_ids;

    @ApiModelProperty("订单类型 0 花火订单 1 邀约-播放器下 2 邀约-弹幕 3 邀约-浮层")
    private List<Integer> order_types;

    @ApiModelProperty("花火订单完成开始时间")
    private Long pickup_date_begin;

    @ApiModelProperty("花火订单完成结束时间")
    private Long pickup_date_end;

    @ApiModelProperty("聚合字段 teamLevel1 teamLevel2 teamLevel3 teamLevel4  saleId")
    private String team_kpi_agg_field;

    @ApiModelProperty("计收开始时间")
    private Long check_date_begin;

    @ApiModelProperty("计收结束时间")
    private Long check_date_end;

    @ApiModelProperty("统一账户一级行业")
    private List<Integer> acc_united_first_industry_ids;

    @ApiModelProperty("统一账户二级行业")
    private List<Integer> acc_united_second_industry_ids;

    @ApiModelProperty("统一账户三级行业")
    private List<Integer> acc_united_third_industry_ids;

    @ApiModelProperty("统一客户一级行业")
    private List<Integer> customer_united_first_industry_ids;

    @ApiModelProperty("统一客户二级行业")
    private List<Integer> customer_united_second_industry_ids;

    @ApiModelProperty("统一客户三级行业")
    private List<Integer> customer_united_third_industry_ids;

    private String migrateBizScene;

    private Integer query_detail_list_type;
}
