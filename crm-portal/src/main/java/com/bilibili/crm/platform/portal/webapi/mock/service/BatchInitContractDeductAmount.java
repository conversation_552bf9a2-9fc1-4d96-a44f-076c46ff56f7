package com.bilibili.crm.platform.portal.webapi.mock.service;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/3/2 2:34 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BatchInitContractDeductAmount {

    /**
     * 合同初始化抵扣金额列表
     */
    private List<ContractInitDeductAmount> initDeductAmounts;

    /**
     * 可以刷的合同号列表
     */
    private List<Long> canRefreshContractNos;

    private String fileName;
}
