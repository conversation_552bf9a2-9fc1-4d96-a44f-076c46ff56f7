package com.bilibili.crm.platform.portal.webapi.wxapp.service;

import com.bilibili.crm.platform.biz.po.CmcGoodsDayStatReportPo;
import com.bilibili.crm.platform.biz.repo.wx_daily.CmcGoodsDayStatReportRepo;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.portal.webapi.wxapp.sales_good.vo.DailyCollectViewVo;
import com.bilibili.crm.platform.portal.webapi.wxapp.sales_good.vo.GmvWithSourceViewVo;
import com.bilibili.crm.platform.portal.webapi.wxapp.sales_good.vo.QuarterGmvViewVo;
import com.bilibili.crm.platform.portal.webapi.wxapp.sales_good.vo.QuarterIncomeSellGoodsVo;
import com.bilibili.crm.platform.soa.dto.wxapp.TrendDateIncomeDto;
import com.bilibili.utils.TimeUtil;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.math.BigInteger;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WxAppDailyReportService {

    @Autowired
    private CmcGoodsDayStatReportRepo cmcGoodsDayStatReportRepo;

    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Autowired
    private IncomeUtil incomeUtil;

    public GmvWithSourceViewVo queryGmvWithSourceView(Timestamp timestamp) {
        CmcGoodsDayStatReportPo cmcGoodsDayStatReportPo =
                cmcGoodsDayStatReportRepo.queryByAssignDate(timestamp);
        if (cmcGoodsDayStatReportPo == null) {
            log.warn("queryGmvWithSourceView break ! data not finish timestamp {}", timestamp);
            return null;
        }
        Timestamp lastWeekTs =
                incomeTimeUtil.getLastWeekTime(timestamp);
        Timestamp yesterday = incomeTimeUtil.getYesterdayBegin(timestamp);

        BigDecimal averagePrice = incomeUtil.divide(cmcGoodsDayStatReportPo.getGmv()
                , BigDecimal.valueOf(cmcGoodsDayStatReportPo.getOrderCnt()),2);
        CmcGoodsDayStatReportPo yesterdayReportPo = cmcGoodsDayStatReportRepo.queryByAssignDate(yesterday);
        yesterdayReportPo = yesterdayReportPo == null ? getZero(timestamp) : yesterdayReportPo;

        BigDecimal yesterdayAveragePrice = incomeUtil.divide(yesterdayReportPo.getGmv()
                , BigDecimal.valueOf(yesterdayReportPo.getOrderCnt()), 2);
        // 上周数据
        CmcGoodsDayStatReportPo lastWeekReportPo = cmcGoodsDayStatReportRepo.queryByAssignDate(lastWeekTs);
        lastWeekReportPo = lastWeekReportPo == null ? getZero(timestamp) : lastWeekReportPo;

        BigDecimal lastWeekAveragePrice = incomeUtil.divide(lastWeekReportPo.getGmv()
                , BigDecimal.valueOf(lastWeekReportPo.getOrderCnt()), 2);

        if (cmcGoodsDayStatReportPo == null) {
            return null;
        }
        GmvWithSourceViewVo gmvWithSourceViewVo =
                GmvWithSourceViewVo.builder()
                        .gmv(cmcGoodsDayStatReportPo.getGmv())
                        .gmv_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getGmv()
                                , yesterdayReportPo.getGmv()))
                        .gmv_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getGmv()
                                , lastWeekReportPo.getGmv()))
                        .live_gmv(cmcGoodsDayStatReportPo.getLiveGmv())
                        .live_gmv_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getLiveGmv()
                                , yesterdayReportPo.getLiveGmv()))
                        .live_gmv_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getLiveGmv()
                                , lastWeekReportPo.getLiveGmv()))
                        .video_gmv(cmcGoodsDayStatReportPo.getVideoGmv())
                        .video_gmv_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getVideoGmv()
                                , yesterdayReportPo.getVideoGmv()))
                        .video_gmv_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getVideoGmv()
                                , lastWeekReportPo.getVideoGmv()))
                        .jd_gmv(cmcGoodsDayStatReportPo.getJdGmv())
                        .jd_gmv_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getJdGmv()
                                , yesterdayReportPo.getJdGmv()))
                        .jd_gmv_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getJdGmv()
                                , lastWeekReportPo.getJdGmv()))
                        .taobao_gmv(cmcGoodsDayStatReportPo.getTaobaoGmv())
                        .taobao_gmv_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getTaobaoGmv()
                                , yesterdayReportPo.getTaobaoGmv()))
                        .taobao_gmv_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getTaobaoGmv()
                                , lastWeekReportPo.getTaobaoGmv()))
                        .pdd_gmv(cmcGoodsDayStatReportPo.getPddGmv())
                        .pdd_gmv_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getPddGmv()
                                , yesterdayReportPo.getPddGmv()))
                        .pdd_gmv_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getPddGmv()
                                , lastWeekReportPo.getPddGmv()))
                        .order_cnt(cmcGoodsDayStatReportPo.getOrderCnt())
                        .yesterday_order_cnt(yesterdayReportPo.getOrderCnt())
                        .last_week_order_cnt(lastWeekReportPo.getOrderCnt())
                        .order_cnt_day_ratio(getCompareRate(cmcGoodsDayStatReportPo.getOrderCnt()
                                , yesterdayReportPo.getOrderCnt()))
                        .order_cnt_week_ratio(getCompareRate(cmcGoodsDayStatReportPo.getOrderCnt()
                                , lastWeekReportPo.getOrderCnt()))
                        .average_price(averagePrice)
                        .yesterday_average_price(yesterdayAveragePrice)
                        .last_week_average_price(lastWeekAveragePrice)
                        .average_price_day_ratio(getCompareRate(averagePrice
                                , yesterdayAveragePrice))
                        .average_price_week_ratio(getCompareRate(averagePrice
                                , lastWeekAveragePrice))
                        .build();

        Timestamp beforeThirtyTs =
                incomeTimeUtil.getPeriodStartTimeBeforeToday(29, timestamp);
        List<CmcGoodsDayStatReportPo> cmcGoodsDayStatReportPos
                = cmcGoodsDayStatReportRepo.selectGmvTrend(
                        beforeThirtyTs, timestamp);

        Map<Timestamp, CmcGoodsDayStatReportPo> cmcGoodsDayStatReportPoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(cmcGoodsDayStatReportPos)) {
            cmcGoodsDayStatReportPoMap = cmcGoodsDayStatReportPos.stream()
                    .collect(Collectors.toMap(e -> e.getStatDay()
                            , e -> e, (v1, v2) -> v1));
        }

        List<BigDecimal> taobaoGmvTrend = new ArrayList<>();

        List<BigDecimal> jdGmvTrend = new ArrayList<>();

        List<BigDecimal> pddGmvTrend = new ArrayList<>();

        List<BigDecimal> liveGmvTrend = new ArrayList<>();

        List<BigDecimal> gmvTrend = new ArrayList<>();

        List<BigDecimal> videoGmvTrend = new ArrayList<>();

        for (int i = 29; i >= 0; i--) {
            Timestamp tempTs =
                    incomeTimeUtil.getPeriodStartTimeBeforeToday(i, timestamp);
            CmcGoodsDayStatReportPo cmcGoodsDayPo
                    = cmcGoodsDayStatReportPoMap.get(tempTs);
            if (cmcGoodsDayPo == null) {
                gmvTrend.add(new BigDecimal(BigInteger.ZERO));
                liveGmvTrend.add(new BigDecimal(BigInteger.ZERO));
                videoGmvTrend.add(new BigDecimal(BigInteger.ZERO));
                jdGmvTrend.add(new BigDecimal(BigInteger.ZERO));
                taobaoGmvTrend.add(new BigDecimal(BigInteger.ZERO));
                pddGmvTrend.add(new BigDecimal(BigInteger.ZERO));
            } else {
                gmvTrend.add(cmcGoodsDayPo.getGmv());
                liveGmvTrend.add(cmcGoodsDayPo.getLiveGmv());
                videoGmvTrend.add(cmcGoodsDayPo.getVideoGmv());
                jdGmvTrend.add(cmcGoodsDayPo.getJdGmv());
                taobaoGmvTrend.add(cmcGoodsDayPo.getTaobaoGmv());
                pddGmvTrend.add(cmcGoodsDayPo.getPddGmv());
            }
        }
        gmvWithSourceViewVo.setTaobaoGmvTrend(taobaoGmvTrend);
        gmvWithSourceViewVo.setJdGmvTrend(jdGmvTrend);
        gmvWithSourceViewVo.setPddGmvTrend(pddGmvTrend);
        gmvWithSourceViewVo.setLiveGmvTrend(liveGmvTrend);
        gmvWithSourceViewVo.setGmvTrend(gmvTrend);
        gmvWithSourceViewVo.setVideoGmvTrend(videoGmvTrend);
        return gmvWithSourceViewVo;
    }


    public DailyCollectViewVo queryDailyCollectViewVo(Timestamp timestamp) {
        // 上周当天
        Timestamp lastWeekTs =
                incomeTimeUtil.getLastWeekTime(timestamp);

        Timestamp yesterday = incomeTimeUtil.getYesterdayBegin(timestamp);

        // 当天
        CmcGoodsDayStatReportPo currentWeekReportPo = cmcGoodsDayStatReportRepo.queryByAssignDate(timestamp);
        // 当天数据没出来
        if (currentWeekReportPo == null) {
            log.warn("queryDailyCollectViewVo break ! data not finish timestamp {}", timestamp);
            return null;
        }
        BigDecimal currentWeekUserPermeability = incomeUtil.divide(currentWeekReportPo.getTakeGoodsCallupUserNum()
                , currentWeekReportPo.getAppDau(), 4);

        // 昨天
        CmcGoodsDayStatReportPo yesterdayReportPo = cmcGoodsDayStatReportRepo.queryByAssignDate(yesterday);
        yesterdayReportPo = yesterdayReportPo == null ? getZero(timestamp) : yesterdayReportPo;

        BigDecimal yesterdayUserPermeability = incomeUtil.divide(yesterdayReportPo.getTakeGoodsCallupUserNum()
                , yesterdayReportPo.getAppDau(), 4);

        // 上周数据
        CmcGoodsDayStatReportPo lastWeekReportPo = cmcGoodsDayStatReportRepo.queryByAssignDate(lastWeekTs);
        lastWeekReportPo = lastWeekReportPo == null ? getZero(timestamp) : lastWeekReportPo;

        BigDecimal lastWeekUserPermeability = incomeUtil.divide(lastWeekReportPo.getTakeGoodsCallupUserNum()
                , lastWeekReportPo.getAppDau(), 4);


        DailyCollectViewVo dailyCollectViewVo = DailyCollectViewVo.builder()
                .gmv(currentWeekReportPo.getGmv())
                .yesterday_gmv(yesterdayReportPo.getGmv())
                .last_week_gmv(lastWeekReportPo.getGmv())
                .gmv_day_ratio(getCompareRate(currentWeekReportPo.getGmv(), yesterdayReportPo.getGmv()))
                .gmv_week_ratio(getCompareRate(currentWeekReportPo.getGmv(), lastWeekReportPo.getGmv()))
                .up_num(currentWeekReportPo.getTakeGoodsUpNum())
                .yesterday_up_num(yesterdayReportPo.getTakeGoodsUpNum())
                .last_week_up_num(lastWeekReportPo.getTakeGoodsUpNum())
                .up_num_day_ratio(getCompareRate(currentWeekReportPo.getTakeGoodsUpNum(), yesterdayReportPo.getTakeGoodsUpNum()))
                .up_num_week_ratio(getCompareRate(currentWeekReportPo.getTakeGoodsUpNum(), lastWeekReportPo.getTakeGoodsUpNum()))
                .user_permeability(currentWeekUserPermeability)
                .yesterday_user_permeability(yesterdayUserPermeability)
                .last_week_user_permeability(lastWeekUserPermeability)
                .user_permeability_day_ratio(getCompareRate(currentWeekUserPermeability, yesterdayUserPermeability))
                .user_permeability_week_ratio(getCompareRate(currentWeekUserPermeability, lastWeekUserPermeability))
//                .cost()
//                .cost_day_ratio()
//                .cost_week_ratio()
                .build();
        return dailyCollectViewVo;
    }


    public QuarterGmvViewVo queryQuarterGmvViewVo(Timestamp timestamp) {
        QuarterGmvViewVo quarterGmvViewVo = new QuarterGmvViewVo();

        CmcGoodsDayStatReportPo cmcGoodsDayStatReportPo =
                cmcGoodsDayStatReportRepo.queryByAssignDate(timestamp);
        if (cmcGoodsDayStatReportPo == null) {
            log.warn("queryQuarterGmvViewVo break ! data not finish timestamp {}", timestamp);
            return null;
        }
        // 季度开始
        Timestamp beginQuarter = incomeTimeUtil.getQuarterFirstDate(timestamp);
        //上个季度同期
        long gap = timestamp.getTime() - beginQuarter.getTime();
        long lastQuarterBegin = incomeTimeUtil.getStartOrEndDayOfQuarter(beginQuarter, -1, true);
        // 上季同期
        Timestamp lastQuarterSame = new Timestamp(lastQuarterBegin + gap);

        long lastQuarterEnd = incomeTimeUtil.getStartOrEndDayOfQuarter(new Timestamp(lastQuarterBegin),
                0, false);
        // 加上 gap 超出上个q 最后一天
        Timestamp realLastQuarter = lastQuarterSame.getTime() > lastQuarterEnd
                ? new Timestamp(lastQuarterEnd) : lastQuarterSame;
        CmcGoodsDayStatReportPo lastQuarterStatReportPo =
                cmcGoodsDayStatReportRepo.queryByAssignDate(realLastQuarter);
        lastQuarterStatReportPo = lastQuarterStatReportPo == null ? getZero(timestamp) : lastQuarterStatReportPo;


        // 环比（%）=(当q总gmv-上个q总gmv)/上个q总gmv*100
        BigDecimal quarterCompareRate =
                getCompareRate(cmcGoodsDayStatReportPo.getQuarterGmv()
                        , lastQuarterStatReportPo.getQuarterGmv()
                );
        BigDecimal yearquarterCompareRate =
                getCompareRate(cmcGoodsDayStatReportPo.getQuarterGmv()
                        , cmcGoodsDayStatReportPo.getLastYearQuarterGmv());
        quarterGmvViewVo.setQuarter_gmv(cmcGoodsDayStatReportPo.getQuarterGmv());
        quarterGmvViewVo.setGmv_quarter_ratio(quarterCompareRate);
        quarterGmvViewVo.setGmv_year_ratio(yearquarterCompareRate);
        return quarterGmvViewVo;
    }


    /**
     * 填充消耗信息
     */
    public void fillCostInfo(QuarterIncomeSellGoodsVo quarterIncomeSellGoodsVo
            , Timestamp timestamp) {

        CmcGoodsDayStatReportPo cmcGoodsDayStatReportPo =
                cmcGoodsDayStatReportRepo.queryByAssignDate(timestamp);
        if (cmcGoodsDayStatReportPo == null) {
            log.warn("fillCostInfo break ! data not finish timestamp {}", timestamp);
            return;
        }
        // 查询直播消耗趋势
        List<TrendDateIncomeDto> liveHistoryIncome = queryLiveCostTrend(timestamp);
        if (CollectionUtils.isEmpty(liveHistoryIncome)
                || CollectionUtils.isEmpty(quarterIncomeSellGoodsVo.getHistory_income())) {
            return;
        }

        // 总消耗
        List<TrendDateIncomeDto> historyIncome = quarterIncomeSellGoodsVo.getHistory_income();
        Map<Timestamp, BigDecimal> historyIncomeMap = historyIncome.stream()
                .collect(Collectors.toMap(e -> e.getDate(), e -> e.getIncome()
                        , (v1, v2) -> v1));

        List<TrendDateIncomeDto> videoHistoryIncome = new ArrayList<>();

        TrendDateIncomeDto yesterdayData = null;
        TrendDateIncomeDto currentData = null;
        TrendDateIncomeDto lastWeekData = null;
        Timestamp yesterday = incomeTimeUtil.getYesterdayBegin(timestamp);
        Timestamp lastWeekTs = incomeTimeUtil.getLastWeekTime(timestamp);
        if (!CollectionUtils.isEmpty(liveHistoryIncome)) {
            for (TrendDateIncomeDto trendDateIncomeDto : liveHistoryIncome) {
                if (trendDateIncomeDto.getDate().equals(TimeUtil.getBeginOfDay(timestamp))) {
                    currentData = trendDateIncomeDto;
                } else if (trendDateIncomeDto.getDate().equals(yesterday)) {
                    yesterdayData = trendDateIncomeDto;
                } else if (trendDateIncomeDto.getDate().equals(lastWeekTs)) {
                    lastWeekData = trendDateIncomeDto;
                }
                if (historyIncomeMap.get(trendDateIncomeDto.getDate()) != null) {
                    BigDecimal totalIncome = historyIncomeMap.get(trendDateIncomeDto.getDate());
                    TrendDateIncomeDto videoIncome =
                            TrendDateIncomeDto.builder().date(trendDateIncomeDto.getDate())
                                    .income(totalIncome.subtract(trendDateIncomeDto.getIncome())).build();
                    videoHistoryIncome.add(videoIncome);
                }else{
                    TrendDateIncomeDto videoIncome =
                            TrendDateIncomeDto.builder().date(trendDateIncomeDto.getDate())
                                    .income(BigDecimal.ZERO).build();
                    videoHistoryIncome.add(videoIncome);
                }
            }
        }

        // 防止npe
        TrendDateIncomeDto zeroTrendDateIncomeDto = TrendDateIncomeDto
                .builder().income(new BigDecimal(BigInteger.ZERO)).build();
        yesterdayData = yesterdayData == null ? zeroTrendDateIncomeDto : yesterdayData;
        lastWeekData = lastWeekData == null ? zeroTrendDateIncomeDto : lastWeekData;

        if (!CollectionUtils.isEmpty(liveHistoryIncome)) {
            List<BigDecimal> live_history_income = liveHistoryIncome.stream()
                    .sorted(Comparator.comparing(TrendDateIncomeDto::getDate))
                    .map(e -> e.getIncome())
                    .collect(Collectors.toList());
            quarterIncomeSellGoodsVo.setLive_history_income(live_history_income);
        }
        if (!CollectionUtils.isEmpty(videoHistoryIncome)) {
            List<BigDecimal> video_history_income = videoHistoryIncome.stream()
                    .sorted(Comparator.comparing(TrendDateIncomeDto::getDate))
                    .map(e -> e.getIncome())
                    .collect(Collectors.toList());
            quarterIncomeSellGoodsVo.setVideo_history_income(video_history_income);
        }

        // 取当天的数据
        if (currentData != null) {
            // 日消耗
            BigDecimal sellGoodsIncome = quarterIncomeSellGoodsVo.getSell_goods_income();
            quarterIncomeSellGoodsVo.setLive_income(currentData.getIncome());
            quarterIncomeSellGoodsVo.setVideo_income(sellGoodsIncome
                    .subtract(currentData.getIncome()));

            // 直播环同比
            quarterIncomeSellGoodsVo.setLive_income_day_ratio(
                    getCompareRate(currentData.getIncome(), yesterdayData.getIncome()));
            quarterIncomeSellGoodsVo.setLive_income_week_yoy_ratio(
                    getCompareRate(currentData.getIncome(), lastWeekData.getIncome()));

            // 历史收入
            BigDecimal yesterdayTotal = historyIncomeMap.get(yesterday);
            BigDecimal lastWeekTotal = historyIncomeMap.get(lastWeekTs);

            BigDecimal videoCurrentIncome = quarterIncomeSellGoodsVo.getVideo_income();
            BigDecimal videoYesterdayIncome = yesterdayTotal.subtract(yesterdayData.getIncome());
            BigDecimal videoLastWeekIncome = lastWeekTotal.subtract(lastWeekData.getIncome());

            quarterIncomeSellGoodsVo.setVideo_income_day_ratio(getCompareRate(
                    videoCurrentIncome, videoYesterdayIncome));
            quarterIncomeSellGoodsVo.setVideo_income_week_yoy_ratio(getCompareRate(
                    videoCurrentIncome, videoLastWeekIncome));

        }

    }

    /**
     * 直播消耗
     */
    public List<TrendDateIncomeDto> queryLiveCostTrend(Timestamp timestamp) {
        // 查询30 day 直播消耗
        Timestamp beforeThirtyTs =
                incomeTimeUtil.getPeriodStartTimeBeforeToday(29, timestamp);
        List<CmcGoodsDayStatReportPo> cmcGoodsDayStatReportPos =
                cmcGoodsDayStatReportRepo.selectCostTrend(beforeThirtyTs, timestamp);

        Map<Timestamp, CmcGoodsDayStatReportPo> cmcGoodsDayStatReportPoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(cmcGoodsDayStatReportPos)) {
            cmcGoodsDayStatReportPoMap = cmcGoodsDayStatReportPos.stream()
                    .collect(Collectors.toMap(e -> e.getStatDay()
                            , e -> e, (v1, v2) -> v1));
        }
        List<TrendDateIncomeDto> liveTrendDateIncomeDtos = new ArrayList<>();
        for (int i = 29; i >= 0; i--) {
            Timestamp tempTs =
                    incomeTimeUtil.getPeriodStartTimeBeforeToday(i, timestamp);
            CmcGoodsDayStatReportPo cmcGoodsDayPo
                    = cmcGoodsDayStatReportPoMap.get(tempTs);
            TrendDateIncomeDto trendDateIncomeDto = new TrendDateIncomeDto();
            if (cmcGoodsDayPo == null) {
                trendDateIncomeDto.setDate(tempTs);
                trendDateIncomeDto.setIncome(new BigDecimal(BigInteger.ZERO));
            } else {
                trendDateIncomeDto.setDate(cmcGoodsDayPo.getStatDay());
                BigDecimal takeGoodsFlyLiveVideoCost = cmcGoodsDayPo.getTakeGoodsFlyLiveVideoCost() == null
                        ? new BigDecimal(BigInteger.ZERO) :
                        cmcGoodsDayPo.getTakeGoodsFlyLiveVideoCost();
                BigDecimal takeGoodsFlyLiveCost = cmcGoodsDayPo.getTakeGoodsFlyLiveCost() == null
                        ? new BigDecimal(BigInteger.ZERO)
                        : cmcGoodsDayPo.getTakeGoodsFlyLiveCost();
                BigDecimal sum = takeGoodsFlyLiveCost.add(takeGoodsFlyLiveVideoCost);
                trendDateIncomeDto.setIncome(sum);
            }
            liveTrendDateIncomeDtos.add(trendDateIncomeDto);
        }
        return liveTrendDateIncomeDtos;
    }


    /**
     * 计算同比环比数据
     *
     * @param lastData    上一期数据
     * @param currentDate 当期数据
     * @return
     */
    private BigDecimal getCompareRate(BigDecimal currentDate, BigDecimal lastData) {
        if (currentDate == null || lastData == null) {
            return new BigDecimal(BigInteger.ZERO);
        }
        // 环比（%）=(当q总gmv-上个q总gmv)/上个q总gmv*100
        BigDecimal compareGap = (null != lastData) ?
                currentDate.subtract(lastData)
                : null;
        BigDecimal compareRate = (null != compareGap)
                ? incomeUtil.divide(compareGap, lastData, 4)
                : null;
        return compareRate;
    }


    /**
     * 计算同比环比数据
     *
     * @param lastData    上一期数据
     * @param currentDate 当期数据
     * @return
     */
    private BigDecimal getCompareRate(Long currentDate, Long lastData) {
        if (currentDate == null || lastData == null) {
            return new BigDecimal(BigInteger.ZERO);
        }
        // 环比（%）=(当q总gmv-上个q总gmv)/上个q总gmv*100
        Long compareGap = (null != lastData) ? currentDate - lastData : null;
        BigDecimal compareRate = (null != compareGap)
                ? incomeUtil.divide(compareGap, lastData, 4)
                : null;
        return compareRate;
    }

    /**
     * 获取 0
     * @param timestamp
     * @return
     */
    public CmcGoodsDayStatReportPo getZero(Timestamp timestamp) {

        CmcGoodsDayStatReportPo cmcGoodsDayStatReportPo = CmcGoodsDayStatReportPo.builder().appDau(0L)
                .videoVv(0L).livePv(0L).gmv(new BigDecimal(BigInteger.ZERO))
                .quarterGmv(new BigDecimal(BigInteger.ZERO)).orderCnt(0L)
                .liveGmv(new BigDecimal(BigInteger.ZERO)).videoGmv(new BigDecimal(BigInteger.ZERO))
                .taobaoGmv(new BigDecimal(BigInteger.ZERO)).jdGmv(new BigDecimal(BigInteger.ZERO))
                .pddGmv(new BigDecimal(BigInteger.ZERO)).otherGmv(0L).saleProductNum(0L)
                .quarterlyTakeGoodsFlyCost(new BigDecimal(BigInteger.ZERO)).cm3rdPubShareFee(new BigDecimal(BigInteger.ZERO))
                .notLiveTakeGoodsUpNum(0L).videoTakeGoodsUpNum(0L).liveTakeGoodsUpNum(0L)
                .takeGoodsUpNum(0L).takeGoodsFlyMobileCpm(new BigDecimal(BigInteger.ZERO)).adPv(0L)
                .takeGoodsFlyMobileCost(new BigDecimal(BigInteger.ZERO))
                .takeGoodsFlyLiveCost(new BigDecimal(BigInteger.ZERO))
                .takeGoodsFlyLiveVideoCost(new BigDecimal(BigInteger.ZERO))
                .takeGoodsFlyVideoCost(new BigDecimal(BigInteger.ZERO))
                .takeGoodsFlyCost(new BigDecimal(BigInteger.ZERO))
                .lastYearQuarterGmv(new BigDecimal(BigInteger.ZERO))
                .statDay(incomeTimeUtil.getYesterdayBegin(timestamp)).build();
        return cmcGoodsDayStatReportPo;
    }

}
