package com.bilibili.crm.platform.portal.webapi.finance.vo.flow;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractBindPolicyFlowVo {

    @ApiModelProperty("政策id")
    private Integer policy_id;

    @ApiModelProperty("流程id")
    private String process_instance_id;

    @ApiModelProperty("流程状态")
    private Integer status;
    private String status_name;

    /**
     * 广告主账号id
     */
    @ApiModelProperty("广告主账号")
    private Integer account_id;
    private String account_name;

    /**
     * 广告主客户id
     */
    @ApiModelProperty("广告主客户")
    private Integer customer_id;
    private String customer_name;

    /**
     * 合同总收入
     */
    @ApiModelProperty("合同总收入(元)")
    private BigDecimal contract_total_income;
}
