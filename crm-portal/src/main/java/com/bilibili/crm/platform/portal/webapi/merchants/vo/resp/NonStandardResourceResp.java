package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import com.bilibili.crm.platform.portal.attachment.AttachmentInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/4
 */
@Data
public class NonStandardResourceResp {

    @ApiModelProperty("资源id")
    private String resourceId;

    @ApiModelProperty("资源位置")
    private String resourceLocation;

    @ApiModelProperty("资源类型")
    private String resourceType;

    @ApiModelProperty("资源名称")
    private String resourceName;

    @ApiModelProperty("权益描述")
    private String rightsDesc;

    @ApiModelProperty("资源平台")
    private String resourcePlatform;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("适用项目等级")
    private String projectLevel;

    @ApiModelProperty("适用项目类型")
    private String projectType;

    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("资源核价模式")
    private String resourcePricingModule;

    @ApiModelProperty("净价单价")
    private Long netUnitPrice;

    @ApiModelProperty("刊例单价-低")
    private Long publishUnitPriceLow;

    @ApiModelProperty("刊例单价-高")
    private Long publishUnitPriceHigh;

    @ApiModelProperty("最低起售CPM量")
    private Integer minCpmSaleNum;

    @ApiModelProperty("单位时长")
    private Integer unitDuration;

    @ApiModelProperty("利润率")
    private Integer profitRate;

    @ApiModelProperty("状态: 0-启用 1-封禁 2-删除")
    private Byte status;

    @ApiModelProperty("资源大类")
    private String tabType;

    @ApiModelProperty("demo")
    private List<AttachmentInfoVo> demo;

}
