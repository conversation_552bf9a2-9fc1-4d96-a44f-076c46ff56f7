package com.bilibili.crm.platform.portal.webapi.merchants.convert;

import com.bilibili.crm.biz.merchants.bo.QueryPackageWorkOrderParam;
import com.bilibili.crm.biz.merchants.enums.PackageWorkOrderTypeEnum;
import com.bilibili.crm.biz.merchants.po.PackageWorkOrderPo;
import com.bilibili.crm.platform.biz.util.DateUtils;
import com.bilibili.crm.platform.portal.webapi.merchants.vo.req.NonStandardWorkOrderQueryReq;
import com.bilibili.crm.platform.portal.webapi.merchants.vo.req.WorkOrderDetailQueryReq;
import com.bilibili.crm.platform.portal.webapi.merchants.vo.resp.QueryCustomerPackageResp;
import com.bilibili.crm.platform.portal.webapi.merchants.vo.resp.QueryResourceWorkOrderResp;

import java.util.Map;

/**
 * <AUTHOR>
 * @date 2025/3/29
 */
public class ResourceWorkOrderConvert {

    public static QueryPackageWorkOrderParam convert(WorkOrderDetailQueryReq req) {
        QueryPackageWorkOrderParam queryPackageWorkOrderParam = new QueryPackageWorkOrderParam();
        queryPackageWorkOrderParam.setProjectId(req.getProjectId());
        queryPackageWorkOrderParam.setSeatId(req.getSeatId());
        queryPackageWorkOrderParam.setWorkOrderId(req.getWorkOrderId());
        queryPackageWorkOrderParam.setCustomerId(req.getCustomerId());
        queryPackageWorkOrderParam.setWorkOrderType(req.getWorkOrderType());
        return queryPackageWorkOrderParam;
    }

    public static QueryPackageWorkOrderParam convert(NonStandardWorkOrderQueryReq req) {
        QueryPackageWorkOrderParam queryPackageWorkOrderParam = new QueryPackageWorkOrderParam();
        queryPackageWorkOrderParam.setProjectId(req.getProjectId());
        queryPackageWorkOrderParam.setWorkOrderId(req.getWorkOrderId());
        queryPackageWorkOrderParam.setCustomerId(req.getCustomerId());
        queryPackageWorkOrderParam.setWorkOrderType(req.getWorkOrderType());
        queryPackageWorkOrderParam.setPage(req.getPage());
        queryPackageWorkOrderParam.setSize(req.getSize());
        queryPackageWorkOrderParam.setStatus(req.getStatus());
        return queryPackageWorkOrderParam;
    }

    public static QueryResourceWorkOrderResp convert(PackageWorkOrderPo po, Map<Integer, String> map) {
        QueryResourceWorkOrderResp queryResourceWorkOrderResp = new QueryResourceWorkOrderResp();
        queryResourceWorkOrderResp.setWorkOrderId(po.getId());
        queryResourceWorkOrderResp.setWorkOrderType(PackageWorkOrderTypeEnum.getName(po.getType()));
        queryResourceWorkOrderResp.setStatus(po.getStatus());
        queryResourceWorkOrderResp.setProjectName(map.get(po.getProjectId()));
        queryResourceWorkOrderResp.setProjectId(String.valueOf(po.getProjectId()));
        queryResourceWorkOrderResp.setSeatId(po.getSeatId());
        queryResourceWorkOrderResp.setCustomerId(po.getCustomerId());
        queryResourceWorkOrderResp.setCustomerName(po.getCustomerName());
        queryResourceWorkOrderResp.setSeatType(po.getSeatType());
        queryResourceWorkOrderResp.setSeatTypeDesc(po.getSeatTypeDesc());
        queryResourceWorkOrderResp.setCreator(po.getCreateUser());
        queryResourceWorkOrderResp.setCtime(DateUtils.convertTimestamp(po.getCtime()));
        return queryResourceWorkOrderResp;
    }

    public static QueryCustomerPackageResp convert(PackageWorkOrderPo workOrderPo) {
        QueryCustomerPackageResp queryCustomerPackageResp = new QueryCustomerPackageResp();
        queryCustomerPackageResp.setCustomerWorkOrderId(workOrderPo.getId());
        queryCustomerPackageResp.setCustomerId(workOrderPo.getCustomerId());
        queryCustomerPackageResp.setCustomerName(workOrderPo.getCustomerName());
        queryCustomerPackageResp.setStatus(workOrderPo.getStatus());
        queryCustomerPackageResp.setCreateUser(workOrderPo.getCreateUser());
        queryCustomerPackageResp.setSaleNickName(workOrderPo.getSaleName());
        queryCustomerPackageResp.setPackageSumNetPrice(workOrderPo.getPackageSumNetPrice());
        queryCustomerPackageResp.setPublishSumPrice(workOrderPo.getPublishSumPrice());
        queryCustomerPackageResp.setNetSumPrice(workOrderPo.getNetSumPrice());
        return queryCustomerPackageResp;

    }
}
