package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AchievementContractOrderedUnRecordDetailVo {


    @ApiModelProperty(notes = "投放周期")
    @ExcelResources(title = "投放周期")
    private String period;

    @ExcelResources(title = "合同id")
    private Integer contract_id;

    // 合同号
    @ExcelResources(title = "合同号")
    private Long contract_number;

    // 合同名称
    @ExcelResources(title = "合同名称")
    private String contract_name;

    @ExcelResources(title = "合同创建时间")
    private Timestamp contract_create_time;

    // 合同开始时间
    @ExcelResources(title = "合同开始时间")
    private String begin_time;

    // 合同结束时间
    @ExcelResources(title = "合同结束时间")
    private String end_time;

    // 合同类型
    @ExcelResources(title = "合同类型")
    private String contract_type_desc;

    // 合同状态
    @ExcelResources(title = "合同状态")
    private String contract_status_desc;

    // 合同打包价
    @ExcelResources(title = "合同打包价")
    private BigDecimal contract_amount;

    // 项目名称
    @ExcelResources(title = "合同项目名称")
    private String project_name;

    // 存档状态
    @ExcelResources(title = "存档状态")
    private String archive_status_desc;

    @ExcelResources(title = "集团ID")
    private Integer group_id;

    // 集团名称
    @ExcelResources(title = "集团名称")
    private String group_name;

    // 产品线名称
    @ExcelResources(title = "产品线名称")
    private String product_line_name;

    // 产品名称
    @ExcelResources(title = "品牌名称")
    private String product_name;


    @ExcelResources(title = "品牌Id")
    private Integer product_id;

    // 客户信息


    @ExcelResources(title = "广告主账户ID")
    private Integer account_id;

    @ExcelResources(title = "广告主账户名称")
    private String account_name;

    // 客户ID
    @ExcelResources(title = "广告主客户ID")
    private Integer customer_id;

    // 客户名称
    @ExcelResources(title = "广告主客户名称")
    private String customer_name;

    // 公司名称
    @ExcelResources(title = "公司名称")
    private String company_name;

    // 合同账户所属部门
    @ExcelResources(title = "所属部门")
    private String account_department_name;

    // 一级行业分类
    @ExcelResources(title = "一级行业分类")
    private String first_category_name;

    // 二级行业分类
    @ExcelResources(title = "二级行业分类")
    private String second_category_name;

    @ExcelResources(title = "业务一级行业分类")
    private String biz_industry_category_first_name;

    @ExcelResources(title = "业务二级行业分类")
    private String biz_industry_category_second_name;

    // 代理商id
    @ExcelResources(title = "代理商账户id")
    private Integer agent_id;

    private Integer agent_account_id;
    // 代理商名称
    @ExcelResources(title = "代理商账户名称")
    private String agent_name;

    // 代理商id
    @ExcelResources(title = "代理商客户id")
    private Integer agent_customer_id;

    // 代理商名称
    @ExcelResources(title = "代理商客户名称")
    private String agent_customer_name;

    // 销售信息

//    // 销售员
//    @ExcelResources(title = "销售员")
//    private String sale_name;

    @ExcelResources(title = "直客销售")
    private String direct_sales;

    @ExcelResources(title = "渠道销售")
    private String channel_sales;

    // 合同创建人
    @ExcelResources(title = "合同创建人")
    private String creator;

    // 项目执行
    @ExcelResources(title = "项目执行")
    private String execute_name;


    @ApiModelProperty("整包金额")
    @ExcelResources(title = "整包金额")
    private BigDecimal package_amount;

    @ApiModelProperty("已录入点位金额")
    @ExcelResources(title = "已录入点位金额")
    private BigDecimal record_amount;

    @ApiModelProperty("未录入金额总计")
    @ExcelResources(title = "未录入金额总计")
    private BigDecimal un_record_amount;

    @ApiModelProperty("品牌未录入金额")
    @ExcelResources(title = "品牌未录入金额")
    private BigDecimal brand_un_record_amount;


    @ApiModelProperty("商单未录入金额")
    @ExcelResources(title = "商单未录入金额")
    private BigDecimal pick_up_un_record_amount;


    @ExcelResources(title = "订单类型")
    private String order_type_desc;

    private Integer order_type;

    @ExcelResources(title = "非标一级产品")
    private String first_category_product;

    private Integer first_category_product_id;

    @ExcelResources(title = "非标二级产品")
    private String second_category_product;

    private Integer second_category_product_id;

    @ExcelResources(title = "账单金额")
    private BigDecimal daily_package_amount;
    @ExcelResources(title = "商机代理ID")
    private Integer opt_agent_customer_id;

    @ExcelResources(title = "商机代理全称")
    private String opt_agent_customer_name;

    @ExcelResources(title = "代理集团id")
    private Integer agent_account_group_id;

    @ExcelResources(title = "代理集团名称")
    private String agent_account_group_name;

    private String straight_name_group;

    private String straight_name_second_group;

    private String channel_name_group;

    private String channel_name_second_group;

    private String united_first_industry_name;
    private String united_second_industry_name;
    private String united_third_industry_name;

}
