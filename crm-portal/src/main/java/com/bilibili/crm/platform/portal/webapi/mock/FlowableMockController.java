package com.bilibili.crm.platform.portal.webapi.mock;

import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.biz.dao.ACTGEPROPERTYDao;
import com.bilibili.crm.platform.biz.po.ACTGEPROPERTYPo;
import com.bilibili.crm.platform.biz.po.ACTGEPROPERTYPoExample;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

@Slf4j
@RestController
@RequestMapping("/web_api/v1/mock/flowable")
public class FlowableMockController {

    @Autowired
    private ACTGEPROPERTYDao actgepropertyDao;

    @RequestMapping(value = "/initProperty", method = RequestMethod.GET)
    public Response<Integer> initProperty(@RequestParam(value = "dbid", required = false) String dbid, @RequestParam(value =
            "version", required = false) String version) {
        actgepropertyDao.deleteByExample(new ACTGEPROPERTYPoExample());

        ACTGEPROPERTYPo actgepropertyPo1 = new ACTGEPROPERTYPo();
        actgepropertyPo1.setName("cfg.execution-related-entities-count");
        actgepropertyPo1.setValue("true");
        actgepropertyPo1.setRev(1);
        actgepropertyDao.insert(actgepropertyPo1);

        ACTGEPROPERTYPo actgepropertyPo2 = new ACTGEPROPERTYPo();
        actgepropertyPo2.setName("cfg.task-related-entities-count");
        actgepropertyPo2.setValue("true");
        actgepropertyPo2.setRev(1);
        actgepropertyDao.insert(actgepropertyPo2);

        ACTGEPROPERTYPo actgepropertyPo3 = new ACTGEPROPERTYPo();
        actgepropertyPo3.setName("next.dbid");
        actgepropertyPo3.setValue(dbid);
        actgepropertyPo3.setRev(1);
        actgepropertyDao.insert(actgepropertyPo3);

        ACTGEPROPERTYPo actgepropertyPo4 = new ACTGEPROPERTYPo();
        actgepropertyPo4.setName("common.schema.version");
        actgepropertyPo4.setValue(version);
        actgepropertyPo4.setRev(1);
        actgepropertyDao.insert(actgepropertyPo4);

        ACTGEPROPERTYPo actgepropertyPo5 = new ACTGEPROPERTYPo();
        actgepropertyPo5.setName("entitylink.schema.version");
        actgepropertyPo5.setValue(version);
        actgepropertyPo5.setRev(1);
        actgepropertyDao.insert(actgepropertyPo5);

        ACTGEPROPERTYPo actgepropertyPo6 = new ACTGEPROPERTYPo();
        actgepropertyPo6.setName("eventsubscription.schema.version");
        actgepropertyPo6.setValue(version);
        actgepropertyPo6.setRev(1);
        actgepropertyDao.insert(actgepropertyPo6);

        ACTGEPROPERTYPo actgepropertyPo7 = new ACTGEPROPERTYPo();
        actgepropertyPo7.setName("identitylink.schema.version");
        actgepropertyPo7.setValue(version);
        actgepropertyPo7.setRev(1);
        actgepropertyDao.insert(actgepropertyPo7);

        ACTGEPROPERTYPo actgepropertyPo8 = new ACTGEPROPERTYPo();
        actgepropertyPo8.setName("job.schema.version");
        actgepropertyPo8.setValue(version);
        actgepropertyPo8.setRev(1);
        actgepropertyDao.insert(actgepropertyPo8);

        ACTGEPROPERTYPo actgepropertyPo9 = new ACTGEPROPERTYPo();
        actgepropertyPo9.setName("schema.version");
        actgepropertyPo9.setValue(version);
        actgepropertyPo9.setRev(1);
        actgepropertyDao.insert(actgepropertyPo9);

        ACTGEPROPERTYPo actgepropertyPo10 = new ACTGEPROPERTYPo();
        actgepropertyPo10.setName("task.schema.version");
        actgepropertyPo10.setValue(version);
        actgepropertyPo10.setRev(1);
        actgepropertyDao.insert(actgepropertyPo10);

        ACTGEPROPERTYPo actgepropertyPo11 = new ACTGEPROPERTYPo();
        actgepropertyPo11.setName("variable.schema.version");
        actgepropertyPo11.setValue(version);
        actgepropertyPo11.setRev(1);
        actgepropertyDao.insert(actgepropertyPo11);

        ACTGEPROPERTYPo actgepropertyPo12 = new ACTGEPROPERTYPo();
        actgepropertyPo12.setName("schema.history");
        actgepropertyPo12.setValue(version);
        actgepropertyPo12.setRev(1);
        actgepropertyDao.insert(actgepropertyPo12);
        return Response.SUCCESS();
    }
}
