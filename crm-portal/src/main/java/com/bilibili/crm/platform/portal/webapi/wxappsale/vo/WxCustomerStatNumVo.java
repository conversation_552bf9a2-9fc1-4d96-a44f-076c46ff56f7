package com.bilibili.crm.platform.portal.webapi.wxappsale.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * @author: brady
 * @time: 2021/6/29 4:51 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WxCustomerStatNumVo implements Serializable {

    private static final long serialVersionUID = 1L;
    @ApiModelProperty("在投数")
    private Integer cur_num;
    @ApiModelProperty("活跃数")
    private Integer active_num;
    @ApiModelProperty("新增数")
    private Integer new_num;
    @ApiModelProperty("流失数")
    private Integer loss_num;

    @ApiModelProperty("较上个周期在投数")
    private Integer compare_last_cur_num;
    @ApiModelProperty("较上个周期活跃数")
    private Integer compare_last_active_num;
    @ApiModelProperty("较上个周期新增数")
    private Integer compare_last_new_num;
    @ApiModelProperty("较上个周期流失数")
    private Integer compare_last_loss_num;
}
