package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
public class QueryResourceWorkOrderResp {

    @ApiModelProperty("资源包id")
    private Long workOrderId;

    @ApiModelProperty("资源包类型")
    private String workOrderType;

    @ApiModelProperty("包核价状态: 0 - 待提交 1-审批中 2-通过 3-拒绝 4-废弃")
    private Byte status;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("项目Id")
    private String projectId;

    @ApiModelProperty("席位id")
    private Long seatId;

    @ApiModelProperty("客户id")
    private Integer customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("席位类型")
    private String seatType;

    @ApiModelProperty("席位类型描述")
    private String seatTypeDesc;

    @ApiModelProperty("创建人")
    private String creator;

    @ApiModelProperty("创建时间")
    private Long ctime;
}
