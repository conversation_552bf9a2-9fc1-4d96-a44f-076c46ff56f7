package com.bilibili.crm.platform.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.util.List;

/**
 * Created by user on 2017/8/10.
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateRefundVo {

    @ApiModelProperty("记录ID")
    @NotNull(message = "记录ID不可为空")
    private Integer id;

    @ApiModelProperty("账号ID")
    @NotNull(message = "账号ID不可为空")
    private Integer account_id;

    @ApiModelProperty("退款金额,单位(元)")
    @NotNull(message = "退款金额不可为空")
    private BigDecimal amount;

    @ApiModelProperty("退款附件")
    private List<RefundAttachmentVo> refund_attachment_vo;

    @ApiModelProperty("备注：如实银行转账,记录流水号")
    private String remark;

}
