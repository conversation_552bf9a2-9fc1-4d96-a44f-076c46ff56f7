package com.bilibili.crm.platform.portal.filter;


import com.bilibili.bjcom.sso.SSOAuthFailHandler;
import com.bilibili.bjcom.sso.SSOClient;
import com.bilibili.bjcom.sso.SSOUserInfo;
import com.bilibili.bjcom.sso.StdSSOAuthFailHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.codec.digest.DigestUtils;
import org.apache.commons.lang.StringUtils;

import javax.servlet.*;
import javax.servlet.http.Cookie;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;

/**
 * SSOFilter精简版
 * 解析userinfo，未登录不处理
 */
@Slf4j
public class SSOUnBlockFilter implements Filter {
    public final static String USERINFO_ATTR_NAME = "USERINFO";
    public final static String JSESSIONID_COOKIE_NAME = "_AJSESSIONID";
    public final static String USERNAME_COOKIE_NAME = "username";
    public final static String CACHE_SIGN_COOKIE_NAME = "sso_local_sign";

    @Override
    public void init(FilterConfig filterConfig) throws ServletException {}
    @Override
    public void destroy() {
        try {
            ssoClient.close();
        } catch (IOException e) {
            log.warn("SSO client closing error", e);
        }
    }

    private boolean enableHeaderToken = false;
    private int cacheTimeout = 0;
    private String cacheSecret = "sn503wgee6vem2kz";
    private SSOAuthFailHandler authFailHandler = StdSSOAuthFailHandler.INSTANCE;
    private SSOClient ssoClient;

    @Override
    public void doFilter(ServletRequest servletRequest, ServletResponse servletResponse, FilterChain filterChain) throws IOException, ServletException {
        HttpServletRequest request = (HttpServletRequest) servletRequest;
        HttpServletResponse response = (HttpServletResponse) servletResponse;
        CookieInfo cookieInfo = getCookieInfo(request, response);
        if (StringUtils.isBlank(cookieInfo.sessionId)) {
            //不处理
//            if (loginFail(request, response)) {
                filterChain.doFilter(servletRequest, servletResponse);
//            }
            return;
        }
        // 获取到sessionId
        long timeId = 0;
        if (cacheTimeout > 0) {
            timeId = System.currentTimeMillis() / (cacheTimeout * 1000);

            if (StringUtils.isNotBlank(cookieInfo.username) &&
                    StringUtils.isNotBlank(cookieInfo.cacheSign) &&
                    buildSign(cookieInfo.sessionId, cookieInfo.username, timeId).equals(cookieInfo.cacheSign)) {
                // 缓存有效
                request.setAttribute(USERINFO_ATTR_NAME,SSOUserInfo.builder().userName(cookieInfo.username).build());
                filterChain.doFilter(servletRequest, servletResponse);
                return;
            }
        }
        // 缓存无效或未启用
        SSOUserInfo userInfo = ssoClient.verify(cookieInfo.sessionId);
        if (userInfo == null) {
            // 未登录
            if (loginFail(request, response)) {
                filterChain.doFilter(servletRequest, servletResponse);
            }
            return;
        }
        // 已登录
        if (cacheTimeout > 0) {
            response.addCookie(buildRootCookie(CACHE_SIGN_COOKIE_NAME,buildSign(cookieInfo.sessionId, userInfo.getUserName(), timeId)));
        }
        request.setAttribute(USERINFO_ATTR_NAME, userInfo);
        filterChain.doFilter(servletRequest, servletResponse);
    }

    private CookieInfo getCookieInfo(HttpServletRequest request, HttpServletResponse response) {
        String sessionId = null;
        String username = null;
        String cacheSign = null;

        Cookie[] cookies = request.getCookies();
        if (cookies != null) {
            for (Cookie cookie : cookies) {
                if (JSESSIONID_COOKIE_NAME.equals(cookie.getName())) {
                    sessionId = cookie.getValue();
                } else if (USERNAME_COOKIE_NAME.equals(cookie.getName())) {
                    username = cookie.getValue();
                } else if (CACHE_SIGN_COOKIE_NAME.equals(cookie.getName())) {
                    cacheSign = cookie.getValue();
                }
            }
        }

        if (enableHeaderToken) {
            String headerSession = request.getHeader(JSESSIONID_COOKIE_NAME);
            if (StringUtils.isNotBlank(headerSession) && !headerSession.equals(sessionId)) {
                sessionId = headerSession;
                response.addCookie(buildRootCookie(JSESSIONID_COOKIE_NAME, headerSession));
            }
        }

        return new CookieInfo(sessionId, username, cacheSign);
    }

    private Cookie buildRootCookie(String name, String value) {
        Cookie cookie = new Cookie(name, value);
        cookie.setPath("/");
        return cookie;
    }

    private boolean loginFail(HttpServletRequest request, HttpServletResponse response) throws IOException {
        return authFailHandler.handle(request, response, ssoClient);
    }

    private String buildSign(String sessionId, String username, long timeId) {
        return DigestUtils.sha1Hex(new StringBuilder().append(sessionId).append(username).append(timeId).append(ssoClient.getCaller()).append(cacheSecret).toString());
    }

    public void setEnableHeaderToken(boolean enableHeaderToken) {
        this.enableHeaderToken = enableHeaderToken;
    }

    public void setCacheTimeout(int cacheTimeout) {
        this.cacheTimeout = cacheTimeout;
    }

    public void setCacheSecret(String cacheSecret) {
        this.cacheSecret = cacheSecret;
    }

    public void setSsoClient(SSOClient ssoClient) {
        this.ssoClient = ssoClient;
    }

    private static class CookieInfo {
        String sessionId;
        String username;
        String cacheSign;
        CookieInfo(String sessionId, String username, String cacheSign) {
            this.sessionId = sessionId;
            this.username = username;
            this.cacheSign = cacheSign;
        }
    }
}
