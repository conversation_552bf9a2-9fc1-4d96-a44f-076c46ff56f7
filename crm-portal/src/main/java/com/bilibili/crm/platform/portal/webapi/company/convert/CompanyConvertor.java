package com.bilibili.crm.platform.portal.webapi.company.convert;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.commercialorder.api.upper.enums.EnableStatus;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.service.ICommerceCenterCategoryService;
import com.bilibili.crm.platform.api.company.dto.CorporationGroupDto;
import com.bilibili.crm.platform.api.company.dto.GroupDealerConfigDTO;
import com.bilibili.crm.platform.api.company.dto.ProductDto;
import com.bilibili.crm.platform.api.company.dto.ProductLineDto;
import com.bilibili.crm.platform.biz.po.AccCompanyGroupPo;
import com.bilibili.crm.platform.biz.repo.AccCompanyGroupRepo;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import com.bilibili.crm.platform.portal.common.DropBoxVo;
import com.bilibili.crm.platform.portal.webapi.company.vo.CorporationGroupVo;
import com.bilibili.crm.platform.portal.webapi.company.vo.ProductLineVo;
import com.bilibili.crm.platform.portal.webapi.company.vo.ProductVo;
import com.bilibili.crm.platform.portal.webapi.sale_sea.service.WebSaleSeaService;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class CompanyConvertor {

	@Autowired
	private WebSaleSeaService webSaleSeaService;
	@Autowired
	private ICommerceCenterCategoryService iCommerceCenterCategoryService;
	@Resource
	private AccCompanyGroupRepo accCompanyGroupRepo;

	public static List<CorporationGroupVo> convert2VosFromCorporationGroupDtos(List<CorporationGroupDto> records) {
		if(CollectionUtils.isEmpty(records)) {
			return Collections.emptyList();
		}
		
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<CorporationGroupVo> list = new ArrayList<>(records.size());
		for(CorporationGroupDto dto : records) {
			EnableStatus enableStatus = EnableStatus.getByCode(dto.getStatus());
			CorporationGroupVo vo = new CorporationGroupVo();
			BeanUtils.copyProperties(dto, vo);
			
			String tag = dto.getTag();
			if(StringUtils.isNotBlank(tag)) {
				String[] tagItems = tag.split(",");
				vo.setTags(Arrays.asList(tagItems));
			}

			vo.setStatus(dto.getStatus());
			vo.setStatusName(enableStatus!=null?enableStatus.getDesc(): null);
			vo.setUpdate_user(dto.getUpdateUser());
			vo.setMtime(format.format(dto.getMtime()));
			
			list.add(vo);
		}
		return list;
	}

	public static CorporationGroupVo convert2VoFromCorporationGroupDto(CorporationGroupDto dto) {
		if(dto == null) {
			return null;
		}
		CorporationGroupVo vo = new CorporationGroupVo();
		BeanUtils.copyProperties(dto, vo);
		
		if(StringUtils.isNotBlank(dto.getTag())) {
			String[] tagItems = dto.getTag().split(",");
			vo.setTags(Arrays.asList(tagItems));
		}
		
		vo.setUpdate_user(dto.getUpdateUser());
		vo.setMtime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dto.getMtime()));
		return vo;
	}

	public static ProductLineVo convert2VoFromProductLineDto(ProductLineDto dto) {
		if(dto == null) {
			return null;
		}
		ProductLineVo vo = new ProductLineVo();
		BeanUtils.copyProperties(dto, vo);
		
		if(StringUtils.isNotBlank(dto.getTag())) {
			String[] tagItems = dto.getTag().split(",");
			vo.setTags(Arrays.asList(tagItems));
		}
		vo.setGroup_id(dto.getGroupId());
		vo.setUpdate_user(dto.getUpdateUser());
		vo.setMtime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dto.getMtime()));
		return vo;
	}

	public List<ProductLineVo> convert2VosFromProductLineDtos(List<ProductLineDto> records) {
		if(CollectionUtils.isEmpty(records)) {
			return Collections.emptyList();
		}
		
		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<ProductLineVo> list = new ArrayList<>(records.size());
		for(ProductLineDto dto : records) {
			EnableStatus enableStatus = EnableStatus.getByCode(dto.getStatus());
			ProductLineVo vo = new ProductLineVo();
			BeanUtils.copyProperties(dto, vo);
			
			String tag = dto.getTag();
			if(StringUtils.isNotBlank(tag)) {
				String[] tagItems = tag.split(",");
				vo.setTags(Arrays.asList(tagItems));
			}
			vo.setGroup_id(dto.getGroupId());
			vo.setStatus(dto.getStatus());
			vo.setStatusName(enableStatus!=null?enableStatus.getDesc():null);
			vo.setUpdate_user(dto.getUpdateUser());
			vo.setMtime(format.format(dto.getMtime()));

			// 公私海名单
			Map<Integer, Map<Integer, List<DropBoxVo>>> saleMap = webSaleSeaService.getSaleMap2(dto.getProductSaleMap());
			if (!CollectionUtils.isEmpty(saleMap)) {
				Map<Integer, List<DropBoxVo>> brandBiliUserMap =
						saleMap.getOrDefault(SaleSeaProductCategory.BRAND.getCode(), Collections.emptyMap());
				List<DropBoxVo> brandList = brandBiliUserMap.getOrDefault(BiliUserType.STRAIGHT_MANAGER.getCode(),
						Collections.emptyList());
				vo.setBrand_sale_list(brandList.stream().map(DropBoxVo::getName).collect(Collectors.joining(",")));

				Map<Integer, List<DropBoxVo>> rtbBiliUserMap =
						saleMap.getOrDefault(SaleSeaProductCategory.RTB.getCode(), Collections.emptyMap());
				List<DropBoxVo> rtbList = rtbBiliUserMap.getOrDefault(BiliUserType.STRAIGHT_MANAGER.getCode(),
						Collections.emptyList());
				vo.setRtb_sale_list(rtbList.stream().map(DropBoxVo::getName).collect(Collectors.joining(",")));
			}

			list.add(vo);
		}
		return list;
	}

	public static ProductVo convert2VoFromProductDto(ProductDto dto) {
		if(dto == null) {
			return null;
		}
		ProductVo vo = new ProductVo();
		BeanUtils.copyProperties(dto, vo);
		
		if(StringUtils.isNotBlank(dto.getTag())) {
			String[] tagItems = dto.getTag().split(",");
			vo.setTags(Arrays.asList(tagItems));
		}
		vo.setUpdate_user(dto.getUpdateUser());
		vo.setMtime(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(dto.getMtime()));
		return vo;
	}

	public List<ProductVo> convert2VosFromProductDtos(List<ProductDto> records) {
		if(CollectionUtils.isEmpty(records)) {
			return Collections.emptyList();
		}

		List<Integer> allCategories = new ArrayList<>();
		List<Integer> categoryFirstIds = records.stream().map(ProductDto::getCommerceCategoryFirstId).collect(Collectors.toList());
		List<Integer> categorySecondIds = records.stream().map(ProductDto::getCommerceCategorySecondId).collect(Collectors.toList());
		allCategories.addAll(categoryFirstIds);
		allCategories.addAll(categorySecondIds);
		Map<Integer, CategoryDto> categoryDtoMap = iCommerceCenterCategoryService.getCategoryDtoInIds(allCategories);


		SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");
		List<ProductVo> list = new ArrayList<>(records.size());
		for(ProductDto dto : records) {
			EnableStatus enableStatus = EnableStatus.getByCode(dto.getStatus());
			ProductVo vo = new ProductVo();
			BeanUtils.copyProperties(dto, vo);
			
			String tag = dto.getTag();
			if(StringUtils.isNotBlank(tag)) {
				String[] tagItems = tag.split(",");
				vo.setTags(Arrays.asList(tagItems));
			}
			vo.setStatus(dto.getStatus());
			vo.setStatusName(enableStatus!=null?enableStatus.getDesc():null);
			vo.setUpdate_user(dto.getUpdateUser());
			vo.setMtime(format.format(dto.getMtime()));

			vo.setCommerce_category_first_id(dto.getCommerceCategoryFirstId());
			vo.setCommerce_category_first_name(categoryDtoMap.getOrDefault(dto.getCommerceCategoryFirstId(), CategoryDto.builder().build()).getName());
			vo.setCommerce_category_second_id(dto.getCommerceCategorySecondId());
			vo.setCommerce_category_second_name(categoryDtoMap.getOrDefault(dto.getCommerceCategorySecondId(), CategoryDto.builder().build()).getName());
			vo.setCurrent_budget(Utils.fromFenToYuan(dto.getCurrentBudget()));

			// 公私海名单
			Map<Integer, Map<Integer, List<DropBoxVo>>> saleMap = webSaleSeaService.getSaleMap2(dto.getProductSaleMap());
			if (!CollectionUtils.isEmpty(saleMap)) {
				Map<Integer, List<DropBoxVo>> brandBiliUserMap =
						saleMap.getOrDefault(SaleSeaProductCategory.BRAND.getCode(), Collections.emptyMap());
				List<DropBoxVo> brandList = brandBiliUserMap.getOrDefault(BiliUserType.STRAIGHT_MANAGER.getCode(),
						Collections.emptyList());
				vo.setBrand_sale_list(brandList.stream().map(DropBoxVo::getName).collect(Collectors.joining(",")));

				Map<Integer, List<DropBoxVo>> rtbBiliUserMap =
						saleMap.getOrDefault(SaleSeaProductCategory.RTB.getCode(), Collections.emptyMap());
				List<DropBoxVo> rtbList = rtbBiliUserMap.getOrDefault(BiliUserType.STRAIGHT_MANAGER.getCode(),
						Collections.emptyList());
				vo.setRtb_sale_list(rtbList.stream().map(DropBoxVo::getName).collect(Collectors.joining(",")));
			}

			list.add(vo);
		}
		return list;
	}


	public List<GroupDealerConfigDTO> checkDataForEachRowWithDealerConfig(List<String> rows) {
		if (CollectionUtils.isEmpty(rows)) {
			throw new IllegalArgumentException("请录入至少一行的配置信息");
		}
		List<GroupDealerConfigDTO> configDTOList = new ArrayList<>(rows.size());
		List<Integer> errorRowIdList = new ArrayList<>();
		List<Integer> errorConfigList = new ArrayList<>();
		List<Integer> errorRowSizeList = new ArrayList<>();
		List<String> idStrList = new ArrayList<>(rows.size());
		Map<String, String> idNameMap = new HashMap<>(rows.size());
		for (int i = 0; i < rows.size(); i++) {
			String line = rows.get(i);
			String[] array = line.split("-~-");
			if (array.length != 3) {
				errorRowSizeList.add(i + 2);
				continue;
			}
			String idStr = array[0].trim();
			String nameStr = array[1].trim();
			String dealer = array[2].trim();
			if (!Objects.equals(dealer, "是") && !Objects.equals(dealer, "否")) {
				errorConfigList.add(i + 2);
				continue;
			}
			idStrList.add(idStr);
			if (idNameMap.containsKey(idStr)) {
				errorRowIdList.add(i + 2);
				continue;
			} else {
				idNameMap.put(idStr, nameStr);
			}
			if (!StringUtils.isNumeric(idStr)) {
				errorRowIdList.add(i + 2);
				continue;
			}
			GroupDealerConfigDTO dto = new GroupDealerConfigDTO();
			dto.setId(Integer.valueOf(idStr));
			dto.setDealer(Objects.equals(dealer, "是") ? 1 : 0);
			configDTOList.add(dto);
		}
		if (!errorRowSizeList.isEmpty()) {
			throw new IllegalArgumentException("第" + errorRowSizeList.stream().map(String::valueOf).collect(Collectors.joining(",")) + "行列填写有误");
		}
		if (!errorConfigList.isEmpty()) {
			throw new IllegalArgumentException("第" + errorConfigList.stream().map(String::valueOf).collect(Collectors.joining(",")) + "行经销商标签列填写有误");
		}
		if (!errorRowIdList.isEmpty()) {
			throw new IllegalArgumentException("第" + errorRowIdList.stream().map(String::valueOf).collect(Collectors.joining(",")) + "行集团id录入有误，请修改后上传");
		}
		List<Integer> idIntList = idStrList.stream().map(Integer::valueOf).collect(Collectors.toList());
		List<AccCompanyGroupPo> queryIdList = accCompanyGroupRepo.queryListByIds(idIntList);
		Map<Integer, AccCompanyGroupPo> queryIdMap = queryIdList.stream().collect(Collectors.toMap(AccCompanyGroupPo::getId, Function.identity()));
		for (int i = 0; i < idIntList.size(); i++) {
			Integer id = idIntList.get(i);
			AccCompanyGroupPo po = queryIdMap.getOrDefault(id, AccCompanyGroupPo.builder().name("notExist").build());
			if (!StringUtils.equals(po.getName(), idNameMap.get(String.valueOf(id)))) {
				errorRowIdList.add(i + 2);
			}
		}
		if (!errorRowIdList.isEmpty()) {
			throw new IllegalArgumentException("第" + errorRowIdList.stream().map(String::valueOf).collect(Collectors.joining(",")) + "行集团信息录入有误，请修改后上传");
		}
		return configDTOList;
	}
}
