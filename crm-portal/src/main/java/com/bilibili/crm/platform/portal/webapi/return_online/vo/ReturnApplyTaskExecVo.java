package com.bilibili.crm.platform.portal.webapi.return_online.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/5 下午4:54
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ReturnApplyTaskExecVo {

    /**
     * id
     */
    @ApiModelProperty("返货申请id")
    private Integer id;

    /**
     * 知会人
     */
    @ApiModelProperty("知会人")
    private List<String> notice_users;

    /**
     * 预审审核人
     */
    @ApiModelProperty("预审审核人")
    private List<String> pre_examine_users;

    @ApiModelProperty("强制执行，不关心流程状态(前端忽略)")
    private Boolean force_exe;
}
