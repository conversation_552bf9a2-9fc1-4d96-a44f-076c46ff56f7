package com.bilibili.crm.platform.portal.webapi.sale.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2017年2月28日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class UpdateStatusVo {
    
	@ApiModelProperty(notes="销售ID")
    @NotNull(message="销售ID不可为空")
    private Integer sale_id;
    @ApiModelProperty(notes="状态：1-正常 2-封禁")
    @NotNull(message="状态不可为空")
    private Integer status;
}
