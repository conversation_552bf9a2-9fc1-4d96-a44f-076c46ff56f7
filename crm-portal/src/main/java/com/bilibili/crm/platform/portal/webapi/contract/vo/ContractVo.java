package com.bilibili.crm.platform.portal.webapi.contract.vo;

import com.bilibili.crm.platform.portal.webapi.finance.vo.flow.ContractPolicyFlowSimpleVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/5/15.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractVo {
    @ApiModelProperty("合同id")
    private Integer crm_contract_id;
    @ApiModelProperty("合同号")
    private String contract_number;

    @ApiModelProperty("合同类型 0 普通合同 1 up主制作合同")
    private Integer type;

    @ApiModelProperty("合同类型-展示值")
    private String type_desc;

    @ApiModelProperty("项目名称")
    private String project_name;

    @ApiModelProperty("合同名")
    private String name;

    @ApiModelProperty("法务合同id")
    private String legal_contract_id;


    @ApiModelProperty("合同总金额")
    private BigDecimal all_amount;

    @ApiModelProperty("合同金额")
    private BigDecimal amount;

    @ApiModelProperty("合同PD金额")
    private BigDecimal pd_amount;

    @ApiModelProperty("商单总金额")
    private BigDecimal pickup_amount;

    @ApiModelProperty("金额备注")
    private String amount_remark;

    @ApiModelProperty("开始时间")
    private Long begin_time;

    @ApiModelProperty("结束时间")
    private Long end_time;

    @ApiModelProperty("创建时间")
    private Long ctime;

    @ApiModelProperty("存档状态 0未盖章、1我方盖章、2对方盖章、3已存档")
    private Integer archive_status;

    @ApiModelProperty("存档状态-展示")
    private String archive_status_desc;

    /**
     * 合同客户，其实是合同绑定的账号
     */
    @ApiModelProperty("客户id -- 其实账户id")
    private Integer account_id;

    @ApiModelProperty("客户id")
    private Integer customer_id;

    @ApiModelProperty("客户名称")
    private String customer_name;
    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("客户名")
    private String username;

    @ApiModelProperty("一级行业")
    private String category_first_name;

    @ApiModelProperty("二级行业")
    private String category_second_name;

    @ApiModelProperty("代理商id")
    private Integer agent_id;

    @ApiModelProperty("代理商名称")
    private String agent_name;

    @ApiModelProperty("代理商客户id")
    private Integer agent_customer_id;

    @ApiModelProperty("代理商客户名称")
    private String agent_customer_name;

    @ApiModelProperty("是否有商机代理")
    private Integer has_opt_agent;

    @ApiModelProperty("商机代理ID")
    private Integer opt_agent_id;

    @ApiModelProperty("商机代理名称")
    private String opt_agent_name;

    @ApiModelProperty("销售")
    private List<ContractSaleVo> sales;

    @ApiModelProperty("合同提成直客_限提成制(仅针对外部-营销中心合同)")
    private List<ContractSaleVo> direct_sales;

    @ApiModelProperty("合同提成渠道_限提成制(仅针对外部-营销中心合同)")
    private List<ContractSaleVo> channel_sales;

    @ApiModelProperty("是否有回款")
    private Boolean has_receipts;

    @ApiModelProperty("是否有开票")
    private Boolean has_invoice;

    @ApiModelProperty("开票状态")
    private String invoice_status;

    @ApiModelProperty("拒绝理由")
    private String review_remark;

    @ApiModelProperty("财务结算title代理商id")
    private Integer finance_title_agent_id;
    @ApiModelProperty("财务结算title代理商名称")
    private String finance_title_agent_name;

    @ApiModelProperty("是否有审核通过记录")
    private Boolean audited = false;

    @ApiModelProperty("下单附件（列表页面没有）")
    private List<ContractMailOssVo> contract_mail_oss;

    @ApiModelProperty("【CRM3.1.1】合同对应的配送政策")
    private Integer distribution;

    @ApiModelProperty("【CRM3.6.6】合同对应的折扣政策")
    private BigDecimal discount;

    @ApiModelProperty("【CRM3.5】调整项目金额")
    private BigDecimal adjust_amount;

    @ApiModelProperty("【CRM3.6】 合同是否实结 0-未实结  1-已实结")
    private Integer is_contract_closed;

    @ApiModelProperty("【CRM3.6.6】 促销政策：（0-无 1-坚冰行业 2-破冰蓝海 3-单笔订单促销政策 4-其他）")
    private Integer promotion_policy;

    @ApiModelProperty("【CRM3.6.6】 合同创建人")
    private String creator;

    @ApiModelProperty("【CRM3.6.11】 集团id")
    private Integer group_id;

    @ApiModelProperty("【CRM3.6.11】 集团名称")
    private String group_name;

    @ApiModelProperty("父集团id")
    private Integer parent_group_id;

    @ApiModelProperty("父集团名称")
    private String parent_group_name;

    @ApiModelProperty("【CRM3.6.11】 产品线id")
    private Integer product_line_id;

    @ApiModelProperty("【CRM3.6.11】 产品线名称")
    private String product_line_name;

    @ApiModelProperty("【CRM3.6.11】 产品id")
    private Integer product_id;

    @ApiModelProperty("【CRM3.6.11】 产品名称")
    private String product_name;

    @ApiModelProperty("项目执行id")
    private Integer execute_id;
    @ApiModelProperty("项目执行name")
    private String execute_name;

    @ApiModelProperty("业务状态")
    private Integer bus_status;

    @ApiModelProperty("是否oa开票：0-未开票，1-开票")
    private Integer is_oa_open_bill;

    @ApiModelProperty("业务状态翻译")
    private String bus_status_desc;

    @ApiModelProperty("审核状态")
    private Integer audit_status;

    @ApiModelProperty("审核状态翻译")
    private String audit_status_desc;

    @ApiModelProperty("合同下是否存在需要审核的订单")
    private Integer exist_auditing_order;

    @ApiModelProperty("合同下是否存在需要审核的交付件")
    private Integer exist_auditing_delivery;

    @ApiModelProperty("部门id")
    private Integer department_id;

    @ApiModelProperty("部门名称")
    private String department_name;

    @ApiModelProperty("合同营销类型id")
    private Integer market_type;

    @ApiModelProperty("合同营销类型")
    private String market_type_name;

    @ApiModelProperty("商机idList")
    private List<Integer> bsi_opportunity_id_list;
    @Deprecated
    @ApiModelProperty("商机id")
    private Integer bsi_opportunity_id;

    @ApiModelProperty("是否创建了订单, true/false")
    private Boolean if_created_orders;

    @ApiModelProperty("合同是否审核通过过, true/false")
    private Boolean has_audit_pass;

    @ApiModelProperty("合同已绑定的政策流程列表")
    private List<ContractPolicyFlowSimpleVo> policyFlowSimpleVos;

    @ApiModelProperty("我方签约主体")
    private String sign_subject_name;

    @ApiModelProperty("合同营销类型")
    private String project_item_name;

    @ApiModelProperty("我方签约主体ID")
    private Integer sign_subject_id;

    @ApiModelProperty("合同营销类型ID")
    private Integer project_item_id;

    @ApiModelProperty("召集令tag id")
    private Long tag_id;

    @ApiModelProperty("召集令lite版活动开始时间")
    private Long lite_pickup_begin_time;

    @ApiModelProperty("召集令lite版活动结束时间")
    private Long lite_pickup_end_time;

    @ApiModelProperty("合同账期")
    private Integer contract_bill_period;

    @ApiModelProperty("合同账期大于90天附件")
    private List<ContractMailOssVo> contract_bill_period_oss;

    @ApiModelProperty("下单邮箱")
    private String order_email;

    @ApiModelProperty("内外部排期是否一致")
    private Integer is_schedule_the_same;

    @ApiModelProperty("外部排期开始时间")
    private String external_schedule_start_time;

    @ApiModelProperty("外部排期结束时间")
    private String external_schedule_end_time;

    @ApiModelProperty("排期表附件")
    private ContractMailOssVo contract_schedule_oss;

    @ApiModelProperty("促销政策多选")
    private List<Integer> promotion_policy_list;

    @ApiModelProperty("追款直客销售")
    private List<ContractSaleVo> substitute_direct_sale;

    @ApiModelProperty("追款渠道销售")
    private List<ContractSaleVo> substitute_channel_sale;

    @ApiModelProperty("合同上线时间")
    private String online_time;

    @ApiModelProperty("渠道合约账期（月）")
    private Float bill_period;

    @ApiModelProperty("帐期拆解 0 不拆解 1 拆解")
    private String bill_period_unpack_str;

    @ApiModelProperty("帐期拆解 0 不拆解 1 拆解")
    private Integer bill_period_unpack;

    @ApiModelProperty("项目周期开始时间")
    private Long period_start;

    @ApiModelProperty("项目周期结束时间")
    private Long period_end;

    @ApiModelProperty("商单已计收金额")
    private BigDecimal pickup_check_amount;

    @ApiModelProperty("合同已计收金额")
    private BigDecimal contract_check_amount;

    @ApiModelProperty("商机代理全称")
    private String opt_Agent_name;

    @ApiModelProperty("商机代理客户ID")
    private Integer opt_Agent_customer_id;

    @ApiModelProperty("产品标签类型 1 汽车 2 手机 3 无")
    @Deprecated
    private Integer product_model_type;

    /**
     * 是否支持产品标签
     */
    private Boolean is_support_product_model;

    @ApiModelProperty(notes = "一级产品型号")
    private String first_product_model;

    @ApiModelProperty(notes = "一级产品型号 id")
    private Long first_product_model_id;


    @ApiModelProperty(notes = "二级产品型号")
    private String second_product_model;

    @ApiModelProperty(notes = "二级产品型号 id")
    private Long second_product_model_id;

    @ApiModelProperty("统一一级行业")
    private String united_first_industry_name;

    @ApiModelProperty("统一二级行业")
    private String united_second_industry_name;

    @ApiModelProperty("统一三级行业")
    private String united_third_industry_name;

    @ApiModelProperty("整包金额")
    private BigDecimal package_amount;

    @ApiModelProperty("实结后金额")
    private BigDecimal adjusted_total_amount;

    @ApiModelProperty("商单总实收金额")
    private BigDecimal up_amount;

    /**
     * 花火商单已经使用的预算总金额
     */
    @ApiModelProperty("花火商单已经使用的预算总金额")
    private BigDecimal pickupBudgetOccupiedAmount;

    /**
     * 花火商单剩余可以使用的预算总金额
     */
    @ApiModelProperty("花火商单剩余可以使用的预算总金额")
    private BigDecimal pickupBudgetRemainingAmount;

    @ApiModelProperty("合同版本（用于版本迭代）")
    private Integer crm_ver;
    @ApiModelProperty("当前业绩直客-前端直接展示逗号分割的字符串")
    private String curr_biz_direct_sales;
    @ApiModelProperty("当前业绩渠道-前端直接展示逗号分割的字符串")
    private String curr_biz_channel_sales;
    @ApiModelProperty("历史业绩直客-前端直接展示逗号分割的字符串")
    private String his_biz_direct_sales;
    @ApiModelProperty("历史业绩渠道-前端直接展示逗号分割的字符串")
    private String his_biz_channel_sales;
    @ApiModelProperty("业绩直客是否手动调整  true=是 false = 否 null=空")
    private Boolean is_biz_direct_sales_manual_modified;
    @ApiModelProperty("业绩渠道是否手动调整  true=是 false = 否 null=空")
    private Boolean is_biz_channel_sales_manual_modified;

}
