package com.bilibili.crm.platform.portal.webapi.download.coverter;

import com.bilibili.crm.platform.api.effect.dto.BossAttachmentsDto;
import com.bilibili.crm.platform.api.effect.enums.BossTaskStatus;
import com.bilibili.crm.platform.api.effect.enums.ReportColumnBehavior;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.portal.webapi.download.vo.TaskVo;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-11-09 17:05:12
 * @description:
 **/

@Component
public class DownloadUtil {

    public static final String return_str = "有新增下载任务, 请前往任务中心查看";


    @Value("${crm.boss.download.url:https://boss.hdslb.com/business_ad_crm/}")
    private String BOSS_DOWNLOAD_URL;


    public List<TaskVo> taskDtoToVo(List<BossAttachmentsDto> attachmentsDtoList) {
        if (CollectionUtils.isEmpty(attachmentsDtoList)) {
            return new ArrayList<>();
        }
        List<TaskVo> result = new ArrayList<>();
        for (BossAttachmentsDto dto : attachmentsDtoList) {
            result.add(TaskVo.builder()
                    .boss_attachment_id(dto.getBossAttachmentId())
                    .boss_key(dto.getBossKey())
                    .start_time(CrmUtils.formatDate(dto.getStartTime()))
                    .expire_time(CrmUtils.formatDate(dto.getExpireTime()))
                    .content_type_str(ReportColumnBehavior.queryDesc(dto.getBehaviorId()))
                    .content_type(dto.getBehaviorId())
                    .file_name(dto.getFileName())
                    .task_status_desc(BossTaskStatus.queryByCode(dto.getTaskStatus()).getDesc())
                    .task_status(dto.getTaskStatus())
                    .download_url(BOSS_DOWNLOAD_URL+ dto.getBossKey())
                    .build());
        }
        return result;

    }

    public static String generateFileName(Timestamp start, Timestamp end, ReportColumnBehavior behavior) {
        if (start == null || end == null) {
            return behavior.name() + "_" + CrmUtils.formatDate(new Timestamp(System.currentTimeMillis()), CrmUtils.YYYY_MM_DD) + ".xlsx";
        } else {
            return behavior.name() + "_" + CrmUtils.formatDate(start, CrmUtils.YYYY_MM_DD) + "_"
                    + CrmUtils.formatDate(end, CrmUtils.YYYY_MM_DD) + ".xlsx";
        }
    }

    public static String generateFileNameWithDesc(Timestamp start, Timestamp end, ReportColumnBehavior behavior) {
        if (start == null || end == null) {
            return behavior.getDesc() + "_" + CrmUtils.formatDate(new Timestamp(System.currentTimeMillis()), CrmUtils.YYYY_MM_DD) + ".xlsx";
        } else {
            return behavior.getDesc() + "_" + CrmUtils.formatDate(start, CrmUtils.YYYY_MM_DD) + "_"
                    + CrmUtils.formatDate(end, CrmUtils.YYYY_MM_DD) + ".xlsx";
        }
    }
}
