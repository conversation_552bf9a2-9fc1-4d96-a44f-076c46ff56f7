package com.bilibili.crm.platform.portal.webapi.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseImageVo {

    @ApiModelProperty(notes="图片URL")
    private String url;

    @ApiModelProperty(notes="hash值")
    @NotBlank(message="hash值不可为空")
    private String hash;

    @ApiModelProperty(notes="token")
    private String token;
}
