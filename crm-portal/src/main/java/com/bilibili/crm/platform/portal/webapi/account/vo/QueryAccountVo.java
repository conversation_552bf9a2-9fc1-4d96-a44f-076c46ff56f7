package com.bilibili.crm.platform.portal.webapi.account.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryAccountVo {

    @ApiModelProperty(notes = "账号名称")
    private String username_like;

    @ApiModelProperty(notes = "是否是代理商: 0-否 1-是")
    private Integer is_agent;

    @ApiModelProperty(notes = "客户id")
    private Integer customer_id;

    @ApiModelProperty(notes = "账户类型 0个人用户 1机构用户 2个人起飞用户 不传查全部")
    private Integer user_type;

    @ApiModelProperty(notes = "查询定制账号返回类型")
    private Integer customize;

}
