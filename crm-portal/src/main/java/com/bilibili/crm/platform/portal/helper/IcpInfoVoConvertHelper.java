package com.bilibili.crm.platform.portal.helper;

import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.crm.platform.api.account.dto.IcpInfoDto;
import com.bilibili.crm.platform.portal.webapi.account.vo.IcpInfoVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Base64Utils;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/7/21.
 */
@Component
public class IcpInfoVoConvertHelper implements BeanConvertHelper<IcpInfoVo, IcpInfoDto> {
    @Autowired
    private IBfsService bfsService;

    @Override
    public IcpInfoVo convertDtoToVo(IcpInfoDto icpInfoDto) {
        try {
            return IcpInfoVo.builder()
                    .url(icpInfoDto.getImageUrl())
                    .hash(Base64Utils.encodeToString(icpInfoDto.getImageUrl().getBytes()))
                    .account_id(icpInfoDto.getAccountId())
                    .token(bfsService.getTokenByUrl(icpInfoDto.getImageUrl()))
                    .build();
        } catch (ServiceException e) {
            return null;
        }
    }
}
