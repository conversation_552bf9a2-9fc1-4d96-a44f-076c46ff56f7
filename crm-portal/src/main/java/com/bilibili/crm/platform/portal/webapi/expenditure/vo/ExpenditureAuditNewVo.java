package com.bilibili.crm.platform.portal.webapi.expenditure.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-05-23 20:21:10
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpenditureAuditNewVo {

    @ApiModelProperty("支出项id")
    @NotNull
    private List<Integer> ids;

    @ApiModelProperty("操作类型 1 提交审核 2 驳回申请 3 废弃")
    @NotNull
    private Integer operate_type;

    @ApiModelProperty("驳回理由")
    private String reject_reason;
}
