package com.bilibili.crm.platform.portal.webapi.break_tape.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-07-17 21:29:28
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BreakTapeReceiptVo implements Serializable {

    /**
     * 主键id
     */
    @ApiModelProperty("回执id")
    private Long break_tape_Id;

    /**
     * 账户id
     */
    @ApiModelProperty("账户id")
    private Integer account_id;


    @ApiModelProperty("撞线时间日预算")
    private BigDecimal account_budget;

    @ApiModelProperty("撞线时间日消耗")
    private BigDecimal account_cost;

    @ApiModelProperty("撞线时间账户余额")
    private BigDecimal account_balance;

    /**
     * 撞线类型
     */
    @ApiModelProperty("撞线类型 1 日预算撞线 2 余额撞线")
    private Integer break_line_type;

    @ApiModelProperty("撞线类型 str")
    private String break_line_type_str;

    /**
     * 撞线时间
     */
    @ApiModelProperty("撞线时间")
    private String break_line_time;

    /**
     * 回执类型
     */
    @ApiModelProperty("回执类型 1 已催  2 后端效果不达标 3 客户控预算 4 其他")
    private Integer receipt_type;

    @ApiModelProperty("回执类型 str")
    private String receipt_type_str;

    /**
     * 具体描述
     */
    @ApiModelProperty("具体描述")
    private String desc;

    @ApiModelProperty("操作人")
    private String operator;


}
