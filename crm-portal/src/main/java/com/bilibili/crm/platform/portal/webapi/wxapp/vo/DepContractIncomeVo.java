package com.bilibili.crm.platform.portal.webapi.wxapp.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DepContractIncomeVo {
    @ApiModelProperty("已执行待交付")
    private BigDecimal executed_un_delivery;

    @ApiModelProperty("本季度待交付")
    private BigDecimal current_quarter_executed_un_delivery;

    @ApiModelProperty("上季度本季度待交付（变更为历史季度）")
    private BigDecimal last_quarter_executed_un_delivery;

    @ApiModelProperty("上季度本季度待交付 + 历史未关帐")
    private BigDecimal history_executed_un_delivery;

    @ApiModelProperty("历史未关帐")
    private BigDecimal history_unclosed;

    @ApiModelProperty("已下单待执行")
    private BigDecimal ordered_un_execute;
    @ApiModelProperty("已预订待审核")
    private BigDecimal reserved_un_audit;
    @ApiModelProperty("已预订待提审")
    private BigDecimal reserved_un_push;
    @ApiModelProperty("已录点位未过审")
    private BigDecimal recorded_un_audit;

    @ApiModelProperty("已下单未录点位")
    private BigDecimal ordered_un_record;
    /**
     * 商单未录入金额total
     */
    private BigDecimal pick_up_un_record;
    /**
     * 品牌未录入金额total
     */
    private BigDecimal brand_un_record;
    @ApiModelProperty("下单预估(系统可见)")
    private BigDecimal order_forecast;
    @ApiModelProperty("下单预估(当季下单)")
    private BigDecimal current_quarter_order_forecast;
    @ApiModelProperty("up主产品-下单待执行")
    private BigDecimal up_order_un_executed;
    @ApiModelProperty("up主产品已下单预估(近期订单)")
    private BigDecimal up_order_forecast;
    @ApiModelProperty("up主产品已下单预估(历史累计)")
    private BigDecimal history_up_order_forecast;
}
