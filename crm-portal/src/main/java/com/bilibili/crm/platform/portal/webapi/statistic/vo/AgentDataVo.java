package com.bilibili.crm.platform.portal.webapi.statistic.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * Created by cuihaichuan on 2017/11/14.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgentDataVo {

    @ExcelResources(title = "日期")
    @ApiModelProperty("日期")
    private String date;

    @ExcelResources(title = "代理商ID")
    @ApiModelProperty("代理商账号ID")
    private Integer sys_agent_id;

    @ExcelResources(title = "代理商名称")
    @ApiModelProperty("代理商名称")
    private String agent_name;

    @ExcelResources(title = "代理商客户总数")
    @ApiModelProperty("代理商客户总数")
    private Long agent_customer_count;

    @ExcelResources(title = "充值(元)")
    @ApiModelProperty("充值")
    private BigDecimal recharge;

    @ExcelResources(title = "返货(元)")
    @ApiModelProperty("返货")
    private BigDecimal back_red_packet;

    @ExcelResources(title = "现金转入(元)")
    @ApiModelProperty("现金转入")
    private BigDecimal cash_turn_in;

    @ExcelResources(title = "现金转出(元)")
    @ApiModelProperty("现金转出")
    private BigDecimal cash_turn_out;

    @ExcelResources(title = "返货转入(元)")
    @ApiModelProperty("返货转入")
    private BigDecimal red_packet_turn_in;

    @ExcelResources(title = "返货转出(元)")
    @ApiModelProperty("返货转出")
    private BigDecimal red_packet_turn_out;

    @ExcelResources(title = "现金退款(元)")
    @ApiModelProperty("现金退款")
    private BigDecimal cash_refund;

    @ExcelResources(title = "返货退款(元)")
    @ApiModelProperty("返货退款")
    private BigDecimal red_packet_refund;

    @ExcelResources(title = "现金消耗(元)")
    @ApiModelProperty("现金消耗")
    private BigDecimal cash_consume;

    @ExcelResources(title = "返货消耗(元)")
    @ApiModelProperty("返货消耗")
    private BigDecimal red_packet_consume;

    @ExcelResources(title = "现金反作弊(元)")
    @ApiModelProperty("现金反作弊")
    private BigDecimal cash_cheat;

    @ExcelResources(title = "返货反作弊(元)")
    @ApiModelProperty("返货反作弊")
    private BigDecimal red_packet_cheat;

    @ExcelResources(title = "现金余额(元)")
    @ApiModelProperty("现金余额")
    private BigDecimal cash_balance;

    @ExcelResources(title = "返货余额(元)")
    @ApiModelProperty("返货余额")
    private BigDecimal red_packet_balance;

    @ExcelResources(title = "活跃客户数")
    @ApiModelProperty("活跃客户数")
    private Integer active_customer_count;

    @ExcelResources(title = "最终现金消耗")
    @ApiModelProperty("最终现金消耗")
    private BigDecimal final_cash_consume;

    @ExcelResources(title = "最终返货消耗")
    @ApiModelProperty("最终返货消耗")
    private BigDecimal final_red_packet_consume;

    @ExcelResources(title = "最终总体消耗")
    @ApiModelProperty("最终总体消耗")
    private BigDecimal final_consume;

    @ApiModelProperty("一级行业")
    private String first_category;

    @ApiModelProperty("二级行业")
    private String second_category;

    @ExcelResources(title = "所属部门")
    @ApiModelProperty("所属部门")
    private String department_name;

    @ExcelResources(title = "统一一级行业")
    @ApiModelProperty("统一一级行业")
    private String united_first_industry;

    @ExcelResources(title = "统一二级行业")
    @ApiModelProperty("统一二级行业")
    private String united_second_industry;

    @ExcelResources(title = "统一三级行业")
    @ApiModelProperty("统一三级行业")
    private String united_third_industry;
}
