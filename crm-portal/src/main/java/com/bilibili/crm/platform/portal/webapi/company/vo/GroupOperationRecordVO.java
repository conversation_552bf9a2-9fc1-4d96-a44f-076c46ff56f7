package com.bilibili.crm.platform.portal.webapi.company.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2025/1/20 15:31.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupOperationRecordVO implements Serializable {

    private static final long serialVersionUID = -9166224032809185285L;

    @ApiModelProperty(notes = "记录ID")
    private Long id;
    @ApiModelProperty(notes = "集团ID")
    private Integer groupId;
    @ApiModelProperty(notes = "操作类型")
    private String modifyType;
    @ApiModelProperty(notes = "操作人域账号")
    private String operatorName;
    @ApiModelProperty(notes = "操作时间")
    private String ctime;
    @ApiModelProperty(notes = "操作内容")
    private String content;

}

