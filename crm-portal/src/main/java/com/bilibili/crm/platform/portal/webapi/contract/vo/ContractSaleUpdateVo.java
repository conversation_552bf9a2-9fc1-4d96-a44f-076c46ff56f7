package com.bilibili.crm.platform.portal.webapi.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractSaleUpdateVo {

    @ApiModelProperty("合同ID")
    private Integer contract_id;

    @ApiModelProperty("直客销售ID")
    private List<Integer> direct_sale_ids;

    @ApiModelProperty("渠道销售ID")
    private List<Integer> channel_sale_ids;
    @ApiModelProperty("追款直客销售ID")
    private List<Integer> substitute_direct_sale_ids;

    @ApiModelProperty("追款渠道销售ID")
    private List<Integer> substitute_channel_sale_ids;
}
