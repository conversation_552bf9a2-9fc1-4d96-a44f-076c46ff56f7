package com.bilibili.crm.platform.portal.webapi.mock;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.controller.BaseController;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.account.dto.*;
import com.bilibili.crm.platform.api.account.service.IAccountLabelService;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountLabelDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountLabelMappingDao;
import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPo;
import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPoExample;
import com.bilibili.crm.platform.biz.po.AccAccountLabelPo;
import com.bilibili.crm.platform.biz.po.AccAccountLabelPoExample;
import com.bilibili.crm.platform.biz.service.account.label.AccountLabelRuleService;
import com.bilibili.crm.platform.common.AccountAutoUpdateLabel;
import com.bilibili.crm.platform.common.AccountStatus;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.account.label.AccountLabelMappingType;
import com.bilibili.crm.platform.portal.webapi.mock.service.WebAccountLabelService;
import com.bilibili.crm.platform.portal.webapi.mock.vo.*;
import com.google.common.collect.Lists;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;
import springfox.documentation.annotations.ApiIgnore;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2019/5/8
 **/

@RestController
@RequestMapping("/web_api/v1/mock/label")
public class LabelMockController extends BaseController {

    private static final Logger logger = LoggerFactory.getLogger(LabelMockController.class);

    @Autowired
    private IAccountLabelService accountLabelService;
    @Autowired
    private WebAccountLabelService webAccountLabelService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private AccAccountLabelMappingDao accAccountLabelMappingDao;
    @Autowired
    private AccAccountLabelDao accAccountLabelDao;
    @Autowired
    private AccountLabelRuleService accountLabelRuleService;

    @RequestMapping(value = "/query_account", method = RequestMethod.GET)
    public Response<Pagination<List<AccountLabelViewVo>>> queryAccount(QueryAccountLabelVo queryVo) {

        QueryAccountLabelDto queryParam = webAccountLabelService.vo2Dto(queryVo);
        PageResult<AccountLabelViewDto> result = accountLabelService.queryAccount(queryParam);
        List<AccountLabelViewVo> vos = webAccountLabelService.dto2Vo(result.getRecords());
        return Response.SUCCESS(new Pagination<>(queryVo.getPage(), result.getTotal(), vos));
    }

    @RequestMapping(value = "/binding", method = RequestMethod.PUT)
    public Response<Object> binding(@ApiIgnore Context context, @RequestBody LabelBindingVo bindingVo) throws Exception {

        Context ctx = new Context(10003, "产品的账户", 1, "");
        LabelBindingDto bindingDto = webAccountLabelService.labelBindingVo2Dto(bindingVo);
        accountLabelService.bindAccountLabel(getOperator(ctx), bindingDto, ModifyType.ACCOUNT_LABEL_MANUALLY);
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "查询帐号标签的绑定详情")
    @RequestMapping(value = "/query_account/{accountId}", method = RequestMethod.GET)
    public Response<LabelBindingVo> getBean(@PathVariable("accountId") Integer accountId) throws Exception {

        LabelBindingDto bindingDto = accountLabelService.getBindingInfoByAccountId(accountId);
        LabelBindingVo vo = webAccountLabelService.dto2Vo(bindingDto);
        return Response.SUCCESS(vo);
    }

    @ApiOperation(value = "获取所有标签列表")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Response<List<AccountLabelVo>> getAllLabels(@ApiIgnore Context context) throws Exception {

        List<AccountLabelBaseDto> allLabels = accountLabelService.getAllLabels();
        List<AccountLabelVo> vos = webAccountLabelService.labelDto2Vo(allLabels);
        return Response.SUCCESS(vos);
    }

    @ApiOperation(value = "获取帐号匹配的标签列表")
    @RequestMapping(value = "/matched/{accountId}", method = RequestMethod.GET)
    public Response<AccountLabelMatchingVo> getMatchedLabel(@PathVariable("accountId") Integer accountId) throws Exception {

        AccountLabelMatchingDto matchingDto = accountLabelService.getMatchedLabelByAccountId(accountId);
        AccountLabelMatchingVo vo = webAccountLabelService.dto2Vo(matchingDto);
        return Response.SUCCESS(vo);
    }

    @ApiOperation(value = "获取帐号绑定标签的日志")
    @RequestMapping(value = "/query_log/{accountId}", method = RequestMethod.GET)
    public Response<Pagination<List<QueryAccountLabelLogVo>>> queryLog(@PathVariable("accountId") Integer accountId,
       @RequestParam(value = "page", defaultValue = "1", required = false) @ApiParam("页码") Integer page,
       @RequestParam(value = "size", defaultValue = "15", required = false) @ApiParam("页长") Integer size) throws ServiceException {

        PageResult<QueryAccountLabelLogDto> result = accountLabelService.queryLog(accountId, page, size);
        List<QueryAccountLabelLogVo> vos = webAccountLabelService.logDto2Vo(result.getRecords());
        return Response.SUCCESS(new Pagination<>(page, result.getTotal(), vos));
    }

    @ApiOperation(value = "刷新所有帐号的标签绑定关系")
    @RequestMapping(value = "/refresh_all_account_label", method = RequestMethod.GET)
    public Response<Object> refreshAllAccountLabel(@RequestParam(value = "page", required = false) Integer page) throws Exception {

        Context ctx = new Context(3, "商业产品专用测试账号", 1, "");
        Operator operator = getOperator(ctx);
        Integer size = 100;
        PageResult<AccountBaseDto> result = queryAccountService.queryAccount(QueryAccountParam.builder()
                .statusList(Lists.newArrayList(AccountStatus.ON.getCode()))
                .build(), page, size);

        List<AccountBaseDto> accountBaseDtos = result.getRecords();

        if (CollectionUtils.isEmpty(accountBaseDtos)) {
            return null;
        }
        accountBaseDtos.forEach(baseDto -> {
            AccountDto accountDto = AccountDto.builder().build();
            BeanUtils.copyProperties(baseDto, accountDto);

            List<AccountLabelBaseDto> matchedLabels = accountLabelService.getMatchedLabelByAccount(accountDto);
            if (!CollectionUtils.isEmpty(matchedLabels)) {
                LabelBindingDto bind = LabelBindingDto.builder()
                        .accountId(accountDto.getAccountId())
                        .labelIds(matchedLabels.stream().map(AccountLabelBaseDto::getId).collect(Collectors.toList()))
                        .autoUpdate(AccountAutoUpdateLabel.ON.getCode())
                        .build();
                accountLabelService.bindAccountLabel(operator, bind, ModifyType.ACCOUNT_LABEL_MANUALLY);
            }
        });

        return Response.SUCCESS(accountBaseDtos.size());
    }

    @ApiOperation(value = "更新标签的状态")
    @RequestMapping(value = "/update_status", method = RequestMethod.GET)
    public Response<String> updateLabelStatus(@RequestParam(value = "labelId", required = false) Integer labelId, @RequestParam(value = "status", required = false) Integer status) {
        accAccountLabelDao.updateByPrimaryKeySelective(AccAccountLabelPo.builder().id(labelId).status(status).build());
        return Response.SUCCESS("ok");
    }


    @ApiOperation(value = "硬删某标签下的mapping关系")
    @RequestMapping(value = "/delete/mapping_opt/{labelId}", method = RequestMethod.GET)
    public Response<Object> deleteMappingByLabelIdOpt(@PathVariable("labelId") Integer labelId,
                                                      @ApiParam("begin") Timestamp begin,
                                                      @ApiParam("end") Timestamp end,
                                                      @ApiParam("startId") Integer startId,
                                                      @ApiParam("update_mapping_mode") Integer updateMappingMode
    ) {
        if (!Utils.isPositive(labelId)) {
            return Response.SUCCESS(null);
        }
        Integer offset = startId;
        while (true) {
            try {
                AccAccountLabelMappingPoExample mappingPoExample = new AccAccountLabelMappingPoExample();

                AccAccountLabelMappingPoExample.Criteria criteria = mappingPoExample.or();
                criteria.andLabelIdEqualTo(labelId)
                        .andCtimeGreaterThanOrEqualTo(begin)
                        .andCtimeLessThanOrEqualTo(end)
                        .andIdGreaterThanOrEqualTo(offset);

                if (!Objects.isNull(updateMappingMode) && !Objects.equals(updateMappingMode, IsValid.TRUE.getCode())) {
                    criteria.andMappingTypeEqualTo(AccountLabelMappingType.AUTO.getCode());
                }
                mappingPoExample.setLimit(1000);
                List<AccAccountLabelMappingPo> pos = accAccountLabelMappingDao.selectByExample(mappingPoExample);
                if (CollectionUtils.isEmpty(pos)) {
                    return Response.SUCCESS("ok");
                }
                logger.info("labelId {} , offset {}", labelId, offset);
                offset = pos.get(pos.size() - 1).getId();
                pos.forEach(accAccountLabelMappingPo -> accAccountLabelMappingDao.deleteByPrimaryKey(accAccountLabelMappingPo.getId()));
            } catch (Exception e) {
                logger.error("error", e);
                return Response.SUCCESS("error");
            }
        }
    }

    @ApiOperation(value = "硬删某标签下的mapping关系")
    @RequestMapping(value = "/delete/mapping/{labelId}", method = RequestMethod.GET)
    public Response<Object> deleteMappingByLabelId(@PathVariable("labelId") Integer labelId) {
        if(!Utils.isPositive(labelId)){
            return Response.SUCCESS(null);
        }
        AccAccountLabelMappingPoExample mappingPoExample = new AccAccountLabelMappingPoExample();
        mappingPoExample.or().andLabelIdEqualTo(labelId);
        List<AccAccountLabelMappingPo> pos  = accAccountLabelMappingDao.selectByExample(mappingPoExample);
        if(CollectionUtils.isEmpty(pos)){
            return Response.SUCCESS("ok");
        }

        pos.forEach(accAccountLabelMappingPo -> accAccountLabelMappingDao.deleteByPrimaryKey(accAccountLabelMappingPo.getId()));
        return Response.SUCCESS("ok");
    }

    @ApiOperation(value = "判断打标方式")
    @RequestMapping(value = "/judge/mapping_type", method = RequestMethod.GET)
    public Response<Object> judgeMappingType() {
        List<AccAccountLabelPo> labelPos = accAccountLabelDao.selectByExampleWithBLOBs(new AccAccountLabelPoExample());
        if(CollectionUtils.isEmpty(labelPos)){
            return Response.SUCCESS(null);
        }

        labelPos.forEach(labelPo ->{
            if(StringUtils.isEmpty(labelPo.getLabelRule())){
                return;
            }
            AccountLabelRuleDto ruleDto = JSON.parseObject(labelPo.getLabelRule(),AccountLabelRuleDto.class);
            //标签匹配到的账号
            List<Integer> matchAccountIds = accountLabelRuleService.matchAccountByLabelRule(ruleDto, null);

            //标签已经绑定的账号
            AccAccountLabelMappingPoExample example = new AccAccountLabelMappingPoExample();
            example.or().andLabelIdEqualTo(labelPo.getId());
            List<AccAccountLabelMappingPo> mappingPos = accAccountLabelMappingDao.selectByExample(example);

            //第一种情况：该标签没有与账号的映射关系
            if(CollectionUtils.isEmpty(mappingPos)) {
                return;
            }

            //第二种情况：该标签没有匹配到账号，目前的映射关系都为手动更新
            if(CollectionUtils.isEmpty(matchAccountIds)){
                accAccountLabelMappingDao.updateByExampleSelective(AccAccountLabelMappingPo.builder().mappingType(AccountLabelMappingType.MANUAL.getCode()).build(), example);
                return;
            }

            //第三种情况：匹配到的与已有绑定关系的交集
            List<Integer> mappingAccountIds = mappingPos.stream().map(AccAccountLabelMappingPo::getAccountId).collect(Collectors.toList());

            List<Integer> intersectionAccountId = new ArrayList<>(mappingAccountIds);
            intersectionAccountId.retainAll(matchAccountIds);
            if(!CollectionUtils.isEmpty(intersectionAccountId)){
                AccAccountLabelMappingPoExample mappingPoExample = new AccAccountLabelMappingPoExample();
                mappingPoExample.or().andLabelIdEqualTo(labelPo.getId()).andAccountIdIn(intersectionAccountId);
                accAccountLabelMappingDao.updateByExampleSelective(AccAccountLabelMappingPo.builder().mappingType(AccountLabelMappingType.AUTO.getCode()).build(), mappingPoExample);
            }

            //第四种情况：已有绑定关系中有，匹配中无的部分，打手动
            mappingAccountIds.removeAll(matchAccountIds);
            if(!CollectionUtils.isEmpty(mappingAccountIds)){
                AccAccountLabelMappingPoExample mappingExample = new AccAccountLabelMappingPoExample();
                mappingExample.or().andLabelIdEqualTo(labelPo.getId()).andAccountIdIn(mappingAccountIds);
                accAccountLabelMappingDao.updateByExampleSelective(AccAccountLabelMappingPo.builder().mappingType(AccountLabelMappingType.MANUAL.getCode()).build(), mappingExample);
            }
        });

        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "给某账号重绑标签")
    @RequestMapping(value = "/bind", method = RequestMethod.GET)
    public Response<String> bind(@RequestParam(value = "start_id", required = false, defaultValue = "") Integer startId,
                                 @RequestParam(value = "end_id", required = false, defaultValue = "") Integer endId) throws Exception {
        for(Integer accountId = startId; accountId <= endId; accountId++){
            // 查询账号匹配的标签
            AccountLabelMatchingDto matchedLabel = accountLabelService.getMatchedLabelByAccountId(accountId);
            if (Objects.isNull(matchedLabel) || CollectionUtils.isEmpty(matchedLabel.getMatchedLabels())) {
                return Response.SUCCESS("");
            }
            // 生成绑定关系
            List<Integer> labelIds = matchedLabel.getMatchedLabels().stream().map(AccountLabelBaseDto::getId).collect(Collectors.toList());
            LabelBindingDto bindingDto = LabelBindingDto.builder()
                    .accountId(accountId)
                    .labelIds(labelIds)
                    .autoUpdate(matchedLabel.getAutoUpdate() == null ? AccountAutoUpdateLabel.ON.getCode() : matchedLabel.getAutoUpdate())
                    .build();
            // 保存账号标签绑定关系
            accountLabelService.bindAccountLabel(Operator.SYSTEM, bindingDto, ModifyType.ACCOUNT_LABEL_BIND_AUTO);
        }

        return Response.SUCCESS("");
    }
}
