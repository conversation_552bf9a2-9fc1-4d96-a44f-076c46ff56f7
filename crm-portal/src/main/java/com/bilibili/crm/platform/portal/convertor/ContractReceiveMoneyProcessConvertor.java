package com.bilibili.crm.platform.portal.convertor;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.automation.ContractReceiveMoneyProcessDto;
import com.bilibili.crm.platform.api.finance.dto.automation.ContractReceiveMoneyProcessItemDto;
import com.bilibili.crm.platform.api.finance.dto.automation.ContractReceiveMoneyYearProcessDto;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.ContractReceiveMoneyProcessItemVo;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.ContractReceiveMoneyProcessVo;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.ContractReceiveMoneyYearProcessVo;

/**
 * <AUTHOR>
 * @date 2021/7/27 下午5:20
 */
public class ContractReceiveMoneyProcessConvertor {

    public static ContractReceiveMoneyProcessVo convertReceiveMoneyProcessDto2Vo(ContractReceiveMoneyProcessDto contractReceiveMoneyProcessDto) {
        ContractReceiveMoneyProcessVo receiveMoneyProcessVo = ContractReceiveMoneyProcessVo.builder().build();
        if (contractReceiveMoneyProcessDto == null) {
            return receiveMoneyProcessVo;
        }
        receiveMoneyProcessVo.setLatest_update_time(contractReceiveMoneyProcessDto.getLatestUpdateTime());
        receiveMoneyProcessVo.setCur_year_process(convertYearProcessDto2Vo(contractReceiveMoneyProcessDto.getCurYearProcess()));
        receiveMoneyProcessVo.setPre_year_process(convertYearProcessDto2Vo(contractReceiveMoneyProcessDto.getPreYearProcess()));
        return receiveMoneyProcessVo;
    }

    private static ContractReceiveMoneyYearProcessVo convertYearProcessDto2Vo(ContractReceiveMoneyYearProcessDto curYearProcess) {
        ContractReceiveMoneyYearProcessVo receiveMoneyYearProcessVo = ContractReceiveMoneyYearProcessVo.builder().build();
        if (curYearProcess == null) {
            return receiveMoneyYearProcessVo;
        }
        receiveMoneyYearProcessVo.setYear(curYearProcess.getYear());
        receiveMoneyYearProcessVo.setWhole_process(convertProcessItemDto2Vo(curYearProcess.getWholeProcess()));
        receiveMoneyYearProcessVo.setHas_billed_process(convertProcessItemDto2Vo(curYearProcess.getHasBilledProcess()));
        receiveMoneyYearProcessVo.setHas_billed_rebate_process(convertProcessItemDto2Vo(curYearProcess.getHasBilledRebateProcess()));
        return receiveMoneyYearProcessVo;
    }

    private static ContractReceiveMoneyProcessItemVo convertProcessItemDto2Vo(ContractReceiveMoneyProcessItemDto contractReceiveMoneyProcessItemDto) {
        ContractReceiveMoneyProcessItemVo receiveMoneyProcessItemVo =
                ContractReceiveMoneyProcessItemVo.builder().build();
        if (contractReceiveMoneyProcessItemDto == null) {
            return receiveMoneyProcessItemVo;
        }
        receiveMoneyProcessItemVo.setAll_data(Utils.fromFenToYuan(contractReceiveMoneyProcessItemDto.getAllData()));
        receiveMoneyProcessItemVo.setCompleted_data(Utils.fromFenToYuan(contractReceiveMoneyProcessItemDto.getCompletedData()));
        receiveMoneyProcessItemVo.setRate(contractReceiveMoneyProcessItemDto.getRate());
        return receiveMoneyProcessItemVo;
    }
}
