package com.bilibili.crm.platform.portal.webapi.finance.vo.transfer;

import com.bilibili.crm.platform.portal.webapi.contract.vo.CommonAttachmentUploadResultVo;
import com.bilibili.crm.platform.portal.webapi.contract.vo.CommonAttachmentUploadVo;
import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/7/9 3:19 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class TransferVo {
    @ApiModelProperty("id")
    private Integer id;

    @ApiModelProperty("流水号")
    private String serial_number;

    @ApiModelProperty("转出账号ID")
    private Integer out_account_id;

    @ApiModelProperty("转出账号名称")
    private String out_account_name;

    @ApiModelProperty("转入账号ID")
    private Integer in_account_id;

    @ApiModelProperty("转入账号名称")
    private String in_account_name;

    @ApiModelProperty("转入金额,单位(元)")
    private BigDecimal amount;

    @ApiModelProperty("转入账户类型 1-现金账号 2-返货账户")
    private Integer account_finance_type;

    @ApiModelProperty("转入账户类型 1-现金账号 2-返货账户")
    private String account_finance_type_desc;

    @ApiModelProperty("备注")
    private String remark;

    @ApiModelProperty("状态")
    private Integer audit_status;

    @ApiModelProperty("状态描述")
    private String audit_status_desc;

    @ApiModelProperty("审核人")
    private String auditor;

    @ApiModelProperty("审核时间")
    private String processing_ime;

    @ApiModelProperty("提交人")
    private String creator;

    @ApiModelProperty("提交时间")
    private String ctime;

    @ApiModelProperty("驳回理由")
    private String reject_reason;

    @ApiModelProperty("转户附件")
    private List<CommonAttachmentUploadResultVo> attachment_vo;
}
