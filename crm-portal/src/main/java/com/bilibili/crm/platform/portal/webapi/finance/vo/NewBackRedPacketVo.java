package com.bilibili.crm.platform.portal.webapi.finance.vo;

import java.math.BigDecimal;
import java.util.List;

import javax.validation.constraints.NotNull;

import com.fasterxml.jackson.annotation.JsonInclude;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * Created by user on 2017/8/10.
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class NewBackRedPacketVo {

    @ApiModelProperty("返货类型: 1-系统自动返货 2-人工返货 3-特批返货")
    @NotNull(message = "返货类型不可为空")
    private Integer back_type;

    @ApiModelProperty("待返货的账号id")
    @NotNull(message = "待返货的账号id不可为空")
    private Integer account_id;

    @ApiModelProperty("充值流水号")
    private String recharge_serial_number;

    @ApiModelProperty("特批邮件列表")
    private List<EmailScreenshotVo> emails;

    @ApiModelProperty("返货金额,单位(元)")
    @NotNull(message = "返货金额不可为空")
    private BigDecimal amount;

    @ApiModelProperty("备注")
    private String remark;
    
    @ApiModelProperty("返货归属日期")
    private Long belong_date;

    @ApiModelProperty("适用返货政策 1:一返, 2:二返, 3:三返, 4:其他, 5:专项返货(仅特批返货时可用)")
    private Integer back_policy_type;
}
