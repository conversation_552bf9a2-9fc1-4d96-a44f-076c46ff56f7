package com.bilibili.crm.platform.portal.webapi.customer.vo;

import com.bilibili.crm.platform.portal.webapi.achievement.vo.AchievementRtbQuotaVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;


@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AgentPortraitTableCardConfirmVo {

    @ApiModelProperty("总计")
    private BigDecimal total;

    @ApiModelProperty("合约广告已确认")
    private BigDecimal contract_closed;

    @ApiModelProperty("合约广告待确认")
    private BigDecimal contract_unclosed;

    @ApiModelProperty("花火商单")
    private BigDecimal pick_order;

    @ApiModelProperty("cpc+cpm")
    private BigDecimal cpc_cpm;

    @ApiModelProperty("dpa")
    private BigDecimal dpa;

    @ApiModelProperty("adx")
    private BigDecimal adx;

    @ApiModelProperty("商业起飞")
    private BigDecimal biz_fly;

    @ApiModelProperty("内容起飞")
    private BigDecimal content_fly;

    @ApiModelProperty("个人起飞")
    private BigDecimal person_fly;

    public static AgentPortraitTableCardConfirmVo empty(){
        return AgentPortraitTableCardConfirmVo.builder()
                .total(BigDecimal.ZERO)
                .contract_closed(BigDecimal.ZERO)
                .contract_unclosed(BigDecimal.ZERO)
                .pick_order(BigDecimal.ZERO)
                .cpc_cpm(BigDecimal.ZERO)
                .dpa(BigDecimal.ZERO)
                .adx(BigDecimal.ZERO)
                .biz_fly(BigDecimal.ZERO)
                .person_fly(BigDecimal.ZERO)
                .content_fly(BigDecimal.ZERO)
                .build();
    }
}
