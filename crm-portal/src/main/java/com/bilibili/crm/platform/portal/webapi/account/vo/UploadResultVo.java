package com.bilibili.crm.platform.portal.webapi.account.vo;

import java.util.Map;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2017年2月27日
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class UploadResultVo {
    @ApiModelProperty(notes="图片URL地址")
    private String url;
    @ApiModelProperty(notes="hash值")
    private String hash;
    @ApiModelProperty(notes="token")
    private String token;
}
