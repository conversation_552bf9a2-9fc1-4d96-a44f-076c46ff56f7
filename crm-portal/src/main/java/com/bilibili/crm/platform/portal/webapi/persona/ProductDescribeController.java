package com.bilibili.crm.platform.portal.webapi.persona;

import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.effect.enums.ReportColumnBehavior;
import com.bilibili.crm.platform.biz.annotation.Export;
import com.bilibili.crm.platform.portal.common.ExportUtils;
import com.bilibili.crm.platform.portal.common.UploadCenterUtil;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.company.vo.ProductInfoExportVo;
import com.bilibili.crm.platform.portal.webapi.download.coverter.DownloadUtil;
import com.bilibili.crm.platform.portal.webapi.persona.service.WebPersonaService;
import com.bilibili.crm.platform.portal.webapi.persona.service.WebProductDescribeService;
import com.bilibili.crm.platform.portal.webapi.persona.vo.*;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import lombok.extern.slf4j.Slf4j;
import java.util.function.Supplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/3/22 11:59 上午
 */
@Slf4j
@RestController
@RequestMapping("/web_api/v1/persona/product")
@Api(value = "/persona/product", description = "产品画像")
public class ProductDescribeController extends BaseRestfulController {

    @Autowired
    private ExportUtils exportUtils;
    @Autowired
    private WebPersonaService webPersonaService;
    @Autowired
    private WebProductDescribeService webProductDescribeService;

    @Resource
    private UploadCenterUtil uploadCenterUtil;

    @ApiOperation(value = "基本信息")
    @RequestMapping(value = "/base_info", method = RequestMethod.GET)
    public Response<ProductPersonaBaseInfoVo> queryBsiPickup(@ApiIgnore Context context, QueryPersonaVo queryVo) {

        ProductPersonaBaseInfoVo baseInfo = webProductDescribeService.getBaseInfo(queryVo, super.getOperator(context));
        return Response.SUCCESS(baseInfo);
    }

    @ApiOperation(value = "产品消费时间")
    @RequestMapping(value = "/consume_date", method = RequestMethod.GET)
    public Response<PersonaProductVo> getProductConsumeDateInfo(@ApiIgnore Context context, QueryPersonaVo queryVo) {

        PersonaProductVo personaCostTime = webPersonaService.getPersonaCostTime(queryVo, super.getOperator(context));
        return Response.SUCCESS(personaCostTime);
    }


    @ApiOperation(value = "客户-合作详情")
    @RequestMapping(value = "/cooperate", method = RequestMethod.GET)
    public Response<PersonaCooperateVo> getCooperateInfo(@ApiIgnore Context context, QueryPersonaCooperateVo queryVo) {

        PersonaCooperateVo cooperateInfo = webPersonaService.getCooperateInfo(queryVo, super.getOperator(context));
        return Response.SUCCESS(cooperateInfo);
    }

    @ApiOperation(value = "硬广折扣力度")
    @RequestMapping(value = "/hard_ad", method = RequestMethod.GET)
    public Response<PersonaBrandHardVo> getHardDiscount(@ApiIgnore Context context, QueryPersonaCooperateVo queryVo) {

        PersonaBrandHardVo hardDiscount = webPersonaService.getHardDiscount(queryVo, super.getOperator(context));
        return Response.SUCCESS(hardDiscount);
    }

    @ApiOperation(value = "导出明细")
    @Export
    @RequestMapping(value = "/export", method = RequestMethod.GET)
    public Response<String> exportDetail(@ApiIgnore Context context, QueryPersonaCooperateVo queryVo) {

        Supplier<List<PersonaExportBeanVo>> supplier = () -> {
            try {
                List<PersonaExportBeanVo> personaReport = webPersonaService.getPersonaReport(queryVo, super.getOperator(context));
                return personaReport;
            } catch (Exception e) {
                log.error("error", e);
            }
            return Collections.EMPTY_LIST;
        };

        try {
            String fileName = DownloadUtil.generateFileName(queryVo.getDate_begin() == null ? null : new Timestamp(queryVo.getDate_begin()), queryVo.getDate_end() == null ? null : new Timestamp(queryVo.getDate_end()), ReportColumnBehavior.CORPORATION_PRODUCT_EXPORT);
            uploadCenterUtil.uploadToCenterWithEasyExcel(getOperator(context), PersonaExportBeanVo.class, supplier, Collections.emptyList(), fileName, ReportColumnBehavior.CORPORATION_PRODUCT_EXPORT);
        } catch (Exception e) {
            log.error("corporation_product_export_error", e);
            return Response.SUCCESS("品牌导出数据失败");
        }
        return Response.SUCCESS(DownloadUtil.return_str);

    }

}
