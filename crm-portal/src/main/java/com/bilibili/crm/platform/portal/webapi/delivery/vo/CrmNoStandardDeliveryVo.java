package com.bilibili.crm.platform.portal.webapi.delivery.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * description：订单交付件详情
 * date       ：2021/2/4 2:32 下午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrmNoStandardDeliveryVo {

    @ExcelResources(title = "交付件id")
    @ApiModelProperty("交付件id")
    private Integer id;

    @ExcelResources(title = "应交付日期")
    @ApiModelProperty("应交付日期")
    private String deliver_date;

    @ExcelResources(title = "交付件状态")
    @ApiModelProperty("交付件状态（0：等待中，1：待审核，2：审核通过，3：审核拒绝，4：废弃。默认为0）")
    private String status;

    @ExcelResources(title = "关联订单id")
    @ApiModelProperty("关联订单id")
    private Integer order_id;

    @ExcelResources(title = "关联订单名称")
    @ApiModelProperty("关联订单名称")
    private String order_name;

    @ExcelResources(title = "计收方式")
    @ApiModelProperty("计收方式")
    private String settle_type;

    @ExcelResources(title = "关联订单类型")
    @ApiModelProperty("关联订单类型")
    private String order_type;

    @ExcelResources(title = "订单刊例价")
    @ApiModelProperty("订单刊例价")
    private BigDecimal amount;

    @ExcelResources(title = "订单实收金额")
    @ApiModelProperty("订单实收金额")
    private BigDecimal discount_price;

    @ExcelResources(title = "订单资源类型")
    @ApiModelProperty("订单资源类型")
    private String resouce_type;

    @ExcelResources(title = "非标一级产品")
    @ApiModelProperty("非标一级产品名称")
    private String first_product_name;

    @ExcelResources(title = "非标二级产品")
    @ApiModelProperty("非标二级产品名称")
    private String second_product_name;

    @ExcelResources(title = "项目/节目类型")
    @ApiModelProperty("项目/节目类型")
    private String project_item_name;

    @ExcelResources(title = "资源提供方")
    @ApiModelProperty("资源提供方")
    private String order_department_name;

    @ExcelResources(title = "合同id")
    @ApiModelProperty("合同id")
    private Integer crm_contract_id;

    @ExcelResources(title = "合同号")
    @ApiModelProperty("合同号")
    private String contract_number;

    @ExcelResources(title = "合同名称")
    @ApiModelProperty("合同名称")
    private String contract_name;

    @ExcelResources(title = "合同开始时间")
    @ApiModelProperty("合同开始时间")
    private String contract_begin_time;

    @ExcelResources(title = "合同结束时间")
    @ApiModelProperty("合同结束时间")
    private String contract_end_time;

    @ExcelResources(title = "合同类型")
    @ApiModelProperty("合同类型")
    private String contract_type;

    @ExcelResources(title = "合同状态")
    @ApiModelProperty("合同状态")
    private String contract_status;

    @ExcelResources(title = "合同打包价")
    @ApiModelProperty("合同打包价")
    private BigDecimal contract_amount;

    @ExcelResources(title = "合同项目名称")
    @ApiModelProperty("合同项目名称")
    private String contract_project_name;

    @ExcelResources(title = "合同存档状态")
    @ApiModelProperty("合同存档状态")
    private String contract_archive_status;

    @ExcelResources(title = "集团名称")
    @ApiModelProperty("集团名称")
    private String group_name;

    @ExcelResources(title = "产品线名称")
    @ApiModelProperty("产品线名称")
    private String product_line_name;

    @ExcelResources(title = "品牌名称")
    @ApiModelProperty("品牌名称")
    private String product_name;

    @ExcelResources(title = "客户id")
    @ApiModelProperty("客户id")
    private Integer account_id;

    @ExcelResources(title = "客户名称")
    @ApiModelProperty("客户名称")
    private String account_name;

    @ExcelResources(title = "公司名称")
    @ApiModelProperty("公司名称")
    private String company_name;

    @ExcelResources(title = "所属部门")
    @ApiModelProperty("所属部门")
    private String department_name;

    @ExcelResources(title = "代理商id")
    @ApiModelProperty("代理商id")
    private Integer agent_id;

    @ExcelResources(title = "代理商名称")
    @ApiModelProperty("代理商名称")
    private String agent_name;

    @ExcelResources(title = "销售员")
    @ApiModelProperty("销售员")
    private List<String> saleMan;

    @ExcelResources(title = "合同创建人")
    @ApiModelProperty("合同创建人")
    private String contract_creator;

    @ExcelResources(title = "项目执行")
    @ApiModelProperty("项目执行")
    private String exectue_name;

    @ExcelResources(title = "up主mid")
    @ApiModelProperty("up主mid")
    private Long mid;

    @ExcelResources(title = "up主昵称")
    @ApiModelProperty("up主昵称")
    private String upper_name;

    @ExcelResources(title = "统一一级行业分类")
    @ApiModelProperty("统一一级行业分类")
    private String united_first_industry_name;

    @ExcelResources(title = "统一二级行业分类")
    @ApiModelProperty("统一二级行业分类")
    private String united_second_industry_name;

    @ExcelResources(title = "统一三级行业分类")
    @ApiModelProperty("统一三级行业分类")
    private String united_third_industry_name;
}
