package com.bilibili.crm.platform.portal.opentelemetry;

import com.bilibili.warp.spring.boot.autoconfigure.web.WarpHttpServerInterceptor;
import io.opentelemetry.api.trace.Span;
import lombok.RequiredArgsConstructor;
import org.springframework.beans.BeansException;
import org.springframework.beans.factory.config.BeanPostProcessor;
import org.springframework.context.ApplicationContext;
import org.springframework.remoting.httpinvoker.HttpInvokerServiceExporter;
import org.springframework.remoting.support.RemoteInvocation;
import org.springframework.util.ReflectionUtils;
import org.springframework.web.method.HandlerMethod;

import javax.annotation.Nonnull;
import javax.servlet.ServletException;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;

/**
 * <NAME_EMAIL> on 2024/11/27.
 *
 * 替换HttpInvokerServiceExporter，使其接入OpenTelemetry
 *
 * 偷懒用warp的拦截器实现OpenTelemetry接入
 *
 * 替换HttpInvokerServiceExporter时，以下属性没有复制，因而不支持:
 * - beanClassLoader
 * - interceptors
 * - registerTraceInterceptor
 *
 * <AUTHOR>
 */
@RequiredArgsConstructor
public class OtelHttpInvokerServerPostProcessor implements BeanPostProcessor {

    @Nonnull
    private final ApplicationContext applicationContext;

    private volatile WarpHttpServerInterceptor warpHttpServerInterceptor;
    @Override
    public Object postProcessBeforeInitialization(Object bean, String beanName) throws BeansException {
        if (bean instanceof HttpInvokerServiceExporter) {
            return new OtelHttpInvokerServiceExporter((HttpInvokerServiceExporter) bean, findWarpHttpServerInterceptor());
        } else {
            return bean;
        }
    }



    @Nonnull
    private WarpHttpServerInterceptor findWarpHttpServerInterceptor() {
        if (warpHttpServerInterceptor != null) {
            return warpHttpServerInterceptor;
        }
        try {
            warpHttpServerInterceptor = applicationContext.getBean(WarpHttpServerInterceptor.class);
        } catch (BeansException e) {
            throw new IllegalStateException("WarpHttpServerInterceptor not found");
        }
        return warpHttpServerInterceptor;
    }

    /**
     * 为了记录方法名，采用继承，而不是代理
     */
    public static class OtelHttpInvokerServiceExporter extends HttpInvokerServiceExporter {
        private static final Method HANDLE_REQUEST_METHOD = handleRequestMethod();

        // 这几个字段没有getter, 强制访问
        private static final List<Field> FIELDS_TO_COPY = findFields(HttpInvokerServiceExporter.class,
                "registerTraceInterceptor", "interceptors", "beanClassLoader");
        @Nonnull
        private final WarpHttpServerInterceptor warpHttpServerInterceptor;
        @Nonnull
        private final HandlerMethod handlerMethod;



        public OtelHttpInvokerServiceExporter(@Nonnull HttpInvokerServiceExporter prototype,
                                              @Nonnull WarpHttpServerInterceptor warpHttpServerInterceptor) {
            super();
            this.warpHttpServerInterceptor = warpHttpServerInterceptor;
            this.handlerMethod = new HandlerMethod(prototype, HANDLE_REQUEST_METHOD);

            this.setService(prototype.getService());
            this.setServiceInterface(prototype.getServiceInterface());
            this.setContentType(prototype.getContentType());
            this.setAcceptProxyClasses(prototype.isAcceptProxyClasses());
            this.setRemoteInvocationExecutor(prototype.getRemoteInvocationExecutor());
            // 这些字段没有getter, 强制访问
            // this.setRegisterTraceInterceptor()
            // this.setInterceptors()
            // this.setBeanClassLoader(prototype.getBeanClassLoader())
            forceCopyFields(prototype);

        }

        private void forceCopyFields(HttpInvokerServiceExporter prototype) {
            for (Field field : FIELDS_TO_COPY) {
                try {
                    field.set(this, field.get(prototype));
                } catch (IllegalAccessException e) {
                    throw new RuntimeException(e);
                }
            }
        }

        private static Method handleRequestMethod()  {
            try {
                return HttpInvokerServiceExporter.class.getMethod("handleRequest", HttpServletRequest.class, HttpServletResponse.class);
            } catch (NoSuchMethodException e) {
                throw new RuntimeException(e);
            }
        }

        private static List<Field> findFields(Class<?> clazz, String... fieldNames) {
            List<Field> fields = new ArrayList<>(fieldNames.length);
            for (String fieldName : fieldNames) {
                Field field = findFieldIfExist(clazz, fieldName);
                if (field != null) {
                    fields.add(field);
                }
            }
            return fields;
        }

        private static Field findFieldIfExist(Class<?> clazz, String fieldName) {
            Field declaredField = ReflectionUtils.findField(clazz, fieldName);
            if (declaredField == null) {
                return null;
            }
            declaredField.setAccessible(true);
            return declaredField;
        }

        @Override
        public void handleRequest(HttpServletRequest request, HttpServletResponse response) throws ServletException, IOException {
            // 偷懒用warp的拦截器
            try {
                warpHttpServerInterceptor.preHandle(request, response, handlerMethod);
                super.handleRequest(request, response);
                warpHttpServerInterceptor.afterCompletion(request, response, handlerMethod, null);
            } catch (Exception e) {
                warpHttpServerInterceptor.afterCompletion(request, response, handlerMethod, e);
                throw e;
            }
        }

        @Override
        protected RemoteInvocation readRemoteInvocation(HttpServletRequest request) throws IOException, ClassNotFoundException {
            RemoteInvocation remoteInvocation = super.readRemoteInvocation(request);
            Span.current().setAttribute("http.invoker.method", String.valueOf(remoteInvocation.getMethodName()));
            return remoteInvocation;
        }
    }
}
