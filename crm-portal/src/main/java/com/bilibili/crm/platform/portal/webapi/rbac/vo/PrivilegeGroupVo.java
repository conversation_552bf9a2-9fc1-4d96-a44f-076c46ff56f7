package com.bilibili.crm.platform.portal.webapi.rbac.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import com.fasterxml.jackson.databind.annotation.JsonSerialize;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * Created by fanwen<PERSON> on 2017/4/5.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class PrivilegeGroupVo {
    private Integer id;
    private String name;
    private String remark;
    private List<PrivilegeVo> privileges;
}
