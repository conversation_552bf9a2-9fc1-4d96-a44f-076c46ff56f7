package com.bilibili.crm.platform.portal.webapi.expenditure.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddExpenditureSecondTypeVo {

    @ApiModelProperty("一级成本类型id")
    private Integer p_id;

    @ApiModelProperty("二级成本类型名称")
    private String name;

    @ApiModelProperty("是否关联订单 0否 1是")
    private Integer is_relate_order;

    @ApiModelProperty("是否对外付款 0否 1是")
    private Integer pay_flag;

    @ApiModelProperty("是否计入广告成本 0否 1是")
    private Integer ad_cost_flag;

    @ApiModelProperty("资源提供方默认值")
    private Integer department_id;

    @ApiModelProperty("支出项名称默认值")
    private String expenditure_name;

    @ApiModelProperty("备注")
    private String remark;
}
