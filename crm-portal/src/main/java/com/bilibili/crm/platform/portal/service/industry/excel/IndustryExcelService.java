package com.bilibili.crm.platform.portal.service.industry.excel;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.biz.boss.service.BossFileService;
import com.bilibili.crm.platform.biz.dao.CrmUnitedIndustryDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerDao;
import com.bilibili.crm.platform.biz.dao.CustomerSeaDao;
import com.bilibili.crm.platform.biz.dao.NewToOldCategoryMappingDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.soa.SoaQueryAccountService;
import com.bilibili.crm.platform.biz.util.FileUtils;
import com.bilibili.crm.platform.biz.util.ThreadPoolManager;
import com.bilibili.crm.platform.portal.common.ExportUtils;
import com.bilibili.crm.platform.portal.service.industry.excel.bo.*;
import com.google.common.base.Stopwatch;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.core.io.FileSystemResource;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.io.File;
import java.io.IOException;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/6/28
 */
@Service
@Slf4j
@Deprecated
public class IndustryExcelService {

    private static final Integer PAGE_SIZE = 1000;

    private static boolean stop = false;

    @Value("${crm.boss.download.url:https://boss.hdslb.com/business_ad_crm/}")
    private String BOSS_DOWNLOAD_URL;

    @Resource
    private CrmUnitedIndustryDao crmUnitedIndustryDao;

    @Resource
    private NewToOldCategoryMappingDao newToOldCategoryMappingDao;

    @Resource
    private ExportUtils exportUtils;

    @Resource
    private AccAccountDao accAccountDao;

    @Resource
    private CustomerDao customerDao;

    @Resource
    private CustomerSeaDao customerSeaDao;

    @Resource
    private SoaQueryAccountService soaQueryAccountService;

    @Resource
    private IQueryAccountService queryAccountService;

    @Resource
    private BossFileService bossFileService;


    public void updateStop(String stopStr) {
        stop = "stop".equals(stopStr);
    }

    public void importToDataBase(String path) throws IOException {
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        log.info("path = {}", fileSystemResource.getPath());
        AnalysisEventListener<UnitedIndustryImportExcelBo> analysisEventListener = new AnalysisEventListener<UnitedIndustryImportExcelBo>() {
            private List<UnitedIndustryImportExcelBo> data = new ArrayList<>();

            @Override
            public void invoke(UnitedIndustryImportExcelBo o, AnalysisContext analysisContext) {
                data.add(o);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                saveData();
            }

            private void saveData() {
                CrmUnitedIndustryPo po = new CrmUnitedIndustryPo();
                Map<String, Integer> firstMap = new HashMap<>();
                Map<String, Integer> secondMap = new HashMap<>();
                Map<String, Integer> thirdMap = new HashMap<>();
                for (UnitedIndustryImportExcelBo bo : data) {
                    if (!firstMap.containsKey(bo.getFirstIndustryName())) {
                        po.setLevel(0);
                        po.setName(bo.getFirstIndustryName());
                        po.setPId(0);
                        crmUnitedIndustryDao.insertSelective(po);
                        firstMap.put(bo.getFirstIndustryName(), po.getId());
                    }
                    if (!secondMap.containsKey(bo.getSecondIndustryName() + "-" + bo.getFirstIndustryName())) {
                        po.setLevel(1);
                        po.setName(bo.getSecondIndustryName());
                        po.setPId(firstMap.get(bo.getFirstIndustryName()));
                        crmUnitedIndustryDao.insertSelective(po);
                        secondMap.put(bo.getSecondIndustryName() + "-" + bo.getFirstIndustryName(), po.getId());

                    }
                    if (!thirdMap.containsKey(bo.getThirdIndustryName() + "-" + bo.getSecondIndustryName() + "-" + bo.getFirstIndustryName())) {
                        po.setLevel(2);
                        po.setName(bo.getThirdIndustryName());
                        po.setPId(secondMap.get(bo.getSecondIndustryName() + "-" + bo.getFirstIndustryName()));
                        crmUnitedIndustryDao.insertSelective(po);
                        thirdMap.put(bo.getThirdIndustryName() + "-" + bo.getSecondIndustryName() + "-" + bo.getFirstIndustryName(), po.getId());
                    }
                }

            }
        };
        EasyExcel.read(fileSystemResource.getPath(), UnitedIndustryImportExcelBo.class, analysisEventListener).sheet(0).doRead();
    }

    public void export(String str) {
        log.info("export start, str = {}", str);
        CrmUnitedIndustryPoExample example = new CrmUnitedIndustryPoExample();
        example.setOrderByClause("id asc");
        CrmUnitedIndustryPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(0);
        List<UnitedIndustryExportBo> result = convert(crmUnitedIndustryDao.selectByExample(example));
        exportUtils.exportToEmail("zhuguangwen", result, "导出", false, UnitedIndustryExportBo.class);
    }

    private List<UnitedIndustryExportBo> convert(List<CrmUnitedIndustryPo> crmUnitedIndustryPos) {
        Map<Integer, CrmUnitedIndustryPo> map = crmUnitedIndustryPos.stream().collect(Collectors.toMap(CrmUnitedIndustryPo::getId, Function.identity()));


        return crmUnitedIndustryPos.stream().map(po -> {
            UnitedIndustryExportBo result = new UnitedIndustryExportBo();
            result.setId(po.getId());
            if (po.getLevel() == 0) {
                result.setFirstName(po.getName());
            }
            if (po.getLevel() == 1) {
                result.setSecondName(po.getName());
                result.setFirstName(map.get(po.getPId()).getName());
            }
            if (po.getLevel() == 2) {
                result.setThirdName(po.getName());
                Integer secondPid = po.getPId();
                CrmUnitedIndustryPo crmUnitedIndustryPo = map.get(secondPid);
                result.setSecondName(crmUnitedIndustryPo.getName());
                Integer thirdPid = crmUnitedIndustryPo.getPId();
                result.setFirstName(map.get(thirdPid).getName());
            }
            return result;
        }).collect(Collectors.toList());
    }

    public void importUnitedMapping(String path, String importType) {
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        log.info("path = {}", fileSystemResource.getPath());
        AnalysisEventListener<UnitedIndustryMappingExcelBo> analysisEventListener = new AnalysisEventListener<UnitedIndustryMappingExcelBo>() {
            private List<UnitedIndustryMappingExcelBo> data = new ArrayList<>();

            @Override
            public void invoke(UnitedIndustryMappingExcelBo o, AnalysisContext analysisContext) {
                data.add(o);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                saveData();
            }

            private void saveData() {
                for (UnitedIndustryMappingExcelBo bo : data) {
                    NewToOldCategoryMappingPo po = new NewToOldCategoryMappingPo();
                    po.setIndustryMappingType(importType);
                    po.setNewCategoryThirdId(bo.getUnitedId());
                    po.setOldCategorySecondId(bo.getMappingId());
                    newToOldCategoryMappingDao.insertSelective(po);
                }
            }
        };
        EasyExcel.read(fileSystemResource.getPath(), UnitedIndustryMappingExcelBo.class, analysisEventListener).sheet(0).doRead();
    }

    public void importCustomerAccountInfo(String path, String type) {
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        log.info("path = {}", fileSystemResource.getPath());
        Stopwatch started = Stopwatch.createStarted();
        Set<Integer> failed = new HashSet<>();
        AnalysisEventListener<MappingIndustryExcelBo> analysisEventListener = new AnalysisEventListener<MappingIndustryExcelBo>() {
            private List<MappingIndustryExcelBo> data = new ArrayList<>();

            public void onException(Exception exception, AnalysisContext context) throws Exception {
                log.error("importCustomerAccountInfo-onException error", exception);
            }

            @Override
            public void invoke(MappingIndustryExcelBo o, AnalysisContext analysisContext) {
                data.add(o);
                if (data.size() >= 100) {
                    if (stop) {
                        log.info("importCustomerAccountInfo stop");
                        throw new RuntimeException("importCustomerAccountInfo stop");
                    }
                    update();
                    data.clear();
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                update();
            }

            private void update() {
                try {
                    if ("customer".equals(type)) {
                        updateCustomer(data, failed);
                    } else if ("account".equals(type)) {
                        updateAccount(data, failed);
                    }
                } catch (Exception e) {
                    log.error("update error", e);
                }
            }
        };
        try {
            EasyExcel.read(fileSystemResource.getPath(), MappingIndustryExcelBo.class, analysisEventListener).sheet(0).doRead();
        } catch (Exception e) {
            log.error("importCustomerAccountInfo error", e);
        }
        log.info("update end , cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        List<MappingFailedExcelBo> result = new ArrayList<>();
        failed.forEach(fail -> {
            MappingFailedExcelBo bo = new MappingFailedExcelBo();
            bo.setId(fail);
            result.add(bo);
        });
        if (CollectionUtils.isNotEmpty(result)) {
            exportUtils.exportToEmail("zhuguangwen", result, "失败的id", false, MappingFailedExcelBo.class);
        }
    }

    private void updateCustomer(List<MappingIndustryExcelBo> bos, Set<Integer> failIds) {
        try {
            if (CollectionUtils.isEmpty(bos)) {
                return;
            }
            List<Integer> excelIds = bos.stream().map(MappingIndustryExcelBo::getMappingId).collect(Collectors.toList());
            List<Integer> realIds = excelIds.stream().filter(id -> id != null && id > 0).collect(Collectors.toList());
            excelIds.removeAll(realIds);
            if (CollectionUtils.isNotEmpty(excelIds)) {
                log.warn("there are invalid customerIds in excel = {}", excelIds);
                failIds.addAll(excelIds);
            }

            CustomerPoExample customerPoExample = new CustomerPoExample();
            CustomerPoExample.Criteria criteria = customerPoExample.createCriteria();
            criteria.andIdIn(realIds);
            Stopwatch started = Stopwatch.createStarted();
            List<CustomerPo> customerPos = customerDao.selectByExample(customerPoExample);

            CustomerSeaPoExample customerSeaPoExample = new CustomerSeaPoExample();
            CustomerSeaPoExample.Criteria seaPoExampleCriteria = customerSeaPoExample.createCriteria();
            seaPoExampleCriteria.andCustomerIdIn(realIds);
            List<CustomerSeaPo> customerSeaPos = customerSeaDao.selectByExample(customerSeaPoExample);
            Map<Integer, List<CustomerSeaPo>> seaPoMap = customerSeaPos.stream().collect(Collectors.groupingBy(CustomerSeaPo::getCustomerId));

            if (CollectionUtils.isEmpty(customerPos)) {
                failIds.addAll(realIds);
                log.warn("there are invalid customerIds in db= {}", realIds);
                return;
            }
            List<Integer> customerIds = customerPos.stream().map(CustomerPo::getId).collect(Collectors.toList());
            realIds.removeAll(customerIds);
            if (CollectionUtils.isNotEmpty(realIds)) {
                failIds.addAll(realIds);
                log.warn("there are invalid customerIds in db= {}", realIds);
            }

            Map<Integer, MappingIndustryExcelBo> map = bos.stream().collect(Collectors.toMap(
                    MappingIndustryExcelBo::getMappingId, Function.identity(), (k1, k2) -> k1));
            customerPos.forEach(po -> {
                MappingIndustryExcelBo mappingIndustryExcelBo = map.get(po.getId());
                if (null == mappingIndustryExcelBo.getUnitedFirstIndustryId() ||
                        null == mappingIndustryExcelBo.getUnitedSecondIndustryId() ||
                        null == mappingIndustryExcelBo.getUnitedThirdIndustryId()) {
                    failIds.add(po.getId());
                    return;
                }
                po.setUnitedFirstIndustryId(mappingIndustryExcelBo.getUnitedFirstIndustryId());
                po.setUnitedSecondIndustryId(mappingIndustryExcelBo.getUnitedSecondIndustryId());
                po.setUnitedThirdIndustryId(mappingIndustryExcelBo.getUnitedThirdIndustryId());
                customerDao.updateByPrimaryKeySelective(po);

                List<CustomerSeaPo> customerSeaPoList = seaPoMap.get(po.getId());
                if (CollectionUtils.isNotEmpty(customerSeaPoList)) {
                    customerSeaPoList.forEach(customerSeaPo -> {
                        customerSeaPo.setUnitedFirstIndustryId(mappingIndustryExcelBo.getUnitedFirstIndustryId());
                        customerSeaPo.setUnitedSecondIndustryId(mappingIndustryExcelBo.getUnitedSecondIndustryId());
                        customerSeaPo.setUnitedThirdIndustryId(mappingIndustryExcelBo.getUnitedThirdIndustryId());
                        customerSeaDao.updateByPrimaryKeySelective(customerSeaPo);
                    });
                } else {
                    log.warn("updateCustomer-customerSeaPo is null, customerId= {}", po.getId());
                }
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
            log.info("updateCustomer cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("updateCustomer error, size = {}", failIds.size(), e);
        }
    }

    private void updateAccount(List<MappingIndustryExcelBo> bos, Set<Integer> failIds) {
        try {
            if (CollectionUtils.isEmpty(bos)) {
                return;
            }
            List<Integer> excelIds = bos.stream().map(MappingIndustryExcelBo::getMappingId).collect(Collectors.toList());
            List<Integer> realIds = excelIds.stream().filter(id -> id != null && id > 0).collect(Collectors.toList());
            excelIds.removeAll(realIds);
            if (CollectionUtils.isNotEmpty(excelIds)) {
                log.warn("there are invalid accountIds in excel = {}", excelIds);
                failIds.addAll(excelIds);
            }

            AccAccountPoExample accPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accPoExample.createCriteria();
            criteria.andAccountIdIn(realIds);
            Stopwatch started = Stopwatch.createStarted();
            List<AccAccountPo> accountPos = accAccountDao.selectByExample(accPoExample);
            if (CollectionUtils.isEmpty(accountPos)) {
                failIds.addAll(realIds);
                log.warn("there are invalid accountIds in db= {}", realIds);
                return;
            }

            List<Integer> accountIds = accountPos.stream().map(AccAccountPo::getAccountId).collect(Collectors.toList());
            realIds.removeAll(accountIds);
            if (CollectionUtils.isNotEmpty(realIds)) {
                failIds.addAll(realIds);
                log.warn("there are invalid accountIds in db= {}", realIds);
            }


            Map<Integer, MappingIndustryExcelBo> map = bos.stream().collect(Collectors.toMap(
                    MappingIndustryExcelBo::getMappingId, Function.identity(), (k1, k2) -> k1));
            accountPos.forEach(po -> {
                MappingIndustryExcelBo mappingIndustryExcelBo = map.get(po.getAccountId());
                po.setUnitedFirstIndustryId(mappingIndustryExcelBo.getUnitedFirstIndustryId());
                po.setUnitedSecondIndustryId(mappingIndustryExcelBo.getUnitedSecondIndustryId());
                po.setUnitedThirdIndustryId(mappingIndustryExcelBo.getUnitedThirdIndustryId());
                po.setCommerceCategoryFirstId(mappingIndustryExcelBo.getCommerceFirstId());
                po.setCommerceCategorySecondId(mappingIndustryExcelBo.getCommerceSecondId());

                accAccountDao.updateByPrimaryKeySelective(po);
                try {
                    Thread.sleep(10);
                } catch (InterruptedException e) {
                    throw new RuntimeException(e);
                }
            });
            log.info("updateAccount cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("updateAccount error, size = {}", failIds.size(), e);
        }
    }

    public void refreshNotOrgCustomerUnitedIndustry(String flag) {
        try {
            log.info("refreshCustomerUnitedIndustry start = {}", flag);
            CustomerPoExample customerPoExample = new CustomerPoExample();
            CustomerPoExample.Criteria criteria = customerPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUnitedThirdIndustryIdNotEqualTo(1129);
            criteria.andCustomerCategoryNotIn(Lists.newArrayList(0, 1));
            criteria.andIsAgentEqualTo(0);
            long count = customerDao.countByExample(customerPoExample);
            log.info("refreshNotOrgCustomerUnitedIndustry count = {}", count);
            customerPoExample.setOffset(0);
            customerPoExample.setLimit(1);
            customerPoExample.setOrderByClause("id asc");
            List<CustomerPo> oneCustomer = customerDao.selectByExample(customerPoExample);
            if (CollectionUtils.isEmpty(oneCustomer)) {
                log.info("refreshNotOrgCustomerUnitedIndustry count is 0");
                return;
            }
            CustomerPo customerPo = oneCustomer.get(0);
            log.info("refreshNotOrgCustomerUnitedIndustry customerPo = {}", JSON.toJSONString(customerPo));

            customerPoExample.setOrderByClause("id desc");
            List<CustomerPo> finalPos = customerDao.selectByExample(customerPoExample);
            CustomerPo finalCustomerPo = finalPos.get(0);
            log.info("refreshNotOrgCustomerUnitedIndustry final customerPo = {}", JSON.toJSONString(finalCustomerPo));


            int startId = customerPo.getId() - 1;
            int endId = startId + PAGE_SIZE;
            Stopwatch started = Stopwatch.createStarted();
            while (finalCustomerPo.getId() > startId) {
                if (stop) {
                    log.info("refreshNotOrgCustomerUnitedIndustry stop");
                    throw new RuntimeException("refreshNotOrgCustomerUnitedIndustry stop");
                }
                customerPoExample = new CustomerPoExample();
                criteria = customerPoExample.createCriteria();
                criteria.andIdGreaterThan(startId);
                criteria.andIdLessThanOrEqualTo(endId);
                criteria.andIsDeletedEqualTo(0);
                criteria.andUnitedThirdIndustryIdNotEqualTo(1129);
                criteria.andCustomerCategoryNotIn(Lists.newArrayList(0, 1));
                criteria.andIsAgentEqualTo(0);
                List<CustomerPo> customerPos = customerDao.selectByExample(customerPoExample);
                if (CollectionUtils.isEmpty(customerPos)) {
                    startId = startId + PAGE_SIZE;
                    endId = endId + PAGE_SIZE;
                    continue;
                }
                startId = startId + PAGE_SIZE;
                endId = endId + PAGE_SIZE;
                customerPos.forEach(po -> {
                    po.setUnitedFirstIndustryId(1126);
                    po.setUnitedSecondIndustryId(1127);
                    po.setUnitedThirdIndustryId(1129);
                    customerDao.updateByPrimaryKeySelective(po);
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            log.info("refreshNotOrgCustomerUnitedIndustry cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("refreshNotOrgCustomerUnitedIndustry error", e);
        }
    }

    public void refreshNotOrgAccountUnitedIndustry(String flag) {
        try {

            log.info("refreshNotOrgAccountUnitedIndustry start = {}", flag);
            AccAccountPoExample accAccountPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accAccountPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUnitedThirdIndustryIdNotEqualTo(1129);
            criteria.andUserTypeNotIn(Lists.newArrayList(0, 1));
            criteria.andIsAgentEqualTo(0);
            long count = accAccountDao.countByExample(accAccountPoExample);
            log.info("refreshNotOrgAccountUnitedIndustry count = {}", count);

            accAccountPoExample.setOffset(0);
            accAccountPoExample.setLimit(1);
            accAccountPoExample.setOrderByClause("account_id asc");
            List<AccAccountPo> oneAccount = accAccountDao.selectByExample(accAccountPoExample);
            if (CollectionUtils.isEmpty(oneAccount)) {
                log.info("refreshNotOrgAccountUnitedIndustry count is 0");
                return;
            }

            AccAccountPo accAccountPo = oneAccount.get(0);
            log.info("refreshNotOrgAccountUnitedIndustry accAccountPo = {}", JSON.toJSONString(accAccountPo));

            accAccountPoExample.setOrderByClause("account_id desc");
            List<AccAccountPo> finalOneAccount = accAccountDao.selectByExample(accAccountPoExample);
            AccAccountPo finalAccountPo = finalOneAccount.get(0);
            log.info("refreshNotOrgAccountUnitedIndustry finalAccountPo = {}", JSON.toJSONString(finalAccountPo));

            int startId = accAccountPo.getAccountId() - 1;
            int endId = startId + PAGE_SIZE;
            Stopwatch started = Stopwatch.createStarted();
            while (finalAccountPo.getAccountId() > startId) {
                if (stop) {
                    log.info("refreshNotOrgAccountUnitedIndustry stop");
                    throw new RuntimeException("refreshNotOrgAccountUnitedIndustry stop");
                }
                accAccountPoExample = new AccAccountPoExample();
                criteria = accAccountPoExample.createCriteria();
                criteria.andAccountIdGreaterThan(startId);
                criteria.andAccountIdLessThanOrEqualTo(endId);
                criteria.andIsDeletedEqualTo(0);
                criteria.andUserTypeNotIn(Lists.newArrayList(0, 1));
                criteria.andIsAgentEqualTo(0);
                criteria.andUnitedThirdIndustryIdNotEqualTo(1129);
                List<AccAccountPo> accountPos = accAccountDao.selectByExample(accAccountPoExample);
                if (CollectionUtils.isEmpty(accountPos)) {
                    startId = startId + PAGE_SIZE;
                    endId = endId + PAGE_SIZE;
                    continue;
                }
                startId = startId + PAGE_SIZE;
                endId = endId + PAGE_SIZE;
                accountPos.forEach(po -> {
                    po.setUnitedFirstIndustryId(1126);
                    po.setUnitedSecondIndustryId(1127);
                    po.setUnitedThirdIndustryId(1129);
                    accAccountDao.updateByPrimaryKeySelective(po);
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            log.info("refreshNotOrgAccountUnitedIndustry cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("refreshNotOrgAccountUnitedIndustry error", e);
        }
    }

    public void refreshBannedCustomer(String flag) {
        try {
            log.info("refreshBannedCustomer start, flag ={}", flag);
            CustomerPoExample customerPoExample = new CustomerPoExample();
            CustomerPoExample.Criteria criteria = customerPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUnitedFirstIndustryIdEqualTo(0);
            criteria.andCustomerCategoryEqualTo(1);
            criteria.andIsAgentEqualTo(0);
            long count = customerDao.countByExample(customerPoExample);
            log.info("refreshBannedCustomer count = {}", count);

            int startId = 0;
            int endId = PAGE_SIZE;
            Stopwatch started = Stopwatch.createStarted();
            while (count > 0) {
                if (stop) {
                    log.info("refreshBannedCustomer stop");
                    throw new RuntimeException("refreshBannedCustomer stop");
                }
                customerPoExample.clear();
                criteria = customerPoExample.createCriteria();
                criteria.andIsDeletedEqualTo(0);
                criteria.andUnitedFirstIndustryIdEqualTo(0);
                criteria.andCustomerCategoryEqualTo(1);
                criteria.andIsAgentEqualTo(0);
                criteria.andIdGreaterThan(startId);
                criteria.andIdLessThanOrEqualTo(endId);
                List<CustomerPo> customerPos = customerDao.selectByExample(customerPoExample);
                if (CollectionUtils.isEmpty(customerPos)) {
                    startId = endId;
                    endId = endId + PAGE_SIZE;
                    continue;
                }
                startId = endId;
                endId = endId + PAGE_SIZE;
                count = count - customerPos.size();
                customerPos.forEach(po -> {
                    po.setUnitedFirstIndustryId(1290);
                    po.setUnitedSecondIndustryId(1291);
                    po.setUnitedThirdIndustryId(1292);
                    customerDao.updateByPrimaryKeySelective(po);
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }
                });
            }
            log.info("refreshBannedCustomer cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("refreshBannedCustomer error", e);
        }
    }

    public void refreshBannedAccount(String flag) {
        try {
            log.info("refreshBannedAccount start, flag ={}", flag);
            AccAccountPoExample accAccountPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accAccountPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUnitedFirstIndustryIdEqualTo(0);
            criteria.andUserTypeEqualTo(1);
            criteria.andIsAgentEqualTo(0);
            long count = accAccountDao.countByExample(accAccountPoExample);
            log.info("refreshBannedAccount count = {}", count);

            int startId = 0;
            int endId = PAGE_SIZE;
            Stopwatch started = Stopwatch.createStarted();
            while (count > 0) {
                if (stop) {
                    log.info("refreshBannedAccount stop");
                    throw new RuntimeException("refreshBannedAccount stop");
                }
                accAccountPoExample.clear();
                criteria = accAccountPoExample.createCriteria();
                criteria.andAccountIdGreaterThan(startId);
                criteria.andAccountIdLessThanOrEqualTo(endId);
                criteria.andIsDeletedEqualTo(0);
                criteria.andUnitedFirstIndustryIdEqualTo(0);
                criteria.andUserTypeEqualTo(1);
                criteria.andIsAgentEqualTo(0);
                List<AccAccountPo> accountPos = accAccountDao.selectByExample(accAccountPoExample);
                if (CollectionUtils.isEmpty(accountPos)) {
                    startId = endId;
                    endId = endId + PAGE_SIZE;
                    continue;
                }
                startId = endId;
                endId = endId + PAGE_SIZE;
                count = count - accountPos.size();
                accountPos.forEach(po -> {
                    po.setUnitedFirstIndustryId(1290);
                    po.setUnitedSecondIndustryId(1291);
                    po.setUnitedThirdIndustryId(1292);
                    accAccountDao.updateByPrimaryKeySelective(po);
                    try {
                        Thread.sleep(10);
                    } catch (InterruptedException e) {
                        throw new RuntimeException(e);
                    }

                });
            }
            log.info("refreshBannedAccount cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
        } catch (Exception e) {
            log.error("refreshBannedAccount error", e);
        }
    }

    public void exportCustomerWithoutUnitedIndustry(String flag) {
        try {
            log.info("exportCustomerWithoutUnitedIndustry start = {}", flag);
            CustomerPoExample customerPoExample = new CustomerPoExample();
            CustomerPoExample.Criteria criteria = customerPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andCustomerStatusEqualTo(1);
            criteria.andUnitedFirstIndustryIdEqualTo(0);
            criteria.andCustomerCategoryEqualTo(1);
            long count = customerDao.countByExample(customerPoExample);
            log.info("exportCustomerWithoutUnitedIndustry count = {}", count);
            List<CustomerPo> customerPos = customerDao.selectByExample(customerPoExample);
            List<MappingFailedExcelBo> customerIds = customerPos.stream().map(po -> {
                MappingFailedExcelBo bo = new MappingFailedExcelBo();
                bo.setId(po.getId());
                return bo;
            }).collect(Collectors.toList());
            exportUtils.exportToEmail("zhuguangwen", customerIds, "机构客户无行业id", false, MappingFailedExcelBo.class);
        } catch (Exception e) {
            log.error("exportCustomerWithoutUnitedIndustry error", e);
        }
    }

    public void exportAccountWithoutUnitedIndustry(String flag) {
        try {
            log.info("exportAccountWithoutUnitedIndustry start = {}", flag);
            AccAccountPoExample accAccountPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accAccountPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andAccountStatusEqualTo(0);
            criteria.andUnitedFirstIndustryIdEqualTo(0);
            criteria.andUserTypeEqualTo(1);
            long count = accAccountDao.countByExample(accAccountPoExample);
            log.info("exportAccountWithoutUnitedIndustry count = {}", count);
            List<AccAccountPo> accountPos = accAccountDao.selectByExample(accAccountPoExample);
            List<MappingFailedExcelBo> accountIds = accountPos.stream().map(po -> {
                MappingFailedExcelBo bo = new MappingFailedExcelBo();
                bo.setId(po.getAccountId());
                return bo;
            }).collect(Collectors.toList());
            exportUtils.exportToEmail("zhuguangwen", accountIds, "机构账号无行业id", false, MappingFailedExcelBo.class);
        } catch (Exception e) {
            log.error("exportAccountWithoutUnitedIndustry error", e);
        }
    }

    public void refreshAgentCustomer(String flag) {
        try {
            log.info("refreshAgentCustomer flag = {}", flag);
            CustomerPoExample customerPoExample = new CustomerPoExample();
            CustomerPoExample.Criteria criteria = customerPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUnitedFirstIndustryIdNotEqualTo(290);
            criteria.andIsAgentEqualTo(1);
            criteria.andCustomerCategoryEqualTo(1);
            long count = customerDao.countByExample(customerPoExample);
            if (0 == count) {
                log.info("refreshAgentCustomer count is 0 ");
                return;
            }

            long mod = count % PAGE_SIZE;
            int pageCount = (int) count / PAGE_SIZE;
            pageCount = mod == 0 ? pageCount : pageCount + 1;
            int pageIndex = 1;
            customerPoExample.setLimit(PAGE_SIZE);
            while (pageCount >= pageIndex) {
                customerPoExample.setOffset((pageIndex - 1) * PAGE_SIZE);
                List<CustomerPo> customerPos = customerDao.selectByExample(customerPoExample);
                pageIndex++;
                if (CollectionUtils.isEmpty(customerPos)) {
                    continue;
                }
                customerPos.forEach(po -> {
                    po.setUnitedFirstIndustryId(290);
                    po.setUnitedSecondIndustryId(304);
                    po.setUnitedThirdIndustryId(305);
                    customerDao.updateByPrimaryKeySelective(po);
                });
                Thread.sleep(10);

            }
        } catch (Exception e) {
            log.error("refreshAgentCustomer error", e);
        }
        log.info("refreshAgentCustomer end");
    }

    public void refreshAgentAccount(String flag) {
        try {
            log.info("refreshAgentAccount flag = {}", flag);
            AccAccountPoExample accPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUnitedFirstIndustryIdNotEqualTo(290);
            criteria.andIsAgentEqualTo(1);
            criteria.andUserTypeEqualTo(1);
            long count = accAccountDao.countByExample(accPoExample);
            if (0 == count) {
                log.info("refreshAgentAccount count is 0 ");
                return;
            }

            long mod = count % PAGE_SIZE;
            int pageCount = (int) count / PAGE_SIZE;
            pageCount = mod == 0 ? pageCount : pageCount + 1;
            int pageIndex = 1;
            accPoExample.setLimit(PAGE_SIZE);
            while (pageCount >= pageIndex) {
                accPoExample.setOffset((pageIndex - 1) * PAGE_SIZE);
                List<AccAccountPo> accountPos = accAccountDao.selectByExample(accPoExample);
                pageIndex++;
                if (CollectionUtils.isEmpty(accountPos)) {
                    continue;
                }
                accountPos.forEach(po -> {
                    po.setUnitedFirstIndustryId(290);
                    po.setUnitedSecondIndustryId(304);
                    po.setUnitedThirdIndustryId(305);
                    accAccountDao.updateByPrimaryKeySelective(po);
                });
                Thread.sleep(10);

            }
        } catch (Exception e) {
            log.error("refreshAgentAccount error", e);
        }
        log.info("refreshAgentAccount end");
    }


    public void refreshCustomerSea(String flag) {
        Stopwatch started = Stopwatch.createStarted();
        try {
            log.info("refreshCustomerSea flag = {}", flag);
            CustomerSeaPoExample example = new CustomerSeaPoExample();
            CustomerSeaPoExample.Criteria criteria = example.createCriteria();
            criteria.andIsAgentEqualTo(0);
            criteria.andUnitedFirstIndustryIdEqualTo(0);
            criteria.andCustomerCategoryEqualTo(1);
            criteria.andIsDeletedEqualTo(0);
            List<CustomerSeaPo> customerSeaPos = customerSeaDao.selectByExample(example);
            log.info("refreshCustomerSea customerSeaPos size = {}", customerSeaPos.size());
            for (CustomerSeaPo po : customerSeaPos) {
                Thread.sleep(10);
                CustomerPo customerPo = customerDao.selectByPrimaryKey(po.getCustomerId());
                if (null == customerPo) {
                    log.warn("customerPo is null, id = {}", po.getCustomerId());
                    continue;
                }
                Integer unitedFirstIndustryId = customerPo.getUnitedFirstIndustryId();
                if (unitedFirstIndustryId != 0) {
                    po.setUnitedFirstIndustryId(customerPo.getUnitedFirstIndustryId());
                    po.setUnitedSecondIndustryId(customerPo.getUnitedSecondIndustryId());
                    po.setUnitedThirdIndustryId(customerPo.getUnitedThirdIndustryId());
                    customerSeaDao.updateByPrimaryKeySelective(po);
                    continue;
                }

                po.setUnitedFirstIndustryId(1290);
                po.setUnitedSecondIndustryId(1291);
                po.setUnitedThirdIndustryId(1292);
                customerSeaDao.updateByPrimaryKeySelective(po);
            }
        } catch (Exception e) {
            log.error("refreshCustomerSea");
        }
        log.info("refreshCustomerSea end, cost ={}", started.elapsed(TimeUnit.MILLISECONDS));
    }

    public void refreshSingleAccount(String flag) {
        Stopwatch started = Stopwatch.createStarted();
        try {
            log.info("refreshSingleAccount start, flag = {}", flag);
            AccAccountPoExample accAccountPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accAccountPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andUserTypeEqualTo(0);
            List<AccAccountPo> accountPos = accAccountDao.selectByExample(accAccountPoExample);
            if (CollectionUtils.isEmpty(accountPos)) {
                log.info("refreshSingleAccount count is 0");
                return;
            }
            log.info("refreshSingleAccount size = {}", accountPos.size());
            for (AccAccountPo po : accountPos) {
                po.setUnitedFirstIndustryId(1126);
                po.setUnitedSecondIndustryId(1127);
                po.setUnitedThirdIndustryId(1128);
                accAccountDao.updateByPrimaryKeySelective(po);
                Thread.sleep(10);
            }

        } catch (Exception e) {
            log.error("refreshSingleAccount error", e);
        }
        log.info("refreshSingleAccount end cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
    }

    public void refreshSingleCustomer(String flag) {
        Stopwatch started = Stopwatch.createStarted();
        try {
            log.info("refreshSingleCustomer start, flag = {}", flag);
            CustomerPoExample customerPoExample = new CustomerPoExample();
            CustomerPoExample.Criteria criteria = customerPoExample.createCriteria();
            criteria.andIsDeletedEqualTo(0);
            criteria.andCustomerCategoryEqualTo(0);
            List<CustomerPo> customerPos = customerDao.selectByExample(customerPoExample);
            if (CollectionUtils.isEmpty(customerPos)) {
                log.info("refreshSingleCustomer count is 0");
                return;
            }
            log.info("refreshSingleCustomer size = {}", customerPos.size());
            for (CustomerPo po : customerPos) {
                po.setUnitedFirstIndustryId(1126);
                po.setUnitedSecondIndustryId(1127);
                po.setUnitedThirdIndustryId(1128);
                customerDao.updateByPrimaryKeySelective(po);
                Thread.sleep(10);
            }

        } catch (Exception e) {
            log.error("refreshSingleCustomer error", e);
        }
        log.info("refreshSingleCustomer end cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
    }

    public void updateCustomerSea(Integer customerId, Integer firstId, Integer secondId, Integer thirdId) {
        CustomerSeaPoExample customerSeaPoExample = new CustomerSeaPoExample();
        CustomerSeaPoExample.Criteria criteria = customerSeaPoExample.createCriteria();
        criteria.andCustomerIdEqualTo(customerId);
        List<CustomerSeaPo> customerSeaPos = customerSeaDao.selectByExample(customerSeaPoExample);
        if (CollectionUtils.isEmpty(customerSeaPos)) {
            return;
        }
        customerSeaPos.forEach(po -> {
            po.setUnitedFirstIndustryId(firstId);
            po.setUnitedSecondIndustryId(secondId);
            po.setUnitedThirdIndustryId(thirdId);
            customerSeaDao.updateByPrimaryKeySelective(po);
        });
    }

    public void importOldIndustry(String path, String industryType, String userType) {
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        log.info("path = {}", fileSystemResource.getPath());
        Stopwatch started = Stopwatch.createStarted();
        Set<Integer> failed = new HashSet<>();
        AnalysisEventListener<OldIndustryExcelBo> analysisEventListener = new AnalysisEventListener<OldIndustryExcelBo>() {
            private List<OldIndustryExcelBo> data = new ArrayList<>();

            public void onException(Exception exception, AnalysisContext context) throws Exception {
                log.error("importOldIndustry-onException error", exception);
            }

            @Override
            public void invoke(OldIndustryExcelBo o, AnalysisContext analysisContext) {
                data.add(o);
                if (data.size() >= 100) {
                    update();
                    data.clear();
                }
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                update();
            }

            private void update() {
                if (industryType.equals("commerce") && userType.equals("account")) {
                    data.forEach(d -> {
                        AccAccountPoExample poExample = new AccAccountPoExample();
                        AccAccountPoExample.Criteria criteria = poExample.createCriteria();
                        criteria.andAccountIdEqualTo(d.getMappingId());
                        List<AccAccountPo> accountPos = accAccountDao.selectByExample(poExample);
                        if (CollectionUtils.isNotEmpty(accountPos)) {
                            AccAccountPo accAccountPo = accountPos.get(0);
                            accAccountPo.setCommerceCategoryFirstId(d.getFirstIndustryId());
                            accAccountPo.setCommerceCategorySecondId(d.getSecondIndustryId());
                            accAccountDao.updateByPrimaryKeySelective(accAccountPo);
                        }
                    });
                }
            }
        };
        try {
            EasyExcel.read(fileSystemResource.getPath(), OldIndustryExcelBo.class, analysisEventListener).sheet(0).doRead();
        } catch (Exception e) {
            log.error("OldIndustryExcelBo error", e);
        }
        log.info("update end , cost = {}", started.elapsed(TimeUnit.MILLISECONDS));
    }

    public void sendEmail(String path, String name) {
        try {
            File file = new File(path);
            exportUtils.sendMail(Lists.newArrayList(name + "@bilibili.com"), file, path);
        } catch (Exception e) {
            log.error("sendEmail-backdoor error", e);
        }
    }

    public String uploadFile(String path) {
        String bossKey = UUID.randomUUID().toString();
        Stopwatch started = Stopwatch.createStarted();
        try {
            bossFileService.directUpload(bossKey,
                    FileUtils.getMultipartFile(new File(path), "jvm" + System.currentTimeMillis() + ".hprof"));
        } catch (Exception e) {
            log.error("jvm-downLoadFile error", e);
        }
        String url = BOSS_DOWNLOAD_URL + bossKey;
        log.info("uploadFile cost = {}, url = {}", started.elapsed(TimeUnit.MILLISECONDS), url);
        return url;
    }


    public void queryAccountByAccountId(Integer start, Integer end, Integer sleep) {
        CompletableFuture.runAsync(() -> {
            Stopwatch started = Stopwatch.createStarted();
            AccAccountPoExample accAccountPoExample = new AccAccountPoExample();
            AccAccountPoExample.Criteria criteria = accAccountPoExample.createCriteria();
            criteria.andAccountIdGreaterThan(start);
            criteria.andAccountIdLessThan(end);
            List<AccAccountPo> accountPos = accAccountDao.selectByExample(accAccountPoExample);
            try {
                Thread.sleep(sleep);
            } catch (InterruptedException e) {
                throw new RuntimeException(e);
            }
            log.info("accountBaseDtos cost ={}, size = {}", started.elapsed(TimeUnit.MILLISECONDS), accountPos.size());
        }, ThreadPoolManager.DEFAULT_THREAD_POOL_DEPRECATED);
    }

    public void exportIndustrySale(String path) {
        FileSystemResource fileSystemResource = new FileSystemResource(path);
        log.info("exportIndustrySale-path = {}", fileSystemResource.getPath());
        AnalysisEventListener<MappingIndustrySaleExcelBo> analysisEventListener = new AnalysisEventListener<MappingIndustrySaleExcelBo>() {
            private List<MappingIndustrySaleExcelBo> data = new ArrayList<>();

            @Override
            public void invoke(MappingIndustrySaleExcelBo o, AnalysisContext analysisContext) {
                data.add(o);
            }

            @Override
            public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                saveData();
            }

            private void saveData() {
                CrmUnitedIndustryPo po = new CrmUnitedIndustryPo();
                data.forEach(d -> {
                    po.setId(d.getIndustryId());
                    po.setIsVertical("垂直".equals(d.getVertical()) ? 1 : 0);
                    po.setAchieveBelongSaleGroupNames(d.getSaleGroupName());
                    po.setAchieveBelongSaleGroupIds(d.getSaleGroupId());
                    try {
                        crmUnitedIndustryDao.updateByPrimaryKeySelective(po);
                    } catch (Exception e) {
                        log.error("exportIndustrySale-saveData error", e);
                    }
                });
            }
        };
        try {
            EasyExcel.read(fileSystemResource.getPath(), MappingIndustrySaleExcelBo.class, analysisEventListener).sheet(0).doRead();
        } catch (Exception e) {
            log.error("exportIndustrySale error", e);
        }
    }

    public void syncCustomerSeaIndustry(String flag) {
        log.info("syncCustomerSeaIndustry start");
        int offset = 0;
        int limit = 10000;
        CustomerSeaPoExample customerSeaPoExample = new CustomerSeaPoExample();
        customerSeaPoExample.setOrderByClause("id desc");
        while (limit < 70000) {
            customerSeaPoExample.setLimit(limit);
            customerSeaPoExample.setOffset(offset);
            List<CustomerSeaPo> customerSeaPos = customerSeaDao.selectByExample(customerSeaPoExample);
            limit = limit + 10000;
            offset = offset + 10000;
            customerSeaPos.forEach(po -> {
                try {
                    CustomerPo customerPo = customerDao.selectByPrimaryKey(po.getCustomerId());
                    if (!Objects.equals(po.getUnitedFirstIndustryId(), customerPo.getUnitedFirstIndustryId()) || !Objects.equals(po.getUnitedSecondIndustryId(), customerPo.getUnitedSecondIndustryId())
                            || !Objects.equals(po.getUnitedThirdIndustryId(), customerPo.getUnitedThirdIndustryId())) {
                        po.setUnitedFirstIndustryId(customerPo.getUnitedFirstIndustryId());
                        po.setUnitedSecondIndustryId(customerPo.getUnitedSecondIndustryId());
                        po.setUnitedThirdIndustryId(customerPo.getUnitedThirdIndustryId());
                        customerSeaDao.updateByPrimaryKeySelective(po);
                    }
                } catch (Exception e) {
                    log.error("syncCustomerSeaIndustry error", e);
                }
            });
        }
    }
}
