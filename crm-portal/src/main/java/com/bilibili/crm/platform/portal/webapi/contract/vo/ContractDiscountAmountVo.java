package com.bilibili.crm.platform.portal.webapi.contract.vo;

import com.bilibili.crm.platform.portal.webapi.contract.vo.OrderDiscountVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * <AUTHOR>
 * @time 2018/3/26 18:14
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractDiscountAmountVo {

    @ApiModelProperty("订单id")
    private Integer crm_order_id;

    @ApiModelProperty("折扣金额")
    private Integer discount_amount;
}
