package com.bilibili.crm.platform.portal.webapi.expenditure.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-05-22 22:23:03
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ExpenditureRelateVo implements Serializable {

    @ApiModelProperty("订单id")
    private Integer crm_order_id;

    @ApiModelProperty("支出ID")
    private Integer id;


}
