package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

/**
 * <AUTHOR>
 * @date 2025/3/30
 */
@Data
public class QueryCustomerPackageResp {

    @ApiModelProperty("客户工单id")
    private Long customerWorkOrderId;

    @ApiModelProperty("客户id")
    private Integer customerId;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("核价状态")
    private Byte status;

    @ApiModelProperty("创建人")
    private String createUser;

    @ApiModelProperty("销售对接人")
    private String saleNickName;

    @ApiModelProperty("整包总净价")
    private Long packageSumNetPrice;

    @ApiModelProperty("刊例总价")
    private Long publishSumPrice;

    @ApiModelProperty("净价总价")
    private Long netSumPrice;

    @ApiModelProperty("招商周期")
    private String projectTime;

    @ApiModelProperty("注意事项")
    private String attention;
}
