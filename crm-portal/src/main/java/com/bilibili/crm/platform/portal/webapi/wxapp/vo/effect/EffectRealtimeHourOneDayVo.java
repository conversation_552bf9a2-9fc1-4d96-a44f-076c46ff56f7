package com.bilibili.crm.platform.portal.webapi.wxapp.vo.effect;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/19 7:56 下午
 */
@Data
public class EffectRealtimeHourOneDayVo {

    @ApiModelProperty("数据")
    private List<EffectRealtimeHourOneDayItemVo> hours_data_list;

    public static EffectRealtimeHourOneDayVo init() {
        EffectRealtimeHourOneDayVo effectRealtimeHourOneDayVo = new EffectRealtimeHourOneDayVo();
        List<EffectRealtimeHourOneDayItemVo> hoursDataList = new ArrayList<>();
        for (int i = 0; i <= 23; i++) {
            EffectRealtimeHourOneDayItemVo oneDayItemDto = EffectRealtimeHourOneDayItemVo.init();
            hoursDataList.add(oneDayItemDto);
        }
        effectRealtimeHourOneDayVo.setHours_data_list(hoursDataList);
        return effectRealtimeHourOneDayVo;
    }
}
