package com.bilibili.crm.platform.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by user on 2017/8/10.
 */

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class AgentFundsQueryVo {

    @ApiModelProperty("代理商账号id")
    private List<Integer> account_ids;

    @ApiModelProperty("转出代理商账号id")
    private List<Integer> from_account_ids;

    @ApiModelProperty("转入代理商账号id")
    private List<Integer> to_account_ids;

    @ApiModelProperty("type 1-广告账户 2-起飞账户 3-花火账户 4-企业号 -1 未知  6 返回的数据不会重复 7 三连推广 8 内容起飞 9 线索通")
    private Integer sys_type;

    @ApiModelProperty("1 代理商-代理商之间转账 2 广告主-广告主之间转账")
    private Integer acc_transfer_type;

    @ApiModelProperty("开始时间")
    private Long begin;

    @ApiModelProperty("结束时间")
    private Long end;

    @ApiModelProperty("当前页")
    @NotNull
    private Integer page;

    @ApiModelProperty("每页大小")
    @NotNull
    private Integer size;

}
