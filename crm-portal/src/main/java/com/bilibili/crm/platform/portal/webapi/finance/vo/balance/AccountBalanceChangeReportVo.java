package com.bilibili.crm.platform.portal.webapi.finance.vo.balance;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2021/11/16 10:30 上午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccountBalanceChangeReportVo {
    @ExcelResources(title = "余额变动时间")
    private String ctime;

    @ExcelResources(title = "消耗归属时间")
    private String belong_date;

    @ExcelResources(title = "流水号")
    private String serial_number;

    @ExcelResources(title = "账号ID")
    private Integer account_id;

    @ExcelResources(title = "账号名称")
    private String account_name;

    @ExcelResources(title = "账户类型")
    private String finance_type_desc;

    @ExcelResources(title = "交易金额")
    private BigDecimal amount;

    @ExcelResources(title = "账户余额")
    private BigDecimal balance_amount;

    @ExcelResources(title = "交易类型")
    private String trade_type_desc;

    /**
     * 对手账号名称
     */
    @ExcelResources(title = "对手信息")
    private String counterparty_account_name;

    @ExcelResources(title = "操作人")
    private String operator;

    @ExcelResources(title = "交易备注")
    private String remark;

}
