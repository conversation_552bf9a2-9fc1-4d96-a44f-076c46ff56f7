package com.bilibili.crm.platform.portal.webapi.white;

import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.white.IWxWriteListService;
import com.bilibili.crm.platform.api.white.dto.NewWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.QueryWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.UpdateWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteDto;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.white.vo.NewWxAppWhiteVo;
import com.bilibili.crm.platform.portal.webapi.white.vo.QueryWxAppWhiteVo;
import com.bilibili.crm.platform.portal.webapi.white.vo.UpdateWxAppWhiteVo;
import com.bilibili.crm.platform.portal.webapi.white.vo.WxAppWhiteVo;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;
import springfox.documentation.annotations.ApiIgnore;

import javax.validation.Valid;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: brady
 * @time: 2020/11/16 4:43 下午
 */
@RestController
@RequestMapping("/web_api/v1/white")
@Api(value = "/white", description = "微信日报白名单")
public class WxAppWhiteListController extends BaseRestfulController {

    @Autowired
    private IWxWriteListService wxWriteListService;

    @ApiOperation(value = "新增日报白名单")
    @RequestMapping(value = "", method = RequestMethod.POST)
    public Response<Integer> createWhite(@ApiIgnore Context context,
                                         @ApiParam("白名单基本信息") @Valid @RequestBody NewWxAppWhiteVo NewWxAppWhiteVo) {

        Integer contractId = wxWriteListService.createWhite(getOperator(context), newVo2Dto(NewWxAppWhiteVo));
        return Response.SUCCESS(contractId);
    }

    @ApiOperation(value = "修改白名单")
    @RequestMapping(value = "", method = RequestMethod.PUT)
    public Response<Integer> updateWhite(@ApiIgnore Context context,
                                         @ApiParam("更新白名单基本信息") @Valid @RequestBody UpdateWxAppWhiteVo updateWxAppWhiteVo) {

        wxWriteListService.updateWhite(getOperator(context), updateVo2Dto(updateWxAppWhiteVo));
        return Response.SUCCESS(null);
    }

    @ApiOperation(value = "白名单查询")
    @RequestMapping(value = "", method = RequestMethod.GET)
    public Response<List<WxAppWhiteVo>> queryWhite(@ApiIgnore Context context,
                                                       @ApiParam("查询白名单基本信息") QueryWxAppWhiteVo queryWxAppWhiteVo) {

        List<WxAppWhiteDto> wxAppWhiteDtos = wxWriteListService.queryWhite(queryVo2Dto(queryWxAppWhiteVo));
        List<WxAppWhiteVo> vos = wxAppWhiteDtos.stream().map(this::queryDtoVo).collect(Collectors.toList());
        return Response.SUCCESS(vos);
    }


    private QueryWxAppWhiteDto queryVo2Dto(QueryWxAppWhiteVo vo) {
        return QueryWxAppWhiteDto.builder()
                .userName(vo.getUser_name())
                .whiteType(vo.getWhite_type())
                .build();
    }


    private NewWxAppWhiteDto newVo2Dto(NewWxAppWhiteVo vo) {
        return NewWxAppWhiteDto.builder()
                .userName(vo.getUser_name())
                .nickName(vo.getNick_name())
                .whiteType(vo.getWhite_type())
                .workNum(vo.getWork_num())
                .isManager(vo.getIs_manager())
                .isPush(vo.getIs_push())
                .build();
    }

    private UpdateWxAppWhiteDto updateVo2Dto(UpdateWxAppWhiteVo vo) {
        return UpdateWxAppWhiteDto.builder()
                .id(vo.getId())
                .whiteStatus(vo.getWhite_status())
                .nickName(vo.getNick_name())
                .workNum(vo.getWork_num())
                .userName(vo.getUser_name())
                .isPush(vo.getIs_push())
                .isManager(vo.getIs_manager())
                .build();
    }

    private WxAppWhiteVo queryDtoVo(WxAppWhiteDto dto) {
        return WxAppWhiteVo.builder()
                .id(dto.getId())
                .nick_name(dto.getNickName())
                .user_name(dto.getUserName())
                .work_num(dto.getWorkNum())
                .mtime(dto.getMtime())
                .operator(dto.getOperator())
                .white_type(dto.getWhiteType())
                .white_status(dto.getWhiteStatus())
                .is_manager(dto.getIsManager())
                .build();
    }


}
