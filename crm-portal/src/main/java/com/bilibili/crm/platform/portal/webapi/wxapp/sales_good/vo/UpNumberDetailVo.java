/**
 * Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.bilibili.crm.platform.portal.webapi.wxapp.sales_good.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * 带货up主数量统计。
 *
 * <AUTHOR>
 * @version $Id: UpNumberDetailVo.java, v 0.1 2023-03-03 6:09 PM <PERSON> Exp $$
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class UpNumberDetailVo implements Serializable {

    private static final long serialVersionUID = 6952800960977936431L;

    @ApiModelProperty("当日up主数")
    private Long day_up_number;

    @ApiModelProperty("近30日均值")
    private BigDecimal month_up_avg_number;

    @ApiModelProperty("近30日up主数量趋势")
    private List<Long> history_up_number;

    @ApiModelProperty("日环比-当日up主数量")
    private BigDecimal day_up_number_compare_rate;

    @ApiModelProperty("周同比-当日up主数量")
    private BigDecimal week_up_number_yoy_ratio;

}