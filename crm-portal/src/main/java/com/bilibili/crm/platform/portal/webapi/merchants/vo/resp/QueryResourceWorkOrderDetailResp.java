package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import com.bilibili.crm.platform.portal.webapi.merchants.vo.NonStandardResourceVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
public class QueryResourceWorkOrderDetailResp {

    @ApiModelProperty("项目id")
    private Integer projectId;

    @ApiModelProperty("席位id")
    private Long seatId;

    @ApiModelProperty("席位类型")
    private String seatType;

    @ApiModelProperty("席位描述")
    private String seatTypeDesc;

    @ApiModelProperty("资源列表")
    private List<NonStandardResourceVo> resources;

    @ApiModelProperty("自定义资源列表")
    private List<NonStandardResourceVo> defineResources;

    @ApiModelProperty("工单类型: LIBRARY-资源库, COMMON_PACKAGE-通包, CUSTOMER_PACKAGE客户包")
    private String workOrderType;

    @ApiModelProperty("项目名称")
    private String projectName;

    @ApiModelProperty("营销团队 :1-垂类生态组, 2-创新赋能组, 3-平台内容组")
    private Byte marketingTeam;

    @ApiModelProperty("招商等级:值有S_ADD,S,A,B,TBD")
    private String investmentLevelType;

    @ApiModelProperty("客户id")
    private Integer customerId;

    @ApiModelProperty("成本说明")
    private String costDesc;

    @ApiModelProperty("客户名称")
    private String customerName;

    @ApiModelProperty("销售id")
    private Integer saleId;

    @ApiModelProperty("销售昵称")
    private String saleNickName;

    @ApiModelProperty("商机id")
    private Integer bsiId;

    @ApiModelProperty("商机名称")
    private String bsiName;

    @ApiModelProperty("整包总净价")
    private Long packageSumNetPrice;

    @ApiModelProperty("非标总净价")
    private Long nonStandardDefineSumNetPrice;

    @ApiModelProperty("硬广总净价")
    private Long hardAdSumNetPrice;

    @ApiModelProperty("商单总净价")
    private Long businessOrderSumNetPrice;

    @ApiModelProperty("资源净价总价")
    private Long netSumPrice;

    @ApiModelProperty("资源刊例总价")
    private Long publishSumPrice;

    @ApiModelProperty("非标净价总价")
    private Long nonStandardNetSumPrice;

    @ApiModelProperty("非标刊例总价")
    private Long nonStandardPublishSumPrice;

    @ApiModelProperty("自定义资源净价总价")
    private Long defineNetSumPrice;

    @ApiModelProperty("自定义资源刊例总价")
    private Long definePublishSumPrice;

    @ApiModelProperty("招商周期")
    private String projectTime;

    @ApiModelProperty("注意事项")
    private String attention;

    @ApiModelProperty("工单状态")
    private Byte status;

    @ApiModelProperty("导出字段")
    private List<String> exportFields;
}
