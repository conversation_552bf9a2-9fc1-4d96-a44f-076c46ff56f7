package com.bilibili.crm.platform.portal.webapi.achievement.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.customer_operate_label.bo.QueryCustomerOperateLabelParam;
import com.bilibili.crm.biz.customer_operate_label.dto.EasyCustomerOperateLabelDto;
import com.bilibili.crm.biz.customer_operate_label.enums.CustomerOperateLabelStatusEnum;
import com.bilibili.crm.biz.customer_operate_label.enums.CustomerOperateLabelTypeEnum;
import com.bilibili.crm.biz.customer_operate_label.repository.CustomerOperateLabelRepository;
import com.bilibili.crm.platform.api.achievement.IAchievementContractService;
import com.bilibili.crm.platform.api.achievement.IAchievementPickUpService;
import com.bilibili.crm.platform.api.achievement.IAchievementRtbService;
import com.bilibili.crm.platform.api.achievement.dto.*;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromBillService;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromCluePassService;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromWalletService;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.company.dto.CorporationGroupDto;
import com.bilibili.crm.platform.api.company.service.ICorporationGroupService;
import com.bilibili.crm.platform.api.enums.PickupAchieveTypeEnum;
import com.bilibili.crm.platform.api.income.dto.ContractAdIncome;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.non.standard.dto.ProjectItemPackageDto;
import com.bilibili.crm.platform.api.non.standard.service.INonStandardPackageService;
import com.bilibili.crm.platform.api.order.dto.CrmOrderExtraDto;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.api.settle.BillDetailReport;
import com.bilibili.crm.platform.api.stat.IPerformanceAdService;
import com.bilibili.crm.platform.api.stat.dto.AdvertiserLaunchDataDto;
import com.bilibili.crm.platform.api.stat.dto.PerformanceAdQueryDto;
import com.bilibili.crm.platform.api.stat.dto.TotalAndPageListDto;
import com.bilibili.crm.platform.biz.common.ClosedType;
import com.bilibili.crm.platform.biz.crm.account.dao.read.ExtAccAccountDao;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.service.achievement.AchievementReportService;
import com.bilibili.crm.platform.biz.service.achievement.AchievementRtbReportService;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchieveContractParamHelper;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchievementHelper;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchievementRtbServiceHelper;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.order.OrderUtils;
import com.bilibili.crm.platform.biz.service.settle_account.common.CheckingType;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.biz.util.DateUtils;
import com.bilibili.crm.platform.common.BillSourceType;
import com.bilibili.crm.platform.common.customer.CustomerCategoryType;
import com.bilibili.crm.platform.portal.webapi.achievement.helper.WebAchievementHelper;
import com.bilibili.crm.platform.portal.webapi.achievement.helper.export.WebAchieveFlyExportHelper;
import com.bilibili.crm.platform.portal.webapi.achievement.helper.export.WebDashboardExportHelper;
import com.bilibili.crm.platform.portal.webapi.achievement.vo.*;
import com.bilibili.crm.platform.portal.webapi.customer.vo.AgentPortraitTableCardConfirmVo;
import com.bilibili.crm.platform.utils.AdUtils;
import com.google.common.collect.Lists;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.MapUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: brady
 * @time: 2020/10/22 2:56 下午
 */
@Service
@Slf4j
public class WebAchieveBoardService {
    @Autowired
    private IAchievementRtbService rtbService;
    @Autowired
    private AchievementRtbServiceHelper rtbHelper;
    @Autowired
    private WebAchievementHelper webAchievementHelper;
    @Autowired
    private IAchievementContractService achievementContractService;
    @Autowired
    private AchievementHelper achievementHelper;
    @Autowired
    private AchievementRtbReportService rtbReportService;
    @Autowired
    private AchievementReportService reportService;
    @Autowired
    private OrderUtils orderUtils;
    @Autowired
    private IAchievementPickUpService achievementPickUpService;
    @Autowired
    private IncomeUtil incomeUtil;
    @Autowired
    private IAchieveFromWalletService achieveFromWalletService;
    @Autowired
    private IAchieveFromBillService achieveFromBillService;
    @Autowired
    private IAgentService iagentService;
    @Autowired
    private AccAccountRepo accAccountRepo;
    @Autowired
    private IPerformanceAdService performanceAdService;
    @Autowired
    private ICorporationGroupService corporationGroupService;


    @Autowired
    private AchieveContractParamHelper achieveContractParamHelper;
    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private IAchieveFromCluePassService achieveFromCluePassService;

    @Autowired
    private IOrderService iOrderService;

    @Autowired
    private INonStandardPackageService iNonStandardPackageService;

    @Resource
    private CustomerOperateLabelRepository customerOperateLabelRepository;

    @Resource
    private WebDashboardExportHelper webDashboardExportHelper;

    @Getter
    @Value("${crm.quota.export.time.range:**********}")
    private Long quotaExportRange;
    @Autowired
    private ExtAccAccountDao extAccAccountDao;
    public AchievementRtbQuotaVo getBoardTotalQuotaV2(Operator operator, QueryAchieveBoardVo queryVo) {

        QueryAchieveDto queryDto = queryVo2Dto(queryVo);
        return getBoardTotalQuotaV2(operator, queryDto);
    }

    /**
     * 总额指示板  指标卡
     */
    public AchievementRtbQuotaVo getBoardTotalQuotaV2(Operator operator, QueryAchieveDto queryDto) {

        //合约广告已确认
        AchievementContractCheckQuotaVo contractCheckQuotaVo = this.getContractCheckedQuota(operator, queryDto);
        BigDecimal check_amount = contractCheckQuotaVo.getTotal_amount();
        List<AchievementRtbData> adx = rtbService.getAdxDaySourceData(operator, queryDto);
        List<AchievementRtbData> dpa = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.DPA, achieveFromWalletService::getWalletAggByDayByWideES);
        // 属于合约广告的产品类型：CPT、GD、闪屏、TopView、非标、搜索CPT、OTT、直播类订单、直播商单、商业起飞（后付费）
        //品牌广告   目前包括：CPT、GD、闪屏、TopView、非标、搜索CPT、OTT、直播类订单
        AchievementContractCheckQuotaDto cpt = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.CPT);
        BigDecimal cptAmount = amountSum(cpt);
        AchievementContractCheckQuotaDto gd = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.GD);
        BigDecimal gdAmount = amountSum(gd);
        AchievementContractCheckQuotaDto ssa = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.SSA);
        AchievementContractCheckQuotaDto topView = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.TOP_VIEW);
        AchievementContractCheckQuotaDto noStand = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.NON_STANDARD);
        AchievementContractCheckQuotaDto searchCpt = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.SEARCH_CPT);
        AchievementContractCheckQuotaDto ott = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.OTT_TOP_VIEW);
        AchievementContractCheckQuotaDto live = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.LIVE);
        AchievementContractCheckQuotaDto livePickUp = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.LIVE_PICK_UP);
        AchievementContractCheckQuotaDto businessPostPay = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.BUSINESS_FLY_POST_PAY);
        //花火已确认
        List<AchievementDto> pickConfirmed = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryDto, IncomeComposition.PICK_UP_CONFIRMED);
        //效果广告   目前包括：CPC、竞价CPM、DPA、ADX
        //cpc
        List<AchievementRtbData> cpc = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.CPC, achieveFromWalletService::getWalletAggByDayByWideES);
        //cpm
        List<AchievementRtbData> cpm = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.CPM, achieveFromWalletService::getWalletAggByDayByWideES);
        // 效果实结
        List<AchievementRtbData> close = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.EFFECT_CLOSE, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> contentFly = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.CONTENT_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> personFly = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        //up主广告   目前包括：花火商单、直播商单、商业起飞（后付费）、商业起飞（预付费）、内容起飞、个人起飞
        List<AchievementRtbData> pickUpOrder = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.PICKUP_ORDER, achieveFromBillService::getBillAggByDayByWideES);
        List<AchievementRtbData> businessAdvancePay = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> cluePass = rtbService.getConsumeDataByFunction(operator, queryDto, IncomeComposition.CLUE_PASS, achieveFromCluePassService::queryCluePass);
        //品牌收入总额
        BigDecimal ssaAmount = amountSum(ssa);
        BigDecimal topViewAmount = amountSum(topView);
        BigDecimal noStandAmount = amountSum(noStand);
        BigDecimal searchCptAmount = amountSum(searchCpt);
        BigDecimal ottAmount = amountSum(ott);
        BigDecimal liveAmount = amountSum(live);
        BigDecimal businessPostPayAmount = amountSum(businessPostPay);
        BigDecimal brandConsume = cptAmount.add(gdAmount).add(ssaAmount).add(topViewAmount).add(noStandAmount)
                .add(searchCptAmount).add(ottAmount).add(liveAmount).add(businessPostPayAmount);
        BigDecimal cpcAmount = amountSum(cpc);
        BigDecimal adxAmount = amountSum(adx);
        BigDecimal dpaAmount = amountSum(dpa);
        BigDecimal cpmAmount = amountSum(cpm);
        BigDecimal cluePassAmount = amountSum(cluePass);
        BigDecimal pickUpOrderAmount = amountSum(pickUpOrder);
        BigDecimal pickConfirmedAmount = pickConfirmed.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal livePickUpAmount = amountSum(livePickUp);
        BigDecimal contentFlyAmount = amountSum(contentFly);
        BigDecimal personFlyAmount = amountSum(personFly);
        BigDecimal businessAdvancePayAmount = amountSum(businessAdvancePay);
        BigDecimal closeAmount = amountSum(close);
        BigDecimal rtbSum = cpcAmount.add(adxAmount).add(dpaAmount).add(cpmAmount).add(contentFlyAmount).add(personFlyAmount).add(businessAdvancePayAmount).add(closeAmount);
        BigDecimal upSum = pickUpOrderAmount.add(livePickUpAmount).add(cluePassAmount);
        TotalAndPageListDto<AdvertiserLaunchDataDto, AdvertiserLaunchDataDto> totalAndPageListDto = performanceAdService.queryAdvertiserLaunchDataWithPageAndTotal(PerformanceAdQueryDto.builder()
                .accountIds(Lists.newArrayList(342572))
                .fromTime(queryDto.getDateBegin())
                .toTime(queryDto.getDateEnd())
                .onlyCustomer(true)
                .groupType(3)
                .build(), 1, 10);
        contentFlyAmount = contentFlyAmount.add(totalAndPageListDto.getTotal().getTotalConsume());
        BigDecimal total = check_amount.add(businessAdvancePayAmount).add(pickConfirmedAmount).add(cpcAmount).add(cpmAmount)
                .add(dpaAmount).add(adxAmount).add(contentFlyAmount).add(personFlyAmount).add(closeAmount).add(cluePassAmount);
        return AchievementRtbQuotaVo.builder()
                .total_amount(total)
                .contract_amount(check_amount)
                .contract_amount_living(contractCheckQuotaVo.getLive_pickup_amount())
                .contract_amount_other(contractCheckQuotaVo.getOther_pickup_amount())
                .contract_amount_no_other(contractCheckQuotaVo.getTotal_amount().subtract(contractCheckQuotaVo.getLive_pickup_amount()).subtract(contractCheckQuotaVo.getOther_pickup_amount()))
                .pickup_confirmed_amount(pickConfirmedAmount)
                .close_amount(closeAmount)
                .close_ratio(incomeUtil.divide(closeAmount, total, 4))
                .total_ratio(incomeUtil.divide(total, total, 4))
                .brand_amount(brandConsume)
                .bsi_fly_amount(businessAdvancePayAmount)
                .bsifly_ratio(incomeUtil.divide(businessAdvancePayAmount, total, 4))
                .up_amount(upSum)
                .rtb_amount(rtbSum)
                .rtb_ratio(incomeUtil.divide(cpcAmount.add(cpmAmount), total, 4))  // CPC + CPM的总和占比
                .cpt(cptAmount)
                .cpt_ratio(incomeUtil.divide(cptAmount, total, 4))
                .cpc(cpcAmount)
                .cpc_ratio(incomeUtil.divide(cpcAmount, total, 4))
                .gd(gdAmount)
                .gd_ratio(incomeUtil.divide(gdAmount, total, 4))
                .ssa(ssaAmount)
                .ssa_ratio(incomeUtil.divide(ssaAmount, total, 4))
                .topView(topViewAmount)
                .topView_ratio(incomeUtil.divide(topViewAmount, total, 4))
                .noStand(noStandAmount)
                .noStand_ratio(incomeUtil.divide(noStandAmount, total, 4))
                .search_cpt(searchCptAmount)
                .search_cpt_ratio(incomeUtil.divide(searchCptAmount, total, 4))
                .ott(ottAmount)
                .ott_ratio(incomeUtil.divide(ottAmount, total, 4))
                .live(liveAmount)
                .live_ratio(incomeUtil.divide(liveAmount, total, 4))
                .cpm(cpmAmount)
                .cpm_ratio(incomeUtil.divide(cpmAmount, total, 4))
                .dpa_amount(dpaAmount)
                .dpa_ratio(incomeUtil.divide(dpaAmount, total, 4))
                .adx_amount(adxAmount)
                .adx_ratio(incomeUtil.divide(adxAmount, total, 4))
                .pick_up_order(pickUpOrderAmount)
                .pick_up_order_ratio(incomeUtil.divide(pickUpOrderAmount, total, 4))
                .live_pick_up(livePickUpAmount)
                .live_pick_up_ratio(incomeUtil.divide(livePickUpAmount, total, 4))
                .business_postPay(businessPostPayAmount)
                .business_postPay_ratio(incomeUtil.divide(businessPostPayAmount, total, 4))
                .business_advance_pay(businessAdvancePayAmount)
                .business_advance_pay_ratio(incomeUtil.divide(businessAdvancePayAmount, total, 4))
                .content_fly(contentFlyAmount)
                .content_fly_ratio(incomeUtil.divide(contentFlyAmount, total, 4))
                .personal_fly(personFlyAmount)
                .personal_fly_ratio(incomeUtil.divide(personFlyAmount, total, 4))
                .contract_check_ratio(incomeUtil.divide(check_amount, total, 4))
                .pickup_confirmed(pickConfirmedAmount)
                .pickup_confirmed_ratio(incomeUtil.divide(pickConfirmedAmount, total, 4))
                .clue_pass_amount(cluePassAmount)
                .clue_pass_amount_ratio(incomeUtil.divide(cluePassAmount, total, 4))
                .build();
    }

    private BigDecimal amountSum(List<AchievementRtbData> data) {
        return data.stream().map(AchievementRtbData::getTotalConsume).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public static BigDecimal amountSum(AchievementContractCheckQuotaDto data) {
        return data.getClosedAmount().add(data.getUnClosedAmount());
    }

    public static BigDecimal amountSumForAgent(AchievementContractCheckQuotaDto data, QueryAchieveDto queryDto) {
        if (queryDto.getCloseBillDateBegin() != null || queryDto.getCloseBillDateEnd() != null) {
            return data.getClosedAmount();
        }
        return data.getClosedAmount().add(data.getUnClosedAmount());
    }

//    private BigDecimal getPickupPostAmountQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
//        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryVo2Dto(queryVo), IncomeComposition.BUSINESS_FLY_POST_PAY);
//        return (quotaDto.getClosedAmount().add(quotaDto.getUnClosedAmount()));
//    }


    private BigDecimal getLiveQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryVo2Dto(queryVo), IncomeComposition.LIVE);
        return (quotaDto.getClosedAmount().add(quotaDto.getUnClosedAmount()));
    }

//    private BigDecimal getBrandConsume(Operator operator, QueryAchieveBoardVo queryVo) {
//        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryVo2Dto(queryVo), IncomeComposition.BRADN);
//        return (quotaDto.getClosedAmount().add(quotaDto.getUnClosedAmount()));
//    }

//    private BigDecimal getPickupConfirmQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
//        return pickUpConfirmEdQuota(operator, queryVo).getTotal_consume();
//    }

    /**
     * 总额指示板  指标卡
     */
    public AchievementRtbQuotaVo getBoardTotalQuotaForAgentPortrait(Operator operator, QueryAchieveDto queryDto) {

        QueryAchieveDto rtbQueryDto = QueryAchieveDto.builder().build();
        QueryAchieveDto pickUpQueryDto = QueryAchieveDto.builder().build();
        BeanUtils.copyProperties(queryDto, rtbQueryDto);
        rtbQueryDto.setOrAgentIds(queryDto.getRtbNoBelongAgentIds());
        BeanUtils.copyProperties(queryDto, pickUpQueryDto);
        pickUpQueryDto.setOrAgentIds(queryDto.getPickUpNoBelongAgentIds());
        buildClosedTime(queryDto);
        //合约广告已确认
        BigDecimal check_amount = new BigDecimal(0);
        AchievementContractCheckQuotaVo vo = getContractCheckedQuota(operator, queryDto);
        if (queryDto.getCloseBillDateBegin() != null || queryDto.getCloseBillDateEnd() != null) {
            check_amount = vo.getClosed_amount();
        } else {
            check_amount = vo.getTotal_amount();
        }


        //ADX
        List<AchievementRtbData> adx = rtbService.getAdxDaySourceData(operator, rtbQueryDto);
        //dpa
        List<AchievementRtbData> dpa = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.DPA, achieveFromWalletService::getWalletAggByDayByWideES);

        // 属于合约广告的产品类型：CPT、GD、闪屏、TopView、非标、搜索CPT、OTT、直播类订单、直播商单、商业起飞（后付费）
        //品牌广告   目前包括：CPT、GD、闪屏、TopView、非标、搜索CPT、OTT、直播类订单
        AchievementContractCheckQuotaDto cpt = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.CPT);
        BigDecimal cptAmount = amountSumForAgent(cpt, queryDto);
        AchievementContractCheckQuotaDto gd = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.GD);
        BigDecimal gdAmount = amountSumForAgent(gd, queryDto);
        AchievementContractCheckQuotaDto ssa = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.SSA);
        AchievementContractCheckQuotaDto topView = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.TOP_VIEW);
        AchievementContractCheckQuotaDto noStand = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.NON_STANDARD);
        AchievementContractCheckQuotaDto searchCpt = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.SEARCH_CPT);
        AchievementContractCheckQuotaDto ott = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.OTT_TOP_VIEW);
        AchievementContractCheckQuotaDto live = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.LIVE);
        AchievementContractCheckQuotaDto livePickUp = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.LIVE_PICK_UP);
        AchievementContractCheckQuotaDto businessPostPay = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.BUSINESS_FLY_POST_PAY);
        //花火已确认
        List<AchievementDto> pickConfirmed = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, pickUpQueryDto, IncomeComposition.PICK_UP_CONFIRMED);
        //助推商单
        List<AchievementDto> boostingPickConfirmed = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, pickUpQueryDto, IncomeComposition.BOOSTING_CONFIRMED);
        pickConfirmed.addAll(boostingPickConfirmed);

        //效果广告   目前包括：CPC、竞价CPM、DPA、ADX
        //cpc
        List<AchievementRtbData> cpc = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CPC, achieveFromWalletService::getWalletAggByDayByWideES);
        //cpm
        List<AchievementRtbData> cpm = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CPM, achieveFromWalletService::getWalletAggByDayByWideES);
        // 效果实结
        List<AchievementRtbData> close = rtbService.getConsumeDataByFunctionV2(operator, queryDto, IncomeComposition.EFFECT_CLOSE, achieveFromWalletService::getWalletAggByDayByWideES);

        List<AchievementRtbData> contentFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CONTENT_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> personFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        //up主广告   目前包括：花火商单、直播商单、商业起飞（后付费）、商业起飞（预付费）、内容起飞、个人起飞
        List<AchievementRtbData> pickUpOrder = rtbService.getConsumeDataByFunctionV2(operator, pickUpQueryDto, IncomeComposition.PICKUP_ORDER, achieveFromBillService::getBillAggByDayByWideES);
        List<AchievementRtbData> businessAdvancePay = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);

        //品牌收入总额
        BigDecimal ssaAmount = amountSumForAgent(ssa, queryDto);
        BigDecimal topViewAmount = amountSumForAgent(topView, queryDto);
        BigDecimal noStandAmount = amountSumForAgent(noStand, queryDto);
        BigDecimal searchCptAmount = amountSumForAgent(searchCpt, queryDto);
        BigDecimal ottAmount = amountSumForAgent(ott, queryDto);
        BigDecimal liveAmount = amountSumForAgent(live, queryDto);

        BigDecimal brandConsume = cptAmount.add(gdAmount).add(ssaAmount).add(topViewAmount).add(noStandAmount)
                .add(searchCptAmount).add(ottAmount).add(liveAmount);

        BigDecimal cpcAmount = amountSum(cpc);
        BigDecimal adxAmount = amountSum(adx);
        BigDecimal dpaAmount = amountSum(dpa);
        BigDecimal cpmAmount = amountSum(cpm);
        BigDecimal closeAmount = amountSum(close);

        BigDecimal rtbSum = cpcAmount.add(adxAmount).add(dpaAmount).add(cpmAmount);

        BigDecimal pickUpOrderAmount = amountSum(pickUpOrder);
        BigDecimal pickConfirmedAmount = pickConfirmed.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        BigDecimal livePickUpAmount = amountSumForAgent(livePickUp, queryDto);
        BigDecimal businessPostPayAmount = amountSumForAgent(businessPostPay, queryDto);
        BigDecimal contentFlyAmount = amountSum(contentFly);
        BigDecimal personFlyAmount = amountSum(personFly);
        BigDecimal businessAdvancePayAmount = amountSum(businessAdvancePay);


        BigDecimal upSum = pickUpOrderAmount.add(livePickUpAmount).add(businessAdvancePayAmount)
                .add(businessPostPayAmount).add(contentFlyAmount).add(personFlyAmount);

        BigDecimal total = check_amount.add(businessAdvancePayAmount).add(pickConfirmedAmount).add(cpcAmount).add(cpmAmount)
                .add(dpaAmount).add(adxAmount).add(contentFlyAmount).add(personFlyAmount).add(closeAmount);

        return AchievementRtbQuotaVo.builder()
                .total_amount(total)
                .close_amount(closeAmount)
                .close_ratio(incomeUtil.divide(closeAmount, total, 4))
                .total_ratio(incomeUtil.divide(total, total, 4))
                .brand_amount(brandConsume)
                .bsi_fly_amount(businessAdvancePayAmount)
                .bsifly_ratio(incomeUtil.divide(businessAdvancePayAmount, total, 4))
                .up_amount(upSum)
                .rtb_amount(rtbSum)
                .rtb_ratio(incomeUtil.divide(cpcAmount.add(cpmAmount), total, 4))  // CPC + CPM的总和占比
                .cpt(cptAmount)
                .cpt_ratio(incomeUtil.divide(cptAmount, total, 4))
                .cpc(cpcAmount)
                .cpc_ratio(incomeUtil.divide(cpcAmount, total, 4))
                .gd(gdAmount)
                .gd_ratio(incomeUtil.divide(gdAmount, total, 4))
                .ssa(ssaAmount)
                .ssa_ratio(incomeUtil.divide(ssaAmount, total, 4))
                .topView(topViewAmount)
                .topView_ratio(incomeUtil.divide(topViewAmount, total, 4))
                .noStand(noStandAmount)
                .noStand_ratio(incomeUtil.divide(noStandAmount, total, 4))
                .search_cpt(searchCptAmount)
                .search_cpt_ratio(incomeUtil.divide(searchCptAmount, total, 4))
                .ott(ottAmount)
                .ott_ratio(incomeUtil.divide(ottAmount, total, 4))
                .live(liveAmount)
                .live_ratio(incomeUtil.divide(liveAmount, total, 4))
                .cpm(cpmAmount)
                .cpm_ratio(incomeUtil.divide(cpmAmount, total, 4))
                .dpa_amount(dpaAmount)
                .dpa_ratio(incomeUtil.divide(dpaAmount, total, 4))
                .adx_amount(adxAmount)
                .adx_ratio(incomeUtil.divide(adxAmount, total, 4))
                .pick_up_order(pickUpOrderAmount)
                .pick_up_order_ratio(incomeUtil.divide(pickUpOrderAmount, total, 4))
                .live_pick_up(livePickUpAmount)
                .live_pick_up_ratio(incomeUtil.divide(livePickUpAmount, total, 4))
                .business_postPay(businessPostPayAmount)
                .business_postPay_ratio(incomeUtil.divide(businessPostPayAmount, total, 4))
                .business_advance_pay(businessAdvancePayAmount)
                .business_advance_pay_ratio(incomeUtil.divide(businessAdvancePayAmount, total, 4))
                .content_fly(contentFlyAmount)
                .content_fly_ratio(incomeUtil.divide(contentFlyAmount, total, 4))
                .personal_fly(personFlyAmount)
                .personal_fly_ratio(incomeUtil.divide(personFlyAmount, total, 4))
                .contract_check_ratio(incomeUtil.divide(check_amount, total, 4))
                .pickup_confirmed(pickConfirmedAmount)
                .pickup_confirmed_ratio(incomeUtil.divide(pickConfirmedAmount, total, 4))
                .build();
    }

    public void buildClosedTime(QueryAchieveDto queryDto) {
        if (queryDto.getCloseBillDateBegin() != null && queryDto.getDateBegin() != null) {
            queryDto.setIsClosed(ClosedType.IS_CLOSED.getCode());
            queryDto.setDateBegin(queryDto.getCloseBillDateBegin().before(queryDto.getDateBegin()) ? queryDto.getDateBegin() : queryDto.getCloseBillDateBegin());
        }
        if (queryDto.getDateEnd() != null && queryDto.getCloseBillDateEnd() != null) {
            queryDto.setIsClosed(ClosedType.IS_CLOSED.getCode());
            queryDto.setDateEnd(queryDto.getCloseBillDateEnd().before(queryDto.getDateEnd()) ? queryDto.getCloseBillDateEnd() : queryDto.getDateEnd());
        }
    }


    /**
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementConsumeQuotaVo getBsFlyQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> data = rtbService.getConsumeDataByFunctionV2(operator, queryVo2Dto(queryVo), IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()))
                .cash_consume(rtbData.getCashAmount())
                .build();
    }

    /**
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementConsumeQuotaVo getCluePassQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> data = rtbService.getConsumeDataByFunction(operator, queryVo2Dto(queryVo), IncomeComposition.CLUE_PASS, achieveFromCluePassService::queryCluePass);
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()))
                .cash_consume(rtbData.getCashAmount())
                .build();
    }

    /**
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementConsumeQuotaVo getPersonalFlyQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> data = rtbService.getConsumeDataByFunctionV2(operator, queryVo2Dto(queryVo), IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .fly_support_consume(rtbData.getFlySupportAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()))
                .cash_consume(rtbData.getCashAmount())
                .build();
    }


    /**
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementConsumeQuotaVo getContentFlyQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> data = rtbService.getConsumeDataByFunctionV2(operator, queryVo2Dto(queryVo), IncomeComposition.CONTENT_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        //TODO 加上342572账号的消耗数字 参照 https://www.tapd.bilibili.co/********/prong/stories/view/11********002895973
        TotalAndPageListDto<AdvertiserLaunchDataDto, AdvertiserLaunchDataDto> totalAndPageListDto = performanceAdService.queryAdvertiserLaunchDataWithPageAndTotal(PerformanceAdQueryDto.builder()
                .accountIds(Lists.newArrayList(342572))
                .fromTime(new Timestamp(queryVo.getDate_begin()))
                .toTime(new Timestamp(queryVo.getDate_end()))
                .onlyCustomer(true)
                .groupType(3)
                .build(), 1, 10);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume().add(totalAndPageListDto.getTotal().getTotalConsume()))
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()).add(totalAndPageListDto.getTotal().getTotalRedPacketConsume()).add(totalAndPageListDto.getTotal().getTotalSpecialRedPacketConsume()))
                .cash_consume(rtbData.getCashAmount().add(totalAndPageListDto.getTotal().getTotalCashConsume()))
                .build();
    }

    /**
     * 必选指示板
     */
    public AchievementConsumeQuotaVo getRtbQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementConsumeQuotaVo vo = getRtbQuotaData(operator, queryVo);
        return vo;
    }

    private AchievementConsumeQuotaVo getRtbQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {

//        List<AchievementRtbData> data = rtbService.getRtbSourceDataByES(operator, queryVo2Dto(queryVo));
//        List<AchievementRtbData> data = rtbService.getRtbSourceDataByWideES(operator, queryVo2Dto(queryVo));
        List<AchievementRtbData> data = rtbService.getRtbSourceDataAggByTimeByWideES(operator, queryVo2Dto(queryVo));
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()))
                .cash_consume(rtbData.getCashAmount())
                .build();
    }

    /**
     * 商业起飞指示板
     */
    public AchievementConsumeQuotaVo getBusinessFlyQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementConsumeQuotaVo vo = getBusinessFlyQuotaData(operator, queryVo);
        return vo;
    }

    public AchievementConsumeQuotaVo getBusinessFlyQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> data = rtbService.getBsiFlySourceDataByES(operator, queryVo2Dto(queryVo));
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()))
                .cash_consume(rtbData.getCashAmount())
                .build();
    }

    /**
     * Adx指示板
     */
    public AchievementConsumeQuotaVo getAdxQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementConsumeQuotaVo vo = getAdxQuotaData(operator, queryVo);
        return vo;
    }

    public AchievementConsumeQuotaVo getAdxQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> data = rtbService.getAdxDaySourceDataWithClose(operator, queryVo2Dto(queryVo));
        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);
        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .cash_consume(rtbData.getCashAmount())
                .build();
    }

    /**
     * dpa指示板
     */
    public AchievementConsumeQuotaVo getDpaQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementConsumeQuotaVo vo = getDpaQuotaData(operator, queryVo);
        return vo;
    }

    public AchievementConsumeQuotaVo getDpaQuotaData(Operator operator, QueryAchieveBoardVo queryVo) {
//        List<AchievementRtbData> data = rtbService.getDpaSourceDataByES(operator, queryVo2Dto(queryVo));
        //指标卡查宽表
//        List<AchievementRtbData> data = rtbService.getDpaSourceDataByWideES(operator, queryVo2Dto(queryVo));
        List<AchievementRtbData> data = rtbService.getConsumeDataByFunctionV2(operator, queryVo2Dto(queryVo), IncomeComposition.DPA, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> closeData = rtbService.getConsumeDataByFunctionV2(operator, queryVo2Dto(queryVo), IncomeComposition.DPA_CLOSE, achieveFromWalletService::getWalletAggByDayByWideES);
        data = Stream.of(data, closeData).flatMap(Collection::stream).collect(Collectors.toList());

        AchievementRtbData rtbData = rtbHelper.calculateAchievementRtbData(data);

        return AchievementConsumeQuotaVo.builder()
                .total_consume(rtbData.getTotalConsume())
                .red_packet_consumer(rtbData.getRedPacketAmount())
                .special_red_packet_consumer(rtbData.getSpecialRedPacketAmount())
                .all_red_packet_consumer(rtbData.getRedPacketAmount().add(rtbData.getSpecialRedPacketAmount()))
                .cash_consume(rtbData.getCashAmount())
                .build();
    }

    /**
     * 合约广告已下单待确认金额
     *
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementContractCheckQuotaVo getContractCheckedQuota(Operator operator, QueryAchieveContractVo queryVo) {
        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getCheckedBillQuotaDto(operator, queryVo2Dto(queryVo));
        return AchievementContractCheckQuotaVo.builder()
                .closed_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount()))
                .un_closed_amount(Utils.fromFenToYuan(quotaDto.getUnClosedAmount()))
                .live_pickup_amount(Utils.fromFenToYuan(quotaDto.getLivePickUpAmount()))
                .other_pickup_amount(Utils.fromFenToYuan(quotaDto.getOtherPickUpAmount()))
                .brand_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount().add(quotaDto.getUnClosedAmount()).subtract(quotaDto.getLivePickUpAmount()).subtract(quotaDto.getOtherPickUpAmount())))
                .total_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount()
                        .add(quotaDto.getUnClosedAmount())))
                .build();
    }


    public AchievementContractCheckQuotaVo getContractHistoryUnClosedQuota(Operator operator, QueryAchieveContractVo vo) {
        Timestamp lastQuarter = incomeTimeUtil.getStartOfLastQuarter(new Timestamp(System.currentTimeMillis()));
        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getHistoryUnclosedBillQuotaDto(operator, QueryAchieveDto.builder()
                .dateBegin(null)
                .dateEnd(Utils.getEndOfDay(incomeTimeUtil.getQuarterEndDate(lastQuarter)))
                .customerIds(vo.getCustomer_ids())
                .accountIds(vo.getAccount_ids())
                .agentIds(vo.getAgent_ids())
                .saleGroupIds(vo.getSale_group_ids())
                .saleIds(vo.getSale_ids())
                .build());
        return AchievementContractCheckQuotaVo.builder()
                .closed_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount()))
                .un_closed_amount(Utils.fromFenToYuan(quotaDto.getUnClosedAmount()))
                .live_pickup_amount(Utils.fromFenToYuan(quotaDto.getLivePickUpAmount()))
                .other_pickup_amount(Utils.fromFenToYuan(quotaDto.getOtherPickUpAmount()))
                .brand_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount().add(quotaDto.getUnClosedAmount()).subtract(quotaDto.getLivePickUpAmount()).subtract(quotaDto.getOtherPickUpAmount())))
                .total_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount()
                        .add(quotaDto.getUnClosedAmount())))
                .build();
    }


    public AchievementContractCheckQuotaVo getContractCheckedQuota(Operator operator, QueryAchieveDto queryAchieveDto) {
        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getCheckedBillQuotaDto(operator, queryAchieveDto);
        return AchievementContractCheckQuotaVo.builder()
                .closed_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount()))
                .un_closed_amount(Utils.fromFenToYuan(quotaDto.getUnClosedAmount()))
                .total_amount(Utils.fromFenToYuan(quotaDto.getClosedAmount().add(quotaDto.getUnClosedAmount())))
                .live_pickup_amount(Utils.fromFenToYuan(quotaDto.getLivePickUpAmount()))
                .other_pickup_amount(Utils.fromFenToYuan(quotaDto.getOtherPickUpAmount()))
                .build();
    }

    /**
     * 待确认金额
     *
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementContractUnCheckQuotaVo getContractUnCheckedQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementContractUnCheckQuotaDto quotaDto = achievementContractService.getUnCheckedBillQuotaDto(operator, queryVo2Dto(queryVo));
        return AchievementContractUnCheckQuotaVo.builder()
                .un_executed_amount(Utils.fromFenToYuan(quotaDto.getUnExecutedAmount()))
                .un_delivery_amount(Utils.fromFenToYuan(quotaDto.getUnDeliveryAmount()))
                .un_executed_live_pickup_amount(Utils.fromFenToYuan(quotaDto.getLivePickUpAmount()))
                .un_executed_other_pickup_amount(Utils.fromFenToYuan(quotaDto.getOtherPickUpAmount()))
                .brand_amount(Utils.fromFenToYuan(quotaDto.getUnExecutedAmount().add(quotaDto.getUnDeliveryAmount()).subtract(quotaDto.getLivePickUpAmount()).subtract(quotaDto.getOtherPickUpAmount())))
                .total_amount(Utils.fromFenToYuan(quotaDto.getUnExecutedAmount()
                        .add(quotaDto.getUnDeliveryAmount())
                ))
                .build();
    }

    public AchievementContractUnCheckQuotaVo getUnDeliveryQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        AchievementContractUnCheckQuotaDto quotaDto = achievementContractService.getUnDeliveryQuotaDto(operator, queryVo2Dto(queryVo));
        return AchievementContractUnCheckQuotaVo.builder()
//                .un_executed_amount(Utils.fromFenToYuan(quotaDto.getUnExecutedAmount()))
                .un_delivery_amount(Utils.fromFenToYuan(quotaDto.getUnDeliveryAmount()))
//                .total_amount(Utils.fromFenToYuan(quotaDto.getUnExecutedAmount().add(quotaDto.getUnDeliveryAmount())))
                .build();
    }

    /**
     * @param operator
     * @param queryAchieveDto
     * @return
     */
    public AchievementContractForecastQuotaVo getContractForecastQuota(Operator operator, QueryAchieveDto queryAchieveDto) {
        List<ContractAdIncome> bills = achievementContractService.getContractForecast(operator, queryAchieveDto);
        if (CollectionUtils.isEmpty(bills)) {
            return AchievementContractForecastQuotaVo.builder().total_amount(BigDecimal.ZERO).un_audit_amount(BigDecimal.ZERO).un_push_amount(BigDecimal.ZERO).build();
        }
        BigDecimal unAuditAmount = achievementHelper.getBusinessReservedUnAuditAmount(bills);
        BigDecimal unPushAmount = achievementHelper.getBusinessReservedUnPushAmount(bills);
        List<ContractAdIncome> otherPickupBill = achieveContractParamHelper.filterCrmPickUpOther(bills);
        List<ContractAdIncome> livePickupBill = achieveContractParamHelper.filterCrmPickUpLiving(bills);
        BigDecimal otherPickUpAmount = incomeUtil.reduceIncome(otherPickupBill);
        BigDecimal livePickUpAmount = incomeUtil.reduceIncome(livePickupBill);
        return AchievementContractForecastQuotaVo.builder()
                .total_amount(Utils.fromFenToYuan(unAuditAmount.add(unPushAmount)))
                .un_audit_amount(Utils.fromFenToYuan(unAuditAmount))
                .un_push_amount(Utils.fromFenToYuan(unPushAmount))
                .live_pickup_amount(Utils.fromFenToYuan(livePickUpAmount))
                .other_pickup_amount(Utils.fromFenToYuan(otherPickUpAmount))
                .brand_amount(Utils.fromFenToYuan(unAuditAmount.add(unPushAmount).subtract(livePickUpAmount).subtract(otherPickUpAmount)))
                .build();
    }

    /**
     * @param operator
     * @param queryAchieveDto
     * @return
     */
    public AchievementContractForecastQuotaVo getUnCheckedBillQuota(Operator operator, QueryAchieveDto queryAchieveDto) {
        List<ContractAdIncome> bills = achievementContractService.getContractForecast(operator, queryAchieveDto);
        if (CollectionUtils.isEmpty(bills)) {
            return AchievementContractForecastQuotaVo.builder().total_amount(BigDecimal.ZERO).un_audit_amount(BigDecimal.ZERO).un_push_amount(BigDecimal.ZERO).build();
        }

        BigDecimal unAuditAmount = achievementHelper.getBusinessReservedUnAuditAmount(bills);
        BigDecimal unPushAmount = achievementHelper.getBusinessReservedUnPushAmount(bills);

        return AchievementContractForecastQuotaVo.builder()
                .total_amount(Utils.fromFenToYuan(unAuditAmount.add(unPushAmount)))
                .un_audit_amount(Utils.fromFenToYuan(unAuditAmount))
                .un_push_amount(Utils.fromFenToYuan(unPushAmount))
                .build();
    }

    /**
     * 预估金额
     *
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementContractForecastQuotaVo getContractForecastQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        QueryAchieveDto queryAchieveDto = queryVo2Dto(queryVo);
        return getContractForecastQuota(operator, queryAchieveDto);
    }

    /**
     * 效果团队复计收入
     *
     * @param operator
     * @param queryVo
     * @return
     */
    public BigDecimal getRepeatData(Operator operator, QueryAchieveBoardVo queryVo) {
        List<AchievementRtbData> rtbRepeatData = rtbService.getRtbRepeatData(operator, queryVo2Dto(queryVo));
        if (CollectionUtils.isEmpty(rtbRepeatData)) {
            return BigDecimal.ZERO;
        }
        return rtbRepeatData.stream().map(AchievementRtbData::getTotalConsume).reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    /**
     * 花火商单复计业绩
     *
     * @param operator
     * @param queryVo
     * @return
     */
    public BigDecimal getPickUpRepeatData(Operator operator, QueryAchieveBoardVo queryVo) {

        BigDecimal pickUpIncome = achievementPickUpService.getPickUpIncome(operator, queryVo2Dto(queryVo));
        return Utils.fromFenToYuan(pickUpIncome);
    }


    public QueryAchieveDto queryVo2Dto(QueryAchieveBoardVo vo) {
        //看板参数转化
        webAchievementHelper.validQueryAchieveVo(vo);

        Timestamp beginTime = vo.getDate_begin() == null ? null : new Timestamp(vo.getDate_begin());
        Timestamp endTime = vo.getDate_end() == null ? null : new Timestamp(vo.getDate_end());
        return QueryAchieveDto.builder()
                .dateBegin(beginTime)
                .dateEnd(endTime)
                .saleGroupIds(vo.getSale_group_ids())
                .saleIds(vo.getSale_ids())
                .build();
    }

    private QueryAchieveDto queryVo2Dto(QueryAchieveContractVo vo) {

        Timestamp beginTime = vo.getDate_begin() == null ? null : new Timestamp(vo.getDate_begin());
        Timestamp endTime = vo.getDate_end() == null ? null : new Timestamp(vo.getDate_end());
        return QueryAchieveDto.builder()
                .dateBegin(beginTime)
                .dateEnd(endTime)
                .customerIds(vo.getCustomer_ids())
                .accountIds(vo.getAccount_ids())
                .agentIds(vo.getAgent_ids())
                .saleGroupIds(vo.getSale_group_ids())
                .businessType(vo.getBusiness_type())
                .saleIds(vo.getSale_ids())
                .build();
    }

    /**
     * 已确认收入导出
     */
    public List<AchieveReportVo> getAchieveCheckedExportByMonth(Operator operator, QueryAchieveBoardVo queryVo) {
        return getAchieveCheckedExportMonth(operator, queryVo2Dto(queryVo));
    }

    public List<AchieveReportVo> getAchieveCheckedExportMonth(Operator operator, QueryAchieveDto queryAchieveDto) {
        //合约广告
        List<BillDetailReport> achievementCheckedReport = reportService.getAchievementCheckedReport(operator, queryAchieveDto);
        //必选 宽表
        List<RtbReportAchievement> rtbData = rtbReportService.getRtbReportData(operator, queryAchieveDto);
        //商业起飞 宽表
        List<RtbReportAchievement> bsiFlyData = rtbReportService.getBsiFlyReportDataFormWideEs(operator, queryAchieveDto);
        //DPA 宽表
        List<RtbReportAchievement> dpaData = rtbReportService.getDpaReportData(operator, queryAchieveDto);
        //ADX 宽表
        List<AdxReportAchievement> adxReportData = rtbReportService.getAdxReportData(operator, queryAchieveDto);

        // 效果实结数据
        List<RtbReportAchievement> rtbCloseData = rtbReportService.getRtbCloseReportData(operator, queryAchieveDto);
        List<RtbReportAchievement> dpaCloseData = rtbReportService.getDpaCloseReportData(operator, queryAchieveDto);
        List<AdxReportAchievement> adxCloseData = rtbReportService.getAdxCloseReportData(operator, queryAchieveDto);

        //内容起飞 宽表
        List<RtbReportAchievement> contentFlyData = rtbReportService.getContentFlyReportData(operator, queryAchieveDto);
        //个人起飞 宽表
        List<RtbReportAchievement> personFlyData = rtbReportService.getPersonFlyReportData(operator, queryAchieveDto);
        // 合伙人-线索通
        List<RtbReportAchievement> cluePassData = rtbReportService.getCluePassReportData(operator, queryAchieveDto);
        //花火已确认收入 宽表
        List<BillDetailReport> pickConfirmData = reportService.getPickUpConfirmReportData(operator, queryAchieveDto);

        List<AchieveReportVo> adx = aggByAccAgentSaleMonthData(adx2AchieveReportVo(adxReportData));
        List<AchieveReportVo> dpa = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(dpaData));
        List<AchieveReportVo> bsiFly = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(bsiFlyData));
        List<AchieveReportVo> rtb = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(rtbData));
        List<AchieveReportVo> contentFly = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(contentFlyData));
        List<AchieveReportVo> personFly = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(personFlyData));
        List<AchieveReportVo> rtbClose = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(rtbCloseData));
        List<AchieveReportVo> adxClose = aggByAccAgentSaleMonthData(adx2AchieveReportVo(adxCloseData));
        List<AchieveReportVo> dpaClose = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(dpaCloseData));
        List<AchieveReportVo> cluePass = aggByAccAgentSaleMonthData(rtb2AchieveReportVo(cluePassData));
        List<AchieveReportVo> pickConfirm = aggByAccAgentSaleMonthData(contract2AchieveReportVo(pickConfirmData, IncomeComposition.PICK_UP_ORDER));
        List<AchieveReportVo> contract = aggByAccAgentSaleMonthData(contract2AchieveReportVo(achievementCheckedReport, IncomeComposition.CONTRACT_AD));
        return Stream.of(contract, adx, dpa, bsiFly, rtb, contentFly, personFly, rtbClose, dpaClose, adxClose, pickConfirm, cluePass).flatMap(Collection::stream).collect(Collectors.toList());
    }

    public List<AchieveReportVo> getCluePassData(Operator operator, QueryAchieveBoardVo queryVo) {
        // 合伙人-线索通
        List<RtbReportAchievement> cluePassData = rtbReportService.getCluePassReportData(operator, queryVo2Dto(queryVo));
        List<AchieveReportVo> cluePass = rtb2AchieveReportVo(cluePassData);
        return cluePass;
    }

    private List<AchieveReportVo> aggByAccAgentSaleMonthData(List<AchieveReportVo> data) {
        List<AchieveReportVo> result = new ArrayList<>();
        data.forEach(item -> {
            item.setDate_time(Utils.getBeginDayOfMonth(Utils.getBeginOfDay(new Timestamp(item.getDate_time().getTime()))));
            item.setDate(CrmUtils.formatDate(new Timestamp(item.getDate_time().getTime()), CrmUtils.YYYYMM));
        });

        Map<String, List<AchieveReportVo>> aggMap = data.stream().collect(Collectors.groupingBy(item -> item.getAccount_id() + "_" + item.getAgent_id() + "_" + item.getDate()
                + "_" + item.getChannel_name() + "_" + item.getStraight_name() + "_" + item.getContract_id() + "_" + item.getOrder_id() + "_" + item.getBill_source()));
        for (Map.Entry<String, List<AchieveReportVo>> entry : aggMap.entrySet()) {
            BigDecimal income = entry.getValue().stream().map(AchieveReportVo::getIncome).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal redPacket = entry.getValue().stream().map(AchieveReportVo::getRed_packet).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal specialRedPacket = entry.getValue().stream().map(AchieveReportVo::getSpecial_red_packet).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal showAmount = entry.getValue().stream().map(AchieveReportVo::getShow_amount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal cash = entry.getValue().stream().map(AchieveReportVo::getCash).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal credit = entry.getValue().stream().map(AchieveReportVo::getCredit).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
            BigDecimal clickAmount = entry.getValue().stream().map(AchieveReportVo::getClick_amount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

            AchieveReportVo achieveReportVo = entry.getValue().get(0);
            achieveReportVo.setIncome(income);
            achieveReportVo.setRed_packet(redPacket);
            achieveReportVo.setSpecial_red_packet(specialRedPacket);
            achieveReportVo.setCash(cash);
            achieveReportVo.setCredit(credit);
            achieveReportVo.setClick_amount(clickAmount);
            achieveReportVo.setShow_amount(showAmount);
            achieveReportVo.setCpc(AdUtils.getCpc(income, clickAmount));
            achieveReportVo.setCtr(AdUtils.getCtr(income, showAmount));
            achieveReportVo.setEcpm(AdUtils.getEcpm(income, showAmount));

            if (IncomeComposition.PICK_UP_ORDER.getDesc().equals(achieveReportVo.getProduct_type())) {
                achieveReportVo.setOrder_id(null);
                achieveReportVo.setOrder_create_time(null);
                achieveReportVo.setOrder_start_time(null);
                achieveReportVo.setOrder_status_desc(null);
                achieveReportVo.setOrder_type_desc(null);
                achieveReportVo.setOrder_amount(null);
                achieveReportVo.setOrder_discounted_amount(null);
                achieveReportVo.setPick_up_order_sub_task_id(null);
                achieveReportVo.setPick_up_order_sub_task_name(null);
                achieveReportVo.setPickup_order_no(null);
                achieveReportVo.setPickup_id(null);
                achieveReportVo.setPickup_order_status_desc(null);
                achieveReportVo.setCooperation_type_desc(null);
                achieveReportVo.setPickup_create_date(null);
                achieveReportVo.setOnline_date(null);
                achieveReportVo.setActual_online_date(null);
                achieveReportVo.setComplete_date(null);
                achieveReportVo.setAvid(null);
                achieveReportVo.setMid(null);
                achieveReportVo.setUp_name(null);
                achieveReportVo.setOut_task_cm_name(null);
                achieveReportVo.setOut_task_no(null);
                achieveReportVo.setTag(null);
                achieveReportVo.setBvid(null);
            }

            result.add(achieveReportVo);
        }
        return result;
    }

    /**
     * 已确认收入导出
     */
    public List<AchieveReportVo> getAchieveCheckedExport(Operator operator, QueryAchieveBoardVo queryVo) {
        return getAchieveCheckedExport(operator, queryVo2Dto(queryVo), null);
    }


    /**
     * 已确认收入导出含有测试数据
     */
    public List<AchieveReportVo> getAchieveCheckedExportWithTestData(Operator operator, QueryAchieveBoardVo queryVo) {
        return getAchieveCheckedExport(operator, queryVo2Dto(queryVo), 1);
    }

    public List<AchieveReportVo> getSellGoodExport(Operator operator, QueryBoardCustomVo queryVo) {
        List<RtbReportAchievement> sellGoodData = rtbReportService.getSellGoodReportData(operator, QueryAchieveDto.builder()
                .dateBegin(new Timestamp(queryVo.getDate_begin()))
                .dateEnd(new Timestamp(queryVo.getDate_end()))
                .saleGroupIds(queryVo.getSale_group_ids())
                .saleIds(queryVo.getSale_ids())
                .bizIndustryCategoryFirstIds(queryVo.getBiz_industry_category_first_ids())
                .bizIndustryCategorySecondIds(queryVo.getBiz_industry_category_second_ids())
                .bizIndustryCategoryFirstIdsOr(queryVo.getBiz_industry_category_first_ids_or())
                .bizIndustryCategorySecondIdsOr(queryVo.getBiz_industry_category_second_ids_or())
                .groupIds(queryVo.getGroup_ids())
                .accountIds(queryVo.getAccount_ids())
                .agentIds(queryVo.getAgent_ids())
                .customerIds(queryVo.getCustomer_ids())
                .build());
        return rtb2AchieveReportVo(sellGoodData);
    }

    public List<AchieveReportVo> getAchieveCheckedExport(Operator operator, QueryAchieveDto queryAchieveDto, Integer haveTestData) {
        //合约广告
        List<BillDetailReport> achievementCheckedReport = new ArrayList<>();
        if (Objects.equals(haveTestData, 1)) {
            achievementCheckedReport = reportService.getAchievementCheckedReportWithTestData(operator, queryAchieveDto);
        } else {
            achievementCheckedReport = reportService.getAchievementCheckedReport(operator, queryAchieveDto);
        }
        //必选 宽表
        List<RtbReportAchievement> rtbData = rtbReportService.getRtbReportData(operator, queryAchieveDto);
        //商业起飞 宽表
        List<RtbReportAchievement> bsiFlyData = rtbReportService.getBsiFlyReportDataFormWideEs(operator, queryAchieveDto);
        //DPA 宽表
        List<RtbReportAchievement> dpaData = rtbReportService.getDpaReportData(operator, queryAchieveDto);
        //ADX 宽表
        List<AdxReportAchievement> adxReportData = rtbReportService.getAdxReportData(operator, queryAchieveDto);
        // 效果实结数据
        List<RtbReportAchievement> rtbCloseData = rtbReportService.getRtbCloseReportData(operator, queryAchieveDto);
        List<RtbReportAchievement> dpaCloseData = rtbReportService.getDpaCloseReportData(operator, queryAchieveDto);
        List<AdxReportAchievement> adxCloseData = rtbReportService.getAdxCloseReportData(operator, queryAchieveDto);
        //内容起飞 宽表
        List<RtbReportAchievement> contentFlyData = rtbReportService.getContentFlyReportData(operator, queryAchieveDto);
        //个人起飞 宽表
        List<RtbReportAchievement> personFlyData = rtbReportService.getPersonFlyReportData(operator, queryAchieveDto);
        // 合伙人-线索通
        List<RtbReportAchievement> cluePassData = rtbReportService.getCluePassReportData(operator, queryAchieveDto);
        //花火已确认收入 宽表
        List<BillDetailReport> pickConfirmData = reportService.getPickUpConfirmReportData(operator, queryAchieveDto);
        List<AchieveReportVo> adx = adx2AchieveReportVo(adxReportData);
        List<AchieveReportVo> dpa = rtb2AchieveReportVo(dpaData);
        List<AchieveReportVo> bsiFly = rtb2AchieveReportVo(bsiFlyData);
        List<AchieveReportVo> rtb = rtb2AchieveReportVo(rtbData);
        List<AchieveReportVo> contentFly = rtb2AchieveReportVo(contentFlyData);
        List<AchieveReportVo> personFly = rtb2AchieveReportVo(personFlyData);
        WebAchieveFlyExportHelper.buildFlyBeforeConsume(personFly);
        List<AchieveReportVo> rtbClose = rtb2AchieveReportVo(rtbCloseData);
        List<AchieveReportVo> adxClose = adx2AchieveReportVo(adxCloseData);
        List<AchieveReportVo> dpaClose = rtb2AchieveReportVo(dpaCloseData);
        List<AchieveReportVo> cluePass = rtb2AchieveReportVo(cluePassData);
        List<AchieveReportVo> pickConfirm = contract2AchieveReportVo(pickConfirmData, IncomeComposition.PICK_UP_ORDER);
        List<AchieveReportVo> contract = contract2AchieveReportVo(achievementCheckedReport, IncomeComposition.CONTRACT_AD);
        List<AchieveReportVo> voList = Stream.of(contract, adx, dpa, bsiFly, rtb, contentFly, personFly, rtbClose, dpaClose, adxClose, pickConfirm, cluePass).flatMap(Collection::stream).collect(Collectors.toList());

        buildParentInfo(voList);
        buildCrmProjectCategory(voList);
        fillCustomerOperateLabelInfo(voList, queryAchieveDto.getDateBegin().getTime(), queryAchieveDto.getDateEnd().getTime());
        log.info("getAchieveCheckedExport voList size: {}", voList.size());
        return voList;
    }

    public void buildCrmProjectCategory(List<AchieveReportVo> achieveReportVoList) {
        List<Integer> crmOrderIds = achieveReportVoList.stream().map(AchieveReportVo::getOrder_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, CrmOrderExtraDto> crmOrderExtraMap = iOrderService.queryOrderExtra(crmOrderIds);
        Map<Integer, Integer> crmOrderIdProjectIdMap = crmOrderExtraMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getProjectItemId(),
                (k1, k2) -> k1
        ));
        List<Integer> crmProjectIds = crmOrderExtraMap.values().stream().map(CrmOrderExtraDto::getProjectItemId).distinct().collect(Collectors.toList());

        Map<Integer, ProjectItemPackageDto> crmProjectMap = iNonStandardPackageService.queryPackageByIdList(crmProjectIds);
        achieveReportVoList.forEach(vo -> {
            Integer crmOrderId = vo.getOrder_id();
            if (crmOrderId != null) {
                Integer crmProjectId = crmOrderIdProjectIdMap.get(crmOrderId);
                if (crmProjectId != null) {
                    ProjectItemPackageDto projectItemPackageDto = crmProjectMap.get(crmProjectId);
                    if (projectItemPackageDto != null) {
                        vo.setCrm_project_first_category(projectItemPackageDto.getProjectFirstLevelCategory());
                        vo.setCrm_project_second_category(projectItemPackageDto.getProjectSecondLevelCategory());
                        vo.setCrm_project_third_category(projectItemPackageDto.getProjectMarketingCategory());
                        vo.setMarketingTeam(projectItemPackageDto.getMarketingTeam());
                    }
                }
            }
        });
    }

    public void buildParentInfo(List<AchieveReportVo> voList) {
        Map<Integer, CorporationGroupDto> groupDtoMap = corporationGroupService.getParentGroupInfoMapBySonIds(voList.stream().map(AchieveReportVo::getGroup_id).filter(Utils::isPositive).distinct().collect(Collectors.toList()));
        voList.forEach(t -> {
            if (Utils.isPositive(t.getGroup_id())) {
                CorporationGroupDto parentDto = groupDtoMap.get(t.getGroup_id());
                if (Objects.nonNull(parentDto)) {
                    t.setParent_group_id(parentDto.getId());
                    t.setParent_group_name(parentDto.getName());
                }
            }
        });
    }

    public List<AchieveReportVo> getAchieveCheckedExportForAgentPortrait(Operator operator, QueryAchieveDto queryAchieveDto) {
        log.info("getAchieveCheckedExportForAgentPortraitParam:{}", GsonUtils.toJson(queryAchieveDto));
        QueryAchieveDto contractQueryDto = QueryAchieveDto.builder().build();
        BeanUtils.copyProperties(queryAchieveDto, contractQueryDto);
        buildClosedTime(contractQueryDto);
        //合约广告
        List<BillDetailReport> achievementCheckedReport = reportService.getAchievementCheckedReport(operator, contractQueryDto);
        //必选 宽表
        List<RtbReportAchievement> rtbData = rtbReportService.getRtbReportData(operator, queryAchieveDto);
        //商业起飞 宽表
        List<RtbReportAchievement> bsiFlyData = rtbReportService.getBsiFlyReportDataFormWideEs(operator, queryAchieveDto);
        //DPA 宽表
        List<RtbReportAchievement> dpaData = rtbReportService.getDpaReportData(operator, queryAchieveDto);
        //ADX 宽表
        List<AdxReportAchievement> adxReportData = rtbReportService.getAdxReportDataFromWideEs(operator, queryAchieveDto);
        //内容起飞 宽表
        List<RtbReportAchievement> contentFlyData = rtbReportService.getContentFlyReportData(operator, queryAchieveDto);
        //个人起飞 宽表
        List<RtbReportAchievement> personFlyData = rtbReportService.getPersonFlyReportData(operator, queryAchieveDto);
        // 实结数据
        List<RtbReportAchievement> rtbCloseData = rtbReportService.getRtbCloseReportData(operator, queryAchieveDto);
        List<RtbReportAchievement> dpaCloseData = rtbReportService.getDpaCloseReportData(operator, queryAchieveDto);
        List<AdxReportAchievement> adxCloseData = rtbReportService.getAdxCloseReportData(operator, queryAchieveDto);
        // 合伙人-线索通
        List<RtbReportAchievement> cluePassData = rtbReportService.getCluePassReportData(operator, queryAchieveDto);
        //花火已确认收入 宽表
        List<BillDetailReport> pickConfirmData = reportService.getPickUpConfirmReportData(operator, queryAchieveDto);
        List<AchieveReportVo> adx = adx2AchieveReportVo(adxReportData);
        List<AchieveReportVo> dpa = rtb2AchieveReportVo(dpaData);
        List<AchieveReportVo> bsiFly = rtb2AchieveReportVo(bsiFlyData);
        List<AchieveReportVo> rtb = rtb2AchieveReportVo(rtbData);
        List<AchieveReportVo> contentFly = rtb2AchieveReportVo(contentFlyData);
        List<AchieveReportVo> personFly = rtb2AchieveReportVo(personFlyData);
        List<AchieveReportVo> rtbClose = rtb2AchieveReportVo(rtbCloseData);
        List<AchieveReportVo> adxClose = adx2AchieveReportVo(adxCloseData);
        List<AchieveReportVo> dpaClose = rtb2AchieveReportVo(dpaCloseData);
        List<AchieveReportVo> cluePass = rtb2AchieveReportVo(cluePassData);
        List<AchieveReportVo> pickConfirm = contract2AchieveReportVo(pickConfirmData, IncomeComposition.PICK_UP_ORDER);
        List<AchieveReportVo> contract = contract2AchieveReportVo(achievementCheckedReport, IncomeComposition.CONTRACT_AD);
        return Stream.of(contract, adx, dpa, bsiFly, rtb, contentFly, personFly, adxClose, dpaClose, rtbClose, pickConfirm, cluePass).flatMap(Collection::stream).collect(Collectors.toList());
    }

    private List<AchieveReportVo> pickup2AchieveReportVo(List<AchievePickUpReportDto> data) {
        if (CollectionUtils.isEmpty(data)) {
            return Collections.emptyList();
        }

        return data.stream().map(item -> AchieveReportVo.builder()
                .date(item.getBillDate())
                .product_type(IncomeComposition.PICK_UP_HUA_HUO.getDesc())
                .income(item.getOrderAmount())
                .straight_name(item.getSaleName())
                .is_checking_desc(CheckingType.ENABLE.getName())
                .account_id(item.getAccountId())
                .account_name(item.getAccountName())
                .account_customer_id(item.getAccountCustomerId())
                .account_customer_name(item.getAccountCustomerName())
                .build()).collect(Collectors.toList());
    }

    //转换
    private List<AchieveReportVo> adx2AchieveReportVo(List<AdxReportAchievement> adxData) {

        if (CollectionUtils.isEmpty(adxData)) {
            return Collections.emptyList();
        }
        return adxData.stream().map(item -> AchieveReportVo.builder()
                .date(item.getDate())
                .date_time(item.getDate_time())
                .product_type(item.getProduct_type())
                .income(item.getTotal_consume())
                .cash(item.getCash())
                .red_packet(item.getRed_packet())
                .group_name(item.getGroup_name())
                .group_id(item.getGroup_id())
                .product_line_name(item.getProduct_line_name())
                .product_Id(item.getProduct_id())
                .product_name(item.getProduct_name())
                .account_id(item.getAccount_id())
                .account_name(item.getAccount_name())
                .account_customer_id(item.getAccount_customer_id())
                .account_customer_name(item.getAccount_customer_name())
                .account_department_name(item.getAccount_department_name())
                .first_category_name(item.getCategory_first_name())
                .second_category_name(item.getCategory_second_name())
                .biz_industry_category_first_name(item.getBiz_industry_category_first_name())
                .biz_industry_category_second_name(item.getBiz_industry_category_second_name())
                .straight_name(item.getStraight_name())
                .channel_name(item.getChannel_name())
                .straight_name_group(item.getStraight_name_group())
                .straight_name_second_group(item.getStraight_name_second_group())
                .channel_name_group(item.getChannel_name_group())
                .channel_name_second_group(item.getChannel_name_second_group())
                .operate_name(item.getOperate_name())
                .show_amount(item.getShow_amount())
                .click_amount(item.getClick_amount())
                .ctr(item.getCtr())
                .cpc(item.getCpc())
                .ecpm(item.getEcpm())
                .agent_id(item.getAgent_account_id())
                .agent_name(item.getAgent_account_name())
                .agent_customer_id(item.getAgent_customer_id())
                .agent_customer_name(item.getAgent_customer_name())
                .agent_account_group_id(item.getAgent_account_group_id())
                .agent_account_group(item.getAgent_account_group_name())
                .account_united_first_industry_name(item.getAccountUnitedFirstIndustryName())
                .account_united_second_industry_name(item.getAccountUnitedSecondIndustryName())
                .account_united_third_industry_name(item.getAccountUnitedThirdIndustryName())
                .customer_united_first_industry_name(item.getCustomerUnitedFirstIndustryName())
                .customer_united_second_industry_name(item.getCustomerUnitedSecondIndustryName())
                .customer_united_third_industry_name(item.getCustomerUnitedThirdIndustryName())
                .build()).collect(Collectors.toList());
    }


    //转换
    public List<AchieveReportVo> rtb2AchieveReportVo(List<RtbReportAchievement> rtbData) {

        if (CollectionUtils.isEmpty(rtbData)) {
            return Collections.emptyList();
        }

        List<Integer> agentIds = rtbData.stream().map(RtbReportAchievement::getAgent_id).collect(Collectors.toList());
        Map<Integer, AgentDto> agentMapInIds = iagentService.getAgentMapInIds(agentIds);

        List<Integer> sysAgentIds = agentMapInIds.values().stream().map(AgentDto::getSysAgentId).collect(Collectors.toList());
        Map<Integer, CustomerPo> sysAgentIdToCustomerMap = accAccountRepo.accToCustomerMap(sysAgentIds);

        return rtbData.stream().map(item ->
                {
                    Integer sysAgentId = null == agentMapInIds.get(item.getAgent_id()) ? null : agentMapInIds.get(item.getAgent_id()).getSysAgentId();
                    return AchieveReportVo.builder()
                            .date(item.getDate())
                            .date_time(item.getDate_time())
                            .product_type(item.getProduct_type())
                            .income(item.getTotal_consume())
                            .cash(item.getCash())
                            .credit(item.getCredit())
                            .fly_support_consume(item.getFly_support_amount())
                            .red_packet(item.getRed_packet())
                            .special_red_packet(item.getSpecial_red_packet())
                            .group_name(item.getGroup_name())
                            .group_id(item.getGroup_id())
                            .product_line_name(item.getProduct_line_name())
                            .product_Id(item.getProduct_id())
                            .product_name(item.getProduct_name())
                            .account_id(item.getAccount_id())
                            .account_name(item.getAccount_name())
                            .account_customer_id(item.getAccount_customer_id())
                            .account_customer_name(item.getAccount_customer_name())
                            .account_department_name(item.getAccount_department_name())
                            .first_category_name(item.getCategory_first_name())
                            .second_category_name(item.getCategory_second_name())
                            .biz_industry_category_first_name(item.getBiz_industry_category_first_name())
                            .biz_industry_category_second_name(item.getBiz_industry_category_second_name())
                            .agent_id(sysAgentId)
                            .agent_name(null == agentMapInIds.get(item.getAgent_id()) ? null : agentMapInIds.get(item.getAgent_id()).getName())
                            .agent_customer_id(sysAgentIdToCustomerMap.getOrDefault(sysAgentId, CustomerPo.builder().build()).getId())
                            .agent_customer_name(sysAgentIdToCustomerMap.getOrDefault(sysAgentId, CustomerPo.builder().build()).getUsername())
                            .agent_customer_category(CustomerCategoryType.getByCode(sysAgentIdToCustomerMap.getOrDefault(sysAgentId, CustomerPo.builder().customerCategory(0).build()).getCustomerCategory()).getDesc())
                            .straight_name(item.getStraight_name())
                            .channel_name(item.getChannel_name())
                            .operate_name(item.getOperate_name())
                            .straight_name_group(item.getStraight_name_group())
                            .straight_name_second_group(item.getStraight_name_second_group())
                            .channel_name_group(item.getChannel_name_group())
                            .channel_name_second_group(item.getChannel_name_second_group())
                            .show_amount(item.getShow_amount())
                            .click_amount(item.getClick_amount())
                            .ctr(item.getCtr())
                            .cpc(item.getCpc())
                            .ecpm(item.getEcpm())
                            .clue_pass_task_id(item.getClue_pass_task_id())
                            .clue_pass_task_start_time(item.getClue_pass_task_start_time())
                            .clue_pass_task_end_time(item.getClue_pass_task_end_time())
                            .is_support_pick_up(item.getIs_support_pick_up())
                            .agent_account_group_id(item.getAgent_account_group_id())
                            .agent_account_group(item.getAgent_account_group_name())
                            .account_united_first_industry_name(item.getAccountUnitedFirstIndustryName())
                            .account_united_second_industry_name(item.getAccountUnitedSecondIndustryName())
                            .account_united_third_industry_name(item.getAccountUnitedThirdIndustryName())
                            .customer_united_first_industry_name(item.getCustomerUnitedFirstIndustryName())
                            .customer_united_second_industry_name(item.getCustomerUnitedSecondIndustryName())
                            .customer_united_third_industry_name(item.getCustomerUnitedThirdIndustryName())
                            .build();
                }
        ).collect(Collectors.toList());
    }


    //转换
    private List<AchieveReportVo> contract2AchieveReportVo(List<BillDetailReport> contractData, IncomeComposition type) {

        if (CollectionUtils.isEmpty(contractData)) {
            return Collections.emptyList();
        }
        return contractData.stream().map(item -> AchieveReportVo.builder()
                .pickup_bill_id(item.getBillId())
                .date(CrmUtils.formatDate(item.getBillDate(), CrmUtils.YYYYMMDDHHMMSS))
                .date_time(item.getBillDate())
                .product_type(type.getDesc())
                .income(Utils.fromFenToYuan(item.getDailyPackageAmount()))
                .order_id(item.getOrderId())
                .order_name(item.getOrderName())
                .order_create_time(CrmUtils.formatDate(item.getOrderCreateTime(), CrmUtils.YYYYMMDD))
                .order_start_time(CrmUtils.formatDate(item.getOrderStartTime()))
                .order_end_time(CrmUtils.formatDate(item.getOrderEndTime()))
                .order_type_desc(item.getOrderTypeDesc())
                .order_type(item.getOrderType())
                .order_status_desc(item.getOrderStatusDesc())
                .order_resource_type_desc(item.getOrderResourceTypeDesc())
                .order_amount(Utils.fromFenToYuan(item.getOrderAmount()))
                .order_discounted_amount(Utils.fromFenToYuan(OrderUtils.getDiscountedAmount(item.getOrderAmount(), item.getDiscount())))
                .first_category_product(item.getFirstCategoryProduct())
                .first_category_product_id(item.getFirstCategoryProductId())
                .second_category_product_id(item.getSecondCategoryProductId())
                .second_category_product(item.getSecondCategoryProduct())
                .project_item_name(item.getProjectItemName())
                .achieve_belong_to(item.getAchieveBelongTo())
                .main_category_str(item.getMainCategoryStr())
                .project_department_name(item.getProjectDepartmentName())
                .department_name(item.getDepartmentName())
                .second_department_name(item.getSecondDepartmentName())
                .contract_id(item.getContractId())
                .contract_number(item.getContractNumber())
                .contract_name(item.getContractName())
                .contract_create_time(CrmUtils.formatDate(item.getContractCreateTime(), CrmUtils.YYYYMMDDHHMMSS))
                .begin_time(CrmUtils.formatDate(item.getBeginTime(), CrmUtils.YYYYMMDDHHMMSS))
                .end_time(CrmUtils.formatDate(item.getEndTime(), CrmUtils.YYYYMMDDHHMMSS))
                .contract_type_desc(item.getContractTypeDesc())
                .contract_status_desc(item.getContractStatusDesc())
                .contract_amount(Utils.fromFenToYuan(item.getContractAmount()))
                .contract_package_amount(Utils.fromFenToYuan(Optional.ofNullable(item.getPackageAmount()).orElse(new BigDecimal(0))))
                .project_name(item.getProjectName())
                .archive_status_desc(item.getArchiveStatusDesc())
                .group_id(item.getGroupId())
                .group_name(item.getGroupName())  //需确认
                .product_line_name(item.getProductLineName())//需确认
                .product_name(item.getProductName()) //需确认
                .product_Id(item.getProductId())
                .account_id(item.getAccountId())
                .account_name(item.getCustomerName())
                .account_customer_id(item.getAccountCustomerId()) //新
                .account_customer_name(item.getAccountCustomerName())//新
                .account_department_name(item.getAccountDepartmentName())
                .first_category_name(item.getFirstCategoryName())
                .second_category_name(item.getSecondCategoryName())
                .biz_industry_category_first_name(item.getBizIndustryCategoryFirstName())
                .biz_industry_category_second_name(item.getBizIndustryCategorySecondName())
                .agent_id(item.getAgentAccountId())
                .agent_name(item.getAgentName())
                .agent_customer_id(item.getAgentCustomerId())
                .agent_customer_name(item.getAgentCustomerName())
                .agent_customer_category(item.getAgentCustomerCategory())
                .pick_up_order_sub_task_id(item.getPickUpOrderSubTaskId())
                .pick_up_order_sub_task_name(item.getPickUpOrderSubTaskName())
                .pickup_order_no(item.getPickupOrderNo() == null ? null : String.valueOf(item.getPickupOrderNo()))
                .pickup_id(item.getPickupId())
                .pickup_order_status_desc(item.getPickupOrderStatusDesc())
                .cooperation_type_desc(item.getCooperationTypeDesc())
                .pickup_create_date(item.getPickupCreateDate())
                .online_date(item.getOnlineDate())
                .actual_online_date(item.getActualOnlineDate())
                .new_customer_complete_time(item.getNewCustomerCompleteTime())
                .complete_date(item.getCompleteDate())
                .refund_date(item.getRefundDate())
                .avid(item.getAvid())
                .mid(item.getMid())
                .up_name(item.getUpName())
                .straight_name(item.getDirectSalesName())
                .channel_name(item.getChannelSalesName())
                .creator(item.getCreator())
                .execute_name(item.getExecuteName())
                .is_checking_desc(item.getIsCheckingDesc())
                .closing_date(CrmUtils.formatDate(item.getClosingDate(), CrmUtils.YYYYMMDDHHMMSS))
                .bill_source(-1 == item.getBillSource() ? "" : BillSourceType.getByCode(item.getBillSource()).getName())
                .out_task_no(item.getOut_task_no())
                .out_task_cm(item.getOut_task_cm())
                .out_task_cm_name(item.getOut_task_cm_name())
                .tag(item.getTag())
                .bvid(item.getBvid())
                .straight_name_group(item.getStraightNameGroup())
                .straight_name_second_group(item.getStraightNameSecondGroup())
                .channel_name_group(item.getChannelNameGroup())
                .channel_name_second_group(item.getChannelNameSecondGroup())
                .opt_agent_customer_id(item.getOptAgentId())
                .opt_agent_customer_name(item.getOptAgentName())
                .agent_account_group_id(item.getAgentGroupId())
                .agent_account_group(item.getAgentGroupName())
                .account_united_first_industry_name(item.getAccountUnitedFirstIndustryName())
                .account_united_second_industry_name(item.getAccountUnitedSecondIndustryName())
                .account_united_third_industry_name(item.getAccountUnitedThirdIndustryName())
                .customer_united_first_industry_name(item.getCustomerUnitedFirstIndustryName())
                .customer_united_second_industry_name(item.getCustomerUnitedSecondIndustryName())
                .customer_united_third_industry_name(item.getCustomerUnitedThirdIndustryName())
                .build()).collect(Collectors.toList());
    }

    public AchievementConsumeQuotaVo pickUpUnConfirmEdQuota(Operator operator, QueryAchieveBoardVo queryVo) {

        QueryAchieveDto queryAchieveDto = QueryAchieveDto.builder()
                .dateBegin(new Timestamp(queryVo.getDate_begin()))
                .dateEnd(new Timestamp(queryVo.getDate_end()))
                .pickAchieveTypeEnum(PickupAchieveTypeEnum.UNDONE)
                .saleIds(queryVo.getSale_ids())
                .saleGroupIds(queryVo.getSale_group_ids())
                .build();
        return pickUpUnConfirmEdQuotaV2(operator, queryAchieveDto);
    }

    public AchievementConsumeQuotaVo pickUpUnConfirmEdQuotaBiz(Operator operator, QueryAchieveBoardVo queryVo) {

        QueryAchieveDto queryAchieveDto = QueryAchieveDto.builder()
                .dateBegin(new Timestamp(queryVo.getDate_begin()))
                .dateEnd(new Timestamp(queryVo.getDate_end()))
                .saleIds(queryVo.getSale_ids())
                .saleGroupIds(queryVo.getSale_group_ids())
                .build();
        return pickUpUnConfirmEdQuotaBizV2(operator, queryAchieveDto);
    }

    public AchievementConsumeQuotaVo pickUpUnConfirmEdQuota(Operator operator, QueryAchieveDto queryAchieveDto) {

        List<AchievementDto> unDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNCONFIRMED);
        BigDecimal unDoneIncome = unDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.PAID_UNAUDIT);
        List<AchievementDto> paidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNAUDIT);
        BigDecimal paidIncome = paidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.UNPAID);
        List<AchievementDto> unPaidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNPAID);
        BigDecimal unPaidIncome = unPaidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_UNDONE);
        BigDecimal boostingUnDoneIncome = boostingUnDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnAuditRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_UNAUDIT);
        BigDecimal boostingUnAuditIncome = boostingUnAuditRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        BigDecimal incomeTotal = unDoneIncome.add(paidIncome).add(unPaidIncome).add(boostingUnDoneIncome).add(boostingUnAuditIncome);

        return AchievementConsumeQuotaVo.builder().total_consume(incomeTotal)
                .pick_ip_unpaid(unPaidIncome)
                .pick_up_done_unaudit(paidIncome.add(boostingUnAuditIncome))
                .pick_up_undone(unDoneIncome.add(boostingUnDoneIncome))
                .build();
    }

    public AchievementConsumeQuotaVo pickUpUnConfirmEdQuotaV2(Operator operator, QueryAchieveDto queryAchieveDto) {

        List<AchievementDto> unDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNCONFIRMED);
        BigDecimal unDoneIncome = unDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

//        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.PAID_UNAUDIT);
//        List<AchievementDto> paidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNAUDIT);
//        BigDecimal paidIncome = paidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.UNPAID);
        List<AchievementDto> unPaidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNPAID);
        BigDecimal unPaidIncome = unPaidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_UNDONE);
        BigDecimal boostingUnDoneIncome = boostingUnDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnAuditRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_UNAUDIT);
        BigDecimal boostingUnAuditIncome = boostingUnAuditRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        BigDecimal incomeTotal = unDoneIncome.add(boostingUnDoneIncome).add(boostingUnAuditIncome).add(unPaidIncome);

        return AchievementConsumeQuotaVo.builder().total_consume(incomeTotal)
                .pick_ip_unpaid(unPaidIncome)
                .pick_up_undone(unDoneIncome.add(boostingUnDoneIncome))
                .build();
    }

    public AchievementConsumeQuotaVo pickUpUnConfirmEdQuotaBizV2(Operator operator, QueryAchieveDto queryAchieveDto) {

        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.PICK_UP_PAYED_UNDONE);
        List<AchievementDto> unDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_PAYED_UNDONE);
        BigDecimal unDoneIncome = unDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.PICK_UP_UNPAY_UNORDER);
        List<AchievementDto> paidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_UNPAY_UNORDER);
        BigDecimal paidIncome = paidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        queryAchieveDto.setPickAchieveTypeEnum(PickupAchieveTypeEnum.PICK_UP_AUDIT_REJECT);
        List<AchievementDto> unPaidRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_AUDIT_REJECT);
        BigDecimal unPaidIncome = unPaidRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnDoneRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_UNDONE);
        BigDecimal boostingUnDoneIncome = boostingUnDoneRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingUnAuditRes = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_UNAUDIT);
        BigDecimal boostingUnAuditIncome = boostingUnAuditRes.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        BigDecimal incomeTotal = unDoneIncome.add(paidIncome).add(boostingUnDoneIncome).add(boostingUnAuditIncome).add(unPaidIncome);

        return AchievementConsumeQuotaVo.builder().total_consume(incomeTotal)
                .pick_ip_unpaid(unPaidIncome)
                .pick_up_done_unaudit(paidIncome.add(boostingUnAuditIncome))
                .pick_up_undone(unDoneIncome.add(boostingUnDoneIncome))
                .build();
    }

    /**
     * 有订单完成时间的
     *
     * @param operator
     * @param queryVo
     * @return
     */
    public AchievementConsumeQuotaVo pickUpConfirmEdQuota(Operator operator, QueryAchieveBoardVo queryVo) {
        QueryAchieveDto queryAchieveDto = QueryAchieveDto.builder()
                .dateBegin(new Timestamp(queryVo.getDate_begin()))
                .dateEnd(new Timestamp(queryVo.getDate_end()))
                .pickAchieveTypeEnum(PickupAchieveTypeEnum.CONFIRMED)
                .saleIds(queryVo.getSale_ids())
                .saleGroupIds(queryVo.getSale_group_ids())
                .build();
        List<AchievementDto> pickUpBillDetailByWideEs = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.PICK_UP_CONFIRMED);
        BigDecimal totalConsume = pickUpBillDetailByWideEs.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        List<AchievementDto> boostingPickUpBillDetailByWideEs = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(operator, queryAchieveDto, IncomeComposition.BOOSTING_CONFIRMED);
        BigDecimal boostingTotalConsume = boostingPickUpBillDetailByWideEs.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);

        return AchievementConsumeQuotaVo.builder().total_consume(totalConsume.add(boostingTotalConsume)).build();
    }

    public AgentPortraitTableCardConfirmVo buildAgentPortraitData(Operator operator, QueryAchieveDto queryDto) {
        QueryAchieveDto rtbQueryDto = QueryAchieveDto.builder().build();
        QueryAchieveDto pickUpQueryDto = QueryAchieveDto.builder().build();
        BeanUtils.copyProperties(queryDto, rtbQueryDto);
        rtbQueryDto.setOrAgentIds(queryDto.getRtbNoBelongAgentIds());
        BeanUtils.copyProperties(queryDto, pickUpQueryDto);
        pickUpQueryDto.setOrAgentIds(queryDto.getPickUpNoBelongAgentIds());
        buildClosedTime(queryDto);

        AchievementContractCheckQuotaDto quotaDto = achievementContractService.getCheckedBillQuotaDto(operator, queryDto);
        AchievementContractCheckQuotaDto businessPostPay = achievementContractService.getCheckedBrandBillQuotaDtoFromEs(operator, queryDto, IncomeComposition.BUSINESS_FLY_POST_PAY);
        if (queryDto.getIsClosed() != null && queryDto.getIsClosed().equals(ClosedType.IS_CLOSED.getCode())) {
            quotaDto.setUnClosedAmount(new BigDecimal(0));
            businessPostPay.setUnClosedAmount(new BigDecimal(0));
        }

        //效果数据、商单构建非业绩归属 超管角色 根据代理商条件查询
        List<AchievementRtbData> adx = rtbService.getAdxDaySourceData(operator, rtbQueryDto);
        List<AchievementRtbData> dpa = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.DPA, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> contentFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CONTENT_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> personFly = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.PERSON_FLY, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> cpc = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CPC, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> cpm = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.CPM, achieveFromWalletService::getWalletAggByDayByWideES);
        List<AchievementRtbData> businessAdvance = rtbService.getConsumeDataByFunctionV2(operator, rtbQueryDto, IncomeComposition.BUSINESS_FLY_ADVANCE_PAY, achieveFromWalletService::getWalletAggByDayByWideES);

        List<AchievementRtbData> pickUpOrder = rtbService.getConsumeDataByFunctionV2(operator, pickUpQueryDto, IncomeComposition.PICKUP_ORDER, achieveFromBillService::getBillAggByDayByWideES);

        BigDecimal cpcAmount = amountSum(cpc);
        BigDecimal adxAmount = amountSum(adx);
        BigDecimal dpaAmount = amountSum(dpa);
        BigDecimal cpmAmount = amountSum(cpm);
        BigDecimal contentFlyAmount = amountSum(contentFly);
        BigDecimal personFlyAmount = amountSum(personFly);
        BigDecimal pickUpOrderAmount = amountSum(pickUpOrder);
        BigDecimal businessAdvanceAmount = amountSum(businessAdvance);


        return AgentPortraitTableCardConfirmVo.builder()
                .contract_closed(Utils.fromFenToYuan(quotaDto.getClosedAmount()))
                .contract_unclosed(Utils.fromFenToYuan(quotaDto.getUnClosedAmount()))
                .adx(adxAmount)
                .biz_fly(businessPostPay.getClosedAmount().add(businessPostPay.getUnClosedAmount()).add(businessAdvanceAmount))
                .content_fly(contentFlyAmount)
                .cpc_cpm(cpcAmount.add(cpmAmount))
                .dpa(dpaAmount)
                .person_fly(personFlyAmount)
                .pick_order(pickUpOrderAmount)
                .total(Utils.fromFenToYuan(quotaDto.getClosedAmount()).add(Utils.fromFenToYuan(quotaDto.getUnClosedAmount()))
                        .add(adxAmount)
                        .add(businessPostPay.getClosedAmount().add(businessPostPay.getUnClosedAmount()))
                        .add(businessAdvanceAmount)
                        .add(contentFlyAmount)
                        .add(cpcAmount.add(cpmAmount))
                        .add(dpaAmount)
                        .add(personFlyAmount)
                        .add(pickUpOrderAmount)
                )
                .build();
    }

    public AchievementContractOrderedUnRecordQuotaVo getUnRecordContractAmount(Operator operator, QueryAchieveContractVo queryVo) {
        AchievementUnRecordContractQuotaDto unRecordContractQuota = achievementContractService.getUnRecordContractQuota(operator, queryVo2Dto(queryVo));
        return AchievementContractOrderedUnRecordQuotaVo.builder()
                .order_un_record_amount(Utils.fromFenToYuan(unRecordContractQuota.getOrderUnRecordAmount()))
                .pick_up_un_record_amount(Utils.fromFenToYuan(unRecordContractQuota.getPickUpUnRecordAmount()))
                .brand_un_record_amount(Utils.fromFenToYuan(unRecordContractQuota.getBrandUnRecordAmount()))
                .build();
    }

    public void fillCustomerOperateLabelInfo(List<AchieveReportVo> vos, long dateBegin, long dateEnd) {

        Map<Integer, List<EasyCustomerOperateLabelDto>> labelMap = buildLabelMap(vos, dateBegin, dateEnd);
        if (MapUtils.isEmpty(labelMap)) {
            return;
        }
        for (AchieveReportVo vo : vos) {
            List<EasyCustomerOperateLabelDto> labelDtos = labelMap.get(vo.getAccount_customer_id());
            if (CollectionUtils.isEmpty(labelDtos)) {
                continue;
            }
            matchCustomerOperateLabel(labelDtos, vo);
        }
    }

    private Map<Integer, List<EasyCustomerOperateLabelDto>> buildLabelMap(List<AchieveReportVo> vos, long dateBegin, long dateEnd) {
        if (CollectionUtils.isEmpty(vos)) {
            return Collections.emptyMap();
        }
        if (dateBegin == 0 || dateEnd == 0) {
            return Collections.emptyMap();
        }

        QueryCustomerOperateLabelParam param = buildCustomerOperateLabelParam(dateBegin, dateEnd);
        List<EasyCustomerOperateLabelDto> dtos = customerOperateLabelRepository.batchEasyDto(param);
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }

        log.info("fillCustomerOperateLabelInfo-dtos size = {}", dtos.size());
        return dtos.stream().collect(Collectors.groupingBy(EasyCustomerOperateLabelDto::getCustomerId));
    }

    private void matchCustomerOperateLabel(List<EasyCustomerOperateLabelDto> labelDtos, AchieveReportVo vo) {
        long dateTime = null == vo.getDate_time() ? 0 : vo.getDate_time().getTime();

        boolean directSignEnable = false;
        boolean serviceProvider = false;
        for (EasyCustomerOperateLabelDto dto : labelDtos) {
            if (!(dto.getServiceBeginTime() <= dateTime && dto.getServiceEndTime() >= dateTime)) {
                continue;
            }
            if (dto.getType() == CustomerOperateLabelTypeEnum.DIRECT_SIGN.getCode()) {
                directSignEnable = true;
            } else if (getCustomerOperateLabelIncomeComposition().contains(vo.getProduct_type())
                    && Objects.equals(dto.getServiceProviderId(), vo.getAgent_customer_id())) {
                serviceProvider = true;
            }
            if (directSignEnable && serviceProvider) {
                break;
            }
        }
        if (directSignEnable && serviceProvider) {
            vo.setCustomer_operate_label(CustomerOperateLabelTypeEnum.DIRECT_SIGN.getDesc() + "," + CustomerOperateLabelTypeEnum.SERVICE_PROVIDER.getDesc());
            return;
        }

        if (directSignEnable) {
            vo.setCustomer_operate_label(CustomerOperateLabelTypeEnum.DIRECT_SIGN.getDesc());
            return;
        }

        if (serviceProvider) {
            vo.setCustomer_operate_label(CustomerOperateLabelTypeEnum.SERVICE_PROVIDER.getDesc());
        }
    }

    private QueryCustomerOperateLabelParam buildCustomerOperateLabelParam(long dateBegin, long dateEnd) {
        QueryCustomerOperateLabelParam param = new QueryCustomerOperateLabelParam();
        param.setStatus(Lists.newArrayList(CustomerOperateLabelStatusEnum.getAchievementStatus()));
        param.setGeServiceEndTimeOr(DateUtils.convert(dateBegin));
        param.setLeServiceBeginTimeOr(DateUtils.convert(dateEnd));
        return param;
    }

    private List<String> getCustomerOperateLabelIncomeComposition() {
        return Lists.newArrayList(IncomeComposition.ADX.getDesc(), IncomeComposition.DPA.getDesc(), IncomeComposition.RTB.getDesc(),
                IncomeComposition.BUSINESS_FLY.getDesc(), IncomeComposition.CONTENT_FLY.getDesc(), IncomeComposition.PERSON_FLY.getDesc());
    }
}
