package com.bilibili.crm.platform.portal.openapi.cost_management.vo;

import io.swagger.v3.oas.annotations.media.Schema;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2024/8/8 16:05.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BTPCrmProjectResultVO implements Serializable {

    private static final long serialVersionUID = 2449127764387555238L;

    private Integer crmProjectId;
    private String crmProjectName;

    @Schema(description = "CRM项目大类(一级分类)")
    private String projectFirstLevelCategory;

    @Schema(description = "CRM项目类型(二级分类")
    private String projectSecondLevelCategory;

    @Schema(description = "CRM项目的营销分类(三级分类)")
    private String projectMarketingCategory;

    @Schema(description = "项目四级结构-项目属性名称")
    private String projectPropertyName;
    @Schema(description = "项目四级结构-项目类型名称")
    private String projectTypeName;
    @Schema(description = "项目四级结构-一级类目名称")
    private String projectFirstCategoryName;
    @Schema(description = "项目四级结构-二级类目名称")
    private String projectSecondCategoryName;

    /**
     * 招商目标（分）
     */
    private Long investmentTargetMoney;
    /**
     * 创建人域账号 比如xumeng03
     */
    private String creator;
}

