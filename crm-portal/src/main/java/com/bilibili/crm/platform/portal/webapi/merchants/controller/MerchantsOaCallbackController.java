package com.bilibili.crm.platform.portal.webapi.merchants.controller;

import com.alibaba.fastjson.JSON;
import com.bilibili.crm.biz.merchants.service.MerchantsOaService;
import com.bilibili.crm.biz.oa.bo.OaCommonCallbackBo;
import com.bilibili.crm.platform.api.oa.dto.OaCommonResponse;
import com.bilibili.crm.platform.api.oa.dto.OaNewCommonResponse;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2024/11/10
 */
@Slf4j
@RestController
@RequestMapping("")
public class MerchantsOaCallbackController {

    @Resource
    private MerchantsOaService merchantsOaService;

    @SuppressWarnings("rawtypes")
    @RequestMapping(value = "/oa/merchants/callback", method = RequestMethod.POST)
    public OaNewCommonResponse oaCallbackMerchants(@RequestBody OaCommonCallbackBo commonCallbackBo) {
        if (commonCallbackBo == null || commonCallbackBo.getArgs() == null || commonCallbackBo.getCurrentTask() == null ||
                commonCallbackBo.getArgs().getActionInfo() == null || commonCallbackBo.getArgs().getTaskInfo() == null) {
            log.warn("oaCallbackMerchants invalid-msg");
            return OaNewCommonResponse.builder().code(OaCommonResponse.ERROR_CODE).message(StringUtils.EMPTY).build();
        }
        OaNewCommonResponse result = merchantsOaService.handelOaCallback(commonCallbackBo);
        log.info("oaCallbackMerchants result = {}", JSON.toJSONString(result));
        return result;
    }

}
