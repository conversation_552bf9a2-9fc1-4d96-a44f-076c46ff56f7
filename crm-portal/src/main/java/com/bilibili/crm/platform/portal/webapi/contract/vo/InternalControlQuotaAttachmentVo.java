package com.bilibili.crm.platform.portal.webapi.contract.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

/**
 * <AUTHOR>
 * @time 2018/3/25 23:21
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class InternalControlQuotaAttachmentVo {

    @ApiModelProperty(notes="图片URL")
    @NotBlank(message="URL不可为空")
    private String url;

    @ApiModelProperty(notes="附件名称")
    @NotBlank(message="附件名称不可为空")
    private String name;

    @ApiModelProperty(notes="hash值")
    @NotBlank(message="hash值不可为空")
    private String hash;

    @ApiModelProperty(notes="token")
    private String token;
}
