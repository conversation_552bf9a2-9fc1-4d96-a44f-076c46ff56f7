package com.bilibili.crm.platform.portal.webapi.achievement.helper.export;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.ICrmDepartmentService;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.dto.BoardCustomCardDto;
import com.bilibili.crm.platform.api.dto.BoardCustomFieldDto;
import com.bilibili.crm.platform.api.enums.CheckingStateEnum;
import com.bilibili.crm.platform.api.enums.DashBoardExportEnum;
import com.bilibili.crm.platform.api.enums.ProductTypeExportEnum;
import com.bilibili.crm.platform.api.enums.SignTypeEnum;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.non.standard.dto.NonStandardProductCategoryDto;
import com.bilibili.crm.platform.api.non.standard.dto.ProjectItemPackageDto;
import com.bilibili.crm.platform.api.non.standard.service.INonStandardPackageService;
import com.bilibili.crm.platform.api.non.standard.service.INonStandardProductCategoryService;
import com.bilibili.crm.platform.api.order.dto.CrmOrderExtraDto;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.biz.industry.enums.IndustryTypeEnum;
import com.bilibili.crm.platform.biz.industry.service.AggIndustryService;
import com.bilibili.crm.platform.biz.po.AccCompanyGroupPo;
import com.bilibili.crm.platform.biz.po.AccProductPo;
import com.bilibili.crm.platform.biz.repo.AccCompanyGroupRepo;
import com.bilibili.crm.platform.biz.repo.AccProductRepo;
import com.bilibili.crm.platform.biz.service.BizIndustryRepo;
import com.bilibili.crm.platform.biz.service.achievement.config.beta.DepAchieveConfigBeta;
import com.bilibili.crm.platform.biz.service.settle_account.common.CheckingType;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.customer.CustomerCategoryType;
import com.bilibili.crm.platform.common.income.IncomeProductEnum;
import com.bilibili.crm.platform.portal.webapi.achievement.vo.*;
import com.bilibili.crm.platform.portal.webapi.pickup.vo.AchievePickupExportVo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static com.bilibili.crm.platform.biz.service.achievement.config.beta.DepAchieveConfigBeta.*;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-06-23 20:23:51
 * @description:
 **/

@Slf4j
@Component
public class WebDashboardExportHelper {

    private static final String DIRECT_SIGN = "直客";

    private static final String SERVICE_PROVIDER = "服务商";

    @Resource
    private ICustomerQueryService iCustomerQueryService;

    @Resource
    private AccProductRepo accProductRepo;

    @Resource
    private BizIndustryRepo bizIndustryRepo;

    @Autowired
    private INonStandardProductCategoryService nonStandardProductCategoryService;

    @Resource
    private IFastQueryAccountService fastQueryAccountService;

    @Autowired
    private AccCompanyGroupRepo accCompanyGroupRepo;

    @Autowired
    private ICrmDepartmentService crmDepartmentService;

    @Autowired
    private IOrderService iOrderService;

    @Autowired
    private INonStandardPackageService iNonStandardPackageService;

    @Resource
    private AggIndustryService aggIndustryService;

    public List<BoardCustomExportVo> achieveToExport(List<AchieveReportVo> achieveReportVos) {
        log.info("achieveToExport achieveReportVo size: {}", achieveReportVos.size());

        if (CollectionUtils.isEmpty(achieveReportVos)) {
            return Lists.newArrayList();
        }

        List<BoardCustomExportVo> result = new ArrayList<>();
        for (AchieveReportVo achieveReportVo : achieveReportVos) {
            BoardCustomExportVo customExportVo = new BoardCustomExportVo();
            BeanUtils.copyProperties(achieveReportVo, customExportVo);
            customExportVo.setTask_id(achieveReportVo.getPick_up_order_sub_task_id());
            customExportVo.setTask_name(achieveReportVo.getPick_up_order_sub_task_name());
            customExportVo.setCooperation_type_desc(achieveReportVo.getCooperation_type_desc());
            customExportVo.setOrder_create_time(achieveReportVo.getOrder_create_time());
            customExportVo.setOrder_start_time(achieveReportVo.getOrder_start_time());
            customExportVo.setOrder_end_time(achieveReportVo.getOrder_end_time());
            customExportVo.setContract_create_time(achieveReportVo.getContract_create_time());
            customExportVo.setContract_begin_time(achieveReportVo.getBegin_time());
            customExportVo.setContract_end_time(achieveReportVo.getEnd_time());
            customExportVo.setContract_creator(achieveReportVo.getCreator());
            customExportVo.setAgent_account_id(achieveReportVo.getAgent_id());
            customExportVo.setAgent_account_name(achieveReportVo.getAgent_name());
            customExportVo.setGroup_id(achieveReportVo.getGroup_id());
            customExportVo.setProduct_id(achieveReportVo.getProduct_Id());
            if (customExportVo.getProduct_type().equals(IncomeComposition.PICK_UP_ORDER.getDesc())) {
                customExportVo.setPickup_id(customExportVo.getOrder_id());
                customExportVo.setOrder_create_time(null);
                customExportVo.setOrder_id(null);
            }
            if (IncomeProductEnum.needFill().contains(customExportVo.getProduct_type())) {
                customExportVo.setIs_checking_desc(CheckingType.ENABLE.getName());
            }
            result.add(customExportVo);
        }

        buildCategoryInfo(result);
        buildProductTypeInfo(result);
        buildCrmProjectCategory(result);
        log.info("achieveToExport result: {}", result.size());
        return result;
    }

    public void buildCrmProjectCategory(List<BoardCustomExportVo> boardCustomExportVoList) {
        List<Integer> crmOrderIds = boardCustomExportVoList.stream().map(BoardCustomExportVo::getOrder_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, CrmOrderExtraDto> crmOrderExtraMap = iOrderService.queryOrderExtra(crmOrderIds);
        Map<Integer, Integer> crmOrderIdProjectIdMap = crmOrderExtraMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getProjectItemId(),
                (k1, k2) -> k1
        ));
        List<Integer> crmProjectIds = crmOrderExtraMap.values().stream().map(CrmOrderExtraDto::getProjectItemId).distinct().collect(Collectors.toList());

        Map<Integer, ProjectItemPackageDto> crmProjectMap = iNonStandardPackageService.queryPackageByIdList(crmProjectIds);
        boardCustomExportVoList.forEach(vo -> {
            Integer crmOrderId = vo.getOrder_id();
            if (crmOrderId != null) {
                Integer crmProjectId = crmOrderIdProjectIdMap.get(crmOrderId);
                if (crmProjectId != null) {
                    ProjectItemPackageDto projectItemPackageDto = crmProjectMap.get(crmProjectId);
                    if (projectItemPackageDto != null) {
                        vo.setCrm_project_first_category(projectItemPackageDto.getProjectFirstLevelCategory());
                        vo.setCrm_project_second_category(projectItemPackageDto.getProjectSecondLevelCategory());
                        vo.setCrm_project_third_category(projectItemPackageDto.getProjectMarketingCategory());
                        vo.setMarketingTeam(projectItemPackageDto.getMarketingTeam());
                    }
                }
            }
        });
    }

    public void buildBillCrmProjectCategory(List<BillDetailReportVo> billDetailReportVoList) {
        List<Integer> crmOrderIds = billDetailReportVoList.stream().map(BillDetailReportVo::getOrder_id).distinct().collect(Collectors.toList());
        Map<Integer, CrmOrderExtraDto> crmOrderExtraMap = iOrderService.queryOrderExtra(crmOrderIds);
        Map<Integer, Integer> crmOrderIdProjectIdMap = crmOrderExtraMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getProjectItemId(),
                (k1, k2) -> k1
        ));
        List<Integer> crmProjectIds = crmOrderExtraMap.values().stream().map(CrmOrderExtraDto::getProjectItemId).distinct().collect(Collectors.toList());

        Map<Integer, ProjectItemPackageDto> crmProjectMap = iNonStandardPackageService.queryPackageByIdList(crmProjectIds);
        billDetailReportVoList.forEach(vo -> {
            Integer crmOrderId = vo.getOrder_id();
            if (crmOrderId != null) {
                Integer crmProjectId = crmOrderIdProjectIdMap.get(crmOrderId);
                if (crmProjectId != null) {
                    ProjectItemPackageDto projectItemPackageDto = crmProjectMap.get(crmProjectId);
                    if (projectItemPackageDto != null) {
                        vo.setCrm_project_first_category(projectItemPackageDto.getProjectFirstLevelCategory());
                        vo.setCrm_project_second_category(projectItemPackageDto.getProjectSecondLevelCategory());
                        vo.setCrm_project_third_category(projectItemPackageDto.getProjectMarketingCategory());
                    }
                }
            }
        });
    }

    public List<BoardCustomExportVo> contractToExport(List<BillDetailReportVo> achieveReportVoList) {
        if (CollectionUtils.isEmpty(achieveReportVoList)) {
            return Lists.newArrayList();
        }
        List<BoardCustomExportVo> result = new ArrayList<>();
        for (BillDetailReportVo achieveReportVo : achieveReportVoList) {
            BoardCustomExportVo customExportVo = new BoardCustomExportVo();
            BeanUtils.copyProperties(achieveReportVo, customExportVo);
            customExportVo.setDate(achieveReportVo.getBill_date());
            customExportVo.setOrder_create_time(CrmUtils.formatDate(achieveReportVo.getOrder_create_time()));
            customExportVo.setOrder_end_time(CrmUtils.formatDate(achieveReportVo.getOrder_end_time()));
            customExportVo.setOrder_start_time(CrmUtils.formatDate(achieveReportVo.getOrder_start_time()));
            customExportVo.setIncome(achieveReportVo.getDaily_package_amount());
            customExportVo.setContract_create_time(CrmUtils.formatDate(achieveReportVo.getContract_create_time()));
            customExportVo.setContract_begin_time(achieveReportVo.getBegin_time());
            customExportVo.setContract_end_time(achieveReportVo.getEnd_time());
            customExportVo.setContract_creator(achieveReportVo.getCreator());
            customExportVo.setAccount_name(achieveReportVo.getCustomer_name());
            customExportVo.setGroup_id(achieveReportVo.getGroup_id());
            customExportVo.setAgent_account_id(achieveReportVo.getAgent_id());
            customExportVo.setAgent_account_name(achieveReportVo.getAgent_name());
            customExportVo.setStraight_name(achieveReportVo.getDirect_sales());
            customExportVo.setChannel_name(achieveReportVo.getChannel_sales());
            customExportVo.setProduct_type(IncomeComposition.CONTRACT_AD.getDesc());
            customExportVo.setIs_checking_desc(achieveReportVo.getIs_checking_desc());
            customExportVo.setChecking_state(achieveReportVo.getCheckingStateEnum() == null ? "" : achieveReportVo.getCheckingStateEnum().getDesc());
            result.add(customExportVo);
        }
        buildCategoryInfo(result);
        buildProductTypeInfo(result);
        return result;
    }

    public List<BoardCustomExportVo> pickUpToExport(List<AchievePickupExportVo> achieveReportVoList, DashBoardExportEnum dashBoardExportEnum) {
        if (CollectionUtils.isEmpty(achieveReportVoList)) {
            return Lists.newArrayList();
        }
        List<BoardCustomExportVo> result = new ArrayList<>();
        for (AchievePickupExportVo achieveReportVo : achieveReportVoList) {
            BoardCustomExportVo customExportVo = new BoardCustomExportVo();
            BeanUtils.copyProperties(achieveReportVo, customExportVo);
            customExportVo.setIncome(achieveReportVo.getAmount());
            if (dashBoardExportEnum.equals(DashBoardExportEnum.EXECUTED_PICKUP)) {
                customExportVo.setDate(achieveReportVo.getComplete_date());
            } else {
                customExportVo.setDate(achieveReportVo.getOnline_date());
            }
            customExportVo.setPickup_order_no(achieveReportVo.getPickup_order_no());
            customExportVo.setAgent_account_id(achieveReportVo.getAgent_id());
            customExportVo.setAgent_account_name(achieveReportVo.getAgent_name());
            customExportVo.setAccount_customer_id(achieveReportVo.getCustomer_id());
            customExportVo.setAccount_customer_name(achieveReportVo.getCustomer_name());
            customExportVo.setPickup_create_date(achieveReportVo.getCreate_date());
            customExportVo.setStraight_name(achieveReportVo.getDirect_sale_names());
            customExportVo.setChannel_name(achieveReportVo.getChannel_sale_names());
            customExportVo.setStraight_name_group(achieveReportVo.getDirect_sale_group());
            customExportVo.setStraight_name_second_group(achieveReportVo.getDirect_sale_team());
            customExportVo.setChannel_name_group(achieveReportVo.getChannel_sale_names_group());
            customExportVo.setChannel_name_second_group(achieveReportVo.getChannel_sale_names_team());
            customExportVo.setProduct_id(achieveReportVo.getBrand_id());
            customExportVo.setProduct_name(achieveReportVo.getBrand_name());
            customExportVo.setGroup_id(achieveReportVo.getGroup_id());
            customExportVo.setAccount_department_name(achieveReportVo.getDepartment_name());
            customExportVo.setProduct_type(IncomeComposition.PICK_UP_ORDER.getDesc());
            customExportVo.setFirst_category_name(achieveReportVo.getCategory_first_name());
            customExportVo.setSecond_category_name(achieveReportVo.getCategory_second_name());
            customExportVo.setNew_customer_complete_time(achieveReportVo.getNew_customer_complete_time());
            customExportVo.setPickup_bill_id(achieveReportVo.getId());
            customExportVo.setBill_source(achieveReportVo.getPickup_order_source_desc());
            result.add(customExportVo);
        }
        buildCategoryInfo(result);
        buildProductTypeInfo(result);
        return result;
    }

    public List<BoardCustomExportVo> rtbToExport(List<RtbReportAchievementVo> achieveReportVoList) {
        if (CollectionUtils.isEmpty(achieveReportVoList)) {
            return Lists.newArrayList();
        }
        List<BoardCustomExportVo> result = new ArrayList<>();
        for (RtbReportAchievementVo achieveReportVo : achieveReportVoList) {
            BoardCustomExportVo customExportVo = new BoardCustomExportVo();
            BeanUtils.copyProperties(achieveReportVo, customExportVo);
            customExportVo.setDate(CrmUtils.formatDate(achieveReportVo.getDate_time()));
            customExportVo.setIncome(achieveReportVo.getTotal_consume());
            customExportVo.setAgent_account_name(achieveReportVo.getAgent_name());
            customExportVo.setFirst_category_name(achieveReportVo.getCategory_first_name());
            customExportVo.setSecond_category_name(achieveReportVo.getCategory_second_name());
            customExportVo.setChecking_state(CheckingStateEnum.CHECKED.getDesc());
            customExportVo.setIs_checking_desc(CheckingType.ENABLE.getName());
            result.add(customExportVo);
        }
        buildCategoryInfo(result);
        buildProductTypeInfo(result);
        return result;
    }

    public List<BoardCustomExportVo> adxToExport(List<AdxReportAchievementVo> achieveReportVoList) {
        if (CollectionUtils.isEmpty(achieveReportVoList)) {
            return Lists.newArrayList();
        }
        List<BoardCustomExportVo> result = new ArrayList<>();
        for (AdxReportAchievementVo achieveReportVo : achieveReportVoList) {
            BoardCustomExportVo customExportVo = new BoardCustomExportVo();
            BeanUtils.copyProperties(achieveReportVo, customExportVo);
            customExportVo.setDate(CrmUtils.formatDate(achieveReportVo.getDate_time()));
            customExportVo.setIncome(achieveReportVo.getTotal_consume());
            customExportVo.setFirst_category_name(achieveReportVo.getFirst_category_name());
            customExportVo.setSecond_category_name(achieveReportVo.getSecond_category_name());
            customExportVo.setChecking_state(CheckingStateEnum.CHECKED.getDesc());
            result.add(customExportVo);
        }
        buildCategoryInfo(result);
        buildProductTypeInfo(result);
        return result;
    }

    public List<BoardCustomExportVo> contractUnRecordToExport(List<AchievementContractOrderedUnRecordDetailVo> achieveReportVoList) {
        if (CollectionUtils.isEmpty(achieveReportVoList)) {
            return Lists.newArrayList();
        }
        List<BoardCustomExportVo> result = new ArrayList<>();
        for (AchievementContractOrderedUnRecordDetailVo achieveReportVo : achieveReportVoList) {
            BoardCustomExportVo customExportVo = new BoardCustomExportVo();
            BeanUtils.copyProperties(achieveReportVo, customExportVo);
            customExportVo.setIncome(achieveReportVo.getDaily_package_amount());
            customExportVo.setContract_period(achieveReportVo.getPeriod());
            customExportVo.setUn_record_amount(achieveReportVo.getUn_record_amount());
            customExportVo.setPick_up_un_record_amount(achieveReportVo.getPick_up_un_record_amount());
            customExportVo.setBrand_un_record_amount(achieveReportVo.getBrand_un_record_amount());
            customExportVo.setContract_create_time(CrmUtils.formatDate(achieveReportVo.getContract_create_time()));
            customExportVo.setContract_begin_time(achieveReportVo.getBegin_time());
            customExportVo.setContract_end_time(achieveReportVo.getEnd_time());
            customExportVo.setContract_creator(achieveReportVo.getCreator());
            customExportVo.setAccount_customer_id(achieveReportVo.getCustomer_id());
            customExportVo.setAccount_customer_name(achieveReportVo.getCustomer_name());
            customExportVo.setAgent_account_id(achieveReportVo.getAgent_id());
            customExportVo.setAgent_account_name(achieveReportVo.getAgent_name());
            customExportVo.setContract_period(achieveReportVo.getPeriod());
            customExportVo.setContract_package_amount(achieveReportVo.getPackage_amount());
            customExportVo.setProduct_id(achieveReportVo.getProduct_id());
            customExportVo.setStraight_name(achieveReportVo.getDirect_sales());
            customExportVo.setChannel_name(achieveReportVo.getChannel_sales());
            customExportVo.setProduct_type(IncomeComposition.CONTRACT_AD.getDesc());
            customExportVo.setGroup_id(achieveReportVo.getGroup_id());
            result.add(customExportVo);
        }
        buildCategoryInfo(result);
        buildProductTypeInfo(result);
        return result;
    }

    public List<BoardCustomFieldVo> fieldDtoToVo(List<BoardCustomFieldDto> fieldDtoList) {
        List<BoardCustomFieldVo> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fieldDtoList)) {
            return voList;
        }
        for (BoardCustomFieldDto fieldDto : fieldDtoList) {
            BoardCustomFieldVo vo = new BoardCustomFieldVo();
            vo.setLevel(fieldDto.getLevel());
            vo.setField(fieldDto.getField());
            vo.setName(fieldDto.getName());
            vo.setChoose_type(fieldDto.getChooseType());
            vo.setNext_field_list(fieldDtoToVo(fieldDto.getNextFieldList()));
            voList.add(vo);
        }
        return voList;
    }

    public List<BoardCustomCardVo> cardDtoToVo(List<BoardCustomCardDto> fieldDtoList) {
        List<BoardCustomCardVo> voList = new ArrayList<>();
        if (CollectionUtils.isEmpty(fieldDtoList)) {
            return voList;
        }
        for (BoardCustomCardDto fieldDto : fieldDtoList) {
            BoardCustomCardVo vo = new BoardCustomCardVo();
            vo.setLevel(fieldDto.getLevel());
            vo.setType(fieldDto.getCardType());
            vo.setName(fieldDto.getName());
            vo.setChoose_type(fieldDto.getChooseType());
            vo.setNext_field_list(cardDtoToVo(fieldDto.getNextFieldList()));
            voList.add(vo);
        }
        return voList;
    }

    public void buildCategoryInfo(List<BoardCustomExportVo> customExportVos) {
        if (CollectionUtils.isEmpty(customExportVos)) {
            return;
        }
        List<Integer> productIds = customExportVos.stream().map(BoardCustomExportVo::getProduct_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<Integer> customerIds = customExportVos.stream().map(BoardCustomExportVo::getAccount_customer_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CustomerBaseDto> customerBaseDtos = iCustomerQueryService.getCustomerBaseDtosByIds(Lists.newArrayList(customerIds));
        Map<Integer, CustomerBaseDto> customerDtoMap = customerBaseDtos.stream().collect(Collectors.toMap(CustomerBaseDto::getId, Function.identity()));
        List<Integer> agentAccountIds = customExportVos.stream().map(BoardCustomExportVo::getAgent_account_id).filter(Objects::nonNull).distinct().filter(a -> a > 0).collect(Collectors.toList());
        Map<Integer, AccountBaseDto> agentAccountMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(agentAccountIds)) {
            List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder()
                    .accountIds(agentAccountIds).build(), AccountFieldMapping.accountId, AccountFieldMapping.departmentId, AccountFieldMapping.groupId);
            agentAccountMap = accountBaseDtos.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));
        }
        Map<Integer, String> departmentMap = crmDepartmentService.getDepartmentMap(new ArrayList<>(agentAccountMap.values()));
        List<Integer> groupIds = agentAccountMap.values().stream().map(AccountBaseDto::getGroupId).distinct().collect(Collectors.toList());
        List<AccCompanyGroupPo> agentCompanyGroupPos = accCompanyGroupRepo.queryListByIds(groupIds);
        Map<Integer, AccCompanyGroupPo> agentCompanyGroupMap = agentCompanyGroupPos.stream().collect(Collectors.toMap(AccCompanyGroupPo::getId, Function.identity()));
        Map<Integer, AccProductPo> productPoMap = Maps.newHashMap();
        if (!CollectionUtils.isEmpty(productIds)) {
            productPoMap = accProductRepo.getProductMapByIds(productIds);
        }
        List<Integer> brandCategoryIds = Lists.newArrayList();
        brandCategoryIds.addAll(productPoMap.values().stream().map(AccProductPo::getCommerceCategoryFirstId).collect(Collectors.toList()));
        brandCategoryIds.addAll(productPoMap.values().stream().map(AccProductPo::getCommerceCategorySecondId).collect(Collectors.toList()));
        List<CategoryDto> categories = bizIndustryRepo.getCategoryInIds(brandCategoryIds);

        // 客户行业统一处理
        Map<String, String> customerIndustryMap = aggIndustryService.queryIndustryInfo(customerDtoMap,
                Lists.newArrayList(IndustryTypeEnum.BIZ_INDUSTRY, IndustryTypeEnum.COMMERCE_INDUSTRY, IndustryTypeEnum.UNITED_INDUSTRY));

        Map<Integer, CategoryDto> brandCategoryMap = categories.stream().collect(Collectors.toMap(CategoryDto::getId, Function.identity(), (k1, k2) -> k1));

        for (BoardCustomExportVo exportVo : customExportVos) {
            if (!StringUtils.isEmpty(exportVo.getStraight_name()) && exportVo.getStraight_name().contains("专项PM")) {
                exportVo.setSign_type(SignTypeEnum.INNER.getDesc());
            }
            if (Utils.isPositive(exportVo.getAgent_account_id())) {
                AccountBaseDto agnetAccountBaseDto = agentAccountMap.getOrDefault(exportVo.getAgent_account_id(), AccountBaseDto.builder().groupId(0).build());
                exportVo.setAgent_account_department_id(agnetAccountBaseDto.getDepartmentId());
                exportVo.setAgent_account_department(departmentMap.get(exportVo.getAgent_account_id()));
                exportVo.setAgent_account_group(agentCompanyGroupMap.getOrDefault(agnetAccountBaseDto.getGroupId(), new AccCompanyGroupPo()).getName());
                exportVo.setAgent_account_group_id(agnetAccountBaseDto.getGroupId());
            } else {
                exportVo.setAgent_account_group_id(null);
                exportVo.setAgent_account_group(null);
            }
            if (exportVo.getAccount_customer_id() != null) {
                CustomerBaseDto customerBaseDto = customerDtoMap.getOrDefault(exportVo.getAccount_customer_id(), CustomerBaseDto.builder()
                        .customerCategory(CustomerCategoryType.ORG_CUSTOMER.getCode())
                        .build());
                exportVo.setCustomer_category(CustomerCategoryType.getByCode(customerBaseDto.getCustomerCategory()).getDesc());
                exportVo.setBiz_industry_category_first_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.BIZ_INDUSTRY + "-" + customerBaseDto.getBizIndustryCategoryFirstId(), ""));
                exportVo.setBiz_industry_category_second_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.BIZ_INDUSTRY + "-" + customerBaseDto.getBizIndustryCategorySecondId(), ""));
                exportVo.setRisk_category_first_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.COMMERCE_INDUSTRY + "-" + customerBaseDto.getCommerceCategoryFirstId(), ""));
                exportVo.setRisk_category_second_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.COMMERCE_INDUSTRY + "-" + customerBaseDto.getCommerceCategorySecondId(), ""));
                exportVo.setCustomer_united_first_industry_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + customerBaseDto.getUnitedFirstIndustryId(), ""));
                exportVo.setCustomer_united_second_industry_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + customerBaseDto.getUnitedSecondIndustryId(), ""));
                exportVo.setCustomer_united_third_industry_name(customerIndustryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + customerBaseDto.getUnitedThirdIndustryId(), ""));

                if (StringUtils.isEmpty(exportVo.getSign_type()) && customerBaseDto.getCustomerCategory().equals(CustomerCategoryType.ORG_CUSTOMER.getCode())) {
                    if (StringUtils.isEmpty(exportVo.getStraight_name()) && StringUtils.isEmpty(exportVo.getChannel_name())) {
                        exportVo.setSign_type(SignTypeEnum.SELF.getDesc());
                    } else if (StringUtils.isEmpty(exportVo.getStraight_name()) && !StringUtils.isEmpty(exportVo.getChannel_name())) {
                        exportVo.setSign_type(SignTypeEnum.CHANNEL.getDesc());
                    } else if (!StringUtils.isEmpty(exportVo.getStraight_name()) && StringUtils.isEmpty(exportVo.getChannel_name())) {
                        exportVo.setSign_type(SignTypeEnum.DIRECT.getDesc());
                    }
                }
                if (StringUtils.isEmpty(exportVo.getSign_type())) {
                    exportVo.setSign_type(SignTypeEnum.NO_DIRECT.getDesc());
                }
                if (exportVo.getProduct_id() != null) {
                    AccProductPo accProductPo = productPoMap.getOrDefault(exportVo.getProduct_id(), AccProductPo.builder()
                            .commerceCategoryFirstId(0)
                            .commerceCategorySecondId(0)
                            .build());
                    exportVo.setBrand_category_first_name(brandCategoryMap.getOrDefault(accProductPo.getCommerceCategoryFirstId(), new CategoryDto()).getName());
                    exportVo.setBrand_category_second_name(brandCategoryMap.getOrDefault(accProductPo.getCommerceCategorySecondId(), new CategoryDto()).getName());
                }
            }
        }
    }

    public void buildProductTypeInfo(List<BoardCustomExportVo> customExportVos) {
        if (CollectionUtils.isEmpty(customExportVos)) {
            return;
        }
        List<Integer> productIds = customExportVos.stream()
                .map(BoardCustomExportVo::getSecond_category_product_id)
                .filter(Utils::isPositive)
                .distinct()
                .collect(Collectors.toList());
        List<NonStandardProductCategoryDto> productCategoryDtoList = nonStandardProductCategoryService.getByIds(productIds);
        Map<Integer, NonStandardProductCategoryDto> productCategoryDtoMap = productCategoryDtoList.stream()
                .collect(Collectors.toMap(NonStandardProductCategoryDto::getId, Function.identity(), (v1, v2) -> v1));
        for (BoardCustomExportVo exportVo : customExportVos) {
            CheckingStateEnum checkingStateEnum = CheckingStateEnum.queryByName(exportVo.getChecking_state());
            ProductTypeExportEnum product_first_type_enum = ProductTypeExportEnum.queryByName(exportVo.getProduct_first_type());
            ProductTypeExportEnum product_second_type_enum = ProductTypeExportEnum.queryByName(exportVo.getProduct_second_type());
            ProductTypeExportEnum product_third_type_enum = ProductTypeExportEnum.queryByName(exportVo.getProduct_third_type());

            exportVo.setBusiness_product_type(buildBusinessProductType(exportVo));

            Integer productId = exportVo.getSecond_category_product_id();
            NonStandardProductCategoryDto productCategoryDto = productCategoryDtoMap.get(productId);
            // 使用配置标签的product_third_type
            if (Objects.nonNull(productCategoryDto) && !StringUtils.isEmpty(productCategoryDto.getProductLabel())) {
                String productLabel = productCategoryDto.getProductLabel();
                exportVo.setProduct_third_type(productLabel);
                product_third_type_enum = ProductTypeExportEnum.getByName(productLabel);
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && exportVo.getProduct_type().equals(IncomeComposition.CONTRACT_AD.getDesc())) {
                if ((exportVo.getOrder_type() != null && CrmOrderType.getBrandHardProgramType().contains(exportVo.getOrder_type()))
                        || (exportVo.getSecond_category_product_id() != null && HARD_PROGRAM_SECOND_PRODUCT.contains(exportVo.getSecond_category_product_id()))) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.BRAND_HARD_PROGRAM.getName());
                    product_third_type_enum = ProductTypeExportEnum.BRAND_HARD_PROGRAM;
                } else if ((exportVo.getOrder_type() != null && CrmOrderType.getBrandHardNormalType().contains(exportVo.getOrder_type())) ||
                        (exportVo.getSecond_category_product_id() != null && HOT_SEARCH_SECOND_CATE.contains(exportVo.getSecond_category_product_id()))
                        || (exportVo.getFirst_category_product_id() != null && NORMAL_BRAND_FIRST_CATE.contains(exportVo.getFirst_category_product_id()))) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.BRAND_HARD_NORMAL.getName());
                    product_third_type_enum = ProductTypeExportEnum.BRAND_HARD_NORMAL;
                } else if ((exportVo.getOrder_type() != null && CrmOrderType.LIVE.getCode().equals(exportVo.getOrder_type())) ||
                        (exportVo.getFirst_category_product_id() != null && exportVo.getFirst_category_product_id().equals(LIVING_NORMAL_HARD_AD))) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.NON_STANDARD_LIVING.getName());
                    product_third_type_enum = ProductTypeExportEnum.NON_STANDARD_LIVING;
                } else if ((exportVo.getOrder_type() != null && CrmOrderType.NON_STANDARD.getCode().equals(exportVo.getOrder_type())) &&
                        (exportVo.getFirst_category_product_id() != null && !NON_STANDARD_FOR_EXPORT.contains(exportVo.getFirst_category_product_id()))
                        && !HOT_SEARCH_SECOND_CATE.contains(exportVo.getSecond_category_product_id())) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.NON_STANDARD_NORMAL.getName());
                    product_third_type_enum = ProductTypeExportEnum.NON_STANDARD_NORMAL;
                } else if (exportVo.getFirst_category_product_id() != null && DepAchieveConfigBeta.CONTRACT_ORDER_LIVE_ANCHOR_PICKUP_FIRST_CATE.equals(exportVo.getFirst_category_product_id())) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.LIVING_PICKUP.getName());
                    product_third_type_enum = ProductTypeExportEnum.LIVING_PICKUP;
                } else if ((exportVo.getFirst_category_product_id() != null && DepAchieveConfigBeta.CONTRACT_ORDER_PICKUP_FIRST_CATE.equals(exportVo.getFirst_category_product_id())) ||
                        (exportVo.getOrder_type() != null && DepAchieveConfigBeta.OTHER_PICKUP_ORDER_TYPE.contains(exportVo.getOrder_type()))) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.OTHER_PICKUP.getName());
                    product_third_type_enum = ProductTypeExportEnum.OTHER_PICKUP;
                }
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && IncomeComposition.RTB.getDesc().equals(exportVo.getProduct_type())) {
                exportVo.setProduct_third_type(ProductTypeExportEnum.CPC_CPM.getName());
                product_third_type_enum = ProductTypeExportEnum.CPC_CPM;
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && Lists.newArrayList(IncomeComposition.BUSINESS_FLY.getDesc(), IncomeComposition.CONTENT_FLY.getDesc(), IncomeComposition.PERSON_FLY.getDesc()).contains(exportVo.getProduct_type())) {
                if (!StringUtils.isEmpty(exportVo.getAgent_account_name()) && exportVo.getAgent_account_name().contains("带货")) {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.BUSINESS_FLY_SELL_GOOD.getName());
                    product_third_type_enum = ProductTypeExportEnum.BUSINESS_FLY_SELL_GOOD;
                } else {
                    exportVo.setProduct_third_type(ProductTypeExportEnum.BUSINESS_FLY_NON_SELL_GOOD.getName());
                    product_third_type_enum = ProductTypeExportEnum.BUSINESS_FLY_NON_SELL_GOOD;
                }
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && (IncomeComposition.DPA.getDesc().equals(exportVo.getProduct_type()) || IncomeComposition.DPA_CLOSE.getDesc().equals(exportVo.getProduct_type()))) {
                exportVo.setProduct_third_type(ProductTypeExportEnum.DPA.getName());
                product_third_type_enum = ProductTypeExportEnum.DPA;
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && (IncomeComposition.ADX.getDesc().equals(exportVo.getProduct_type()) || IncomeComposition.ADX_CLOSE.getDesc().equals(exportVo.getProduct_type()))) {
                exportVo.setProduct_third_type(ProductTypeExportEnum.ADX.getName());
                product_third_type_enum = ProductTypeExportEnum.ADX;
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && IncomeComposition.PICK_UP_ORDER.getDesc().equals(exportVo.getProduct_type())) {
                exportVo.setProduct_third_type(ProductTypeExportEnum.NORMAL_PICKUP.getName());
                product_third_type_enum = ProductTypeExportEnum.NORMAL_PICKUP;
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && IncomeComposition.RTB_FLY_CLOSE.getDesc().equals(exportVo.getProduct_type())) {
                exportVo.setProduct_second_type(ProductTypeExportEnum.RTB.getName());
                exportVo.setProduct_first_type(ProductTypeExportEnum.EFFECT.getName());
            } else if (!StringUtils.isEmpty(exportVo.getProduct_type()) && IncomeComposition.CLUE_PASS.getDesc().equals(exportVo.getProduct_type())) {
                exportVo.setProduct_second_type(ProductTypeExportEnum.CLUE_PASS_TWO.getName());
                exportVo.setProduct_third_type(ProductTypeExportEnum.CLUE_PASS_THREE.getName());
                product_third_type_enum = ProductTypeExportEnum.CLUE_PASS_THREE;
            }
            if (product_third_type_enum != null) {
                product_second_type_enum = product_third_type_enum.getParent();
                exportVo.setProduct_second_type(product_second_type_enum.getName());
                product_first_type_enum = product_second_type_enum.getParent();
                exportVo.setProduct_first_type(product_first_type_enum.getName());
            }
            if (product_first_type_enum != null && product_first_type_enum.equals(ProductTypeExportEnum.EFFECT)) {
                exportVo.setChecking_state(CheckingStateEnum.CHECKED.getDesc());
            }
            if (checkingStateEnum != null) {
                exportVo.setChecking_state(checkingStateEnum.getDesc());
            }
        }
    }


    // 口径 ：产品类型 in { 商业起飞，内容起飞，个人起飞}，and 【代理商账户名称】含“带货"字样，or广告主账户【所属部门】含“带货"标签
    public String buildBusinessProductType(BoardCustomExportVo exportVo) {
        return (exportVo.getProduct_type() != null && exportVo.getProduct_type().contains("起飞")) && (exportVo.getAgent_account_name() != null && exportVo.getAgent_account_name().contains("带货")) ||
                (exportVo.getDepartment_name() != null && exportVo.getDepartment_name().contains("带货")) ? "带货" : "非带货";
    }
}
