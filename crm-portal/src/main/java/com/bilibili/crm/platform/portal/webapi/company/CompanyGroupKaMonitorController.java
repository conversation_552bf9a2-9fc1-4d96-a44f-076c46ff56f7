package com.bilibili.crm.platform.portal.webapi.company;


import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.platform.api.effect.enums.ReportColumnBehavior;
import com.bilibili.crm.platform.api.ka_belonging.enums.KaBelongingConfigLevelEnum;
import com.bilibili.crm.platform.biz.annotation.Export;
import com.bilibili.crm.platform.biz.cache.CrmCacheKaMonitorKey;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.repo.kamonitor.*;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.biz.util.ExcelReadUtils;
import com.bilibili.crm.platform.portal.common.UploadCenterUtil;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.download.coverter.DownloadUtil;
import com.bilibili.crm.platform.utils.ThreadPoolUtils;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import java.util.function.Supplier;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.CollectionUtils;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Controller
@RequestMapping(value = {"/web_api/v1/group/kamonitor", "/wx_app/group/kamonitor"})
@Api(value = "group/kamonitor", description = "公司集团的ka监控")
public class CompanyGroupKaMonitorController extends BaseRestfulController {

    @Autowired
    CompanyGroupKaMonitorRepo companyGroupKaMonitorRepo;

    @Resource
    private UploadCenterUtil uploadCenterUtil;

    @Autowired
    private CrmCacheManager crmCacheManager;

    @ApiOperation(value = "新客监控")
    @RequestMapping(value = "/newgroup", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<NewGroupKaMonitorDto>> newGroup(@ApiIgnore Context context,
                                                         @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate) throws ParseException {

        Operator operator = getOperator(context);
        List<NewGroupKaMonitorDto> list = companyGroupKaMonitorRepo.kaMonitorForNew(operator, logDate);

        // 获取7天前数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date servenDate = sdf.parse(logDate);
        Calendar calendar = Calendar.getInstance(); // 获取当前日期
        calendar.setTime(servenDate);
        calendar.add(Calendar.DAY_OF_MONTH, -7); // 将日期减7
        Date previousDate = calendar.getTime();
        String servenDaysAgoDate = sdf.format(previousDate);

        List<NewGroupKaMonitorDto> servenDaysAgolist = companyGroupKaMonitorRepo.kaMonitorForNew(operator, servenDaysAgoDate);

        Map<String, NewGroupKaMonitorDto> servenDaysAgoDtoMap = servenDaysAgolist.stream().collect(Collectors.toMap(NewGroupKaMonitorDto::getKey, Function.identity(), (x1, x2) -> x1));
        for (NewGroupKaMonitorDto dto : list) {
            if (!servenDaysAgoDtoMap.containsKey(dto.getKey())) {
                continue;
            }
            NewGroupKaMonitorDto servenDayDto = servenDaysAgoDtoMap.get(dto.getKey());
            dto.setAllCompanyGroupArpuWow(CrmUtils.dividedByDouble((dto.getAllCompanyGroupArpu() - servenDayDto.getAllCompanyGroupArpu()), servenDayDto.getAllCompanyGroupArpu(), 4));
            dto.setAllCompanyGroupNumWow(dto.getAllCompanyGroupNum() - servenDayDto.getAllCompanyGroupNum());
        }
        return Response.SUCCESS(list);
    }

    @ApiOperation(value = "大盘监控")
    @RequestMapping(value = "/market", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<GroupKaMonitorMarketDto>> market(@ApiIgnore Context context,
                                                          @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate) throws ParseException {
        Operator operator = getOperator(context);

        // 获取7天前数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date servenDate = sdf.parse(logDate);
        Calendar calendar = Calendar.getInstance(); // 获取当前日期
        calendar.setTime(servenDate);
        calendar.add(Calendar.DAY_OF_MONTH, -7); // 将日期减7
        Date previousDate = calendar.getTime();
        String servenDaysAgoDate = sdf.format(previousDate);

        List<String> dateList = new ArrayList<>();
        dateList.add(logDate);
        dateList.add(servenDaysAgoDate);

        List<Map<String, List<GroupKaMonitorMarketDto>>> asyncResult = ThreadPoolUtils.asyncExecute(2, dateList, dates -> {
            List<Map<String, List<GroupKaMonitorMarketDto>>> result = new ArrayList<>();
            List<GroupKaMonitorMarketDto> groupKaMonitorMarketDtos = companyGroupKaMonitorRepo.kaMonitorForMarket(operator, dates.get(0));
            Map<String, List<GroupKaMonitorMarketDto>> resultMap = new HashMap<>();
            resultMap.put(dates.get(0), groupKaMonitorMarketDtos);
            result.add(resultMap);
            return result;
        });
        List<GroupKaMonitorMarketDto> list = new ArrayList<>();
        List<GroupKaMonitorMarketDto> servenDaysAgolist = new ArrayList<>();
        for (Map<String, List<GroupKaMonitorMarketDto>> dtoListMap : asyncResult) {
            if (dtoListMap.containsKey(logDate)) {
                list = dtoListMap.get(logDate);
            } else if (dtoListMap.containsKey(servenDaysAgoDate)) {
                servenDaysAgolist = dtoListMap.get(servenDaysAgoDate);
            }
        }

        Map<String, List<GroupKaMonitorMarketDto>> kaInfoMap = list.stream().collect(Collectors.groupingBy(GroupKaMonitorMarketDto::getKey));
        list.forEach(x -> {
            if (kaInfoMap.containsKey(x.getKey())) {
                List<GroupKaMonitorMarketDto> groupKaMonitorMarketDtos = kaInfoMap.get(x.getKey());
                double allPerformance = groupKaMonitorMarketDtos.stream().filter(y -> y.getKaType() != KaBelongingConfigLevelEnum.UNKNOWN.getCode()).mapToDouble(GroupKaMonitorMarketDto::getWeekCompanyGroupPerformance).sum();
                x.setWeekCompanyGroupPerformancePer((float) CrmUtils.dividedByDouble(x.getWeekCompanyGroupPerformance(), allPerformance, 2));

                if (x.getKaType().equals(KaBelongingConfigLevelEnum.UNKNOWN.getCode())) {
                    x.setWeekCompanyGroupPerformancePer(1.0f);
                }
            }
        });

        Map<String, GroupKaMonitorMarketDto> servenDaysAgoDtoMap = new HashMap<>();
        servenDaysAgolist.forEach(dto -> {
            String key = dto.getKey() + dto.getKaType();
            servenDaysAgoDtoMap.put(key, dto);
        });

        for (GroupKaMonitorMarketDto dto : list) {
            if (Objects.isNull(dto)) {
                continue;
            }
            String currentKey = dto.getKey() + dto.getKaType();

            if (servenDaysAgoDtoMap.containsKey(currentKey)) {
                GroupKaMonitorMarketDto servenDayDto = servenDaysAgoDtoMap.get(currentKey);
                dto.setWeekCompanyGroupArpuWow((float) CrmUtils.dividedByDouble((dto.getWeekCompanyGroupArpu() - servenDayDto.getWeekCompanyGroupArpu()), servenDayDto.getWeekCompanyGroupArpu(), 4));
                dto.setWeekCompanyGroupNumWow((float) CrmUtils.dividedByDouble((dto.getWeekCompanyGroupNum() - servenDayDto.getWeekCompanyGroupNum()), servenDayDto.getWeekCompanyGroupNum(), 4));
                dto.setWeekCompanyGroupPerformanceWow((float) CrmUtils.dividedByDouble((dto.getWeekCompanyGroupPerformance() - servenDayDto.getWeekCompanyGroupPerformance()), servenDayDto.getWeekCompanyGroupPerformance(), 4));
                dto.setWeekCompanyGroupGapNum(dto.getWeekCompanyGroupNum() - servenDayDto.getWeekCompanyGroupNum());
                dto.setQuarterTargetCompanyGroupGapNum(dto.getWeekCompanyGroupNum() - dto.getYearCompanyGroupNum());
                dto.setWeekArpuProgress((float) CrmUtils.dividedByDouble(dto.getWeekCompanyGroupArpu(), dto.getYearCompanyGroupArpu(), 2));
                // qtd 数据
                dto.setQtdCompanyGroupPerformanceWow((float) CrmUtils.dividedByDouble((dto.getQtdCompanyGroupPerformance() - servenDayDto.getQtdCompanyGroupPerformance()), servenDayDto.getQtdCompanyGroupPerformance(), 4));
                dto.setQtdCompanyGroupArpuWow((float) CrmUtils.dividedByDouble((dto.getQtdCompanyGroupArpu() - servenDayDto.getQtdCompanyGroupArpu()), servenDayDto.getQtdCompanyGroupArpu(), 4));
                dto.setQtdActivityCompanyGroupNumWow(dto.getQtdActivityCompanyGroupNum() - servenDayDto.getQtdActivityCompanyGroupNum());

            }
        }
        return Response.SUCCESS(list);
    }

    @ApiOperation(value = "留存监控")
    @RequestMapping(value = "/retention", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<GroupKaMonitorRetentionDto>> retention(@ApiIgnore Context context,
                                                                @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate) throws ParseException {
        Operator operator = getOperator(context);

        // 获取7天前数据
        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date servenDate = sdf.parse(logDate);
        Calendar calendar = Calendar.getInstance(); // 获取当前日期
        calendar.setTime(servenDate);
        calendar.add(Calendar.DAY_OF_MONTH, -7); // 将日期减7
        Date previousDate = calendar.getTime();
        String servenDaysAgoDate = sdf.format(previousDate);

        List<String> dateList = new ArrayList<>();
        dateList.add(logDate);
        dateList.add(servenDaysAgoDate);

        List<Map<String, List<GroupKaMonitorRetentionDto>>> asyncResult = ThreadPoolUtils.asyncExecute(2, dateList, dates -> {
            List<Map<String, List<GroupKaMonitorRetentionDto>>> result = new ArrayList<>();
            List<GroupKaMonitorRetentionDto> groupKaMonitorMarketDtos = companyGroupKaMonitorRepo.kaMonitorForRetention(operator, dates.get(0));
            Map<String, List<GroupKaMonitorRetentionDto>> resultMap = new HashMap<>();
            resultMap.put(dates.get(0), groupKaMonitorMarketDtos);
            result.add(resultMap);
            return result;
        });
        List<GroupKaMonitorRetentionDto> list = new ArrayList<>();
        List<GroupKaMonitorRetentionDto> servenDaysAgolist = new ArrayList<>();
        for (Map<String, List<GroupKaMonitorRetentionDto>> dtoListMap : asyncResult) {
            if (dtoListMap.containsKey(logDate)) {
                list = dtoListMap.get(logDate);
            } else if (dtoListMap.containsKey(servenDaysAgoDate)) {
                servenDaysAgolist = dtoListMap.get(servenDaysAgoDate);
            }
        }


        Map<String, GroupKaMonitorRetentionDto> servenDaysAgoDtoMap = new HashMap<>();
        servenDaysAgolist.forEach(dto -> {
            String key = dto.getKey() + dto.getKaType();
            servenDaysAgoDtoMap.put(key, dto);
        });

        for (GroupKaMonitorRetentionDto dto : list) {
            if (Objects.isNull(dto)) {
                continue;
            }
            String currentKey = dto.getKey() + dto.getKaType();
            if (servenDaysAgoDtoMap.containsKey(currentKey)) {
                GroupKaMonitorRetentionDto servenDayDto = servenDaysAgoDtoMap.get(currentKey);
                dto.setNoadCompanyGroupNumGap(dto.getNoadCompanyGroupNum() - servenDayDto.getNoadCompanyGroupNum());
                dto.setNoadCompanyGroupNumWow((float) CrmUtils.dividedByDouble((double) (dto.getNoadCompanyGroupNum() - servenDayDto.getNoadCompanyGroupNum()), servenDayDto.getNoadCompanyGroupNum(), 4));

            }
        }
        return Response.SUCCESS(list);
    }

    @ApiOperation(value = "跃迁监控")
    @RequestMapping(value = "/transition", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<GroupKaMonitorTransitionDto>> transition(@ApiIgnore Context context,
                                                                  @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate) {
        // 不需要处理周同比
        Operator operator = getOperator(context);
        List<GroupKaMonitorTransitionDto> list = companyGroupKaMonitorRepo.kaMonitorForTransition(operator, logDate);
        return Response.SUCCESS(list);
    }

    @ApiOperation(value = "集团列表")
    @RequestMapping(value = "/company_group_list", method = RequestMethod.GET)
    @ResponseBody
    public Response<PageResult<KaInfo.CompanyGroupInfo>> companyGroupList(@ApiIgnore Context context,
                                                                          @ApiParam("页码") @RequestParam(value = "page_no", required = true) Integer pageNo,
                                                                          @ApiParam("行数") @RequestParam(value = "page_size", required = true) Integer pageSize,
                                                                          @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate,
                                                                          @ApiParam("报表类型") @RequestParam(value = "report_type", required = true) Integer reportType,
                                                                          @ApiParam("报表类型") @RequestParam(value = "key", required = true) String key,
                                                                          @ApiParam("集团分层key") @RequestParam(value = "ka_type", required = false) Integer kaType,
                                                                          @ApiParam("字段key") @RequestParam(value = "column_key", required = true) String columnKey) {
        Operator operator = getOperator(context);
        List<KaInfo.CompanyGroupInfo> list = companyGroupKaMonitorRepo.kaMonitorCompanyGroupList(operator, logDate, reportType, key, columnKey, KaBelongingConfigLevelEnum.getByCode(kaType));
        if (CollectionUtils.isEmpty(list)) {
            return Response.SUCCESS();
        }
        int start = pageNo * pageSize;
        int end = (pageNo + 1) * pageSize;
        if (list.size() - 1 < start) {
            return Response.SUCCESS();
        }
        if (list.size() < end) {
            end = list.size();
        }
        PageResult<KaInfo.CompanyGroupInfo> pageResult = new PageResult<>(list.size(), list.subList(start, end));
        return Response.SUCCESS(pageResult);
    }

    @ApiOperation(value = "集团列表下载")
    @RequestMapping(value = "/company_group_list_download", method = RequestMethod.GET)
    @Export
    @ResponseBody
    public Response<String> companyGroupListDownList(@ApiIgnore Context context,
                                                     @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate,
                                                     @ApiParam("报表类型") @RequestParam(value = "report_type", required = true) Integer reportType,
                                                     @ApiParam("报表类型") @RequestParam(value = "key", required = true) String key,
                                                     @ApiParam("集团分层key") @RequestParam(value = "ka_type", required = false) Integer kaType,
                                                     @ApiParam("字段key") @RequestParam(value = "column_key", required = true) String columnKey) {
        Operator operator = getOperator(context);
        Supplier<List<KaInfo.CompanyGroupInfo>> supplier = () -> companyGroupKaMonitorRepo.kaMonitorCompanyGroupList(operator, logDate, reportType, key, columnKey, KaBelongingConfigLevelEnum.getByCode(kaType));
        String fileName = DownloadUtil.generateFileName(null, null, ReportColumnBehavior.KA_MONITOR_NEW_GROUP);
        uploadCenterUtil.uploadToCenter(operator, supplier, fileName, ReportColumnBehavior.KA_MONITOR_NEW_GROUP);
        return Response.SUCCESS(DownloadUtil.return_str);
    }

    @ApiOperation(value = "曲线")
    @RequestMapping(value = "/is_admin", method = RequestMethod.GET)
    @ResponseBody
    public Response<Boolean> isAdmin(@ApiIgnore Context context) {
        Operator operator = getOperator(context);
        boolean isAdmin = companyGroupKaMonitorRepo.isAdmin(operator);
        return Response.SUCCESS(isAdmin);
    }


    @ApiOperation(value = "曲线")
    @RequestMapping(value = "/rate_line", method = RequestMethod.GET)
    @ResponseBody
    public Response<Map<String, Map<String, List<Float>>>> retentionLine(@ApiIgnore Context context,
                                                                         @ApiParam("日期") @RequestParam(value = "log_date", required = true) String logDate) {

        Operator operator = getOperator(context);
        boolean isAdmin = companyGroupKaMonitorRepo.isAdmin(operator);
        if (!isAdmin) {
            return Response.SUCCESS();
        }

        Map<String, List<Float>> retention1000WMap;
        Map<String, List<Float>> retention300WMap;
        Map<String, List<Float>> transition300WMap;
        Map<String, List<Float>> transition100WMap;

        Map<String, Map<String, List<Float>>> resultLine = new HashMap<>();

        String year = logDate.substring(0, 4);
        if (year.equals("2024")) {
            year = "";
        }

        Object result = crmCacheManager.getValue(year + CrmCacheKaMonitorKey.KA_RETENTION_1000_RATE_LINE);
        retention1000WMap = JSON.parseObject(result.toString(), Map.class);
        result = crmCacheManager.getValue(year + CrmCacheKaMonitorKey.KA_RETENTION_300_RATE_LINE);
        retention300WMap = JSON.parseObject(result.toString(), Map.class);
        result = crmCacheManager.getValue(year + CrmCacheKaMonitorKey.KA_TRANSITION_300_RATE_LINE);
        transition300WMap = JSON.parseObject(result.toString(), Map.class);
        result = crmCacheManager.getValue(year + CrmCacheKaMonitorKey.KA_TRANSITION_100_RATE_LINE);
        transition100WMap = JSON.parseObject(result.toString(), Map.class);

        resultLine.put("retention_1000W", retention1000WMap);
        resultLine.put("retention_300W", retention300WMap);
        resultLine.put("transition_300W", transition300WMap);
        resultLine.put("transition_100W", transition100WMap);

        return Response.SUCCESS(resultLine);
    }

    @ApiOperation(value = "上传配置文件")
    @RequestMapping(value = "/upload_file", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> updateAccSales(@ApiIgnore Context context,
                                           @ApiParam("报表类型") @RequestParam(value = "report_type", required = true) Integer reportType,
                                           @ApiParam("季度") @RequestParam(value = "quarter_type", required = false) Integer quarterType,
                                           @RequestParam(value = "file", required = false) MultipartFile multipartFile) throws Exception {
        List<String> rowsList = ExcelReadUtils.readExcel(multipartFile.getOriginalFilename(), multipartFile.getBytes(), 0);
        // 判断是q几
        String[] values = rowsList.get(0).split(",");
        if (values.length < 10) {
            return Response.FAIL(0, "文件格式异常");
        }
        String year = Integer.valueOf(values[4].substring(0, 4)) + "";
        log.info("values = {}", year);
        if (year.equals("2024")) {
            year = "";
        }
        int q = Integer.valueOf(values[6].substring(1));
        String key = CrmCacheKaMonitorKey.KA_MARKET_CONFIG_Q1;
        if (q == 1) {
            key = CrmCacheKaMonitorKey.KA_MARKET_CONFIG_Q1;
        } else if (q == 2) {
            key = CrmCacheKaMonitorKey.KA_MARKET_CONFIG_Q2;
        } else if (q == 3) {
            key = CrmCacheKaMonitorKey.KA_MARKET_CONFIG_Q3;
        } else if (q == 4) {
            key = CrmCacheKaMonitorKey.KA_MARKET_CONFIG_Q4;
        }
        key = year + key;
        rowsList.remove(1);
        rowsList.remove(0);
        String valuesStr = String.join(";", rowsList);
        log.info("valuesStr = {}", valuesStr);
        log.info("valuesStr key = {}", key);
        crmCacheManager.setKv(key, valuesStr, 1000L, TimeUnit.DAYS);
        return Response.SUCCESS();
    }
}
