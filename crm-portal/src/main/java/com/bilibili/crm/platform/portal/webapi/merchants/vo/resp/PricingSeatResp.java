package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import javax.validation.constraints.Min;

/**
 * <AUTHOR>
 * @date 2025/3/20
 */
@Data
public class PricingSeatResp {

    @ApiModelProperty("席位类型: 0-其他 1-特约赞助 2-行业赞助 3-指定合作 4-总冠 5-联合赞助")
    private Byte seatType;

    @ApiModelProperty("席位类型描述")
    private String seatTypeDesc;

    @ApiModelProperty("席位数量")
    private Long seatCount;

    @ApiModelProperty("席位单价")
    private Long seatMoney;

    @ApiModelProperty("席位状态: 0 - 待提交 1-审批中 2-通过 3-拒绝 4-废弃")
    private Byte seatStatus;

    @ApiModelProperty("通包总净价")
    private Long commonPackageNetPrice;

    @ApiModelProperty("通包总刊例价")
    private Long commonPackagePublishPrice;

    @ApiModelProperty("通包ID")
    private Long workOrderId;

    @ApiModelProperty("更新人")
    private String operator;

    @ApiModelProperty("席位id")
    private Long seatId;

    @ApiModelProperty("通包总净价")
    private Long commonPackageSumNetPrice;
}
