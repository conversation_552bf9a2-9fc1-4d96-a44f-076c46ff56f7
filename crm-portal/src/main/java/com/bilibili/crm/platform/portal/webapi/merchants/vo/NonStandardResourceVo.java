package com.bilibili.crm.platform.portal.webapi.merchants.vo;

import com.bilibili.crm.platform.portal.attachment.AttachmentInfoVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/21
 */
@Data
public class NonStandardResourceVo {

    @ApiModelProperty("资源大类")
    private String tabType;

    @ApiModelProperty("资源ID")
    private String resourceId;

    @ApiModelProperty("资源位置")
    private String resourceLocation;

    @ApiModelProperty("资源类型")
    private String resourceType;

    @ApiModelProperty("资源名称")
    private String resourceName;

    @ApiModelProperty("权益描述")
    private String rightsDesc;

    @ApiModelProperty("资源平台")
    private String resourcePlatform;

    @ApiModelProperty("单位")
    private String unit;

    @ApiModelProperty("刊例单价-低")
    private Long publishUnitPriceLow;

    @ApiModelProperty("刊例单价-高")
    private Long publishUnitPriceHigh;

    @ApiModelProperty("选定刊例价")
    private Long selectedPublishPrice;

    @ApiModelProperty("数据类型")
    private String dataType;

    @ApiModelProperty("数据预估")
    private Long dataEstimate;

    @ApiModelProperty("数量")
    private Integer num;

    @ApiModelProperty("资源时长")
    private Integer resourceDuration;

    @ApiModelProperty("成本")
    private Long cost;

    @ApiModelProperty("单位刊例价")
    private Long publishUnitPrice;

    @ApiModelProperty("单位净价")
    private Long unitNetPrice;

    @ApiModelProperty("刊例总价")
    private Long publishSumPrice;

    @ApiModelProperty("净价总价")
    private Long netSumPrice;

    @ApiModelProperty("demos")
    private List<AttachmentInfoVo> demo;

    @ApiModelProperty("单位时长")
    private Integer unitDuration;

    @ApiModelProperty("净价单价")
    private Long netUnitPrice;

    @ApiModelProperty("利润率")
    private Integer profitRate;

}
