
package com.bilibili.crm.platform.portal.webapi.achievement.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-07-08 17:37:29
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleEfficiencyBasicExportVo implements Serializable {
    private static final long serialVersionUID = 5393696028904511116L;

    @ExcelResources(title = "标签")
    private String biz_type;

    @ExcelResources(title = "日期")
    private String date;

    @ExcelResources(title = "直客/渠道组")
    private String second_level_team_name;

    @ExcelResources(title = "销售大组")
    private String third_level_team_name;

    @ExcelResources(title = "销售小组")
    private String fourth_level_team_name;

    @ExcelResources(title = "组织架构五级")
    private String five_level_team_name;

    @ExcelResources(title = "销售姓名")
    private String sale_name;

    @ExcelResources(title = "在职一线销售数")
    private Integer on_sale_amount;

    @ExcelResources(title = "人均产能（万元）")
    private BigDecimal person_amount;

    @ExcelResources(title = "私海集团数")
    private Integer private_sea_group_amount;

    @ExcelResources(title = "活跃集团数")
    private Integer alive_group_amount;

    @ExcelResources(title = "活跃集团ARPU（万元）")
    private BigDecimal person_alive_group_arpu;

    @ExcelResources(title = "私海集团活跃率(%)")
    private BigDecimal private_sea_group_alive_rate;

    @ExcelResources(title = "活跃集团拜访率(%)")
    private BigDecimal alive_group_visit_rate;

    @ExcelResources(title = "人均拜访次数60分档位线")
    private Integer person_visit_amount_line_60;

    @ExcelResources(title = "人均拜访次数100分档位线")
    private Integer person_visit_amount_line_100;

    @ExcelResources(title = "人均拜访次数60分档位线(QTD)")
    private BigDecimal person_visit_amount_line_60_QTD;

    @ExcelResources(title = "人均拜访次数100分档位线(QTD)")
    private BigDecimal person_visit_amount_line_100_QTD;

    @ExcelResources(title = "累计拜访次数")
    private Integer visit_amount;

    @ExcelResources(title = "人均拜访次数")
    private BigDecimal person_visit_amount;

    @ExcelResources(title = "线下拜访占比目标")
    private BigDecimal offline_visit_amount_target;

    @ExcelResources(title = "线下拜访占比(%)")
    private BigDecimal offline_visit_amount_rate;

    @ExcelResources(title = "线下拜访完成进度(%)")
    private BigDecimal offline_visit_progress;

    @ExcelResources(title = "累计拜访集团数")
    private Integer group_visit_amount;

    @ExcelResources(title = "人均拜访集团数")
    private BigDecimal person_group_visit_amount;

    @ExcelResources(title = "累计拜访活跃集团数")
    private Integer alive_group_visit_amount;

    @ExcelResources(title = "拜访集团活跃率(%)")
    private BigDecimal visit_group_alive_rate;

    @ExcelResources(title = "拜访集团投放金额")
    private BigDecimal visit_group_money;

    @ExcelResources(title = "拜访集团投放金额占比(%)")
    private BigDecimal visit_group_money_rate;

    @ExcelResources(title = "商机录入目标数")
    private Integer bsi_target_amount;

    @ExcelResources(title = "累计商机提报数")
    private Integer bsi_amount;

    @ExcelResources(title = "商机录入目标完成进度(%)")
    private BigDecimal bsi_amount_target_rate;

    @ExcelResources(title = "商机转化率目标(%)")
    private BigDecimal bsi_trans_target;

    @ExcelResources(title = "当季度成单商机数")
    private Integer bsi_complete_amount;

    @ExcelResources(title = "商机成单转化率(%)")
    private BigDecimal bsi_complete_rate;

    @ExcelResources(title = "私海客户数")
    private Integer my_sea_customer_count;

    @ExcelResources(title = "有效活跃客户数目标")
    private Integer active_customer_count_target;

    @ExcelResources(title = "有效活跃客户数")
    private Integer active_customer_count;

    @ExcelResources(title = "有效活跃客户数完成进度(%)")
    private BigDecimal active_customer_progress;

    @ExcelResources(title = "私海品牌数")
    private Integer my_sea_product_count;

    @ExcelResources(title = "有效活跃品牌数目标")
    private Integer active_product_count_target;

    @ExcelResources(title = "有效活跃品牌数")
    private Integer active_product_count;

    @ExcelResources(title = "有效活跃品牌数完成进度(%)")
    private BigDecimal active_product_progress;


    @ExcelResources(title = "有效新客数目标")
    private Integer new_customer_count_target;

    @ExcelResources(title = "有效新客数")
    private Integer new_customer_count;

    @ExcelResources(title = "有效新客数完成进度(%)")
    private BigDecimal new_customer_progress;

    @ExcelResources(title = "项目售卖目标")
    private Integer project_sales_target;

    @ExcelResources(title = "项目售卖数")
    private Integer project_sales_count;

    @ExcelResources(title = "项目售卖完成进度(%)")
    private BigDecimal project_sales_progress;

}

