package com.bilibili.crm.platform.portal.webapi.merchants.vo.resp;

import com.alibaba.excel.annotation.ExcelIgnoreUnannotated;
import com.alibaba.excel.annotation.ExcelProperty;
import com.bilibili.crm.platform.biz.util.excel.LocalDateStringConverter;
import com.bilibili.crm.platform.biz.util.excel.LocalDateTimeStringConverter;
import io.swagger.annotations.ApiModelProperty;
import lombok.Data;

import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2025/1/6
 */
@ExcelIgnoreUnannotated
@Data
public class ExportBsiProjectResp {

    @ExcelProperty(value = "项目id")
    private Integer id;

    @ExcelProperty(value = "项目名称")
    private String projectName;

    @ExcelProperty(value = "项目属性名称")
    private String projectPropertyName;

    @ExcelProperty(value = "项目类型名称")
    private String projectTypeName;

    @ExcelProperty(value = "一级类目名称")
    private String projectFirstCategoryName;

    @ExcelProperty(value = "二级类目名称")
    private String projectSecondCategoryName;

    @ApiModelProperty("常规/定制:1-常规, 2-定制")
    @ExcelProperty(value = "常规/定制")
    private String projectGeneralType;

    @ExcelProperty(value = "营销团队")
    private String merchantMarketingTeam;

    @ExcelProperty(value = "创作类型")
    private String projectCreateType;

    @ExcelProperty(value = "归属一级分区名称")
    private String projectFirstAreaName;

    @ExcelProperty(value = "归属二级分区名称")
    private String projectSecondAreaName;

    @ExcelProperty(value = "招商周期开始日期", converter = LocalDateTimeStringConverter.class)
    private LocalDateTime investmentBeginTime;

    @ExcelProperty(value = "招商周期结束日期", converter = LocalDateTimeStringConverter.class)
    private LocalDateTime investmentEndTime;

    @ExcelProperty(value = "执行周期开始日期", converter = LocalDateStringConverter.class)
    private LocalDate validBeginDay;

    @ExcelProperty(value = "执行周期结束日期", converter = LocalDateStringConverter.class)
    private LocalDate validEndDay;

    @ExcelProperty(value = "商业对接人")
    private String businessContact;

    @ExcelProperty(value = "主站对接人")
    private String mainContact;

    @ExcelProperty(value = "提报商机总数")
    private Integer bsiCount;

    @ExcelProperty(value = "提报商机总金额")
    private String bsiAmount;

    @ExcelProperty(value = "已创建合同的商机总数")
    private Integer contractBsiCount;

    @ExcelProperty(value = "已创建合同的商机总额")
    private String contractBsiAmount;

    @ExcelProperty(value = "已计收的商机总数")
    private Integer completedBsiCount;

    @ExcelProperty(value = "已计收的商机总金额")
    private String completedBsiAmount;
}
