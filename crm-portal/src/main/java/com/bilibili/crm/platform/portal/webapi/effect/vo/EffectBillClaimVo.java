package com.bilibili.crm.platform.portal.webapi.effect.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-12-23 14:45:38
 * @description:
 **/

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EffectBillClaimVo implements Serializable {

    @ApiModelProperty("收支流水表主键id")
    private Integer expenditure_flow_id;

    @ApiModelProperty("认领账单信息")
    private List<EffectBillClaimDetailVo> detail_vos;


}
