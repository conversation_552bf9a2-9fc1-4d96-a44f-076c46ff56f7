package com.bilibili.crm.platform.portal.webapi.persona.vo_new;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;
import java.util.List;

/**
 * <AUTHOR>
 * date 2024/12/19 19:25.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupCustomerPortraitListResultVO implements Serializable {

    private static final long serialVersionUID = -8075614816787444767L;

    @ApiModelProperty("总计")
    private BigDecimal totalKpiAmount;

    @ApiModelProperty("总计下面的明细行")
    private List<GroupCustomerPortraitListItemVO> itemList;

}

