package com.bilibili.crm.platform.portal.convertor;

import com.bilibili.crm.platform.api.finance.dto.BackRedPacketDto;
import com.bilibili.crm.platform.api.finance.dto.BackRedPacketQueryDto;
import com.bilibili.crm.platform.api.finance.dto.EmailScreenshotDto;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.common.AccountBackRedPacketPolicyType;
import com.bilibili.crm.platform.common.FinanceAuditStatus;
import com.bilibili.crm.platform.common.FinanceBackRedPacketType;
import com.bilibili.crm.platform.portal.helper.EmailScreenshotDtoConvertHelper;
import com.bilibili.crm.platform.portal.webapi.finance.vo.BackRedPacketDetailVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.BackRedPacketQueryVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.BackRedPacketVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.EmailScreenshotVo;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2022/1/20 下午5:02
 */
@Component
public class BackRedPacketVoConvertor {

    @Autowired
    private EmailScreenshotDtoConvertHelper emailScreenshotDtoConvertHelper;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    public BackRedPacketVo backRedPacketDto2Vo(BackRedPacketDto dto) {
        return BackRedPacketVo.builder()
                .id(dto.getId())
                .amount(dto.getAmount())
                .recharge_serial_number(String.valueOf(dto.getRechargeSerialNumber()))
                .serial_number(String.valueOf(dto.getSerialNumber()))
                .date(this.getTimestamp2String(dto.getCtime(), "yyyy-MM-dd HH:mm:ss"))
                .processing_time(this.getTimestamp2String(dto.getProcessingTime(), "yyyy-MM-dd HH:mm:ss"))
                .belong_date(dto.getBelongDate() == null ? null : getTimestamp2String(dto.getBelongDate(), "yyyy-MM"))
                .belong_date_type(dto.getBelongDateType())
                .belong_quarter(dto.getBelongDate() != null ? incomeTimeUtil.getQuarter(dto.getBelongDate()): "")
                .operator(dto.getOperator())
                .auditor(dto.getAuditor())
                .remark(dto.getRemark())
                .reason(dto.getReason())
                .back_type(dto.getBackType())
                .back_type_desc(FinanceBackRedPacketType.getByCode(dto.getBackType()).getDesc())
                .status(dto.getStatus())
                .status_desc(FinanceAuditStatus.getByCode(dto.getStatus()).getDesc())
                .emails(convertDtsToVos(dto.getEmails()))
                .back_policy_type(dto.getBackPolicyType())
                .back_policy_type_desc(dto.getBackPolicyType() != null ? AccountBackRedPacketPolicyType.getByCode(dto.getBackPolicyType()).getDesc() : null)
                .build();
    }

    public BackRedPacketDetailVo backRedPacketDto2DetailVo(BackRedPacketDto dto) {
        return BackRedPacketDetailVo.builder()
                .id(dto.getId())
                .amount(dto.getAmount())
                .recharge_serial_number(String.valueOf(dto.getRechargeSerialNumber()))
                .serial_number(String.valueOf(dto.getSerialNumber()))
                .date(this.getTimestamp2String(dto.getCtime(), "yyyy-MM-dd HH:mm:ss"))
                .processing_time(this.getTimestamp2String(dto.getProcessingTime(), "yyyy-MM-dd HH:mm:ss"))
                .belong_date(dto.getBelongDate() == null ? null : dto.getBelongDate().getTime())
                .operator(dto.getOperator())
                .auditor(dto.getAuditor())
                .remark(dto.getRemark())
                .reason(dto.getReason())
                .back_type(dto.getBackType())
                .back_type_desc(FinanceBackRedPacketType.getByCode(dto.getBackType()).getDesc())
                .status(dto.getStatus())
                .status_desc(FinanceAuditStatus.getByCode(dto.getStatus()).getDesc())
                .emails(convertDtsToVos(dto.getEmails()))
                .back_policy_type(dto.getBackPolicyType())
                .build();
    }

    public String getTimestamp2String(Timestamp timestamp, String format) {
        SimpleDateFormat sim = new SimpleDateFormat(format);
        return sim.format(timestamp);
    }

    public List<EmailScreenshotVo> convertDtsToVos(List<EmailScreenshotDto> list) {
        if (null == list || CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream()
                .map(emailScreenshotDtoConvertHelper::convertDtoToVo)
                .collect(Collectors.toList());
    }

    public List<EmailScreenshotDto> parseEmaiList(List<EmailScreenshotVo> list) {
        if (null == list || CollectionUtils.isEmpty(list)) {
            return Collections.emptyList();
        }

        return list.stream()
                .map(emailScreenshotDtoConvertHelper::convertVoToDto)
                .collect(Collectors.toList());
    }
}
