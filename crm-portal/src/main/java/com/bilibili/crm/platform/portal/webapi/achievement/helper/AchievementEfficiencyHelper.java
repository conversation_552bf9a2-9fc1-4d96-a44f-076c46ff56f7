package com.bilibili.crm.platform.portal.webapi.achievement.helper;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.biz.achievement.dto.SaleSnapshotDto;
import com.bilibili.crm.biz.achievement.service.AchievementEstimateBizService;
import com.bilibili.crm.biz_common.config.PdConfigConstants;
import com.bilibili.crm.biz_common.config.PdConfigService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.rbac.dto.DataStrategy;
import com.bilibili.crm.platform.api.sale.dto.CrmSaleKpiDto;
import com.bilibili.crm.platform.api.sale.dto.QuerySaleDto;
import com.bilibili.crm.platform.api.sale.dto.SaleBaseDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.app.cache.sale_efficiency.SaleEfficiencyBasicService;
import com.bilibili.crm.platform.app.cache.sale_efficiency.SaleEfficiencyService;
import com.bilibili.crm.platform.biz.elasticsearch.live_goods.po.SaleAchievementEfficiencyDayPO;
import com.bilibili.crm.platform.biz.elasticsearch.live_goods.po.SaleEfficiencyBasicPO;
import com.bilibili.crm.platform.biz.service.SaleGroupService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.DomainOrgType;
import com.bilibili.crm.platform.common.IsAble;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.SaleLevelEnum;
import com.bilibili.crm.platform.common.dashboard.DashBoardShowOrderEnum;
import com.bilibili.crm.platform.common.sale.SaleGroupEnum;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.bilibili.crm.platform.portal.webapi.achievement.vo.*;
import com.bilibili.crm.platform.utils.MathUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-03-05 14:44:17
 * @description:
 **/

@Slf4j
@Component
public class AchievementEfficiencyHelper {

    @Resource
    private SaleGroupService saleGroupService;


    @Autowired
    private ISaleService saleService;

    @Resource
    private SaleEfficiencyService saleEfficiencyService;

    @Resource
    private SaleEfficiencyBasicService saleEfficiencyBasicService;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;


    private static final Integer DIRECT_GROUP_ID = 46;
    private static final Integer CHANNEL_GROUP_ID = 85;
    private static final Integer CHANNEL_PM_GROUP_ID = 38;
    @Autowired
    private AchievementEstimateBizService achievementEstimateBizService;
    @Autowired
    private PdConfigService pdConfigService;

    public <T> List<T> queryList(Operator operator, SaleEfficiencyQueryVo queryVo, T t) {
        Integer isForBasic;
        if (t.getClass().equals(SaleAchievementEfficiencyDayPO.class)) {
            isForBasic = 0;
        } else {
            isForBasic = 1;
        }
        List<T> poList = new ArrayList<>();
        Timestamp date = new Timestamp(queryVo.getDate());
        String logDateStr = CrmUtils.formatDateCanNull(date, CrmUtils.YYYYMMDD_WITHOUT_SPLIT);
        Timestamp quarterFirstDate = CrmUtils.getQuarterBeginDate(date);
        Timestamp quarterEndDate = Utils.getEndOfDay(CrmUtils.getQuarterEndDate(date));
        List<Integer> thisSaleIdsByGroup = new ArrayList<>();
        List<Integer> thisGroupIdList = new ArrayList<>();
        SaleSnapshotDto thisSale = achievementEstimateBizService.getSaleBaseInfoByLogDate(operator.getOperatorName(), logDateStr);
        AlarmHelper.log("QueryList", thisSale, queryVo);
        if (Objects.nonNull(thisSale) && Objects.equals(thisSale.getId(), -1)) {
            return Collections.emptyList();
        }
        //需要判断销售是直客组还是渠道组
        if (Objects.nonNull(queryVo.getDirect_channel_type()) && Objects.nonNull(thisSale)) {
            List<Integer> directChildIds = achievementEstimateBizService.getAllChildGroupIdListByParentIdListNotWithSelfByLogDate(Collections.singletonList(DIRECT_GROUP_ID), logDateStr);
            directChildIds.add(DIRECT_GROUP_ID);
            List<Integer> channelChildIds = achievementEstimateBizService.getAllChildGroupIdListByParentIdListNotWithSelfByLogDate(Collections.singletonList(CHANNEL_GROUP_ID), logDateStr);
            channelChildIds.add(CHANNEL_GROUP_ID);

            Set<Integer> saleTypes = new HashSet<>();
            if (CollectionUtils.isNotEmpty(thisSale.getGroupId())) {
                for (Integer groupId : thisSale.getGroupId()) {
                    if (directChildIds.contains(groupId)) {
                        saleTypes.add(1);
                    }
                    if (channelChildIds.contains(groupId)) {
                        saleTypes.add(2);
                    }
                }
                AlarmHelper.log("QueryList", thisSale.getGroupId(), saleTypes, queryVo);
                if (!saleTypes.contains(queryVo.getDirect_channel_type())) {
                    return Collections.emptyList();
                }
            }
        }
        if (Objects.nonNull(queryVo.getSale_group_id())) {
            thisGroupIdList = achievementEstimateBizService.getNextChildGroupListByParentIdListByLogDate(Collections.singletonList(queryVo.getSale_group_id()),logDateStr)
                    .stream().filter(e -> !Objects.equals(e, CHANNEL_PM_GROUP_ID)).collect(Collectors.toList());;
            thisSaleIdsByGroup = achievementEstimateBizService.getAllSaleIdsByGroupIdWithLeaderValidQuarterQuitByLogDate(queryVo.getSale_group_id(), quarterFirstDate, quarterEndDate, logDateStr);
        }
        //普通销售权限只能看你自己 和 本层级所有销售及销售组 销售组长除外
        if (Objects.nonNull(thisSale) && !SaleLevelEnum.isLeader(thisSale.getLevel())) {
            log.info("AchievementEfficiencyHelper.queryList_1,operator={}", operator.getOperatorName());
            if (Objects.nonNull(queryVo.getSale_group_id())) {
                poList = queryByType(new Timestamp(queryVo.getDate()), thisSaleIdsByGroup, thisGroupIdList, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
            } else {
                List<Integer> groupIds = achievementEstimateBizService.getNextChildGroupListByParentIdListByLogDate(thisSale.getGroupId(), logDateStr);
                if (!CollectionUtils.isEmpty(groupIds)) {
                    poList = queryByType(new Timestamp(queryVo.getDate()), Lists.newArrayList(thisSale.getId()), groupIds, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
                } else {
                    poList = queryByType(new Timestamp(queryVo.getDate()), Lists.newArrayList(thisSale.getId()), null, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
                }
            }
        }
        //销售leader权限 第一次进来看自己所在的小组
        if (Objects.nonNull(thisSale) && SaleLevelEnum.isLeader(thisSale.getLevel()) && Objects.isNull(queryVo.getSale_group_id())) {
            log.info("AchievementEfficiencyHelper.queryList_2,operator={}", operator.getOperatorName());
            Map<String, List<Integer>> haveAuthMap = achievementEstimateBizService.queryAllHaveAuthSonSaleIdsBySaleIdWithLogDate(thisSale.getId(), logDateStr);
            List<Integer> haveAuthSaleGroupIds = haveAuthMap.getOrDefault("sonSaleGroupList",Collections.emptyList());
            List<Integer> childIds = achievementEstimateBizService.getAllChildGroupIdListByParentIdListNotWithSelfByLogDate(haveAuthSaleGroupIds, logDateStr);
            List<Integer> haveSaleGroupIds = haveAuthSaleGroupIds.stream().filter(e -> !childIds.contains(e)).collect(Collectors.toList());
            poList = queryByType(new Timestamp(queryVo.getDate()), null, haveSaleGroupIds, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
        }
        //销售leader权限 带着小组id进来 该小组不是最后一个层级
        if (Objects.nonNull(thisSale) && SaleLevelEnum.isLeader(thisSale.getLevel()) && Objects.nonNull(queryVo.getSale_group_id()) && CollectionUtils.isNotEmpty(thisGroupIdList)) {
            log.info("AchievementEfficiencyHelper.queryList_3,operator={}", operator.getOperatorName());
            poList = queryByType(new Timestamp(queryVo.getDate()), thisSaleIdsByGroup, thisGroupIdList, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
        }
        //销售leader权限 带着小组id进来 该小组是最后一个层级
        if (Objects.nonNull(thisSale) && SaleLevelEnum.isLeader(thisSale.getLevel()) && Objects.nonNull(queryVo.getSale_group_id()) && CollectionUtils.isEmpty(thisGroupIdList)) {
            log.info("AchievementEfficiencyHelper.queryList_4,operator={}", operator.getOperatorName());
            List<Integer> childSaleIdList = thisSaleIdsByGroup;
            if (CollectionUtils.isNotEmpty(childSaleIdList)) {
                poList = queryByType(new Timestamp(queryVo.getDate()), childSaleIdList, null, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
            }
        }
        //非销售权限 第一次进来看渠道组,直客组
        if (Objects.isNull(thisSale) && Objects.isNull(queryVo.getSale_group_id()) && Objects.isNull(queryVo.getDirect_channel_type())) {
            log.info("AchievementEfficiencyHelper.queryList_5,operator={}", operator.getOperatorName());
            poList = queryByType(new Timestamp(queryVo.getDate()), null, Lists.newArrayList(DIRECT_GROUP_ID, CHANNEL_GROUP_ID), Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
        }
        //非销售权限 第一次进来仅看直客组
        if (Objects.isNull(thisSale) && Objects.isNull(queryVo.getSale_group_id()) && Objects.equals(queryVo.getDirect_channel_type(), 1)) {
            log.info("AchievementEfficiencyHelper.queryList_6,operator={}", operator.getOperatorName());
            poList = queryByType(new Timestamp(queryVo.getDate()), null,
                    Lists.newArrayList(DIRECT_GROUP_ID),
                    Lists.newArrayList(queryVo.getType().getCode()),
                    Lists.newArrayList(2, 3),
                    isForBasic);
        }
        //非销售权限 第一次进来仅看渠道组
        if (Objects.isNull(thisSale) && Objects.isNull(queryVo.getSale_group_id()) && Objects.equals(queryVo.getDirect_channel_type(), 2)) {
            log.info("AchievementEfficiencyHelper.queryList_7,operator={}", operator.getOperatorName());
            poList = queryByType(new Timestamp(queryVo.getDate()), null, Lists.newArrayList(CHANNEL_GROUP_ID), Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
        }
        //非销售权限 带着小组id进来 该小组不是最后一个层级
        if (Objects.isNull(thisSale) && Objects.nonNull(queryVo.getSale_group_id()) && org.apache.commons.collections.CollectionUtils.isNotEmpty(thisGroupIdList)) {
            log.info("AchievementEfficiencyHelper.queryList_8,operator={}", operator.getOperatorName());
            if (DashBoardShowOrderEnum.notSeeSaleGroupIdList.contains(queryVo.getSale_group_id()) && queryVo.getExport_type() == null) {
                thisSaleIdsByGroup = new ArrayList<>();
            }
            poList = queryByType(new Timestamp(queryVo.getDate()), thisSaleIdsByGroup, thisGroupIdList, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
        }
        //非销售权限 带着小组id进来 该小组是最后一个层级
        if (Objects.isNull(thisSale) && Objects.nonNull(queryVo.getSale_group_id()) && org.apache.commons.collections.CollectionUtils.isEmpty(thisGroupIdList)) {
            log.info("AchievementEfficiencyHelper.queryList_9,operator={}", operator.getOperatorName());
            List<Integer> childSaleIdList = thisSaleIdsByGroup;
            if (CollectionUtils.isNotEmpty(childSaleIdList)) {
                poList = queryByType(new Timestamp(queryVo.getDate()), childSaleIdList, null, Lists.newArrayList(queryVo.getType().getCode()), Lists.newArrayList(2, 3), isForBasic);
            }
        }
        return poList;
    }

    public List<SaleEfficiencyVo> poListToExportVo(List<SaleAchievementEfficiencyDayPO> poList, SaleEfficiencyQueryVo queryVO, Operator operator, DataStrategy dataStrategy) {
        List<SaleEfficiencyVo> result = new ArrayList<>();
        List<SaleEfficiencyVo> voList = poListToVo(poList, queryVO, operator, dataStrategy);
        List<Integer> saleIds = poList.stream().filter(a -> a.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())).map(SaleAchievementEfficiencyDayPO::getBiz_id).distinct().filter(a -> a > 0).collect(Collectors.toList());
        List<Integer> saleGroupIds = poList.stream().filter(a -> a.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())).map(SaleAchievementEfficiencyDayPO::getBiz_id).distinct().filter(a -> a > 0).collect(Collectors.toList());
        List<SaleGroupDto> saleGroupDtoList = saleGroupService.getSaleGroupsInIds(saleGroupIds);
        List<SaleDto> saleDtoList = saleService.getSalesInIds(saleIds);
        Map<Integer, SaleGroupDto> saleGroupDtoMap = saleGroupDtoList.stream().collect(Collectors.toMap(SaleGroupDto::getId, Function.identity()));
        Map<Integer, SaleDto> saleDtoMap = saleDtoList.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));

        for (SaleEfficiencyVo efficiencyVo : voList) {
            if (efficiencyVo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())) {
                SaleGroupDto saleGroupDto = saleGroupDtoMap.getOrDefault(efficiencyVo.getBiz_id(), SaleGroupDto.builder().parentGroups(new ArrayList<>()).build());
                if (saleGroupDto.getParentGroups().size() >= 1) {
                    efficiencyVo.setFirst_level_team_name(saleGroupDto.getParentGroups().get(0));
                }
                if (saleGroupDto.getParentGroups().size() >= 2) {
                    efficiencyVo.setSecond_level_team_name(saleGroupDto.getParentGroups().get(1));
                }
                if (saleGroupDto.getParentGroups().size() >= 3) {
                    efficiencyVo.setThird_level_team_name(saleGroupDto.getParentGroups().get(2));
                }
                if (saleGroupDto.getParentGroups().size() >= 4) {
                    efficiencyVo.setFourth_level_team_name(saleGroupDto.getParentGroups().get(3));
                }
                if (saleGroupDto.getParentGroups().size() >= 5) {
                    efficiencyVo.setFive_level_team_name(saleGroupDto.getParentGroups().get(4));
                }
            }
            if (efficiencyVo.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())) {
                SaleDto saleDto = saleDtoMap.getOrDefault(efficiencyVo.getBiz_id(), SaleDto.builder().parentGroups(new ArrayList<>()).build());
                if (saleDto.getParentGroups().size() >= 1) {
                    efficiencyVo.setFirst_level_team_name(saleDto.getParentGroups().get(0));
                }
                if (saleDto.getParentGroups().size() >= 2) {
                    efficiencyVo.setSecond_level_team_name(saleDto.getParentGroups().get(1));
                }
                if (saleDto.getParentGroups().size() >= 3) {
                    efficiencyVo.setThird_level_team_name(saleDto.getParentGroups().get(2));
                }
                if (saleDto.getParentGroups().size() >= 4) {
                    efficiencyVo.setFourth_level_team_name(saleDto.getParentGroups().get(3));
                }
                if (saleDto.getParentGroups().size() >= 5) {
                    efficiencyVo.setFive_level_team_name(saleDto.getParentGroups().get(4));
                }
                efficiencyVo.setSale_name(efficiencyVo.getBiz_name());
            }
            result.add(efficiencyVo);
            if (efficiencyVo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId()) && queryVO.getExport_type().equals(2)) {
                SaleEfficiencyQueryVo queryVOCopy = new SaleEfficiencyQueryVo();
                BeanUtils.copyProperties(queryVO, queryVOCopy);
                queryVOCopy.setSale_group_id(efficiencyVo.getBiz_id());
                List<SaleAchievementEfficiencyDayPO> nextDayPos = queryList(operator, queryVOCopy, new SaleAchievementEfficiencyDayPO());
                result.addAll(poListToExportVo(nextDayPos, queryVOCopy, operator, dataStrategy));
            }
        }
        return result;
    }


    public List<SaleEfficiencyChannelExportVo> poListToExportChannelVo(List<SaleEfficiencyVo> exportVos) {
        if (CollectionUtils.isEmpty(exportVos)) {
            return new ArrayList<>();
        }
        List<SaleEfficiencyChannelExportVo> result = new ArrayList<>();
        for (SaleEfficiencyVo exportVo : exportVos) {
            SaleEfficiencyChannelExportVo newVo = new SaleEfficiencyChannelExportVo();
            BeanUtils.copyProperties(exportVo, newVo);
            newVo.setBiz_type(SaleKpiTypeEnum.getByName(exportVo.getBiz_type()).getDesc());
            result.add(newVo);
        }
        return result;
    }

    public List<SaleEfficiencyBasicExportVo> basicPoListToExportVo(List<SaleEfficiencyBasicPO> basicPos, Operator operator, SaleEfficiencyQueryVo queryVO, DataStrategy dataStrategy) {
        if (CollectionUtils.isEmpty(basicPos)) {
            return new ArrayList<>();
        }
        List<SaleEfficiencyBasicVo> exportVos = basicPoListToVo(basicPos, queryVO, operator, dataStrategy);
        List<SaleEfficiencyBasicExportVo> result = new ArrayList<>();
        List<Integer> saleIds = exportVos.stream().filter(a -> a.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())).map(SaleEfficiencyBasicVo::getBiz_id).distinct().filter(a -> a > 0).collect(Collectors.toList());
        List<Integer> saleGroupIds = exportVos.stream().filter(a -> a.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())).map(SaleEfficiencyBasicVo::getBiz_id).distinct().filter(a -> a > 0).collect(Collectors.toList());
        List<SaleGroupDto> saleGroupDtoList = saleGroupService.getSaleGroupsInIds(saleGroupIds);
        List<SaleDto> saleDtoList = saleService.getSalesInIds(saleIds);
        Map<Integer, SaleGroupDto> saleGroupDtoMap = saleGroupDtoList.stream().collect(Collectors.toMap(SaleGroupDto::getId, Function.identity()));
        Map<Integer, SaleDto> saleDtoMap = saleDtoList.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));

        for (SaleEfficiencyBasicVo efficiencyVo : exportVos) {
            if (efficiencyVo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())) {
                SaleGroupDto saleGroupDto = saleGroupDtoMap.getOrDefault(efficiencyVo.getBiz_id(), SaleGroupDto.builder().parentGroups(new ArrayList<>()).build());
                if (saleGroupDto.getParentGroups().size() >= 1) {
                    efficiencyVo.setFirst_level_team_name(saleGroupDto.getParentGroups().get(0));
                }
                if (saleGroupDto.getParentGroups().size() >= 2) {
                    efficiencyVo.setSecond_level_team_name(saleGroupDto.getParentGroups().get(1));
                }
                if (saleGroupDto.getParentGroups().size() >= 3) {
                    efficiencyVo.setThird_level_team_name(saleGroupDto.getParentGroups().get(2));
                }
                if (saleGroupDto.getParentGroups().size() >= 4) {
                    efficiencyVo.setFourth_level_team_name(saleGroupDto.getParentGroups().get(3));
                }
                if (saleGroupDto.getParentGroups().size() >= 5) {
                    efficiencyVo.setFive_level_team_name(saleGroupDto.getParentGroups().get(4));
                }
            }
            if (efficiencyVo.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())) {
                SaleDto saleDto = saleDtoMap.getOrDefault(efficiencyVo.getBiz_id(), SaleDto.builder().parentGroups(new ArrayList<>()).build());
                if (saleDto.getParentGroups().size() >= 1) {
                    efficiencyVo.setFirst_level_team_name(saleDto.getParentGroups().get(0));
                }
                if (saleDto.getParentGroups().size() >= 2) {
                    efficiencyVo.setSecond_level_team_name(saleDto.getParentGroups().get(1));
                }
                if (saleDto.getParentGroups().size() >= 3) {
                    efficiencyVo.setThird_level_team_name(saleDto.getParentGroups().get(2));
                }
                if (saleDto.getParentGroups().size() >= 4) {
                    efficiencyVo.setFourth_level_team_name(saleDto.getParentGroups().get(3));
                }
                if (saleDto.getParentGroups().size() >= 5) {
                    efficiencyVo.setFive_level_team_name(saleDto.getParentGroups().get(4));
                }
                efficiencyVo.setSale_name(efficiencyVo.getBiz_name());
            }
            SaleEfficiencyBasicExportVo exportVo = new SaleEfficiencyBasicExportVo();
            exportVo.setBiz_type(SaleKpiTypeEnum.getByName(efficiencyVo.getBiz_type()).getDesc());
            BeanUtils.copyProperties(efficiencyVo, exportVo);
            result.add(exportVo);
            if (efficiencyVo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())) {
                SaleEfficiencyQueryVo queryVOCopy = new SaleEfficiencyQueryVo();
                BeanUtils.copyProperties(queryVO, queryVOCopy);
                queryVOCopy.setSale_group_id(efficiencyVo.getBiz_id());
                try {
                    Thread.sleep(1500);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
                List<SaleEfficiencyBasicPO> nextDayPos = queryList(operator, queryVOCopy, new SaleEfficiencyBasicPO());
                result.addAll(basicPoListToExportVo(nextDayPos, operator, queryVOCopy, dataStrategy));
            }
        }
        return result;
    }

    public List<SaleEfficiencyDirectExportVo> poListToExportDirectVo(List<SaleEfficiencyVo> exportVos) {
        if (CollectionUtils.isEmpty(exportVos)) {
            return new ArrayList<>();
        }
        List<SaleEfficiencyDirectExportVo> result = new ArrayList<>();
        for (SaleEfficiencyVo exportVo : exportVos) {
            SaleEfficiencyDirectExportVo newVo = new SaleEfficiencyDirectExportVo();
            BeanUtils.copyProperties(exportVo, newVo);
            newVo.setBiz_type(SaleKpiTypeEnum.getByName(exportVo.getBiz_type()).getDesc());
            result.add(newVo);
        }
        return result;
    }


    public List<SaleEfficiencyExportChannelNewVo> poListToExportChannelVoNew(List<SaleEfficiencyVo> exportVos, SaleEfficiencyQueryVo queryVo) {
        if (CollectionUtils.isEmpty(exportVos)) {
            return new ArrayList<>();
        }
        List<SaleEfficiencyExportChannelNewVo> result = new ArrayList<>();
        for (SaleEfficiencyVo exportVo : exportVos) {
            SaleEfficiencyExportChannelNewVo newVo = new SaleEfficiencyExportChannelNewVo();
            BeanUtils.copyProperties(exportVo, newVo);
            newVo.setDate(incomeTimeUtil.getQuarterDescNow(new Timestamp(queryVo.getDate())));
            result.add(newVo);
        }
        return result;
    }

    public List<SaleEfficiencyExportDirectNewVo> poListToExportDirectVoNew(List<SaleEfficiencyVo> exportVos, SaleEfficiencyQueryVo queryVo) {
        if (CollectionUtils.isEmpty(exportVos)) {
            return new ArrayList<>();
        }
        List<SaleEfficiencyExportDirectNewVo> result = new ArrayList<>();
        for (SaleEfficiencyVo exportVo : exportVos) {
            SaleEfficiencyExportDirectNewVo newVo = new SaleEfficiencyExportDirectNewVo();
            BeanUtils.copyProperties(exportVo, newVo);
            newVo.setDate(incomeTimeUtil.getQuarterDescNow(new Timestamp(queryVo.getDate())));
            result.add(newVo);
        }
        return result;
    }


    public List<SaleEfficiencyVo> poListToVo(List<SaleAchievementEfficiencyDayPO> poList, SaleEfficiencyQueryVo queryVO, Operator operator, DataStrategy dataStrategy) {
        List<SaleEfficiencyVo> resultVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(poList)) {
            return resultVoList;
        }
        String quarterDesc = incomeTimeUtil.getQuarterDescNow(new Timestamp(queryVO.getDate()));
        List<Integer> bizIds = poList.stream().filter(Objects::nonNull).map(SaleAchievementEfficiencyDayPO::getBiz_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CrmSaleKpiDto> saleKpiDtos = saleService.queryAllKpiByQuarterAndBizId(quarterDesc, bizIds);
        List<Integer> saleIds = poList.stream().filter(Objects::nonNull).filter(a -> a.getBiz_type() != null && a.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())).map(SaleAchievementEfficiencyDayPO::getBiz_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<SaleDto> saleDtos = saleService.getSalesInIds(saleIds);
        Map<Integer, SaleDto> saleDtoMap = saleDtos.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));
        List<Integer> groupSeq = pdConfigService.getIntList(PdConfigConstants.YJJK_LIST_QUERY_GROUP_SORT_SEQ, queryVO.getDate());
        List<SaleEfficiencyVo> resultVOS = poList.stream().filter(Objects::nonNull).map(e -> {
            if (e.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())) {
                saleEfficiencyService.buildPOTaskAmount(e, saleKpiDtos, SaleKpiTypeEnum.SALE, e.getBiz_id());
            } else if (e.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId()) || e.getBiz_type().equals(SaleKpiTypeEnum.BILIBILI.getBizId())) {
                saleEfficiencyService.buildPOTaskAmount(e, saleKpiDtos, SaleKpiTypeEnum.SALE_TEAM, e.getBiz_id());
            }
            SaleEfficiencyVo vo = poToVo(e);
            vo.setIs_leader(isLeader(e, saleDtoMap));
            // 直客部分小组需要展开到下层
            if (queryVO.getDirect_channel_type() != null && vo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId()) && Lists.newArrayList(SaleGroupEnum.DIRECT.getBizId(), SaleGroupEnum.CHANNEL.getBizId(),
                    SaleGroupEnum.E_FOOD.getBizId(), SaleGroupEnum.NET_EDU_E.getBizId(), SaleGroupEnum.E_HOUSE.getBizId(), SaleGroupEnum.MAKE_UP.getBizId()).contains(vo.getBiz_id()) && dataStrategy.getOrgType().equals(DomainOrgType.ORDINARY)) {
                SaleEfficiencyQueryVo queryCopyVo = new SaleEfficiencyQueryVo();
                BeanUtils.copyProperties(queryVO, queryCopyVo);
                queryCopyVo.setSale_group_id(vo.getBiz_id());
                if (SaleGroupEnum.DIRECT.getBizId().equals(vo.getBiz_type())) {
                    queryCopyVo.setDirect_channel_type(1);
                } else {
                    queryCopyVo.setDirect_channel_type(null);
                }
                try {
                    Thread.sleep(400);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
                vo.setNext_level(poListToVo(queryList(operator, queryCopyVo, new SaleAchievementEfficiencyDayPO()), queryVO, operator, dataStrategy));
            }
            return vo;
        }).sorted((obj1, obj2) -> {
            // 定义type的排序顺序
            List<Integer> typeOrder = Lists.newArrayList(
                    SaleKpiTypeEnum.SALE.getBizId(),
                    SaleKpiTypeEnum.SALE_TEAM.getBizId()
            );
            int index1 = typeOrder.indexOf(obj1.getBiz_type());
            int index2 = typeOrder.indexOf(obj2.getBiz_type());
            int typeNumber = Integer.compare(index1, index2);
            if (typeNumber != 0) {
                return typeNumber;
            }
            if (obj1.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId()) && obj2.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())) {
                // 销售组长
                int indexSale = Optional.ofNullable(obj2.getIs_leader()).orElse(0) - Optional.ofNullable(obj1.getIs_leader()).orElse(0);
                if (indexSale != 0) {
                    return indexSale;
                }
                // 总任务排名
                indexSale = Optional.ofNullable(obj2.getTotal_task_amount()).orElse(BigDecimal.ZERO).intValue() - Optional.ofNullable(obj1.getTotal_task_amount()).orElse(BigDecimal.ZERO).intValue();
                if (indexSale != 0) {
                    return indexSale;
                }
            }
            // 销售小组默认排名
            if (obj1.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId()) && obj2.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())) {
                int indexGroup1 = groupSeq.indexOf(obj1.getBiz_id());
                int indexGroup2 = groupSeq.indexOf(obj2.getBiz_id());
                if (indexGroup1 < 0) {
                    indexGroup1 = Integer.MAX_VALUE;
                }
                if (indexGroup2 < 0) {
                    indexGroup2 = Integer.MAX_VALUE;
                }
                int typeNumberGroup = Integer.compare(indexGroup1, indexGroup2);
                if (typeNumberGroup != 0) {
                    return typeNumberGroup;
                }
            }
            return Integer.compare(obj1.getBiz_id(), obj2.getBiz_id());
        }).collect(Collectors.toList());
        return resultVOS;
    }


    public SaleEfficiencyVo poToVo(SaleAchievementEfficiencyDayPO po) {
        if (Objects.isNull(po)) {
            po = saleEfficiencyService.buildDefault();
        }
        SaleEfficiencyVo vo = new SaleEfficiencyVo();
        vo.setBiz_id(po.getBiz_id());
        vo.setBiz_name(po.getBiz_name());
        vo.setBiz_type(po.getBiz_type());
        vo.setDate(CrmUtils.formatDate(new Timestamp(po.getAgg_time())));
        vo.setQuarter_complete(CrmUtils.fromFenToWan(po.getTotal_complete_amount()));
        vo.setTotal_task_amount(po.getTotal_task_amount() == null ? null : CrmUtils.fromFenToWan(po.getTotal_task_amount()));
        vo.setQuarter_complete_link(MathUtils.genRate(Optional.ofNullable(po.getTotal_complete_amount()).orElse(0L) - Optional.ofNullable(po.getTotal_complete_link_amount()).orElse(0L), Optional.ofNullable(po.getTotal_complete_link_amount()).orElse(0L)));
        vo.setComplete_rate(Objects.isNull(po.getTotal_task_amount()) ? null : MathUtils.calculatePercentage(po.getTotal_complete_amount(), po.getTotal_task_amount()));
        vo.setGroup_amount(po.getNew_group_amount());
        vo.setGroup_amount_link_amount(Optional.ofNullable(po.getNew_group_amount()).orElse(0) - Optional.ofNullable(po.getNew_group_link_amount()).orElse(0));
        vo.setGroup_amount_link_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getNew_group_amount()).orElse(0) - Optional.ofNullable(po.getNew_group_link_amount()).orElse(0), Optional.ofNullable(po.getNew_group_link_amount()).orElse(0)));
        vo.setOn_sale_amount(po.getOn_sale_amount() <= 1 ? null : po.getOn_sale_amount());
        vo.setOn_sale_amount_link_amount(Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0) <= 1 ? null : Optional.ofNullable(po.getOn_sale_amount()).orElse(0) - Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0));
        vo.setOn_sale_amount_link_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getOn_sale_amount()).orElse(0) - Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0), Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0)));
        vo.setPrivate_sea_group_amount(po.getPrivate_sea_group_amount());
        vo.setPrivate_sea_group_link_amount(Optional.ofNullable(po.getPrivate_sea_group_amount()).orElse(0) - Optional.ofNullable(po.getPrivate_sea_group_link_amount()).orElse(0));
        vo.setPrivate_sea_group_link_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getPrivate_sea_group_amount()).orElse(0) - Optional.ofNullable(po.getPrivate_sea_group_link_amount()).orElse(0), Optional.ofNullable(po.getPrivate_sea_group_link_amount()).orElse(0)));
        vo.setPrivate_sea_agent_amount(po.getPrivate_sea_agent_amount());
        vo.setPrivate_sea_agent_link_amount(Optional.ofNullable(po.getPrivate_sea_agent_amount()).orElse(0) - Optional.ofNullable(po.getPrivate_sea_agent_link_amount()).orElse(0));
        vo.setPrivate_sea_agent_link_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getPrivate_sea_agent_amount()).orElse(0) - Optional.ofNullable(po.getPrivate_sea_agent_link_amount()).orElse(0), Optional.ofNullable(po.getPrivate_sea_agent_link_amount()).orElse(0)));
        vo.setPrivate_sea_group_alive_rate(MathUtils.calculatePercentage(po.getPrivate_sea_group_alive_amount(), po.getPrivate_sea_group_amount()));
        vo.setPrivate_sea_agent_alive_rate(MathUtils.calculatePercentage(po.getPrivate_sea_agent_alive_amount(), po.getPrivate_sea_agent_amount()));
        vo.setPerson_group_arpu(CrmUtils.fromFenToWan(po.getGroup_average_arpu()));
        vo.setPerson_group_arpu_link_rate(MathUtils.calculatePercentageNegative(Optional.ofNullable(po.getGroup_average_arpu()).orElse(0L) - Optional.ofNullable(po.getGroup_average_arpu_link()).orElse(0L), Optional.ofNullable(po.getGroup_average_arpu_link()).orElse(0L)));
        vo.setAlive_person_group_arpu(CrmUtils.fromFenToWan(po.getAlive_group_average_arpu()));
        vo.setAlive_person_group_arpu_link_rate(MathUtils.calculatePercentageNegative(Optional.ofNullable(po.getAlive_group_average_arpu()).orElse(0L) - Optional.ofNullable(po.getAlive_group_average_arpu_link()).orElse(0L), Optional.ofNullable(po.getAlive_group_average_arpu_link()).orElse(0L)));
        vo.setPerson_agent_arpu(CrmUtils.fromFenToWan(po.getAgent_average_arpu()));
        vo.setPerson_agent_arpu_link_rate(MathUtils.calculatePercentageNegative(Optional.ofNullable(po.getAgent_average_arpu()).orElse(0L) - Optional.ofNullable(po.getAgent_average_arpu_link()).orElse(0L), Optional.ofNullable(po.getAgent_average_arpu_link()).orElse(0L)));
        vo.setAlive_person_agent_arpu(CrmUtils.fromFenToWan(po.getAlive_agent_average_arpu()));
        vo.setAlive_person_agent_arpu_link_rate(MathUtils.calculatePercentageNegative(Optional.ofNullable(po.getAlive_agent_average_arpu()).orElse(0L) - Optional.ofNullable(po.getAlive_agent_average_arpu_link()).orElse(0L), Optional.ofNullable(po.getAlive_agent_average_arpu_link()).orElse(0L)));
        BigDecimal person_visit_amount = MathUtils.genRate(Optional.ofNullable(po.getAll_follow_record_amount()).orElse(0), po.getOn_sale_amount());
        vo.setPerson_visit_amount(person_visit_amount.setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_visit_link_amount = MathUtils.genRate(Optional.ofNullable(po.getAll_follow_record_amount_link()).orElse(0), po.getOn_sale_link_amount());
        vo.setPerson_visit_amount_link_amount(person_visit_amount.subtract(person_visit_link_amount).setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_group_visit_amount = MathUtils.genRate(Optional.ofNullable(po.getVisit_group_amount()).orElse(0), po.getOn_sale_amount());
        vo.setPerson_group_visit_amount(person_group_visit_amount.setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_group_visit_link_amount = MathUtils.genRate(Optional.ofNullable(po.getVisit_group_link_amount()).orElse(0), po.getOn_sale_link_amount());
        vo.setPerson_group_visit_link_amount(person_group_visit_amount.subtract(person_group_visit_link_amount).setScale(2, RoundingMode.HALF_UP));
        vo.setPerson_group_visit_link_rate(MathUtils.calculatePercentage(person_group_visit_amount.subtract(person_group_visit_link_amount), person_group_visit_link_amount));
        BigDecimal person_agent_visit_amount = MathUtils.genRate(Optional.ofNullable(po.getVisit_agent_amount()).orElse(0), po.getOn_sale_amount());
        vo.setPerson_agent_visit_amount(person_agent_visit_amount.setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_agent_visit_link_amount = MathUtils.genRate(Optional.ofNullable(po.getVisit_agent_link_amount()).orElse(0), po.getOn_sale_link_amount());
        vo.setPerson_agent_visit_link_amount(person_agent_visit_amount.subtract(person_agent_visit_link_amount).setScale(2, RoundingMode.HALF_UP));
        vo.setPerson_agent_visit_link_rate(MathUtils.calculatePercentage(person_agent_visit_amount.subtract(person_agent_visit_link_amount), person_agent_visit_link_amount));
        vo.setGroup_visit_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getVisit_group_amount()).orElse(0), Optional.ofNullable(po.getPrivate_sea_group_amount()).orElse(0) + Optional.ofNullable(po.getGroup_new_customer_amount()).orElse(0)));
        vo.setAgent_visit_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getVisit_agent_amount()).orElse(0), Optional.ofNullable(po.getPrivate_sea_agent_amount()).orElse(0) + Optional.ofNullable(po.getAgent_new_customer_amount()).orElse(0)));
        vo.setLbs_locate_rate(MathUtils.calculatePercentage(Optional.ofNullable(po.getLbs_follow_record_amount()).orElse(0), Optional.ofNullable(po.getAll_follow_record_amount()).orElse(0)));
        BigDecimal person_bsi_amount = MathUtils.genRate(Optional.ofNullable(po.getBsi_amount()).orElse(0), Optional.ofNullable(po.getOn_sale_amount()).orElse(0));
        vo.setPerson_bsi_amount(person_bsi_amount.setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_bsi_link_amount = MathUtils.genRate(Optional.ofNullable(po.getBsi_amount_link()).orElse(0), Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0));
        vo.setPerson_bsi_amount_link_amount(person_bsi_amount.subtract(person_bsi_link_amount).setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_bsi_group_amount = MathUtils.genRate(Optional.ofNullable(po.getBsi_group_amount()).orElse(0), Optional.ofNullable(po.getOn_sale_amount()).orElse(0));
        vo.setPerson_bsi_group_amount(person_bsi_group_amount.setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_bsi_group_link_amount = MathUtils.genRate(Optional.ofNullable(po.getBsi_group_link_amount()).orElse(0), Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0));
        vo.setPerson_bsi_group_link_amount(person_bsi_group_amount.subtract(person_bsi_group_link_amount).setScale(2, RoundingMode.HALF_UP));
        vo.setPerson_bsi_group_link_rate(MathUtils.calculatePercentage(person_bsi_group_amount.subtract(person_bsi_group_link_amount), person_bsi_group_link_amount));
        BigDecimal person_bsi_agent_amount = MathUtils.genRate(Optional.ofNullable(po.getBsi_agent_amount()).orElse(0), Optional.ofNullable(po.getOn_sale_amount()).orElse(0));
        vo.setPerson_bsi_agent_amount(person_bsi_agent_amount.setScale(2, RoundingMode.HALF_UP));
        BigDecimal person_bsi_agent_link_amount = MathUtils.genRate(Optional.ofNullable(po.getBsi_agent_link_amount()).orElse(0), Optional.ofNullable(po.getOn_sale_link_amount()).orElse(0));
        vo.setPerson_bsi_agent_link_amount(person_bsi_agent_amount.subtract(person_bsi_agent_link_amount).setScale(2, RoundingMode.HALF_UP));
        vo.setPerson_bsi_agent_link_rate(MathUtils.calculatePercentage(person_bsi_agent_amount.subtract(person_bsi_agent_link_amount), person_bsi_agent_link_amount));
        BigDecimal person_follow_customer_link = BigDecimal.ZERO;
        BigDecimal person_follow_customer = BigDecimal.ZERO;
        if (po.getDirect_or_channel() == 1) {
            person_follow_customer = MathUtils.genRate(po.getPrivate_sea_group_amount(), po.getOn_sale_amount());
            vo.setPerson_follow_customer(person_follow_customer.setScale(0, RoundingMode.HALF_UP).intValue());
            person_follow_customer_link = MathUtils.genRate(po.getPrivate_sea_group_link_amount(), po.getOn_sale_link_amount());
            vo.setPerson_follow_customer_link_rate(MathUtils.calculatePercentage(person_follow_customer.subtract(person_follow_customer_link), person_follow_customer_link));
            vo.setBsi_rate(MathUtils.calculatePercentage(po.getBsi_group_amount(), Optional.ofNullable(po.getPrivate_sea_group_amount()).orElse(0) + Optional.ofNullable(po.getGroup_new_customer_amount()).orElse(0)));
        } else {
            person_follow_customer = MathUtils.genRate(po.getPrivate_sea_agent_amount(), po.getOn_sale_amount());
            vo.setPerson_follow_customer(person_follow_customer.setScale(0, RoundingMode.HALF_UP).intValue());
            person_follow_customer_link = MathUtils.genRate(po.getPrivate_sea_agent_link_amount(), po.getOn_sale_link_amount());
            vo.setPerson_follow_customer_link_rate(MathUtils.calculatePercentage(person_follow_customer.subtract(person_follow_customer_link), person_follow_customer_link));
            vo.setBsi_rate(MathUtils.calculatePercentage(po.getBsi_agent_amount(), Optional.ofNullable(po.getPrivate_sea_agent_amount()).orElse(0) + Optional.ofNullable(po.getAgent_new_customer_amount()).orElse(0)));
        }
        vo.setPerson_follow_customer_link_amount(person_follow_customer.subtract(person_follow_customer_link).setScale(0, RoundingMode.HALF_UP).intValue());
        return vo;
    }

    public Integer isLeader(SaleAchievementEfficiencyDayPO po, Map<Integer, SaleDto> saleDtoMap) {
        if (!po.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())) {
            return IsValid.FALSE.getCode();
        }
        SaleDto saleDto = saleDtoMap.get(po.getBiz_id());
        if (saleDto == null) {
            IsValid.FALSE.getCode();
        }
        return Objects.equals(saleDto.getLevel(), SaleLevelEnum.LEADER.getCode()) ? IsValid.TRUE.getCode() : IsValid.FALSE.getCode();
    }

    public void buildQueryVo(SaleEfficiencyQueryVo queryVo) {
        Timestamp now = new Timestamp(System.currentTimeMillis());
        if (queryVo.getDate() == null) {
            queryVo.setDate(incomeTimeUtil.getYesterdayBegin(now).getTime());
        } else {
            queryVo.setDate(Utils.getBeginOfDay(new Timestamp(queryVo.getDate())).getTime());
        }
    }

    public List<Timestamp> queryDate(SaleEfficiencyQueryVo queryVo) {
        Timestamp begin = Utils.getBeginOfDay(new Timestamp(queryVo.getDate()));
        Timestamp end = Utils.getEndOfDay(new Timestamp(queryVo.getDate()));
        switch (queryVo.getType()) {
            case DAY:
                begin = Utils.getBeginOfDay(new Timestamp(queryVo.getDate()));
                end = Utils.getEndOfDay(new Timestamp(queryVo.getDate()));
                break;
            case WEEK:
                begin = incomeTimeUtil.getStartOrEndDayOfWeek(new Timestamp(queryVo.getDate()), true);
                end = Utils.getEndOfDay(new Timestamp(queryVo.getDate()));
                break;
            case MONTH:
                begin = incomeTimeUtil.getMonthFirstDate(new Timestamp(queryVo.getDate()));
                end = Utils.getEndOfDay(new Timestamp(queryVo.getDate()));
                break;
            case QUARTER:
                begin = incomeTimeUtil.getQuarterFirstDate(new Timestamp(queryVo.getDate()));
                end = Utils.getEndOfDay(new Timestamp(queryVo.getDate()));
                break;
        }
        return Lists.newArrayList(begin, end);
    }

    public List<SaleEfficiencyBasicVo> basicPoListToVo(List<SaleEfficiencyBasicPO> poList, SaleEfficiencyQueryVo queryVO, Operator operator, DataStrategy dataStrategy) {
        List<SaleEfficiencyBasicVo> resultVoList = new ArrayList<>();
        if (CollectionUtils.isEmpty(poList)) {
            return resultVoList;
        }
        BigDecimal timeRate = incomeTimeUtil.getQuarterTimeRate(incomeTimeUtil.getNumDayTimeNext(1, new Timestamp(poList.get(0).getAgg_time())));
        String quarterDesc = incomeTimeUtil.getQuarterDescNow(new Timestamp(queryVO.getDate()));
        List<Integer> bizIds = poList.stream().filter(Objects::nonNull).map(SaleEfficiencyBasicPO::getBiz_id).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        List<CrmSaleKpiDto> saleKpiDtos = saleService.queryAllKpiByQuarterAndBizId(quarterDesc, bizIds);
        List<Integer> groupSeq = pdConfigService.getIntList(PdConfigConstants.YJJK_LIST_QUERY_GROUP_SORT_SEQ, queryVO.getDate());
        return poList.stream().filter(Objects::nonNull).map(e -> {
            SaleEfficiencyBasicVo vo = basicPoToVo(e, saleKpiDtos, timeRate);
            // 直客部分小组需要展开到下层
            if (queryVO.getDirect_channel_type() != null && vo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId()) && Lists.newArrayList(SaleGroupEnum.DIRECT.getBizId(), SaleGroupEnum.CHANNEL.getBizId(),
                    SaleGroupEnum.E_FOOD.getBizId(), SaleGroupEnum.NET_EDU_E.getBizId(), SaleGroupEnum.E_HOUSE.getBizId(), SaleGroupEnum.MAKE_UP.getBizId()).contains(vo.getBiz_id()) && dataStrategy.getOrgType().equals(DomainOrgType.ORDINARY)) {
                SaleEfficiencyQueryVo queryCopyVo = new SaleEfficiencyQueryVo();
                BeanUtils.copyProperties(queryVO, queryCopyVo);
                queryCopyVo.setSale_group_id(vo.getBiz_id());
                if (SaleGroupEnum.DIRECT.getBizId().equals(vo.getBiz_type())) {
                    queryCopyVo.setDirect_channel_type(1);
                } else {
                    queryCopyVo.setDirect_channel_type(null);
                }
                try {
                    Thread.sleep(400);
                } catch (InterruptedException ex) {
                    throw new RuntimeException(ex);
                }
                //递归里面调用请求，牛逼 0.0需求时间紧张没时间重构，有的请求有缓存，暂时能支持
                vo.setNext_level(basicPoListToVo(queryList(operator, queryCopyVo, new SaleEfficiencyBasicPO()), queryVO, operator, dataStrategy));
            }
            return vo;
        }).sorted((obj1, obj2) -> {
            // 定义type的排序顺序
            List<Integer> typeOrder = Lists.newArrayList(
                    SaleKpiTypeEnum.SALE.getBizId(),
                    SaleKpiTypeEnum.SALE_TEAM.getBizId()
            );
            int index1 = typeOrder.indexOf(obj1.getBiz_type());
            int index2 = typeOrder.indexOf(obj2.getBiz_type());
            int typeNumber = Integer.compare(index1, index2);
            if (typeNumber != 0) {
                return typeNumber;
            }
            if (obj1.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId()) && obj2.getBiz_type().equals(SaleKpiTypeEnum.SALE.getBizId())) {
                // 总任务排名
                int indexSale = Optional.ofNullable(obj2.getTotal_task_amount()).orElse(BigDecimal.ZERO).intValue() - Optional.ofNullable(obj1.getTotal_task_amount()).orElse(BigDecimal.ZERO).intValue();
                if (indexSale != 0) {
                    return indexSale;
                }
            }
            // 销售小组默认排名
            if (obj1.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId()) && obj2.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())) {
                int indexGroup1 = groupSeq.indexOf(obj1.getBiz_id());
                int indexGroup2 = groupSeq.indexOf(obj2.getBiz_id());
                if (indexGroup1 < 0) {
                    indexGroup1 = Integer.MAX_VALUE;
                }
                if (indexGroup2 < 0) {
                    indexGroup2 = Integer.MAX_VALUE;
                }
                int typeNumberGroup = Integer.compare(indexGroup1, indexGroup2);
                if (typeNumberGroup != 0) {
                    return typeNumberGroup;
                }
            }
            return Integer.compare(obj1.getBiz_id(), obj2.getBiz_id());
        }).collect(Collectors.toList());
    }

    public SaleEfficiencyBasicVo basicPoToVo(SaleEfficiencyBasicPO po, List<CrmSaleKpiDto> saleKpiDtos, BigDecimal timeRate) {
        SaleEfficiencyBasicVo vo = new SaleEfficiencyBasicVo();
        vo.setBiz_id(po.getBiz_id());
        vo.setBiz_name(po.getBiz_name());
        vo.setBiz_type(po.getBiz_type());
        vo.setDate(CrmUtils.formatDate(new Timestamp(po.getAgg_time())));
        buildTaskAmount(vo, saleKpiDtos);
        vo.setOn_sale_amount(po.getBasic_sale_amount());
        vo.setPerson_amount(MathUtils.genRate(CrmUtils.fromFenToWan(po.getTotal_amount()), BigDecimal.valueOf(po.getBasic_sale_amount()), 1));
        vo.setPrivate_sea_group_amount(po.getPrivate_sea_group_amount());
        vo.setAlive_group_amount(po.getPrivate_sea_group_alive_amount());
        vo.setPerson_alive_group_arpu(MathUtils.genRate(CrmUtils.fromFenToWan(po.getAlive_group_money()), BigDecimal.valueOf(po.getPrivate_sea_group_alive_amount()), 1));
        vo.setPrivate_sea_group_alive_rate((MathUtils.calculatePercentageNegative(po.getPrivate_sea_group_alive_amount().longValue(), po.getPrivate_sea_group_amount().longValue())));
        vo.setAlive_group_visit_rate(MathUtils.calculatePercentageNegative(po.getVisit_alive_group_amount().longValue(), po.getPrivate_sea_group_alive_amount().longValue()));
        vo.setPerson_visit_amount_line_60_QTD(vo.getPerson_visit_amount_line_60() == null ? null : timeRate.multiply(BigDecimal.valueOf(Optional.ofNullable(vo.getPerson_visit_amount_line_60()).orElse(0))).setScale(1, RoundingMode.HALF_UP));
        vo.setPerson_visit_amount_line_100_QTD(vo.getPerson_visit_amount_line_100() == null ? null : timeRate.multiply(BigDecimal.valueOf(Optional.ofNullable(vo.getPerson_visit_amount_line_100()).orElse(0))).setScale(1, RoundingMode.HALF_UP));
        vo.setVisit_amount(po.getLbs_follow_record_amount());
        vo.setPerson_visit_amount(MathUtils.genRate(po.getLbs_follow_record_amount(), po.getBasic_sale_amount(), 1));
        if (vo.getPerson_visit_amount_line_60_QTD() != null && vo.getPerson_visit_amount().compareTo(vo.getPerson_visit_amount_line_60_QTD()) < 0) {
            vo.setPerson_visit_amount_line_color(0);
        } else if (vo.getPerson_visit_amount_line_100_QTD() != null && vo.getPerson_visit_amount().compareTo(vo.getPerson_visit_amount_line_100_QTD()) < 0) {
            vo.setPerson_visit_amount_line_color(1);
        } else {
            vo.setPerson_visit_amount_line_color(2);
        }
        vo.setGroup_visit_amount(po.getVisit_group_amount());
        vo.setPerson_group_visit_amount(MathUtils.genRate(po.getVisit_group_amount(), po.getBasic_sale_amount(), 1));
        vo.setAlive_group_visit_amount(po.getVisit_alive_group_amount());
        vo.setVisit_group_alive_rate(MathUtils.calculatePercentageNegative(po.getVisit_alive_group_amount().longValue(), po.getVisit_group_amount().longValue()));
        vo.setVisit_group_money(CrmUtils.fromFenToWan(po.getVisit_group_money()));
        vo.setVisit_group_money_rate(MathUtils.calculatePercentageNegative(po.getVisit_group_money(), po.getTotal_amount()));
        vo.setBsi_amount(po.getBsi_amount());
        vo.setBsi_amount_target_rate(MathUtils.calculatePercentageNegative(po.getBsi_amount().longValue(), Optional.ofNullable(vo.getBsi_target_amount()).orElse(0).longValue()));
        vo.setBsi_complete_amount(po.getBsi_complete_amount());
        vo.setBsi_complete_rate(MathUtils.calculatePercentageNegative(po.getBsi_complete_amount().longValue(), vo.getBsi_amount().longValue()));
        if (Optional.ofNullable(vo.getBsi_trans_target()).orElse(BigDecimal.ZERO).multiply(timeRate).compareTo(vo.getBsi_complete_rate()) > 0) {
            vo.setBsi_trans_color(0);
        } else {
            vo.setBsi_trans_color(1);
        }
        vo.setOffline_visit_amount_rate(MathUtils.calculatePercentageNegative(po.getOffline_visit_amount(), po.getLbs_follow_record_amount()));
        vo.setOffline_visit_progress(MathUtils.calculatePercentageNegative(vo.getOffline_visit_amount_rate(), vo.getOffline_visit_amount_target()));
        if (Objects.equals(po.getActive_customer_perspective(), 1)) {
            vo.setActive_product_count_target(null);
            vo.setNew_customer_count_target(null);
            vo.setProject_sales_target(null);
            vo.setMy_sea_customer_count(po.getMy_sea_customer_count());
            vo.setActive_customer_count(po.getActive_customer_count());
            vo.setActive_customer_progress(MathUtils.calculatePercentageNegative(vo.getActive_customer_count(), vo.getActive_customer_count_target()));
            vo.setActive_customer_color(Optional.ofNullable(vo.getActive_customer_progress()).orElse(BigDecimal.ZERO).compareTo(timeRate.multiply(BigDecimal.valueOf(100))) < 0 ? 0 : 1);
        } else if (Objects.equals(po.getProduct_perspective(), 1)) {
            vo.setActive_customer_count_target(null);
            vo.setNew_customer_count_target(null);
            vo.setProject_sales_target(null);
            vo.setMy_sea_product_count(po.getMy_sea_product_count());
            vo.setActive_product_count(po.getActive_product_count());
            vo.setActive_product_progress(MathUtils.calculatePercentageNegative(vo.getActive_product_count(), vo.getActive_product_count_target()));
            vo.setActive_product_color(Optional.ofNullable(vo.getActive_product_progress()).orElse(BigDecimal.ZERO).compareTo(timeRate.multiply(BigDecimal.valueOf(100))) < 0 ? 0 : 1);
        } else if (Objects.equals(po.getNew_customer_perspective(), 1)) {
            vo.setActive_customer_count_target(null);
            vo.setActive_product_count_target(null);
            vo.setProject_sales_target(null);
            vo.setNew_customer_count(po.getNew_customer_count());
            vo.setNew_customer_progress(MathUtils.calculatePercentageNegative(vo.getNew_customer_count(), vo.getNew_customer_count_target()));
            vo.setNew_customer_color(Optional.ofNullable(vo.getNew_customer_progress()).orElse(BigDecimal.ZERO).compareTo(timeRate.multiply(BigDecimal.valueOf(100))) < 0 ? 0 : 1);
        } else if (Objects.equals(po.getProject_sales_count_perspective(), 1)) {
            vo.setActive_customer_count_target(null);
            vo.setActive_product_count_target(null);
            vo.setNew_customer_count_target(null);
            vo.setProject_sales_count(po.getProject_sales_count());
            vo.setProject_sales_progress(MathUtils.calculatePercentageNegative(vo.getProject_sales_count(), vo.getProject_sales_target()));
            vo.setProject_sales_color(Optional.ofNullable(vo.getProject_sales_progress()).orElse(BigDecimal.ZERO).compareTo(timeRate.multiply(BigDecimal.valueOf(100))) < 0 ? 0 : 1);
        }
        return vo;
    }

    public void buildTaskAmount(SaleEfficiencyBasicVo basicVo, List<CrmSaleKpiDto> kpiDtoList) {
        SaleKpiTypeEnum line60 = SaleKpiTypeEnum.BASIC_SALE_PERSON_VISIT_AMOUNT_LINE_60;
        SaleKpiTypeEnum line100 = SaleKpiTypeEnum.BASIC_SALE_PERSON_VISIT_AMOUNT_LINE_100;
        SaleKpiTypeEnum bsiTarget = SaleKpiTypeEnum.BASIC_SALE_BSI_RECORD_TARGET;
        SaleKpiTypeEnum bsiTrans = SaleKpiTypeEnum.BASIC_SALE_BSI_TRANS_TARGET;
        SaleKpiTypeEnum offlineVisitAmountEnum = SaleKpiTypeEnum.BASIC_SALE_OFFLINE_VISIT_AMOUNT_TARGET;
        SaleKpiTypeEnum activeCustomerCountEnum = SaleKpiTypeEnum.BASIC_SALE_ACTIVE_CUSTOMER_COUNT_TARGET;
        SaleKpiTypeEnum activeProductCountEnum = SaleKpiTypeEnum.BASIC_SALE_ACTIVE_PRODUCT_COUNT_TARGET;
        SaleKpiTypeEnum newCustomerCountEnum = SaleKpiTypeEnum.BASIC_SALE_NEW_CUSTOMER_COUNT_TARGET;
        SaleKpiTypeEnum projectSalesEnum = SaleKpiTypeEnum.BASIC_SALE_PROJECT_SALES_TARGET;
        if (basicVo.getBiz_type().equals(SaleKpiTypeEnum.SALE_TEAM.getBizId())) {
            line60 = SaleKpiTypeEnum.BASIC_GROUP_PERSON_VISIT_AMOUNT_LINE_60;
            line100 = SaleKpiTypeEnum.BASIC_GROUP_PERSON_VISIT_AMOUNT_LINE_100;
            bsiTarget = SaleKpiTypeEnum.BASIC_GROUP_BSI_RECORD_TARGET;
            bsiTrans = SaleKpiTypeEnum.BASIC_GROUP_BSI_TRANS_TARGET;
            offlineVisitAmountEnum = SaleKpiTypeEnum.BASIC_GROUP_OFFLINE_VISIT_AMOUNT_TARGET;
            activeCustomerCountEnum = SaleKpiTypeEnum.BASIC_GROUP_ACTIVE_CUSTOMER_COUNT_TARGET;
            activeProductCountEnum = SaleKpiTypeEnum.BASIC_GROUP_ACTIVE_PRODUCT_COUNT_TARGET;
            newCustomerCountEnum = SaleKpiTypeEnum.BASIC_GROUP_NEW_CUSTOMER_COUNT_TARGET;
            projectSalesEnum = SaleKpiTypeEnum.BASIC_GROUP_PROJECT_GROUPS_TARGET;
        }
        SaleKpiTypeEnum task = SaleKpiTypeEnum.SALE;
        if (basicVo.getBiz_id() == 0) {
            task = SaleKpiTypeEnum.BIZ_PRODUCT_SALE;
        } else {
            task = SaleKpiTypeEnum.getByName(basicVo.getBiz_type());
        }
        SaleKpiTypeEnum finalBizType = task;
        CrmSaleKpiDto itKpi = kpiDtoList.stream()
                .filter(e -> finalBizType.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(0L).build());
        SaleKpiTypeEnum finalLine6 = line60;
        CrmSaleKpiDto line60Kpi = kpiDtoList.stream()
                .filter(e -> finalLine6.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());
        SaleKpiTypeEnum finalLine10 = line100;
        CrmSaleKpiDto line100Kpi = kpiDtoList.stream()
                .filter(e -> finalLine10.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());
        SaleKpiTypeEnum finalBsiTarget = bsiTarget;
        CrmSaleKpiDto bsiTargetKpi = kpiDtoList.stream()
                .filter(e -> finalBsiTarget.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());
        SaleKpiTypeEnum finalBsiTrans = bsiTrans;
        CrmSaleKpiDto bsiTransKpi = kpiDtoList.stream()
                .filter(e -> finalBsiTrans.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());

        SaleKpiTypeEnum finalOfflineVisitAmountEnum = offlineVisitAmountEnum;
        CrmSaleKpiDto offlineVisitAmountKpi = kpiDtoList.stream()
                .filter(e -> finalOfflineVisitAmountEnum.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());

        SaleKpiTypeEnum finalActiveCustomerCountEnum = activeCustomerCountEnum;
        CrmSaleKpiDto activeCustomerCountKpi = kpiDtoList.stream()
                .filter(e -> finalActiveCustomerCountEnum.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());

        SaleKpiTypeEnum finalActiveProductCountEnum = activeProductCountEnum;
        CrmSaleKpiDto activeProductCountKpi = kpiDtoList.stream()
                .filter(e -> finalActiveProductCountEnum.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());

        SaleKpiTypeEnum finalNewCustomerCountEnum = newCustomerCountEnum;
        CrmSaleKpiDto newCustomerCountKpi = kpiDtoList.stream()
                .filter(e -> finalNewCustomerCountEnum.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());

        SaleKpiTypeEnum finalProjectSalesEnum = projectSalesEnum;
        CrmSaleKpiDto projectSalesKpi = kpiDtoList.stream()
                .filter(e -> finalProjectSalesEnum.getBizId().equals(e.getBizType()))
                .filter(e -> basicVo.getBiz_id().equals(e.getBizId())).findFirst()
                .orElse(CrmSaleKpiDto.builder().quarterKpi(null).build());

        basicVo.setTotal_task_amount(itKpi.getQuarterKpi() == null ? null : CrmUtils.fromFenToWan(itKpi.getQuarterKpi(), 1));
        basicVo.setPerson_visit_amount_line_60(line60Kpi.getQuarterKpi() == null ? null : line60Kpi.getQuarterKpi().intValue());
        basicVo.setPerson_visit_amount_line_100(line100Kpi.getQuarterKpi() == null ? null : line100Kpi.getQuarterKpi().intValue());
        basicVo.setBsi_target_amount(bsiTargetKpi.getQuarterKpi() == null ? null : bsiTargetKpi.getQuarterKpi().intValue());
        basicVo.setBsi_trans_target(bsiTransKpi.getQuarterKpi() == null ? null : BigDecimal.valueOf(bsiTransKpi.getQuarterKpi()));

        basicVo.setOffline_visit_amount_target(offlineVisitAmountKpi.getQuarterKpi() == null ? null : BigDecimal.valueOf(offlineVisitAmountKpi.getQuarterKpi()));
        basicVo.setActive_customer_count_target(activeCustomerCountKpi.getQuarterKpi() == null ? null : activeCustomerCountKpi.getQuarterKpi().intValue());
        basicVo.setActive_product_count_target(activeProductCountKpi.getQuarterKpi() == null ? null : activeProductCountKpi.getQuarterKpi().intValue());
        basicVo.setNew_customer_count_target(newCustomerCountKpi.getQuarterKpi() == null ? null : newCustomerCountKpi.getQuarterKpi().intValue());
        basicVo.setProject_sales_target(projectSalesKpi.getQuarterKpi() == null ? null : projectSalesKpi.getQuarterKpi().intValue());
    }

    public <T> List<T> queryByType(Timestamp date, List<Integer> saleIds, List<Integer> saleGroupIds, List<Integer> aggTypes, List<Integer> dataTypes, Integer isForBasic) {
        AlarmHelper.log("QueryByType", date, saleIds, saleGroupIds, aggTypes, dataTypes, isForBasic);
        if (isForBasic == 0) {
            return (List<T>) saleEfficiencyService.queryEfficiencyPoList(date, saleIds, saleGroupIds, aggTypes, dataTypes);
        } else {
            return (List<T>) saleEfficiencyBasicService.queryBasicPoList(date, saleIds, saleGroupIds);
        }
    }

}
