package com.bilibili.crm.platform.portal.webapi.brandreturn;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.web.framework.core.Context;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.adp.web.framework.core.Response;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.brandreturn.BrandReturnCreateDto;
import com.bilibili.crm.platform.api.brandreturn.BrandReturnDetailDto;
import com.bilibili.crm.platform.api.brandreturn.BrandReturnFlowDto;
import com.bilibili.crm.platform.api.contract.dto.ContractBillQueryDto;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.contract.dto.CrmContractBillPeriodDto;
import com.bilibili.crm.platform.api.contract.service.ContractBillPeriodService;
import com.bilibili.crm.platform.api.contract.service.IContractService;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CanDeductContractDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentUploadDto;
import com.bilibili.crm.platform.api.finance.enums.OAFlowStatusEnum;
import com.bilibili.crm.platform.api.finance.enums.SaleTypeEnum;
import com.bilibili.crm.platform.api.flowable.entity.ProcessInfo;
import com.bilibili.crm.platform.api.policy.dto.*;
import com.bilibili.crm.platform.api.policy.enums.FlowTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowStatusEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowUserTypeEnum;
import com.bilibili.crm.platform.api.policy.service.IBrandReturnService;
import com.bilibili.crm.platform.api.policy.service.IPolicyFlowActionService;
import com.bilibili.crm.platform.api.policy.service.IPolicyFlowQueryService;
import com.bilibili.crm.platform.api.sale.dto.SaleBaseDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractAuditStatus;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.Component.attachment.AttachmentUploadFacade;
import com.bilibili.crm.platform.biz.dao.CrmBrandReturnDao;
import com.bilibili.crm.platform.biz.dao.CrmBrandReturnDetailDao;
import com.bilibili.crm.platform.biz.flowable.IFlowProcessQueryService;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.CrmContractRepo;
import com.bilibili.crm.platform.biz.repo.CrmContractSaleMappingRepo;
import com.bilibili.crm.platform.biz.repo.CrmSaleRepo;
import com.bilibili.crm.platform.biz.repo.finance.CrmFlowContractDeductRelationRepo;
import com.bilibili.crm.platform.biz.repo.finance.CrmOaFlowRepo;
import com.bilibili.crm.platform.biz.repo.policy.CrmFwUserGroupRelRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.contracttype.ContractDeductQuerier;
import com.bilibili.crm.platform.biz.service.policy.component.comment.CommentQuerier;
import com.bilibili.crm.platform.biz.service.policy.component.core.node.FlowNodesQuerier;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.biz.util.ExcelReadUtils;
import com.bilibili.crm.platform.common.AttachmentUploadBizModuleEnum;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.policy.ReturnTypeEnum;
import com.bilibili.crm.platform.portal.common.ExportUtils;
import com.bilibili.crm.platform.portal.convertor.AttachmentUploadConvertor;
import com.bilibili.crm.platform.portal.convertor.policy.FlowableCommentConvertor;
import com.bilibili.crm.platform.portal.convertor.policy.PolicyFlowConvertor;
import com.bilibili.crm.platform.portal.convertor.policy.PolicyFlowUserConvertor;
import com.bilibili.crm.platform.portal.webapi.common.BaseRestfulController;
import com.bilibili.crm.platform.portal.webapi.customer.convertor.CustomerFlowConvertor;
import com.bilibili.crm.platform.portal.webapi.customer.vo.report.ProcessInfoVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.flow.PolicyFlowNodeVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.flow.PolicyFlowProcessActionVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.flow.PolicyFlowProcessQueryVo;
import com.bilibili.crm.platform.portal.webapi.finance.vo.flow.PolicyFlowUserVo;
import com.google.common.collect.Lists;
import io.swagger.annotations.Api;
import io.swagger.annotations.ApiOperation;
import io.swagger.annotations.ApiParam;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.IOUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.time.DateUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Controller;
import org.springframework.util.Assert;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.multipart.MultipartFile;
import springfox.documentation.annotations.ApiIgnore;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletResponse;
import java.io.BufferedInputStream;
import java.io.IOException;
import java.math.BigDecimal;
import java.net.URLEncoder;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Controller
@RequestMapping("/web_api/v1/brand/return")
@Api(value = "brand/return", description = "品牌返点")
public class BrandReturnController extends BaseRestfulController {

    @Autowired
    private IBrandReturnService iBrandReturnService;
    @Autowired
    private IPolicyFlowQueryService policyFlowQueryService;
    @Autowired
    private IPolicyFlowActionService policyFlowActionService;
    @Autowired
    private IContractService contractService;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private IFastQueryAccountService fastQueryAccountService;
    @Autowired
    private ICustomerQueryService customerQueryService;
    @Autowired
    private IFlowProcessQueryService iFlowProcessQueryService;
    @Autowired
    private CustomerFlowConvertor customerFlowConvertor;
    @Autowired
    private CrmFwUserGroupRelRepo crmFwUserGroupRelRepo;
    @Autowired
    private CrmContractSaleMappingRepo crmContractSaleMappingRepo;
    @Autowired
    private CrmSaleRepo saleRepo;
    @Autowired
    private CommentQuerier commentQuerier;
    @Autowired
    private AttachmentUploadFacade attachmentUploadFacade;
    @Autowired
    private CrmBrandReturnDao crmBrandReturnDao;
    @Autowired
    private CrmBrandReturnDetailDao crmBrandReturnDetailDao;
    @Autowired
    private CrmOaFlowRepo crmOaFlowRepo;
    @Autowired
    private CrmFlowContractDeductRelationRepo crmFlowContractDeductRelationRepo;
    @Autowired
    private ContractDeductQuerier contractDeductQuerier;
    @Autowired
    private CrmContractRepo crmContractRepo;
    @Autowired
    private FlowNodesQuerier flowNodesQuerier;
    @Autowired
    private ContractBillPeriodService contractBillPeriodService;
    @Resource
    private ExportUtils exportUtils;

    public static final List<Integer> flowIngStatus = Lists.newArrayList(PolicyFlowStatusEnum.REJECTED.getCode(),
            PolicyFlowStatusEnum.PRE_EXAMINE_ING.getCode(),
            PolicyFlowStatusEnum.ADD_SIGN_EXAMINE_ING.getCode());

    @ApiOperation(value = "流程人员列表")
    @RequestMapping(value = "/users", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<PolicyFlowUserVo>> queryFlowUserList(@ApiIgnore Context context,
                                                              @ApiParam("流程类型") @RequestParam(value = "flow_type", required = false, defaultValue = "1") Integer flowType,
                                                              @ApiParam("政策人员类型:1-预审人 2-知会人 3-剩余节点加签审核人") @RequestParam(value = "user_type", required = false) Integer userType) {
        List<PolicyFlowUserDto> policyFlowUserDto = iBrandReturnService.queryFlowUserList(super.getOperator(context), flowType, userType);
        List<PolicyFlowUserVo> policyFlowUserVos = PolicyFlowUserConvertor.convertUserDtos2Vos(policyFlowUserDto);
        return Response.SUCCESS(policyFlowUserVos);
    }

    @ApiOperation(value = "政策默认人员列表")
    @RequestMapping(value = "/default_users", method = RequestMethod.GET)
    @ResponseBody
    public Response<List<PolicyFlowUserVo>> queryDefaultUserList(@ApiIgnore Context context,
                                                                 @ApiParam("流程类型") @RequestParam(value = "flow_type", required = false, defaultValue = "1") Integer flowType,
                                                                 @ApiParam("政策人员类型:1-预审人 2-知会人 3-加签人") @RequestParam(value = "user_type", required = false) Integer userType) {
//        List<PolicyFlowUserDto> policyFlowUserDto = policyFlowQueryService.queryDefaultUserList(flowType, userType);
        List<PolicyFlowUserDto> policyFlowUserDto = iBrandReturnService.queryFlowUserList(super.getOperator(context), flowType, userType);
        List<PolicyFlowUserVo> policyFlowUserVos = PolicyFlowUserConvertor.convertUserDtos2Vos(policyFlowUserDto);
        return Response.SUCCESS(policyFlowUserVos);
    }


    // ================================================================================
    @ApiOperation(value = "全部流程列表")
    @RequestMapping(value = "/task_list/all", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandReturnVo>>> queryAllPolicyFlowTaskList(@ApiIgnore Context context, @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                                                @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                                                PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
        policyFlowProcessQueryDto.setOperator(getOperator(context));
        PageResult<BrandReturnFlowDto> pageResult = iBrandReturnService.queryAllBrandReturnFlowTaskList(currentPage, size, policyFlowProcessQueryDto);
        List<BrandReturnVo> brandReturnVos = Collections.emptyList();
        if (pageResult.getTotal() > 0) {
            brandReturnVos = convertFlowDtos2Vos(pageResult.getRecords());
        }
        return Response.SUCCESS(new Pagination<>(currentPage, pageResult.getTotal(), brandReturnVos));
    }

    @ApiOperation(value = "我的代办列表")
    @RequestMapping(value = "/task_list/todo", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandReturnVo>>> queryTodoPolicyFlowTaskList(@ApiIgnore Context context,
                                                                                 @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                                                 @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                                                 PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
        policyFlowProcessQueryDto.setOperator(getOperator(context));
        AlarmHelper.log("TaskListQueryTodo", policyFlowProcessQueryDto);
        PageResult<BrandReturnFlowDto> pageResult = iBrandReturnService.queryTodoBrandReturnFlowTaskList(currentPage, size, policyFlowProcessQueryDto);
        List<BrandReturnVo> brandReturnVos = Collections.emptyList();
        if (pageResult.getTotal() > 0) {
            brandReturnVos = convertFlowDtos2Vos(pageResult.getRecords());
        }
        return Response.SUCCESS(new Pagination<>(currentPage, pageResult.getTotal(), brandReturnVos));
    }


    @ApiOperation(value = "我的已办列表")
    @RequestMapping(value = "/task_list/has_done", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandReturnVo>>> queryHasDonePolicyFlowTaskList(@ApiIgnore Context context, @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                                                    @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                                                    PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
        policyFlowProcessQueryDto.setOperator(getOperator(context));
        PageResult<BrandReturnFlowDto> pageResult = iBrandReturnService.queryHasDoneBrandReturnFlowTaskList(currentPage, size, policyFlowProcessQueryDto);
        List<BrandReturnVo> brandReturnVos = Collections.emptyList();
        if (pageResult.getTotal() > 0) {
            brandReturnVos = convertFlowDtos2Vos(pageResult.getRecords());
        }
        return Response.SUCCESS(new Pagination<>(currentPage, pageResult.getTotal(), brandReturnVos));
    }

    @ApiOperation(value = "我发起的任务列表")
    @RequestMapping(value = "/task_list/sponsor", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandReturnVo>>> querySponsorPolicyFlowTaskList(@ApiIgnore Context context,
                                                                                    @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                                                    @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                                                    PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
        policyFlowProcessQueryDto.setOperator(getOperator(context));
        PageResult<BrandReturnFlowDto> pageResult = iBrandReturnService.querySponsorBrandReturnFlowTaskList(currentPage, size, policyFlowProcessQueryDto);
        List<BrandReturnVo> brandReturnVos = Collections.emptyList();
        if (pageResult.getTotal() > 0) {
            brandReturnVos = convertFlowDtos2Vos(pageResult.getRecords());
        }
        return Response.SUCCESS(new Pagination<>(currentPage, pageResult.getTotal(), brandReturnVos));
    }

    @ApiOperation(value = "抄送我的列表")
    @RequestMapping(value = "/task_list/copy_me", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandReturnVo>>> queryCopyMePolicyFlowTaskList(@ApiIgnore Context context, @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                                                   @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                                                   PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
        policyFlowProcessQueryDto.setOperator(getOperator(context));

        PageResult<BrandReturnFlowDto> policyFlowDtoPageResult =
                iBrandReturnService.queryCopyMeBrandReturnFlowTaskList(currentPage, size, policyFlowProcessQueryDto);
        List<BrandReturnVo> brandReturnVos = Collections.emptyList();
        if (policyFlowDtoPageResult.getTotal() > 0) {
            brandReturnVos = convertFlowDtos2Vos(policyFlowDtoPageResult.getRecords());
        }
        return Response.SUCCESS(new Pagination<>(currentPage, policyFlowDtoPageResult.getTotal(), brandReturnVos));
    }


    @ApiOperation(value = "获取流程当前节点信息")
    @RequestMapping(value = "/info/node", method = RequestMethod.GET)
    @ResponseBody
    public Response<ProcessInfoVo> queryPolicyProcessNodeInfoById(@ApiIgnore Context context,
                                                                  @RequestParam(value = "process_instance_id", required = false) @ApiParam("流程实例id") String processInstanceId) {
//        ProcessInfo processInfo = iFlowProcessQueryService.queryProcess(processInstanceId);
        ProcessInfo processInfo = new ProcessInfo();
        BrandReturnFlowDto returnFlowDto = iBrandReturnService.queryBrandReturnByProcessId(processInstanceId);
        ProcessInfoVo processInfoVo = customerFlowConvertor.info2Vo(processInfo);
        List<CrmFwUserGroupRelPo> notices = crmFwUserGroupRelRepo.queryByProcessInstanceIdAndType(processInstanceId, PolicyFlowUserTypeEnum.NOTICE.getCode());
        List<CrmFwUserGroupRelPo> preExamines = crmFwUserGroupRelRepo.queryByProcessInstanceIdAndType(processInstanceId, PolicyFlowUserTypeEnum.PRE_EXAMINE.getCode());
        processInfoVo.setNotice_users(notices.stream().map(CrmFwUserGroupRelPo::getUserName).collect(Collectors.toList()));
        processInfoVo.setPre_examine_users(preExamines.stream().map(CrmFwUserGroupRelPo::getUserName).collect(Collectors.toList()));

        // 获取评论
        PolicyFlowProcessDto policyFlowProcessDto = commentQuerier.queryComments(FlowTypeEnum.BRAND_RETURN.getCode(), processInstanceId);
        processInfoVo.setComment_dto_list(FlowableCommentConvertor.convertCommentDtos2Vos(policyFlowProcessDto.getCommentDtoList()));

        //获取上传附件
        List<AttachmentResultDto> attachmentResultDtos = attachmentUploadFacade.queryAttachmentInfo(returnFlowDto.getId().intValue(), AttachmentUploadBizModuleEnum.BRAND_RETURN);
        List<AttachmentResultDto> auditResultDtos = attachmentUploadFacade.queryAttachmentInfo(returnFlowDto.getId().intValue(), AttachmentUploadBizModuleEnum.BRAND_RETURN_AUDIT);
        processInfoVo.setAttachment_upload_result_vos(AttachmentUploadConvertor.convertAttachmentUploadDtos2Vos(attachmentResultDtos));
        processInfoVo.setProcess_attachment_list(AttachmentUploadConvertor.convertAttachmentUploadDtos2Vos(auditResultDtos));

        // 获取当前节点信息
        PolicyFlowNodeDto policyFlowNodeDto = flowNodesQuerier.queryPolicyProcessNodeInfoById(FlowTypeEnum.BRAND_RETURN.getCode(), processInstanceId);
        processInfoVo.setCur_node_vo(PolicyFlowConvertor.convertFlowNodeDto2Vo(policyFlowNodeDto));

        return Response.SUCCESS(processInfoVo);
    }

    // ================================================================================================

    @ApiOperation(value = "品牌返点上传数据")
    @RequestMapping(value = "/create_upload", method = RequestMethod.POST)
    @ResponseBody
    public Response<List<BrandReturnDetailVo>> uploadAndCheckBrandReturnFlow(@ApiIgnore Context context,
                                                                             @RequestParam(value = "process_instance_id", required = false) @ApiParam("流程实例id") String processInstanceId,
                                                                             @RequestParam(value = "file", required = false) MultipartFile multipartFile) {
        List<BrandReturnDetailVo> result = new ArrayList<>();
        List<String> rowsList = new ArrayList<>();
        try {
            rowsList = ExcelReadUtils.readExcel(multipartFile.getOriginalFilename(), multipartFile.getBytes(), 0);
            rowsList.remove(0);
        } catch (Exception e) {
            // exception.message: Cell 上传模版!P2 is part of a multi-cell array formula. You cannot change part of an array.
            throw new IllegalArgumentException("Excel解析失败，请检查文件是否包含公式");
        }

        try {
            for (String row : rowsList) {
                log.info("row {}", row);
                String[] columns = row.split(",");

                if (columns.length < 15 || StringUtils.isBlank(columns[0])) {
                    continue;
                }
                result.add(BrandReturnDetailVo.builder()
                        .contract_number(Long.parseLong(columns[0]))
                        .enable_year(DateUtils.parseDate(columns[1], "yyyy"))
                        .return_type(ReturnTypeEnum.getByName(columns[2]).getCode())
                        .return_type_desc(ReturnTypeEnum.getByName(columns[2]).getName())
                        .hard_nostand(new BigDecimal(columns[3]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .artist(new BigDecimal(columns[4]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .luo_tian_yi(new BigDecimal(columns[5]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .luo_tian_yi_return(new BigDecimal(columns[12]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .live(new BigDecimal(columns[6]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .live_return(new BigDecimal(columns[11]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .pickup(new BigDecimal(columns[7]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .base_return(new BigDecimal(columns[9]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .base_return_ratio(new BigDecimal(columns[8]).multiply(new BigDecimal(100)).intValue())
                        .return_money(new BigDecimal(columns[10]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .other_return(new BigDecimal(columns[13]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .return_amount(new BigDecimal(columns[14]).setScale(3, BigDecimal.ROUND_HALF_UP).setScale(2, BigDecimal.ROUND_HALF_UP))
                        .remark(columns.length == 16 ? columns[15] : "")
                        .build());

            }
        } catch (Exception e) {
            log.error("error ", e);
            throw new RuntimeException(e.getMessage());
        }

        //校验
        checkAndEnhance(result, processInstanceId);

        return Response.SUCCESS(result);
    }

    /**
     * 合同号必须真实存在，且上传的所有合同要属于同一个代理商客户，否则提示，您上传的合同属于多个代理客户，请检查后重新提交
     * <p>
     * 一个合同只能在一个进行中（审核驳回、预审审核中、加签审核中）的流程中展示，否则该条数据上传不成功
     * <p>
     * 日期类字段的格式需要为日期
     * <p>
     * 金额类列的格式需要为数字
     */
    private void checkAndEnhance(List<BrandReturnDetailVo> data, String processInstanceId) {
        List<Long> contractNumbers = data.stream().map(BrandReturnDetailVo::getContract_number).collect(Collectors.toList());
        List<Long> contractNumbersDistinct = contractNumbers.stream().distinct().collect(Collectors.toList());
        Assert.isTrue(contractNumbersDistinct.size() == contractNumbers.size(), "存在重复合同");

        List<ContractDto> contractDtos = contractService.queryContractsInNumbers(contractNumbers);
        for (ContractDto contractDto : contractDtos) {
            Assert.isTrue(Utils.isPositive(contractDto.getAgentId()), "合同没有代理信息" + contractDto.getContractNumber());
        }
        List<Integer> agentAccIds = contractDtos.stream().map(ContractDto::getAgentAccountId).distinct().collect(Collectors.toList());
        List<Integer> signProjectIds = contractDtos.stream().map(ContractDto::getSignSubjectId).distinct().collect(Collectors.toList());
        Assert.isTrue(signProjectIds.size() <= 1, "您提交的合同属于多个我方主体，请分主体提交");
        if (CollectionUtils.isNotEmpty(agentAccIds)) {
            List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(agentAccIds).build(), AccountFieldMapping.accountId, AccountFieldMapping.groupId);
            List<Integer> groupIds = accountBaseDtos.stream().map(AccountBaseDto::getGroupId).distinct().collect(Collectors.toList());
            Assert.isTrue(1 == groupIds.size(), "您上传的合同属于多个代理商集团，请检查后重新提交");
        }

        Map<Long, ContractDto> contractDtoMap = contractDtos.stream().collect(Collectors.toMap(ContractDto::getContractNumber, Function.identity()));

        if (StringUtils.isNotBlank(processInstanceId)) {
            CrmBrandReturnDetailPoExample example = new CrmBrandReturnDetailPoExample();
            example.createCriteria().andProcessInstanceIdEqualTo(processInstanceId);
            crmBrandReturnDetailDao.updateByExampleSelective(CrmBrandReturnDetailPo.builder().isDeleted(IsDeleted.DELETED.getCode()).build(), example);
        }

        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = new PolicyFlowProcessQueryDto();
        policyFlowProcessQueryDto.setContractNumbers(contractNumbers);
        // FIXME 写死分页参数，很扯的代码!!!
        PageResult<BrandReturnDetailDto> brandReturnDetailDtoPageResult = iBrandReturnService.brandReturnDetailList(1, 5000, policyFlowProcessQueryDto);
        List<BrandReturnDetailDto> brandReturnDetailDtos = brandReturnDetailDtoPageResult.getRecords();
        Map<Integer, List<BrandReturnDetailDto>> brandReturnDetailDtoMaps = brandReturnDetailDtos.stream().collect(Collectors.groupingBy(BrandReturnDetailDto::getContractId));

        for (BrandReturnDetailVo detailVo : data) {
            ContractDto contractDto = contractDtoMap.get(detailVo.getContract_number());
            Assert.isTrue(null != contractDto, "合同不存在 " + detailVo.getContract_number());

            List<BrandReturnDetailDto> returnDetailDtos = brandReturnDetailDtoMaps.get(contractDto.getId());
            if (CollectionUtils.isNotEmpty(returnDetailDtos)) {
                for (BrandReturnDetailDto returnDetailDto : returnDetailDtos) {
                    Assert.isTrue(!flowIngStatus.contains(returnDetailDto.getBusStatus()), "当前合同 " + detailVo.getContract_number() + " 有进行中的流程 " + returnDetailDto.getProcessInstanceId());
                }
            }

            detailVo.setContract_name(contractDto.getName());
            detailVo.setContract_id(contractDto.getId());
            detailVo.setIs_oa_open_bill(contractDto.getIsOaOpenBill());
            detailVo.setAdvertise_account_id(contractDto.getAccountId());
            detailVo.setAdvertise_account_name(contractDto.getAccountName());
            detailVo.setChannel_sales(contractDto.getSaleDtos().stream()
                    .filter(r -> Lists.newArrayList(SaleTypeEnum.BRAND_CHANNEL.getCode(), SaleTypeEnum.EFFECT_CHANNEL.getCode()).contains(r.getType())).map(SaleDto::getEmail).collect(Collectors.joining(",")));
            detailVo.setDirect_sales(contractDto.getSaleDtos().stream()
                    .filter(r -> Lists.newArrayList(SaleTypeEnum.BRAND_DIRECT.getCode(), SaleTypeEnum.EFFECT_DIRECT.getCode()).contains(r.getType())).map(SaleDto::getEmail).collect(Collectors.joining(",")));
            detailVo.setAdvertise_customer_id(contractDto.getCustomerId());
            detailVo.setAdvertise_customer_name(contractDto.getCustomerName());
            detailVo.setAgent_account_id(contractDto.getAgentAccountId());
            detailVo.setAgent_account_name(contractDto.getAgentAccountName());
            detailVo.setAgent_customer_id(contractDto.getAgentCustomerId());
            detailVo.setAgent_customer_name(contractDto.getAgentCustomerName());
            detailVo.setContract_audit_status_desc(ContractAuditStatus.getDescByCode(contractDto.getAuditStatus()));
            detailVo.setContract_biz_status_desc(ContractBusStatus.getDescByCode(contractDto.getBusStatus()));
            detailVo.setInvoice_status(contractDto.getInvoiceStatus());
            detailVo.setAmount(Utils.fromFenToYuan(contractDto.getAmount()));
            detailVo.setOpt_agent_id(contractDto.getOptAgentId());
            detailVo.setOpt_agent_name(contractDto.getOptAgentName());
            detailVo.setBegin_time(contractDto.getBeginTime());
            detailVo.setEnd_time(contractDto.getEndTime());
        }

        //总和相加
        data.add(0, BrandReturnDetailVo.builder()
                .artist(data.stream().map(BrandReturnDetailVo::getArtist).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .base_return(data.stream().map(BrandReturnDetailVo::getBase_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .return_money(data.stream().map(BrandReturnDetailVo::getReturn_money).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .live(data.stream().map(BrandReturnDetailVo::getLive).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .live_return(data.stream().map(BrandReturnDetailVo::getLive_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .luo_tian_yi_return(data.stream().map(BrandReturnDetailVo::getLuo_tian_yi_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .luo_tian_yi(data.stream().map(BrandReturnDetailVo::getLuo_tian_yi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .pickup(data.stream().map(BrandReturnDetailVo::getPickup).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .other_return(data.stream().map(BrandReturnDetailVo::getOther_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .return_amount(data.stream().map(BrandReturnDetailVo::getReturn_amount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                .build());
    }

    private void enhanceContract(List<BrandReturnDetailVo> result) {
        List<Integer> contractIds = result.stream().map(BrandReturnDetailVo::getContract_id).distinct().collect(Collectors.toList());
        List<Integer> crmOaFlowIds = result.stream().map(BrandReturnDetailVo::getCrm_oa_flow_id).distinct().collect(Collectors.toList());

        List<CrmContractPo> crmContractPos = crmContractRepo.queryContractListByIds(contractIds);
        List<CanDeductContractDto> canDeductContractDtos = contractDeductQuerier.convertCanDeductContractPos2ContractDtos(crmContractPos, false, null, null);
        if (CollectionUtils.isEmpty(canDeductContractDtos)) {
            return;
        }
        Map<Integer, CanDeductContractDto> deductContractDtoMap = canDeductContractDtos.stream().collect(Collectors.toMap(CanDeductContractDto::getContractId, Function.identity(), (r1, r2) -> r2));

        List<CrmOaFlowPo> crmOaFlowPos = crmOaFlowRepo.queryOaFlowByIds(crmOaFlowIds);
        Map<Integer, CrmOaFlowPo> crmOaFlowPoMap = crmOaFlowPos.stream().collect(Collectors.toMap(CrmOaFlowPo::getId, Function.identity()));

        for (BrandReturnDetailVo returnDetailVo : result) {
            CanDeductContractDto deductContractDto = deductContractDtoMap.get(returnDetailVo.getContract_id());
            if (null == deductContractDto) {
                continue;
            }
            CrmOaFlowPo crmOaFlowPo = crmOaFlowPoMap.getOrDefault(returnDetailVo.getCrm_oa_flow_id(), CrmOaFlowPo.builder().build());

            returnDetailVo.setAdvertise_account_id(deductContractDto.getAdvertiseAccountId());
            returnDetailVo.setAdvertise_account_name(deductContractDto.getAdvertiseAccountName());
            returnDetailVo.setAdvertise_customer_id(deductContractDto.getAdvertiseCustomerId());
            returnDetailVo.setAdvertise_customer_name(deductContractDto.getAdvertiseCustomerName());
            returnDetailVo.setAgent_account_id(deductContractDto.getAgentAccountId());
            returnDetailVo.setAgent_account_name(deductContractDto.getAgentAccountName());
            returnDetailVo.setAgent_customer_id(deductContractDto.getAgentCustomerId());
            returnDetailVo.setAgent_customer_name(deductContractDto.getAgentCustomerName());
            returnDetailVo.setBegin_time(deductContractDto.getBegin_time());
            returnDetailVo.setEnd_time(deductContractDto.getEnd_time());
            returnDetailVo.setChannel_sales(deductContractDto.getBelongSaleDtos().stream()
                    .filter(r -> Lists.newArrayList(SaleTypeEnum.BRAND_CHANNEL.getCode(), SaleTypeEnum.EFFECT_CHANNEL.getCode()).contains(r.getType())).map(SaleBaseDto::getEmail).collect(Collectors.joining(",")));
            returnDetailVo.setDirect_sales(deductContractDto.getBelongSaleDtos().stream()
                    .filter(r -> Lists.newArrayList(SaleTypeEnum.BRAND_DIRECT.getCode(), SaleTypeEnum.EFFECT_DIRECT.getCode()).contains(r.getType())).map(SaleBaseDto::getEmail).collect(Collectors.joining(",")));
            returnDetailVo.setContract_audit_status_desc(deductContractDto.getContractAuditStatusDesc());
            returnDetailVo.setContract_biz_status_desc(deductContractDto.getContractBizStatusDesc());
            returnDetailVo.setContract_invoice_status(deductContractDto.getInvoiceStatus());
            returnDetailVo.setContract_number(Long.parseLong(deductContractDto.getContractNo()));
            returnDetailVo.setContract_id(deductContractDto.getContractId());
            returnDetailVo.setContract_name(deductContractDto.getContractName());
            returnDetailVo.setIs_oa_open_bill(null == crmOaFlowPo.getId() ? 0 : 1);
            returnDetailVo.setIs_oa_open_bill_desc(IsValid.getByCode(returnDetailVo.getIs_oa_open_bill()).getDesc());
            returnDetailVo.setInvoice_status(null == crmOaFlowPo.getStatus() ? null : OAFlowStatusEnum.getByCode(crmOaFlowPo.getStatus()).getOaDesc());
            returnDetailVo.setAmount(Utils.fromFenToYuan(deductContractDto.getContractPackageAmount()));
            returnDetailVo.setOpt_agent_id(deductContractDto.getOptAgentId());
            returnDetailVo.setOpt_agent_name(deductContractDto.getOptAgentName());
            returnDetailVo.setOa_bill_flow_no(crmOaFlowPo.getOaFlowNo());
            returnDetailVo.setInvoice_complete_date(OAFlowStatusEnum.COMPLETED.getCode().equals(crmOaFlowPo.getStatus()) ? crmOaFlowPo.getMtime() : null);
            returnDetailVo.setInvoice_ctime(crmOaFlowPo.getCtime());
            returnDetailVo.setTotalDeductedAmount(Utils.fromFenToYuan(deductContractDto.getTotalClaimAmount()));
            returnDetailVo.setLatest_bank_connect_deductP_pay_time(CrmUtils.formatDate(deductContractDto.getLatestBankConnectDeductPayTime(),
                    CrmUtils.YYYYMMDDHHMMSS));
            returnDetailVo.setIs_deduct_completed(deductContractDto.getContractIsDeductCompleted());
        }
    }


    @ApiOperation(value = "品牌返点新建流程")
    @RequestMapping(value = "/create", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> createBrandReturnFlow(@ApiIgnore Context context,
                                                  @RequestBody BrandReturnCreateVo brandReturnCreateVo) {
        log.info("=====> createBrandReturnFlow, params:{}", JSON.toJSONString(brandReturnCreateVo));
        return Response.SUCCESS(iBrandReturnService.createBrandReturnFlow(super.getOperator(context), voToDto(brandReturnCreateVo)));
    }


    @ApiOperation(value = "品牌返点流程修改")
    @RequestMapping(value = "/update", method = RequestMethod.POST)
    @ResponseBody
    public Response<String> updatePolicyFlow(@ApiIgnore Context context,
                                             @RequestBody BrandReturnCreateVo brandReturnCreateVo) {
        log.info("=====> brandReturnCreateVo, id:{}", brandReturnCreateVo.getId());
        return Response.SUCCESS(iBrandReturnService.updateBrandReturnFlow(getOperator(context), voToDto(brandReturnCreateVo)));
    }

    @ResponseBody
    @ApiOperation(value = "政策流程审核通过(也实现了加签功能)")
    @RequestMapping(value = "/complete", method = RequestMethod.POST)
    public Response<String> completeTask(@ApiIgnore Context context,
                                         @RequestBody PolicyFlowProcessActionVo policyFlowProcessActionVo) {
        log.info("=====> completeTask, params:{}", JSON.toJSONString(policyFlowProcessActionVo));
        PolicyFlowProcessActionDto policyFlowProcessActionDto = PolicyFlowConvertor.convertFlowActionVo2Dto(policyFlowProcessActionVo);
        policyFlowProcessActionDto.setOperator(getOperator(context));
        return Response.SUCCESS(iBrandReturnService.completeTask(policyFlowProcessActionDto));
    }

    @ResponseBody
    @ApiOperation(value = "政策流程驳回")
    @RequestMapping(value = "/reject", method = RequestMethod.POST)
    public Response<String> rejectFlow(@ApiIgnore Context context,
                                       @RequestBody PolicyFlowProcessActionVo policyFlowProcessActionVo) {
        log.info("=====> rejectFlow, params:{}", JSON.toJSONString(policyFlowProcessActionVo));

        PolicyFlowProcessActionDto policyFlowProcessActionDto = PolicyFlowConvertor.convertFlowActionVo2Dto(policyFlowProcessActionVo);
        policyFlowProcessActionDto.setOperator(getOperator(context));
        iBrandReturnService.reject(super.getOperator(context), policyFlowProcessActionVo.getBiz_id(), policyFlowProcessActionVo.getContent());
        return Response.SUCCESS("success");
    }

    @ResponseBody
    @ApiOperation(value = "流程可退回至节点列表")
    @RequestMapping(value = "/return/nodes", method = RequestMethod.GET)
    public Response<List<PolicyFlowNodeVo>> canReturnNodes(@ApiIgnore Context context,
                                                           @ApiParam("流程类型") @RequestParam(value = "flow_type", required = false, defaultValue = "1") Integer flowType,
                                                           @ApiParam("流程实例id") @RequestParam(value = "process_instance_id", required = false) String processInstanceId) {
        List<PolicyFlowNodeDto> policyFlowNodeDtos = policyFlowQueryService.canReturnNodes(flowType, processInstanceId);
        return Response.SUCCESS(PolicyFlowConvertor.convertFlowNodeDtos2Vos(policyFlowNodeDtos));
    }

    @ResponseBody
    @ApiOperation(value = "政策流程退回至某个已完成节点")
    @RequestMapping(value = "/return", method = RequestMethod.POST)
    public Response<String> returnFlow(@ApiIgnore Context context,
                                       @RequestBody PolicyFlowProcessActionVo policyFlowProcessActionVo) {
        log.info("=====> returnFlow, params:{}", JSON.toJSONString(policyFlowProcessActionVo));

        PolicyFlowProcessActionDto policyFlowProcessActionDto = PolicyFlowConvertor.convertFlowActionVo2Dto(policyFlowProcessActionVo);
        policyFlowProcessActionDto.setOperator(getOperator(context));
        return Response.SUCCESS(iBrandReturnService.returnFlow(policyFlowProcessActionDto));
    }

    @ResponseBody
    @ApiOperation(value = "政策流程废弃")
    @RequestMapping(value = "/abandon", method = RequestMethod.POST)
    public Response<String> abandonFlow(@ApiIgnore Context context,
                                        @RequestBody PolicyFlowProcessActionVo policyFlowProcessActionVo) {
        log.info("=====> abandonFlow, params:{}", JSON.toJSONString(policyFlowProcessActionVo));

        PolicyFlowProcessActionDto policyFlowProcessActionDto = PolicyFlowConvertor.convertFlowActionVo2Dto(policyFlowProcessActionVo);
        policyFlowProcessActionDto.setOperator(getOperator(context));
        return Response.SUCCESS(policyFlowActionService.abandonFlow(policyFlowProcessActionDto));
    }

//    @ResponseBody
//    @ApiOperation(value = "流程挂起/激活")
//    @RequestMapping(value = "/process_instance/update_active_status", method = RequestMethod.POST)
//    public Response<String> updateProcessInstanceActiveStatus(@ApiIgnore Context context,
//                                                              @RequestBody PolicyFlowProcessActionVo policyFlowProcessActionVo) {
//        PolicyFlowProcessActionDto policyFlowProcessActionDto = PolicyFlowConvertor.convertFlowActionVo2Dto(policyFlowProcessActionVo);
//        policyFlowProcessActionDto.setOperator(getOperator(context));
//        return Response.SUCCESS(policyFlowActionService.updateProcessInstanceActiveStatus(policyFlowProcessActionDto));
//    }

    // todo 流程撤回

    @Deprecated
    @ResponseBody
    @ApiOperation(value = "上传附件")
    @RequestMapping(value = "/attachment/upload", method = RequestMethod.POST)
    public Response<CommonAttachmentUploadDto> uploadAttachment(@ApiIgnore Context context,
                                                                @RequestParam(value = "process_instance_id", required = false) @ApiParam("附件id") String processInstanceId,
                                                                @RequestParam(value = "file", required = false) MultipartFile file) {
        FileInfoDto fileInfoDto = FileInfoDto.builder().build();
        fileInfoDto.setContentType(file.getContentType());
        fileInfoDto.setProcessInstanceId(processInstanceId);
        fileInfoDto.setName(file.getName());
        fileInfoDto.setOriginalFilename(file.getOriginalFilename());
        try {
            fileInfoDto.setFileInputStream(file.getInputStream());
        } catch (IOException e) {
            e.printStackTrace();
        }
        CommonAttachmentUploadDto commonAttachmentUploadDto = iBrandReturnService.uploadAttachment(fileInfoDto);

        return Response.SUCCESS(commonAttachmentUploadDto);
    }

    /**
     * 自带的下载方式，flowable
     *
     * @param context
     * @param attachmentId
     * @param response
     * @throws IOException
     */
    @Deprecated
    @ApiOperation(value = "下载附件")
    @RequestMapping(value = "/attachment/download", method = RequestMethod.GET)
    @ResponseBody
    public void downloadAttachment(@ApiIgnore Context context,
                                   @RequestParam(value = "attachment_id", required = false) @ApiParam("附件id") String attachmentId,
                                   HttpServletResponse response) throws IOException {
        FileInfoDto fileInfoDto = policyFlowQueryService.downloadAttachment(attachmentId);

        String contentType = StringUtils.substringBefore(fileInfoDto.getContentType(), ";");
        response.addHeader("Content-Type", contentType + ";charset=UTF-8");
        String extensionFileName = StringUtils.substringAfter(fileInfoDto.getContentType(), ";");
        String fileName = fileInfoDto.getName() + "." + extensionFileName;
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        IOUtils.copy(new BufferedInputStream(fileInfoDto.getFileInputStream()), response.getOutputStream());
    }

    /**
     * 下载支持修改文件名
     *
     * @param context
     * @param attachmentId
     * @param response
     * @throws IOException
     */
    @Deprecated
    @ApiOperation(value = "下载附件(指定文件名)")
    @RequestMapping(value = "/attachment/download_by_file_name", method = RequestMethod.GET)
    @ResponseBody
    public void downloadAttachmentByFileName(@ApiIgnore Context context,
                                             @RequestParam(value = "attachment_id", required = false) @ApiParam("附件id") Integer attachmentId,
                                             HttpServletResponse response) throws IOException {
        FileInfoDto fileInfoDto = policyFlowQueryService.downloadAttachmentByFileName(attachmentId);

        String contentType = StringUtils.substringBefore(fileInfoDto.getContentType(), ";");
        response.addHeader("Content-Type", contentType + ";charset=UTF-8");
        String extensionFileName = StringUtils.substringAfter(fileInfoDto.getContentType(), ";");
        String fileName = fileInfoDto.getName();
        fileName = URLEncoder.encode(fileName, "UTF-8");
        response.setHeader("Content-Disposition", "attachment; filename=" + fileName);
        IOUtils.copy(new BufferedInputStream(fileInfoDto.getFileInputStream()), response.getOutputStream());
    }

    @ResponseBody
    @ApiOperation(value = "获取流程图图片")
    @RequestMapping(value = "/loadPngByProcessInstanceId", method = RequestMethod.GET)
    public void loadPngByProcessInstanceId(@ApiIgnore Context context,
                                           @RequestParam(value = "process_instance_id", required = false) @ApiParam("流程实例id") String processInstanceId, HttpServletResponse response) {
        try {
            byte[] b = policyFlowQueryService.loadPngByProcessInstanceId(processInstanceId);
            response.setHeader("Content-Type", "image/png");
            response.getOutputStream().write(b);
        } catch (IOException e) {
            e.printStackTrace();
        }
    }


    //品牌返点管理
    @ApiOperation(value = "品牌返点列表导出")
    @RequestMapping(value = "/detail/list_export", method = RequestMethod.GET)
    @ResponseBody
    public Response<String> brandReturnDetailListExport(@ApiIgnore Context context, @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                        @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                        PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        size = 50000;
        Integer finalSize = size;
        exportUtils.asyncExportToEmail(getOperator(context), () -> {
            try {
                PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
                policyFlowProcessQueryDto.setOperator(getOperator(context));

                if (Utils.isPositive(policyFlowProcessQueryVo.getAgent_customer_id())) {
                    CustomerBaseDto customerBaseDto = customerQueryService.getCustomerBaseDtoById(policyFlowProcessQueryVo.getAgent_customer_id());
                    Integer customerId = null == customerBaseDto ? 0 : customerBaseDto.getId();
                    List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder()
                            .customerId(customerId).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId);
                    if (CollectionUtils.isEmpty(accountBaseDtos)) {
                        policyFlowProcessQueryDto.setAgentIds(Lists.newArrayList(-1));
                    } else {
                        List<Integer> agentAccIds = accountBaseDtos.stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList());
                        Map<Integer, Integer> agentIdMapInAccountIds = agentService.getAccountId2AgentIdMapInAccountIds(agentAccIds);
                        policyFlowProcessQueryDto.setAgentIds(new ArrayList<>(agentIdMapInAccountIds.values()));
                    }
                }

                PageResult<BrandReturnDetailDto> pageResult = iBrandReturnService.brandReturnDetailList(currentPage, finalSize, policyFlowProcessQueryDto);
                List<BrandReturnDetailVo> brandReturnDetailVos = Collections.emptyList();
                if (pageResult.getTotal() > 0) {
                    brandReturnDetailVos = convertDetailDtos2Vos(pageResult.getRecords());
                }
                return brandReturnDetailVos;
            } catch (Exception e) {
                log.error("export error ", e);
            }
            return Collections.emptyList();
        }, "您好，附件是CRM品牌返点审批明细明细数据，请查收", true);

        return Response.SUCCESS("已将导出数据文件发送至你的b站邮箱。前往查看已将导出数据文件发送至你的b站邮箱。前往查看");
    }


    //品牌返点管理
    @ApiOperation(value = "品牌返点列表")
    @RequestMapping(value = "/detail/list", method = RequestMethod.GET)
    @ResponseBody
    public Response<Pagination<List<BrandReturnDetailVo>>> brandReturnDetailList(@ApiIgnore Context context, @RequestParam(value = "page", required = true, defaultValue = "1") @ApiParam("页码") Integer currentPage,
                                                                                 @RequestParam(value = "size", required = true, defaultValue = "15") @ApiParam("页长") Integer size,
                                                                                 PolicyFlowProcessQueryVo policyFlowProcessQueryVo) {
        PolicyFlowProcessQueryDto policyFlowProcessQueryDto = PolicyFlowConvertor.convertFlowQueryVo2Dto(policyFlowProcessQueryVo);
        policyFlowProcessQueryDto.setOperator(getOperator(context));

        if (Utils.isPositive(policyFlowProcessQueryVo.getAgent_customer_id())) {
            CustomerBaseDto customerBaseDto = customerQueryService.getCustomerBaseDtoById(policyFlowProcessQueryVo.getAgent_customer_id());
            Integer customerId = null == customerBaseDto ? 0 : customerBaseDto.getId();
            List<AccountBaseDto> accountBaseDtos = fastQueryAccountService.fetch(QueryAccountParam.builder()
                    .customerId(customerId).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId);
            if (CollectionUtils.isEmpty(accountBaseDtos)) {
                policyFlowProcessQueryDto.setAgentIds(Lists.newArrayList(-1));
            } else {
                List<Integer> agentAccIds = accountBaseDtos.stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList());
                Map<Integer, Integer> agentIdMapInAccountIds = agentService.getAccountId2AgentIdMapInAccountIds(agentAccIds);
                policyFlowProcessQueryDto.setAgentIds(new ArrayList<>(agentIdMapInAccountIds.values()));
            }
        }

        PageResult<BrandReturnDetailDto> pageResult = iBrandReturnService.brandReturnDetailList(currentPage, size, policyFlowProcessQueryDto);
        List<BrandReturnDetailVo> brandReturnDetailVos = new ArrayList<>();
        if (pageResult.getTotal() > 0) {
            brandReturnDetailVos = convertDetailDtos2Vos(pageResult.getRecords());
        }

        if (policyFlowProcessQueryVo.isReturn_total()) {
            //总和相加
            brandReturnDetailVos.add(0, BrandReturnDetailVo.builder()
                    .artist(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getArtist).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .base_return(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getBase_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .return_money(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getReturn_money).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .live(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getLive).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .live_return(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getLive_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .luo_tian_yi_return(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getLuo_tian_yi_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .luo_tian_yi(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getLuo_tian_yi).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .pickup(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getPickup).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .other_return(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getOther_return).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .return_amount(brandReturnDetailVos.stream().map(BrandReturnDetailVo::getReturn_amount).filter(Objects::nonNull).reduce(BigDecimal::add).orElse(BigDecimal.ZERO))
                    .build());
        }

        return Response.SUCCESS(new Pagination<>(currentPage, pageResult.getTotal(), brandReturnDetailVos));
    }

    private List<BrandReturnDetailVo> convertDetailDtos2Vos(List<BrandReturnDetailDto> recordDetails) {
        if (CollectionUtils.isEmpty(recordDetails)) {
            return new ArrayList<>();
        }
        List<Integer> contractIds = recordDetails.stream().map(BrandReturnDetailDto::getContractId).distinct().collect(Collectors.toList());
        ContractBillQueryDto billQueryDto = new ContractBillQueryDto();
        billQueryDto.setCrmContractIds(contractIds);
        List<CrmContractBillPeriodDto> periodDtoList = contractBillPeriodService.queryBill(billQueryDto, null);
        Map<Integer, List<CrmContractBillPeriodDto>> contractIdMap = periodDtoList.stream().collect(Collectors.groupingBy(CrmContractBillPeriodDto::getCrmContractId));

        List<BrandReturnDetailVo> returnDetailVos = recordDetails.stream().map(
                r -> {
                    List<CrmContractBillPeriodDto> periodDtos = contractIdMap.getOrDefault(r.getContractId(), Lists.newArrayList(CrmContractBillPeriodDto.builder().build()));

                    periodDtos.stream().filter(item -> null != item.getCollectionDeadlineTime()).collect(Collectors.toList())
                            .sort(Comparator.comparing(CrmContractBillPeriodDto::getCollectionDeadlineTime));

                    return BrandReturnDetailVo.builder()
                            .id(r.getId())
                            .process_instance_id(r.getProcessInstanceId())
                            .last_contract_return_time(CollectionUtils.isNotEmpty(periodDtos) ? periodDtos.get(0).getCollectionDeadlineTime() : null)
                            .contract_id(r.getContractId())
                            .return_type_desc(ReturnTypeEnum.getByCode(r.getReturnType()).getName())
                            .return_type(r.getReturnType())
                            .artist(Utils.fromFenToYuan(r.getArtist()))
                            .enable_year(r.getEnableYear())
                            .base_return(Utils.fromFenToYuan(r.getBaseReturn()))
                            .base_return_ratio(r.getBaseReturnRatio())
                            .creator(r.getCreator())
                            .ctime(r.getCtime())
                            .mtime(r.getMtime())
                            .return_amount(Utils.fromFenToYuan(r.getReturnAmount()))
                            .return_money(Utils.fromFenToYuan(r.getReturnMoney()))
                            .live_return(Utils.fromFenToYuan(r.getLiveReturn()))
                            .live(Utils.fromFenToYuan(r.getLive()))
                            .luo_tian_yi(Utils.fromFenToYuan(r.getLuoTianYi()))
                            .luo_tian_yi_return(Utils.fromFenToYuan(r.getLuoTianYiReturn()))
                            .pickup(Utils.fromFenToYuan(r.getPickup()))
                            .other_return(Utils.fromFenToYuan(r.getOtherReturn()))
                            .enable_year(r.getEnableYear())
                            .hard_nostand(Utils.fromFenToYuan(r.getHardNostand()))
                            .remark(r.getRemark())
                            .crm_oa_flow_id(r.getCrmOaFlowId())
                            .bus_status(PolicyFlowStatusEnum.getByCode(r.getBusStatus()).getDesc())
                            .process_attachment_list(AttachmentUploadConvertor.convertAttachmentUploadDtos2Vos(r.getProcessAttachmentResultDtos()))
                            .build();
                }
        ).collect(Collectors.toList());
        enhanceContract(returnDetailVos);
        return returnDetailVos;
    }


    private List<BrandReturnVo> convertFlowDtos2Vos(List<BrandReturnFlowDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.emptyList();
        }

        List<String> processIds = records.stream().map(BrandReturnFlowDto::getProcessInstanceId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(processIds)) {
            return Collections.emptyList();
        }

        CrmBrandReturnDetailPoExample example = new CrmBrandReturnDetailPoExample();
        example.createCriteria().andProcessInstanceIdIn(processIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmBrandReturnDetailPo> crmBrandReturnDetailPos = crmBrandReturnDetailDao.selectByExample(example);
        Map<String, List<CrmBrandReturnDetailPo>> brandReturnDetailMap = crmBrandReturnDetailPos.stream().collect(Collectors.groupingBy(CrmBrandReturnDetailPo::getProcessInstanceId));

        List<Integer> contractIds = crmBrandReturnDetailPos.stream().map(CrmBrandReturnDetailPo::getContractId).distinct().collect(Collectors.toList());
        List<ContractDto> contractDtos = contractService.queryContractsInIds(contractIds);
        Map<Integer, ContractDto> contractMaps = contractDtos.stream().collect(Collectors.toMap(ContractDto::getId, Function.identity(), (r1, r2) -> r2));

//        List<Integer> agentCustomerIds = contractDtos.stream().map(ContractDto::getAgentCustomerId).distinct().collect(Collectors.toList());
//        List<CustomerBaseDto> customerBaseDtos = new ArrayList<>();
//        if(CollectionUtils.isNotEmpty(agentCustomerIds)){
//            customerBaseDtos = customerQueryService.queryCustomerByQueryDto(CustomerQueryDto.builder().customerIds(agentCustomerIds).build());
//        }
//        Map<Integer, CustomerBaseDto> customerBaseDtoMap = customerBaseDtos.stream().collect(Collectors.toMap(CustomerBaseDto::getId, Function.identity()));

        return records.stream().map(r -> {
            Set<String> contractAgentCustomer = new HashSet<>();
            List<CrmBrandReturnDetailPo> brandReturnDetailPos = brandReturnDetailMap.getOrDefault(r.getProcessInstanceId(), new ArrayList<>());
            for (CrmBrandReturnDetailPo detailPo : brandReturnDetailPos) {
                ContractDto contractDto = contractMaps.get(detailPo.getContractId());
                if (null != contractDto) {
                    contractAgentCustomer.add(contractDto.getAgentCustomerName() + "（" + contractDto.getAgentCustomerId() + "）");
                }
            }
            return BrandReturnVo.builder()
                    .id(r.getId())
                    .process_instance_id(r.getProcessInstanceId())
                    .status(r.getBusStatus())
                    .status_desc(PolicyFlowStatusEnum.getByCode(r.getBusStatus()).getDesc())
                    .creator(r.getCreator())
                    .ctime(r.getCtime())
                    .is_deleted(r.getIsDeleted())
                    .assignee(String.join(",", r.getAssignee()))
//                    .agent_customer_id(r.getAgentCustomerId())
                    .agent_customer_name(String.join(",", contractAgentCustomer))
                    .build();
        }).collect(Collectors.toList());
    }


    private BrandReturnCreateDto voToDto(BrandReturnCreateVo vo) {
        return BrandReturnCreateDto.builder()
                .id(vo.getId())
                .processInstanceId(vo.getProcess_instance_id())
                .noticeUsers(vo.getNotice_users())
                .preExamineUsers(vo.getPre_examine_users())
                .commonAttachmentUploadDtos(AttachmentUploadConvertor.convertAttachmentUploadVos2Dtos(vo.getAttachment_list()))
                .auditAttachmentUploadDtos(AttachmentUploadConvertor.convertAttachmentUploadVos2Dtos(vo.getAudit_attachment_list()))
                .isWithAudit(vo.getIs_with_audit())
                .brandReturnDetailDtoList(vo.getBrandReturnDetailVoList().stream().map(
                        r -> BrandReturnDetailDto.builder()
                                .contractId(r.getContract_id())
                                .artist(Utils.fromYuanToFen(r.getArtist()))
                                .baseReturn(Utils.fromYuanToFen(r.getBase_return()))
                                .baseReturnRatio(r.getBase_return_ratio())
                                .creator(r.getCreator())
                                .enableYear(r.getEnable_year())
                                .hardNostand(Utils.fromYuanToFen(r.getHard_nostand()))
                                .live(Utils.fromYuanToFen(r.getLive()))
                                .liveReturn(Utils.fromYuanToFen(r.getLive_return()))
                                .luoTianYi(Utils.fromYuanToFen(r.getLuo_tian_yi()))
                                .luoTianYiReturn(Utils.fromYuanToFen(r.getLuo_tian_yi_return()))
                                .pickup(Utils.fromYuanToFen(r.getPickup()))
                                .remark(r.getRemark())
                                .returnType(r.getReturn_type())
                                .returnMoney(Utils.fromYuanToFen(r.getReturn_money()))
                                .returnAmount(Utils.fromYuanToFen(r.getReturn_amount()))
                                .otherReturn(Utils.fromYuanToFen(r.getOther_return()))
                                .build()
                ).collect(Collectors.toList()))
                .build();
    }


}
