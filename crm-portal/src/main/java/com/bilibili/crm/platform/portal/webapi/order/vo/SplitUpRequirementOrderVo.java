package com.bilibili.crm.platform.portal.webapi.order.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SplitUpRequirementOrderVo {
    @ApiModelProperty("被拆订单id")
    @NotNull
    private Integer split_order_id;

    @ApiModelProperty("下单附件（列表页面没有）")
    @NotNull
    private List<NewUpRequirementOrderVo> order_list;
}
