package com.bilibili.crm.platform.portal.config.database;

import com.baomidou.mybatisplus.annotation.DbType;
import com.baomidou.mybatisplus.extension.plugins.MybatisPlusInterceptor;
import com.baomidou.mybatisplus.extension.plugins.inner.PaginationInnerInterceptor;
import com.baomidou.mybatisplus.extension.spring.MybatisSqlSessionFactoryBean;
import com.bilibili.bible.common.plugin.mybatis.OptCatExecutorMybatisPlugin;
import lombok.extern.slf4j.Slf4j;
import org.apache.ibatis.plugin.Interceptor;
import org.apache.ibatis.session.SqlSessionFactory;
import org.mybatis.spring.annotation.MapperScan;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import org.springframework.transaction.annotation.EnableTransactionManagement;

import javax.sql.DataSource;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 默认配置
 */
@Slf4j
@Configuration
@EnableTransactionManagement
@MapperScan(sqlSessionFactoryRef = "offlineCkSqlSessionFactory",
        basePackages = {
                "com.bilibili.crm.biz.wallet.ckdao"
        })
public class OfflineCkSourceConfig {

    //分页插件
    @Bean
    public MybatisPlusInterceptor mybatisPaginationInnerInterceptor() {
        MybatisPlusInterceptor interceptor = new MybatisPlusInterceptor();
        interceptor.addInnerInterceptor(new PaginationInnerInterceptor(DbType.MYSQL));
        return interceptor;
    }

    @Bean
    public SqlSessionFactory offlineCkSqlSessionFactory(List<Interceptor> plugins,
                                                        @Qualifier("primaryOptCatExecutorMybatisPlugin") OptCatExecutorMybatisPlugin primaryOptCatExecutorMybatisPlugin,
                                                        @Qualifier("clickHouse") DataSource dataSource) throws Exception {
        //注册了一堆 **CatExecutorMybatisPlugin，都排除掉，只保留primaryOptCatExecutorMybatisPlugin
        List<Interceptor> pluginList = plugins.stream().filter(it -> {
            if (it instanceof OptCatExecutorMybatisPlugin) {
                return false;
            }
            return true;
        }).collect(Collectors.toList());
        pluginList.add(primaryOptCatExecutorMybatisPlugin);
        MybatisSqlSessionFactoryBean bean = new MybatisSqlSessionFactoryBean();
        bean.setDataSource(dataSource);
        bean.setPlugins(pluginList.toArray(new Interceptor[pluginList.size()]));
        return bean.getObject();
    }
}
