package com.bilibili.crm.platform.portal.convertor;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.automation.*;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.portal.webapi.finance.vo.*;
import com.bilibili.crm.platform.portal.webapi.finance.vo.receivemoney.RebateCheckInfoReportVo;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/5/26 8:37 下午
 */
public class RebateCheckInfoVoConvertor {
    public static RebateCheckInfoVo convertDto2Vo(RebateCheckInfoDto rebateCheckInfoDto) {
        if (rebateCheckInfoDto == null) {
            return null;
        }
        RebateCheckInfoVo rebateCheckInfoVo = RebateCheckInfoVo.builder().build();
        rebateCheckInfoVo.setRebate_check_id(rebateCheckInfoDto.getId());
        rebateCheckInfoVo.setBill_id(rebateCheckInfoDto.getBillId());
        rebateCheckInfoVo.setBill_start_date(CrmUtils.formatDate(rebateCheckInfoDto.getBillDate()));
        rebateCheckInfoVo.setBill_complete_date(CrmUtils.formatDate(rebateCheckInfoDto.getBillCompleteDate()));
        rebateCheckInfoVo.setInvoice_source_desc(rebateCheckInfoDto.getInvoiceSourceDesc());
        rebateCheckInfoVo.setCreator(rebateCheckInfoDto.getCreator());
        rebateCheckInfoVo.setTotal_price_tax(Utils.fromFenToYuan(rebateCheckInfoDto.getTotalPriceTax()));
        rebateCheckInfoVo.setDiscount_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getDiscountAmount()));
        rebateCheckInfoVo.setBill_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getBillAmount()));
        rebateCheckInfoVo.setRebate_check_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getRebateCheckAmount()));
        rebateCheckInfoVo.setDeducted_check_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getDeductedCheckAmount()));
        rebateCheckInfoVo.setNot_deducted_check_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getNotDeductedCheckAmount()));
        rebateCheckInfoVo.setBill_company(rebateCheckInfoDto.getBillCompany());
        rebateCheckInfoVo.setBill_status_desc(rebateCheckInfoDto.getBillStatusDesc());
        rebateCheckInfoVo.setRebate_check_status(rebateCheckInfoDto.getRebateCheckStatus());
        rebateCheckInfoVo.setRebate_check_status_desc(rebateCheckInfoDto.getRebateCheckStatusDesc());
        rebateCheckInfoVo.setRebate_check_type(rebateCheckInfoDto.getRebateCheckType());
        rebateCheckInfoVo.setRebate_check_type_desc(rebateCheckInfoDto.getRebateCheckTypeDesc());
        rebateCheckInfoVo.setContract_number(rebateCheckInfoDto.getContractNumber());
        rebateCheckInfoVo.setOa_flow_no(rebateCheckInfoDto.getOaFlowNo());
        rebateCheckInfoVo.setAccount_id(rebateCheckInfoDto.getAccountId());
        rebateCheckInfoVo.setAccount_name(rebateCheckInfoDto.getAccountName());
        rebateCheckInfoVo.setCustomer_id(rebateCheckInfoDto.getCustomerId());
        rebateCheckInfoVo.setCustomer_name(rebateCheckInfoDto.getCustomerName());
        return rebateCheckInfoVo;
    }

    public static RebateCheckToOuterSaveDto convertToOuterVo2Dto(RebateCheckToOuterSaveVo rebateCheckInfoSaveVo) {
        if (rebateCheckInfoSaveVo == null) {
            return null;
        }
        RebateCheckToOuterSaveDto rebateCheckToOuterSaveDto =
                RebateCheckToOuterSaveDto.builder().rebateCheckId(rebateCheckInfoSaveVo.getRebate_check_id()).build();
        return rebateCheckToOuterSaveDto;
    }

    public static RebateCheckDeductContractSaveDto convertDeductContractVo2Dto(RebateCheckDeductContractSaveVo rebateCheckInfoSaveVo) {
        if (rebateCheckInfoSaveVo == null) {
            return null;
        }
        RebateCheckDeductContractSaveDto rebateCheckDeductContractSaveDto = new RebateCheckDeductContractSaveDto();
        rebateCheckDeductContractSaveDto.setRebateCheckId(rebateCheckInfoSaveVo.getRebate_check_id());
        rebateCheckDeductContractSaveDto.setDetailList(convertDeductContractDetailVos2Dtos(rebateCheckInfoSaveVo.getDetail_list()));
        return rebateCheckDeductContractSaveDto;
    }

    private static List<RebateCheckDeductContractSaveDto.RebateCheckDeductContractDetailInfo> convertDeductContractDetailVos2Dtos(List<RebateCheckDeductContractSaveVo.RebateCheckDeductContractDetailVo> detailList) {
        if (CollectionUtils.isEmpty(detailList)) {
            return Collections.EMPTY_LIST;
        }
        List<RebateCheckDeductContractSaveDto.RebateCheckDeductContractDetailInfo> detailInfos = detailList.stream().map(t -> {
            RebateCheckDeductContractSaveDto.RebateCheckDeductContractDetailInfo deductContractDetailInfo =
                    new RebateCheckDeductContractSaveDto.RebateCheckDeductContractDetailInfo();
            deductContractDetailInfo.setContractNumber(t.getContract_number());
            // 元 -> 分
            deductContractDetailInfo.setDeductAmount(new BigDecimal(Utils.fromYuanToFen(t.getDeduct_amount())));
            deductContractDetailInfo.setContractBillPeriodId(t.getContract_bill_period_id());
            return deductContractDetailInfo;
        }).filter(dto -> dto.getDeductAmount().compareTo(new BigDecimal(0)) > 0).collect(Collectors.toList());
        return detailInfos;
    }

    public static RebateCheckInfoDeductQueryDto convertRebateCheckDeductQueryVo2Dto(RebateCheckDeductInfoQueryVo rebateCheckDeductInfoQueryVo) {
        return null;
    }

    public static RebateCheckInfoQueryDto convertRebateCheckQueryVo2Dto(RebateCheckInfoQueryVo rebateCheckDeductInfoQueryVo) {
        RebateCheckInfoQueryDto rebateCheckInfoQueryDto = RebateCheckInfoQueryDto.builder().build();
        if (rebateCheckDeductInfoQueryVo == null) {
            return rebateCheckInfoQueryDto;
        }
        if (rebateCheckDeductInfoQueryVo.getBill_time_begin() != null) {
            rebateCheckInfoQueryDto.setBillTimeBegin(Utils.getBeginOfDay(new Timestamp(rebateCheckDeductInfoQueryVo.getBill_time_begin())));
        }
        if (rebateCheckDeductInfoQueryVo.getBill_time_end() != null) {
            rebateCheckInfoQueryDto.setBillTimeEnd(Utils.getEndOfDay(new Timestamp(rebateCheckDeductInfoQueryVo.getBill_time_end())));
        }
        rebateCheckInfoQueryDto.setBillId(rebateCheckDeductInfoQueryVo.getBill_id());
        rebateCheckInfoQueryDto.setBillCompany(rebateCheckDeductInfoQueryVo.getBill_company());
        rebateCheckInfoQueryDto.setBillStatusList(rebateCheckDeductInfoQueryVo.getBill_status_list());
        rebateCheckInfoQueryDto.setRebateCheckStatusList(rebateCheckDeductInfoQueryVo.getRebate_check_status_list());
        rebateCheckInfoQueryDto.setRebateCheckTypeList(rebateCheckDeductInfoQueryVo.getRebate_check_type_list());
        rebateCheckInfoQueryDto.setOaFlowNo(rebateCheckDeductInfoQueryVo.getOa_flow_no());
        rebateCheckInfoQueryDto.setContractNo(rebateCheckDeductInfoQueryVo.getContract_number());
        return rebateCheckInfoQueryDto;
    }

    public static List<RebateCheckInfoVo> convertdtos2Vos(List<RebateCheckInfoDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.EMPTY_LIST;
        }
        return records.stream().map(t -> convertDto2Vo(t)).collect(Collectors.toList());
    }

    public static List<RebateCheckInfoReportVo> convertdtos2ReportVos(List<RebateCheckInfoDto> records) {
        if (CollectionUtils.isEmpty(records)) {
            return Collections.EMPTY_LIST;
        }
        return records.stream().map(t -> convertDto2ReportVo(t)).collect(Collectors.toList());
    }

    private static RebateCheckInfoReportVo convertDto2ReportVo(RebateCheckInfoDto rebateCheckInfoDto) {
        if (rebateCheckInfoDto == null) {
            return null;
        }
        RebateCheckInfoReportVo rebateCheckInfoVo = RebateCheckInfoReportVo.builder().build();
        rebateCheckInfoVo.setRebate_check_id(rebateCheckInfoDto.getId());
        rebateCheckInfoVo.setBill_id(rebateCheckInfoDto.getBillId());
        rebateCheckInfoVo.setBill_start_date(CrmUtils.formatDate(rebateCheckInfoDto.getBillDate()));
        rebateCheckInfoVo.setBill_complete_date(CrmUtils.formatDate(rebateCheckInfoDto.getBillCompleteDate()));
        rebateCheckInfoVo.setInvoice_source_desc(rebateCheckInfoDto.getInvoiceSourceDesc());
        rebateCheckInfoVo.setCreator(rebateCheckInfoDto.getCreator());
        rebateCheckInfoVo.setTotal_price_tax(Utils.fromFenToYuan(rebateCheckInfoDto.getTotalPriceTax()));
        rebateCheckInfoVo.setDiscount_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getDiscountAmount()));
        rebateCheckInfoVo.setBill_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getBillAmount()));
        rebateCheckInfoVo.setRebate_check_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getRebateCheckAmount()));
//        rebateCheckInfoVo.setDeducted_check_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getDeductedCheckAmount()));
//        rebateCheckInfoVo.setNot_deducted_check_amount(Utils.fromFenToYuan(rebateCheckInfoDto.getNotDeductedCheckAmount()));
        rebateCheckInfoVo.setBill_company(rebateCheckInfoDto.getBillCompany());
        rebateCheckInfoVo.setBill_status_desc(rebateCheckInfoDto.getBillStatusDesc());
        rebateCheckInfoVo.setRebate_check_status_desc(rebateCheckInfoDto.getRebateCheckStatusDesc());
        rebateCheckInfoVo.setRebate_check_type_desc(rebateCheckInfoDto.getRebateCheckTypeDesc());
        rebateCheckInfoVo.setContract_number(rebateCheckInfoDto.getContractNumber());
        rebateCheckInfoVo.setOa_flow_no(rebateCheckInfoDto.getOaFlowNo());
        rebateCheckInfoVo.setAccount_id(rebateCheckInfoDto.getAccountId());
        rebateCheckInfoVo.setAccount_name(rebateCheckInfoDto.getAccountName());
        rebateCheckInfoVo.setCustomer_id(rebateCheckInfoDto.getCustomerId());
        rebateCheckInfoVo.setCustomer_name(rebateCheckInfoDto.getCustomerName());
        return rebateCheckInfoVo;
    }

    public static RebateContractAmountInfoVo convertRebateContractAmountDto2Vo(RebateContractAmountInfoDto rebateContractAmountInfoDto) {
        if (rebateContractAmountInfoDto == null) {
            return null;
        }
        RebateContractAmountInfoVo rebateContractAmountInfoVo = RebateContractAmountInfoVo.builder().build();
        rebateContractAmountInfoVo.setContract_id(rebateContractAmountInfoDto.getContractId());
        rebateContractAmountInfoVo.setContract_no(rebateContractAmountInfoDto.getContractNo());
        rebateContractAmountInfoVo.setTotal_deducted_amount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getTotalDeductedAmount()));
        rebateContractAmountInfoVo.setContract_to_deduct_amount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getToDeductAmount()));
        rebateContractAmountInfoVo.setPackageAmount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getPackageAmount()));
        rebateContractAmountInfoVo.setTotalBillAmount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getTotalBillAmount()));
        rebateContractAmountInfoVo.setBillAmount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getBillAmount()));
        rebateContractAmountInfoVo.setTotalClaimAmount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getTotalClaimAmount()));
        rebateContractAmountInfoVo.setInitDeductedAmount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getInitDeductedAmount()));
        rebateContractAmountInfoVo.setOfflineBillAmount(Utils.fromFenToYuan(rebateContractAmountInfoDto.getOfflineBillAmount()));
        return rebateContractAmountInfoVo;
    }
}
