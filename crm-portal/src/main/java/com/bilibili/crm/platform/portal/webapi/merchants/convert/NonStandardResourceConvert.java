package com.bilibili.crm.platform.portal.webapi.merchants.convert;

import com.bilibili.crm.biz.merchants.dto.OffStandardResourceDto;
import com.bilibili.crm.biz.merchants.enums.OffStandardStatusEnum;
import com.bilibili.crm.platform.api.exception.CrmAppException;
import com.bilibili.crm.platform.biz.repo.attachment.CrmCommonAttachmentDto;
import com.bilibili.crm.platform.portal.attachment.AttachmentInfoVo;
import com.bilibili.crm.platform.portal.webapi.merchants.vo.req.NonStandardExcel;
import com.bilibili.crm.platform.portal.webapi.merchants.vo.resp.NonStandardResourceResp;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/3
 */
@Slf4j
public class NonStandardResourceConvert {

    public static OffStandardResourceDto convert(NonStandardExcel req, String type) {
        if (null == req) {
            return null;
        }
        try {
            OffStandardResourceDto offStandardResourceDto = new OffStandardResourceDto();
            offStandardResourceDto.setResourceLocation(req.getResourceLocation());
            offStandardResourceDto.setResourceType(req.getResourceType());
            offStandardResourceDto.setResourceName(req.getResourceName());
            offStandardResourceDto.setRightsDesc(req.getRightsDesc());
            offStandardResourceDto.setResourcePlatform(req.getResourcePlatform());
            offStandardResourceDto.setUnit(req.getUnit());
            offStandardResourceDto.setProjectLevel(req.getProjectLevel());
            offStandardResourceDto.setProjectType(req.getProjectType());
            offStandardResourceDto.setDataType(req.getDataType());
            offStandardResourceDto.setResourcePricingModule(req.getResourcePricingModule());

            if (StringUtils.isNotBlank(req.getNetUnitPrice())) {
                offStandardResourceDto.setNetUnitPrice(yuanToFen(req.getNetUnitPrice()));
            }

            if (StringUtils.isNotBlank(req.getPublishUnitPriceLow())) {
                offStandardResourceDto.setPublishUnitPriceLow(yuanToFen(req.getPublishUnitPriceLow()));
            }

            if (StringUtils.isNotBlank(req.getPublishUnitPriceHigh())) {
                offStandardResourceDto.setPublishUnitPriceHigh(yuanToFen(req.getPublishUnitPriceHigh()));
            }

            if (StringUtils.isNotBlank(req.getMinCpmSaleNum())) {
                offStandardResourceDto.setMinCpmSaleNum(Integer.valueOf(req.getMinCpmSaleNum()));
            }
            if (StringUtils.isNotBlank(req.getUnitDuration())) {
                offStandardResourceDto.setUnitDuration(Integer.valueOf(req.getUnitDuration()));
            }

            if (StringUtils.isNotBlank(req.getProfitRate())) {
                offStandardResourceDto.setProfitRate(Integer.valueOf(req.getProfitRate()));
            }
            offStandardResourceDto.setType(type);
            return offStandardResourceDto;
        } catch (Exception e) {
            throw new CrmAppException("excel数据格式不正确");
        }
    }

    public static NonStandardExcel convertExcel(OffStandardResourceDto dto) {
        if (null == dto) {
            return null;
        }
        NonStandardExcel nonStandardExcel = new NonStandardExcel();
        nonStandardExcel.setType(dto.getType());
        nonStandardExcel.setResourceId(dto.getResourceId());
        nonStandardExcel.setResourceLocation(dto.getResourceLocation());
        nonStandardExcel.setResourceType(dto.getResourceType());
        nonStandardExcel.setResourceName(dto.getResourceName());
        nonStandardExcel.setRightsDesc(dto.getRightsDesc());
        nonStandardExcel.setResourcePlatform(dto.getResourcePlatform());
        nonStandardExcel.setUnit(dto.getUnit());
        nonStandardExcel.setProjectLevel(dto.getProjectLevel());
        nonStandardExcel.setProjectType(dto.getProjectType());
        nonStandardExcel.setDataType(dto.getDataType());
        nonStandardExcel.setResourcePricingModule(dto.getResourcePricingModule());

        nonStandardExcel.setNetUnitPrice(fenToYuan(dto.getNetUnitPrice()));
        nonStandardExcel.setPublishUnitPriceLow(fenToYuan(dto.getPublishUnitPriceLow()));
        nonStandardExcel.setPublishUnitPriceHigh(fenToYuan(dto.getPublishUnitPriceHigh()));
        nonStandardExcel.setMinCpmSaleNum(String.valueOf(dto.getMinCpmSaleNum()));
        nonStandardExcel.setUnitDuration(String.valueOf(dto.getUnitDuration()));
        nonStandardExcel.setProfitRate(String.valueOf(dto.getProfitRate()));
        nonStandardExcel.setStatus(OffStandardStatusEnum.getDesc(dto.getStatus()));
        return nonStandardExcel;
    }

    public static NonStandardResourceResp convert(OffStandardResourceDto dto) {
        if (null == dto) {
            return null;
        }

        NonStandardResourceResp nonStandardResourceResp = new NonStandardResourceResp();
        nonStandardResourceResp.setResourceId(dto.getResourceId());
        nonStandardResourceResp.setResourceLocation(dto.getResourceLocation());
        nonStandardResourceResp.setResourceType(dto.getResourceType());
        nonStandardResourceResp.setResourceName(dto.getResourceName());
        nonStandardResourceResp.setRightsDesc(dto.getRightsDesc());
        nonStandardResourceResp.setResourcePlatform(dto.getResourcePlatform());
        nonStandardResourceResp.setUnit(dto.getUnit());
        nonStandardResourceResp.setProjectLevel(dto.getProjectLevel());
        nonStandardResourceResp.setProjectType(dto.getProjectType());
        nonStandardResourceResp.setDataType(dto.getDataType());
        nonStandardResourceResp.setResourcePricingModule(dto.getResourcePricingModule());
        nonStandardResourceResp.setNetUnitPrice(dto.getNetUnitPrice());
        nonStandardResourceResp.setPublishUnitPriceLow(dto.getPublishUnitPriceLow());
        nonStandardResourceResp.setPublishUnitPriceHigh(dto.getPublishUnitPriceHigh());
        nonStandardResourceResp.setMinCpmSaleNum(dto.getMinCpmSaleNum());
        nonStandardResourceResp.setUnitDuration(dto.getUnitDuration());
        nonStandardResourceResp.setProfitRate(dto.getProfitRate());
        nonStandardResourceResp.setStatus(dto.getStatus());
        nonStandardResourceResp.setTabType(dto.getType());
        if (CollectionUtils.isEmpty(dto.getDemos())) {
            return nonStandardResourceResp;
        }
        List<AttachmentInfoVo> attachmentInfoVos = dto.getDemos().stream().map(NonStandardResourceConvert::convertAttachment).collect(Collectors.toList());
        nonStandardResourceResp.setDemo(attachmentInfoVos);
        return nonStandardResourceResp;
    }

    public static AttachmentInfoVo convertAttachment(CrmCommonAttachmentDto dto) {
        if (null == dto) {
            return null;
        }
        AttachmentInfoVo attachmentInfoVo = new AttachmentInfoVo();
        attachmentInfoVo.setUrl(dto.getUrl());
        attachmentInfoVo.setFileName(dto.getFileName());
        attachmentInfoVo.setOssKey(dto.getOssKey());
        return attachmentInfoVo;
    }

    public static Long yuanToFen(String yuan) {
        BigDecimal bigDecimal = new BigDecimal(100);
        String string = new BigDecimal(yuan).multiply(bigDecimal).setScale(0).toString();
        return Long.valueOf(string);
    }

    public static String fenToYuan(Long fen) {
        BigDecimal bigDecimal = new BigDecimal(100);
        return new BigDecimal(fen).divide(bigDecimal, 2, RoundingMode.HALF_UP).toString();
    }
}
