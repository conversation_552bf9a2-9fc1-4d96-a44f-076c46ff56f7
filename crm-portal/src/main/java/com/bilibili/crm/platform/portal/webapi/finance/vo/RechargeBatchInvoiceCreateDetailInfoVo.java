package com.bilibili.crm.platform.portal.webapi.finance.vo;

import com.fasterxml.jackson.annotation.JsonInclude;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

import javax.validation.constraints.NotNull;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@JsonInclude(JsonInclude.Include.NON_NULL)
public class RechargeBatchInvoiceCreateDetailInfoVo {
    @ApiModelProperty("充值id")
    @NotNull(message = "充值id不可为空")
    private Integer recharge_id;

//    @ApiModelProperty("充值类型")
//    @NotEmpty(message = "充值类型不可为空")
//    private String recharge_type;
//
//    @ApiModelProperty("付款公司")
//    @NotEmpty(message = "付款公司不可为空")
//    private String payment_company;
//
//    @ApiModelProperty("收款公司")
//    @NotEmpty(message = "收款公司不可为空")
//    private String collection_company;
//
//    @ApiModelProperty("充值流水号")
//    @NotEmpty(message = "充值流水号不可为空")
//    private String recharge_no;
//
//    @ApiModelProperty("充值日期")
//    @NotEmpty(message = "充值日期不可为空")
//    private String recharge_date;
//
//    @ApiModelProperty("充值金额")
//    @NotEmpty(message = "充值金额不可为空")
//    private String recharge_amount;
//    @ApiModelProperty("充值类型[自动/手动]")
//    @NotEmpty(message = "充值类型不可为空")
//    private String mode_desc;
//
//    @ApiModelProperty("付款公司")
//    @NotEmpty(message = "付款公司不可为空")
//    private String pay_account_name;
//
//    @ApiModelProperty("收款公司")
//    @NotEmpty(message = "收款公司不可为空")
//    private String receive_account_name;
//
//    @ApiModelProperty("充值流水号")
//    @NotEmpty(message = "充值流水号不可为空")
//    private String serial_number;
//
//    @ApiModelProperty("充值日期")
//    @NotEmpty(message = "充值日期不可为空")
//    private String date;
//
//    @ApiModelProperty("充值金额")
//    @NotEmpty(message = "充值金额不可为空")
//    private String amount;
}
