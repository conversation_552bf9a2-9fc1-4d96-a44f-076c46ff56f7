package com.bilibili.crm.platform.portal.webapi.non.standard.vo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * 年度招商目标设置查询VO
 *
 * <AUTHOR>
 * date 2023/10/16 10:58.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ProjectPeriodicityQueryResultVO implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 周期性项目周期类型:枚举=年度/季度
     */
    private String periodicity_type;

    /**
     * 周期性项目类型名称
     */
    private String project_name;

    /**
     * 周期性项目状态:0是有效，1是无效
     */
    private Integer project_status;
    /**
     * 周期性项目状态名称:0是有效，1是无效
     */
    private String project_status_name;
    /**
     * 适用周期开始日期
     */
    private String periodicity_begin_time;

    /**
     * 适用周期结束日期
     */
    private String periodicity_end_time;

    /**
     * 周期性项目类型备注
     */
    private String note;
    private static final long serialVersionUID = 4886656975139218828L;
}

