package com.bilibili.crm.platform.portal.openapi.account.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotBlank;

import javax.validation.constraints.NotNull;

/**
 * <AUTHOR>
 * @date 2018/3/13
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class EffectAccountVo {
    @ApiModelProperty(notes = "平台")
    @NotNull(message = "平台不可为空")
    private Integer platform;

    @ApiModelProperty(notes = "主站MID")
    @NotNull(message = "mid不可为空")
    private Long mid;

    @ApiModelProperty(notes = "公司名称")
    @NotBlank(message = "公司名称不可为空")
    private String company_name;

    @ApiModelProperty(notes = "账号名称")
    private String account_name;
}
