package com.bilibili.crm.platform.portal.webapi.expenditure.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.CollectionWithDefaultPoolHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.commercialorder.api.order.enums.OrderStatus;
import com.bilibili.commercialorder.soa.order.dto.OrderInfoForCrmSynDto;
import com.bilibili.commercialorder.soa.order.dto.QueryOrderInfoForCrmReqDto;
import com.bilibili.commercialorder.soa.order.dto.SoaOrderInfoDto;
import com.bilibili.commercialorder.soa.order.service.ISoaCmOrderService;
import com.bilibili.crm.platform.api.account.dto.CrmDepartmentDto;
import com.bilibili.crm.platform.api.account.service.ICrmDepartmentService;
import com.bilibili.crm.platform.api.contract.dto.ContractBaseDto;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.cost_management.dto.*;
import com.bilibili.crm.platform.api.cost_management.service.IBTPIntegrationService;
import com.bilibili.crm.platform.api.cost_management.service.ICostManagementDataStrategyService;
import com.bilibili.crm.platform.api.expenditure.dto.ExpenditureDto;
import com.bilibili.crm.platform.api.expenditure.dto.ExpenditureTypeDto;
import com.bilibili.crm.platform.api.expenditure.dto.QueryExpenditureParam;
import com.bilibili.crm.platform.api.expenditure.dto.QueryExpenditureTypeDto;
import com.bilibili.crm.platform.api.expenditure.service.IExpenditureService;
import com.bilibili.crm.platform.api.expenditure.service.IExpenditureTypeService;
import com.bilibili.crm.platform.api.non.standard.dto.ProjectItemPackageDto;
import com.bilibili.crm.platform.api.non.standard.service.INonStandardPackageService;
import com.bilibili.crm.platform.api.order.dto.OrderBaseDto;
import com.bilibili.crm.platform.api.order.dto.QueryOrderDto;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.api.pickup.INewPickupSettleService;
import com.bilibili.crm.platform.api.pickup.dto.NewPickupSettleDto;
import com.bilibili.crm.platform.api.pickup.dto.PrePayPickupOrderQueryDto;
import com.bilibili.crm.platform.api.pickup.dto.QueryPickupSettleDto;
import com.bilibili.crm.platform.api.rbac.IDomainUserService;
import com.bilibili.crm.platform.biz.common.PickupSettleTypeEnum;
import com.bilibili.crm.platform.biz.dao.CrmProjectItemPackageDao;
import com.bilibili.crm.platform.biz.pickup.component.PrePayPickupOrderRepo;
import com.bilibili.crm.platform.biz.po.CrmOrderExtraPo;
import com.bilibili.crm.platform.biz.po.CrmProjectItemPackagePo;
import com.bilibili.crm.platform.biz.po.CrmProjectItemPackagePoExample;
import com.bilibili.crm.platform.biz.po.PrePayPickupOrderPo;
import com.bilibili.crm.platform.biz.repo.CrmOrderExtraRepo;
import com.bilibili.crm.platform.biz.service.ContractService;
import com.bilibili.crm.platform.biz.service.order.OrderUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.portal.webapi.expenditure.vo.*;
import com.bilibili.crm.platform.utils.AdUtils;
import com.bilibili.crm.platform.utils.MathUtils;
import com.bilibili.crm.platform.utils.PriceMathUtils;
import com.bilibili.rbac.api.service.IRoleService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Base64Utils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-05-24 20:05:35
 * @description:
 **/
@Slf4j
@Service
public class ExpenditureConverter {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Resource
    private INonStandardPackageService nonStandardPackageService;
    @Resource
    private ContractService contractService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private ICrmDepartmentService crmDepartmentService;
    @Autowired
    private IExpenditureTypeService expenditureTypeService;
    @Autowired
    IBfsService bfsService;
    @Autowired
    private ICostManagementDataStrategyService iCostManagementDataStrategyService;

    @Autowired
    private CrmOrderExtraRepo crmOrderExtraRepo;

    @Autowired
    private IBTPIntegrationService ibtpIntegrationService;

    @Autowired
    private IRoleService roleService;

    @Autowired
    private Integer tenantId;

    @Autowired
    private IDomainUserService domainUserService;

    @Autowired
    private INewPickupSettleService newPickupSettleService;

    @Autowired
    private ISoaCmOrderService iSoaCmOrderService;

    @Autowired
    private PrePayPickupOrderRepo prePayPickupOrderRepo;

    @Autowired
    private CrmProjectItemPackageDao crmProjectItemPackageDao;

    @Autowired
    private ISoaCmOrderService soaCmOrderService;

    @Autowired
    private IExpenditureService expenditureService;

    @Value("${cost.role.department.map:{}}")
    private String roleDepartmentMap;

    public List<ContractExpenditureCostVo> costDtoToVo(List<CostManagementExpenditureResultDto> costList, List<CostManagementIncomeResultDto> incomeList) {
        if (CollectionUtils.isEmpty(costList)) {
            return new ArrayList<>();
        }
        Map<Integer, List<CostManagementExpenditureResultDto>> projectCostMap = costList.stream().collect(Collectors.groupingBy(CostManagementExpenditureResultDto::getPackageId));
        Map<Integer, List<CostManagementIncomeResultDto>> projectIncomeMap = incomeList.stream().collect(Collectors.groupingBy(CostManagementIncomeResultDto::getPackageId));
        List<Integer> packageIds = costList.stream().map(CostManagementExpenditureResultDto::getPackageId).distinct().collect(Collectors.toList());
        Map<Integer, ProjectItemPackageDto> packageDtoMap = nonStandardPackageService.queryPackageByIdList(packageIds);
        List<ContractExpenditureCostVo> result = new ArrayList<>();
        for (Integer packageId : packageIds) {
            ProjectItemPackageDto packageDto = packageDtoMap.getOrDefault(packageId, new ProjectItemPackageDto());
            List<CostManagementExpenditureResultDto> theCostList = projectCostMap.getOrDefault(packageId, new ArrayList<>());
            List<CostManagementIncomeResultDto> theIncomeList = projectIncomeMap.getOrDefault(packageId, new ArrayList<>());
            ContractExpenditureCostVo costVo = new ContractExpenditureCostVo();
            costVo.setRelate_package_id(packageId);
            costVo.setRelate_package_name(packageDto.getName());
            if (packageId == 0) {
                costVo.setRelate_package_name("无项目");
            }
            costVo.setRelate_income(Utils.fromFenToYuan(theIncomeList.stream().mapToLong(CostManagementIncomeResultDto::getEstimatedIncome).sum()));
            costVo.setRelate_cost(Utils.fromFenToYuan(theCostList.stream().mapToLong(CostManagementExpenditureResultDto::getCost).sum()));
            costVo.setChecking_amount(Utils.fromFenToYuan(theIncomeList.stream().mapToLong(CostManagementIncomeResultDto::getAccruedIncome).sum()));
            costVo.setAccrued_cost(Utils.fromFenToYuan(theCostList.stream().mapToLong(CostManagementExpenditureResultDto::getAccruedCost).sum()));
            costVo.setWritten_off_cost(Utils.fromFenToYuan(theCostList.stream().mapToLong(CostManagementExpenditureResultDto::getWrittenOffCost).sum()));
            costVo.setEstimate_cost_rate(MathUtils.calculatePercentageNegative(costVo.getRelate_cost(), costVo.getRelate_income()));
            costVo.setAccrued_cost_rate(MathUtils.calculatePercentageNegative(costVo.getAccrued_cost(), costVo.getChecking_amount()));
            costVo.setWritten_off_rate(MathUtils.calculatePercentageNegative(costVo.getWritten_off_cost(), costVo.getChecking_amount()));
            result.add(costVo);
        }
        result = result.stream().sorted(Comparator.comparing(ContractExpenditureCostVo::getRelate_package_id).reversed()).collect(Collectors.toList());
        result.add(0, buildTotal(result));
        return result;
    }

    public ContractExpenditureCostVo buildTotal(List<ContractExpenditureCostVo> result) {
        ContractExpenditureCostVo costVo = new ContractExpenditureCostVo();
        costVo.setRelate_package_name("总计");
        costVo.setRelate_income(result.stream().map(ContractExpenditureCostVo::getRelate_income).reduce(BigDecimal.ZERO, BigDecimal::add));
        costVo.setRelate_cost(result.stream().map(ContractExpenditureCostVo::getRelate_cost).reduce(BigDecimal.ZERO, BigDecimal::add));
        costVo.setChecking_amount(result.stream().map(ContractExpenditureCostVo::getChecking_amount).reduce(BigDecimal.ZERO, BigDecimal::add));
        costVo.setAccrued_cost(result.stream().map(ContractExpenditureCostVo::getAccrued_cost).reduce(BigDecimal.ZERO, BigDecimal::add));
        costVo.setWritten_off_cost(result.stream().map(ContractExpenditureCostVo::getWritten_off_cost).reduce(BigDecimal.ZERO, BigDecimal::add));
        costVo.setEstimate_cost_rate(MathUtils.calculatePercentageNegative(costVo.getRelate_cost(), costVo.getRelate_income()));
        costVo.setAccrued_cost_rate(MathUtils.calculatePercentageNegative(costVo.getAccrued_cost(), costVo.getChecking_amount()));
        costVo.setWritten_off_rate(MathUtils.calculatePercentageNegative(costVo.getWritten_off_cost(), costVo.getChecking_amount()));
        return costVo;
    }

    public QueryExpenditureParam buildQueryVo(ExpenditureQueryVo expenditureQueryVo, Operator operator) {
        List<Integer> departmentIds = new ArrayList<>();
        Integer contractId = null;
        if (expenditureQueryVo.getContract_number() != null) {
            ContractDto contractDto = contractService.getContractByContractNumber(expenditureQueryVo.getContract_number());
            contractId = contractDto.getId();
        }
        if (expenditureQueryVo.getQuery_type() == 1) {
            List<Integer> audit_status_list = Lists.newArrayList(NewExpenditureAuditStatus.WAITING_FOR_AUDIT.getCode(), NewExpenditureAuditStatus.REJECTED.getCode(), NewExpenditureAuditStatus.AUDITING.getCode());
            if (CollectionUtils.isEmpty(expenditureQueryVo.getAudit_status_list())) {
                expenditureQueryVo.setAudit_status_list(audit_status_list);
            } else {
                audit_status_list.retainAll(expenditureQueryVo.getAudit_status_list());
                if (CollectionUtils.isEmpty(audit_status_list)) {
                    audit_status_list.add(-1);
                }
                expenditureQueryVo.setAudit_status_list(audit_status_list);
            }
        }
        List<Integer> orderIds = new ArrayList<>();
        if (!StringUtils.isEmpty(expenditureQueryVo.getCrm_order_name())) {
            List<OrderBaseDto> orderBaseDtos = orderService.getBaseOrderDtos(QueryOrderDto.builder().explanation(expenditureQueryVo.getCrm_order_name()).build());
            if (!CollectionUtils.isEmpty(orderBaseDtos)) {
                orderIds = orderBaseDtos.stream().map(OrderBaseDto::getId).collect(Collectors.toList());
            } else {
                orderIds.add(-1);
            }
        }
        return QueryExpenditureParam.builder()
                .orderId(expenditureQueryVo.getCrm_order_id())
                .orderIds(orderIds)
                .departmentIds(departmentIds)
                .nameLike(expenditureQueryVo.getName())
                .types(expenditureQueryVo.getTypes())
                .secondTypes(expenditureQueryVo.getSecond_types())
                .expenditureStatus(expenditureQueryVo.getExpenditure_status_list())
                .auditStatus(expenditureQueryVo.getAudit_status_list())
                .source(CollectionUtils.isEmpty(expenditureQueryVo.getSources()) ? Lists.newArrayList(1, 2, 3, 4) : expenditureQueryVo.getSources())
                .contractIds(contractId == null ? null : Lists.newArrayList(contractId))
                .operators(StringUtils.isEmpty(expenditureQueryVo.getOperator()) ? new ArrayList<>() : Lists.newArrayList(expenditureQueryVo.getOperator()))
                .build();

    }

    public List<ExpenditureExportVo> voToExport(List<ExpenditureVo> expenditureVos) {
        if (CollectionUtils.isEmpty(expenditureVos)) {
            return new ArrayList<>();
        }
        List<ExpenditureExportVo> result = new ArrayList<>();
        for (ExpenditureVo expenditureVo : expenditureVos) {
            ExpenditureExportVo expenditureExportVo = new ExpenditureExportVo();
            BeanUtils.copyProperties(expenditureVo, expenditureExportVo);
            expenditureExportVo.setCtime(CrmUtils.formatDateCanNull(expenditureVo.getCtime(), CrmUtils.YYYYMMDDHHMMSS));
            expenditureExportVo.setType_desc(expenditureVo.getType_desc() + "(" + expenditureVo.getSecond_type_desc() + ")");
            result.add(expenditureExportVo);
        }
        return result;
    }


    public List<ExpenditureExportVo> dtoToExportForList(List<ExpenditureDto> dtoList) {
        List<ExpenditureExportVo> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(dtoList)) {
            return result;
        }
        List<Integer> orderIds = dtoList.stream().map(ExpenditureDto::getCrmOrderId).distinct().collect(Collectors.toList());
        List<Integer> departmentIds = dtoList.stream().map(ExpenditureDto::getDepartmentId).distinct().collect(Collectors.toList());
        List<String> btpBatchCodes = dtoList.stream().map(ExpenditureDto::getBtpBatchCode).distinct().collect(Collectors.toList());
        List<Integer> pickUpOrderIds = dtoList.stream().filter(a -> Objects.equals(a.getSource(), ExpenditureSourceType.PICKUP_PLATFORM.getCode()))
                .map(ExpenditureDto::getCrmOrderId).filter(Utils::isPositive).collect(Collectors.toList());
        // 未绑定crm order id的花火订单
        List<Long> pickUpOrderNos = dtoList.stream().filter(a -> Objects.equals(a.getSource(), ExpenditureSourceType.PICKUP_PLATFORM.getCode()))
                .filter(dto -> dto.getCrmOrderId() == 0 && dto.getPickUpOrderNo() != 0).map(ExpenditureDto::getPickUpOrderNo).distinct().collect(Collectors.toList());

        Map<Integer, OrderBaseDto> orderMap = orderService.getOrderMapInCrmOrderIds(orderIds);
        Map<Integer, CrmOrderExtraPo> crmOrderExtraPoMap = crmOrderExtraRepo.queryMapByOrderIds(orderIds);
        List<CostManagementIncomeResultDto> incomeList = iCostManagementDataStrategyService.queryIncomeDataInfo(CostManagementQueryDto.builder()
                .orderIds(orderIds)
                .build());
        Map<Integer, List<CostManagementIncomeResultDto>> incomeMap = incomeList.stream().collect(Collectors.groupingBy(CostManagementIncomeResultDto::getOrderId));

        List<Integer> contractIds = orderMap.values().stream().map(OrderBaseDto::getCrmContractId).distinct().collect(Collectors.toList());
        List<BTPPurchaseBatchDto> btpPurchaseBatchDtos = ibtpIntegrationService.getBTPPurchaseBatchByCodes(btpBatchCodes);
        Map<String, BTPPurchaseBatchDto> btpPurchaseBatchDtoMap = btpPurchaseBatchDtos.stream().collect(Collectors.toMap(BTPPurchaseBatchDto::getCode, Function.identity(), (a, b) -> a));

        CrmProjectItemPackagePoExample packagePoExample = new CrmProjectItemPackagePoExample();
        packagePoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmProjectItemPackagePo> packPoList = crmProjectItemPackageDao.selectByExample(packagePoExample);
        Map<Integer, CrmProjectItemPackagePo> packageDtoMap = packPoList.stream().collect(Collectors.toMap(CrmProjectItemPackagePo::getId, Function.identity()));
        List<String> projectBtpCodes = packageDtoMap.values().stream().map(CrmProjectItemPackagePo::getBtpProjectCode).collect(Collectors.toList());
        List<BTPProjectDto> btpProjectDtoList = ibtpIntegrationService.getAllBTPProjectsByParam(BTPProjectPageRequestDto.builder()
                .projectCodeList(projectBtpCodes)
                .build());
        Map<String, BTPProjectDto> itemProjectDtoMap = btpProjectDtoList.stream().collect(Collectors.toMap(BTPProjectDto::getProjectCode, Function.identity(), (a, b) -> a));

        Map<Integer, ContractBaseDto> contractDtoMap = contractService.queryContractBaseDtoMapInIds(contractIds);
        Map<Integer, CrmDepartmentDto> departmentMap = crmDepartmentService.queryDepartmentMapByIds(departmentIds);
        List<ExpenditureTypeDto> types = expenditureTypeService.query(QueryExpenditureTypeDto.builder().build());
        Map<Integer, ExpenditureTypeDto> expenditureTypeDtoMap = types.stream().collect(Collectors.toMap(ExpenditureTypeDto::getId, t -> t));


        List<PrePayPickupOrderPo> allPrePayPickupOrderPos = prePayPickupOrderRepo.queryPrePayPickupOrderList(PrePayPickupOrderQueryDto.builder().build());
        Map<Long, PrePayPickupOrderPo> allPrePayPickupOrderMap = allPrePayPickupOrderPos.stream().collect(Collectors.toMap(PrePayPickupOrderPo::getOrderNo, Function.identity()));

        Map<Integer, OrderInfoForCrmSynDto> pickUpCrmOrderIdOrderInfoSynDtoMap = new HashMap<>();
        List<OrderInfoForCrmSynDto> pickupOrderNoInfos = new ArrayList<>();
        CollectionWithDefaultPoolHelper.processInBatches(pickUpOrderIds, 100, (ids) -> {
            List<SoaOrderInfoDto> pickUpOrderInfoList = soaCmOrderService.queryOrderInfoByCrmOrderIds(ids);
            Map<Long, Integer> pickUpOrderNoCrmOrderIdMap = pickUpOrderInfoList.stream().collect(Collectors.toMap(SoaOrderInfoDto::getOrderNo, SoaOrderInfoDto::getCrmContractOrderId));
            if (!CollectionUtils.isEmpty(pickUpOrderInfoList)) {
                List<Long> nos = pickUpOrderInfoList.stream().map(SoaOrderInfoDto::getOrderNo).filter(Objects::nonNull).distinct().collect(Collectors.toList());
                List<OrderInfoForCrmSynDto> orderInfoForCrmSynDtoList = soaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder().orderNos(nos).isQueryTask(false).isQueryUpper(false).isQuerySettle(false).build());
                if (!CollectionUtils.isEmpty(orderInfoForCrmSynDtoList)) {
                    Map<Long, OrderInfoForCrmSynDto> orderInfoForCrmSynDtoMap = orderInfoForCrmSynDtoList.stream().collect(Collectors.toMap(OrderInfoForCrmSynDto::getOrderNo, Function.identity()));
                    orderInfoForCrmSynDtoMap.keySet().forEach(k -> {
                        pickUpCrmOrderIdOrderInfoSynDtoMap.put(pickUpOrderNoCrmOrderIdMap.get(k), orderInfoForCrmSynDtoMap.get(k));

                    });
                }
            }
        });
        CollectionWithDefaultPoolHelper.processInBatches(pickUpOrderNos, 100, (nos) -> {
            List<OrderInfoForCrmSynDto> orderInfoForCrmSynDtoList = soaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder().orderNos(nos).isQueryTask(false).isQueryUpper(false).isQuerySettle(false).build());
            if (!CollectionUtils.isEmpty(orderInfoForCrmSynDtoList)) {
                pickupOrderNoInfos.addAll(orderInfoForCrmSynDtoList);
            }
        });

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderNoOrderInfoSynDtoMap = pickupOrderNoInfos.stream()
                .collect(Collectors.toMap(
                        OrderInfoForCrmSynDto::getOrderNo,
                        Function.identity(),
                        (orderInfoForCrmSynDto1, orderInfoForCrmSynDto2) -> {
                            log.info("duplicate key found! {} @ {}", orderInfoForCrmSynDto1, orderInfoForCrmSynDto2);
                            return orderInfoForCrmSynDto1;
                        }));
        for (ExpenditureDto dto : dtoList) {
            OrderBaseDto orderBaseDto = orderMap.getOrDefault(dto.getCrmOrderId(), OrderBaseDto.builder().crmContractId(0).build());
            ContractBaseDto contractDto = contractDtoMap.getOrDefault(orderBaseDto.getCrmContractId(), ContractBaseDto.builder().build());
            CrmDepartmentDto crmDepartmentDto = departmentMap.getOrDefault(dto.getDepartmentId(), CrmDepartmentDto.builder().build());
            CrmOrderExtraPo crmOrderExtraPo = crmOrderExtraPoMap.getOrDefault(dto.getCrmOrderId(), CrmOrderExtraPo.builder().projectItemId(0).build());
            CrmProjectItemPackagePo packageDto = packageDtoMap.getOrDefault(crmOrderExtraPo.getProjectItemId(), CrmProjectItemPackagePo.builder().btpProjectCode("").build());
            BTPProjectDto btpProjectDto = itemProjectDtoMap.getOrDefault(packageDto.getBtpProjectCode(), new BTPProjectDto());
            BTPPurchaseBatchDto batchDto = btpPurchaseBatchDtoMap.get(dto.getBtpBatchCode());
            ExpenditureTypeDto firstTypeDto = expenditureTypeDtoMap.getOrDefault(dto.getType(), ExpenditureTypeDto.builder().build());
            ExpenditureTypeDto secondTypeDto = expenditureTypeDtoMap.getOrDefault(dto.getSecondType(), ExpenditureTypeDto.builder().build());

            OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpCrmOrderIdOrderInfoSynDtoMap.getOrDefault(dto.getCrmOrderId(), OrderInfoForCrmSynDto.builder().build());
            OrderInfoForCrmSynDto orderInfoForNoSynDto = pickUpOrderNoOrderInfoSynDtoMap.getOrDefault(dto.getPickUpOrderNo(), OrderInfoForCrmSynDto.builder().build());
            Integer expenditureStatus = expenditureService.judgeExpenditureStatus(dto, orderMap, pickUpCrmOrderIdOrderInfoSynDtoMap, pickUpOrderNoOrderInfoSynDtoMap);
            Integer expenditureAuditStatus = expenditureService.judgeExpenditureAuditStatus(dto, orderMap.get(dto.getCrmOrderId()));

            ExpenditureExportVo expenditureExportVo = new ExpenditureExportVo();
            expenditureExportVo.setId(dto.getId());
            expenditureExportVo.setName(dto.getName());
            expenditureExportVo.setDepartment_name(crmDepartmentDto.getName());
            expenditureExportVo.setExpenditure_status_desc(ExpenditureStatus.getByCode(expenditureStatus).getDescription());
            expenditureExportVo.setAudit_status_desc(NewExpenditureAuditStatus.getByCode(expenditureAuditStatus).getDescription());
            expenditureExportVo.setCrm_order_id(dto.getCrmOrderId());
            expenditureExportVo.setOrder_name(orderBaseDto.getExplanation());
            expenditureExportVo.setCrm_order_type(Utils.isPositive(orderBaseDto.getType()) ? CrmOrderType.getByCode(orderBaseDto.getType()).getDesc() : "");
            expenditureExportVo.setChecking_amount(Utils.fromFenToYuan(incomeMap.getOrDefault(dto.getCrmOrderId(), new ArrayList<>()).stream().mapToLong(CostManagementIncomeResultDto::getAccruedIncome).sum()));
            expenditureExportVo.setRelate_income(Utils.fromFenToYuan(incomeMap.getOrDefault(dto.getCrmOrderId(), new ArrayList<>()).stream().mapToLong(CostManagementIncomeResultDto::getEstimatedIncome).sum()));
            expenditureExportVo.setRelate_package_id(packageDto.getId());
            expenditureExportVo.setRelate_package_name(packageDto.getName());
            expenditureExportVo.setContract_number(String.valueOf(contractDto.getContractNumber()));
            expenditureExportVo.setContract_name(contractDto.getName());
            expenditureExportVo.setInvoice_number(dto.getInvoiceNumber());
            expenditureExportVo.setOperator(dto.getOperator());
            expenditureExportVo.setSource_desc(ExpenditureSourceType.getByCode(dto.getSource()).getDescription());
            expenditureExportVo.setWritten_off_cost(Utils.fromFenToYuan(dto.getWrittenOffCost()));
            expenditureExportVo.setAccrued_cost(Utils.fromFenToYuan(dto.getAccruedCost()));

            expenditureExportVo.setCtime(Objects.equals(dto.getSource(), ExpenditureSourceType.BTP.getCode()) ? CrmUtils.formatDateCanNull(dto.getBtpTime(), CrmUtils.YYYYMMDDHHMMSS) : CrmUtils.formatDateCanNull(dto.getCtime(), CrmUtils.YYYYMMDDHHMMSS));
            expenditureExportVo.setType_desc(firstTypeDto.getName() + "(" + secondTypeDto.getName() + ")");
            if (Objects.equals(dto.getSource(), ExpenditureSourceType.PICKUP_PLATFORM.getCode()) && Objects.nonNull(orderInfoForCrmSynDto)) {
                BigDecimal platformTotalExpenses = orderInfoForCrmSynDto.getPlatformTotalExpenses() == null ? BigDecimal.ZERO : orderInfoForCrmSynDto.getPlatformTotalExpenses();
                expenditureExportVo.setAmount(platformTotalExpenses);
                if (OrderStatus.ORDER_COMPLETE_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                    expenditureExportVo.setAccrued_cost(platformTotalExpenses);
                    expenditureExportVo.setWritten_off_cost(platformTotalExpenses);
                }
            } else if (Objects.equals(dto.getSource(), ExpenditureSourceType.PICKUP_PLATFORM.getCode()) && Objects.nonNull(orderInfoForNoSynDto)) {
                BigDecimal platformTotalExpenses = orderInfoForNoSynDto.getPlatformTotalExpenses() == null ? BigDecimal.ZERO : orderInfoForNoSynDto.getPlatformTotalExpenses();
                expenditureExportVo.setAmount(platformTotalExpenses);
                PrePayPickupOrderPo prePayPickupOrderPo = allPrePayPickupOrderMap.get(orderInfoForNoSynDto.getOrderNo());
                if (Objects.nonNull(prePayPickupOrderPo) && Utils.isPositive(prePayPickupOrderPo.getCrmProjectId())) {
                    expenditureExportVo.setRelate_package_id(prePayPickupOrderPo.getCrmProjectId().intValue());
                    expenditureExportVo.setRelate_package_name(packageDtoMap.getOrDefault(prePayPickupOrderPo.getCrmProjectId().intValue(), CrmProjectItemPackagePo.builder().build()).getName());
                } else {
                    expenditureExportVo.setRelate_package_id(null);
                    expenditureExportVo.setRelate_package_name(null);
                }
                if (OrderStatus.ORDER_COMPLETE_STATUS.contains(orderInfoForNoSynDto.getOrderStatus())) {
                    expenditureExportVo.setAccrued_cost(platformTotalExpenses);
                    expenditureExportVo.setWritten_off_cost(platformTotalExpenses);
                }
            }

            if (Objects.equals(dto.getSource(), ExpenditureSourceType.BTP.getCode())) {
                if (Objects.nonNull(batchDto)) {
                    expenditureExportVo.setBtp_project_name(batchDto.getProjectName());
                    expenditureExportVo.setBtp_status(Objects.isNull(batchDto.getStatus()) ? null : BtpStatusType.getByCode(batchDto.getStatus()).getDesc());
                } else {
                    expenditureExportVo.setBtp_project_name(btpProjectDto.getProjectName());
                }
            }
            if (Objects.isNull(expenditureExportVo.getAmount())) {
                expenditureExportVo.setAmount(Utils.fromFenToYuan(dto.getPrice()));//兜底逻辑
            }
            result.add(expenditureExportVo);
        }

        return result;
    }

    public List<ExpenditureVo> buildExpenditureVos(List<ExpenditureDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        Set<Integer> orderIds = new HashSet<>();
        Set<Integer> departmentIds = new HashSet<>();
        Set<Integer> firstTypeIds = new HashSet<>();
        Set<Integer> secondTypeIds = new HashSet<>();
        dtos.forEach(dto -> {
            if (Utils.isPositive(dto.getCrmOrderId())) {
                orderIds.add(dto.getCrmOrderId());
            }
            if (Utils.isPositive(dto.getDepartmentId())) {
                departmentIds.add(dto.getDepartmentId());
            }
            if (Utils.isPositive(dto.getType())) {
                firstTypeIds.add(dto.getType());
            }
            if (Utils.isPositive(dto.getSecondType())) {
                secondTypeIds.add(dto.getSecondType());
            }
        });
        //关联订单map
        Map<Integer, OrderBaseDto> orderMap = orderService.getOrderMapInCrmOrderIds(Lists.newArrayList(orderIds));
        List<CostManagementIncomeResultDto> incomeList = new ArrayList<>();
        Map<Integer, CrmOrderExtraPo> crmOrderExtraPoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(orderIds)) {
            incomeList = iCostManagementDataStrategyService.queryIncomeDataInfo(CostManagementQueryDto.builder()
                    .orderIds(new ArrayList<>(orderIds))
                    .build());
            crmOrderExtraPoMap = crmOrderExtraRepo.queryMapByOrderIds(new ArrayList<>(orderIds));
        }
        List<Integer> packageIds = crmOrderExtraPoMap.values().stream().map(CrmOrderExtraPo::getProjectItemId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, ProjectItemPackageDto> packageDtoMap = nonStandardPackageService.queryPackageByIdList(packageIds);
        List<String> projectBtpCodes = packageDtoMap.values().stream().map(ProjectItemPackageDto::getBtpProjectCode).collect(Collectors.toList());
        List<String> btpBatchCodes = dtos.stream().map(ExpenditureDto::getBtpBatchCode).distinct().collect(Collectors.toList());
        List<BTPPurchaseBatchDto> btpPurchaseBatchDtos = new ArrayList<>();
        Map<String, BTPPurchaseBatchDto> btpPurchaseBatchDtoMap = new HashMap<>();

        if (!CollectionUtils.isEmpty(btpBatchCodes)) {
            btpPurchaseBatchDtos = ibtpIntegrationService.getBTPPurchaseBatchByCodes(btpBatchCodes);
            btpPurchaseBatchDtoMap = getBTPPurchaseBatches(btpBatchCodes);
        }

        List<BTPProjectDto> btpProjectDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(projectBtpCodes)) {
            btpProjectDtoList = ibtpIntegrationService.getAllBTPProjectsByParam(BTPProjectPageRequestDto.builder()
                    .projectCodeList(projectBtpCodes)
                    .build());
        }

        Map<String, BTPPurchaseBatchDto> btpPurchaseBatchDtosMap = btpPurchaseBatchDtos.stream().collect(Collectors.toMap(BTPPurchaseBatchDto::getCode, Function.identity(), (a, b) -> a));
        Map<String, BTPProjectDto> btpProjectDtoMap = btpProjectDtoList.stream().collect(Collectors.toMap(BTPProjectDto::getProjectCode, Function.identity(), (a, b) -> a));
        Map<Integer, List<CostManagementIncomeResultDto>> incomeMap = incomeList.stream().collect(Collectors.groupingBy(CostManagementIncomeResultDto::getOrderId));
        List<Integer> contractIds = orderMap.values().stream().map(OrderBaseDto::getCrmContractId).distinct().collect(Collectors.toList());
        Map<Integer, ContractDto> contractDtoMap = contractService.queryContractInContractIds(contractIds);
        Map<Integer, CrmDepartmentDto> departmentMap = crmDepartmentService.queryDepartmentMapByIds(Lists.newArrayList(departmentIds));
        List<Integer> allTypeIds = Lists.newArrayList(firstTypeIds);
        allTypeIds.addAll(Lists.newArrayList(secondTypeIds));
        List<ExpenditureTypeDto> types = expenditureTypeService.query(QueryExpenditureTypeDto.builder().ids(allTypeIds).build());
        Map<Integer, ExpenditureTypeDto> expenditureTypeDtoMap = types.stream().collect(Collectors.toMap(ExpenditureTypeDto::getId, t -> t));
        List<ExpenditureVo> result = new ArrayList<>();
        List<Integer> pickupCrmOrderIds = dtos.stream().filter(a -> a.getSource().equals(ExpenditureSourceType.PICKUP_PLATFORM.getCode()))
                .map(ExpenditureDto::getCrmOrderId)
                .filter(crmOrderId -> crmOrderId != 0)
                .collect(Collectors.toList());

        Map<Integer, OrderInfoForCrmSynDto> pickUpCrmOrderIdOrderInfoSynDtoMap = newPickupSettleService.queryByCrmOrderIds(pickupCrmOrderIds);
        // 未绑定crm order id的花火订单
        List<Long> pickUpOrderNos = dtos.stream().filter(dto -> dto.getCrmOrderId() == 0 && dto.getPickUpOrderNo() != 0).map(ExpenditureDto::getPickUpOrderNo).distinct().collect(Collectors.toList());

        Map<Long, NewPickupSettleDto> pickUpOrderNoNewPickupSettleDtoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pickUpOrderNos)) {
            pickUpOrderNoNewPickupSettleDtoMap = newPickupSettleService.getPickupSettleList(QueryPickupSettleDto.builder().pickupOrderNos(pickUpOrderNos).build()).stream()
                    .collect(Collectors.toMap(
                            NewPickupSettleDto::getPickupOrderNo,
                            Function.identity(),
                            (newPickupSettleDto1, newPickupSettleDto2) -> {
                                log.info("duplicate key found! {} @ {}", newPickupSettleDto1, newPickupSettleDto2);
                                return newPickupSettleDto1;
                            })
                    );
        }

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderNoOrderInfoSynDtoMap = new HashMap<>();
        if (!CollectionUtils.isEmpty(pickUpOrderNos)) {
            pickUpOrderNoOrderInfoSynDtoMap = iSoaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder().orderNos(pickUpOrderNos).isQueryTask(true).isQueryUpper(true).isQuerySettle(true)
                            .build())
                    .stream()
                    .collect(Collectors.toMap(
                            OrderInfoForCrmSynDto::getOrderNo,
                            Function.identity(),
                            (orderInfoForCrmSynDto1, orderInfoForCrmSynDto2) -> {
                                log.info("duplicate key found! {} @ {}", orderInfoForCrmSynDto1, orderInfoForCrmSynDto2);
                                return orderInfoForCrmSynDto1;
                            }));
        }

        for (ExpenditureDto dto : dtos) {
            //花火跟虚拟金支出项 跟审核状态要跟订单联动
            Integer expenditureStatus = expenditureService.judgeExpenditureStatus(dto, orderMap, pickUpCrmOrderIdOrderInfoSynDtoMap, pickUpOrderNoOrderInfoSynDtoMap);
            Integer expenditureAuditStatus = expenditureService.judgeExpenditureAuditStatus(dto, orderMap.get(dto.getCrmOrderId()));

            BTPPurchaseBatchDto btpPurchaseBatchDto = btpPurchaseBatchDtoMap.getOrDefault(dto.getBtpBatchCode(), new BTPPurchaseBatchDto());
            BTPPurchaseDetailDto btpPurchaseDetailDto = getBTPPurchaseDetail(dto.getBtpBatchCode(), dto.getBtpDetailCode(), btpPurchaseBatchDtoMap);

            Timestamp ctime = dto.getCtime();
            String operator = dto.getOperator();
            if (dto.getSource().equals(ExpenditureSourceType.BTP.getCode())) {
                if (!StringUtils.isEmpty(btpPurchaseBatchDto.getApprovedTime())) {
                    ctime = Timestamp.valueOf(LocalDateTime.from(DATE_TIME_FORMATTER.parse(btpPurchaseBatchDto.getApprovedTime())));
                }

                operator = btpPurchaseBatchDto.getCreator();
            }

            ExpenditureVo vo = ExpenditureVo.builder()
                    .id(dto.getId())
                    .crm_contract_id(dto.getCrmContractId())
                    .name(dto.getSource().equals(ExpenditureSourceType.BTP.getCode()) ? btpPurchaseDetailDto.getGoodsName() + "-" + btpPurchaseBatchDto.getCode() + "-" + ctime
                            : dto.getName())
                    .invoice_number(dto.getInvoiceNumber())
                    .crm_order_id(dto.getCrmOrderId())
                    .type(dto.getType())
                    .second_type(dto.getSecondType())
                    .department_id(dto.getDepartmentId())
                    .remark(dto.getRemark())
                    .operator(operator)
                    .status(dto.getStatus())
                    .ctime(CrmUtils.safeGetTime(ctime))
                    .begin_time(dto.getBeginTime())
                    .end_time(dto.getEndTime())
                    .pay_flag(dto.getPayFlag())
                    .ad_cost_flag(dto.getAdCostFlag())
                    .expenditure_status(expenditureStatus)
                    .expenditure_status_desc(ExpenditureStatus.getByCode(expenditureStatus).getDescription())
                    .audit_status(expenditureAuditStatus)
                    .audit_status_desc(NewExpenditureAuditStatus.getByCode(expenditureAuditStatus).getDescription())
                    .source(dto.getSource())
                    .source_desc(ExpenditureSourceType.getByCode(dto.getSource()).getDescription())
                    .btp_batch_code(dto.getBtpBatchCode())
                    .btp_project_code(dto.getBtpProjectCode())
                    .btp_batch_version(dto.getBtpBatchVersion())
                    .btp_category_name(dto.getBtpCategoryName())
                    .btp_budget_purpose_name(dto.getBtpBudgetPurposeName())
                    .written_off_time(CrmUtils.safeGetTime(dto.getWrittenOffTime()))
                    .written_off_cost(Utils.fromFenToYuan(dto.getWrittenOffCost()))
                    .accrued_cost(Utils.fromFenToYuan(dto.getAccruedCost()))
                    .estimated_cost(Utils.fromFenToYuan(dto.getEstimatedCost()))
                    .build();

            OrderBaseDto orderBaseDto = orderMap.getOrDefault(dto.getCrmOrderId(), OrderBaseDto.builder().crmContractId(0).build());
            ContractDto contractDto = contractDtoMap.getOrDefault(orderBaseDto.getCrmContractId(), new ContractDto());
            CrmOrderExtraPo crmOrderExtraPo = crmOrderExtraPoMap.getOrDefault(dto.getCrmOrderId(), CrmOrderExtraPo.builder().projectItemId(0).build());
            ProjectItemPackageDto packageDto = packageDtoMap.getOrDefault(crmOrderExtraPo.getProjectItemId(), ProjectItemPackageDto.builder().btpProjectCode("").build());
            BTPProjectDto btpProjectDto = btpProjectDtoMap.getOrDefault(packageDto.getBtpProjectCode(), new BTPProjectDto());
            BTPPurchaseBatchDto batchDto = btpPurchaseBatchDtosMap.get(dto.getBtpBatchCode());
            Integer orderType = orderBaseDto.getType();
            Long orderAmount = orderBaseDto.getAmount();
            String singleExpenditureRate = (Utils.isPositive(dto.getPrice()) && Utils.isPositive(orderAmount)) ? AdUtils.getRate(dto.getPrice(), orderAmount) + "%" : "";
            CrmDepartmentDto departmentDto = departmentMap.getOrDefault(dto.getDepartmentId(), new CrmDepartmentDto());
            ExpenditureTypeDto firstTypeDto = expenditureTypeDtoMap.getOrDefault(dto.getType(), ExpenditureTypeDto.builder().build());
            ExpenditureTypeDto secondTypeDto = expenditureTypeDtoMap.getOrDefault(dto.getSecondType(), ExpenditureTypeDto.builder().build());
            vo.setCrm_order_type(Utils.isPositive(orderType) ? CrmOrderType.getByCode(orderType).getDesc() : "");

            vo.setCrm_order_amount(Utils.fromFenToYuan(orderAmount));
            //非标订单取实收
            if (null != orderBaseDto.getType() && orderBaseDto.getType().equals(CrmOrderType.NON_STANDARD.getCode())) {
                //分->元
                BigDecimal discountedAmount = Utils.fromFenToYuan(OrderUtils.getDiscountedAmount(orderBaseDto.getAmount(), orderBaseDto.getDiscount()));
                vo.setCrm_order_amount(discountedAmount);
            }

            vo.setOrder_name(orderBaseDto.getExplanation());
            vo.setOrder_begin_time(null == orderBaseDto.getBeginTime() ? null : orderBaseDto.getBeginTime().getTime());
            vo.setOrder_end_time(null == orderBaseDto.getEndTime() ? null : orderBaseDto.getEndTime().getTime());
            vo.setType_desc(firstTypeDto.getName());
            vo.setSecond_type_desc(secondTypeDto.getName());
            vo.setStatus_desc(Utils.isPositive(dto.getStatus()) ? ExpenditureAuditStatus.getByCode(dto.getStatus()).getDesc() : "");
            vo.setRate(singleExpenditureRate);
            vo.setDepartment_name(dto.getSource().equals(ExpenditureSourceType.BTP.getCode()) ? "外部采购" : departmentDto.getName());
            vo.setDepartment_type(dto.getSource().equals(ExpenditureSourceType.BTP.getCode()) ? 2 : departmentDto.getType());
            vo.setAmount(Utils.fromFenToYuan(dto.getPrice()));
            vo.setPay_flag_desc(Objects.equals(IsValid.TRUE.getCode(), dto.getPayFlag()) ? "是" : "否");
            vo.setAd_cost_flag_desc(Objects.equals(IsValid.TRUE.getCode(), dto.getAdCostFlag()) ? "是" : "否");
            vo.setCrm_contract_id(contractDto.getId());
            vo.setContract_number(contractDto.getContractNumber());
            vo.setContract_name(contractDto.getName());
            vo.setChecking_amount(Utils.fromFenToYuan(incomeMap.getOrDefault(dto.getCrmOrderId(), new ArrayList<>()).stream().mapToLong(CostManagementIncomeResultDto::getAccruedIncome).sum()));
            vo.setRelate_income(Utils.fromFenToYuan(incomeMap.getOrDefault(dto.getCrmOrderId(), new ArrayList<>()).stream().mapToLong(CostManagementIncomeResultDto::getEstimatedIncome).sum()));
            vo.setRelate_package_id(packageDto.getId());
            vo.setRelate_package_name(packageDto.getName());
            vo.setBtp_project_code(dto.getBtpProjectCode());
            vo.setBtp_batch_code(dto.getBtpBatchCode());
            vo.setBtp_batch_version(dto.getBtpBatchVersion());

            // 预付费花火商单
            if (dto.getPickUpOrderNo() != 0L && dto.getCrmOrderId() == 0) {
                vo.setCrm_contract_id(dto.getCrmContractId());
                vo.setContract_name(dto.getCrmContractName());
                vo.setContract_number(dto.getCrmContractNumber());
                Long pickUpOrderNo = dto.getPickUpOrderNo();
                PrePayPickupOrderPo po = prePayPickupOrderRepo.getPrePayPickupOrderPoByOrderNo(pickUpOrderNo);
                Long crmProjectId = (po == null ? null : po.getCrmProjectId());
                vo.setRelate_package_id(crmProjectId == null ? null : crmProjectId.intValue());
                if (crmProjectId == null || crmProjectId == 0L) {
                    vo.setRelate_package_name(null);
                } else {
                    CrmProjectItemPackagePo crmProjectItemPackagePo = crmProjectItemPackageDao.selectByPrimaryKey(crmProjectId.intValue());
                    if (crmProjectItemPackagePo != null) {
                        vo.setRelate_package_name(crmProjectItemPackagePo.getName());
                    }
                }

                NewPickupSettleDto newPickupSettleDto = pickUpOrderNoNewPickupSettleDtoMap.get(pickUpOrderNo);
                OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpOrderNoOrderInfoSynDtoMap.get(pickUpOrderNo);
                // 关联收入和计收收入
                if (newPickupSettleDto != null) {
                    vo.setChecking_amount(Utils.fromFenToYuan(newPickupSettleDto.getAmount()));
                    //订单金额 不是实付
                    BigDecimal orderTotalPrice = orderInfoForCrmSynDto == null ? BigDecimal.ZERO : PriceMathUtils.nullSafeAdd(orderInfoForCrmSynDto.getPrice(), orderInfoForCrmSynDto.getServiceFee());
                    BigDecimal relate_income = PickupSettleTypeEnum.PICKUP_ORDER.getCode().equals(newPickupSettleDto.getObjType()) ? orderTotalPrice : null;
                    vo.setRelate_income(relate_income);
                    vo.setCrm_order_amount(relate_income);
                }
            }

            if (dto.getSource() == ExpenditureSourceType.BTP.getCode()) {
                vo.setBtp_project_name(btpProjectDto.getProjectName());
                if (Objects.nonNull(batchDto)) {
                    vo.setBtp_project_name(batchDto.getProjectName());
                    vo.setBtp_status(Objects.isNull(batchDto.getStatus()) ? null : BtpStatusType.getByCode(batchDto.getStatus()).getDesc());
                    vo.setBtp_status_int(Objects.isNull(batchDto.getStatus()) ? null : batchDto.getStatus());
                    vo.setPr_in_flow(Objects.isNull(batchDto.getPrInFlow()) ? null : batchDto.getPrInFlow());
                }
            }
            List<ExpenditureAttachmentVo> attachments = new ArrayList<>();
            if (org.apache.commons.collections.CollectionUtils.isNotEmpty(dto.getAttachments())) {
                dto.getAttachments().forEach(attachmentDto -> {
                    ExpenditureAttachmentVo attachmentVo = ExpenditureAttachmentVo.builder()
                            .name(attachmentDto.getName())
                            .url(attachmentDto.getUrl())
                            .hash(Base64Utils.encodeToString(attachmentDto.getUrl().getBytes()))
                            .build();
                    try {
                        attachmentVo.setToken(bfsService.getTokenByUrl(attachmentDto.getUrl()));
                    } catch (ServiceException e) {
                        log.error("get url token error");
                    }
                    attachments.add(attachmentVo);
                });
            }
            vo.setAttachments(attachments);

            // 花火来源 订单金额实时更新
            if (dto.getSource().equals(ExpenditureSourceType.PICKUP_PLATFORM.getCode()) && dto.getCrmOrderId() != 0) {
                OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpCrmOrderIdOrderInfoSynDtoMap.get(dto.getCrmOrderId());
                if (Objects.nonNull(orderInfoForCrmSynDto)) {
                    vo.setAmount(orderInfoForCrmSynDto.getPlatformTotalExpenses());
                    vo.setEstimated_cost(orderInfoForCrmSynDto.getPlatformTotalExpenses());
                    if (OrderStatus.ORDER_COMPLETE_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                        vo.setWritten_off_time(CrmUtils.safeGetTime(orderInfoForCrmSynDto.getCompletedTime()));
                        vo.setWritten_off_cost(orderInfoForCrmSynDto.getPlatformTotalExpenses());
                        vo.setAccrued_cost(orderInfoForCrmSynDto.getPlatformTotalExpenses());
                    }
                }
            } else if (dto.getSource().equals(ExpenditureSourceType.PICKUP_PLATFORM.getCode())) {
                // 预付费花火商单
                Long pickUpOrderNo = dto.getPickUpOrderNo();
                OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpOrderNoOrderInfoSynDtoMap.get(pickUpOrderNo);
                if (Objects.nonNull(orderInfoForCrmSynDto)) {

                    // 支出
                    BigDecimal platformTotalExpenses = orderInfoForCrmSynDto.getPlatformTotalExpenses() == null ? BigDecimal.ZERO : orderInfoForCrmSynDto.getPlatformTotalExpenses();
                    vo.setAmount(platformTotalExpenses);
                    vo.setEstimated_cost(platformTotalExpenses);
                    if (orderInfoForCrmSynDto.getOrderStatus() != null && OrderStatus.ORDER_COMPLETE_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                        vo.setWritten_off_time(CrmUtils.safeGetTime(orderInfoForCrmSynDto.getPayTime()));
                        vo.setWritten_off_cost(platformTotalExpenses);
                        vo.setAccrued_cost(platformTotalExpenses);
                    }
                }
            }
            result.add(vo);
        }
        return result;
    }

    public Map<String, BTPPurchaseBatchDto> getBTPPurchaseBatches(List<String> btpPurchaseBatchCodes) {
        Map<String, BTPPurchaseBatchDto> results = new ConcurrentHashMap<>();

        CollectionWithDefaultPoolHelper.callInBatchesAsync(btpPurchaseBatchCodes, 100, (eachBTPPurchaseBatchCodes) -> {
            Map<String, BTPPurchaseBatchDto> eachResults = ibtpIntegrationService.getBTPPurchaseBatchByCodes(eachBTPPurchaseBatchCodes)
                    .stream().collect(Collectors.toMap(
                            BTPPurchaseBatchDto::getCode,
                            Function.identity(),
                            (k1, k2) -> k1
                    ));

            results.putAll(eachResults);

            return Collections.emptyList();
        });

        return results;
    }

    public BTPPurchaseDetailDto getBTPPurchaseDetail(String btpPurchaseBatchCode, String btpPurchaseDetailCode, Map<String, BTPPurchaseBatchDto> btpPurchaseBatchMap) {
        if (StringUtils.isEmpty(btpPurchaseBatchCode) || StringUtils.isEmpty(btpPurchaseDetailCode)) {
            return new BTPPurchaseDetailDto();
        }

        List<BTPPurchaseDetailDto> btpPurchaseDetailDtoList = Optional.ofNullable(btpPurchaseBatchMap.get(btpPurchaseBatchCode)).orElse(new BTPPurchaseBatchDto()).getBtpPurchaseDetails();
        if (btpPurchaseDetailDtoList == null) {
            return new BTPPurchaseDetailDto();
        }

        return btpPurchaseDetailDtoList.stream().filter(each -> each.getCode().equals(btpPurchaseDetailCode)).findFirst().orElse(new BTPPurchaseDetailDto());
    }

    public Map<Integer, List<Integer>> getRoleDepartment() {
        return JSON.parseObject(roleDepartmentMap, new TypeReference<Map<Integer, List<Integer>>>() {
        });
    }
}
