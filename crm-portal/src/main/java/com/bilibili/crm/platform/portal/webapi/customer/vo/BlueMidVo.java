package com.bilibili.crm.platform.portal.webapi.customer.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * description：蓝v mid 昵称
 * date       ：2021/4/1 11:34 上午
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BlueMidVo {

    @ApiModelProperty("蓝v mid")
    private Long blue_mid;

    @ApiModelProperty("昵称")
    private String nickname;
}
