package com.bilibili.crm.platform.portal.webapi.account.vo;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @time 2017/8/16 18:27
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AddFirstCategoryVo {

    @ApiModelProperty(notes = "行业分类名称")
    private String parent_name;

    @ApiModelProperty(notes = "备注")
    private String remark;
}
