package com.bilibili.crm.platform.portal.webapi.schedule.cpt.vo;

import com.bilibili.crm.platform.portal.webapi.schedule.SeasonVo;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

/**
 * Created by <PERSON><PERSON><PERSON> on 2017/6/12.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CptScheduleVo {
    @ApiModelProperty("平台id")
    private Integer platform_id;
    @ApiModelProperty("平台")
    private String platform_name;
    @ApiModelProperty("页面id")
    private Integer page_id;
    @ApiModelProperty("页面")
    private String page_name;
    @ApiModelProperty("位置id")
    private Integer resource_id;
    @ApiModelProperty("位置")
    private String resource_name;
    @ApiModelProperty("位次id")
    private Integer source_id;
    @ApiModelProperty("位次")
    private String source_name;
    @ApiModelProperty("等级")
    private String level;
    @ApiModelProperty("开始日期")
    private String begin_date;
    @ApiModelProperty("结束日期")
    private String end_date;
    @ApiModelProperty("排期id")
    private Integer schedule_id;
    @ApiModelProperty("排期名称")
    private String schedule_name;
    @ApiModelProperty("订单id")
    private Integer cpt_order_id;
    @ApiModelProperty("外部价格")
    private BigDecimal external_price;
    @ApiModelProperty("内部价格")
    private BigDecimal internal_price;
    @ApiModelProperty("资源类型警告")
    private String resource_type_alert_msg;
    @ApiModelProperty("是否为内链订单")
    private Boolean launch_inner_jump;
    @ApiModelProperty("剧集信息")
    private List<SeasonVo> ogv_season;
    @ApiModelProperty("唤起选项")
    private  String wake_app_type_desc;
    @ApiModelProperty("刊例周期id")
    private Integer cycle_id;
    @ApiModelProperty("刊例周期名称")
    private String cycle_name;
}
