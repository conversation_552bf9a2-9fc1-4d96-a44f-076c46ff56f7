package com.bilibili.crm.platform.portal.controller;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.bfs.service.IBfsService;
import com.bilibili.crm.platform.api.account.dto.CrmDepartmentDto;
import com.bilibili.crm.platform.api.account.service.ICrmDepartmentService;
import com.bilibili.crm.platform.api.contract.dto.ContractVideoOutcomeDto;
import com.bilibili.crm.platform.api.contract.service.IContractVideoOutcomeService;
import com.bilibili.crm.platform.api.expenditure.dto.AuditResultDto;
import com.bilibili.crm.platform.api.expenditure.dto.ExpenditureAuditDto;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.portal.BaseMockitoTest;
import com.bilibili.crm.platform.portal.helper.expenditure.ExpenditureAuditConvertHelper;
import com.bilibili.crm.platform.portal.webapi.contract.ContractVideoOutComeController;
import com.bilibili.crm.platform.portal.webapi.contract.vo.OutcomeVo;
import com.bilibili.crm.platform.portal.webapi.expenditure.vo.ExpenditureAuditVo;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.Arrays;
import java.util.List;

public class ContractVideoOutComeControllerTest extends BaseMockitoTest {

    @Mock
    private IContractVideoOutcomeService contractVideoOutcomeService;
    @Mock
    IBfsService bfsService;
    @Mock
    private IOrderService orderService;
    @Mock
    private ICrmDepartmentService departmentService;
    @Mock
    private ExpenditureAuditConvertHelper expenditureHelper;
    @InjectMocks
    private ContractVideoOutComeController contractVideoOutComeController;

    @Test
    public void queryVideoOutcomesTest(){
        String str = "[{\"id\":001,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0},{\"id\":002,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0}]";
        List<ContractVideoOutcomeDto> dtos = JSON.parseArray(str, ContractVideoOutcomeDto.class);
        Mockito.when(contractVideoOutcomeService.queryVideoOutcomeList(Mockito.any())).thenReturn(dtos);
        contractVideoOutComeController.queryVideoOutcomes(context,10596);
    }

    @Test
    public void queryVideoOutcomeTest(){
        String str = "{\"id\":001,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0,\"uploaderBelongMcn\":1}";
        ContractVideoOutcomeDto dto = JSON.parseObject(str, ContractVideoOutcomeDto.class);
        String departmentStr = "{\"id\":001,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0}";
        CrmDepartmentDto departmentDto = JSON.parseObject(departmentStr,CrmDepartmentDto.class);
        Mockito.when(contractVideoOutcomeService.queryVideoOutcomeById(Mockito.any())).thenReturn(dto);
        Mockito.when(departmentService.queryDepartmentById(Mockito.any())).thenReturn(departmentDto);
        contractVideoOutComeController.queryVideoOutcome(context,1);
    }

    @Test
    public void createVideoOutcomeTest(){
        String str = "{\"id\":001,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0}";
        OutcomeVo vo = JSON.parseObject(str, OutcomeVo.class);
        Mockito.when(contractVideoOutcomeService.insertVideoOutcome(Mockito.any())).thenReturn(1);
        contractVideoOutComeController.createVideoOutcome(context,vo);
    }

    @Test
    public void updateVideoOutcomeTest(){
        String str = "{\"id\":001,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0}";
        OutcomeVo vo = JSON.parseObject(str, OutcomeVo.class);
        Mockito.when(contractVideoOutcomeService.updateVideoOutcome(Mockito.any())).thenReturn(1);
        contractVideoOutComeController.updateVideoOutcome(context,vo);
    }

    @Test
    public void updAuditStatusTest(){
        String str = "{\"id\":001,\"contractId\":10596,\"uploaderName\":\"mcn广告主2\",\"avNumber\":123,\"priceOfMaking\":10000,\"invoiceNumber\":\"3000001\",\"uploaderCooperateType\":0}";
        ExpenditureAuditVo vo = JSON.parseObject(str, ExpenditureAuditVo.class);
        AuditResultDto dto = AuditResultDto.builder().build();
        Mockito.when(expenditureHelper.vo2ListDto(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(ExpenditureAuditDto.builder().id(1).build()));
        Mockito.when(contractVideoOutcomeService.updAuditStatus(Mockito.any())).thenReturn(dto);
        contractVideoOutComeController.updAuditStatus(context,vo);
    }
}
