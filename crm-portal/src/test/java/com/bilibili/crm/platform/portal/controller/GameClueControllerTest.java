package com.bilibili.crm.platform.portal.controller;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.crm.platform.api.clue.dto.GameClueBaseDto;
import com.bilibili.crm.platform.api.clue.dto.GameClueDetailDto;
import com.bilibili.crm.platform.api.contract.dto.LogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.biz.service.clue.GameClueService;
import com.bilibili.crm.platform.portal.BaseMockitoTest;
import com.bilibili.crm.platform.portal.common.CrmCommonUtils;
import com.bilibili.crm.platform.portal.helper.clue.GameClueHelper;
import com.bilibili.crm.platform.portal.webapi.clue.GameClueController;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;

public class GameClueControllerTest extends BaseMockitoTest {
    @Mock
    private GameClueService gameClueService;
    @Mock
    private GameClueHelper gameClueHelper;
    @Mock
    private CrmCommonUtils crmCommonUtils;
    @Mock
    private ILogOperatorService logOperatorService;

    @InjectMocks
    private GameClueController gameClueController;

    @Test
    public void getBaseInfoList() throws ServiceException {
        List<GameClueBaseDto> dtos = Arrays.asList(GameClueBaseDto.builder().id(1L).source(1).onlineStatus(1).build());
        Mockito.when(gameClueService.getListByPage(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new PageResult<>(dtos.size(),dtos));

        gameClueController.getBaseInfoList(context, "",100L,"",200L,300L,1,1,1);
    }

    @Test
    public void getDetail() throws ServiceException {
        Mockito.when(gameClueService.getGameDtoById(Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(GameClueDetailDto.builder().gameStatus(1).gameStatus(1).build()));

        gameClueController.getDetail(context,100L);
    }

    @Test
    public void viewRecord() throws ServiceException {
        List<LogOperatorDto> dtos = Arrays.asList(LogOperatorDto.builder().id(9999999999L).modifyType("查看游戏线索信息").ctime(new Timestamp(1567440000000L)).build());
        Mockito.when(logOperatorService.queryPageResult(Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(new PageResult<>(dtos.size(),dtos));

        gameClueController.viewRecord(context,1,1,1);
    }

}
