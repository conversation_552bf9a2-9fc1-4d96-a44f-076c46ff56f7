package com.bilibili.crm.platform.portal.convertor;

import com.bilibili.crm.platform.portal.BaseMockitoTest;
import com.bilibili.crm.platform.portal.convertor.effectrealtime.EffectRealtimeConvertor;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.effect.EffectRealtimeDimensionQueryVo;
import com.bilibili.crm.platform.portal.webapi.wxapp.vo.effect.EffectRealtimeHoursQueryVo;
import org.junit.Test;

public class EffectRealtimeConvertorTest extends BaseMockitoTest {

    @Test
    public void testConvertHourQueryVo2Dto() {
        EffectRealtimeHoursQueryVo queryVo = new EffectRealtimeHoursQueryVo();
        EffectRealtimeConvertor.convertHourQueryVo2Dto(queryVo);
    }

    @Test
    public void testConvertDimensionQueryVo2Dto() {
        EffectRealtimeConvertor.convertDimensionQueryVo2Dto(new EffectRealtimeDimensionQueryVo());
    }
}