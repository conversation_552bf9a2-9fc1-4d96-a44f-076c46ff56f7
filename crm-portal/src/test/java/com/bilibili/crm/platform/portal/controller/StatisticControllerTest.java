package com.bilibili.crm.platform.portal.controller;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.crm.platform.api.account.service.ICrmDepartmentService;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.contract.service.IContractStatService;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.portal.BaseMockitoTest;
import com.bilibili.crm.platform.portal.common.CrmCommonUtils;
import com.bilibili.crm.platform.portal.helper.statistic.StatisticQueryHelper;
import com.bilibili.crm.platform.portal.webapi.statistic.StatisticController;
import com.bilibili.crm.report.api.dto.AdvertiserLaunchDataDto;
import com.bilibili.crm.report.api.dto.AdvertiserReportDto;
import com.bilibili.crm.report.api.service.IAccountReportService;
import com.bilibili.crm.report.api.service.IAgentReportService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.unidal.web.http.HttpServletResponseMock;

import javax.servlet.ServletOutputStream;
import javax.servlet.WriteListener;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.util.Arrays;

public class StatisticControllerTest extends BaseMockitoTest {
    @Mock
    private IQueryAccountService queryAccountService;
    @Mock
    private IContractStatService contractStatService;
    @Mock
    private IAccountReportService accountReportService;
    @Mock
    private IAgentReportService agentReportService;
    @Mock
    private RedisLock redisLock;
    @Mock
    private CrmCommonUtils crmCommonUtils;
    @Mock
    private ICrmDepartmentService departmentService;
    @Mock
    private StatisticQueryHelper statisticQueryHelper;
    @InjectMocks
    private StatisticController statisticController;

//    @Test
//    public void dowanloadAdvertiserReportTest() throws ServiceException, IOException {
//        Mockito.when(statisticQueryHelper.filterQueryAccountId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(1,2,3,4,5));
//        Mockito.when(accountReportService.getAdvertiserData(Mockito.any())).thenReturn(Arrays.asList(AdvertiserReportDto.builder().build()));
//        HttpServletResponse response =  Mockito.mock(HttpServletResponse.class);
//        Mockito.when(response.getOutputStream()).thenReturn(new ServletOutputStream() {
//            @Override
//            public boolean isReady() {
//                return true;
//            }
//
//            @Override
//            public void setWriteListener(WriteListener writeListener) {
//
//            }
//
//            @Override
//            public void write(int b) throws IOException {
//
//            }
//        });
//        statisticController.dowanloadAdvertiserReport(response,context,Arrays.asList(1,2),Arrays.asList(1,2),Arrays.asList(1,2),Arrays.asList(1,2),1,1567440000000L,1567494651468L, 1);
//    }

    @Test
    public void getAdvertiserDataVosTest() throws ServiceException {
        AdvertiserLaunchDataDto data = AdvertiserLaunchDataDto.builder().build();
        AdvertiserLaunchDataDto data1 = AdvertiserLaunchDataDto.builder().build();
        Mockito.when(statisticQueryHelper.filterQueryAccountId(Mockito.any(),Mockito.any(),Mockito.any(),Mockito.any())).thenReturn(Arrays.asList(1,2,3,4,5));
        Mockito.when(accountReportService.getAdvertiserData(Mockito.any())).thenReturn(Arrays.asList(AdvertiserReportDto.builder().cpcLaunchDatas(data).cpmLaunchDatas(data1).build()));

        statisticController.getAdvertiserDataVos(context,Arrays.asList(1,2),Arrays.asList(1,2),Arrays.asList(1,2),Arrays.asList(1,2),1,1567440000000L,1567494651468L);

    }

}
