package com.bilibili.crm.platform.adx.biz.po;

import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.DateFormat;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;

import java.io.Serializable;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Date;

import static com.fasterxml.jackson.annotation.JsonFormat.DEFAULT_TIMEZONE;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-09-12
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = "adx_bidder_day")
public class ESAdxBidderDayPo implements Serializable {
    private static final long serialVersionUID = 1L;

    /**
     * ID
     */
    @Id
    private String id;
    /**
     * 日期
     */
    @JsonFormat(timezone = "Asia/Shanghai", pattern="yyyy-MM-dd")
    private Timestamp day;
    /**
     * 账号id
     */
    private Integer account_id;
    /**
     * 出价方ID
     */
    private Integer bidder_id;
    /**
     * 曝光数量
     */
    private Integer show_count;
    /**
     * 点击次数
     */
    private Integer click_count;
    /**
     * 应扣费计费（单位：毫分）
     */
    private Long charged_cost_milli;
}
