package com.bilibili.crm.platform.adx.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdxStatBidderTemplateSourceDayPo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 出价方ID
     */
    private Integer bidderId;

    /**
     * 模板ID
     */
    private Integer templateId;

    /**
     * 广告位source ID
     */
    private Integer sourceId;

    /**
     * 时间序列-日期
     */
    private Timestamp groupTime;

    /**
     * 曝光数量
     */
    private Integer showCount;

    /**
     * 点击次数
     */
    private Integer clickCount;

    /**
     * 应扣费计费（单位：毫分）
     */
    private Long chargedCostMilli;

    /**
     * 累计出价 单位（毫分）
     */
    private Long bidCostMilli;

    /**
     * 反作弊曝光量
     */
    private Integer acShowCount;

    /**
     * 反作弊点击次数
     */
    private Integer acClickCount;

    /**
     * 反作弊计费（单位：毫分）
     */
    private Long acCostMilli;

    /**
     * 反作弊累计出价 单位（毫分）
     */
    private Long acBidCostMilli;

    /**
     * 记录版本
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 未登录点击反作弊策略下，作弊点击次数
     */
    private Integer unloginClickAcClickCount;

    /**
     * 未登录点击次数
     */
    private Integer unloginClickCount;

    /**
     * 未登录曝光数量
     */
    private Integer unloginShowCount;

    private static final long serialVersionUID = 1L;
}