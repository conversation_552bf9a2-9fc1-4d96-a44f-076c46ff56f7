package com.bilibili.crm.platform.adx.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.crm.platform.adx.biz.dao.AdxBidderConfigDao;
import com.bilibili.crm.platform.adx.biz.dao.AdxStatBidderDayDao;
import com.bilibili.crm.platform.adx.biz.dao.DefinedAdxStatBidderDayDao;
import com.bilibili.crm.platform.adx.biz.po.*;
import com.bilibili.crm.platform.api.adx.dto.AdxBidderConfigDto;
import com.bilibili.crm.platform.api.adx.dto.QueryAdxBidderDayDto;
import com.bilibili.crm.platform.api.adx.service.IAdxBidderDayService;
import com.bilibili.crm.platform.api.adx.dto.AdxBidderDayDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-09-11
 **/
@Service
public class AdxBidderDayService implements IAdxBidderDayService {
    @Autowired
    private AdxBidderConfigDao adxBidderConfigDao;
    @Autowired
    private AdxStatBidderDayDao adxStatBidderDayDao;
    @Autowired
    private DefinedAdxStatBidderDayDao definedAdxStatBidderDayDao;
    @Autowired
    private AdxBidderServiceHelper adxBidderHelper;
    /**
     * 查询返回最大条数限制
     */
    private final static Integer DEFAULT_LIMIT = 2000;

    @Override
    public Map<Integer, AdxBidderConfigDto> queryMapFromBidderConfig() {
        AdxBidderConfigPoExample example = new AdxBidderConfigPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<AdxBidderConfigPo> list = adxBidderConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(list)){
            return Collections.EMPTY_MAP;
        }
        return list.stream()
                .collect(Collectors.toMap(AdxBidderConfigPo::getId, this::convertConfigPo2Dto));
    }

    @Override
    public List<AdxBidderDayDto> queryBidderByDay(Timestamp day) {
        AdxStatBidderDayPoExample example = new AdxStatBidderDayPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andGroupTimeEqualTo(day);

        List<AdxStatBidderDayPo> list = adxStatBidderDayDao.selectByExample(example);
        if (CollectionUtils.isEmpty(list)){
            return Collections.EMPTY_LIST;
        }
        return list.stream().map(this::convertPo2Dto).collect(Collectors.toList());
    }

    @Override
    public List<AdxBidderDayDto> queryBidderBeforeDay(Timestamp day) {
        AdxStatBidderDayPoExample example = new AdxStatBidderDayPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andGroupTimeLessThan(day);

        List<AdxStatBidderDayPo> list = adxStatBidderDayDao.selectByExample(example);
        if (CollectionUtils.isEmpty(list)){
            return Collections.EMPTY_LIST;
        }
        return list.stream().map(this::convertPo2Dto).collect(Collectors.toList());
    }

    @Override
    public List<AdxBidderDayDto> queryBidderByAllDay() {
        List<AdxStatBidderDayPo> result = new ArrayList<>();
        AdxStatBidderDayPoExample example = new AdxStatBidderDayPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setLimit(DEFAULT_LIMIT);
        int offset = 0;

        List<AdxStatBidderDayPo> list = definedAdxStatBidderDayDao.selectByExampleWithOptimize(example);
        result.addAll(list);
        while (!CollectionUtils.isEmpty(list)){
            offset += DEFAULT_LIMIT;
            example.setOffset(offset);
            list = definedAdxStatBidderDayDao.selectByExampleWithOptimize(example);
            result.addAll(list);
        }

        return result.stream()
                .map(this::convertPo2Dto)
                .collect(Collectors.toList());
    }
    //AdxBidderDayDto条件查询
    @Override
    public List<AdxBidderDayDto> queryAdxStatBidderDayDtos(QueryAdxBidderDayDto queryDto) {

        AdxStatBidderDayPoExample example = buildAdxBidderDayExample(queryDto);

        List<AdxStatBidderDayPo> list = adxStatBidderDayDao.selectByExample(example);
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }
        List<AdxBidderDayDto> bidderDtos = list.stream().map(this::convertPo2Dto).collect(Collectors.toList());
        return adxBidderHelper.enhanceDay(bidderDtos);
    }

    private AdxStatBidderDayPoExample buildAdxBidderDayExample(QueryAdxBidderDayDto queryDto){
        AdxStatBidderDayPoExample example = new AdxStatBidderDayPoExample();
        AdxStatBidderDayPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.setObject(queryDto::getGroupTimeBegin, criteria::andGroupTimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getGroupTimeEnd, criteria::andGroupTimeLessThan);
        ObjectUtils.setList(queryDto::getBidderIds, criteria::andBidderIdIn);
        return example;
    }
    @Override
    public List<AdxBidderConfigDto> queryConfigByAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.EMPTY_LIST;
        }
        AdxBidderConfigPoExample example = new AdxBidderConfigPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdIn(accountIds);
        List<AdxBidderConfigPo> pos = adxBidderConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.EMPTY_LIST;
        }
        return pos.stream().map(this::convertConfigPo2Dto).collect(Collectors.toList());
    }

    @Override
    public List<AdxBidderConfigDto> queryConfigByDspnameLike(String dspnameLike) {
        Assert.hasText(dspnameLike, "DSP名称不可为空");
        AdxBidderConfigPoExample example = new AdxBidderConfigPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andNameLike(dspnameLike+"%");
        List<AdxBidderConfigPo> pos = adxBidderConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)){
            return Collections.EMPTY_LIST;
        }
        return pos.stream()
                .map(this::convertConfigPo2Dto)
                .collect(Collectors.toList());
    }

    @Override
    public List<AdxBidderConfigDto> queryConfigByIds(List<Integer> bidderIds) {
        Assert.notNull(CollectionUtils.isEmpty(bidderIds), "bidder id 不可为空");
        AdxBidderConfigPoExample example = new AdxBidderConfigPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdIn(bidderIds);
        List<AdxBidderConfigPo> pos = adxBidderConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)){
            return Collections.EMPTY_LIST;
        }
        return pos.stream()
                .map(this::convertConfigPo2Dto)
                .collect(Collectors.toList());
    }

    private AdxBidderConfigDto convertConfigPo2Dto(AdxBidderConfigPo po){
        return AdxBidderConfigDto.builder()
                .bidderId(po.getId())
                .accountId(po.getAccountId())
                .name(po.getName())
                .build();
    }

    private AdxBidderDayDto convertPo2Dto(AdxStatBidderDayPo po){
        return AdxBidderDayDto.builder()
                .day(po.getGroupTime())
                .bidderId(po.getBidderId())
                .showCount(po.getShowCount())
                .clickCount(po.getClickCount())
                .chargedCostMilli(po.getChargedCostMilli())
                .ctime(po.getCtime())
                .build();
    }
}
