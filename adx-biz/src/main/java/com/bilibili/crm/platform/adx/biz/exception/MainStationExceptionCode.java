package com.bilibili.crm.platform.adx.biz.exception;

import lombok.Getter;

/**
 * 主站异常 code
 *
 * <AUTHOR>
 */
public enum MainStationExceptionCode {
    SUCCESS("0", "成功"),
    REQUEST_PARAM_ERROR("**********", "参数非空"),
    INVALID_PARTNER_ID("**********", "无效业务方id"),
    SIGN_ERROR("**********", "签名错误"),
    ACCOUNT_NOT_ENOUGH_ERROR("**********", "可用账号不足"),
    ACCOUNT_ASSIGN_ERROR("**********", "账号分配失败（可重试）"),
    INTERNAL_ERROR("**********", "服务端内部错误（可重试）"),
    UNKNOWN("-1", "服务端内部错误（可重试）"),
    ;

    MainStationExceptionCode(String code, String message) {
        this.code = code;
        this.message = message;
    }

    @Getter
    private String code;
    @Getter
    private String message;

    public static MainStationExceptionCode getByCode(String code) {
        for (MainStationExceptionCode bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return null;
    }
}
