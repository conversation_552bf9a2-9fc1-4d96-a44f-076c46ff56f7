package com.bilibili.crm.report.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.math.BigDecimal;

/**
 * Created by cuihaichuan on 2017/11/10.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdvertiserReportDto implements Serializable {

    private static final long serialVersionUID = -8903269563982848274L;

    private String date;

    private Integer accountId;

    private String accountName;

    private String dependencyAgentName;

    private AdvertiserFundDataDto fundDatas;

    private AdvertiserLaunchDataDto cpcLaunchDatas;

    private AdvertiserLaunchDataDto cpmLaunchDatas;

    private BigDecimal cashBalance;
    private BigDecimal redPacketBalance;

    private String firstCategory;
    private String secondCategory;

    private String unitedFirstIndustry;

    private String unitedSecondIndustry;

    private String unitedThirdIndustry;


}
