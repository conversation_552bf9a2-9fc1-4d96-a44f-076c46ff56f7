package com.bilibili.crm.report.api.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.List;

/**
 * Created by fan<PERSON><PERSON> on 2017/9/5.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class ContractReportDto {
    /**
     * 开始时间
     */
    private Timestamp beginTime;

    /**
     * 结束时间
     */
    private Timestamp endTime;

    /**
     * 客户id
     */
    private Integer accountId;

    /**
     * 客户名
     */
    private String accountName;

    /**
     * 代理商id
     */
    private Integer agentId;

    /**
     * 代理商名称
     */
    private String agentName;

    /**
     * 0其他1up主代理商
     */
    private Integer agentType;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 合同号（14到18位）
     */
    private Long contractNumber;

    /**
     * 合同名称
     */
    private String contractName;

    /**
     * 合同类型 0 普通合同 1 up主制作合同
     */
    private Integer contractType;

    /**
     * 0编辑中、1待审核、2已拒绝、3审核通过、4执行中、5可执行完成、6待收款、7已完成、8禁用
     */
    private Integer contractStatus;

    /**
     * 新增合同的时间
     */
    private Timestamp newCtime;

    /**
     * 执行合同的时间
     */
    private Timestamp execCtime;

    /**
     * 待收款的时间
     */
    private Timestamp receivableCtime;

    /**
     * 完成合同的时间
     */
    private Timestamp doneCtime;

    /**
     * 订单数量
     */
    private Integer orderNumber;

    /**
     * 外部价,单位(分)
     */
    private Long externalPrice;

    /**
     * 内部价,单位(分)
     */
    private Long internalPrice;

    /**
     * 折后价，单位(分)
     */
    private Long discountPrice;

    /**
     * 合同打包价（单位 分）
     */
    private Long amount;

    /**
     * 0未盖章、1我方盖章、2对方盖章、3已存档
     */
    private Integer archiveStatus;

    /**
     * 是否开票，0、没有 1、开过票
     */
    private Integer isInvoice;

    /**
     * 是否收款，0、未收款、1、收过款
     */
    private Integer isReceipt;

    private List<String> sales;

    /**
     * 可执行完成的时间
     */
    private Timestamp execDoneCtime;

    /**
     * 客户行业一级分类id
     */
    private Integer categoryFirstId;

    /**
     * 客户行业一级分类名称
     */
    private String categoryFirstName;

    /**
     * 客户行业二级分类id
     */
    private Integer categorySecondId;

    /**
     * 客户行业二级分类名称
     */
    private String categorySecondName;

    /**
     * 合同配送金额,单位(分)
     */
    private Long distributePrice;

    /**
     * 合同配送比例, 配送订单外部刊例价之和/合同打包价*100
     */
    private BigDecimal distributeRate;

    /**
     * 合同补量金额,单位(分)
     */
    private Long compensatoryPrice;
//    /**
//     * 直客销售员
//     */
//    private String directSalesmans;
//    /**
//     * 直客销售组长
//     */
//    private String directGroupLeaders;
//    /**
//     * 直客销售总监
//     */
//    private String directChiefs;
//    /**
//     * 渠道销售员
//     */
//    private String channelSalesmans;
//    /**
//     * 渠道销售组长
//     */
//    private String channelGroupLeaders;
//    /**
//     * 渠道销售总监
//     */
//    private String channelChiefs;

    /**
     *非标执行成本比例
     */

    /**
     * 合同执行成本比例（支出总计/实际合同金额）
     */
}
