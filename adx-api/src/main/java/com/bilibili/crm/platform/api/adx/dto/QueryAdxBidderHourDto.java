package com.bilibili.crm.platform.api.adx.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class QueryAdxBidderHourDto {

    private Timestamp groupTimeBegin;
    private Timestamp groupTimeEnd;
    private List<Integer> bidderIds;

    private Timestamp mtimeBegin;
    private Timestamp mtimeEnd;

}
