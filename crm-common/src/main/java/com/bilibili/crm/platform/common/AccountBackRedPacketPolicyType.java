package com.bilibili.crm.platform.common;


import lombok.AllArgsConstructor;

import java.util.Arrays;
import java.util.List;

/**
 * 返货政策类型 1-一返 2-二返 3-三返 4-其他 5-专项返货 6-oCPX赔付 7-超预算赔付 8-效果返货
 */
@AllArgsConstructor
public enum AccountBackRedPacketPolicyType {
    ACCOUNT_BACK_POLICY_TYPE_DEFAULT(0, "未知"),
    ACCOUNT_BACK_POLICY_TYPE_FIRST(1, "一返"),
    ACCOUNT_BACK_POLICY_TYPE_SECOND(2, "二返"),
    ACCOUNT_BACK_POLICY_TYPE_THIRD(3, "三返"),
    ACCOUNT_BACK_POLICY_TYPE_OTHER(4, "其他"),
    ACCOUNT_BACK_POLICY_TYPE_SPECIAL(5, "专项返货"),
    ACCOUNT_BACK_POLICY_TYPE_OCPX(6, "oCPX赔付"),
    ACCOUNT_BACK_POLICY_TYPE_ACCOUNT_BACK_RED_PACKET_BUDGET_OUT(7, "超预算赔付"),

    /**
     * 系统计算返货的特征
     */
    ACCOUNT_BACK_POLICY_TYPE_EFFECT(8, "效果返货"),

    UNIT_INVALID_CONSUME_COMPENSATE(9, "单元空消耗赔付"),
    ;

    private Integer code;
    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AccountBackRedPacketPolicyType getByCode(int code) {
        for (AccountBackRedPacketPolicyType bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        //throw new IllegalArgumentException("unknown code FinanceTaskType "+code);
        return AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_DEFAULT;
    }

    public static List<Integer> SPECIAL_RED_BACK_POLICY_TYPE_LIST = Arrays.asList(
            AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_SPECIAL.getCode(),
            AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_OCPX.getCode());


    public static List<Integer> AGENT_ACCOUNT_BATCH_POLICY_TYPE_LIST = Arrays.asList(
            AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_FIRST.getCode(),
            AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_SECOND.getCode(),
            AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_THIRD.getCode(),
            AccountBackRedPacketPolicyType.ACCOUNT_BACK_POLICY_TYPE_OTHER.getCode());
}

