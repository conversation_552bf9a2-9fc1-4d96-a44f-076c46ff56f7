package com.bilibili.crm.platform.common.account;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/6/1
 **/
public enum AccTransferFlowLogTypeEnum {
    AGENT_TO_AGENT(1, "代理商和代理商转账"),
    ACC_TO_ACC(2, "广告主和广告主转账"),
    ;

    AccTransferFlowLogTypeEnum(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    private Integer code;

    private String desc;

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static AccTransferFlowLogTypeEnum getByCode(Integer code) {

        for (AccTransferFlowLogTypeEnum flowLogTypeEnum : AccTransferFlowLogTypeEnum.values()) {
            if (flowLogTypeEnum.getCode().equals(code)) {
                return flowLogTypeEnum;
            }
        }
        return null;
    }
}
