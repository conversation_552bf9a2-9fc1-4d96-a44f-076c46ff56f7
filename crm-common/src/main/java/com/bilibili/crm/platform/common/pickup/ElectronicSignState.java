package com.bilibili.crm.platform.common.pickup;

import java.util.Arrays;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/12 15:34
 */
public enum ElectronicSignState {
    SIGNING(1, "填写中"),
    SIGNED(4, "已签约"),
    MIDDLE_STAMP(70, "中间态：合同盖章中"),
    MIDDLE_INPUT(71, "中间态：合同录入中"),
    MIDDLE_APPROVE(100, "中间态：审批中"),
    REFUSE(2, "已驳回"),
    CLOSE_OPT_REVOCATION(31, "已关闭：运营撤回"),
    CLOSE_EXPIRE_REVOCATION(32, "已关闭：到期撤回 "),
    CLOSE_ACCOUNT_AUDIT_FALSE(33, "已关闭：账号核实失败"),
    EXPIRED(5, "已到期"),
    DELETED(6, "已删除"),
    USER_SIGN(111, "用户签约"),
    DEFAULT(999, "--"),
    ;

    private Integer code;
    private String desc;

    public static List<Integer> regardedAsSignCodes = Arrays.asList(SIGNING.code, SIGNED.code, MIDDLE_STAMP.code, MIDDLE_INPUT.code, MIDDLE_APPROVE.code);
    public static List<Integer> customSignCodes = Arrays.asList(SIGNING.code, SIGNED.code, MIDDLE_STAMP.code, MIDDLE_INPUT.code, MIDDLE_APPROVE.code, REFUSE.code, CLOSE_OPT_REVOCATION.code, CLOSE_ACCOUNT_AUDIT_FALSE.code, EXPIRED.code);

    public Integer getCode() {
        return this.code;
    }

    public String getDesc() { return this.desc; }

    private ElectronicSignState(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public static String getDescByCode(Integer state) {
        for (ElectronicSignState signState : ElectronicSignState.values()) {
            if (signState.getCode().equals(state)) {
                return signState.getDesc();
            }
        }

        return DEFAULT.getDesc();
    }
}