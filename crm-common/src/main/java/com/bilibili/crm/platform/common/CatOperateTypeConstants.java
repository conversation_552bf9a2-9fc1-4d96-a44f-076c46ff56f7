package com.bilibili.crm.platform.common;

/**
 * cat 操作类型常量
 *
 * <AUTHOR>
 * @date 2021/1/6 11:07 上午
 */
public class CatOperateTypeConstants {

    /**
     * 账号相关
     */
    public static final String CREATE_ACCOUNT = "create_account";
    public static final String UPDATE_ACCOUNT = "update_account";
    public static final String ADOPT_ACCOUNT = "adopt_account";
    public static final String ENABLE_ACCOUNT = "enable_account";
    public static final String DISABLE_ACCOUNT = "disable_account";

    /**
     * 客户相关
     */
    public static final String CREATE_CUSTOMER = "create_customer";
    public static final String UPDATE_CUSTOMER = "update_customer";
    public static final String ADOPT_CUSTOMER = "adopt_customer";
    public static final String ADOPT_CUSTOMER_FOR_SUPPLEMENT = "adopt_customer_for_supplement";
    public static final String ENABLE_CUSTOMER = "enable_customer";
    public static final String DISABLE_CUSTOMER = "disable_customer";

    /**
     * 订单相关
     */
    public static final String CREATE_NO_STANDARD_ORDER = "create_no_standard_order";
    public static final String CREATE_UP_REQUIREMENT_ORDER = "create_up_requirement_order";

    /**
     * 收款流水相关
     */
    public static final String FLOW_CLAIM = "flow_claim";
    public static final String AUTO_RECHARGE = "auto_recharge";

    /**
     * 起飞资质相关
     */
    public static final String CREATE_FLY_QUALIFICATION = "create_fly_qualification";

    /**
     * oa 相关
     */
    public static final String OA_CREATE_CONTRACT_INVOICE = "create_contract_oa_invoice_batch";
    public static final String OA_CREATE_RECHARGE_INVOICE = "create_recharge_oa_invoice_batch";
    public static final String OA_CREATE_PICKUP_INVOICE = "create_pickup_oa_invoice_batch";
    public static final String OA_CREATE_CONTRACT_REBATE_CHECK_INVOICE = "create_contract_rebate_check_oa_invoice_batch";
    public static final String OA_CREATE_STATEMENT_INVOICE = "create_statement_oa_invoice_batch";

    public static final String OA_CREATE_RETURN_MONEY_INVOICE = "create_return_money_oa_invoice_batch";
    public static final String OA_CREATE_STATEMENT_INVOICE_CLUE_PASS = "create_statement_oa_invoice_batch_clue_pass";

    public static final String OA_CREATE_STATEMENT_INVOICE_SPARK = "create_statement_oa_invoice_batch_spark";

    /**
     * 返点核算相关
     */
    public static final String REBATE_CHECK = "rebate_check";

    /**
     * 政策流程
     */
    public static final String POLICY_FLOW = "policy_flow";

    /**
     * 品牌返货
     */
    public static final String BRAND_RETURN = "brand_return";

    /**
     * 返货申请
     */
    public static final String RETURN_APPLY = "return_apply";

    /**
     * 账号标签
     */
    public static final String ACCOUNT_LABEL = "account_label";

    /**
     * 执行单
     */
    public static final String EXECUTIVE_ORDER = "executive_order";

}
