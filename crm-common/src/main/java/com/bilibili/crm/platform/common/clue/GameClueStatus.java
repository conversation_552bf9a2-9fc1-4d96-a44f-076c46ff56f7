package com.bilibili.crm.platform.common.clue;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * 游戏状态：0 下载 1 预约（跳过详情）2 预约 3 测试 4 测试+预约 5.跳过详情页
 */
@AllArgsConstructor
public enum GameClueStatus {

    DOWNLOAD(0, "下载"),
    APPOINTMENT(1, "预约（跳过详情）"),
    APPOINTMENT_DETAIL(2, "预约"),
    TEST(3, "测试"),
    TEST_APPOINTMENT(4, "测试+预约"),
    SKIP_DETAIL(5, "跳过详情页");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static GameClueStatus getByCode(int code) {
        for (GameClueStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code GameClueStatus: " + code);
    }
}
