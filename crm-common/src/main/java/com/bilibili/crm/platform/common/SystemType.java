package com.bilibili.crm.platform.common;
/**
 * 账号钱包交易的系统来源：1-品牌系统 2-crm系统 3-花火系统 4-效果系统
 * **/
public enum SystemType {

    /**
     * 品牌
     */
    BRAND(1, "品牌系统", "brand"),

    /**
     * CRM系统
     */
    CRM(2, "crm系统", "crm"),

    /**
     * 花火系统
     */
    PICKUP(3, "花火系统", "pickup"),

    /**
     * 效果系统
     */
    ADP(4, "效果系统", "adp"),

    /**
     * 个人起飞系统
     */
    FLY(5, "个人起飞系统", "fly"),

    MAS_CLUE_PASS(6, "互选线索通", "mas_clue_pass"),

    SELL_GOOD(7, "带货投流", "sell_good"),
    ;

    private Integer code;
    private String desc;
    private String key;

    SystemType(Integer code, String desc, String key) {
        this.code = code;
        this.desc = desc;
        this.key = key;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public String getKey() {
        return key;
    }

    public static SystemType getByCode(int code) {
        for (SystemType type : values()) {
            if (type.code == code) {
                return type;
            }
        }
        return null;
    }

    public static void main(String[] args) {

    }
}
