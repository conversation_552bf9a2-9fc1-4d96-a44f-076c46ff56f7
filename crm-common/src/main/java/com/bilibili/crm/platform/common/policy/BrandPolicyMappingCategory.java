package com.bilibili.crm.platform.common.policy;

import lombok.AllArgsConstructor;
import lombok.Getter;

/**
 * @author: brady
 * @time: 2021/5/1 11:13 上午
 */
@AllArgsConstructor
public enum BrandPolicyMappingCategory {
    DEFAULT(0, "其他"),
    SUBJECT(1, "关联主体"),
    PRODUCT(2, "产品类型"),
    AGENT(3, "服务代理");

    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static BrandPolicyMappingCategory getByCode(Integer code) {
        for (BrandPolicyMappingCategory bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return BrandPolicyMappingCategory.DEFAULT;
    }
}
