package com.bilibili.crm.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;
import java.util.EnumSet;

/**
 * 0编辑中、1待审核、2已拒绝、3审核通过、4执行中、5可执行完成、6待收款、7已完成、8禁用
 * Created by <PERSON><PERSON><PERSON> on 2017/5/16.
 */
@AllArgsConstructor
public enum ContractStatus {
    EDIT(0, "编辑中") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.EDIT,
                    ContractStatus.TO_BE_AUDIT,
                    ContractStatus.DISABLED
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return true;
        }
    },
    TO_BE_AUDIT(1, "待审核") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.EDIT,
                    ContractStatus.TO_BE_AUDIT,
                    ContractStatus.REJECTED,
                    ContractStatus.AUDITED,
                    ContractStatus.DISABLED
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return false;
        }
    },
    REJECTED(2, "已拒绝") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.TO_BE_AUDIT,
                    ContractStatus.DISABLED
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return true;
        }
    },
    AUDITED(3, "审核通过") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.EXECUTE,
                    ContractStatus.DISABLED,
                    ContractStatus.TO_BE_AUDIT
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return true;
        }
    },
    EXECUTE(4, "执行中") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.EXECUTABLE,
                    ContractStatus.TO_BE_AUDIT
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return true;
        }
    },
    EXECUTABLE(5, "可执行完成") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.RECEIVABLE,
                    ContractStatus.TO_BE_AUDIT
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return true;
        }
    },
    RECEIVABLE(6, "待收款") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return EnumSet.of(
                    ContractStatus.COMPLETED,
                    ContractStatus.TO_BE_AUDIT
            ).contains(toStatus);
        }

        @Override
        public boolean validateCouldAddOrder() {
            return true;
        }
    },
    COMPLETED(7, "已完成") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return false;
        }

        @Override
        public boolean validateCouldAddOrder() {
            return false;
        }
    },
    DISABLED(8, "禁用") {
        @Override
        public boolean validateToStatus(ContractStatus toStatus) {
            return false;
        }

        @Override
        public boolean validateCouldAddOrder() {
            return false;
        }
    },;
    @Getter
    private Integer code;
    @Getter
    private String desc;

    public static ContractStatus getByCode(int code) {
        for (ContractStatus bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code ContractStatus " + code);
    }

    public abstract boolean validateToStatus(ContractStatus toStatus);

    public abstract boolean validateCouldAddOrder();
}
