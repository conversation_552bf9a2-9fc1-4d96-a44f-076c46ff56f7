package com.bilibili.crm.platform.processor;

import javax.annotation.processing.AbstractProcessor;
import javax.annotation.processing.RoundEnvironment;
import javax.annotation.processing.SupportedAnnotationTypes;
import javax.annotation.processing.SupportedSourceVersion;
import javax.lang.model.SourceVersion;
import javax.lang.model.element.*;
import javax.lang.model.type.TypeMirror;
import javax.tools.Diagnostic;
import java.util.Set;

@SupportedAnnotationTypes({
        "org.springframework.transaction.annotation.Transactional"
})
@SupportedSourceVersion(SourceVersion.RELEASE_8)
public class TransactionContextProcessor extends AbstractProcessor {

    @Override
    public boolean process(Set<? extends TypeElement> annotations, RoundEnvironment roundEnv) {
        for (TypeElement annotation : annotations) {
            Set<? extends Element> elements = roundEnv.getElementsAnnotatedWith(annotation);

            for (Element element : elements) {
                if (element.getKind() != ElementKind.METHOD) {
                    continue;
                }

                ExecutableElement method = (ExecutableElement) element;
                // 添加调试信息
//                processingEnv.getMessager().printMessage(
//                        Diagnostic.Kind.WARNING,
//                        "Processing method: " + method.getSimpleName() + " in class: " + method.getEnclosingElement().toString()
//                );
                // 调试指定方法
                if (!method.getSimpleName().toString().equals("writeNormalTrxWithJta")) {
                    continue;
                }

                boolean hasTransactionContext = false;

                for (VariableElement parameter : method.getParameters()) {
                    TypeMirror paramType = parameter.asType();
                    if (paramType.toString().endsWith("TransactionMarker")) {
                        hasTransactionContext = true;
                        break;
                    }
                }

                if (!hasTransactionContext) {
                    processingEnv.getMessager().printMessage(
                            Diagnostic.Kind.ERROR,
                            "Method annotated with @Transactional must have (*)TransactionMarker parameter",
                            element
                    );
                }
            }
        }
        return true;
    }
}
