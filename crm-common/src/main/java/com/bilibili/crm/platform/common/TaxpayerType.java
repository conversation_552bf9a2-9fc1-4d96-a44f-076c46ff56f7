package com.bilibili.crm.platform.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

import java.util.Arrays;
import java.util.List;

/**
 * 纳税人类型 1-一般纳税人 2-小规模纳税人
 *
 * <AUTHOR>
 * @date 2022/8/29
 */
@AllArgsConstructor
public enum TaxpayerType {

    UNKNOWN(-1, "未知"),

    GENERAL_TAXPAYER(1, "一般纳税人"),

    SMALL_SCALE_GENERAL_TAXPAYER(2, "小规模纳税人"),

    ;


    @Getter
    private Integer code;
    @Getter
    private String typeName;

    public static TaxpayerType getByCode(Integer code) {
        for (TaxpayerType bean : values()) {
            if (bean.getCode().equals(code)) {
                return bean;
            }
        }
        return UNKNOWN;
    }

    public static TaxpayerType getByDesc(String typeName) {
        for (TaxpayerType bean : values()) {
            if (bean.getTypeName().equals(typeName)) {
                return bean;
            }
        }
        return UNKNOWN;
    }

    public static boolean isValid(Integer code) {
        for (TaxpayerType bean : values()) {
            if (!UNKNOWN.getCode().equals(code) && bean.getCode().equals(code)) {
                return true;
            }
        }
        return false;
    }
}
