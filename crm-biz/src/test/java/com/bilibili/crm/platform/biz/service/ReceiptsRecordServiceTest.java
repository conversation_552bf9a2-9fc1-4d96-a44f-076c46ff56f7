package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.bjcom.mock.BeanTestUtils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.crm.platform.api.contract.dto.NewReceiptsRecordDto;
import com.bilibili.crm.platform.api.contract.service.IContractService;
import com.bilibili.crm.platform.api.finance.service.IFinanceService;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.biz.dao.CrmContractReceiptsRecordDao;
import com.bilibili.crm.platform.biz.dao.CrmContractReceiptsRecordSaleMappingDao;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.redisson.api.RedissonClient;


public class ReceiptsRecordServiceTest extends MockitoDefaultTest {
    @Mock
    private CrmContractReceiptsRecordDao crmContractReceiptsRecordDao;

    @Mock
    private CrmContractReceiptsRecordSaleMappingDao crmContractReceiptsRecordSaleMappingDao;

    @Mock
    private IContractService contractService;

    @Mock
    private ISaleService saleService;

    @Mock
    private ILogOperatorService logOperatorService;

    @Mock
    private IFinanceService financeService;

    @Mock
    private RedissonClient redissonClient;

    @Mock
    private ReceiptsRecordServiceDelegate receiptsRecordServiceDelegate;
    @InjectMocks
    private ReceiptsRecordService receiptsRecordService;

    @Test
    public void createReceiptsRecord() {
        receiptsRecordService.createReceiptsRecord(BeanTestUtils.initSimpleFields(NewReceiptsRecordDto.builder().build()), Operator.SYSTEM);
    }
}
