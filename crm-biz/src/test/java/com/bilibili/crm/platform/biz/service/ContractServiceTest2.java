package com.bilibili.crm.platform.biz.service;

import java.util.*;

import com.bilibili.crm.platform.api.contract.dto.QueryContractParam;
import com.bilibili.crm.platform.api.rbac.IDomainUserService;
import com.bilibili.crm.platform.biz.po.CrmContractSaleMappingPo;
import com.bilibili.rbac.api.dto.UserBaseDto;
import com.bilibili.rbac.api.service.IUserRoleService;
import com.bilibili.rbac.api.service.IUserService;
import com.google.common.collect.Lists;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;

import com.alibaba.fastjson.JSON;
import com.bilibili.brand.api.schedule.soa.service.ISoaCptScheduleService;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.account.service.IRealNameMaterialService;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.company.service.ICorporationGroupService;
import com.bilibili.crm.platform.api.contract.dto.NewContractDto;
import com.bilibili.crm.platform.api.contract.dto.UpdateContractDto;
import com.bilibili.crm.platform.api.contract.service.ICloseContractNowService;
import com.bilibili.crm.platform.api.contract.service.IContractFinanceRebateRecordService;
import com.bilibili.crm.platform.api.contract.service.IContractMailOssService;
import com.bilibili.crm.platform.api.contract.service.IContractMailScreenshotService;
import com.bilibili.crm.platform.api.contract.service.IContractUserMappingService;
import com.bilibili.crm.platform.api.contract.service.IInvoiceRecordService;
import com.bilibili.crm.platform.api.contract.service.IReceiptsRecordService;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.dao.CrmContractDao;
import com.bilibili.crm.platform.biz.dao.CrmContractSaleMappingDao;
import com.bilibili.crm.platform.biz.dao.CrmContractStatusRecordDao;
import com.bilibili.crm.platform.biz.po.CrmContractPo;

public class ContractServiceTest2 extends BaseMockitoTest {
	@Mock
    private ICloseContractNowService closeContractNowService;
	@Mock
    private CrmContractDao crmContractDao;
	@Mock
    private CrmContractSaleMappingDao crmContractSaleMappingDao;
	@Mock
    private CrmContractStatusRecordDao statusRecordDao;
	@Mock
    private ISaleService saleService;
	@Mock
    private IInvoiceRecordService invoiceRecordService;
	@Mock
    private IReceiptsRecordService receiptsRecordService;
	@Mock
    private IAgentService agentService;
	@Mock
    private IOrderService orderService;
	@Mock
    private ILogOperatorService logOperatorService;
	@Mock
    private ApplicationEventPublisher applicationEventPublisher;
	@Mock
    private IQueryAccountService queryAccountService;
	@Mock
    private IRealNameMaterialService realNameMaterialService;
	@Mock
    private IContractMailScreenshotService screenshotService;
	@Mock
    private IContractMailOssService mailOssService;
	@Mock
    private ISoaCptScheduleService cptScheduleService;
	@Mock
    private IContractFinanceRebateRecordService contractFinanceRebateRecordService;
	@Mock
    private ICorporationGroupService corporationGroupService;
	@Mock
    private IContractUserMappingService contractUserMappingService;
	@Mock
	private IUserRoleService userRoleService;
	@Mock
	private IUserService userService;
	@Mock
	private IDomainUserService domainUserService;
	@InjectMocks
	private ContractService contractService;


	
	@Before
	public void before() {
		String contractPoStr = "{\"accountId\":101,\"agentId\":2,\"amount\":0,\"amountRemark\":\"\",\"archiveStatus\":0,\"beginTime\":*************,\"contractNumber\":**************,\"creator\":\"zhayuzhong\",\"ctime\":*************,\"deductQuota\":0,\"discount\":100.00,\"distribution\":100,\"endTime\":*************,\"financeTitleAgentId\":0,\"groupId\":1,\"id\":897,\"isDeleted\":0,\"legalContractId\":\"\",\"mtime\":*************,\"name\":\"配件箱009\",\"orderBeginTime\":*************,\"orderEndTime\":*************,\"productId\":1,\"productLineId\":1,\"projectName\":\"【测试】配件箱009\",\"promotionPolicy\":1,\"quotaId\":0,\"remark\":\"\",\"reviewRemark\":\"\",\"status\":1,\"type\":0,\"version\":0}";
		
		CrmContractPo crmContractPo = JSON.parseObject(contractPoStr, CrmContractPo.class);
		Mockito.when(crmContractDao.selectByPrimaryKey(Mockito.any())).thenReturn(crmContractPo);
		Mockito.when(crmContractDao.selectByExample(Mockito.any())).thenReturn(Arrays.asList(crmContractPo));
		Mockito.when(crmContractDao.countByExample(Mockito.any())).thenReturn(1L);
		Mockito.when(crmContractDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
		Mockito.when(crmContractDao.insertSelective(Mockito.any())).thenAnswer(answer ->{
			CrmContractPo po = answer.getArgument(0, CrmContractPo.class);
			po.setId(11);
			return po.getId();			
		});
        
		Mockito.when(queryAccountService.getAccountBaseDtoById(Mockito.any())).thenReturn(AccountBaseDto.builder().accountId(101).accountStatus(0).isAgent(0).build());
		Mockito.when(agentService.load(Mockito.any())).thenReturn(AgentDto.builder().build());
		
		Mockito.when(orderService.countOrdersByContractId(Mockito.any())).thenReturn(2L);
	}
	
	@Test
	public void getContractByIdTest() {
		contractService.getContractById(11);
	}
	
}
