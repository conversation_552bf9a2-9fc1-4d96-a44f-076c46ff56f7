package com.bilibili.crm.platform.biz.service.contract;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.statusmachine.contract.ContractStatusContext;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractAuditStatus;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.dao.CrmContractDao;
import com.bilibili.crm.platform.biz.dao.CrmContractStatusRecordDao;
import com.bilibili.crm.platform.biz.statusmachine.contract.updater.ContractStatusUpdater;
import com.bilibili.crm.platform.biz.statusmachine.status_mapping.StatusMapping;
import com.bilibili.crm.platform.common.ContractStatus;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

/**
 * <AUTHOR>
 * @date 2019/12/9
 **/
public class ContractStatusUpdaterTest extends BaseMockitoTest {

    @Mock
    private StatusMapping statusMapping;
    @Mock
    private CrmContractDao crmContractDao;
    @Mock
    private CrmContractStatusRecordDao statusRecordDao;
    @Mock
    private ILogOperatorService logOperatorService;
    @InjectMocks
    private ContractStatusUpdater testObject;

    @Before
    public void before () {

        Mockito.when(statusMapping.getContractOldStatus(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(ContractStatus.COMPLETED);

        Mockito.when(crmContractDao.updateByExampleSelective(Mockito.any(), Mockito.any())).thenReturn(1);
    }

    @Test
    public void updateBusStatus () {

        ContractStatusContext context = ContractStatusContext.builder()
                .contractId(1)
                .operator(operator)
                .currentBusStatus(ContractBusStatus.COMPLETED)
                .currentAuditStatus(ContractAuditStatus.SUCCESS)
                .beginTime(Utils.getToday())
                .endTime(Utils.getToday())
                .build();
        testObject.updateBusStatus(context, ContractBusStatus.COMPLETED);
    }

    @Test
    public void updateAuditStatus () {

        ContractStatusContext context = ContractStatusContext.builder()
                .contractId(1)
                .operator(operator)
                .currentBusStatus(ContractBusStatus.COMPLETED)
                .currentAuditStatus(ContractAuditStatus.SUCCESS)
                .beginTime(Utils.getToday())
                .endTime(Utils.getToday())
                .build();
        testObject.updateAuditStatus(context, ContractAuditStatus.SUCCESS);
    }


}
