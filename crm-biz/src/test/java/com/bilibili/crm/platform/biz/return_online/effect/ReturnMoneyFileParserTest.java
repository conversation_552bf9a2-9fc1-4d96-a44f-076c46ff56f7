package com.bilibili.crm.platform.biz.return_online.effect;

import com.alibaba.excel.EasyExcel;
import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.biz.service.return_online.effect.ReturnMoneyFileParser;
import com.bilibili.crm.platform.biz.service.return_online.effect.dto.ReturnMoneyExcelDataDto;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.core.io.ClassPathResource;

import java.io.IOException;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/29 12:09
 */
@Slf4j
public class ReturnMoneyFileParserTest {
    ReturnMoneyFileParser parser = new ReturnMoneyFileParser();

    @Test
    public void parse() throws IOException {

        ClassPathResource resource = new ClassPathResource("return_money.xlsx");
        List<ReturnMoneyExcelDataDto> dataList = EasyExcel.read(resource.getInputStream()).head(ReturnMoneyExcelDataDto.class).sheet().doReadSync();

        log.info(">>> dataList: {}", JSON.toJSONString(dataList));
    }

    @Test
    public void validate() {
        parser.validateReturnPeriod(1, "2022-Q1-12");
    }
}
