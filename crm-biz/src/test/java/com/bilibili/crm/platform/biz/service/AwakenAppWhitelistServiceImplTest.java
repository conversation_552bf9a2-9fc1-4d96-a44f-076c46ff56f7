package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.util.Page;
import com.bilibili.crm.platform.api.account.dto.AccountAwakenAppMappingDto;
import com.bilibili.crm.platform.api.account.service.IAccountAwakenAppMappingService;
import com.bilibili.crm.platform.api.app_awaken.dto.NewAwakenAppWhitelistDto;
import com.bilibili.crm.platform.api.app_awaken.dto.UpdateAwakenAppWhitelistDto;
import com.bilibili.crm.platform.api.logOperation.ILogService;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.dao.ResAwakenAppWhitelistDao;
import com.bilibili.crm.platform.biz.po.ResAwakenAppWhitelistPo;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.util.HashMap;
import java.util.List;

public class AwakenAppWhitelistServiceImplTest extends BaseMockitoTest {

    @InjectMocks
    private AwakenAppWhitelistServiceImpl awakenAppWhitelistService;

    @Mock
    private ResAwakenAppWhitelistDao resAwakenAppWhitelistDao;
    @Mock
    private IAccountAwakenAppMappingService accountAwakenAppMappingService;
    @Mock
    private ILogService logService;

    @Test
    public void testLoad() {
        Mockito.when(resAwakenAppWhitelistDao.selectByPrimaryKey(Mockito.anyInt())).thenReturn(ResAwakenAppWhitelistPo.builder().build());
        awakenAppWhitelistService.load(1);
    }

    @Test
    public void testGetByScheme() {
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder().id(1).build()));
        awakenAppWhitelistService.getByScheme("aaaa");
    }

    @Test
    public void testGetScheme2NameInSchemes() {
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder().id(1).scheme("aa").name("ss").build()));
        awakenAppWhitelistService.getScheme2NameInSchemes(Lists.newArrayList("aaaa"));
    }

    @Test
    public void testTestGetByScheme() {
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder().id(1).build()));
        Mockito.when(accountAwakenAppMappingService.getByAccountId(Mockito.anyInt())).thenReturn(Lists.newArrayList(AccountAwakenAppMappingDto.builder().accountId(1).appId(1).build()));
        awakenAppWhitelistService.getByScheme(1,"aaaa");
    }

    @Test
    public void testCreate() {
        Mockito.when(resAwakenAppWhitelistDao.insertSelective(Mockito.any())).thenReturn(1);
        awakenAppWhitelistService.create(operator, NewAwakenAppWhitelistDto.builder().name("ss").scheme("aa").build());
    }

    @Test
    public void testUpdate() {
        Mockito.when(resAwakenAppWhitelistDao.selectByPrimaryKey(Mockito.anyInt())).thenReturn(ResAwakenAppWhitelistPo.builder().name("a").scheme("s").build());
        awakenAppWhitelistService.update(operator, UpdateAwakenAppWhitelistDto.builder().id(1).name("ss").scheme("aa").build());

    }

    @Test
    public void testEnable() {
        Mockito.when(resAwakenAppWhitelistDao.selectByPrimaryKey(Mockito.anyInt())).thenReturn(ResAwakenAppWhitelistPo.builder().name("a").scheme("s").status(0).build());
        Mockito.when(resAwakenAppWhitelistDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        awakenAppWhitelistService.enable(operator, 1);
    }

    @Test
    public void testDisable() {
        Mockito.when(accountAwakenAppMappingService.getBindedAccountCountByAppId(Mockito.anyInt())).thenReturn(0L);
        Mockito.when(resAwakenAppWhitelistDao.selectByPrimaryKey(Mockito.anyInt())).thenReturn(ResAwakenAppWhitelistPo.builder().name("a").scheme("s").status(0).build());
        Mockito.when(resAwakenAppWhitelistDao.updateByPrimaryKeySelective(Mockito.any())).thenReturn(1);
        awakenAppWhitelistService.disable(operator, 1);
    }

    @Test
    public void testGetList() {
        Mockito.when(resAwakenAppWhitelistDao.countByExample(Mockito.any())).thenReturn(1L);
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder().scheme("aa").build()));
        awakenAppWhitelistService.getList(1, new Page());
    }

    @Test
    public void testGetAllValid() {
        Mockito.when(resAwakenAppWhitelistDao.selectByExample(Mockito.any())).thenReturn(Lists.newArrayList(ResAwakenAppWhitelistPo.builder().scheme("aa").build()));
        awakenAppWhitelistService.getAllValid();
    }

    @Test
    public void testGetAccountId2AppWhitelistMapInAccountIds() {
        HashMap<Integer, List<Integer>> map = new HashMap<>();
        map.put(1,Lists.newArrayList(1));
        Mockito.when(accountAwakenAppMappingService.getAccount2AppIdsMapInAccountIds(Mockito.anyList())).thenReturn(map);
        awakenAppWhitelistService.getAccountId2AppWhitelistMapInAccountIds(Lists.newArrayList(1));
    }

    @Test
    public void testGetAppWhitelistByAccountId() {
        Mockito.when(accountAwakenAppMappingService.getByAccountId(Mockito.anyInt())).thenReturn(Lists.newArrayList(AccountAwakenAppMappingDto.builder().appId(1).build()));
        awakenAppWhitelistService.getAppWhitelistByAccountId(1);
    }
}