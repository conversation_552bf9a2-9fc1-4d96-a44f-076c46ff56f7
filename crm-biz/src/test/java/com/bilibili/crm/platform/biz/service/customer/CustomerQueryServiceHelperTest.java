package com.bilibili.crm.platform.biz.service.customer;

import com.bilibili.crm.platform.api.customer.dto.CustomerQueryDto;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.po.CustomerPoExample;
import junit.framework.TestCase;
import org.junit.Test;
import org.mockito.InjectMocks;

import java.util.Arrays;
import java.util.List;

public class CustomerQueryServiceHelperTest extends BaseMockitoTest {

    @InjectMocks
    private CustomerQueryServiceHelper customerQueryServiceHelper;

    @Test
    public void testGetCustomerPoExample_null() {
        CustomerPoExample customerPoExample = customerQueryServiceHelper.getCustomerPoExample(null);
    }

    @Test
    public void testGetCustomerPoExample_not_null() {
        List<Integer> customerIdsOfAgent = Arrays.asList(1);
        CustomerPoExample customerPoExample =
                customerQueryServiceHelper.getCustomerPoExample(CustomerQueryDto.builder().customerIdsOfAgent(customerIdsOfAgent).customerIdsOfDepartment(customerIdsOfAgent)
                        .groupIds(customerIdsOfAgent)
                        .username("123")
                        .customerIdsOfChannelManager(customerIdsOfAgent).build());
    }
}