package com.bilibili.crm.platform.biz.service.order;

import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.contract.service.IContractService;
import com.bilibili.crm.platform.api.order.dto.OrderDto;
import com.bilibili.crm.platform.api.order.service.IOrderQueryService;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.dao.CrmOrderDao;
import com.bilibili.crm.platform.biz.dao.CrmOrderExtraDao;
import com.bilibili.crm.platform.biz.po.CrmOrderExtraPo;
import com.bilibili.crm.platform.biz.po.CrmOrderPo;
import com.bilibili.crm.platform.biz.service.mas.MutualAdSystemService;
import com.bilibili.crm.platform.common.CrmOrderStatus;
import com.bilibili.crm.platform.soa.dto.SoaOgvOrderQueryDto;
import com.bilibili.mas.api.soa.IMasSoaTaskService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.when;

public class MutualAdSystemServiceTest extends BaseMockitoTest {

    @Mock
    private IOrderService orderService;
    @Mock
    private CrmOrderDao orderDao;
    @Mock
    private IMasSoaTaskService masTaskService;
    @Mock
    private IContractService contractService;
    @InjectMocks
    private MutualAdSystemService mutualAdSystemService;
    @Mock
    private CrmOrderExtraDao crmOrderExtraDao;
    @Mock
    private IContractQueryService contractQueryService;
    @Mock
    private IOrderQueryService orderQueryService;
    @Before
    public void init() {
        String contractStr = "[{\"createUser\":\"sys\",\"ctime\":1546580425000,\"id\":1,\"isDeleted\":0,\"mtime\":1546919374000,\"name\":\"微盟集团\",\"tag\":\"\",\"updateUser\":\"sys\"},{\"createUser\":\"sys\",\"ctime\":1546580434000,\"id\":2,\"isDeleted\":0,\"mtime\":1546919386000,\"name\":\"阿里集团\",\"tag\":\"\",\"updateUser\":\"sys\"}]";
        List<ContractDto> contractDtos = JSON.parseArray(contractStr, ContractDto.class);
        when(contractQueryService.queryContractList(Mockito.any())).thenReturn(contractDtos);
        when(contractService.queryContractsInIds(Mockito.any())).thenReturn(contractDtos);

        String orderStr = "[{\"type\":9,\"ctime\":1546580425000,\"id\":1,\"isDeleted\":0,\"mtime\":1546919374000,\"name\":\"微盟集团\",\"tag\":\"\",\"updateUser\":\"sys\"},{\"type\":9,\"ctime\":1546580434000,\"id\":2,\"isDeleted\":0,\"mtime\":1546919386000,\"name\":\"阿里集团\",\"tag\":\"\",\"updateUser\":\"sys\"}]";
        List<CrmOrderPo> orderPos = JSON.parseArray(orderStr, CrmOrderPo.class);
        when(orderDao.selectByExample(Mockito.any())).thenReturn(orderPos);
        String orderDtoStr = "[{\"type\":9,\"ctime\":1546580425000,\"id\":1,\"isDeleted\":0,\"mtime\":1546919374000,\"attractInvestmentType\":1,\"tag\":\"\",\"updateUser\":\"sys\",\"status\":1},{\"type\":9,\"ctime\":1546580434000,\"id\":2,\"isDeleted\":0,\"mtime\":1546919386000,\"name\":\"阿里集团\",\"attractInvestmentType\":1,\"tag\":\"\",\"updateUser\":\"sys\",\"status\":1}]";
        List<OrderDto> orderDtos = JSON.parseArray(orderDtoStr, OrderDto.class);
        when(orderQueryService.queryOrderDtos(Mockito.any())).thenReturn(orderDtos);

        String orderExtraStr = "[{\"type\":9,\"ctime\":1546580425000,\"id\":1,\"isDeleted\":0,\"mtime\":1546919374000,\"name\":\"微盟集团\",\"tag\":\"\",\"updateUser\":\"sys\",\"crmOrderId\":1,\"attractInvestmentType\":1},{\"type\":9,\"ctime\":1546580434000,\"id\":2,\"isDeleted\":0,\"mtime\":1546919386000,\"name\":\"阿里集团\",\"tag\":\"\",\"updateUser\":\"sys\",\"crmOrderId\":2,\"attractInvestmentType\":2}]";
        List<CrmOrderExtraPo> orderExtraPos = JSON.parseArray(orderExtraStr, CrmOrderExtraPo.class);
        when(crmOrderExtraDao.selectByExample(Mockito.any())).thenReturn(orderExtraPos);
    }

    @Test
    public void queryOgvOrderByPageTest(){
        mutualAdSystemService.queryOgvOrderByPage(1,1,15);
    }

    @Test
    public void queryOgvOrderByParamTest(){
        mutualAdSystemService.queryOgvOrderByParam(SoaOgvOrderQueryDto.builder().accountId(1005).statusList(Arrays.asList(CrmOrderStatus.INVALID.getCode(),CrmOrderStatus.WAIT.getCode(),CrmOrderStatus.IN_PROGRESS.getCode())).build());

    }
}
