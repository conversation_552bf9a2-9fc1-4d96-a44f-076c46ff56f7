package com.bilibili.crm.platform.biz.service.account;

import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.crm.platform.api.account.dto.BizMappingDto;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.company.service.ICorporationGroupService;
import com.bilibili.crm.platform.biz.soa.SoaQueryAccountService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

public class SoaQueryAccountServiceTest extends MockitoDefaultTest {

    @Mock
    private IQueryAccountService queryAccountService;
    @Mock
    private ICorporationGroupService corporationGroupService;
    @InjectMocks
    private SoaQueryAccountService testObject;

    @Test
    public void getBiliUserBaseInfoByMidTest(){
        testObject.getBiliUserBaseInfoByMid(1);
    }

    @Test
    public void getMidTest(){
        testObject.getMid(BizMappingDto.builder().build());
    }

    @Test
    public void isPickUpKaAccount () {

        testObject.isPickUpKaAccount(1);
    }
}
