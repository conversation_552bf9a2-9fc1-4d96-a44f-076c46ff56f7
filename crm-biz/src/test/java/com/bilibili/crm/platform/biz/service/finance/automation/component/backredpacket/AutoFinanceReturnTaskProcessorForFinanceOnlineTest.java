package com.bilibili.crm.platform.biz.service.finance.automation.component.backredpacket;

import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.finance.service.IFinanceService;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.po.CrmAccountRechargeRecordPo;
import com.bilibili.crm.platform.biz.repo.finance.CrmAccountRedPacketBackRecordRepo;
import com.bilibili.crm.platform.biz.service.AccountFinanceServiceDelegate;
import com.bilibili.crm.platform.common.AdStatus;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.RechargeSource;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;

public class AutoFinanceReturnTaskProcessorForFinanceOnlineTest extends BaseMockitoTest {

    @InjectMocks
    private AutoBackRedPacketProcessorForFinanceOnline autoBackRedPacketProcessor;
    @Mock
    private AutoBackRedPacketConfig autoBackRedPacketConfig;
    @Mock
    private ILogOperatorService logOperatorService;
    @Mock
    private AccountFinanceServiceDelegate accountFinanceServiceDelegate;
    @Mock
    private CrmAccountRedPacketBackRecordRepo crmAccountRedPacketBackRecordRepo;
    @Mock
    private IQueryAccountService queryAccountService;
    @Mock
    private IFinanceService financeService;
    @Mock
    private RedisLock redisLock;

    /**
     * 自动返货
     * 非代理商
     */
    @Test
    public void testCreateBackRedPacketBySystemIfNecessary1() {
        AccAccountPo accountPo = AccAccountPo.builder()
                .accountId(1).isInner(IsInnerEnum.OUTER.getCode()).isSupportFly(IsValid.TRUE.getCode())
                .build();
        CrmAccountRechargeRecordPo recordPo = CrmAccountRechargeRecordPo.builder()
                .id(1001)
                .source(RechargeSource.BILIBILI_SALE.getCode())
                .build();
        autoBackRedPacketProcessor.createBackRedPacketBySystemIfNecessary(accountPo, recordPo);
    }

    /**
     * 自动返货
     * 内广
     */
    @Test
    public void testCreateBackRedPacketBySystemIfNecessary2() {
        AccAccountPo accountPo = AccAccountPo.builder()
                .accountId(1).isInner(IsInnerEnum.INNER.getCode()).isSupportFly(IsValid.TRUE.getCode())
                .build();
        CrmAccountRechargeRecordPo recordPo = CrmAccountRechargeRecordPo.builder()
                .id(1001)
                .source(RechargeSource.AGENT.getCode())
                .build();
        autoBackRedPacketProcessor.createBackRedPacketBySystemIfNecessary(accountPo, recordPo);
    }

    /**
     * 自动返货
     * （允许投放效果广告 或 允许投放内容起飞 或 允许投放商业起飞） 不满足
     */
    @Test
    public void testCreateBackRedPacketBySystemIfNecessary3() {
        AccAccountPo accountPo = AccAccountPo.builder()
                .accountId(1).isInner(IsInnerEnum.OUTER.getCode()).isSupportFly(IsValid.FALSE.getCode()).adStatus(AdStatus.DISABLE.getCode()).isSupportContent(IsValid.FALSE.getCode())
                .build();
        CrmAccountRechargeRecordPo recordPo = CrmAccountRechargeRecordPo.builder()
                .id(1001)
                .source(RechargeSource.AGENT.getCode())
                .build();
        autoBackRedPacketProcessor.createBackRedPacketBySystemIfNecessary(accountPo, recordPo);
    }

    /**
     * 自动返货
     * < 5万
     */
    @Test
    public void testCreateBackRedPacketBySystemIfNecessary4() {
        Mockito.when(autoBackRedPacketConfig.getBACK_AMOUNT()).thenReturn(50000L);
        AccAccountPo accountPo = AccAccountPo.builder()
                .accountId(1).isInner(IsInnerEnum.OUTER.getCode()).isSupportFly(IsValid.TRUE.getCode()).adStatus(AdStatus.DISABLE.getCode()).isSupportContent(IsValid.FALSE.getCode())
                .build();
        CrmAccountRechargeRecordPo recordPo = CrmAccountRechargeRecordPo.builder()
                .id(1001)
                .amount(10000L)
                .source(RechargeSource.AGENT.getCode())
                .build();
        autoBackRedPacketProcessor.createBackRedPacketBySystemIfNecessary(accountPo, recordPo);
    }

    /**
     * 自动返货
     * ((允许投放效果广告 或 允许投放内容起飞 或 允许投放商业起飞) && 外部账号 && 代理商账号) && 单笔充值大于等于5万元
     */
    @Test
    public void testCreateBackRedPacketBySystemIfNecessary5() {
        Mockito.when(autoBackRedPacketConfig.getBACK_AMOUNT()).thenReturn(50000L);
        Mockito.when(autoBackRedPacketConfig.getRedPacketBackDiscount()).thenReturn(new BigDecimal(0.1));
        CrmAccountRechargeRecordPo accountRechargeRecordPo = CrmAccountRechargeRecordPo.builder().build();
        Mockito.when(accountFinanceServiceDelegate.getRechargePoById(Mockito.anyInt())).thenReturn(accountRechargeRecordPo);
        AccountBaseDto accountBaseDto = AccountBaseDto.builder().build();
        Mockito.when(queryAccountService.getAccountBaseDtoById(Mockito.anyInt())).thenReturn(accountBaseDto);

        AccAccountPo accountPo = AccAccountPo.builder()
                .accountId(1).isInner(IsInnerEnum.OUTER.getCode()).isSupportFly(IsValid.TRUE.getCode()).adStatus(AdStatus.DISABLE.getCode()).isSupportContent(IsValid.FALSE.getCode())
                .build();
        CrmAccountRechargeRecordPo recordPo = CrmAccountRechargeRecordPo.builder()
                .id(1001)
                .amount(50000L)
                .source(RechargeSource.AGENT.getCode())
                .build();
        autoBackRedPacketProcessor.createBackRedPacketBySystemIfNecessary(accountPo, recordPo);
    }
}