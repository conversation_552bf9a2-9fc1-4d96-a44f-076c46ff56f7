package com.bilibili.crm.platform.biz.grpc;

import com.bapis.ad.crm.account.AccountIndustryItem;
import com.bilibili.crm.common.GrpcJsonUtil;
import org.junit.Test;

public class AccountReadServiceImplTest {

    @Test
    public void queryAccountIndustryByAccountId() {
        AccountIndustryItem item = AccountIndustryItem
                .newBuilder()
                .setAccountId(10)
                .build();
        System.out.println(GrpcJsonUtil.toJsonString(item));
    }
}