package com.bilibili.crm.platform.biz.service.income.service;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.income.dto.IncomeCompositionQueryParam;
import com.bilibili.crm.platform.api.income.dto.ProductIncomeComposition;
import com.bilibili.crm.platform.biz.service.income.config.product.ProductPvConfig;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

public class ProductQuotaServiceImplTest extends MockitoDefaultTest {
    @Mock
    private ProductPvManager productPvService;
    @Mock
    private WxQuotaService wxECPMService;
    @Mock
    private ProductPvConfig productPvConfig;
    @InjectMocks
    private ProductQuotaManager test;

    @Test
    public void queryAdQuota() {
        IncomeCompositionQueryParam param = IncomeCompositionQueryParam.builder().dateBegin(Utils.getToday()).dateEnd(Utils.getToday()).build();

        List<ProductIncomeComposition> list = Stream.of(
                ProductIncomeComposition.CPC_INFORMATION,
                ProductIncomeComposition.CPC_PLAY_PAGE,
                ProductIncomeComposition.CPM,
                ProductIncomeComposition.DPA,
                ProductIncomeComposition.ADX,
                ProductIncomeComposition.MAS,
                ProductIncomeComposition.CONTENT_FLY,
                ProductIncomeComposition.PERSON_FLY,
                ProductIncomeComposition.BUSINESS_FLY,
                ProductIncomeComposition.CPT,
                ProductIncomeComposition.SSA_CPT,
                ProductIncomeComposition.GD)
                .collect(Collectors.toList());
        list.forEach(composition -> {
            test.queryAdQuota(composition,param);
        });
    }


}
