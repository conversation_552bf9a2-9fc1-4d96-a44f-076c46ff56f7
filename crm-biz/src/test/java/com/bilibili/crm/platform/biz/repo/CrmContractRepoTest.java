package com.bilibili.crm.platform.biz.repo;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.dao.CrmContractDao;
import com.bilibili.crm.platform.biz.po.CrmContractPo;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

public class CrmContractRepoTest extends BaseMockitoTest {

    @Mock
    private CrmContractDao crmContractDao;
    @InjectMocks
    private CrmContractRepo crmContractRepo;

    /**
     * 修改 oa 开票信息
     */
    public void testUpdateOaBillInfo() {

    }

    /**
     * 合同归档状态
     * id null
     */
    @Test(expected = IllegalArgumentException.class)
    public void updateArchiveStatus1() {
        crmContractRepo.updateArchiveStatus(null, null);

    }

    /**
     * 合同归档状态
     * status null
     */
    @Test(expected = IllegalArgumentException.class)
    public void updateArchiveStatus2() {
        crmContractRepo.updateArchiveStatus(1, null);
    }

    /**
     * 合同归档状态
     * ok
     */
    @Test
    public void updateArchiveStatus3() {
        CrmContractPo crmContractPo = CrmContractPo.builder().id(1).build();
        Mockito.when(crmContractDao.selectByPrimaryKey(Mockito.anyInt())).thenReturn(crmContractPo);
        crmContractRepo.updateArchiveStatus(1, 2);

    }


    @Test
    public void testSomething() {
        String periodStart = Utils.getTimestamp2String(Utils.getSomeDayAgo(Utils.getNow(), 15), "yyyy-MM-dd HH:mm:ss");
        String periodEnd = Utils.getTimestamp2String(Utils.getNow(), "yyyy-MM-dd HH:mm:ss");
        System.out.println("dd");
    }
}