package com.bilibili.crm.platform.biz.service.account.label;

import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.api.account.dto.accountlabel.AccountLabelBatchBindItemDto;
import com.bilibili.crm.platform.api.account.dto.accountlabel.AccountLabelExcelItemDto;
import junit.framework.TestCase;
import org.junit.Test;

import java.util.ArrayList;
import java.util.List;

public class AccountLabelBatchUploadProcTest extends TestCase {

    @Test
    public void testSpliceData() {
        AccountLabelBatchUploadProc uploadProc = new AccountLabelBatchUploadProc();
        List<AccountLabelExcelItemDto> excelItemDtoList = new ArrayList<>();
        AccountLabelExcelItemDto excelItemDto = AccountLabelExcelItemDto.builder()
                .accountIdsStr("123，4324")
                .labelIdsStr("1,11")
                .build();
        excelItemDtoList.add(excelItemDto);
        List<AccountLabelBatchBindItemDto> batchBindItemDtos = uploadProc.spliceData(excelItemDtoList);
        System.out.println(JSON.toJSONString(batchBindItemDtos));
    }
}