package com.bilibili.crm.platform.biz.util;

import com.bilibili.crm.platform.biz.service.income.util.Java8DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.junit.Test;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2022/7/20 17:38
 */
@Slf4j
public class DateUtilsTest {

    @Test
    public void timeRange() {
        Long fromTime = 1658307829804L;
        Long toTime = 1655715829804L;
        Assert.isTrue(Java8DateUtils.validMonthRange(fromTime, toTime, 3), "最大跨度为三个自然月");
    }
}
