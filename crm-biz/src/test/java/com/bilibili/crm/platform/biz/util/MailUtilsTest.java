package com.bilibili.crm.platform.biz.util;


import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.bjcom.mock.MockitoDefaultTest;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.google.common.collect.Lists;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;

public class MailUtilsTest extends MockitoDefaultTest {

    @InjectMocks
    private MailUtils test;

    @Mock
    private ISaleService saleService;

    @Mock
    private IMailService mailService;

    @Test
    public void saleIdConvert2Email() {
        test.saleIdsConvert2Email(Arrays.asList(1));
    }

    @Test
    public void sendEmail() {
        test.sendEmail("test","test", Lists.newArrayList("brady"));
    }
}
