package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.contract.service.IContractService;
import com.bilibili.crm.platform.api.finance.dto.BrandIncomeConfirmationDto;
import com.bilibili.crm.platform.api.finance.service.IBrandIncomeConfirmationService;
import com.bilibili.crm.platform.api.interlayer.dto.InterlayerCalculateDto;
import com.bilibili.crm.platform.api.interlayer.service.IInterlayerMoneyService;
import com.bilibili.crm.platform.api.order.dto.OrderBaseDto;
import com.bilibili.crm.platform.api.order.service.IOrderQueryService;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.api.statusmachine.order.enums.OrderAuditStatus;
import com.bilibili.crm.platform.api.statusmachine.order.enums.OrderBusStatus;
import com.bilibili.crm.platform.biz.BaseMockitoTest;
import com.bilibili.crm.platform.biz.service.settle_account.SettleAccountService;
import com.bilibili.crm.platform.biz.service.settle_account.bill.BillCreatorService;
import com.bilibili.crm.platform.biz.service.settle_account.statement.StatementResultService;
import com.bilibili.crm.platform.common.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.List;
import java.util.Map;

/**
 * <AUTHOR>
 * @date 2019/6/21
 **/
public class SettleAccountServiceTest extends BaseMockitoTest {

    @Mock
    private IContractService contractService;
    @Mock
    private IOrderService orderService;
    @Mock
    private IInterlayerMoneyService interlayerMoneyService;
    @Mock
    private IBrandIncomeConfirmationService incomeConfirmationService;
    @Mock
    private BillCreatorService billCreatorService;
    @Mock
    private StatementResultService statementResultService;
    @Mock
    private IContractQueryService contractQueryService;
    @Mock
    private IOrderQueryService orderQueryService;
    @Mock
    private LogOperatorService logOperatorService;

    @InjectMocks
    private SettleAccountService testObject;

    @Before
    public void before() {

        Map<String, List<BrandIncomeConfirmationDto>> confirmMap = Maps.newHashMap();
        confirmMap.put("2019-06-01", Lists.newArrayList(BrandIncomeConfirmationDto.builder()
                .type(BrandIncomeConfirmType.FINANCE.getCode())
                .status(BrandIncomeConfirmStatus.CONFIRMED.getCode())
                .build()));
        Mockito.when(incomeConfirmationService.queryBrandIncomeConfirmationMap()).thenReturn(confirmMap);

        Map<Integer, ContractDto> contractMap = Maps.newHashMap();
        contractMap.put(1, ContractDto.builder().id(1).build());
        Mockito.when(contractQueryService.queryContractMapInIds(Mockito.any())).thenReturn(contractMap);

        Map<Integer, List<OrderBaseDto>> orderMap = Maps.newHashMap();
        orderMap.put(1, Lists.newArrayList(OrderBaseDto.builder()
                .type(CrmOrderType.CPT.getCode())
                .resourceType(CrmOrderResourceType.SALES.getCode())
                .status(CrmOrderStatus.IN_PROGRESS.getCode())
                .busStatus(OrderBusStatus.EXECUTING.getCode())
                .auditStatus(OrderAuditStatus.SUCCESS.getCode())
                .amount(1000L)
                .discount(new BigDecimal(100))
                .beginTime(Utils.getToday())
                .endTime(Utils.getEndOfDay(Utils.getToday()))
                .settleType(CrmSettleType.DAILY_TYPE.getCode())
                .build()));
        Mockito.when(orderQueryService.getOrderMapInCrmContractIds(Mockito.any())).thenReturn(orderMap);

        Mockito.when(billCreatorService.generate(Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(Lists.newArrayList(InterlayerCalculateDto.builder().build()));

        Mockito.when(contractService.getContractById(Mockito.any())).thenReturn(ContractDto.builder().build());
    }

}
