<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.pickup.readonly.dao.CmOrderDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CmOrderPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="order_no" jdbcType="BIGINT" property="orderNo" />
    <result column="task_id" jdbcType="INTEGER" property="taskId" />
    <result column="mcn_id" jdbcType="BIGINT" property="mcnId" />
    <result column="upper_mid" jdbcType="BIGINT" property="upperMid" />
    <result column="agent_id" jdbcType="INTEGER" property="agentId" />
    <result column="secondary_agent_mid" jdbcType="BIGINT" property="secondaryAgentMid" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="cooperation_type" jdbcType="TINYINT" property="cooperationType" />
    <result column="paid_price" jdbcType="DECIMAL" property="paidPrice" />
    <result column="upper_price" jdbcType="DECIMAL" property="upperPrice" />
    <result column="revise_base" jdbcType="DECIMAL" property="reviseBase" />
    <result column="platform_price" jdbcType="DECIMAL" property="platformPrice" />
    <result column="actual_upper_price" jdbcType="DECIMAL" property="actualUpperPrice" />
    <result column="actual_platform_price" jdbcType="DECIMAL" property="actualPlatformPrice" />
    <result column="actual_cost_price" jdbcType="DECIMAL" property="actualCostPrice" />
    <result column="service_fee_rate" jdbcType="DECIMAL" property="serviceFeeRate" />
    <result column="service_fee" jdbcType="DECIMAL" property="serviceFee" />
    <result column="price" jdbcType="DECIMAL" property="price" />
    <result column="order_status" jdbcType="TINYINT" property="orderStatus" />
    <result column="termination_before_status" jdbcType="TINYINT" property="terminationBeforeStatus" />
    <result column="is_publish_invitation_ad" jdbcType="TINYINT" property="isPublishInvitationAd" />
    <result column="is_skip_deliverable" jdbcType="TINYINT" property="isSkipDeliverable" />
    <result column="sales_support_user" jdbcType="VARCHAR" property="salesSupportUser" />
    <result column="sales_support_user_id" jdbcType="INTEGER" property="salesSupportUserId" />
    <result column="online_time" jdbcType="TIMESTAMP" property="onlineTime" />
    <result column="upper_type" jdbcType="TINYINT" property="upperType" />
    <result column="crm_contract_order_id" jdbcType="INTEGER" property="crmContractOrderId" />
    <result column="chaodian_order_id" jdbcType="VARCHAR" property="chaodianOrderId" />
    <result column="accept_invitation_ad_status" jdbcType="TINYINT" property="acceptInvitationAdStatus" />
    <result column="status_mtime" jdbcType="TIMESTAMP" property="statusMtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="final_draft_id" jdbcType="INTEGER" property="finalDraftId" />
    <result column="discount_rate" jdbcType="DECIMAL" property="discountRate" />
    <result column="operator_type" jdbcType="INTEGER" property="operatorType" />
    <result column="operate_status" jdbcType="INTEGER" property="operateStatus" />
    <result column="is_core_account" jdbcType="TINYINT" property="isCoreAccount" />
    <result column="service_provider_id" jdbcType="INTEGER" property="serviceProviderId" />
    <result column="settle_type" jdbcType="TINYINT" property="settleType" />
    <result column="service_provider_fee_rate" jdbcType="DECIMAL" property="serviceProviderFeeRate" />
    <result column="payment_confirm" jdbcType="TINYINT" property="paymentConfirm" />
    <result column="payment_confirm_time" jdbcType="TIMESTAMP" property="paymentConfirmTime" />
    <result column="is_ka_account" jdbcType="TINYINT" property="isKaAccount" />
    <result column="settle_info_id" jdbcType="INTEGER" property="settleInfoId" />
    <result column="refund_price" jdbcType="DECIMAL" property="refundPrice" />
    <result column="refund_voucher_url" jdbcType="VARCHAR" property="refundVoucherUrl" />
    <result column="online_start_time" jdbcType="TIMESTAMP" property="onlineStartTime" />
    <result column="cooperation_type_remark" jdbcType="VARCHAR" property="cooperationTypeRemark" />
    <result column="is_change_price" jdbcType="TINYINT" property="isChangePrice" />
    <result column="publish_id" jdbcType="INTEGER" property="publishId" />
    <result column="special_settle_sign" jdbcType="TINYINT" property="specialSettleSign" />
    <result column="service_provider_income" jdbcType="DECIMAL" property="serviceProviderIncome" />
    <result column="order_rights" jdbcType="INTEGER" property="orderRights" />
    <result column="sec_agent_id" jdbcType="INTEGER" property="secAgentId" />
    <result column="expected_online_time" jdbcType="TIMESTAMP" property="expectedOnlineTime" />
    <result column="draft_delayed_time" jdbcType="TIMESTAMP" property="draftDelayedTime" />
    <result column="order_type" jdbcType="TINYINT" property="orderType" />
    <result column="price_version" jdbcType="TINYINT" property="priceVersion" />
    <result column="terminated_service_fee_refund" jdbcType="DECIMAL" property="terminatedServiceFeeRefund" />
    <result column="draft_reject_reason_id" jdbcType="INTEGER" property="draftRejectReasonId" />
    <result column="out_task_no" jdbcType="VARCHAR" property="outTaskNo" />
    <result column="out_task_cm" jdbcType="TINYINT" property="outTaskCm" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="invite_ad_version" jdbcType="TINYINT" property="inviteAdVersion" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, order_no, task_id, mcn_id, upper_mid, agent_id, secondary_agent_mid, account_id, 
    cooperation_type, paid_price, upper_price, revise_base, platform_price, actual_upper_price, 
    actual_platform_price, actual_cost_price, service_fee_rate, service_fee, price, order_status, 
    termination_before_status, is_publish_invitation_ad, is_skip_deliverable, sales_support_user, 
    sales_support_user_id, online_time, upper_type, crm_contract_order_id, chaodian_order_id, 
    accept_invitation_ad_status, status_mtime, is_deleted, ctime, mtime, final_draft_id, 
    discount_rate, operator_type, operate_status, is_core_account, service_provider_id, 
    settle_type, service_provider_fee_rate, payment_confirm, payment_confirm_time, is_ka_account, 
    settle_info_id, refund_price, refund_voucher_url, online_start_time, cooperation_type_remark, 
    is_change_price, publish_id, special_settle_sign, service_provider_income, order_rights, 
    sec_agent_id, expected_online_time, draft_delayed_time, order_type, price_version, 
    terminated_service_fee_refund, draft_reject_reason_id, out_task_no, out_task_cm, 
    remark, invite_ad_version
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from cm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from cm_order
    where id = #{id,jdbcType=INTEGER}
  </select>
<!--  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">-->
<!--    delete from cm_order-->
<!--    where id = #{id,jdbcType=INTEGER}-->
<!--  </delete>-->
<!--  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPoExample">-->
<!--    delete from cm_order-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Example_Where_Clause" />-->
<!--    </if>-->
<!--  </delete>-->
<!--  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPo">-->
<!--    insert into cm_order (id, order_no, task_id, -->
<!--      mcn_id, upper_mid, agent_id, -->
<!--      secondary_agent_mid, account_id, cooperation_type, -->
<!--      paid_price, upper_price, revise_base, -->
<!--      platform_price, actual_upper_price, actual_platform_price, -->
<!--      actual_cost_price, service_fee_rate, service_fee, -->
<!--      price, order_status, termination_before_status, -->
<!--      is_publish_invitation_ad, is_skip_deliverable, -->
<!--      sales_support_user, sales_support_user_id, online_time, -->
<!--      upper_type, crm_contract_order_id, chaodian_order_id, -->
<!--      accept_invitation_ad_status, status_mtime, -->
<!--      is_deleted, ctime, mtime, -->
<!--      final_draft_id, discount_rate, operator_type, -->
<!--      operate_status, is_core_account, service_provider_id, -->
<!--      settle_type, service_provider_fee_rate, payment_confirm, -->
<!--      payment_confirm_time, is_ka_account, settle_info_id, -->
<!--      refund_price, refund_voucher_url, online_start_time, -->
<!--      cooperation_type_remark, is_change_price, publish_id, -->
<!--      special_settle_sign, service_provider_income, -->
<!--      order_rights, sec_agent_id, expected_online_time, -->
<!--      draft_delayed_time, order_type, price_version, -->
<!--      terminated_service_fee_refund, draft_reject_reason_id, -->
<!--      out_task_no, out_task_cm, remark, -->
<!--      invite_ad_version)-->
<!--    values (#{id,jdbcType=INTEGER}, #{orderNo,jdbcType=BIGINT}, #{taskId,jdbcType=INTEGER}, -->
<!--      #{mcnId,jdbcType=BIGINT}, #{upperMid,jdbcType=BIGINT}, #{agentId,jdbcType=INTEGER}, -->
<!--      #{secondaryAgentMid,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{cooperationType,jdbcType=TINYINT}, -->
<!--      #{paidPrice,jdbcType=DECIMAL}, #{upperPrice,jdbcType=DECIMAL}, #{reviseBase,jdbcType=DECIMAL}, -->
<!--      #{platformPrice,jdbcType=DECIMAL}, #{actualUpperPrice,jdbcType=DECIMAL}, #{actualPlatformPrice,jdbcType=DECIMAL}, -->
<!--      #{actualCostPrice,jdbcType=DECIMAL}, #{serviceFeeRate,jdbcType=DECIMAL}, #{serviceFee,jdbcType=DECIMAL}, -->
<!--      #{price,jdbcType=DECIMAL}, #{orderStatus,jdbcType=TINYINT}, #{terminationBeforeStatus,jdbcType=TINYINT}, -->
<!--      #{isPublishInvitationAd,jdbcType=TINYINT}, #{isSkipDeliverable,jdbcType=TINYINT}, -->
<!--      #{salesSupportUser,jdbcType=VARCHAR}, #{salesSupportUserId,jdbcType=INTEGER}, #{onlineTime,jdbcType=TIMESTAMP}, -->
<!--      #{upperType,jdbcType=TINYINT}, #{crmContractOrderId,jdbcType=INTEGER}, #{chaodianOrderId,jdbcType=VARCHAR}, -->
<!--      #{acceptInvitationAdStatus,jdbcType=TINYINT}, #{statusMtime,jdbcType=TIMESTAMP}, -->
<!--      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, -->
<!--      #{finalDraftId,jdbcType=INTEGER}, #{discountRate,jdbcType=DECIMAL}, #{operatorType,jdbcType=INTEGER}, -->
<!--      #{operateStatus,jdbcType=INTEGER}, #{isCoreAccount,jdbcType=TINYINT}, #{serviceProviderId,jdbcType=INTEGER}, -->
<!--      #{settleType,jdbcType=TINYINT}, #{serviceProviderFeeRate,jdbcType=DECIMAL}, #{paymentConfirm,jdbcType=TINYINT}, -->
<!--      #{paymentConfirmTime,jdbcType=TIMESTAMP}, #{isKaAccount,jdbcType=TINYINT}, #{settleInfoId,jdbcType=INTEGER}, -->
<!--      #{refundPrice,jdbcType=DECIMAL}, #{refundVoucherUrl,jdbcType=VARCHAR}, #{onlineStartTime,jdbcType=TIMESTAMP}, -->
<!--      #{cooperationTypeRemark,jdbcType=VARCHAR}, #{isChangePrice,jdbcType=TINYINT}, #{publishId,jdbcType=INTEGER}, -->
<!--      #{specialSettleSign,jdbcType=TINYINT}, #{serviceProviderIncome,jdbcType=DECIMAL}, -->
<!--      #{orderRights,jdbcType=INTEGER}, #{secAgentId,jdbcType=INTEGER}, #{expectedOnlineTime,jdbcType=TIMESTAMP}, -->
<!--      #{draftDelayedTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=TINYINT}, #{priceVersion,jdbcType=TINYINT}, -->
<!--      #{terminatedServiceFeeRefund,jdbcType=DECIMAL}, #{draftRejectReasonId,jdbcType=INTEGER}, -->
<!--      #{outTaskNo,jdbcType=VARCHAR}, #{outTaskCm,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, -->
<!--      #{inviteAdVersion,jdbcType=TINYINT})-->
<!--  </insert>-->
<!--  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPo">-->
<!--    insert into cm_order-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="orderNo != null">-->
<!--        order_no,-->
<!--      </if>-->
<!--      <if test="taskId != null">-->
<!--        task_id,-->
<!--      </if>-->
<!--      <if test="mcnId != null">-->
<!--        mcn_id,-->
<!--      </if>-->
<!--      <if test="upperMid != null">-->
<!--        upper_mid,-->
<!--      </if>-->
<!--      <if test="agentId != null">-->
<!--        agent_id,-->
<!--      </if>-->
<!--      <if test="secondaryAgentMid != null">-->
<!--        secondary_agent_mid,-->
<!--      </if>-->
<!--      <if test="accountId != null">-->
<!--        account_id,-->
<!--      </if>-->
<!--      <if test="cooperationType != null">-->
<!--        cooperation_type,-->
<!--      </if>-->
<!--      <if test="paidPrice != null">-->
<!--        paid_price,-->
<!--      </if>-->
<!--      <if test="upperPrice != null">-->
<!--        upper_price,-->
<!--      </if>-->
<!--      <if test="reviseBase != null">-->
<!--        revise_base,-->
<!--      </if>-->
<!--      <if test="platformPrice != null">-->
<!--        platform_price,-->
<!--      </if>-->
<!--      <if test="actualUpperPrice != null">-->
<!--        actual_upper_price,-->
<!--      </if>-->
<!--      <if test="actualPlatformPrice != null">-->
<!--        actual_platform_price,-->
<!--      </if>-->
<!--      <if test="actualCostPrice != null">-->
<!--        actual_cost_price,-->
<!--      </if>-->
<!--      <if test="serviceFeeRate != null">-->
<!--        service_fee_rate,-->
<!--      </if>-->
<!--      <if test="serviceFee != null">-->
<!--        service_fee,-->
<!--      </if>-->
<!--      <if test="price != null">-->
<!--        price,-->
<!--      </if>-->
<!--      <if test="orderStatus != null">-->
<!--        order_status,-->
<!--      </if>-->
<!--      <if test="terminationBeforeStatus != null">-->
<!--        termination_before_status,-->
<!--      </if>-->
<!--      <if test="isPublishInvitationAd != null">-->
<!--        is_publish_invitation_ad,-->
<!--      </if>-->
<!--      <if test="isSkipDeliverable != null">-->
<!--        is_skip_deliverable,-->
<!--      </if>-->
<!--      <if test="salesSupportUser != null">-->
<!--        sales_support_user,-->
<!--      </if>-->
<!--      <if test="salesSupportUserId != null">-->
<!--        sales_support_user_id,-->
<!--      </if>-->
<!--      <if test="onlineTime != null">-->
<!--        online_time,-->
<!--      </if>-->
<!--      <if test="upperType != null">-->
<!--        upper_type,-->
<!--      </if>-->
<!--      <if test="crmContractOrderId != null">-->
<!--        crm_contract_order_id,-->
<!--      </if>-->
<!--      <if test="chaodianOrderId != null">-->
<!--        chaodian_order_id,-->
<!--      </if>-->
<!--      <if test="acceptInvitationAdStatus != null">-->
<!--        accept_invitation_ad_status,-->
<!--      </if>-->
<!--      <if test="statusMtime != null">-->
<!--        status_mtime,-->
<!--      </if>-->
<!--      <if test="isDeleted != null">-->
<!--        is_deleted,-->
<!--      </if>-->
<!--      <if test="ctime != null">-->
<!--        ctime,-->
<!--      </if>-->
<!--      <if test="mtime != null">-->
<!--        mtime,-->
<!--      </if>-->
<!--      <if test="finalDraftId != null">-->
<!--        final_draft_id,-->
<!--      </if>-->
<!--      <if test="discountRate != null">-->
<!--        discount_rate,-->
<!--      </if>-->
<!--      <if test="operatorType != null">-->
<!--        operator_type,-->
<!--      </if>-->
<!--      <if test="operateStatus != null">-->
<!--        operate_status,-->
<!--      </if>-->
<!--      <if test="isCoreAccount != null">-->
<!--        is_core_account,-->
<!--      </if>-->
<!--      <if test="serviceProviderId != null">-->
<!--        service_provider_id,-->
<!--      </if>-->
<!--      <if test="settleType != null">-->
<!--        settle_type,-->
<!--      </if>-->
<!--      <if test="serviceProviderFeeRate != null">-->
<!--        service_provider_fee_rate,-->
<!--      </if>-->
<!--      <if test="paymentConfirm != null">-->
<!--        payment_confirm,-->
<!--      </if>-->
<!--      <if test="paymentConfirmTime != null">-->
<!--        payment_confirm_time,-->
<!--      </if>-->
<!--      <if test="isKaAccount != null">-->
<!--        is_ka_account,-->
<!--      </if>-->
<!--      <if test="settleInfoId != null">-->
<!--        settle_info_id,-->
<!--      </if>-->
<!--      <if test="refundPrice != null">-->
<!--        refund_price,-->
<!--      </if>-->
<!--      <if test="refundVoucherUrl != null">-->
<!--        refund_voucher_url,-->
<!--      </if>-->
<!--      <if test="onlineStartTime != null">-->
<!--        online_start_time,-->
<!--      </if>-->
<!--      <if test="cooperationTypeRemark != null">-->
<!--        cooperation_type_remark,-->
<!--      </if>-->
<!--      <if test="isChangePrice != null">-->
<!--        is_change_price,-->
<!--      </if>-->
<!--      <if test="publishId != null">-->
<!--        publish_id,-->
<!--      </if>-->
<!--      <if test="specialSettleSign != null">-->
<!--        special_settle_sign,-->
<!--      </if>-->
<!--      <if test="serviceProviderIncome != null">-->
<!--        service_provider_income,-->
<!--      </if>-->
<!--      <if test="orderRights != null">-->
<!--        order_rights,-->
<!--      </if>-->
<!--      <if test="secAgentId != null">-->
<!--        sec_agent_id,-->
<!--      </if>-->
<!--      <if test="expectedOnlineTime != null">-->
<!--        expected_online_time,-->
<!--      </if>-->
<!--      <if test="draftDelayedTime != null">-->
<!--        draft_delayed_time,-->
<!--      </if>-->
<!--      <if test="orderType != null">-->
<!--        order_type,-->
<!--      </if>-->
<!--      <if test="priceVersion != null">-->
<!--        price_version,-->
<!--      </if>-->
<!--      <if test="terminatedServiceFeeRefund != null">-->
<!--        terminated_service_fee_refund,-->
<!--      </if>-->
<!--      <if test="draftRejectReasonId != null">-->
<!--        draft_reject_reason_id,-->
<!--      </if>-->
<!--      <if test="outTaskNo != null">-->
<!--        out_task_no,-->
<!--      </if>-->
<!--      <if test="outTaskCm != null">-->
<!--        out_task_cm,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="inviteAdVersion != null">-->
<!--        invite_ad_version,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="orderNo != null">-->
<!--        #{orderNo,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="taskId != null">-->
<!--        #{taskId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="mcnId != null">-->
<!--        #{mcnId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="upperMid != null">-->
<!--        #{upperMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="agentId != null">-->
<!--        #{agentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="secondaryAgentMid != null">-->
<!--        #{secondaryAgentMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="accountId != null">-->
<!--        #{accountId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="cooperationType != null">-->
<!--        #{cooperationType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="paidPrice != null">-->
<!--        #{paidPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="upperPrice != null">-->
<!--        #{upperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="reviseBase != null">-->
<!--        #{reviseBase,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="platformPrice != null">-->
<!--        #{platformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualUpperPrice != null">-->
<!--        #{actualUpperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualPlatformPrice != null">-->
<!--        #{actualPlatformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualCostPrice != null">-->
<!--        #{actualCostPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="serviceFeeRate != null">-->
<!--        #{serviceFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="serviceFee != null">-->
<!--        #{serviceFee,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="price != null">-->
<!--        #{price,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="orderStatus != null">-->
<!--        #{orderStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="terminationBeforeStatus != null">-->
<!--        #{terminationBeforeStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="isPublishInvitationAd != null">-->
<!--        #{isPublishInvitationAd,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="isSkipDeliverable != null">-->
<!--        #{isSkipDeliverable,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="salesSupportUser != null">-->
<!--        #{salesSupportUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesSupportUserId != null">-->
<!--        #{salesSupportUserId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="onlineTime != null">-->
<!--        #{onlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="upperType != null">-->
<!--        #{upperType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="crmContractOrderId != null">-->
<!--        #{crmContractOrderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="chaodianOrderId != null">-->
<!--        #{chaodianOrderId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="acceptInvitationAdStatus != null">-->
<!--        #{acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="statusMtime != null">-->
<!--        #{statusMtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="isDeleted != null">-->
<!--        #{isDeleted,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="ctime != null">-->
<!--        #{ctime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="mtime != null">-->
<!--        #{mtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="finalDraftId != null">-->
<!--        #{finalDraftId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="discountRate != null">-->
<!--        #{discountRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="operatorType != null">-->
<!--        #{operatorType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="operateStatus != null">-->
<!--        #{operateStatus,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isCoreAccount != null">-->
<!--        #{isCoreAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderId != null">-->
<!--        #{serviceProviderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="settleType != null">-->
<!--        #{settleType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderFeeRate != null">-->
<!--        #{serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="paymentConfirm != null">-->
<!--        #{paymentConfirm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="paymentConfirmTime != null">-->
<!--        #{paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="isKaAccount != null">-->
<!--        #{isKaAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="settleInfoId != null">-->
<!--        #{settleInfoId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="refundPrice != null">-->
<!--        #{refundPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="refundVoucherUrl != null">-->
<!--        #{refundVoucherUrl,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="onlineStartTime != null">-->
<!--        #{onlineStartTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="cooperationTypeRemark != null">-->
<!--        #{cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isChangePrice != null">-->
<!--        #{isChangePrice,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="publishId != null">-->
<!--        #{publishId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="specialSettleSign != null">-->
<!--        #{specialSettleSign,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderIncome != null">-->
<!--        #{serviceProviderIncome,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="orderRights != null">-->
<!--        #{orderRights,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="secAgentId != null">-->
<!--        #{secAgentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="expectedOnlineTime != null">-->
<!--        #{expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="draftDelayedTime != null">-->
<!--        #{draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="orderType != null">-->
<!--        #{orderType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="priceVersion != null">-->
<!--        #{priceVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="terminatedServiceFeeRefund != null">-->
<!--        #{terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="draftRejectReasonId != null">-->
<!--        #{draftRejectReasonId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="outTaskNo != null">-->
<!--        #{outTaskNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="outTaskCm != null">-->
<!--        #{outTaskCm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="inviteAdVersion != null">-->
<!--        #{inviteAdVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPoExample" resultType="java.lang.Long">
    select count(*) from cm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <select id="actualUpperPriceSum" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPoExample" resultType="java.lang.Double">
    select  IFNULL(sum(actual_upper_price),0) from cm_order
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
<!--  <update id="updateByExampleSelective" parameterType="map">-->
<!--    update cm_order-->
<!--    <set>-->
<!--      <if test="record.id != null">-->
<!--        id = #{record.id,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.orderNo != null">-->
<!--        order_no = #{record.orderNo,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.taskId != null">-->
<!--        task_id = #{record.taskId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.mcnId != null">-->
<!--        mcn_id = #{record.mcnId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.upperMid != null">-->
<!--        upper_mid = #{record.upperMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.agentId != null">-->
<!--        agent_id = #{record.agentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.secondaryAgentMid != null">-->
<!--        secondary_agent_mid = #{record.secondaryAgentMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="record.accountId != null">-->
<!--        account_id = #{record.accountId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.cooperationType != null">-->
<!--        cooperation_type = #{record.cooperationType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.paidPrice != null">-->
<!--        paid_price = #{record.paidPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.upperPrice != null">-->
<!--        upper_price = #{record.upperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.reviseBase != null">-->
<!--        revise_base = #{record.reviseBase,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.platformPrice != null">-->
<!--        platform_price = #{record.platformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.actualUpperPrice != null">-->
<!--        actual_upper_price = #{record.actualUpperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.actualPlatformPrice != null">-->
<!--        actual_platform_price = #{record.actualPlatformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.actualCostPrice != null">-->
<!--        actual_cost_price = #{record.actualCostPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.serviceFeeRate != null">-->
<!--        service_fee_rate = #{record.serviceFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.serviceFee != null">-->
<!--        service_fee = #{record.serviceFee,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.price != null">-->
<!--        price = #{record.price,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.orderStatus != null">-->
<!--        order_status = #{record.orderStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.terminationBeforeStatus != null">-->
<!--        termination_before_status = #{record.terminationBeforeStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.isPublishInvitationAd != null">-->
<!--        is_publish_invitation_ad = #{record.isPublishInvitationAd,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.isSkipDeliverable != null">-->
<!--        is_skip_deliverable = #{record.isSkipDeliverable,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.salesSupportUser != null">-->
<!--        sales_support_user = #{record.salesSupportUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.salesSupportUserId != null">-->
<!--        sales_support_user_id = #{record.salesSupportUserId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.onlineTime != null">-->
<!--        online_time = #{record.onlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.upperType != null">-->
<!--        upper_type = #{record.upperType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.crmContractOrderId != null">-->
<!--        crm_contract_order_id = #{record.crmContractOrderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.chaodianOrderId != null">-->
<!--        chaodian_order_id = #{record.chaodianOrderId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.acceptInvitationAdStatus != null">-->
<!--        accept_invitation_ad_status = #{record.acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.statusMtime != null">-->
<!--        status_mtime = #{record.statusMtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.isDeleted != null">-->
<!--        is_deleted = #{record.isDeleted,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.ctime != null">-->
<!--        ctime = #{record.ctime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.mtime != null">-->
<!--        mtime = #{record.mtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.finalDraftId != null">-->
<!--        final_draft_id = #{record.finalDraftId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.discountRate != null">-->
<!--        discount_rate = #{record.discountRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.operatorType != null">-->
<!--        operator_type = #{record.operatorType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.operateStatus != null">-->
<!--        operate_status = #{record.operateStatus,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.isCoreAccount != null">-->
<!--        is_core_account = #{record.isCoreAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.serviceProviderId != null">-->
<!--        service_provider_id = #{record.serviceProviderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.settleType != null">-->
<!--        settle_type = #{record.settleType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.serviceProviderFeeRate != null">-->
<!--        service_provider_fee_rate = #{record.serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.paymentConfirm != null">-->
<!--        payment_confirm = #{record.paymentConfirm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.paymentConfirmTime != null">-->
<!--        payment_confirm_time = #{record.paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.isKaAccount != null">-->
<!--        is_ka_account = #{record.isKaAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.settleInfoId != null">-->
<!--        settle_info_id = #{record.settleInfoId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.refundPrice != null">-->
<!--        refund_price = #{record.refundPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.refundVoucherUrl != null">-->
<!--        refund_voucher_url = #{record.refundVoucherUrl,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.onlineStartTime != null">-->
<!--        online_start_time = #{record.onlineStartTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.cooperationTypeRemark != null">-->
<!--        cooperation_type_remark = #{record.cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.isChangePrice != null">-->
<!--        is_change_price = #{record.isChangePrice,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.publishId != null">-->
<!--        publish_id = #{record.publishId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.specialSettleSign != null">-->
<!--        special_settle_sign = #{record.specialSettleSign,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.serviceProviderIncome != null">-->
<!--        service_provider_income = #{record.serviceProviderIncome,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.orderRights != null">-->
<!--        order_rights = #{record.orderRights,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.secAgentId != null">-->
<!--        sec_agent_id = #{record.secAgentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.expectedOnlineTime != null">-->
<!--        expected_online_time = #{record.expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.draftDelayedTime != null">-->
<!--        draft_delayed_time = #{record.draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="record.orderType != null">-->
<!--        order_type = #{record.orderType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.priceVersion != null">-->
<!--        price_version = #{record.priceVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.terminatedServiceFeeRefund != null">-->
<!--        terminated_service_fee_refund = #{record.terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="record.draftRejectReasonId != null">-->
<!--        draft_reject_reason_id = #{record.draftRejectReasonId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="record.outTaskNo != null">-->
<!--        out_task_no = #{record.outTaskNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.outTaskCm != null">-->
<!--        out_task_cm = #{record.outTaskCm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="record.remark != null">-->
<!--        remark = #{record.remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="record.inviteAdVersion != null">-->
<!--        invite_ad_version = #{record.inviteAdVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--    </set>-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
<!--  <update id="updateByExample" parameterType="map">-->
<!--    update cm_order-->
<!--    set id = #{record.id,jdbcType=INTEGER},-->
<!--      order_no = #{record.orderNo,jdbcType=BIGINT},-->
<!--      task_id = #{record.taskId,jdbcType=INTEGER},-->
<!--      mcn_id = #{record.mcnId,jdbcType=BIGINT},-->
<!--      upper_mid = #{record.upperMid,jdbcType=BIGINT},-->
<!--      agent_id = #{record.agentId,jdbcType=INTEGER},-->
<!--      secondary_agent_mid = #{record.secondaryAgentMid,jdbcType=BIGINT},-->
<!--      account_id = #{record.accountId,jdbcType=INTEGER},-->
<!--      cooperation_type = #{record.cooperationType,jdbcType=TINYINT},-->
<!--      paid_price = #{record.paidPrice,jdbcType=DECIMAL},-->
<!--      upper_price = #{record.upperPrice,jdbcType=DECIMAL},-->
<!--      revise_base = #{record.reviseBase,jdbcType=DECIMAL},-->
<!--      platform_price = #{record.platformPrice,jdbcType=DECIMAL},-->
<!--      actual_upper_price = #{record.actualUpperPrice,jdbcType=DECIMAL},-->
<!--      actual_platform_price = #{record.actualPlatformPrice,jdbcType=DECIMAL},-->
<!--      actual_cost_price = #{record.actualCostPrice,jdbcType=DECIMAL},-->
<!--      service_fee_rate = #{record.serviceFeeRate,jdbcType=DECIMAL},-->
<!--      service_fee = #{record.serviceFee,jdbcType=DECIMAL},-->
<!--      price = #{record.price,jdbcType=DECIMAL},-->
<!--      order_status = #{record.orderStatus,jdbcType=TINYINT},-->
<!--      termination_before_status = #{record.terminationBeforeStatus,jdbcType=TINYINT},-->
<!--      is_publish_invitation_ad = #{record.isPublishInvitationAd,jdbcType=TINYINT},-->
<!--      is_skip_deliverable = #{record.isSkipDeliverable,jdbcType=TINYINT},-->
<!--      sales_support_user = #{record.salesSupportUser,jdbcType=VARCHAR},-->
<!--      sales_support_user_id = #{record.salesSupportUserId,jdbcType=INTEGER},-->
<!--      online_time = #{record.onlineTime,jdbcType=TIMESTAMP},-->
<!--      upper_type = #{record.upperType,jdbcType=TINYINT},-->
<!--      crm_contract_order_id = #{record.crmContractOrderId,jdbcType=INTEGER},-->
<!--      chaodian_order_id = #{record.chaodianOrderId,jdbcType=VARCHAR},-->
<!--      accept_invitation_ad_status = #{record.acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--      status_mtime = #{record.statusMtime,jdbcType=TIMESTAMP},-->
<!--      is_deleted = #{record.isDeleted,jdbcType=TINYINT},-->
<!--      ctime = #{record.ctime,jdbcType=TIMESTAMP},-->
<!--      mtime = #{record.mtime,jdbcType=TIMESTAMP},-->
<!--      final_draft_id = #{record.finalDraftId,jdbcType=INTEGER},-->
<!--      discount_rate = #{record.discountRate,jdbcType=DECIMAL},-->
<!--      operator_type = #{record.operatorType,jdbcType=INTEGER},-->
<!--      operate_status = #{record.operateStatus,jdbcType=INTEGER},-->
<!--      is_core_account = #{record.isCoreAccount,jdbcType=TINYINT},-->
<!--      service_provider_id = #{record.serviceProviderId,jdbcType=INTEGER},-->
<!--      settle_type = #{record.settleType,jdbcType=TINYINT},-->
<!--      service_provider_fee_rate = #{record.serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--      payment_confirm = #{record.paymentConfirm,jdbcType=TINYINT},-->
<!--      payment_confirm_time = #{record.paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--      is_ka_account = #{record.isKaAccount,jdbcType=TINYINT},-->
<!--      settle_info_id = #{record.settleInfoId,jdbcType=INTEGER},-->
<!--      refund_price = #{record.refundPrice,jdbcType=DECIMAL},-->
<!--      refund_voucher_url = #{record.refundVoucherUrl,jdbcType=VARCHAR},-->
<!--      online_start_time = #{record.onlineStartTime,jdbcType=TIMESTAMP},-->
<!--      cooperation_type_remark = #{record.cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--      is_change_price = #{record.isChangePrice,jdbcType=TINYINT},-->
<!--      publish_id = #{record.publishId,jdbcType=INTEGER},-->
<!--      special_settle_sign = #{record.specialSettleSign,jdbcType=TINYINT},-->
<!--      service_provider_income = #{record.serviceProviderIncome,jdbcType=DECIMAL},-->
<!--      order_rights = #{record.orderRights,jdbcType=INTEGER},-->
<!--      sec_agent_id = #{record.secAgentId,jdbcType=INTEGER},-->
<!--      expected_online_time = #{record.expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--      draft_delayed_time = #{record.draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--      order_type = #{record.orderType,jdbcType=TINYINT},-->
<!--      price_version = #{record.priceVersion,jdbcType=TINYINT},-->
<!--      terminated_service_fee_refund = #{record.terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--      draft_reject_reason_id = #{record.draftRejectReasonId,jdbcType=INTEGER},-->
<!--      out_task_no = #{record.outTaskNo,jdbcType=VARCHAR},-->
<!--      out_task_cm = #{record.outTaskCm,jdbcType=TINYINT},-->
<!--      remark = #{record.remark,jdbcType=VARCHAR},-->
<!--      invite_ad_version = #{record.inviteAdVersion,jdbcType=TINYINT}-->
<!--    <if test="_parameter != null">-->
<!--      <include refid="Update_By_Example_Where_Clause" />-->
<!--    </if>-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPo">-->
<!--    update cm_order-->
<!--    <set>-->
<!--      <if test="orderNo != null">-->
<!--        order_no = #{orderNo,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="taskId != null">-->
<!--        task_id = #{taskId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="mcnId != null">-->
<!--        mcn_id = #{mcnId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="upperMid != null">-->
<!--        upper_mid = #{upperMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="agentId != null">-->
<!--        agent_id = #{agentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="secondaryAgentMid != null">-->
<!--        secondary_agent_mid = #{secondaryAgentMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="accountId != null">-->
<!--        account_id = #{accountId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="cooperationType != null">-->
<!--        cooperation_type = #{cooperationType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="paidPrice != null">-->
<!--        paid_price = #{paidPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="upperPrice != null">-->
<!--        upper_price = #{upperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="reviseBase != null">-->
<!--        revise_base = #{reviseBase,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="platformPrice != null">-->
<!--        platform_price = #{platformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualUpperPrice != null">-->
<!--        actual_upper_price = #{actualUpperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualPlatformPrice != null">-->
<!--        actual_platform_price = #{actualPlatformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualCostPrice != null">-->
<!--        actual_cost_price = #{actualCostPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="serviceFeeRate != null">-->
<!--        service_fee_rate = #{serviceFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="serviceFee != null">-->
<!--        service_fee = #{serviceFee,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="price != null">-->
<!--        price = #{price,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="orderStatus != null">-->
<!--        order_status = #{orderStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="terminationBeforeStatus != null">-->
<!--        termination_before_status = #{terminationBeforeStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="isPublishInvitationAd != null">-->
<!--        is_publish_invitation_ad = #{isPublishInvitationAd,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="isSkipDeliverable != null">-->
<!--        is_skip_deliverable = #{isSkipDeliverable,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="salesSupportUser != null">-->
<!--        sales_support_user = #{salesSupportUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesSupportUserId != null">-->
<!--        sales_support_user_id = #{salesSupportUserId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="onlineTime != null">-->
<!--        online_time = #{onlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="upperType != null">-->
<!--        upper_type = #{upperType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="crmContractOrderId != null">-->
<!--        crm_contract_order_id = #{crmContractOrderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="chaodianOrderId != null">-->
<!--        chaodian_order_id = #{chaodianOrderId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="acceptInvitationAdStatus != null">-->
<!--        accept_invitation_ad_status = #{acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="statusMtime != null">-->
<!--        status_mtime = #{statusMtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="isDeleted != null">-->
<!--        is_deleted = #{isDeleted,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="ctime != null">-->
<!--        ctime = #{ctime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="mtime != null">-->
<!--        mtime = #{mtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="finalDraftId != null">-->
<!--        final_draft_id = #{finalDraftId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="discountRate != null">-->
<!--        discount_rate = #{discountRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="operatorType != null">-->
<!--        operator_type = #{operatorType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="operateStatus != null">-->
<!--        operate_status = #{operateStatus,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isCoreAccount != null">-->
<!--        is_core_account = #{isCoreAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderId != null">-->
<!--        service_provider_id = #{serviceProviderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="settleType != null">-->
<!--        settle_type = #{settleType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderFeeRate != null">-->
<!--        service_provider_fee_rate = #{serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="paymentConfirm != null">-->
<!--        payment_confirm = #{paymentConfirm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="paymentConfirmTime != null">-->
<!--        payment_confirm_time = #{paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="isKaAccount != null">-->
<!--        is_ka_account = #{isKaAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="settleInfoId != null">-->
<!--        settle_info_id = #{settleInfoId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="refundPrice != null">-->
<!--        refund_price = #{refundPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="refundVoucherUrl != null">-->
<!--        refund_voucher_url = #{refundVoucherUrl,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="onlineStartTime != null">-->
<!--        online_start_time = #{onlineStartTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="cooperationTypeRemark != null">-->
<!--        cooperation_type_remark = #{cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isChangePrice != null">-->
<!--        is_change_price = #{isChangePrice,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="publishId != null">-->
<!--        publish_id = #{publishId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="specialSettleSign != null">-->
<!--        special_settle_sign = #{specialSettleSign,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderIncome != null">-->
<!--        service_provider_income = #{serviceProviderIncome,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="orderRights != null">-->
<!--        order_rights = #{orderRights,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="secAgentId != null">-->
<!--        sec_agent_id = #{secAgentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="expectedOnlineTime != null">-->
<!--        expected_online_time = #{expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="draftDelayedTime != null">-->
<!--        draft_delayed_time = #{draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="orderType != null">-->
<!--        order_type = #{orderType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="priceVersion != null">-->
<!--        price_version = #{priceVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="terminatedServiceFeeRefund != null">-->
<!--        terminated_service_fee_refund = #{terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="draftRejectReasonId != null">-->
<!--        draft_reject_reason_id = #{draftRejectReasonId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="outTaskNo != null">-->
<!--        out_task_no = #{outTaskNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="outTaskCm != null">-->
<!--        out_task_cm = #{outTaskCm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark = #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="inviteAdVersion != null">-->
<!--        invite_ad_version = #{inviteAdVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--    </set>-->
<!--    where id = #{id,jdbcType=INTEGER}-->
<!--  </update>-->
<!--  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPo">-->
<!--    update cm_order-->
<!--    set order_no = #{orderNo,jdbcType=BIGINT},-->
<!--      task_id = #{taskId,jdbcType=INTEGER},-->
<!--      mcn_id = #{mcnId,jdbcType=BIGINT},-->
<!--      upper_mid = #{upperMid,jdbcType=BIGINT},-->
<!--      agent_id = #{agentId,jdbcType=INTEGER},-->
<!--      secondary_agent_mid = #{secondaryAgentMid,jdbcType=BIGINT},-->
<!--      account_id = #{accountId,jdbcType=INTEGER},-->
<!--      cooperation_type = #{cooperationType,jdbcType=TINYINT},-->
<!--      paid_price = #{paidPrice,jdbcType=DECIMAL},-->
<!--      upper_price = #{upperPrice,jdbcType=DECIMAL},-->
<!--      revise_base = #{reviseBase,jdbcType=DECIMAL},-->
<!--      platform_price = #{platformPrice,jdbcType=DECIMAL},-->
<!--      actual_upper_price = #{actualUpperPrice,jdbcType=DECIMAL},-->
<!--      actual_platform_price = #{actualPlatformPrice,jdbcType=DECIMAL},-->
<!--      actual_cost_price = #{actualCostPrice,jdbcType=DECIMAL},-->
<!--      service_fee_rate = #{serviceFeeRate,jdbcType=DECIMAL},-->
<!--      service_fee = #{serviceFee,jdbcType=DECIMAL},-->
<!--      price = #{price,jdbcType=DECIMAL},-->
<!--      order_status = #{orderStatus,jdbcType=TINYINT},-->
<!--      termination_before_status = #{terminationBeforeStatus,jdbcType=TINYINT},-->
<!--      is_publish_invitation_ad = #{isPublishInvitationAd,jdbcType=TINYINT},-->
<!--      is_skip_deliverable = #{isSkipDeliverable,jdbcType=TINYINT},-->
<!--      sales_support_user = #{salesSupportUser,jdbcType=VARCHAR},-->
<!--      sales_support_user_id = #{salesSupportUserId,jdbcType=INTEGER},-->
<!--      online_time = #{onlineTime,jdbcType=TIMESTAMP},-->
<!--      upper_type = #{upperType,jdbcType=TINYINT},-->
<!--      crm_contract_order_id = #{crmContractOrderId,jdbcType=INTEGER},-->
<!--      chaodian_order_id = #{chaodianOrderId,jdbcType=VARCHAR},-->
<!--      accept_invitation_ad_status = #{acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--      status_mtime = #{statusMtime,jdbcType=TIMESTAMP},-->
<!--      is_deleted = #{isDeleted,jdbcType=TINYINT},-->
<!--      ctime = #{ctime,jdbcType=TIMESTAMP},-->
<!--      mtime = #{mtime,jdbcType=TIMESTAMP},-->
<!--      final_draft_id = #{finalDraftId,jdbcType=INTEGER},-->
<!--      discount_rate = #{discountRate,jdbcType=DECIMAL},-->
<!--      operator_type = #{operatorType,jdbcType=INTEGER},-->
<!--      operate_status = #{operateStatus,jdbcType=INTEGER},-->
<!--      is_core_account = #{isCoreAccount,jdbcType=TINYINT},-->
<!--      service_provider_id = #{serviceProviderId,jdbcType=INTEGER},-->
<!--      settle_type = #{settleType,jdbcType=TINYINT},-->
<!--      service_provider_fee_rate = #{serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--      payment_confirm = #{paymentConfirm,jdbcType=TINYINT},-->
<!--      payment_confirm_time = #{paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--      is_ka_account = #{isKaAccount,jdbcType=TINYINT},-->
<!--      settle_info_id = #{settleInfoId,jdbcType=INTEGER},-->
<!--      refund_price = #{refundPrice,jdbcType=DECIMAL},-->
<!--      refund_voucher_url = #{refundVoucherUrl,jdbcType=VARCHAR},-->
<!--      online_start_time = #{onlineStartTime,jdbcType=TIMESTAMP},-->
<!--      cooperation_type_remark = #{cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--      is_change_price = #{isChangePrice,jdbcType=TINYINT},-->
<!--      publish_id = #{publishId,jdbcType=INTEGER},-->
<!--      special_settle_sign = #{specialSettleSign,jdbcType=TINYINT},-->
<!--      service_provider_income = #{serviceProviderIncome,jdbcType=DECIMAL},-->
<!--      order_rights = #{orderRights,jdbcType=INTEGER},-->
<!--      sec_agent_id = #{secAgentId,jdbcType=INTEGER},-->
<!--      expected_online_time = #{expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--      draft_delayed_time = #{draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--      order_type = #{orderType,jdbcType=TINYINT},-->
<!--      price_version = #{priceVersion,jdbcType=TINYINT},-->
<!--      terminated_service_fee_refund = #{terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--      draft_reject_reason_id = #{draftRejectReasonId,jdbcType=INTEGER},-->
<!--      out_task_no = #{outTaskNo,jdbcType=VARCHAR},-->
<!--      out_task_cm = #{outTaskCm,jdbcType=TINYINT},-->
<!--      remark = #{remark,jdbcType=VARCHAR},-->
<!--      invite_ad_version = #{inviteAdVersion,jdbcType=TINYINT}-->
<!--    where id = #{id,jdbcType=INTEGER}-->
<!--  </update>-->
<!--  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPo">-->
<!--    insert into cm_order (id, order_no, task_id, -->
<!--      mcn_id, upper_mid, agent_id, -->
<!--      secondary_agent_mid, account_id, cooperation_type, -->
<!--      paid_price, upper_price, revise_base, -->
<!--      platform_price, actual_upper_price, actual_platform_price, -->
<!--      actual_cost_price, service_fee_rate, service_fee, -->
<!--      price, order_status, termination_before_status, -->
<!--      is_publish_invitation_ad, is_skip_deliverable, -->
<!--      sales_support_user, sales_support_user_id, online_time, -->
<!--      upper_type, crm_contract_order_id, chaodian_order_id, -->
<!--      accept_invitation_ad_status, status_mtime, -->
<!--      is_deleted, ctime, mtime, -->
<!--      final_draft_id, discount_rate, operator_type, -->
<!--      operate_status, is_core_account, service_provider_id, -->
<!--      settle_type, service_provider_fee_rate, payment_confirm, -->
<!--      payment_confirm_time, is_ka_account, settle_info_id, -->
<!--      refund_price, refund_voucher_url, online_start_time, -->
<!--      cooperation_type_remark, is_change_price, publish_id, -->
<!--      special_settle_sign, service_provider_income, -->
<!--      order_rights, sec_agent_id, expected_online_time, -->
<!--      draft_delayed_time, order_type, price_version, -->
<!--      terminated_service_fee_refund, draft_reject_reason_id, -->
<!--      out_task_no, out_task_cm, remark, -->
<!--      invite_ad_version)-->
<!--    values (#{id,jdbcType=INTEGER}, #{orderNo,jdbcType=BIGINT}, #{taskId,jdbcType=INTEGER}, -->
<!--      #{mcnId,jdbcType=BIGINT}, #{upperMid,jdbcType=BIGINT}, #{agentId,jdbcType=INTEGER}, -->
<!--      #{secondaryAgentMid,jdbcType=BIGINT}, #{accountId,jdbcType=INTEGER}, #{cooperationType,jdbcType=TINYINT}, -->
<!--      #{paidPrice,jdbcType=DECIMAL}, #{upperPrice,jdbcType=DECIMAL}, #{reviseBase,jdbcType=DECIMAL}, -->
<!--      #{platformPrice,jdbcType=DECIMAL}, #{actualUpperPrice,jdbcType=DECIMAL}, #{actualPlatformPrice,jdbcType=DECIMAL}, -->
<!--      #{actualCostPrice,jdbcType=DECIMAL}, #{serviceFeeRate,jdbcType=DECIMAL}, #{serviceFee,jdbcType=DECIMAL}, -->
<!--      #{price,jdbcType=DECIMAL}, #{orderStatus,jdbcType=TINYINT}, #{terminationBeforeStatus,jdbcType=TINYINT}, -->
<!--      #{isPublishInvitationAd,jdbcType=TINYINT}, #{isSkipDeliverable,jdbcType=TINYINT}, -->
<!--      #{salesSupportUser,jdbcType=VARCHAR}, #{salesSupportUserId,jdbcType=INTEGER}, #{onlineTime,jdbcType=TIMESTAMP}, -->
<!--      #{upperType,jdbcType=TINYINT}, #{crmContractOrderId,jdbcType=INTEGER}, #{chaodianOrderId,jdbcType=VARCHAR}, -->
<!--      #{acceptInvitationAdStatus,jdbcType=TINYINT}, #{statusMtime,jdbcType=TIMESTAMP}, -->
<!--      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, -->
<!--      #{finalDraftId,jdbcType=INTEGER}, #{discountRate,jdbcType=DECIMAL}, #{operatorType,jdbcType=INTEGER}, -->
<!--      #{operateStatus,jdbcType=INTEGER}, #{isCoreAccount,jdbcType=TINYINT}, #{serviceProviderId,jdbcType=INTEGER}, -->
<!--      #{settleType,jdbcType=TINYINT}, #{serviceProviderFeeRate,jdbcType=DECIMAL}, #{paymentConfirm,jdbcType=TINYINT}, -->
<!--      #{paymentConfirmTime,jdbcType=TIMESTAMP}, #{isKaAccount,jdbcType=TINYINT}, #{settleInfoId,jdbcType=INTEGER}, -->
<!--      #{refundPrice,jdbcType=DECIMAL}, #{refundVoucherUrl,jdbcType=VARCHAR}, #{onlineStartTime,jdbcType=TIMESTAMP}, -->
<!--      #{cooperationTypeRemark,jdbcType=VARCHAR}, #{isChangePrice,jdbcType=TINYINT}, #{publishId,jdbcType=INTEGER}, -->
<!--      #{specialSettleSign,jdbcType=TINYINT}, #{serviceProviderIncome,jdbcType=DECIMAL}, -->
<!--      #{orderRights,jdbcType=INTEGER}, #{secAgentId,jdbcType=INTEGER}, #{expectedOnlineTime,jdbcType=TIMESTAMP}, -->
<!--      #{draftDelayedTime,jdbcType=TIMESTAMP}, #{orderType,jdbcType=TINYINT}, #{priceVersion,jdbcType=TINYINT}, -->
<!--      #{terminatedServiceFeeRefund,jdbcType=DECIMAL}, #{draftRejectReasonId,jdbcType=INTEGER}, -->
<!--      #{outTaskNo,jdbcType=VARCHAR}, #{outTaskCm,jdbcType=TINYINT}, #{remark,jdbcType=VARCHAR}, -->
<!--      #{inviteAdVersion,jdbcType=TINYINT})-->
<!--    <trim prefix="on duplicate key update" suffixOverrides=",">-->
<!--      order_no = values(order_no),-->
<!--      task_id = values(task_id),-->
<!--      mcn_id = values(mcn_id),-->
<!--      upper_mid = values(upper_mid),-->
<!--      agent_id = values(agent_id),-->
<!--      secondary_agent_mid = values(secondary_agent_mid),-->
<!--      account_id = values(account_id),-->
<!--      cooperation_type = values(cooperation_type),-->
<!--      paid_price = values(paid_price),-->
<!--      upper_price = values(upper_price),-->
<!--      revise_base = values(revise_base),-->
<!--      platform_price = values(platform_price),-->
<!--      actual_upper_price = values(actual_upper_price),-->
<!--      actual_platform_price = values(actual_platform_price),-->
<!--      actual_cost_price = values(actual_cost_price),-->
<!--      service_fee_rate = values(service_fee_rate),-->
<!--      service_fee = values(service_fee),-->
<!--      price = values(price),-->
<!--      order_status = values(order_status),-->
<!--      termination_before_status = values(termination_before_status),-->
<!--      is_publish_invitation_ad = values(is_publish_invitation_ad),-->
<!--      is_skip_deliverable = values(is_skip_deliverable),-->
<!--      sales_support_user = values(sales_support_user),-->
<!--      sales_support_user_id = values(sales_support_user_id),-->
<!--      online_time = values(online_time),-->
<!--      upper_type = values(upper_type),-->
<!--      crm_contract_order_id = values(crm_contract_order_id),-->
<!--      chaodian_order_id = values(chaodian_order_id),-->
<!--      accept_invitation_ad_status = values(accept_invitation_ad_status),-->
<!--      status_mtime = values(status_mtime),-->
<!--      is_deleted = values(is_deleted),-->
<!--      ctime = values(ctime),-->
<!--      mtime = values(mtime),-->
<!--      final_draft_id = values(final_draft_id),-->
<!--      discount_rate = values(discount_rate),-->
<!--      operator_type = values(operator_type),-->
<!--      operate_status = values(operate_status),-->
<!--      is_core_account = values(is_core_account),-->
<!--      service_provider_id = values(service_provider_id),-->
<!--      settle_type = values(settle_type),-->
<!--      service_provider_fee_rate = values(service_provider_fee_rate),-->
<!--      payment_confirm = values(payment_confirm),-->
<!--      payment_confirm_time = values(payment_confirm_time),-->
<!--      is_ka_account = values(is_ka_account),-->
<!--      settle_info_id = values(settle_info_id),-->
<!--      refund_price = values(refund_price),-->
<!--      refund_voucher_url = values(refund_voucher_url),-->
<!--      online_start_time = values(online_start_time),-->
<!--      cooperation_type_remark = values(cooperation_type_remark),-->
<!--      is_change_price = values(is_change_price),-->
<!--      publish_id = values(publish_id),-->
<!--      special_settle_sign = values(special_settle_sign),-->
<!--      service_provider_income = values(service_provider_income),-->
<!--      order_rights = values(order_rights),-->
<!--      sec_agent_id = values(sec_agent_id),-->
<!--      expected_online_time = values(expected_online_time),-->
<!--      draft_delayed_time = values(draft_delayed_time),-->
<!--      order_type = values(order_type),-->
<!--      price_version = values(price_version),-->
<!--      terminated_service_fee_refund = values(terminated_service_fee_refund),-->
<!--      draft_reject_reason_id = values(draft_reject_reason_id),-->
<!--      out_task_no = values(out_task_no),-->
<!--      out_task_cm = values(out_task_cm),-->
<!--      remark = values(remark),-->
<!--      invite_ad_version = values(invite_ad_version),-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <insert id="insertBatch" parameterType="java.util.List">-->
<!--    insert into -->
<!--      cm_order-->
<!--      (order_no,task_id,mcn_id,upper_mid,agent_id,secondary_agent_mid,account_id,cooperation_type,paid_price,upper_price,revise_base,platform_price,actual_upper_price,actual_platform_price,actual_cost_price,service_fee_rate,service_fee,price,order_status,termination_before_status,is_publish_invitation_ad,is_skip_deliverable,sales_support_user,sales_support_user_id,online_time,upper_type,crm_contract_order_id,chaodian_order_id,accept_invitation_ad_status,status_mtime,is_deleted,ctime,mtime,final_draft_id,discount_rate,operator_type,operate_status,is_core_account,service_provider_id,settle_type,service_provider_fee_rate,payment_confirm,payment_confirm_time,is_ka_account,settle_info_id,refund_price,refund_voucher_url,online_start_time,cooperation_type_remark,is_change_price,publish_id,special_settle_sign,service_provider_income,order_rights,sec_agent_id,expected_online_time,draft_delayed_time,order_type,price_version,terminated_service_fee_refund,draft_reject_reason_id,out_task_no,out_task_cm,remark,invite_ad_version)-->
<!--    values-->
<!--    <foreach collection="list" item="item" separator=",">-->
<!--      <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--        #{item.orderNo,jdbcType=BIGINT},-->
<!--        #{item.taskId,jdbcType=INTEGER},-->
<!--        #{item.mcnId,jdbcType=BIGINT},-->
<!--        #{item.upperMid,jdbcType=BIGINT},-->
<!--        #{item.agentId,jdbcType=INTEGER},-->
<!--        #{item.secondaryAgentMid,jdbcType=BIGINT},-->
<!--        #{item.accountId,jdbcType=INTEGER},-->
<!--        #{item.cooperationType,jdbcType=TINYINT},-->
<!--        #{item.paidPrice,jdbcType=DECIMAL},-->
<!--        #{item.upperPrice,jdbcType=DECIMAL},-->
<!--        #{item.reviseBase,jdbcType=DECIMAL},-->
<!--        #{item.platformPrice,jdbcType=DECIMAL},-->
<!--        #{item.actualUpperPrice,jdbcType=DECIMAL},-->
<!--        #{item.actualPlatformPrice,jdbcType=DECIMAL},-->
<!--        #{item.actualCostPrice,jdbcType=DECIMAL},-->
<!--        #{item.serviceFeeRate,jdbcType=DECIMAL},-->
<!--        #{item.serviceFee,jdbcType=DECIMAL},-->
<!--        #{item.price,jdbcType=DECIMAL},-->
<!--        #{item.orderStatus,jdbcType=TINYINT},-->
<!--        #{item.terminationBeforeStatus,jdbcType=TINYINT},-->
<!--        #{item.isPublishInvitationAd,jdbcType=TINYINT},-->
<!--        #{item.isSkipDeliverable,jdbcType=TINYINT},-->
<!--        #{item.salesSupportUser,jdbcType=VARCHAR},-->
<!--        #{item.salesSupportUserId,jdbcType=INTEGER},-->
<!--        #{item.onlineTime,jdbcType=TIMESTAMP},-->
<!--        #{item.upperType,jdbcType=TINYINT},-->
<!--        #{item.crmContractOrderId,jdbcType=INTEGER},-->
<!--        #{item.chaodianOrderId,jdbcType=VARCHAR},-->
<!--        #{item.acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--        #{item.statusMtime,jdbcType=TIMESTAMP},-->
<!--        #{item.isDeleted,jdbcType=TINYINT},-->
<!--        #{item.ctime,jdbcType=TIMESTAMP},-->
<!--        #{item.mtime,jdbcType=TIMESTAMP},-->
<!--        #{item.finalDraftId,jdbcType=INTEGER},-->
<!--        #{item.discountRate,jdbcType=DECIMAL},-->
<!--        #{item.operatorType,jdbcType=INTEGER},-->
<!--        #{item.operateStatus,jdbcType=INTEGER},-->
<!--        #{item.isCoreAccount,jdbcType=TINYINT},-->
<!--        #{item.serviceProviderId,jdbcType=INTEGER},-->
<!--        #{item.settleType,jdbcType=TINYINT},-->
<!--        #{item.serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--        #{item.paymentConfirm,jdbcType=TINYINT},-->
<!--        #{item.paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--        #{item.isKaAccount,jdbcType=TINYINT},-->
<!--        #{item.settleInfoId,jdbcType=INTEGER},-->
<!--        #{item.refundPrice,jdbcType=DECIMAL},-->
<!--        #{item.refundVoucherUrl,jdbcType=VARCHAR},-->
<!--        #{item.onlineStartTime,jdbcType=TIMESTAMP},-->
<!--        #{item.cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--        #{item.isChangePrice,jdbcType=TINYINT},-->
<!--        #{item.publishId,jdbcType=INTEGER},-->
<!--        #{item.specialSettleSign,jdbcType=TINYINT},-->
<!--        #{item.serviceProviderIncome,jdbcType=DECIMAL},-->
<!--        #{item.orderRights,jdbcType=INTEGER},-->
<!--        #{item.secAgentId,jdbcType=INTEGER},-->
<!--        #{item.expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--        #{item.draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--        #{item.orderType,jdbcType=TINYINT},-->
<!--        #{item.priceVersion,jdbcType=TINYINT},-->
<!--        #{item.terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--        #{item.draftRejectReasonId,jdbcType=INTEGER},-->
<!--        #{item.outTaskNo,jdbcType=VARCHAR},-->
<!--        #{item.outTaskCm,jdbcType=TINYINT},-->
<!--        #{item.remark,jdbcType=VARCHAR},-->
<!--        #{item.inviteAdVersion,jdbcType=TINYINT},-->
<!--      </trim>-->
<!--    </foreach>-->
<!--  </insert>-->
<!--  <insert id="insertUpdateBatch" parameterType="java.util.List">-->
<!--    insert into -->
<!--      cm_order-->
<!--      (order_no,task_id,mcn_id,upper_mid,agent_id,secondary_agent_mid,account_id,cooperation_type,paid_price,upper_price,revise_base,platform_price,actual_upper_price,actual_platform_price,actual_cost_price,service_fee_rate,service_fee,price,order_status,termination_before_status,is_publish_invitation_ad,is_skip_deliverable,sales_support_user,sales_support_user_id,online_time,upper_type,crm_contract_order_id,chaodian_order_id,accept_invitation_ad_status,status_mtime,is_deleted,ctime,mtime,final_draft_id,discount_rate,operator_type,operate_status,is_core_account,service_provider_id,settle_type,service_provider_fee_rate,payment_confirm,payment_confirm_time,is_ka_account,settle_info_id,refund_price,refund_voucher_url,online_start_time,cooperation_type_remark,is_change_price,publish_id,special_settle_sign,service_provider_income,order_rights,sec_agent_id,expected_online_time,draft_delayed_time,order_type,price_version,terminated_service_fee_refund,draft_reject_reason_id,out_task_no,out_task_cm,remark,invite_ad_version)-->
<!--    values-->
<!--    <foreach collection="list" item="item" separator=",">-->
<!--      <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--        #{item.orderNo,jdbcType=BIGINT},-->
<!--        #{item.taskId,jdbcType=INTEGER},-->
<!--        #{item.mcnId,jdbcType=BIGINT},-->
<!--        #{item.upperMid,jdbcType=BIGINT},-->
<!--        #{item.agentId,jdbcType=INTEGER},-->
<!--        #{item.secondaryAgentMid,jdbcType=BIGINT},-->
<!--        #{item.accountId,jdbcType=INTEGER},-->
<!--        #{item.cooperationType,jdbcType=TINYINT},-->
<!--        #{item.paidPrice,jdbcType=DECIMAL},-->
<!--        #{item.upperPrice,jdbcType=DECIMAL},-->
<!--        #{item.reviseBase,jdbcType=DECIMAL},-->
<!--        #{item.platformPrice,jdbcType=DECIMAL},-->
<!--        #{item.actualUpperPrice,jdbcType=DECIMAL},-->
<!--        #{item.actualPlatformPrice,jdbcType=DECIMAL},-->
<!--        #{item.actualCostPrice,jdbcType=DECIMAL},-->
<!--        #{item.serviceFeeRate,jdbcType=DECIMAL},-->
<!--        #{item.serviceFee,jdbcType=DECIMAL},-->
<!--        #{item.price,jdbcType=DECIMAL},-->
<!--        #{item.orderStatus,jdbcType=TINYINT},-->
<!--        #{item.terminationBeforeStatus,jdbcType=TINYINT},-->
<!--        #{item.isPublishInvitationAd,jdbcType=TINYINT},-->
<!--        #{item.isSkipDeliverable,jdbcType=TINYINT},-->
<!--        #{item.salesSupportUser,jdbcType=VARCHAR},-->
<!--        #{item.salesSupportUserId,jdbcType=INTEGER},-->
<!--        #{item.onlineTime,jdbcType=TIMESTAMP},-->
<!--        #{item.upperType,jdbcType=TINYINT},-->
<!--        #{item.crmContractOrderId,jdbcType=INTEGER},-->
<!--        #{item.chaodianOrderId,jdbcType=VARCHAR},-->
<!--        #{item.acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--        #{item.statusMtime,jdbcType=TIMESTAMP},-->
<!--        #{item.isDeleted,jdbcType=TINYINT},-->
<!--        #{item.ctime,jdbcType=TIMESTAMP},-->
<!--        #{item.mtime,jdbcType=TIMESTAMP},-->
<!--        #{item.finalDraftId,jdbcType=INTEGER},-->
<!--        #{item.discountRate,jdbcType=DECIMAL},-->
<!--        #{item.operatorType,jdbcType=INTEGER},-->
<!--        #{item.operateStatus,jdbcType=INTEGER},-->
<!--        #{item.isCoreAccount,jdbcType=TINYINT},-->
<!--        #{item.serviceProviderId,jdbcType=INTEGER},-->
<!--        #{item.settleType,jdbcType=TINYINT},-->
<!--        #{item.serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--        #{item.paymentConfirm,jdbcType=TINYINT},-->
<!--        #{item.paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--        #{item.isKaAccount,jdbcType=TINYINT},-->
<!--        #{item.settleInfoId,jdbcType=INTEGER},-->
<!--        #{item.refundPrice,jdbcType=DECIMAL},-->
<!--        #{item.refundVoucherUrl,jdbcType=VARCHAR},-->
<!--        #{item.onlineStartTime,jdbcType=TIMESTAMP},-->
<!--        #{item.cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--        #{item.isChangePrice,jdbcType=TINYINT},-->
<!--        #{item.publishId,jdbcType=INTEGER},-->
<!--        #{item.specialSettleSign,jdbcType=TINYINT},-->
<!--        #{item.serviceProviderIncome,jdbcType=DECIMAL},-->
<!--        #{item.orderRights,jdbcType=INTEGER},-->
<!--        #{item.secAgentId,jdbcType=INTEGER},-->
<!--        #{item.expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--        #{item.draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--        #{item.orderType,jdbcType=TINYINT},-->
<!--        #{item.priceVersion,jdbcType=TINYINT},-->
<!--        #{item.terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--        #{item.draftRejectReasonId,jdbcType=INTEGER},-->
<!--        #{item.outTaskNo,jdbcType=VARCHAR},-->
<!--        #{item.outTaskCm,jdbcType=TINYINT},-->
<!--        #{item.remark,jdbcType=VARCHAR},-->
<!--        #{item.inviteAdVersion,jdbcType=TINYINT},-->
<!--      </trim>-->
<!--    </foreach>-->
<!--    <trim prefix="on duplicate key update" suffixOverrides=",">-->
<!--      order_no = values(order_no),-->
<!--      task_id = values(task_id),-->
<!--      mcn_id = values(mcn_id),-->
<!--      upper_mid = values(upper_mid),-->
<!--      agent_id = values(agent_id),-->
<!--      secondary_agent_mid = values(secondary_agent_mid),-->
<!--      account_id = values(account_id),-->
<!--      cooperation_type = values(cooperation_type),-->
<!--      paid_price = values(paid_price),-->
<!--      upper_price = values(upper_price),-->
<!--      revise_base = values(revise_base),-->
<!--      platform_price = values(platform_price),-->
<!--      actual_upper_price = values(actual_upper_price),-->
<!--      actual_platform_price = values(actual_platform_price),-->
<!--      actual_cost_price = values(actual_cost_price),-->
<!--      service_fee_rate = values(service_fee_rate),-->
<!--      service_fee = values(service_fee),-->
<!--      price = values(price),-->
<!--      order_status = values(order_status),-->
<!--      termination_before_status = values(termination_before_status),-->
<!--      is_publish_invitation_ad = values(is_publish_invitation_ad),-->
<!--      is_skip_deliverable = values(is_skip_deliverable),-->
<!--      sales_support_user = values(sales_support_user),-->
<!--      sales_support_user_id = values(sales_support_user_id),-->
<!--      online_time = values(online_time),-->
<!--      upper_type = values(upper_type),-->
<!--      crm_contract_order_id = values(crm_contract_order_id),-->
<!--      chaodian_order_id = values(chaodian_order_id),-->
<!--      accept_invitation_ad_status = values(accept_invitation_ad_status),-->
<!--      status_mtime = values(status_mtime),-->
<!--      is_deleted = values(is_deleted),-->
<!--      ctime = values(ctime),-->
<!--      mtime = values(mtime),-->
<!--      final_draft_id = values(final_draft_id),-->
<!--      discount_rate = values(discount_rate),-->
<!--      operator_type = values(operator_type),-->
<!--      operate_status = values(operate_status),-->
<!--      is_core_account = values(is_core_account),-->
<!--      service_provider_id = values(service_provider_id),-->
<!--      settle_type = values(settle_type),-->
<!--      service_provider_fee_rate = values(service_provider_fee_rate),-->
<!--      payment_confirm = values(payment_confirm),-->
<!--      payment_confirm_time = values(payment_confirm_time),-->
<!--      is_ka_account = values(is_ka_account),-->
<!--      settle_info_id = values(settle_info_id),-->
<!--      refund_price = values(refund_price),-->
<!--      refund_voucher_url = values(refund_voucher_url),-->
<!--      online_start_time = values(online_start_time),-->
<!--      cooperation_type_remark = values(cooperation_type_remark),-->
<!--      is_change_price = values(is_change_price),-->
<!--      publish_id = values(publish_id),-->
<!--      special_settle_sign = values(special_settle_sign),-->
<!--      service_provider_income = values(service_provider_income),-->
<!--      order_rights = values(order_rights),-->
<!--      sec_agent_id = values(sec_agent_id),-->
<!--      expected_online_time = values(expected_online_time),-->
<!--      draft_delayed_time = values(draft_delayed_time),-->
<!--      order_type = values(order_type),-->
<!--      price_version = values(price_version),-->
<!--      terminated_service_fee_refund = values(terminated_service_fee_refund),-->
<!--      draft_reject_reason_id = values(draft_reject_reason_id),-->
<!--      out_task_no = values(out_task_no),-->
<!--      out_task_cm = values(out_task_cm),-->
<!--      remark = values(remark),-->
<!--      invite_ad_version = values(invite_ad_version),-->
<!--    </trim>-->
<!--  </insert>-->
<!--  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CmOrderPo">-->
<!--    insert into cm_order-->
<!--    <trim prefix="(" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        id,-->
<!--      </if>-->
<!--      <if test="orderNo != null">-->
<!--        order_no,-->
<!--      </if>-->
<!--      <if test="taskId != null">-->
<!--        task_id,-->
<!--      </if>-->
<!--      <if test="mcnId != null">-->
<!--        mcn_id,-->
<!--      </if>-->
<!--      <if test="upperMid != null">-->
<!--        upper_mid,-->
<!--      </if>-->
<!--      <if test="agentId != null">-->
<!--        agent_id,-->
<!--      </if>-->
<!--      <if test="secondaryAgentMid != null">-->
<!--        secondary_agent_mid,-->
<!--      </if>-->
<!--      <if test="accountId != null">-->
<!--        account_id,-->
<!--      </if>-->
<!--      <if test="cooperationType != null">-->
<!--        cooperation_type,-->
<!--      </if>-->
<!--      <if test="paidPrice != null">-->
<!--        paid_price,-->
<!--      </if>-->
<!--      <if test="upperPrice != null">-->
<!--        upper_price,-->
<!--      </if>-->
<!--      <if test="reviseBase != null">-->
<!--        revise_base,-->
<!--      </if>-->
<!--      <if test="platformPrice != null">-->
<!--        platform_price,-->
<!--      </if>-->
<!--      <if test="actualUpperPrice != null">-->
<!--        actual_upper_price,-->
<!--      </if>-->
<!--      <if test="actualPlatformPrice != null">-->
<!--        actual_platform_price,-->
<!--      </if>-->
<!--      <if test="actualCostPrice != null">-->
<!--        actual_cost_price,-->
<!--      </if>-->
<!--      <if test="serviceFeeRate != null">-->
<!--        service_fee_rate,-->
<!--      </if>-->
<!--      <if test="serviceFee != null">-->
<!--        service_fee,-->
<!--      </if>-->
<!--      <if test="price != null">-->
<!--        price,-->
<!--      </if>-->
<!--      <if test="orderStatus != null">-->
<!--        order_status,-->
<!--      </if>-->
<!--      <if test="terminationBeforeStatus != null">-->
<!--        termination_before_status,-->
<!--      </if>-->
<!--      <if test="isPublishInvitationAd != null">-->
<!--        is_publish_invitation_ad,-->
<!--      </if>-->
<!--      <if test="isSkipDeliverable != null">-->
<!--        is_skip_deliverable,-->
<!--      </if>-->
<!--      <if test="salesSupportUser != null">-->
<!--        sales_support_user,-->
<!--      </if>-->
<!--      <if test="salesSupportUserId != null">-->
<!--        sales_support_user_id,-->
<!--      </if>-->
<!--      <if test="onlineTime != null">-->
<!--        online_time,-->
<!--      </if>-->
<!--      <if test="upperType != null">-->
<!--        upper_type,-->
<!--      </if>-->
<!--      <if test="crmContractOrderId != null">-->
<!--        crm_contract_order_id,-->
<!--      </if>-->
<!--      <if test="chaodianOrderId != null">-->
<!--        chaodian_order_id,-->
<!--      </if>-->
<!--      <if test="acceptInvitationAdStatus != null">-->
<!--        accept_invitation_ad_status,-->
<!--      </if>-->
<!--      <if test="statusMtime != null">-->
<!--        status_mtime,-->
<!--      </if>-->
<!--      <if test="isDeleted != null">-->
<!--        is_deleted,-->
<!--      </if>-->
<!--      <if test="ctime != null">-->
<!--        ctime,-->
<!--      </if>-->
<!--      <if test="mtime != null">-->
<!--        mtime,-->
<!--      </if>-->
<!--      <if test="finalDraftId != null">-->
<!--        final_draft_id,-->
<!--      </if>-->
<!--      <if test="discountRate != null">-->
<!--        discount_rate,-->
<!--      </if>-->
<!--      <if test="operatorType != null">-->
<!--        operator_type,-->
<!--      </if>-->
<!--      <if test="operateStatus != null">-->
<!--        operate_status,-->
<!--      </if>-->
<!--      <if test="isCoreAccount != null">-->
<!--        is_core_account,-->
<!--      </if>-->
<!--      <if test="serviceProviderId != null">-->
<!--        service_provider_id,-->
<!--      </if>-->
<!--      <if test="settleType != null">-->
<!--        settle_type,-->
<!--      </if>-->
<!--      <if test="serviceProviderFeeRate != null">-->
<!--        service_provider_fee_rate,-->
<!--      </if>-->
<!--      <if test="paymentConfirm != null">-->
<!--        payment_confirm,-->
<!--      </if>-->
<!--      <if test="paymentConfirmTime != null">-->
<!--        payment_confirm_time,-->
<!--      </if>-->
<!--      <if test="isKaAccount != null">-->
<!--        is_ka_account,-->
<!--      </if>-->
<!--      <if test="settleInfoId != null">-->
<!--        settle_info_id,-->
<!--      </if>-->
<!--      <if test="refundPrice != null">-->
<!--        refund_price,-->
<!--      </if>-->
<!--      <if test="refundVoucherUrl != null">-->
<!--        refund_voucher_url,-->
<!--      </if>-->
<!--      <if test="onlineStartTime != null">-->
<!--        online_start_time,-->
<!--      </if>-->
<!--      <if test="cooperationTypeRemark != null">-->
<!--        cooperation_type_remark,-->
<!--      </if>-->
<!--      <if test="isChangePrice != null">-->
<!--        is_change_price,-->
<!--      </if>-->
<!--      <if test="publishId != null">-->
<!--        publish_id,-->
<!--      </if>-->
<!--      <if test="specialSettleSign != null">-->
<!--        special_settle_sign,-->
<!--      </if>-->
<!--      <if test="serviceProviderIncome != null">-->
<!--        service_provider_income,-->
<!--      </if>-->
<!--      <if test="orderRights != null">-->
<!--        order_rights,-->
<!--      </if>-->
<!--      <if test="secAgentId != null">-->
<!--        sec_agent_id,-->
<!--      </if>-->
<!--      <if test="expectedOnlineTime != null">-->
<!--        expected_online_time,-->
<!--      </if>-->
<!--      <if test="draftDelayedTime != null">-->
<!--        draft_delayed_time,-->
<!--      </if>-->
<!--      <if test="orderType != null">-->
<!--        order_type,-->
<!--      </if>-->
<!--      <if test="priceVersion != null">-->
<!--        price_version,-->
<!--      </if>-->
<!--      <if test="terminatedServiceFeeRefund != null">-->
<!--        terminated_service_fee_refund,-->
<!--      </if>-->
<!--      <if test="draftRejectReasonId != null">-->
<!--        draft_reject_reason_id,-->
<!--      </if>-->
<!--      <if test="outTaskNo != null">-->
<!--        out_task_no,-->
<!--      </if>-->
<!--      <if test="outTaskCm != null">-->
<!--        out_task_cm,-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark,-->
<!--      </if>-->
<!--      <if test="inviteAdVersion != null">-->
<!--        invite_ad_version,-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="values (" suffix=")" suffixOverrides=",">-->
<!--      <if test="id != null">-->
<!--        #{id,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="orderNo != null">-->
<!--        #{orderNo,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="taskId != null">-->
<!--        #{taskId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="mcnId != null">-->
<!--        #{mcnId,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="upperMid != null">-->
<!--        #{upperMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="agentId != null">-->
<!--        #{agentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="secondaryAgentMid != null">-->
<!--        #{secondaryAgentMid,jdbcType=BIGINT},-->
<!--      </if>-->
<!--      <if test="accountId != null">-->
<!--        #{accountId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="cooperationType != null">-->
<!--        #{cooperationType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="paidPrice != null">-->
<!--        #{paidPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="upperPrice != null">-->
<!--        #{upperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="reviseBase != null">-->
<!--        #{reviseBase,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="platformPrice != null">-->
<!--        #{platformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualUpperPrice != null">-->
<!--        #{actualUpperPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualPlatformPrice != null">-->
<!--        #{actualPlatformPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="actualCostPrice != null">-->
<!--        #{actualCostPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="serviceFeeRate != null">-->
<!--        #{serviceFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="serviceFee != null">-->
<!--        #{serviceFee,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="price != null">-->
<!--        #{price,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="orderStatus != null">-->
<!--        #{orderStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="terminationBeforeStatus != null">-->
<!--        #{terminationBeforeStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="isPublishInvitationAd != null">-->
<!--        #{isPublishInvitationAd,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="isSkipDeliverable != null">-->
<!--        #{isSkipDeliverable,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="salesSupportUser != null">-->
<!--        #{salesSupportUser,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="salesSupportUserId != null">-->
<!--        #{salesSupportUserId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="onlineTime != null">-->
<!--        #{onlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="upperType != null">-->
<!--        #{upperType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="crmContractOrderId != null">-->
<!--        #{crmContractOrderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="chaodianOrderId != null">-->
<!--        #{chaodianOrderId,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="acceptInvitationAdStatus != null">-->
<!--        #{acceptInvitationAdStatus,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="statusMtime != null">-->
<!--        #{statusMtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="isDeleted != null">-->
<!--        #{isDeleted,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="ctime != null">-->
<!--        #{ctime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="mtime != null">-->
<!--        #{mtime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="finalDraftId != null">-->
<!--        #{finalDraftId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="discountRate != null">-->
<!--        #{discountRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="operatorType != null">-->
<!--        #{operatorType,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="operateStatus != null">-->
<!--        #{operateStatus,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="isCoreAccount != null">-->
<!--        #{isCoreAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderId != null">-->
<!--        #{serviceProviderId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="settleType != null">-->
<!--        #{settleType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderFeeRate != null">-->
<!--        #{serviceProviderFeeRate,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="paymentConfirm != null">-->
<!--        #{paymentConfirm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="paymentConfirmTime != null">-->
<!--        #{paymentConfirmTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="isKaAccount != null">-->
<!--        #{isKaAccount,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="settleInfoId != null">-->
<!--        #{settleInfoId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="refundPrice != null">-->
<!--        #{refundPrice,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="refundVoucherUrl != null">-->
<!--        #{refundVoucherUrl,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="onlineStartTime != null">-->
<!--        #{onlineStartTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="cooperationTypeRemark != null">-->
<!--        #{cooperationTypeRemark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="isChangePrice != null">-->
<!--        #{isChangePrice,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="publishId != null">-->
<!--        #{publishId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="specialSettleSign != null">-->
<!--        #{specialSettleSign,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="serviceProviderIncome != null">-->
<!--        #{serviceProviderIncome,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="orderRights != null">-->
<!--        #{orderRights,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="secAgentId != null">-->
<!--        #{secAgentId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="expectedOnlineTime != null">-->
<!--        #{expectedOnlineTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="draftDelayedTime != null">-->
<!--        #{draftDelayedTime,jdbcType=TIMESTAMP},-->
<!--      </if>-->
<!--      <if test="orderType != null">-->
<!--        #{orderType,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="priceVersion != null">-->
<!--        #{priceVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="terminatedServiceFeeRefund != null">-->
<!--        #{terminatedServiceFeeRefund,jdbcType=DECIMAL},-->
<!--      </if>-->
<!--      <if test="draftRejectReasonId != null">-->
<!--        #{draftRejectReasonId,jdbcType=INTEGER},-->
<!--      </if>-->
<!--      <if test="outTaskNo != null">-->
<!--        #{outTaskNo,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="outTaskCm != null">-->
<!--        #{outTaskCm,jdbcType=TINYINT},-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        #{remark,jdbcType=VARCHAR},-->
<!--      </if>-->
<!--      <if test="inviteAdVersion != null">-->
<!--        #{inviteAdVersion,jdbcType=TINYINT},-->
<!--      </if>-->
<!--    </trim>-->
<!--    <trim prefix="on duplicate key update" suffixOverrides=",">-->
<!--      <if test="orderNo != null">-->
<!--        order_no = values(order_no),-->
<!--      </if>-->
<!--      <if test="taskId != null">-->
<!--        task_id = values(task_id),-->
<!--      </if>-->
<!--      <if test="mcnId != null">-->
<!--        mcn_id = values(mcn_id),-->
<!--      </if>-->
<!--      <if test="upperMid != null">-->
<!--        upper_mid = values(upper_mid),-->
<!--      </if>-->
<!--      <if test="agentId != null">-->
<!--        agent_id = values(agent_id),-->
<!--      </if>-->
<!--      <if test="secondaryAgentMid != null">-->
<!--        secondary_agent_mid = values(secondary_agent_mid),-->
<!--      </if>-->
<!--      <if test="accountId != null">-->
<!--        account_id = values(account_id),-->
<!--      </if>-->
<!--      <if test="cooperationType != null">-->
<!--        cooperation_type = values(cooperation_type),-->
<!--      </if>-->
<!--      <if test="paidPrice != null">-->
<!--        paid_price = values(paid_price),-->
<!--      </if>-->
<!--      <if test="upperPrice != null">-->
<!--        upper_price = values(upper_price),-->
<!--      </if>-->
<!--      <if test="reviseBase != null">-->
<!--        revise_base = values(revise_base),-->
<!--      </if>-->
<!--      <if test="platformPrice != null">-->
<!--        platform_price = values(platform_price),-->
<!--      </if>-->
<!--      <if test="actualUpperPrice != null">-->
<!--        actual_upper_price = values(actual_upper_price),-->
<!--      </if>-->
<!--      <if test="actualPlatformPrice != null">-->
<!--        actual_platform_price = values(actual_platform_price),-->
<!--      </if>-->
<!--      <if test="actualCostPrice != null">-->
<!--        actual_cost_price = values(actual_cost_price),-->
<!--      </if>-->
<!--      <if test="serviceFeeRate != null">-->
<!--        service_fee_rate = values(service_fee_rate),-->
<!--      </if>-->
<!--      <if test="serviceFee != null">-->
<!--        service_fee = values(service_fee),-->
<!--      </if>-->
<!--      <if test="price != null">-->
<!--        price = values(price),-->
<!--      </if>-->
<!--      <if test="orderStatus != null">-->
<!--        order_status = values(order_status),-->
<!--      </if>-->
<!--      <if test="terminationBeforeStatus != null">-->
<!--        termination_before_status = values(termination_before_status),-->
<!--      </if>-->
<!--      <if test="isPublishInvitationAd != null">-->
<!--        is_publish_invitation_ad = values(is_publish_invitation_ad),-->
<!--      </if>-->
<!--      <if test="isSkipDeliverable != null">-->
<!--        is_skip_deliverable = values(is_skip_deliverable),-->
<!--      </if>-->
<!--      <if test="salesSupportUser != null">-->
<!--        sales_support_user = values(sales_support_user),-->
<!--      </if>-->
<!--      <if test="salesSupportUserId != null">-->
<!--        sales_support_user_id = values(sales_support_user_id),-->
<!--      </if>-->
<!--      <if test="onlineTime != null">-->
<!--        online_time = values(online_time),-->
<!--      </if>-->
<!--      <if test="upperType != null">-->
<!--        upper_type = values(upper_type),-->
<!--      </if>-->
<!--      <if test="crmContractOrderId != null">-->
<!--        crm_contract_order_id = values(crm_contract_order_id),-->
<!--      </if>-->
<!--      <if test="chaodianOrderId != null">-->
<!--        chaodian_order_id = values(chaodian_order_id),-->
<!--      </if>-->
<!--      <if test="acceptInvitationAdStatus != null">-->
<!--        accept_invitation_ad_status = values(accept_invitation_ad_status),-->
<!--      </if>-->
<!--      <if test="statusMtime != null">-->
<!--        status_mtime = values(status_mtime),-->
<!--      </if>-->
<!--      <if test="isDeleted != null">-->
<!--        is_deleted = values(is_deleted),-->
<!--      </if>-->
<!--      <if test="ctime != null">-->
<!--        ctime = values(ctime),-->
<!--      </if>-->
<!--      <if test="mtime != null">-->
<!--        mtime = values(mtime),-->
<!--      </if>-->
<!--      <if test="finalDraftId != null">-->
<!--        final_draft_id = values(final_draft_id),-->
<!--      </if>-->
<!--      <if test="discountRate != null">-->
<!--        discount_rate = values(discount_rate),-->
<!--      </if>-->
<!--      <if test="operatorType != null">-->
<!--        operator_type = values(operator_type),-->
<!--      </if>-->
<!--      <if test="operateStatus != null">-->
<!--        operate_status = values(operate_status),-->
<!--      </if>-->
<!--      <if test="isCoreAccount != null">-->
<!--        is_core_account = values(is_core_account),-->
<!--      </if>-->
<!--      <if test="serviceProviderId != null">-->
<!--        service_provider_id = values(service_provider_id),-->
<!--      </if>-->
<!--      <if test="settleType != null">-->
<!--        settle_type = values(settle_type),-->
<!--      </if>-->
<!--      <if test="serviceProviderFeeRate != null">-->
<!--        service_provider_fee_rate = values(service_provider_fee_rate),-->
<!--      </if>-->
<!--      <if test="paymentConfirm != null">-->
<!--        payment_confirm = values(payment_confirm),-->
<!--      </if>-->
<!--      <if test="paymentConfirmTime != null">-->
<!--        payment_confirm_time = values(payment_confirm_time),-->
<!--      </if>-->
<!--      <if test="isKaAccount != null">-->
<!--        is_ka_account = values(is_ka_account),-->
<!--      </if>-->
<!--      <if test="settleInfoId != null">-->
<!--        settle_info_id = values(settle_info_id),-->
<!--      </if>-->
<!--      <if test="refundPrice != null">-->
<!--        refund_price = values(refund_price),-->
<!--      </if>-->
<!--      <if test="refundVoucherUrl != null">-->
<!--        refund_voucher_url = values(refund_voucher_url),-->
<!--      </if>-->
<!--      <if test="onlineStartTime != null">-->
<!--        online_start_time = values(online_start_time),-->
<!--      </if>-->
<!--      <if test="cooperationTypeRemark != null">-->
<!--        cooperation_type_remark = values(cooperation_type_remark),-->
<!--      </if>-->
<!--      <if test="isChangePrice != null">-->
<!--        is_change_price = values(is_change_price),-->
<!--      </if>-->
<!--      <if test="publishId != null">-->
<!--        publish_id = values(publish_id),-->
<!--      </if>-->
<!--      <if test="specialSettleSign != null">-->
<!--        special_settle_sign = values(special_settle_sign),-->
<!--      </if>-->
<!--      <if test="serviceProviderIncome != null">-->
<!--        service_provider_income = values(service_provider_income),-->
<!--      </if>-->
<!--      <if test="orderRights != null">-->
<!--        order_rights = values(order_rights),-->
<!--      </if>-->
<!--      <if test="secAgentId != null">-->
<!--        sec_agent_id = values(sec_agent_id),-->
<!--      </if>-->
<!--      <if test="expectedOnlineTime != null">-->
<!--        expected_online_time = values(expected_online_time),-->
<!--      </if>-->
<!--      <if test="draftDelayedTime != null">-->
<!--        draft_delayed_time = values(draft_delayed_time),-->
<!--      </if>-->
<!--      <if test="orderType != null">-->
<!--        order_type = values(order_type),-->
<!--      </if>-->
<!--      <if test="priceVersion != null">-->
<!--        price_version = values(price_version),-->
<!--      </if>-->
<!--      <if test="terminatedServiceFeeRefund != null">-->
<!--        terminated_service_fee_refund = values(terminated_service_fee_refund),-->
<!--      </if>-->
<!--      <if test="draftRejectReasonId != null">-->
<!--        draft_reject_reason_id = values(draft_reject_reason_id),-->
<!--      </if>-->
<!--      <if test="outTaskNo != null">-->
<!--        out_task_no = values(out_task_no),-->
<!--      </if>-->
<!--      <if test="outTaskCm != null">-->
<!--        out_task_cm = values(out_task_cm),-->
<!--      </if>-->
<!--      <if test="remark != null">-->
<!--        remark = values(remark),-->
<!--      </if>-->
<!--      <if test="inviteAdVersion != null">-->
<!--        invite_ad_version = values(invite_ad_version),-->
<!--      </if>-->
<!--    </trim>-->
<!--  </insert>-->
</mapper>