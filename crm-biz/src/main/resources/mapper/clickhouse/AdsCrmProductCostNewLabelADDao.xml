<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.crm.platform.biz.clickhouse.dao.AdsCrmProductCostNewLabelADDao">
    <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmTargetCostNewLabelADPO">
        <result column="product_id" jdbcType="VARCHAR" property="mappingId"/>
        <result column="product_name" jdbcType="VARCHAR" property="mappingName"/>
        <result column="product_first_category_type" jdbcType="VARCHAR" property="productFirstCategoryType"/>
        <result column="agent_customer_id" jdbcType="VARCHAR" property="agentCustomerId"/>
        <result column="agent_customer_name" jdbcType="VARCHAR" property="agentCustomerName"/>
        <result column="group_id_list" jdbcType="VARCHAR" property="groupIdList"/>
        <result column="group_name_list" jdbcType="VARCHAR" property="groupNameList"/>
        <result column="agent_customer_id_list" jdbcType="VARCHAR" property="agentCustomerIdList"/>
        <result column="agent_customer_name_list" jdbcType="VARCHAR" property="agentCustomerNameList"/>
        <result column="agent_group_id_list" jdbcType="VARCHAR" property="agentGroupIdList"/>
        <result column="agent_group_name_list" jdbcType="VARCHAR" property="agentGroupNameList"/>
        <result column="last360_cost_platform" jdbcType="DOUBLE" property="last360CostPlatform"/>
        <result column="lastday_cost_platform" jdbcType="DOUBLE" property="lastdayCostPlatform"/>
        <result column="last360_cost_category" jdbcType="DOUBLE" property="last360CostCategory"/>
        <result column="lastday_cost_category" jdbcType="DOUBLE" property="lastdayCostCategory"/>
        <result column="last360_cost_agent" jdbcType="DOUBLE" property="last360CostAgent"/>
        <result column="lastday_cost_agent" jdbcType="DOUBLE" property="lastdayCostAgent"/>
        <result column="last360_cost_agent_category" jdbcType="DOUBLE" property="last360CostAgentCategory"/>
        <result column="lastday_cost_agent_category" jdbcType="DOUBLE" property="lastdayCostAgentCategory"/>
        <result column="is_platform_new_cust" jdbcType="BIGINT" property="isPlatformNewCust"/>
        <result column="is_platform_product_new_cust" jdbcType="BIGINT" property="isPlatformProductNewCust"/>
        <result column="is_platform_xiaoguo_new_cust" jdbcType="BIGINT" property="isPlatformXiaoguoNewCust"/>
        <result column="is_platform_huahuo_new_cust" jdbcType="BIGINT" property="isPlatformHuahuoNewCust"/>
        <result column="is_agent_new_cust" jdbcType="BIGINT" property="isAgentNewCust"/>
        <result column="is_agent_product_new_cust" jdbcType="BIGINT" property="isAgentProductNewCust"/>
        <result column="is_agent_xiaoguo_new_cust" jdbcType="BIGINT" property="isAgentXiaoguoNewCust"/>
        <result column="is_agent_huahuo_new_cust" jdbcType="BIGINT" property="isAgentHuahuoNewCust"/>
        <result column="last_cost_platform" jdbcType="DOUBLE" property="lastCostPlatform"/>
        <result column="current_cost_platform" jdbcType="DOUBLE" property="currentCostPlatform"/>
        <result column="last_cost_category" jdbcType="DOUBLE" property="lastCostCategory"/>
        <result column="current_cost_category" jdbcType="DOUBLE" property="currentCostCategory"/>
        <result column="last_cost_agent" jdbcType="DOUBLE" property="lastCostAgent"/>
        <result column="current_cost_agent" jdbcType="DOUBLE" property="currentCostAgent"/>
        <result column="last_cost_agent_category" jdbcType="DOUBLE" property="lastCostAgentCategory"/>
        <result column="current_cost_agent_category" jdbcType="DOUBLE" property="currentCostAgentCategory"/>
        <result column="is_platform_new_cust_q" jdbcType="BIGINT" property="isPlatformNewCustQ"/>
        <result column="is_platform_product_new_cust_q" jdbcType="BIGINT" property="isPlatformProductNewCustQ"/>
        <result column="is_platform_xiaoguo_new_cust_q" jdbcType="BIGINT" property="isPlatformXiaoguoNewCustQ"/>
        <result column="is_platform_huahuo_new_cust_q" jdbcType="BIGINT" property="isPlatformHuahuoNewCustQ"/>
        <result column="is_agent_new_cust_q" jdbcType="BIGINT" property="isAgentNewCustQ"/>
        <result column="is_agent_product_new_cust_q" jdbcType="BIGINT" property="isAgentProductNewCustQ"/>
        <result column="is_agent_xiaoguo_new_cust_q" jdbcType="BIGINT" property="isAgentXiaoguoNewCustQ"/>
        <result column="is_agent_huahuo_new_cust_q" jdbcType="BIGINT" property="isAgentHuahuoNewCustQ"/>
        <result column="log_date" jdbcType="VARCHAR" property="logDate"/>
    </resultMap>
    <sql id="Base_Column_List">
        product_id,
        product_name,
        product_first_category_type,
        agent_customer_id,
        agent_customer_name,
        group_id_list,
        group_name_list,
        agent_customer_id_list,
        agent_customer_name_list,
        agent_group_id_list,
        agent_group_name_list,
        last360_cost_platform,
        lastday_cost_platform,
        last360_cost_category,
        lastday_cost_category,
        last360_cost_agent,
        lastday_cost_agent,
        last360_cost_agent_category,
        lastday_cost_agent_category,
        is_platform_new_cust,
        is_platform_product_new_cust,
        is_platform_xiaoguo_new_cust,
        is_platform_huahuo_new_cust,
        is_agent_new_cust,
        is_agent_product_new_cust,
        is_agent_xiaoguo_new_cust,
        is_agent_huahuo_new_cust,
        last_cost_platform,
        current_cost_platform,
        last_cost_category,
        current_cost_category,
        last_cost_agent,
        current_cost_agent,
        last_cost_agent_category,
        current_cost_agent_category,
        is_platform_new_cust_q,
        is_platform_product_new_cust_q,
        is_platform_xiaoguo_new_cust_q,
        is_platform_huahuo_new_cust_q,
        is_agent_new_cust_q,
        is_agent_product_new_cust_q,
        is_agent_xiaoguo_new_cust_q,
        is_agent_huahuo_new_cust_q,
        log_date
    </sql>


    <select id="selectNewProductListForYearAndQuarterly" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from ads_crm_product_cost_new_label_a_d
        where 1=1
        <if test="param.yearOrQuarter != null and param.yearOrQuarter == 1">
            AND ( is_platform_new_cust = 1 OR is_platform_product_new_cust = 1 OR is_platform_xiaoguo_new_cust = 1 OR
            is_platform_huahuo_new_cust = 1 )
        </if>
        <if test="param.yearOrQuarter != null and param.yearOrQuarter == 2">
            AND ( is_platform_new_cust_q = 1 OR is_platform_product_new_cust_q = 1 OR is_platform_xiaoguo_new_cust_q = 1
            OR is_platform_huahuo_new_cust_q = 1 )
        </if>
        <if test="param.businessType == 0">
            AND ( is_platform_new_cust = 1 OR is_platform_new_cust_q = 1 OR is_platform_product_new_cust = 1 OR
            is_platform_xiaoguo_new_cust = 1 OR is_platform_huahuo_new_cust = 1 OR is_platform_product_new_cust_q = 1 OR
            is_platform_xiaoguo_new_cust_q = 1 OR is_platform_huahuo_new_cust_q = 1)
        </if>
        <if test="param.businessType == 1">
            AND ( is_platform_product_new_cust = 1 OR is_platform_product_new_cust_q = 1)
        </if>
        <if test="param.businessType == 2">
            AND ( is_platform_xiaoguo_new_cust = 1 OR is_platform_xiaoguo_new_cust_q = 1)
        </if>
        <if test="param.businessType == 3">
            AND ( is_platform_huahuo_new_cust = 1 OR is_platform_huahuo_new_cust_q = 1)
        </if>
        <if test="param.mappingIdList != null and param.mappingIdList.size() > 0">
            AND product_id IN
            <foreach item="item" index="index" collection="param.mappingIdList" open="(" close=")"
                     separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.mappingIdNotList != null and param.mappingIdNotList.size() > 0">
            AND product_id NOT IN
            <foreach item="item" index="index" collection="param.mappingIdNotList" open="(" close=")"
                     separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.logDateBegin != null">
            AND log_date  <![CDATA[ >= ]]> #{param.logDateBegin}
        </if>
        <if test="param.logDateEnd != null">
            AND log_date  <![CDATA[ <= ]]> #{param.logDateEnd}
        </if>
    </select>

    <select id="countNewProduct" resultType="java.lang.Integer">
        select count(distinct product_id)
        from ads_crm_product_cost_new_label_a_d
        where 1 = 1
        <if test="param.yearOrQuarter != null and param.yearOrQuarter == 1">
            AND ( is_platform_new_cust = 1 OR is_platform_product_new_cust = 1 OR is_platform_xiaoguo_new_cust = 1 OR
            is_platform_huahuo_new_cust = 1 )
        </if>
        <if test="param.yearOrQuarter != null and param.yearOrQuarter == 2">
            AND ( is_platform_new_cust_q = 1 OR is_platform_product_new_cust_q = 1 OR is_platform_xiaoguo_new_cust_q = 1
            OR is_platform_huahuo_new_cust_q = 1 )
        </if>
        <if test="param.businessType == 0">
            AND ( is_platform_new_cust = 1 OR is_platform_new_cust_q = 1 OR is_platform_product_new_cust = 1 OR
            is_platform_xiaoguo_new_cust = 1 OR is_platform_huahuo_new_cust = 1 OR is_platform_product_new_cust_q = 1 OR
            is_platform_xiaoguo_new_cust_q = 1 OR is_platform_huahuo_new_cust_q = 1)
        </if>
        <if test="param.businessType == 1">
            AND ( is_platform_product_new_cust = 1 OR is_platform_product_new_cust_q = 1)
        </if>
        <if test="param.businessType == 2">
            AND ( is_platform_xiaoguo_new_cust = 1 OR is_platform_xiaoguo_new_cust_q = 1)
        </if>
        <if test="param.businessType == 3">
            AND ( is_platform_huahuo_new_cust = 1 OR is_platform_huahuo_new_cust_q = 1)
        </if>
        <if test="param.mappingIdList != null and param.mappingIdList.size() > 0">
            AND product_id IN
            <foreach item="item" index="index" collection="param.mappingIdList" open="(" close=")"
                     separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.mappingIdNotList != null and param.mappingIdNotList.size() > 0">
            AND product_id NOT IN
            <foreach item="item" index="index" collection="param.mappingIdNotList" open="(" close=")"
                     separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.logDateBegin != null">
            AND log_date  <![CDATA[ >= ]]> #{param.logDateBegin}
        </if>
        <if test="param.logDateEnd != null">
            AND log_date  <![CDATA[ <= ]]> #{param.logDateEnd}
        </if>
    </select>

    <select id="pageQueryNewProduct" resultMap="BaseResultMap">
        select distinct product_id, product_name
        from ads_crm_product_cost_new_label_a_d
        where 1=1
        <if test="param.yearOrQuarter != null and param.yearOrQuarter == 1">
            AND ( is_platform_new_cust = 1 OR is_platform_product_new_cust = 1 OR is_platform_xiaoguo_new_cust = 1 OR
            is_platform_huahuo_new_cust = 1 )
        </if>
        <if test="param.yearOrQuarter != null and param.yearOrQuarter == 2">
            AND ( is_platform_new_cust_q = 1 OR is_platform_product_new_cust_q = 1 OR is_platform_xiaoguo_new_cust_q = 1
            OR is_platform_huahuo_new_cust_q = 1 )
        </if>
        <if test="param.businessType == 0">
            AND ( is_platform_new_cust = 1 OR is_platform_new_cust_q = 1 OR is_platform_product_new_cust = 1 OR
            is_platform_xiaoguo_new_cust = 1 OR is_platform_huahuo_new_cust = 1 OR is_platform_product_new_cust_q = 1 OR
            is_platform_xiaoguo_new_cust_q = 1 OR is_platform_huahuo_new_cust_q = 1)
        </if>
        <if test="param.businessType == 1">
            AND ( is_platform_product_new_cust = 1 OR is_platform_product_new_cust_q = 1)
        </if>
        <if test="param.businessType == 2">
            AND ( is_platform_xiaoguo_new_cust = 1 OR is_platform_xiaoguo_new_cust_q = 1)
        </if>
        <if test="param.businessType == 3">
            AND ( is_platform_huahuo_new_cust = 1 OR is_platform_huahuo_new_cust_q = 1)
        </if>
        <if test="param.mappingIdList != null and param.mappingIdList.size() > 0">
            AND product_id IN
            <foreach item="item" index="index" collection="param.mappingIdList" open="(" close=")"
                     separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.mappingIdNotList != null and param.mappingIdNotList.size() > 0">
            AND product_id NOT IN
            <foreach item="item" index="index" collection="param.mappingIdNotList" open="(" close=")"
                     separator=",">
                #{item, jdbcType=VARCHAR}
            </foreach>
        </if>
        <if test="param.logDateBegin != null">
            AND log_date  <![CDATA[ >= ]]> #{param.logDateBegin}
        </if>
        <if test="param.logDateEnd != null">
            AND log_date  <![CDATA[ <= ]]> #{param.logDateEnd}
        </if>
        <if test="param.limit != null">
            <if test="param.offset != null">
                limit ${param.offset}, ${param.limit}
            </if>
            <if test="param.offset == null">
                limit ${param.limit}
            </if>
        </if>
    </select>
</mapper>