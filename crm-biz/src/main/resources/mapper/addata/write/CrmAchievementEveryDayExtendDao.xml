<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.addata.dao.write.CrmAchievementEveryDayExtendDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.addata.CrmAchievementEveryDayExtendPo">
    <result column="group_id" jdbcType="INTEGER" property="groupId"/>
    <result column="group_name" jdbcType="VARCHAR" property="groupName"/>
    <result column="completed_task_amount_all" jdbcType="BIGINT" property="completedTaskAmountALL"/>
    <result column="brand_completed_task_amount_all" jdbcType="BIGINT" property="brandCompletedTaskAmountALL"/>
    <result column="effect_completed_task_amount_all" jdbcType="BIGINT" property="effectCompletedTaskAmountALL"/>
    <result column="pickup_completed_task_amount_all" jdbcType="BIGINT" property="pickupCompletedTaskAmountALL"/>
    <result column="direct_sale_name_str" jdbcType="VARCHAR" property="directSaleNameStr"/>
    <result column="channel_sale_name_str" jdbcType="VARCHAR" property="channelSaleNameStr"/>
    <result column="direct_sale_group_name_str" jdbcType="VARCHAR" property="directSaleGroupNameStr"/>
    <result column="channel_sale_group_name_str" jdbcType="VARCHAR" property="channelSaleGroupNameStr"/>
    <result column="direct_sea_group_name_str" jdbcType="VARCHAR" property="directSeaGroupNameStr"/>
  </resultMap>
  <sql id="Base_Column_List">
    id, biz_name, biz_id, biz_type, direct_or_channel, agg_time, group_id, group_name,
    completed_task_amount, brand_completed_task_amount, effect_completed_task_amount,
    pickup_completed_task_amount, all_direct_sale_name_str, all_channel_sale_name_str,
    all_direct_sale_group_name_str, all_channel_sale_group_name_str, brand_direct_sale_name_str,
    brand_channel_sale_name_str, brand_direct_sale_group_name_str, brand_channel_sale_group_name_str,
    effect_direct_sale_name_str, effect_channel_sale_name_str, effect_direct_sale_group_name_str,
    effect_channel_sale_group_name_str, pickup_direct_sale_name_str, pickup_channel_sale_name_str,
    pickup_direct_sale_group_name_str, pickup_channel_sale_group_name_str, data_env,
    is_deleted, ctime, mtime
  </sql>

  <sql id="Base_All_Column_List">
    group_id,group_name,
    SUM(completed_task_amount) AS completed_task_amount_all,
    SUM(brand_completed_task_amount) AS brand_completed_task_amount_all,
    SUM(effect_completed_task_amount) AS effect_completed_task_amount_all,
    SUM(pickup_completed_task_amount) AS pickup_completed_task_amount_all,
    all_direct_sale_name_str AS direct_sale_name_str,
    all_channel_sale_name_str AS channel_sale_name_str,
    all_direct_sale_group_name_str AS direct_sale_group_name_str,
    all_channel_sale_group_name_str AS channel_sale_group_name_str,
    all_direct_sea_group_name_str AS direct_sea_group_name_str
  </sql>
  <sql id="Base_Brand_Column_List">
    group_id,group_name,
    SUM(brand_completed_task_amount) AS brand_completed_task_amount_all,
    brand_direct_sale_name_str AS direct_sale_name_str,
    brand_channel_sale_name_str AS channel_sale_name_str,
    brand_direct_sale_group_name_str AS direct_sale_group_name_str,
    brand_channel_sale_group_name_str AS channel_sale_group_name_str,
    brand_direct_sea_group_name_str AS direct_sea_group_name_str
  </sql>
  <sql id="Base_Effect_Column_List">
    group_id,group_name,
    SUM(effect_completed_task_amount) AS effect_completed_task_amount_all,
    effect_direct_sale_name_str AS direct_sale_name_str,
    effect_channel_sale_name_str AS channel_sale_name_str,
    effect_direct_sale_group_name_str AS direct_sale_group_name_str,
    effect_channel_sale_group_name_str AS channel_sale_group_name_str,
    effect_direct_sea_group_name_str AS direct_sea_group_name_str
  </sql>
  <sql id="Base_PickUp_Column_List">
    group_id,group_name,
    SUM(pickup_completed_task_amount) AS pickup_completed_task_amount_all,
    pickup_direct_sale_name_str AS direct_sale_name_str,
    pickup_channel_sale_name_str AS channel_sale_name_str,
    pickup_direct_sale_group_name_str AS direct_sale_group_name_str,
    pickup_channel_sale_group_name_str AS channel_sale_group_name_str,
    pickup_direct_sea_group_name_str AS direct_sea_group_name_str
  </sql>


  <sql id="Base_All_Order_By_List">
    ORDER BY completed_task_amount_all DESC
  </sql>
  <sql id="Base_Brand_Order_By_List">
    ORDER BY brand_completed_task_amount_all DESC
  </sql>
  <sql id="Base_Effect_Order_By_List">
    ORDER BY effect_completed_task_amount_all DESC
  </sql>
  <sql id="Base_PickUp_Order_By_List">
    ORDER BY pickup_completed_task_amount_all DESC
  </sql>

  <select id="selectListGroupForBizIdAndType" resultMap="BaseResultMap">
    select
    <if test="completeType != null and completeType == 0">
      <include refid="Base_All_Column_List"/>
    </if>
    <if test="completeType != null and completeType == 1">
      <include refid="Base_Brand_Column_List"/>
    </if>
    <if test="completeType != null and completeType == 2">
      <include refid="Base_Effect_Column_List"/>
    </if>
    <if test="completeType != null and completeType == 3">
      <include refid="Base_PickUp_Column_List"/>
    </if>

    from crm_achievement_every_day
    where agg_time <![CDATA[ >= ]]> #{analysisBeginDate,jdbcType=TIMESTAMP}
    AND agg_time <![CDATA[ <= ]]> #{analysisEndDate,jdbcType=TIMESTAMP}
    AND group_id <![CDATA[ != ]]> 0
    AND data_env <![CDATA[ = ]]> #{env,jdbcType=VARCHAR}
    AND biz_id <![CDATA[ = ]]> #{bizId,jdbcType=INTEGER}
    AND biz_type <![CDATA[ = ]]> #{bizType,jdbcType=INTEGER}
    <if test="completeType != null and completeType == 0">
      AND completed_task_amount <![CDATA[ != ]]> 0
    </if>
    <if test="completeType != null and completeType == 1">
      AND brand_completed_task_amount <![CDATA[ != ]]> 0
    </if>
    <if test="completeType != null and completeType == 2">
      AND effect_completed_task_amount <![CDATA[ != ]]> 0
    </if>
    <if test="completeType != null and completeType == 3">
      AND pickup_completed_task_amount <![CDATA[ != ]]> 0
    </if>
    GROUP BY group_id
    <if test="completeType != null and completeType == 0">
      <include refid="Base_All_Order_By_List"/>
    </if>
    <if test="completeType != null and completeType == 1">
      <include refid="Base_Brand_Order_By_List"/>
    </if>
    <if test="completeType != null and completeType == 2">
      <include refid="Base_Effect_Order_By_List"/>
    </if>
    <if test="completeType != null and completeType == 3">
      <include refid="Base_PickUp_Order_By_List"/>
    </if>
    LIMIT 10;

  </select>

  <insert id="batchInsertByPoList" parameterType="java.util.List">
    insert into
    crm_achievement_every_day
    (biz_name,biz_id,biz_type,direct_or_channel,agg_time,group_id,group_name,completed_task_amount,brand_completed_task_amount,
    effect_completed_task_amount,pickup_completed_task_amount,all_direct_sale_name_str,all_channel_sale_name_str,all_direct_sale_group_name_str,
    all_channel_sale_group_name_str,all_direct_sea_group_name_str,brand_direct_sale_name_str,brand_channel_sale_name_str,
    brand_direct_sale_group_name_str,brand_channel_sale_group_name_str,brand_direct_sea_group_name_str,effect_direct_sale_name_str,
    effect_channel_sale_name_str,effect_direct_sale_group_name_str,effect_channel_sale_group_name_str,effect_direct_sea_group_name_str,
    pickup_direct_sale_name_str,pickup_channel_sale_name_str,pickup_direct_sale_group_name_str,pickup_channel_sale_group_name_str,
    pickup_direct_sea_group_name_str,data_env)
    values
    <foreach collection="poList" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bizName,jdbcType=VARCHAR},
        #{item.bizId,jdbcType=INTEGER},
        #{item.bizType,jdbcType=INTEGER},
        #{item.directOrChannel,jdbcType=INTEGER},
        #{item.aggTime,jdbcType=TIMESTAMP},
        #{item.groupId,jdbcType=INTEGER},
        #{item.groupName,jdbcType=VARCHAR},
        #{item.completedTaskAmount,jdbcType=BIGINT},
        #{item.brandCompletedTaskAmount,jdbcType=BIGINT},
        #{item.effectCompletedTaskAmount,jdbcType=BIGINT},
        #{item.pickupCompletedTaskAmount,jdbcType=BIGINT},
        #{item.allDirectSaleNameStr,jdbcType=VARCHAR},
        #{item.allChannelSaleNameStr,jdbcType=VARCHAR},
        #{item.allDirectSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.allChannelSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.allDirectSeaGroupNameStr,jdbcType=VARCHAR},
        #{item.brandDirectSaleNameStr,jdbcType=VARCHAR},
        #{item.brandChannelSaleNameStr,jdbcType=VARCHAR},
        #{item.brandDirectSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.brandChannelSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.brandDirectSeaGroupNameStr,jdbcType=VARCHAR},
        #{item.effectDirectSaleNameStr,jdbcType=VARCHAR},
        #{item.effectChannelSaleNameStr,jdbcType=VARCHAR},
        #{item.effectDirectSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.effectChannelSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.effectDirectSeaGroupNameStr,jdbcType=VARCHAR},
        #{item.pickupDirectSaleNameStr,jdbcType=VARCHAR},
        #{item.pickupChannelSaleNameStr,jdbcType=VARCHAR},
        #{item.pickupDirectSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.pickupChannelSaleGroupNameStr,jdbcType=VARCHAR},
        #{item.pickupDirectSeaGroupNameStr,jdbcType=VARCHAR},
        #{item.dataEnv,jdbcType=VARCHAR},
      </trim>
    </foreach>
    </insert>
</mapper>