<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.new_platform.dao.CrmAgentGroupLevelLabelsMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="agent_group_id" jdbcType="INTEGER" property="agentGroupId" />
    <result column="agent_group_name" jdbcType="VARCHAR" property="agentGroupName" />
    <result column="main_type_label_v1_id" jdbcType="INTEGER" property="mainTypeLabelV1Id" />
    <result column="main_type_label_v1_name" jdbcType="VARCHAR" property="mainTypeLabelV1Name" />
    <result column="main_type_label_v2_id" jdbcType="INTEGER" property="mainTypeLabelV2Id" />
    <result column="main_type_label_v2_name" jdbcType="VARCHAR" property="mainTypeLabelV2Name" />
    <result column="main_type_label_v3_id" jdbcType="INTEGER" property="mainTypeLabelV3Id" />
    <result column="main_type_label_v3_name" jdbcType="VARCHAR" property="mainTypeLabelV3Name" />
    <result column="bus_type_label_id" jdbcType="INTEGER" property="busTypeLabelId" />
    <result column="bus_type_label_name" jdbcType="VARCHAR" property="busTypeLabelName" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    <id column="crm_agent_group_level_labels_mapping_id" jdbcType="BIGINT" property="id" />
    <result column="crm_agent_group_level_labels_mapping_agent_group_id" jdbcType="INTEGER" property="agentGroupId" />
    <result column="crm_agent_group_level_labels_mapping_agent_group_name" jdbcType="VARCHAR" property="agentGroupName" />
    <result column="crm_agent_group_level_labels_mapping_main_type_label_v1_id" jdbcType="INTEGER" property="mainTypeLabelV1Id" />
    <result column="crm_agent_group_level_labels_mapping_main_type_label_v1_name" jdbcType="VARCHAR" property="mainTypeLabelV1Name" />
    <result column="crm_agent_group_level_labels_mapping_main_type_label_v2_id" jdbcType="INTEGER" property="mainTypeLabelV2Id" />
    <result column="crm_agent_group_level_labels_mapping_main_type_label_v2_name" jdbcType="VARCHAR" property="mainTypeLabelV2Name" />
    <result column="crm_agent_group_level_labels_mapping_main_type_label_v3_id" jdbcType="INTEGER" property="mainTypeLabelV3Id" />
    <result column="crm_agent_group_level_labels_mapping_main_type_label_v3_name" jdbcType="VARCHAR" property="mainTypeLabelV3Name" />
    <result column="crm_agent_group_level_labels_mapping_bus_type_label_id" jdbcType="INTEGER" property="busTypeLabelId" />
    <result column="crm_agent_group_level_labels_mapping_bus_type_label_name" jdbcType="VARCHAR" property="busTypeLabelName" />
    <result column="crm_agent_group_level_labels_mapping_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_agent_group_level_labels_mapping_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_agent_group_level_labels_mapping_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_agent_group_level_labels_mapping_id, ${alias}.agent_group_id as crm_agent_group_level_labels_mapping_agent_group_id, 
    ${alias}.agent_group_name as crm_agent_group_level_labels_mapping_agent_group_name, 
    ${alias}.main_type_label_v1_id as crm_agent_group_level_labels_mapping_main_type_label_v1_id, 
    ${alias}.main_type_label_v1_name as crm_agent_group_level_labels_mapping_main_type_label_v1_name, 
    ${alias}.main_type_label_v2_id as crm_agent_group_level_labels_mapping_main_type_label_v2_id, 
    ${alias}.main_type_label_v2_name as crm_agent_group_level_labels_mapping_main_type_label_v2_name, 
    ${alias}.main_type_label_v3_id as crm_agent_group_level_labels_mapping_main_type_label_v3_id, 
    ${alias}.main_type_label_v3_name as crm_agent_group_level_labels_mapping_main_type_label_v3_name, 
    ${alias}.bus_type_label_id as crm_agent_group_level_labels_mapping_bus_type_label_id, 
    ${alias}.bus_type_label_name as crm_agent_group_level_labels_mapping_bus_type_label_name, 
    ${alias}.is_deleted as crm_agent_group_level_labels_mapping_is_deleted, ${alias}.ctime as crm_agent_group_level_labels_mapping_ctime, 
    ${alias}.mtime as crm_agent_group_level_labels_mapping_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, agent_group_id, agent_group_name, main_type_label_v1_id, main_type_label_v1_name, 
    main_type_label_v2_id, main_type_label_v2_name, main_type_label_v3_id, main_type_label_v3_name, 
    bus_type_label_id, bus_type_label_name, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_agent_group_level_labels_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_agent_group_level_labels_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_agent_group_level_labels_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPoExample">
    delete from crm_agent_group_level_labels_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_group_level_labels_mapping (agent_group_id, agent_group_name, main_type_label_v1_id, 
      main_type_label_v1_name, main_type_label_v2_id, 
      main_type_label_v2_name, main_type_label_v3_id, 
      main_type_label_v3_name, bus_type_label_id, bus_type_label_name, 
      is_deleted, ctime, mtime
      )
    values (#{agentGroupId,jdbcType=INTEGER}, #{agentGroupName,jdbcType=VARCHAR}, #{mainTypeLabelV1Id,jdbcType=INTEGER}, 
      #{mainTypeLabelV1Name,jdbcType=VARCHAR}, #{mainTypeLabelV2Id,jdbcType=INTEGER}, 
      #{mainTypeLabelV2Name,jdbcType=VARCHAR}, #{mainTypeLabelV3Id,jdbcType=INTEGER}, 
      #{mainTypeLabelV3Name,jdbcType=VARCHAR}, #{busTypeLabelId,jdbcType=INTEGER}, #{busTypeLabelName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_group_level_labels_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="agentGroupId != null">
        agent_group_id,
      </if>
      <if test="agentGroupName != null">
        agent_group_name,
      </if>
      <if test="mainTypeLabelV1Id != null">
        main_type_label_v1_id,
      </if>
      <if test="mainTypeLabelV1Name != null">
        main_type_label_v1_name,
      </if>
      <if test="mainTypeLabelV2Id != null">
        main_type_label_v2_id,
      </if>
      <if test="mainTypeLabelV2Name != null">
        main_type_label_v2_name,
      </if>
      <if test="mainTypeLabelV3Id != null">
        main_type_label_v3_id,
      </if>
      <if test="mainTypeLabelV3Name != null">
        main_type_label_v3_name,
      </if>
      <if test="busTypeLabelId != null">
        bus_type_label_id,
      </if>
      <if test="busTypeLabelName != null">
        bus_type_label_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="agentGroupId != null">
        #{agentGroupId,jdbcType=INTEGER},
      </if>
      <if test="agentGroupName != null">
        #{agentGroupName,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV1Id != null">
        #{mainTypeLabelV1Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV1Name != null">
        #{mainTypeLabelV1Name,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV2Id != null">
        #{mainTypeLabelV2Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV2Name != null">
        #{mainTypeLabelV2Name,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV3Id != null">
        #{mainTypeLabelV3Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV3Name != null">
        #{mainTypeLabelV3Name,jdbcType=VARCHAR},
      </if>
      <if test="busTypeLabelId != null">
        #{busTypeLabelId,jdbcType=INTEGER},
      </if>
      <if test="busTypeLabelName != null">
        #{busTypeLabelName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPoExample" resultType="java.lang.Long">
    select count(*) from crm_agent_group_level_labels_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_agent_group_level_labels_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.agentGroupId != null">
        agent_group_id = #{record.agentGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.agentGroupName != null">
        agent_group_name = #{record.agentGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.mainTypeLabelV1Id != null">
        main_type_label_v1_id = #{record.mainTypeLabelV1Id,jdbcType=INTEGER},
      </if>
      <if test="record.mainTypeLabelV1Name != null">
        main_type_label_v1_name = #{record.mainTypeLabelV1Name,jdbcType=VARCHAR},
      </if>
      <if test="record.mainTypeLabelV2Id != null">
        main_type_label_v2_id = #{record.mainTypeLabelV2Id,jdbcType=INTEGER},
      </if>
      <if test="record.mainTypeLabelV2Name != null">
        main_type_label_v2_name = #{record.mainTypeLabelV2Name,jdbcType=VARCHAR},
      </if>
      <if test="record.mainTypeLabelV3Id != null">
        main_type_label_v3_id = #{record.mainTypeLabelV3Id,jdbcType=INTEGER},
      </if>
      <if test="record.mainTypeLabelV3Name != null">
        main_type_label_v3_name = #{record.mainTypeLabelV3Name,jdbcType=VARCHAR},
      </if>
      <if test="record.busTypeLabelId != null">
        bus_type_label_id = #{record.busTypeLabelId,jdbcType=INTEGER},
      </if>
      <if test="record.busTypeLabelName != null">
        bus_type_label_name = #{record.busTypeLabelName,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_agent_group_level_labels_mapping
    set id = #{record.id,jdbcType=BIGINT},
      agent_group_id = #{record.agentGroupId,jdbcType=INTEGER},
      agent_group_name = #{record.agentGroupName,jdbcType=VARCHAR},
      main_type_label_v1_id = #{record.mainTypeLabelV1Id,jdbcType=INTEGER},
      main_type_label_v1_name = #{record.mainTypeLabelV1Name,jdbcType=VARCHAR},
      main_type_label_v2_id = #{record.mainTypeLabelV2Id,jdbcType=INTEGER},
      main_type_label_v2_name = #{record.mainTypeLabelV2Name,jdbcType=VARCHAR},
      main_type_label_v3_id = #{record.mainTypeLabelV3Id,jdbcType=INTEGER},
      main_type_label_v3_name = #{record.mainTypeLabelV3Name,jdbcType=VARCHAR},
      bus_type_label_id = #{record.busTypeLabelId,jdbcType=INTEGER},
      bus_type_label_name = #{record.busTypeLabelName,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    update crm_agent_group_level_labels_mapping
    <set>
      <if test="agentGroupId != null">
        agent_group_id = #{agentGroupId,jdbcType=INTEGER},
      </if>
      <if test="agentGroupName != null">
        agent_group_name = #{agentGroupName,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV1Id != null">
        main_type_label_v1_id = #{mainTypeLabelV1Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV1Name != null">
        main_type_label_v1_name = #{mainTypeLabelV1Name,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV2Id != null">
        main_type_label_v2_id = #{mainTypeLabelV2Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV2Name != null">
        main_type_label_v2_name = #{mainTypeLabelV2Name,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV3Id != null">
        main_type_label_v3_id = #{mainTypeLabelV3Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV3Name != null">
        main_type_label_v3_name = #{mainTypeLabelV3Name,jdbcType=VARCHAR},
      </if>
      <if test="busTypeLabelId != null">
        bus_type_label_id = #{busTypeLabelId,jdbcType=INTEGER},
      </if>
      <if test="busTypeLabelName != null">
        bus_type_label_name = #{busTypeLabelName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    update crm_agent_group_level_labels_mapping
    set agent_group_id = #{agentGroupId,jdbcType=INTEGER},
      agent_group_name = #{agentGroupName,jdbcType=VARCHAR},
      main_type_label_v1_id = #{mainTypeLabelV1Id,jdbcType=INTEGER},
      main_type_label_v1_name = #{mainTypeLabelV1Name,jdbcType=VARCHAR},
      main_type_label_v2_id = #{mainTypeLabelV2Id,jdbcType=INTEGER},
      main_type_label_v2_name = #{mainTypeLabelV2Name,jdbcType=VARCHAR},
      main_type_label_v3_id = #{mainTypeLabelV3Id,jdbcType=INTEGER},
      main_type_label_v3_name = #{mainTypeLabelV3Name,jdbcType=VARCHAR},
      bus_type_label_id = #{busTypeLabelId,jdbcType=INTEGER},
      bus_type_label_name = #{busTypeLabelName,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_group_level_labels_mapping (agent_group_id, agent_group_name, main_type_label_v1_id, 
      main_type_label_v1_name, main_type_label_v2_id, 
      main_type_label_v2_name, main_type_label_v3_id, 
      main_type_label_v3_name, bus_type_label_id, bus_type_label_name, 
      is_deleted, ctime, mtime
      )
    values (#{agentGroupId,jdbcType=INTEGER}, #{agentGroupName,jdbcType=VARCHAR}, #{mainTypeLabelV1Id,jdbcType=INTEGER}, 
      #{mainTypeLabelV1Name,jdbcType=VARCHAR}, #{mainTypeLabelV2Id,jdbcType=INTEGER}, 
      #{mainTypeLabelV2Name,jdbcType=VARCHAR}, #{mainTypeLabelV3Id,jdbcType=INTEGER}, 
      #{mainTypeLabelV3Name,jdbcType=VARCHAR}, #{busTypeLabelId,jdbcType=INTEGER}, #{busTypeLabelName,jdbcType=VARCHAR}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      agent_group_id = values(agent_group_id),
      agent_group_name = values(agent_group_name),
      main_type_label_v1_id = values(main_type_label_v1_id),
      main_type_label_v1_name = values(main_type_label_v1_name),
      main_type_label_v2_id = values(main_type_label_v2_id),
      main_type_label_v2_name = values(main_type_label_v2_name),
      main_type_label_v3_id = values(main_type_label_v3_id),
      main_type_label_v3_name = values(main_type_label_v3_name),
      bus_type_label_id = values(bus_type_label_id),
      bus_type_label_name = values(bus_type_label_name),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_agent_group_level_labels_mapping
      (agent_group_id,agent_group_name,main_type_label_v1_id,main_type_label_v1_name,main_type_label_v2_id,main_type_label_v2_name,main_type_label_v3_id,main_type_label_v3_name,bus_type_label_id,bus_type_label_name,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.agentGroupId,jdbcType=INTEGER},
        #{item.agentGroupName,jdbcType=VARCHAR},
        #{item.mainTypeLabelV1Id,jdbcType=INTEGER},
        #{item.mainTypeLabelV1Name,jdbcType=VARCHAR},
        #{item.mainTypeLabelV2Id,jdbcType=INTEGER},
        #{item.mainTypeLabelV2Name,jdbcType=VARCHAR},
        #{item.mainTypeLabelV3Id,jdbcType=INTEGER},
        #{item.mainTypeLabelV3Name,jdbcType=VARCHAR},
        #{item.busTypeLabelId,jdbcType=INTEGER},
        #{item.busTypeLabelName,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_agent_group_level_labels_mapping
      (agent_group_id,agent_group_name,main_type_label_v1_id,main_type_label_v1_name,main_type_label_v2_id,main_type_label_v2_name,main_type_label_v3_id,main_type_label_v3_name,bus_type_label_id,bus_type_label_name,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.agentGroupId,jdbcType=INTEGER},
        #{item.agentGroupName,jdbcType=VARCHAR},
        #{item.mainTypeLabelV1Id,jdbcType=INTEGER},
        #{item.mainTypeLabelV1Name,jdbcType=VARCHAR},
        #{item.mainTypeLabelV2Id,jdbcType=INTEGER},
        #{item.mainTypeLabelV2Name,jdbcType=VARCHAR},
        #{item.mainTypeLabelV3Id,jdbcType=INTEGER},
        #{item.mainTypeLabelV3Name,jdbcType=VARCHAR},
        #{item.busTypeLabelId,jdbcType=INTEGER},
        #{item.busTypeLabelName,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      agent_group_id = values(agent_group_id),
      agent_group_name = values(agent_group_name),
      main_type_label_v1_id = values(main_type_label_v1_id),
      main_type_label_v1_name = values(main_type_label_v1_name),
      main_type_label_v2_id = values(main_type_label_v2_id),
      main_type_label_v2_name = values(main_type_label_v2_name),
      main_type_label_v3_id = values(main_type_label_v3_id),
      main_type_label_v3_name = values(main_type_label_v3_name),
      bus_type_label_id = values(bus_type_label_id),
      bus_type_label_name = values(bus_type_label_name),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.new_platform.po.CrmAgentGroupLevelLabelsMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_group_level_labels_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="agentGroupId != null">
        agent_group_id,
      </if>
      <if test="agentGroupName != null">
        agent_group_name,
      </if>
      <if test="mainTypeLabelV1Id != null">
        main_type_label_v1_id,
      </if>
      <if test="mainTypeLabelV1Name != null">
        main_type_label_v1_name,
      </if>
      <if test="mainTypeLabelV2Id != null">
        main_type_label_v2_id,
      </if>
      <if test="mainTypeLabelV2Name != null">
        main_type_label_v2_name,
      </if>
      <if test="mainTypeLabelV3Id != null">
        main_type_label_v3_id,
      </if>
      <if test="mainTypeLabelV3Name != null">
        main_type_label_v3_name,
      </if>
      <if test="busTypeLabelId != null">
        bus_type_label_id,
      </if>
      <if test="busTypeLabelName != null">
        bus_type_label_name,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="agentGroupId != null">
        #{agentGroupId,jdbcType=INTEGER},
      </if>
      <if test="agentGroupName != null">
        #{agentGroupName,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV1Id != null">
        #{mainTypeLabelV1Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV1Name != null">
        #{mainTypeLabelV1Name,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV2Id != null">
        #{mainTypeLabelV2Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV2Name != null">
        #{mainTypeLabelV2Name,jdbcType=VARCHAR},
      </if>
      <if test="mainTypeLabelV3Id != null">
        #{mainTypeLabelV3Id,jdbcType=INTEGER},
      </if>
      <if test="mainTypeLabelV3Name != null">
        #{mainTypeLabelV3Name,jdbcType=VARCHAR},
      </if>
      <if test="busTypeLabelId != null">
        #{busTypeLabelId,jdbcType=INTEGER},
      </if>
      <if test="busTypeLabelName != null">
        #{busTypeLabelName,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="agentGroupId != null">
        agent_group_id = values(agent_group_id),
      </if>
      <if test="agentGroupName != null">
        agent_group_name = values(agent_group_name),
      </if>
      <if test="mainTypeLabelV1Id != null">
        main_type_label_v1_id = values(main_type_label_v1_id),
      </if>
      <if test="mainTypeLabelV1Name != null">
        main_type_label_v1_name = values(main_type_label_v1_name),
      </if>
      <if test="mainTypeLabelV2Id != null">
        main_type_label_v2_id = values(main_type_label_v2_id),
      </if>
      <if test="mainTypeLabelV2Name != null">
        main_type_label_v2_name = values(main_type_label_v2_name),
      </if>
      <if test="mainTypeLabelV3Id != null">
        main_type_label_v3_id = values(main_type_label_v3_id),
      </if>
      <if test="mainTypeLabelV3Name != null">
        main_type_label_v3_name = values(main_type_label_v3_name),
      </if>
      <if test="busTypeLabelId != null">
        bus_type_label_id = values(bus_type_label_id),
      </if>
      <if test="busTypeLabelName != null">
        bus_type_label_name = values(bus_type_label_name),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>