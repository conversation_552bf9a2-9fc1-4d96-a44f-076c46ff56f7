<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmReturnApplyAgentInputDataDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="rel_id" jdbcType="INTEGER" property="relId" />
    <result column="return_apply_id" jdbcType="INTEGER" property="returnApplyId" />
    <result column="return_policy_id" jdbcType="INTEGER" property="returnPolicyId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="task_value" jdbcType="BIGINT" property="taskValue" />
    <result column="point_level" jdbcType="VARCHAR" property="pointLevel" />
    <result column="grade_level" jdbcType="VARCHAR" property="gradeLevel" />
    <result column="merge_group" jdbcType="VARCHAR" property="mergeGroup" />
    <result column="cal_status" jdbcType="TINYINT" property="calStatus" />
    <result column="cal_level" jdbcType="VARCHAR" property="calLevel" />
    <result column="cal_complete_gear" jdbcType="VARCHAR" property="calCompleteGear" />
    <result column="cal_return_ratio" jdbcType="DECIMAL" property="calReturnRatio" />
    <result column="complate_ratio" jdbcType="DECIMAL" property="complateRatio" />
    <result column="cal_return_amount" jdbcType="BIGINT" property="calReturnAmount" />
    <result column="cal_cur_period_total_cost" jdbcType="BIGINT" property="calCurPeriodTotalCost" />
    <result column="cal_cur_period_cash_cost" jdbcType="BIGINT" property="calCurPeriodCashCost" />
    <result column="cal_acc_pre_task_cost" jdbcType="BIGINT" property="calAccPreTaskCost" />
    <result column="cal_acc_cur_task_cost" jdbcType="BIGINT" property="calAccCurTaskCost" />
    <result column="cal_group_pre_task_cost" jdbcType="BIGINT" property="calGroupPreTaskCost" />
    <result column="cal_group_cur_task_cost" jdbcType="BIGINT" property="calGroupCurTaskCost" />
    <result column="cal_group_pre_return_cost" jdbcType="BIGINT" property="calGroupPreReturnCost" />
    <result column="cal_group_cur_return_cost" jdbcType="BIGINT" property="calGroupCurReturnCost" />
    <result column="cal_acc_cur_return_cost" jdbcType="BIGINT" property="calAccCurReturnCost" />
    <result column="task_stand_cardinal_number" jdbcType="BIGINT" property="taskStandCardinalNumber" />
    <result column="return_cal_cardinal_number" jdbcType="BIGINT" property="returnCalCardinalNumber" />
    <result column="reckon_in_task_stand" jdbcType="TINYINT" property="reckonInTaskStand" />
    <result column="reckon_in_return_cal" jdbcType="TINYINT" property="reckonInReturnCal" />
    <result column="product_types" jdbcType="VARCHAR" property="productTypes" />
    <result column="complete_time" jdbcType="TIMESTAMP" property="completeTime" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="row" jdbcType="INTEGER" property="row" />
    <result column="real_cal_group_pre_task_cost" jdbcType="BIGINT" property="realCalGroupPreTaskCost" />
    <result column="real_cal_group_cur_task_cost" jdbcType="BIGINT" property="realCalGroupCurTaskCost" />
    <result column="last_quarter_task_stand_cardinal_number" jdbcType="BIGINT" property="lastQuarterTaskStandCardinalNumber" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, rel_id, return_apply_id, return_policy_id, account_id, task_value, point_level, 
    grade_level, merge_group, cal_status, cal_level, cal_complete_gear, cal_return_ratio, 
    complate_ratio, cal_return_amount, cal_cur_period_total_cost, cal_cur_period_cash_cost, 
    cal_acc_pre_task_cost, cal_acc_cur_task_cost, cal_group_pre_task_cost, cal_group_cur_task_cost, 
    cal_group_pre_return_cost, cal_group_cur_return_cost, cal_acc_cur_return_cost, task_stand_cardinal_number, 
    return_cal_cardinal_number, reckon_in_task_stand, reckon_in_return_cal, product_types, 
    complete_time, remark, ctime, mtime, is_deleted, row, real_cal_group_pre_task_cost, 
    real_cal_group_cur_task_cost, last_quarter_task_stand_cardinal_number
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_return_apply_agent_input_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_return_apply_agent_input_data
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_return_apply_agent_input_data
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPoExample">
    delete from crm_return_apply_agent_input_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_return_apply_agent_input_data (rel_id, return_apply_id, return_policy_id, 
      account_id, task_value, point_level, 
      grade_level, merge_group, cal_status, 
      cal_level, cal_complete_gear, cal_return_ratio, 
      complate_ratio, cal_return_amount, cal_cur_period_total_cost, 
      cal_cur_period_cash_cost, cal_acc_pre_task_cost, 
      cal_acc_cur_task_cost, cal_group_pre_task_cost, cal_group_cur_task_cost, 
      cal_group_pre_return_cost, cal_group_cur_return_cost, 
      cal_acc_cur_return_cost, task_stand_cardinal_number, 
      return_cal_cardinal_number, reckon_in_task_stand, 
      reckon_in_return_cal, product_types, complete_time, 
      remark, ctime, mtime, 
      is_deleted, row, real_cal_group_pre_task_cost, 
      real_cal_group_cur_task_cost, last_quarter_task_stand_cardinal_number
      )
    values (#{relId,jdbcType=INTEGER}, #{returnApplyId,jdbcType=INTEGER}, #{returnPolicyId,jdbcType=INTEGER}, 
      #{accountId,jdbcType=INTEGER}, #{taskValue,jdbcType=BIGINT}, #{pointLevel,jdbcType=VARCHAR}, 
      #{gradeLevel,jdbcType=VARCHAR}, #{mergeGroup,jdbcType=VARCHAR}, #{calStatus,jdbcType=TINYINT}, 
      #{calLevel,jdbcType=VARCHAR}, #{calCompleteGear,jdbcType=VARCHAR}, #{calReturnRatio,jdbcType=DECIMAL}, 
      #{complateRatio,jdbcType=DECIMAL}, #{calReturnAmount,jdbcType=BIGINT}, #{calCurPeriodTotalCost,jdbcType=BIGINT}, 
      #{calCurPeriodCashCost,jdbcType=BIGINT}, #{calAccPreTaskCost,jdbcType=BIGINT}, 
      #{calAccCurTaskCost,jdbcType=BIGINT}, #{calGroupPreTaskCost,jdbcType=BIGINT}, #{calGroupCurTaskCost,jdbcType=BIGINT}, 
      #{calGroupPreReturnCost,jdbcType=BIGINT}, #{calGroupCurReturnCost,jdbcType=BIGINT}, 
      #{calAccCurReturnCost,jdbcType=BIGINT}, #{taskStandCardinalNumber,jdbcType=BIGINT}, 
      #{returnCalCardinalNumber,jdbcType=BIGINT}, #{reckonInTaskStand,jdbcType=TINYINT}, 
      #{reckonInReturnCal,jdbcType=TINYINT}, #{productTypes,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{row,jdbcType=INTEGER}, #{realCalGroupPreTaskCost,jdbcType=BIGINT}, 
      #{realCalGroupCurTaskCost,jdbcType=BIGINT}, #{lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_return_apply_agent_input_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relId != null">
        rel_id,
      </if>
      <if test="returnApplyId != null">
        return_apply_id,
      </if>
      <if test="returnPolicyId != null">
        return_policy_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="taskValue != null">
        task_value,
      </if>
      <if test="pointLevel != null">
        point_level,
      </if>
      <if test="gradeLevel != null">
        grade_level,
      </if>
      <if test="mergeGroup != null">
        merge_group,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="calLevel != null">
        cal_level,
      </if>
      <if test="calCompleteGear != null">
        cal_complete_gear,
      </if>
      <if test="calReturnRatio != null">
        cal_return_ratio,
      </if>
      <if test="complateRatio != null">
        complate_ratio,
      </if>
      <if test="calReturnAmount != null">
        cal_return_amount,
      </if>
      <if test="calCurPeriodTotalCost != null">
        cal_cur_period_total_cost,
      </if>
      <if test="calCurPeriodCashCost != null">
        cal_cur_period_cash_cost,
      </if>
      <if test="calAccPreTaskCost != null">
        cal_acc_pre_task_cost,
      </if>
      <if test="calAccCurTaskCost != null">
        cal_acc_cur_task_cost,
      </if>
      <if test="calGroupPreTaskCost != null">
        cal_group_pre_task_cost,
      </if>
      <if test="calGroupCurTaskCost != null">
        cal_group_cur_task_cost,
      </if>
      <if test="calGroupPreReturnCost != null">
        cal_group_pre_return_cost,
      </if>
      <if test="calGroupCurReturnCost != null">
        cal_group_cur_return_cost,
      </if>
      <if test="calAccCurReturnCost != null">
        cal_acc_cur_return_cost,
      </if>
      <if test="taskStandCardinalNumber != null">
        task_stand_cardinal_number,
      </if>
      <if test="returnCalCardinalNumber != null">
        return_cal_cardinal_number,
      </if>
      <if test="reckonInTaskStand != null">
        reckon_in_task_stand,
      </if>
      <if test="reckonInReturnCal != null">
        reckon_in_return_cal,
      </if>
      <if test="productTypes != null">
        product_types,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="row != null">
        row,
      </if>
      <if test="realCalGroupPreTaskCost != null">
        real_cal_group_pre_task_cost,
      </if>
      <if test="realCalGroupCurTaskCost != null">
        real_cal_group_cur_task_cost,
      </if>
      <if test="lastQuarterTaskStandCardinalNumber != null">
        last_quarter_task_stand_cardinal_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relId != null">
        #{relId,jdbcType=INTEGER},
      </if>
      <if test="returnApplyId != null">
        #{returnApplyId,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyId != null">
        #{returnPolicyId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="taskValue != null">
        #{taskValue,jdbcType=BIGINT},
      </if>
      <if test="pointLevel != null">
        #{pointLevel,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevel != null">
        #{gradeLevel,jdbcType=VARCHAR},
      </if>
      <if test="mergeGroup != null">
        #{mergeGroup,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="calLevel != null">
        #{calLevel,jdbcType=VARCHAR},
      </if>
      <if test="calCompleteGear != null">
        #{calCompleteGear,jdbcType=VARCHAR},
      </if>
      <if test="calReturnRatio != null">
        #{calReturnRatio,jdbcType=DECIMAL},
      </if>
      <if test="complateRatio != null">
        #{complateRatio,jdbcType=DECIMAL},
      </if>
      <if test="calReturnAmount != null">
        #{calReturnAmount,jdbcType=BIGINT},
      </if>
      <if test="calCurPeriodTotalCost != null">
        #{calCurPeriodTotalCost,jdbcType=BIGINT},
      </if>
      <if test="calCurPeriodCashCost != null">
        #{calCurPeriodCashCost,jdbcType=BIGINT},
      </if>
      <if test="calAccPreTaskCost != null">
        #{calAccPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calAccCurTaskCost != null">
        #{calAccCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupPreTaskCost != null">
        #{calGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupCurTaskCost != null">
        #{calGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupPreReturnCost != null">
        #{calGroupPreReturnCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupCurReturnCost != null">
        #{calGroupCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="calAccCurReturnCost != null">
        #{calAccCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="taskStandCardinalNumber != null">
        #{taskStandCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="returnCalCardinalNumber != null">
        #{returnCalCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="reckonInTaskStand != null">
        #{reckonInTaskStand,jdbcType=TINYINT},
      </if>
      <if test="reckonInReturnCal != null">
        #{reckonInReturnCal,jdbcType=TINYINT},
      </if>
      <if test="productTypes != null">
        #{productTypes,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="row != null">
        #{row,jdbcType=INTEGER},
      </if>
      <if test="realCalGroupPreTaskCost != null">
        #{realCalGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="realCalGroupCurTaskCost != null">
        #{realCalGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="lastQuarterTaskStandCardinalNumber != null">
        #{lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPoExample" resultType="java.lang.Long">
    select count(*) from crm_return_apply_agent_input_data
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_return_apply_agent_input_data
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.relId != null">
        rel_id = #{record.relId,jdbcType=INTEGER},
      </if>
      <if test="record.returnApplyId != null">
        return_apply_id = #{record.returnApplyId,jdbcType=INTEGER},
      </if>
      <if test="record.returnPolicyId != null">
        return_policy_id = #{record.returnPolicyId,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.taskValue != null">
        task_value = #{record.taskValue,jdbcType=BIGINT},
      </if>
      <if test="record.pointLevel != null">
        point_level = #{record.pointLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.gradeLevel != null">
        grade_level = #{record.gradeLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.mergeGroup != null">
        merge_group = #{record.mergeGroup,jdbcType=VARCHAR},
      </if>
      <if test="record.calStatus != null">
        cal_status = #{record.calStatus,jdbcType=TINYINT},
      </if>
      <if test="record.calLevel != null">
        cal_level = #{record.calLevel,jdbcType=VARCHAR},
      </if>
      <if test="record.calCompleteGear != null">
        cal_complete_gear = #{record.calCompleteGear,jdbcType=VARCHAR},
      </if>
      <if test="record.calReturnRatio != null">
        cal_return_ratio = #{record.calReturnRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.complateRatio != null">
        complate_ratio = #{record.complateRatio,jdbcType=DECIMAL},
      </if>
      <if test="record.calReturnAmount != null">
        cal_return_amount = #{record.calReturnAmount,jdbcType=BIGINT},
      </if>
      <if test="record.calCurPeriodTotalCost != null">
        cal_cur_period_total_cost = #{record.calCurPeriodTotalCost,jdbcType=BIGINT},
      </if>
      <if test="record.calCurPeriodCashCost != null">
        cal_cur_period_cash_cost = #{record.calCurPeriodCashCost,jdbcType=BIGINT},
      </if>
      <if test="record.calAccPreTaskCost != null">
        cal_acc_pre_task_cost = #{record.calAccPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="record.calAccCurTaskCost != null">
        cal_acc_cur_task_cost = #{record.calAccCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="record.calGroupPreTaskCost != null">
        cal_group_pre_task_cost = #{record.calGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="record.calGroupCurTaskCost != null">
        cal_group_cur_task_cost = #{record.calGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="record.calGroupPreReturnCost != null">
        cal_group_pre_return_cost = #{record.calGroupPreReturnCost,jdbcType=BIGINT},
      </if>
      <if test="record.calGroupCurReturnCost != null">
        cal_group_cur_return_cost = #{record.calGroupCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="record.calAccCurReturnCost != null">
        cal_acc_cur_return_cost = #{record.calAccCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="record.taskStandCardinalNumber != null">
        task_stand_cardinal_number = #{record.taskStandCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="record.returnCalCardinalNumber != null">
        return_cal_cardinal_number = #{record.returnCalCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="record.reckonInTaskStand != null">
        reckon_in_task_stand = #{record.reckonInTaskStand,jdbcType=TINYINT},
      </if>
      <if test="record.reckonInReturnCal != null">
        reckon_in_return_cal = #{record.reckonInReturnCal,jdbcType=TINYINT},
      </if>
      <if test="record.productTypes != null">
        product_types = #{record.productTypes,jdbcType=VARCHAR},
      </if>
      <if test="record.completeTime != null">
        complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.row != null">
        row = #{record.row,jdbcType=INTEGER},
      </if>
      <if test="record.realCalGroupPreTaskCost != null">
        real_cal_group_pre_task_cost = #{record.realCalGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="record.realCalGroupCurTaskCost != null">
        real_cal_group_cur_task_cost = #{record.realCalGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="record.lastQuarterTaskStandCardinalNumber != null">
        last_quarter_task_stand_cardinal_number = #{record.lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_return_apply_agent_input_data
    set id = #{record.id,jdbcType=INTEGER},
      rel_id = #{record.relId,jdbcType=INTEGER},
      return_apply_id = #{record.returnApplyId,jdbcType=INTEGER},
      return_policy_id = #{record.returnPolicyId,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      task_value = #{record.taskValue,jdbcType=BIGINT},
      point_level = #{record.pointLevel,jdbcType=VARCHAR},
      grade_level = #{record.gradeLevel,jdbcType=VARCHAR},
      merge_group = #{record.mergeGroup,jdbcType=VARCHAR},
      cal_status = #{record.calStatus,jdbcType=TINYINT},
      cal_level = #{record.calLevel,jdbcType=VARCHAR},
      cal_complete_gear = #{record.calCompleteGear,jdbcType=VARCHAR},
      cal_return_ratio = #{record.calReturnRatio,jdbcType=DECIMAL},
      complate_ratio = #{record.complateRatio,jdbcType=DECIMAL},
      cal_return_amount = #{record.calReturnAmount,jdbcType=BIGINT},
      cal_cur_period_total_cost = #{record.calCurPeriodTotalCost,jdbcType=BIGINT},
      cal_cur_period_cash_cost = #{record.calCurPeriodCashCost,jdbcType=BIGINT},
      cal_acc_pre_task_cost = #{record.calAccPreTaskCost,jdbcType=BIGINT},
      cal_acc_cur_task_cost = #{record.calAccCurTaskCost,jdbcType=BIGINT},
      cal_group_pre_task_cost = #{record.calGroupPreTaskCost,jdbcType=BIGINT},
      cal_group_cur_task_cost = #{record.calGroupCurTaskCost,jdbcType=BIGINT},
      cal_group_pre_return_cost = #{record.calGroupPreReturnCost,jdbcType=BIGINT},
      cal_group_cur_return_cost = #{record.calGroupCurReturnCost,jdbcType=BIGINT},
      cal_acc_cur_return_cost = #{record.calAccCurReturnCost,jdbcType=BIGINT},
      task_stand_cardinal_number = #{record.taskStandCardinalNumber,jdbcType=BIGINT},
      return_cal_cardinal_number = #{record.returnCalCardinalNumber,jdbcType=BIGINT},
      reckon_in_task_stand = #{record.reckonInTaskStand,jdbcType=TINYINT},
      reckon_in_return_cal = #{record.reckonInReturnCal,jdbcType=TINYINT},
      product_types = #{record.productTypes,jdbcType=VARCHAR},
      complete_time = #{record.completeTime,jdbcType=TIMESTAMP},
      remark = #{record.remark,jdbcType=VARCHAR},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      row = #{record.row,jdbcType=INTEGER},
      real_cal_group_pre_task_cost = #{record.realCalGroupPreTaskCost,jdbcType=BIGINT},
      real_cal_group_cur_task_cost = #{record.realCalGroupCurTaskCost,jdbcType=BIGINT},
      last_quarter_task_stand_cardinal_number = #{record.lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    update crm_return_apply_agent_input_data
    <set>
      <if test="relId != null">
        rel_id = #{relId,jdbcType=INTEGER},
      </if>
      <if test="returnApplyId != null">
        return_apply_id = #{returnApplyId,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyId != null">
        return_policy_id = #{returnPolicyId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="taskValue != null">
        task_value = #{taskValue,jdbcType=BIGINT},
      </if>
      <if test="pointLevel != null">
        point_level = #{pointLevel,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevel != null">
        grade_level = #{gradeLevel,jdbcType=VARCHAR},
      </if>
      <if test="mergeGroup != null">
        merge_group = #{mergeGroup,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        cal_status = #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="calLevel != null">
        cal_level = #{calLevel,jdbcType=VARCHAR},
      </if>
      <if test="calCompleteGear != null">
        cal_complete_gear = #{calCompleteGear,jdbcType=VARCHAR},
      </if>
      <if test="calReturnRatio != null">
        cal_return_ratio = #{calReturnRatio,jdbcType=DECIMAL},
      </if>
      <if test="complateRatio != null">
        complate_ratio = #{complateRatio,jdbcType=DECIMAL},
      </if>
      <if test="calReturnAmount != null">
        cal_return_amount = #{calReturnAmount,jdbcType=BIGINT},
      </if>
      <if test="calCurPeriodTotalCost != null">
        cal_cur_period_total_cost = #{calCurPeriodTotalCost,jdbcType=BIGINT},
      </if>
      <if test="calCurPeriodCashCost != null">
        cal_cur_period_cash_cost = #{calCurPeriodCashCost,jdbcType=BIGINT},
      </if>
      <if test="calAccPreTaskCost != null">
        cal_acc_pre_task_cost = #{calAccPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calAccCurTaskCost != null">
        cal_acc_cur_task_cost = #{calAccCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupPreTaskCost != null">
        cal_group_pre_task_cost = #{calGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupCurTaskCost != null">
        cal_group_cur_task_cost = #{calGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupPreReturnCost != null">
        cal_group_pre_return_cost = #{calGroupPreReturnCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupCurReturnCost != null">
        cal_group_cur_return_cost = #{calGroupCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="calAccCurReturnCost != null">
        cal_acc_cur_return_cost = #{calAccCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="taskStandCardinalNumber != null">
        task_stand_cardinal_number = #{taskStandCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="returnCalCardinalNumber != null">
        return_cal_cardinal_number = #{returnCalCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="reckonInTaskStand != null">
        reckon_in_task_stand = #{reckonInTaskStand,jdbcType=TINYINT},
      </if>
      <if test="reckonInReturnCal != null">
        reckon_in_return_cal = #{reckonInReturnCal,jdbcType=TINYINT},
      </if>
      <if test="productTypes != null">
        product_types = #{productTypes,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        complete_time = #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="row != null">
        row = #{row,jdbcType=INTEGER},
      </if>
      <if test="realCalGroupPreTaskCost != null">
        real_cal_group_pre_task_cost = #{realCalGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="realCalGroupCurTaskCost != null">
        real_cal_group_cur_task_cost = #{realCalGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="lastQuarterTaskStandCardinalNumber != null">
        last_quarter_task_stand_cardinal_number = #{lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    update crm_return_apply_agent_input_data
    set rel_id = #{relId,jdbcType=INTEGER},
      return_apply_id = #{returnApplyId,jdbcType=INTEGER},
      return_policy_id = #{returnPolicyId,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      task_value = #{taskValue,jdbcType=BIGINT},
      point_level = #{pointLevel,jdbcType=VARCHAR},
      grade_level = #{gradeLevel,jdbcType=VARCHAR},
      merge_group = #{mergeGroup,jdbcType=VARCHAR},
      cal_status = #{calStatus,jdbcType=TINYINT},
      cal_level = #{calLevel,jdbcType=VARCHAR},
      cal_complete_gear = #{calCompleteGear,jdbcType=VARCHAR},
      cal_return_ratio = #{calReturnRatio,jdbcType=DECIMAL},
      complate_ratio = #{complateRatio,jdbcType=DECIMAL},
      cal_return_amount = #{calReturnAmount,jdbcType=BIGINT},
      cal_cur_period_total_cost = #{calCurPeriodTotalCost,jdbcType=BIGINT},
      cal_cur_period_cash_cost = #{calCurPeriodCashCost,jdbcType=BIGINT},
      cal_acc_pre_task_cost = #{calAccPreTaskCost,jdbcType=BIGINT},
      cal_acc_cur_task_cost = #{calAccCurTaskCost,jdbcType=BIGINT},
      cal_group_pre_task_cost = #{calGroupPreTaskCost,jdbcType=BIGINT},
      cal_group_cur_task_cost = #{calGroupCurTaskCost,jdbcType=BIGINT},
      cal_group_pre_return_cost = #{calGroupPreReturnCost,jdbcType=BIGINT},
      cal_group_cur_return_cost = #{calGroupCurReturnCost,jdbcType=BIGINT},
      cal_acc_cur_return_cost = #{calAccCurReturnCost,jdbcType=BIGINT},
      task_stand_cardinal_number = #{taskStandCardinalNumber,jdbcType=BIGINT},
      return_cal_cardinal_number = #{returnCalCardinalNumber,jdbcType=BIGINT},
      reckon_in_task_stand = #{reckonInTaskStand,jdbcType=TINYINT},
      reckon_in_return_cal = #{reckonInReturnCal,jdbcType=TINYINT},
      product_types = #{productTypes,jdbcType=VARCHAR},
      complete_time = #{completeTime,jdbcType=TIMESTAMP},
      remark = #{remark,jdbcType=VARCHAR},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      row = #{row,jdbcType=INTEGER},
      real_cal_group_pre_task_cost = #{realCalGroupPreTaskCost,jdbcType=BIGINT},
      real_cal_group_cur_task_cost = #{realCalGroupCurTaskCost,jdbcType=BIGINT},
      last_quarter_task_stand_cardinal_number = #{lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_return_apply_agent_input_data (rel_id, return_apply_id, return_policy_id, 
      account_id, task_value, point_level, 
      grade_level, merge_group, cal_status, 
      cal_level, cal_complete_gear, cal_return_ratio, 
      complate_ratio, cal_return_amount, cal_cur_period_total_cost, 
      cal_cur_period_cash_cost, cal_acc_pre_task_cost, 
      cal_acc_cur_task_cost, cal_group_pre_task_cost, cal_group_cur_task_cost, 
      cal_group_pre_return_cost, cal_group_cur_return_cost, 
      cal_acc_cur_return_cost, task_stand_cardinal_number, 
      return_cal_cardinal_number, reckon_in_task_stand, 
      reckon_in_return_cal, product_types, complete_time, 
      remark, ctime, mtime, 
      is_deleted, row, real_cal_group_pre_task_cost, 
      real_cal_group_cur_task_cost, last_quarter_task_stand_cardinal_number
      )
    values (#{relId,jdbcType=INTEGER}, #{returnApplyId,jdbcType=INTEGER}, #{returnPolicyId,jdbcType=INTEGER}, 
      #{accountId,jdbcType=INTEGER}, #{taskValue,jdbcType=BIGINT}, #{pointLevel,jdbcType=VARCHAR}, 
      #{gradeLevel,jdbcType=VARCHAR}, #{mergeGroup,jdbcType=VARCHAR}, #{calStatus,jdbcType=TINYINT}, 
      #{calLevel,jdbcType=VARCHAR}, #{calCompleteGear,jdbcType=VARCHAR}, #{calReturnRatio,jdbcType=DECIMAL}, 
      #{complateRatio,jdbcType=DECIMAL}, #{calReturnAmount,jdbcType=BIGINT}, #{calCurPeriodTotalCost,jdbcType=BIGINT}, 
      #{calCurPeriodCashCost,jdbcType=BIGINT}, #{calAccPreTaskCost,jdbcType=BIGINT}, 
      #{calAccCurTaskCost,jdbcType=BIGINT}, #{calGroupPreTaskCost,jdbcType=BIGINT}, #{calGroupCurTaskCost,jdbcType=BIGINT}, 
      #{calGroupPreReturnCost,jdbcType=BIGINT}, #{calGroupCurReturnCost,jdbcType=BIGINT}, 
      #{calAccCurReturnCost,jdbcType=BIGINT}, #{taskStandCardinalNumber,jdbcType=BIGINT}, 
      #{returnCalCardinalNumber,jdbcType=BIGINT}, #{reckonInTaskStand,jdbcType=TINYINT}, 
      #{reckonInReturnCal,jdbcType=TINYINT}, #{productTypes,jdbcType=VARCHAR}, #{completeTime,jdbcType=TIMESTAMP}, 
      #{remark,jdbcType=VARCHAR}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{row,jdbcType=INTEGER}, #{realCalGroupPreTaskCost,jdbcType=BIGINT}, 
      #{realCalGroupCurTaskCost,jdbcType=BIGINT}, #{lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      rel_id = values(rel_id),
      return_apply_id = values(return_apply_id),
      return_policy_id = values(return_policy_id),
      account_id = values(account_id),
      task_value = values(task_value),
      point_level = values(point_level),
      grade_level = values(grade_level),
      merge_group = values(merge_group),
      cal_status = values(cal_status),
      cal_level = values(cal_level),
      cal_complete_gear = values(cal_complete_gear),
      cal_return_ratio = values(cal_return_ratio),
      complate_ratio = values(complate_ratio),
      cal_return_amount = values(cal_return_amount),
      cal_cur_period_total_cost = values(cal_cur_period_total_cost),
      cal_cur_period_cash_cost = values(cal_cur_period_cash_cost),
      cal_acc_pre_task_cost = values(cal_acc_pre_task_cost),
      cal_acc_cur_task_cost = values(cal_acc_cur_task_cost),
      cal_group_pre_task_cost = values(cal_group_pre_task_cost),
      cal_group_cur_task_cost = values(cal_group_cur_task_cost),
      cal_group_pre_return_cost = values(cal_group_pre_return_cost),
      cal_group_cur_return_cost = values(cal_group_cur_return_cost),
      cal_acc_cur_return_cost = values(cal_acc_cur_return_cost),
      task_stand_cardinal_number = values(task_stand_cardinal_number),
      return_cal_cardinal_number = values(return_cal_cardinal_number),
      reckon_in_task_stand = values(reckon_in_task_stand),
      reckon_in_return_cal = values(reckon_in_return_cal),
      product_types = values(product_types),
      complete_time = values(complete_time),
      remark = values(remark),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      row = values(row),
      real_cal_group_pre_task_cost = values(real_cal_group_pre_task_cost),
      real_cal_group_cur_task_cost = values(real_cal_group_cur_task_cost),
      last_quarter_task_stand_cardinal_number = values(last_quarter_task_stand_cardinal_number),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_return_apply_agent_input_data
      (rel_id,return_apply_id,return_policy_id,account_id,task_value,point_level,grade_level,merge_group,cal_status,cal_level,cal_complete_gear,cal_return_ratio,complate_ratio,cal_return_amount,cal_cur_period_total_cost,cal_cur_period_cash_cost,cal_acc_pre_task_cost,cal_acc_cur_task_cost,cal_group_pre_task_cost,cal_group_cur_task_cost,cal_group_pre_return_cost,cal_group_cur_return_cost,cal_acc_cur_return_cost,task_stand_cardinal_number,return_cal_cardinal_number,reckon_in_task_stand,reckon_in_return_cal,product_types,complete_time,remark,ctime,mtime,is_deleted,row,real_cal_group_pre_task_cost,real_cal_group_cur_task_cost,last_quarter_task_stand_cardinal_number)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.relId,jdbcType=INTEGER},
        #{item.returnApplyId,jdbcType=INTEGER},
        #{item.returnPolicyId,jdbcType=INTEGER},
        #{item.accountId,jdbcType=INTEGER},
        #{item.taskValue,jdbcType=BIGINT},
        #{item.pointLevel,jdbcType=VARCHAR},
        #{item.gradeLevel,jdbcType=VARCHAR},
        #{item.mergeGroup,jdbcType=VARCHAR},
        #{item.calStatus,jdbcType=TINYINT},
        #{item.calLevel,jdbcType=VARCHAR},
        #{item.calCompleteGear,jdbcType=VARCHAR},
        #{item.calReturnRatio,jdbcType=DECIMAL},
        #{item.complateRatio,jdbcType=DECIMAL},
        #{item.calReturnAmount,jdbcType=BIGINT},
        #{item.calCurPeriodTotalCost,jdbcType=BIGINT},
        #{item.calCurPeriodCashCost,jdbcType=BIGINT},
        #{item.calAccPreTaskCost,jdbcType=BIGINT},
        #{item.calAccCurTaskCost,jdbcType=BIGINT},
        #{item.calGroupPreTaskCost,jdbcType=BIGINT},
        #{item.calGroupCurTaskCost,jdbcType=BIGINT},
        #{item.calGroupPreReturnCost,jdbcType=BIGINT},
        #{item.calGroupCurReturnCost,jdbcType=BIGINT},
        #{item.calAccCurReturnCost,jdbcType=BIGINT},
        #{item.taskStandCardinalNumber,jdbcType=BIGINT},
        #{item.returnCalCardinalNumber,jdbcType=BIGINT},
        #{item.reckonInTaskStand,jdbcType=TINYINT},
        #{item.reckonInReturnCal,jdbcType=TINYINT},
        #{item.productTypes,jdbcType=VARCHAR},
        #{item.completeTime,jdbcType=TIMESTAMP},
        #{item.remark,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.row,jdbcType=INTEGER},
        #{item.realCalGroupPreTaskCost,jdbcType=BIGINT},
        #{item.realCalGroupCurTaskCost,jdbcType=BIGINT},
        #{item.lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_return_apply_agent_input_data
      (rel_id,return_apply_id,return_policy_id,account_id,task_value,point_level,grade_level,merge_group,cal_status,cal_level,cal_complete_gear,cal_return_ratio,complate_ratio,cal_return_amount,cal_cur_period_total_cost,cal_cur_period_cash_cost,cal_acc_pre_task_cost,cal_acc_cur_task_cost,cal_group_pre_task_cost,cal_group_cur_task_cost,cal_group_pre_return_cost,cal_group_cur_return_cost,cal_acc_cur_return_cost,task_stand_cardinal_number,return_cal_cardinal_number,reckon_in_task_stand,reckon_in_return_cal,product_types,complete_time,remark,ctime,mtime,is_deleted,row,real_cal_group_pre_task_cost,real_cal_group_cur_task_cost,last_quarter_task_stand_cardinal_number)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.relId,jdbcType=INTEGER},
        #{item.returnApplyId,jdbcType=INTEGER},
        #{item.returnPolicyId,jdbcType=INTEGER},
        #{item.accountId,jdbcType=INTEGER},
        #{item.taskValue,jdbcType=BIGINT},
        #{item.pointLevel,jdbcType=VARCHAR},
        #{item.gradeLevel,jdbcType=VARCHAR},
        #{item.mergeGroup,jdbcType=VARCHAR},
        #{item.calStatus,jdbcType=TINYINT},
        #{item.calLevel,jdbcType=VARCHAR},
        #{item.calCompleteGear,jdbcType=VARCHAR},
        #{item.calReturnRatio,jdbcType=DECIMAL},
        #{item.complateRatio,jdbcType=DECIMAL},
        #{item.calReturnAmount,jdbcType=BIGINT},
        #{item.calCurPeriodTotalCost,jdbcType=BIGINT},
        #{item.calCurPeriodCashCost,jdbcType=BIGINT},
        #{item.calAccPreTaskCost,jdbcType=BIGINT},
        #{item.calAccCurTaskCost,jdbcType=BIGINT},
        #{item.calGroupPreTaskCost,jdbcType=BIGINT},
        #{item.calGroupCurTaskCost,jdbcType=BIGINT},
        #{item.calGroupPreReturnCost,jdbcType=BIGINT},
        #{item.calGroupCurReturnCost,jdbcType=BIGINT},
        #{item.calAccCurReturnCost,jdbcType=BIGINT},
        #{item.taskStandCardinalNumber,jdbcType=BIGINT},
        #{item.returnCalCardinalNumber,jdbcType=BIGINT},
        #{item.reckonInTaskStand,jdbcType=TINYINT},
        #{item.reckonInReturnCal,jdbcType=TINYINT},
        #{item.productTypes,jdbcType=VARCHAR},
        #{item.completeTime,jdbcType=TIMESTAMP},
        #{item.remark,jdbcType=VARCHAR},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.row,jdbcType=INTEGER},
        #{item.realCalGroupPreTaskCost,jdbcType=BIGINT},
        #{item.realCalGroupCurTaskCost,jdbcType=BIGINT},
        #{item.lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      rel_id = values(rel_id),
      return_apply_id = values(return_apply_id),
      return_policy_id = values(return_policy_id),
      account_id = values(account_id),
      task_value = values(task_value),
      point_level = values(point_level),
      grade_level = values(grade_level),
      merge_group = values(merge_group),
      cal_status = values(cal_status),
      cal_level = values(cal_level),
      cal_complete_gear = values(cal_complete_gear),
      cal_return_ratio = values(cal_return_ratio),
      complate_ratio = values(complate_ratio),
      cal_return_amount = values(cal_return_amount),
      cal_cur_period_total_cost = values(cal_cur_period_total_cost),
      cal_cur_period_cash_cost = values(cal_cur_period_cash_cost),
      cal_acc_pre_task_cost = values(cal_acc_pre_task_cost),
      cal_acc_cur_task_cost = values(cal_acc_cur_task_cost),
      cal_group_pre_task_cost = values(cal_group_pre_task_cost),
      cal_group_cur_task_cost = values(cal_group_cur_task_cost),
      cal_group_pre_return_cost = values(cal_group_pre_return_cost),
      cal_group_cur_return_cost = values(cal_group_cur_return_cost),
      cal_acc_cur_return_cost = values(cal_acc_cur_return_cost),
      task_stand_cardinal_number = values(task_stand_cardinal_number),
      return_cal_cardinal_number = values(return_cal_cardinal_number),
      reckon_in_task_stand = values(reckon_in_task_stand),
      reckon_in_return_cal = values(reckon_in_return_cal),
      product_types = values(product_types),
      complete_time = values(complete_time),
      remark = values(remark),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      row = values(row),
      real_cal_group_pre_task_cost = values(real_cal_group_pre_task_cost),
      real_cal_group_cur_task_cost = values(real_cal_group_cur_task_cost),
      last_quarter_task_stand_cardinal_number = values(last_quarter_task_stand_cardinal_number),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentInputDataPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_return_apply_agent_input_data
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="relId != null">
        rel_id,
      </if>
      <if test="returnApplyId != null">
        return_apply_id,
      </if>
      <if test="returnPolicyId != null">
        return_policy_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="taskValue != null">
        task_value,
      </if>
      <if test="pointLevel != null">
        point_level,
      </if>
      <if test="gradeLevel != null">
        grade_level,
      </if>
      <if test="mergeGroup != null">
        merge_group,
      </if>
      <if test="calStatus != null">
        cal_status,
      </if>
      <if test="calLevel != null">
        cal_level,
      </if>
      <if test="calCompleteGear != null">
        cal_complete_gear,
      </if>
      <if test="calReturnRatio != null">
        cal_return_ratio,
      </if>
      <if test="complateRatio != null">
        complate_ratio,
      </if>
      <if test="calReturnAmount != null">
        cal_return_amount,
      </if>
      <if test="calCurPeriodTotalCost != null">
        cal_cur_period_total_cost,
      </if>
      <if test="calCurPeriodCashCost != null">
        cal_cur_period_cash_cost,
      </if>
      <if test="calAccPreTaskCost != null">
        cal_acc_pre_task_cost,
      </if>
      <if test="calAccCurTaskCost != null">
        cal_acc_cur_task_cost,
      </if>
      <if test="calGroupPreTaskCost != null">
        cal_group_pre_task_cost,
      </if>
      <if test="calGroupCurTaskCost != null">
        cal_group_cur_task_cost,
      </if>
      <if test="calGroupPreReturnCost != null">
        cal_group_pre_return_cost,
      </if>
      <if test="calGroupCurReturnCost != null">
        cal_group_cur_return_cost,
      </if>
      <if test="calAccCurReturnCost != null">
        cal_acc_cur_return_cost,
      </if>
      <if test="taskStandCardinalNumber != null">
        task_stand_cardinal_number,
      </if>
      <if test="returnCalCardinalNumber != null">
        return_cal_cardinal_number,
      </if>
      <if test="reckonInTaskStand != null">
        reckon_in_task_stand,
      </if>
      <if test="reckonInReturnCal != null">
        reckon_in_return_cal,
      </if>
      <if test="productTypes != null">
        product_types,
      </if>
      <if test="completeTime != null">
        complete_time,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="row != null">
        row,
      </if>
      <if test="realCalGroupPreTaskCost != null">
        real_cal_group_pre_task_cost,
      </if>
      <if test="realCalGroupCurTaskCost != null">
        real_cal_group_cur_task_cost,
      </if>
      <if test="lastQuarterTaskStandCardinalNumber != null">
        last_quarter_task_stand_cardinal_number,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="relId != null">
        #{relId,jdbcType=INTEGER},
      </if>
      <if test="returnApplyId != null">
        #{returnApplyId,jdbcType=INTEGER},
      </if>
      <if test="returnPolicyId != null">
        #{returnPolicyId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="taskValue != null">
        #{taskValue,jdbcType=BIGINT},
      </if>
      <if test="pointLevel != null">
        #{pointLevel,jdbcType=VARCHAR},
      </if>
      <if test="gradeLevel != null">
        #{gradeLevel,jdbcType=VARCHAR},
      </if>
      <if test="mergeGroup != null">
        #{mergeGroup,jdbcType=VARCHAR},
      </if>
      <if test="calStatus != null">
        #{calStatus,jdbcType=TINYINT},
      </if>
      <if test="calLevel != null">
        #{calLevel,jdbcType=VARCHAR},
      </if>
      <if test="calCompleteGear != null">
        #{calCompleteGear,jdbcType=VARCHAR},
      </if>
      <if test="calReturnRatio != null">
        #{calReturnRatio,jdbcType=DECIMAL},
      </if>
      <if test="complateRatio != null">
        #{complateRatio,jdbcType=DECIMAL},
      </if>
      <if test="calReturnAmount != null">
        #{calReturnAmount,jdbcType=BIGINT},
      </if>
      <if test="calCurPeriodTotalCost != null">
        #{calCurPeriodTotalCost,jdbcType=BIGINT},
      </if>
      <if test="calCurPeriodCashCost != null">
        #{calCurPeriodCashCost,jdbcType=BIGINT},
      </if>
      <if test="calAccPreTaskCost != null">
        #{calAccPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calAccCurTaskCost != null">
        #{calAccCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupPreTaskCost != null">
        #{calGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupCurTaskCost != null">
        #{calGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupPreReturnCost != null">
        #{calGroupPreReturnCost,jdbcType=BIGINT},
      </if>
      <if test="calGroupCurReturnCost != null">
        #{calGroupCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="calAccCurReturnCost != null">
        #{calAccCurReturnCost,jdbcType=BIGINT},
      </if>
      <if test="taskStandCardinalNumber != null">
        #{taskStandCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="returnCalCardinalNumber != null">
        #{returnCalCardinalNumber,jdbcType=BIGINT},
      </if>
      <if test="reckonInTaskStand != null">
        #{reckonInTaskStand,jdbcType=TINYINT},
      </if>
      <if test="reckonInReturnCal != null">
        #{reckonInReturnCal,jdbcType=TINYINT},
      </if>
      <if test="productTypes != null">
        #{productTypes,jdbcType=VARCHAR},
      </if>
      <if test="completeTime != null">
        #{completeTime,jdbcType=TIMESTAMP},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="row != null">
        #{row,jdbcType=INTEGER},
      </if>
      <if test="realCalGroupPreTaskCost != null">
        #{realCalGroupPreTaskCost,jdbcType=BIGINT},
      </if>
      <if test="realCalGroupCurTaskCost != null">
        #{realCalGroupCurTaskCost,jdbcType=BIGINT},
      </if>
      <if test="lastQuarterTaskStandCardinalNumber != null">
        #{lastQuarterTaskStandCardinalNumber,jdbcType=BIGINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="relId != null">
        rel_id = values(rel_id),
      </if>
      <if test="returnApplyId != null">
        return_apply_id = values(return_apply_id),
      </if>
      <if test="returnPolicyId != null">
        return_policy_id = values(return_policy_id),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="taskValue != null">
        task_value = values(task_value),
      </if>
      <if test="pointLevel != null">
        point_level = values(point_level),
      </if>
      <if test="gradeLevel != null">
        grade_level = values(grade_level),
      </if>
      <if test="mergeGroup != null">
        merge_group = values(merge_group),
      </if>
      <if test="calStatus != null">
        cal_status = values(cal_status),
      </if>
      <if test="calLevel != null">
        cal_level = values(cal_level),
      </if>
      <if test="calCompleteGear != null">
        cal_complete_gear = values(cal_complete_gear),
      </if>
      <if test="calReturnRatio != null">
        cal_return_ratio = values(cal_return_ratio),
      </if>
      <if test="complateRatio != null">
        complate_ratio = values(complate_ratio),
      </if>
      <if test="calReturnAmount != null">
        cal_return_amount = values(cal_return_amount),
      </if>
      <if test="calCurPeriodTotalCost != null">
        cal_cur_period_total_cost = values(cal_cur_period_total_cost),
      </if>
      <if test="calCurPeriodCashCost != null">
        cal_cur_period_cash_cost = values(cal_cur_period_cash_cost),
      </if>
      <if test="calAccPreTaskCost != null">
        cal_acc_pre_task_cost = values(cal_acc_pre_task_cost),
      </if>
      <if test="calAccCurTaskCost != null">
        cal_acc_cur_task_cost = values(cal_acc_cur_task_cost),
      </if>
      <if test="calGroupPreTaskCost != null">
        cal_group_pre_task_cost = values(cal_group_pre_task_cost),
      </if>
      <if test="calGroupCurTaskCost != null">
        cal_group_cur_task_cost = values(cal_group_cur_task_cost),
      </if>
      <if test="calGroupPreReturnCost != null">
        cal_group_pre_return_cost = values(cal_group_pre_return_cost),
      </if>
      <if test="calGroupCurReturnCost != null">
        cal_group_cur_return_cost = values(cal_group_cur_return_cost),
      </if>
      <if test="calAccCurReturnCost != null">
        cal_acc_cur_return_cost = values(cal_acc_cur_return_cost),
      </if>
      <if test="taskStandCardinalNumber != null">
        task_stand_cardinal_number = values(task_stand_cardinal_number),
      </if>
      <if test="returnCalCardinalNumber != null">
        return_cal_cardinal_number = values(return_cal_cardinal_number),
      </if>
      <if test="reckonInTaskStand != null">
        reckon_in_task_stand = values(reckon_in_task_stand),
      </if>
      <if test="reckonInReturnCal != null">
        reckon_in_return_cal = values(reckon_in_return_cal),
      </if>
      <if test="productTypes != null">
        product_types = values(product_types),
      </if>
      <if test="completeTime != null">
        complete_time = values(complete_time),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="row != null">
        row = values(row),
      </if>
      <if test="realCalGroupPreTaskCost != null">
        real_cal_group_pre_task_cost = values(real_cal_group_pre_task_cost),
      </if>
      <if test="realCalGroupCurTaskCost != null">
        real_cal_group_cur_task_cost = values(real_cal_group_cur_task_cost),
      </if>
      <if test="lastQuarterTaskStandCardinalNumber != null">
        last_quarter_task_stand_cardinal_number = values(last_quarter_task_stand_cardinal_number),
      </if>
    </trim>
  </insert>
</mapper>