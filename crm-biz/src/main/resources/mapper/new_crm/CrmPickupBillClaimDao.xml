<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmPickupBillClaimDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="pickup_bill_id" jdbcType="BIGINT" property="pickupBillId" />
    <result column="revenue_flow_id" jdbcType="INTEGER" property="revenueFlowId" />
    <result column="claim_amount" jdbcType="BIGINT" property="claimAmount" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    <id column="crm_pickup_bill_claim_id" jdbcType="BIGINT" property="id" />
    <result column="crm_pickup_bill_claim_pickup_bill_id" jdbcType="BIGINT" property="pickupBillId" />
    <result column="crm_pickup_bill_claim_revenue_flow_id" jdbcType="INTEGER" property="revenueFlowId" />
    <result column="crm_pickup_bill_claim_claim_amount" jdbcType="BIGINT" property="claimAmount" />
    <result column="crm_pickup_bill_claim_operator" jdbcType="VARCHAR" property="operator" />
    <result column="crm_pickup_bill_claim_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_pickup_bill_claim_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_pickup_bill_claim_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_pickup_bill_claim_id, ${alias}.pickup_bill_id as crm_pickup_bill_claim_pickup_bill_id, 
    ${alias}.revenue_flow_id as crm_pickup_bill_claim_revenue_flow_id, ${alias}.claim_amount as crm_pickup_bill_claim_claim_amount, 
    ${alias}.operator as crm_pickup_bill_claim_operator, ${alias}.is_deleted as crm_pickup_bill_claim_is_deleted, 
    ${alias}.ctime as crm_pickup_bill_claim_ctime, ${alias}.mtime as crm_pickup_bill_claim_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, pickup_bill_id, revenue_flow_id, claim_amount, operator, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_pickup_bill_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_pickup_bill_claim
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_pickup_bill_claim
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPoExample">
    delete from crm_pickup_bill_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_pickup_bill_claim (pickup_bill_id, revenue_flow_id, claim_amount, 
      operator, is_deleted, ctime, 
      mtime)
    values (#{pickupBillId,jdbcType=BIGINT}, #{revenueFlowId,jdbcType=INTEGER}, #{claimAmount,jdbcType=BIGINT}, 
      #{operator,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_pickup_bill_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pickupBillId != null">
        pickup_bill_id,
      </if>
      <if test="revenueFlowId != null">
        revenue_flow_id,
      </if>
      <if test="claimAmount != null">
        claim_amount,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pickupBillId != null">
        #{pickupBillId,jdbcType=BIGINT},
      </if>
      <if test="revenueFlowId != null">
        #{revenueFlowId,jdbcType=INTEGER},
      </if>
      <if test="claimAmount != null">
        #{claimAmount,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPoExample" resultType="java.lang.Long">
    select count(*) from crm_pickup_bill_claim
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_pickup_bill_claim
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.pickupBillId != null">
        pickup_bill_id = #{record.pickupBillId,jdbcType=BIGINT},
      </if>
      <if test="record.revenueFlowId != null">
        revenue_flow_id = #{record.revenueFlowId,jdbcType=INTEGER},
      </if>
      <if test="record.claimAmount != null">
        claim_amount = #{record.claimAmount,jdbcType=BIGINT},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_pickup_bill_claim
    set id = #{record.id,jdbcType=BIGINT},
      pickup_bill_id = #{record.pickupBillId,jdbcType=BIGINT},
      revenue_flow_id = #{record.revenueFlowId,jdbcType=INTEGER},
      claim_amount = #{record.claimAmount,jdbcType=BIGINT},
      operator = #{record.operator,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    update crm_pickup_bill_claim
    <set>
      <if test="pickupBillId != null">
        pickup_bill_id = #{pickupBillId,jdbcType=BIGINT},
      </if>
      <if test="revenueFlowId != null">
        revenue_flow_id = #{revenueFlowId,jdbcType=INTEGER},
      </if>
      <if test="claimAmount != null">
        claim_amount = #{claimAmount,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    update crm_pickup_bill_claim
    set pickup_bill_id = #{pickupBillId,jdbcType=BIGINT},
      revenue_flow_id = #{revenueFlowId,jdbcType=INTEGER},
      claim_amount = #{claimAmount,jdbcType=BIGINT},
      operator = #{operator,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_pickup_bill_claim (pickup_bill_id, revenue_flow_id, claim_amount, 
      operator, is_deleted, ctime, 
      mtime)
    values (#{pickupBillId,jdbcType=BIGINT}, #{revenueFlowId,jdbcType=INTEGER}, #{claimAmount,jdbcType=BIGINT}, 
      #{operator,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      id = values(id),
      pickup_bill_id = values(pickup_bill_id),
      revenue_flow_id = values(revenue_flow_id),
      claim_amount = values(claim_amount),
      operator = values(operator),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_pickup_bill_claim
      (id,pickup_bill_id,revenue_flow_id,claim_amount,operator,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.pickupBillId,jdbcType=BIGINT},
        #{item.revenueFlowId,jdbcType=INTEGER},
        #{item.claimAmount,jdbcType=BIGINT},
        #{item.operator,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_pickup_bill_claim
      (id,pickup_bill_id,revenue_flow_id,claim_amount,operator,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.id,jdbcType=BIGINT},
        #{item.pickupBillId,jdbcType=BIGINT},
        #{item.revenueFlowId,jdbcType=INTEGER},
        #{item.claimAmount,jdbcType=BIGINT},
        #{item.operator,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      id = values(id),
      pickup_bill_id = values(pickup_bill_id),
      revenue_flow_id = values(revenue_flow_id),
      claim_amount = values(claim_amount),
      operator = values(operator),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmPickupBillClaimPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_pickup_bill_claim
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="pickupBillId != null">
        pickup_bill_id,
      </if>
      <if test="revenueFlowId != null">
        revenue_flow_id,
      </if>
      <if test="claimAmount != null">
        claim_amount,
      </if>
      <if test="operator != null">
        operator,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="pickupBillId != null">
        #{pickupBillId,jdbcType=BIGINT},
      </if>
      <if test="revenueFlowId != null">
        #{revenueFlowId,jdbcType=INTEGER},
      </if>
      <if test="claimAmount != null">
        #{claimAmount,jdbcType=BIGINT},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="id != null">
        id = values(id),
      </if>
      <if test="pickupBillId != null">
        pickup_bill_id = values(pickup_bill_id),
      </if>
      <if test="revenueFlowId != null">
        revenue_flow_id = values(revenue_flow_id),
      </if>
      <if test="claimAmount != null">
        claim_amount = values(claim_amount),
      </if>
      <if test="operator != null">
        operator = values(operator),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>