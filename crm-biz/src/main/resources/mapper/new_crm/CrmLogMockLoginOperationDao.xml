<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmLogMockLoginOperationDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="mock_op_time" jdbcType="TIMESTAMP" property="mockOpTime" />
    <result column="mock_op_url" jdbcType="VARCHAR" property="mockOpUrl" />
    <result column="mock_op_param" jdbcType="VARCHAR" property="mockOpParam" />
    <result column="mock_username" jdbcType="VARCHAR" property="mockUsername" />
    <result column="operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    <id column="crm_log_mock_login_operation_id" jdbcType="BIGINT" property="id" />
    <result column="crm_log_mock_login_operation_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_log_mock_login_operation_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_log_mock_login_operation_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="crm_log_mock_login_operation_mock_op_time" jdbcType="TIMESTAMP" property="mockOpTime" />
    <result column="crm_log_mock_login_operation_mock_op_url" jdbcType="VARCHAR" property="mockOpUrl" />
    <result column="crm_log_mock_login_operation_mock_op_param" jdbcType="VARCHAR" property="mockOpParam" />
    <result column="crm_log_mock_login_operation_mock_username" jdbcType="VARCHAR" property="mockUsername" />
    <result column="crm_log_mock_login_operation_operator" jdbcType="VARCHAR" property="operator" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_log_mock_login_operation_id, ${alias}.is_deleted as crm_log_mock_login_operation_is_deleted, 
    ${alias}.ctime as crm_log_mock_login_operation_ctime, ${alias}.mtime as crm_log_mock_login_operation_mtime, 
    ${alias}.mock_op_time as crm_log_mock_login_operation_mock_op_time, ${alias}.mock_op_url as crm_log_mock_login_operation_mock_op_url, 
    ${alias}.mock_op_param as crm_log_mock_login_operation_mock_op_param, ${alias}.mock_username as crm_log_mock_login_operation_mock_username, 
    ${alias}.operator as crm_log_mock_login_operation_operator
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, is_deleted, ctime, mtime, mock_op_time, mock_op_url, mock_op_param, mock_username, 
    operator
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_log_mock_login_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_log_mock_login_operation
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_log_mock_login_operation
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPoExample">
    delete from crm_log_mock_login_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_log_mock_login_operation (is_deleted, ctime, mtime, 
      mock_op_time, mock_op_url, mock_op_param, 
      mock_username, operator)
    values (#{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{mockOpTime,jdbcType=TIMESTAMP}, #{mockOpUrl,jdbcType=VARCHAR}, #{mockOpParam,jdbcType=VARCHAR}, 
      #{mockUsername,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_log_mock_login_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="mockOpTime != null">
        mock_op_time,
      </if>
      <if test="mockOpUrl != null">
        mock_op_url,
      </if>
      <if test="mockOpParam != null">
        mock_op_param,
      </if>
      <if test="mockUsername != null">
        mock_username,
      </if>
      <if test="operator != null">
        operator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mockOpTime != null">
        #{mockOpTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mockOpUrl != null">
        #{mockOpUrl,jdbcType=VARCHAR},
      </if>
      <if test="mockOpParam != null">
        #{mockOpParam,jdbcType=VARCHAR},
      </if>
      <if test="mockUsername != null">
        #{mockUsername,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPoExample" resultType="java.lang.Long">
    select count(*) from crm_log_mock_login_operation
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_log_mock_login_operation
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mockOpTime != null">
        mock_op_time = #{record.mockOpTime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mockOpUrl != null">
        mock_op_url = #{record.mockOpUrl,jdbcType=VARCHAR},
      </if>
      <if test="record.mockOpParam != null">
        mock_op_param = #{record.mockOpParam,jdbcType=VARCHAR},
      </if>
      <if test="record.mockUsername != null">
        mock_username = #{record.mockUsername,jdbcType=VARCHAR},
      </if>
      <if test="record.operator != null">
        operator = #{record.operator,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_log_mock_login_operation
    set id = #{record.id,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      mock_op_time = #{record.mockOpTime,jdbcType=TIMESTAMP},
      mock_op_url = #{record.mockOpUrl,jdbcType=VARCHAR},
      mock_op_param = #{record.mockOpParam,jdbcType=VARCHAR},
      mock_username = #{record.mockUsername,jdbcType=VARCHAR},
      operator = #{record.operator,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    update crm_log_mock_login_operation
    <set>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mockOpTime != null">
        mock_op_time = #{mockOpTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mockOpUrl != null">
        mock_op_url = #{mockOpUrl,jdbcType=VARCHAR},
      </if>
      <if test="mockOpParam != null">
        mock_op_param = #{mockOpParam,jdbcType=VARCHAR},
      </if>
      <if test="mockUsername != null">
        mock_username = #{mockUsername,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        operator = #{operator,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    update crm_log_mock_login_operation
    set is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      mock_op_time = #{mockOpTime,jdbcType=TIMESTAMP},
      mock_op_url = #{mockOpUrl,jdbcType=VARCHAR},
      mock_op_param = #{mockOpParam,jdbcType=VARCHAR},
      mock_username = #{mockUsername,jdbcType=VARCHAR},
      operator = #{operator,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_log_mock_login_operation (is_deleted, ctime, mtime, 
      mock_op_time, mock_op_url, mock_op_param, 
      mock_username, operator)
    values (#{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{mockOpTime,jdbcType=TIMESTAMP}, #{mockOpUrl,jdbcType=VARCHAR}, #{mockOpParam,jdbcType=VARCHAR}, 
      #{mockUsername,jdbcType=VARCHAR}, #{operator,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      mock_op_time = values(mock_op_time),
      mock_op_url = values(mock_op_url),
      mock_op_param = values(mock_op_param),
      mock_username = values(mock_username),
      operator = values(operator),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_log_mock_login_operation
      (is_deleted,ctime,mtime,mock_op_time,mock_op_url,mock_op_param,mock_username,operator)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.mockOpTime,jdbcType=TIMESTAMP},
        #{item.mockOpUrl,jdbcType=VARCHAR},
        #{item.mockOpParam,jdbcType=VARCHAR},
        #{item.mockUsername,jdbcType=VARCHAR},
        #{item.operator,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_log_mock_login_operation
      (is_deleted,ctime,mtime,mock_op_time,mock_op_url,mock_op_param,mock_username,operator)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.mockOpTime,jdbcType=TIMESTAMP},
        #{item.mockOpUrl,jdbcType=VARCHAR},
        #{item.mockOpParam,jdbcType=VARCHAR},
        #{item.mockUsername,jdbcType=VARCHAR},
        #{item.operator,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      mock_op_time = values(mock_op_time),
      mock_op_url = values(mock_op_url),
      mock_op_param = values(mock_op_param),
      mock_username = values(mock_username),
      operator = values(operator),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmLogMockLoginOperationPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_log_mock_login_operation
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="mockOpTime != null">
        mock_op_time,
      </if>
      <if test="mockOpUrl != null">
        mock_op_url,
      </if>
      <if test="mockOpParam != null">
        mock_op_param,
      </if>
      <if test="mockUsername != null">
        mock_username,
      </if>
      <if test="operator != null">
        operator,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="mockOpTime != null">
        #{mockOpTime,jdbcType=TIMESTAMP},
      </if>
      <if test="mockOpUrl != null">
        #{mockOpUrl,jdbcType=VARCHAR},
      </if>
      <if test="mockOpParam != null">
        #{mockOpParam,jdbcType=VARCHAR},
      </if>
      <if test="mockUsername != null">
        #{mockUsername,jdbcType=VARCHAR},
      </if>
      <if test="operator != null">
        #{operator,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="mockOpTime != null">
        mock_op_time = values(mock_op_time),
      </if>
      <if test="mockOpUrl != null">
        mock_op_url = values(mock_op_url),
      </if>
      <if test="mockOpParam != null">
        mock_op_param = values(mock_op_param),
      </if>
      <if test="mockUsername != null">
        mock_username = values(mock_username),
      </if>
      <if test="operator != null">
        operator = values(operator),
      </if>
    </trim>
  </insert>
</mapper>