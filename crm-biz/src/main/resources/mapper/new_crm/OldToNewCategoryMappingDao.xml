<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.OldToNewCategoryMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="old_category_second_id" jdbcType="INTEGER" property="oldCategorySecondId" />
    <result column="new_category_second_id" jdbcType="INTEGER" property="newCategorySecondId" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, old_category_second_id, new_category_second_id, is_deleted, ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from old_to_new_category_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from old_to_new_category_mapping
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from old_to_new_category_mapping
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPoExample">
    delete from old_to_new_category_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into old_to_new_category_mapping (old_category_second_id, new_category_second_id, 
      is_deleted, ctime, mtime
      )
    values (#{oldCategorySecondId,jdbcType=INTEGER}, #{newCategorySecondId,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into old_to_new_category_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="oldCategorySecondId != null">
        old_category_second_id,
      </if>
      <if test="newCategorySecondId != null">
        new_category_second_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="oldCategorySecondId != null">
        #{oldCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="newCategorySecondId != null">
        #{newCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPoExample" resultType="java.lang.Long">
    select count(*) from old_to_new_category_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update old_to_new_category_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.oldCategorySecondId != null">
        old_category_second_id = #{record.oldCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="record.newCategorySecondId != null">
        new_category_second_id = #{record.newCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update old_to_new_category_mapping
    set id = #{record.id,jdbcType=INTEGER},
      old_category_second_id = #{record.oldCategorySecondId,jdbcType=INTEGER},
      new_category_second_id = #{record.newCategorySecondId,jdbcType=INTEGER},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    update old_to_new_category_mapping
    <set>
      <if test="oldCategorySecondId != null">
        old_category_second_id = #{oldCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="newCategorySecondId != null">
        new_category_second_id = #{newCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    update old_to_new_category_mapping
    set old_category_second_id = #{oldCategorySecondId,jdbcType=INTEGER},
      new_category_second_id = #{newCategorySecondId,jdbcType=INTEGER},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into old_to_new_category_mapping (old_category_second_id, new_category_second_id, 
      is_deleted, ctime, mtime
      )
    values (#{oldCategorySecondId,jdbcType=INTEGER}, #{newCategorySecondId,jdbcType=INTEGER}, 
      #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      old_category_second_id = values(old_category_second_id),
      new_category_second_id = values(new_category_second_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      old_to_new_category_mapping
      (old_category_second_id,new_category_second_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.oldCategorySecondId,jdbcType=INTEGER},
        #{item.newCategorySecondId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      old_to_new_category_mapping
      (old_category_second_id,new_category_second_id,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.oldCategorySecondId,jdbcType=INTEGER},
        #{item.newCategorySecondId,jdbcType=INTEGER},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      old_category_second_id = values(old_category_second_id),
      new_category_second_id = values(new_category_second_id),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.OldToNewCategoryMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into old_to_new_category_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="oldCategorySecondId != null">
        old_category_second_id,
      </if>
      <if test="newCategorySecondId != null">
        new_category_second_id,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="oldCategorySecondId != null">
        #{oldCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="newCategorySecondId != null">
        #{newCategorySecondId,jdbcType=INTEGER},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="oldCategorySecondId != null">
        old_category_second_id = values(old_category_second_id),
      </if>
      <if test="newCategorySecondId != null">
        new_category_second_id = values(new_category_second_id),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>