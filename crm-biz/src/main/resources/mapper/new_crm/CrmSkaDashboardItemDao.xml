<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmSkaDashboardItemDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sale_name" jdbcType="VARCHAR" property="saleName" />
    <result column="sale_id" jdbcType="INTEGER" property="saleId" />
    <result column="sale_group_id" jdbcType="INTEGER" property="saleGroupId" />
    <result column="ska_sub_tag" jdbcType="VARCHAR" property="skaSubTag" />
    <result column="ska_sub_tag_source" jdbcType="VARCHAR" property="skaSubTagSource" />
    <result column="ska_group_Id" jdbcType="INTEGER" property="skaGroupId" />
    <result column="ska_group_name" jdbcType="VARCHAR" property="skaGroupName" />
    <result column="ska_complete_money" jdbcType="BIGINT" property="skaCompleteMoney" />
    <result column="ska_brand_complete_money" jdbcType="BIGINT" property="skaBrandCompleteMoney" />
    <result column="ska_rtb_complete_money" jdbcType="BIGINT" property="skaRtbCompleteMoney" />
    <result column="ska_pickup_complete_money" jdbcType="BIGINT" property="skaPickupCompleteMoney" />
    <result column="query_date" jdbcType="VARCHAR" property="queryDate" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ska_before_complete_money" jdbcType="BIGINT" property="skaBeforeCompleteMoney" />
    <result column="group_profile_flag" jdbcType="INTEGER" property="groupProfileFlag" />
    <result column="group_contacts_flag" jdbcType="INTEGER" property="groupContactsFlag" />
    <result column="group_bsi_count" jdbcType="INTEGER" property="groupBsiCount" />
    <result column="group_visit_count" jdbcType="INTEGER" property="groupVisitCount" />
    <result column="group_sign_process" jdbcType="VARCHAR" property="groupSignProcess" />
    <result column="group_year_target" jdbcType="BIGINT" property="groupYearTarget" />
    <result column="group_sign_money" jdbcType="BIGINT" property="groupSignMoney" />
    <result column="group_sign_complete_money" jdbcType="BIGINT" property="groupSignCompleteMoney" />
    <result column="ska_quarter_target" jdbcType="BIGINT" property="skaQuarterTarget" />
    <result column="data_env" jdbcType="VARCHAR" property="dataEnv" />
    <result column="group_contacts_repeat_flag" jdbcType="INTEGER" property="groupContactsRepeatFlag" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    <id column="crm_ska_dashboard_item_id" jdbcType="BIGINT" property="id" />
    <result column="crm_ska_dashboard_item_sale_name" jdbcType="VARCHAR" property="saleName" />
    <result column="crm_ska_dashboard_item_sale_id" jdbcType="INTEGER" property="saleId" />
    <result column="crm_ska_dashboard_item_sale_group_id" jdbcType="INTEGER" property="saleGroupId" />
    <result column="crm_ska_dashboard_item_ska_sub_tag" jdbcType="VARCHAR" property="skaSubTag" />
    <result column="crm_ska_dashboard_item_ska_sub_tag_source" jdbcType="VARCHAR" property="skaSubTagSource" />
    <result column="crm_ska_dashboard_item_ska_group_Id" jdbcType="INTEGER" property="skaGroupId" />
    <result column="crm_ska_dashboard_item_ska_group_name" jdbcType="VARCHAR" property="skaGroupName" />
    <result column="crm_ska_dashboard_item_ska_complete_money" jdbcType="BIGINT" property="skaCompleteMoney" />
    <result column="crm_ska_dashboard_item_ska_brand_complete_money" jdbcType="BIGINT" property="skaBrandCompleteMoney" />
    <result column="crm_ska_dashboard_item_ska_rtb_complete_money" jdbcType="BIGINT" property="skaRtbCompleteMoney" />
    <result column="crm_ska_dashboard_item_ska_pickup_complete_money" jdbcType="BIGINT" property="skaPickupCompleteMoney" />
    <result column="crm_ska_dashboard_item_query_date" jdbcType="VARCHAR" property="queryDate" />
    <result column="crm_ska_dashboard_item_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_ska_dashboard_item_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_ska_dashboard_item_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="crm_ska_dashboard_item_ska_before_complete_money" jdbcType="BIGINT" property="skaBeforeCompleteMoney" />
    <result column="crm_ska_dashboard_item_group_profile_flag" jdbcType="INTEGER" property="groupProfileFlag" />
    <result column="crm_ska_dashboard_item_group_contacts_flag" jdbcType="INTEGER" property="groupContactsFlag" />
    <result column="crm_ska_dashboard_item_group_bsi_count" jdbcType="INTEGER" property="groupBsiCount" />
    <result column="crm_ska_dashboard_item_group_visit_count" jdbcType="INTEGER" property="groupVisitCount" />
    <result column="crm_ska_dashboard_item_group_sign_process" jdbcType="VARCHAR" property="groupSignProcess" />
    <result column="crm_ska_dashboard_item_group_year_target" jdbcType="BIGINT" property="groupYearTarget" />
    <result column="crm_ska_dashboard_item_group_sign_money" jdbcType="BIGINT" property="groupSignMoney" />
    <result column="crm_ska_dashboard_item_group_sign_complete_money" jdbcType="BIGINT" property="groupSignCompleteMoney" />
    <result column="crm_ska_dashboard_item_ska_quarter_target" jdbcType="BIGINT" property="skaQuarterTarget" />
    <result column="crm_ska_dashboard_item_data_env" jdbcType="VARCHAR" property="dataEnv" />
    <result column="crm_ska_dashboard_item_group_contacts_repeat_flag" jdbcType="INTEGER" property="groupContactsRepeatFlag" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_ska_dashboard_item_id, ${alias}.sale_name as crm_ska_dashboard_item_sale_name, 
    ${alias}.sale_id as crm_ska_dashboard_item_sale_id, ${alias}.sale_group_id as crm_ska_dashboard_item_sale_group_id, 
    ${alias}.ska_sub_tag as crm_ska_dashboard_item_ska_sub_tag, ${alias}.ska_sub_tag_source as crm_ska_dashboard_item_ska_sub_tag_source, 
    ${alias}.ska_group_Id as crm_ska_dashboard_item_ska_group_Id, ${alias}.ska_group_name as crm_ska_dashboard_item_ska_group_name, 
    ${alias}.ska_complete_money as crm_ska_dashboard_item_ska_complete_money, ${alias}.ska_brand_complete_money as crm_ska_dashboard_item_ska_brand_complete_money, 
    ${alias}.ska_rtb_complete_money as crm_ska_dashboard_item_ska_rtb_complete_money, 
    ${alias}.ska_pickup_complete_money as crm_ska_dashboard_item_ska_pickup_complete_money, 
    ${alias}.query_date as crm_ska_dashboard_item_query_date, ${alias}.is_deleted as crm_ska_dashboard_item_is_deleted, 
    ${alias}.ctime as crm_ska_dashboard_item_ctime, ${alias}.mtime as crm_ska_dashboard_item_mtime, 
    ${alias}.ska_before_complete_money as crm_ska_dashboard_item_ska_before_complete_money, 
    ${alias}.group_profile_flag as crm_ska_dashboard_item_group_profile_flag, ${alias}.group_contacts_flag as crm_ska_dashboard_item_group_contacts_flag, 
    ${alias}.group_bsi_count as crm_ska_dashboard_item_group_bsi_count, ${alias}.group_visit_count as crm_ska_dashboard_item_group_visit_count, 
    ${alias}.group_sign_process as crm_ska_dashboard_item_group_sign_process, ${alias}.group_year_target as crm_ska_dashboard_item_group_year_target, 
    ${alias}.group_sign_money as crm_ska_dashboard_item_group_sign_money, ${alias}.group_sign_complete_money as crm_ska_dashboard_item_group_sign_complete_money, 
    ${alias}.ska_quarter_target as crm_ska_dashboard_item_ska_quarter_target, ${alias}.data_env as crm_ska_dashboard_item_data_env, 
    ${alias}.group_contacts_repeat_flag as crm_ska_dashboard_item_group_contacts_repeat_flag
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sale_name, sale_id, sale_group_id, ska_sub_tag, ska_sub_tag_source, ska_group_Id, 
    ska_group_name, ska_complete_money, ska_brand_complete_money, ska_rtb_complete_money, 
    ska_pickup_complete_money, query_date, is_deleted, ctime, mtime, ska_before_complete_money, 
    group_profile_flag, group_contacts_flag, group_bsi_count, group_visit_count, group_sign_process, 
    group_year_target, group_sign_money, group_sign_complete_money, ska_quarter_target, 
    data_env, group_contacts_repeat_flag
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_ska_dashboard_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_ska_dashboard_item
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_ska_dashboard_item
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPoExample">
    delete from crm_ska_dashboard_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_ska_dashboard_item (sale_name, sale_id, sale_group_id, 
      ska_sub_tag, ska_sub_tag_source, ska_group_Id, 
      ska_group_name, ska_complete_money, ska_brand_complete_money, 
      ska_rtb_complete_money, ska_pickup_complete_money, 
      query_date, is_deleted, ctime, 
      mtime, ska_before_complete_money, group_profile_flag, 
      group_contacts_flag, group_bsi_count, group_visit_count, 
      group_sign_process, group_year_target, group_sign_money, 
      group_sign_complete_money, ska_quarter_target, 
      data_env, group_contacts_repeat_flag)
    values (#{saleName,jdbcType=VARCHAR}, #{saleId,jdbcType=INTEGER}, #{saleGroupId,jdbcType=INTEGER}, 
      #{skaSubTag,jdbcType=VARCHAR}, #{skaSubTagSource,jdbcType=VARCHAR}, #{skaGroupId,jdbcType=INTEGER}, 
      #{skaGroupName,jdbcType=VARCHAR}, #{skaCompleteMoney,jdbcType=BIGINT}, #{skaBrandCompleteMoney,jdbcType=BIGINT}, 
      #{skaRtbCompleteMoney,jdbcType=BIGINT}, #{skaPickupCompleteMoney,jdbcType=BIGINT}, 
      #{queryDate,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{skaBeforeCompleteMoney,jdbcType=BIGINT}, #{groupProfileFlag,jdbcType=INTEGER}, 
      #{groupContactsFlag,jdbcType=INTEGER}, #{groupBsiCount,jdbcType=INTEGER}, #{groupVisitCount,jdbcType=INTEGER}, 
      #{groupSignProcess,jdbcType=VARCHAR}, #{groupYearTarget,jdbcType=BIGINT}, #{groupSignMoney,jdbcType=BIGINT}, 
      #{groupSignCompleteMoney,jdbcType=BIGINT}, #{skaQuarterTarget,jdbcType=BIGINT}, 
      #{dataEnv,jdbcType=VARCHAR}, #{groupContactsRepeatFlag,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_ska_dashboard_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleName != null">
        sale_name,
      </if>
      <if test="saleId != null">
        sale_id,
      </if>
      <if test="saleGroupId != null">
        sale_group_id,
      </if>
      <if test="skaSubTag != null">
        ska_sub_tag,
      </if>
      <if test="skaSubTagSource != null">
        ska_sub_tag_source,
      </if>
      <if test="skaGroupId != null">
        ska_group_Id,
      </if>
      <if test="skaGroupName != null">
        ska_group_name,
      </if>
      <if test="skaCompleteMoney != null">
        ska_complete_money,
      </if>
      <if test="skaBrandCompleteMoney != null">
        ska_brand_complete_money,
      </if>
      <if test="skaRtbCompleteMoney != null">
        ska_rtb_complete_money,
      </if>
      <if test="skaPickupCompleteMoney != null">
        ska_pickup_complete_money,
      </if>
      <if test="queryDate != null">
        query_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="skaBeforeCompleteMoney != null">
        ska_before_complete_money,
      </if>
      <if test="groupProfileFlag != null">
        group_profile_flag,
      </if>
      <if test="groupContactsFlag != null">
        group_contacts_flag,
      </if>
      <if test="groupBsiCount != null">
        group_bsi_count,
      </if>
      <if test="groupVisitCount != null">
        group_visit_count,
      </if>
      <if test="groupSignProcess != null">
        group_sign_process,
      </if>
      <if test="groupYearTarget != null">
        group_year_target,
      </if>
      <if test="groupSignMoney != null">
        group_sign_money,
      </if>
      <if test="groupSignCompleteMoney != null">
        group_sign_complete_money,
      </if>
      <if test="skaQuarterTarget != null">
        ska_quarter_target,
      </if>
      <if test="dataEnv != null">
        data_env,
      </if>
      <if test="groupContactsRepeatFlag != null">
        group_contacts_repeat_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleName != null">
        #{saleName,jdbcType=VARCHAR},
      </if>
      <if test="saleId != null">
        #{saleId,jdbcType=INTEGER},
      </if>
      <if test="saleGroupId != null">
        #{saleGroupId,jdbcType=INTEGER},
      </if>
      <if test="skaSubTag != null">
        #{skaSubTag,jdbcType=VARCHAR},
      </if>
      <if test="skaSubTagSource != null">
        #{skaSubTagSource,jdbcType=VARCHAR},
      </if>
      <if test="skaGroupId != null">
        #{skaGroupId,jdbcType=INTEGER},
      </if>
      <if test="skaGroupName != null">
        #{skaGroupName,jdbcType=VARCHAR},
      </if>
      <if test="skaCompleteMoney != null">
        #{skaCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaBrandCompleteMoney != null">
        #{skaBrandCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaRtbCompleteMoney != null">
        #{skaRtbCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaPickupCompleteMoney != null">
        #{skaPickupCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="queryDate != null">
        #{queryDate,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="skaBeforeCompleteMoney != null">
        #{skaBeforeCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="groupProfileFlag != null">
        #{groupProfileFlag,jdbcType=INTEGER},
      </if>
      <if test="groupContactsFlag != null">
        #{groupContactsFlag,jdbcType=INTEGER},
      </if>
      <if test="groupBsiCount != null">
        #{groupBsiCount,jdbcType=INTEGER},
      </if>
      <if test="groupVisitCount != null">
        #{groupVisitCount,jdbcType=INTEGER},
      </if>
      <if test="groupSignProcess != null">
        #{groupSignProcess,jdbcType=VARCHAR},
      </if>
      <if test="groupYearTarget != null">
        #{groupYearTarget,jdbcType=BIGINT},
      </if>
      <if test="groupSignMoney != null">
        #{groupSignMoney,jdbcType=BIGINT},
      </if>
      <if test="groupSignCompleteMoney != null">
        #{groupSignCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaQuarterTarget != null">
        #{skaQuarterTarget,jdbcType=BIGINT},
      </if>
      <if test="dataEnv != null">
        #{dataEnv,jdbcType=VARCHAR},
      </if>
      <if test="groupContactsRepeatFlag != null">
        #{groupContactsRepeatFlag,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPoExample" resultType="java.lang.Long">
    select count(*) from crm_ska_dashboard_item
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_ska_dashboard_item
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.saleName != null">
        sale_name = #{record.saleName,jdbcType=VARCHAR},
      </if>
      <if test="record.saleId != null">
        sale_id = #{record.saleId,jdbcType=INTEGER},
      </if>
      <if test="record.saleGroupId != null">
        sale_group_id = #{record.saleGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.skaSubTag != null">
        ska_sub_tag = #{record.skaSubTag,jdbcType=VARCHAR},
      </if>
      <if test="record.skaSubTagSource != null">
        ska_sub_tag_source = #{record.skaSubTagSource,jdbcType=VARCHAR},
      </if>
      <if test="record.skaGroupId != null">
        ska_group_Id = #{record.skaGroupId,jdbcType=INTEGER},
      </if>
      <if test="record.skaGroupName != null">
        ska_group_name = #{record.skaGroupName,jdbcType=VARCHAR},
      </if>
      <if test="record.skaCompleteMoney != null">
        ska_complete_money = #{record.skaCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="record.skaBrandCompleteMoney != null">
        ska_brand_complete_money = #{record.skaBrandCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="record.skaRtbCompleteMoney != null">
        ska_rtb_complete_money = #{record.skaRtbCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="record.skaPickupCompleteMoney != null">
        ska_pickup_complete_money = #{record.skaPickupCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="record.queryDate != null">
        query_date = #{record.queryDate,jdbcType=VARCHAR},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.skaBeforeCompleteMoney != null">
        ska_before_complete_money = #{record.skaBeforeCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="record.groupProfileFlag != null">
        group_profile_flag = #{record.groupProfileFlag,jdbcType=INTEGER},
      </if>
      <if test="record.groupContactsFlag != null">
        group_contacts_flag = #{record.groupContactsFlag,jdbcType=INTEGER},
      </if>
      <if test="record.groupBsiCount != null">
        group_bsi_count = #{record.groupBsiCount,jdbcType=INTEGER},
      </if>
      <if test="record.groupVisitCount != null">
        group_visit_count = #{record.groupVisitCount,jdbcType=INTEGER},
      </if>
      <if test="record.groupSignProcess != null">
        group_sign_process = #{record.groupSignProcess,jdbcType=VARCHAR},
      </if>
      <if test="record.groupYearTarget != null">
        group_year_target = #{record.groupYearTarget,jdbcType=BIGINT},
      </if>
      <if test="record.groupSignMoney != null">
        group_sign_money = #{record.groupSignMoney,jdbcType=BIGINT},
      </if>
      <if test="record.groupSignCompleteMoney != null">
        group_sign_complete_money = #{record.groupSignCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="record.skaQuarterTarget != null">
        ska_quarter_target = #{record.skaQuarterTarget,jdbcType=BIGINT},
      </if>
      <if test="record.dataEnv != null">
        data_env = #{record.dataEnv,jdbcType=VARCHAR},
      </if>
      <if test="record.groupContactsRepeatFlag != null">
        group_contacts_repeat_flag = #{record.groupContactsRepeatFlag,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_ska_dashboard_item
    set id = #{record.id,jdbcType=BIGINT},
      sale_name = #{record.saleName,jdbcType=VARCHAR},
      sale_id = #{record.saleId,jdbcType=INTEGER},
      sale_group_id = #{record.saleGroupId,jdbcType=INTEGER},
      ska_sub_tag = #{record.skaSubTag,jdbcType=VARCHAR},
      ska_sub_tag_source = #{record.skaSubTagSource,jdbcType=VARCHAR},
      ska_group_Id = #{record.skaGroupId,jdbcType=INTEGER},
      ska_group_name = #{record.skaGroupName,jdbcType=VARCHAR},
      ska_complete_money = #{record.skaCompleteMoney,jdbcType=BIGINT},
      ska_brand_complete_money = #{record.skaBrandCompleteMoney,jdbcType=BIGINT},
      ska_rtb_complete_money = #{record.skaRtbCompleteMoney,jdbcType=BIGINT},
      ska_pickup_complete_money = #{record.skaPickupCompleteMoney,jdbcType=BIGINT},
      query_date = #{record.queryDate,jdbcType=VARCHAR},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      ska_before_complete_money = #{record.skaBeforeCompleteMoney,jdbcType=BIGINT},
      group_profile_flag = #{record.groupProfileFlag,jdbcType=INTEGER},
      group_contacts_flag = #{record.groupContactsFlag,jdbcType=INTEGER},
      group_bsi_count = #{record.groupBsiCount,jdbcType=INTEGER},
      group_visit_count = #{record.groupVisitCount,jdbcType=INTEGER},
      group_sign_process = #{record.groupSignProcess,jdbcType=VARCHAR},
      group_year_target = #{record.groupYearTarget,jdbcType=BIGINT},
      group_sign_money = #{record.groupSignMoney,jdbcType=BIGINT},
      group_sign_complete_money = #{record.groupSignCompleteMoney,jdbcType=BIGINT},
      ska_quarter_target = #{record.skaQuarterTarget,jdbcType=BIGINT},
      data_env = #{record.dataEnv,jdbcType=VARCHAR},
      group_contacts_repeat_flag = #{record.groupContactsRepeatFlag,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    update crm_ska_dashboard_item
    <set>
      <if test="saleName != null">
        sale_name = #{saleName,jdbcType=VARCHAR},
      </if>
      <if test="saleId != null">
        sale_id = #{saleId,jdbcType=INTEGER},
      </if>
      <if test="saleGroupId != null">
        sale_group_id = #{saleGroupId,jdbcType=INTEGER},
      </if>
      <if test="skaSubTag != null">
        ska_sub_tag = #{skaSubTag,jdbcType=VARCHAR},
      </if>
      <if test="skaSubTagSource != null">
        ska_sub_tag_source = #{skaSubTagSource,jdbcType=VARCHAR},
      </if>
      <if test="skaGroupId != null">
        ska_group_Id = #{skaGroupId,jdbcType=INTEGER},
      </if>
      <if test="skaGroupName != null">
        ska_group_name = #{skaGroupName,jdbcType=VARCHAR},
      </if>
      <if test="skaCompleteMoney != null">
        ska_complete_money = #{skaCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaBrandCompleteMoney != null">
        ska_brand_complete_money = #{skaBrandCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaRtbCompleteMoney != null">
        ska_rtb_complete_money = #{skaRtbCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaPickupCompleteMoney != null">
        ska_pickup_complete_money = #{skaPickupCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="queryDate != null">
        query_date = #{queryDate,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="skaBeforeCompleteMoney != null">
        ska_before_complete_money = #{skaBeforeCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="groupProfileFlag != null">
        group_profile_flag = #{groupProfileFlag,jdbcType=INTEGER},
      </if>
      <if test="groupContactsFlag != null">
        group_contacts_flag = #{groupContactsFlag,jdbcType=INTEGER},
      </if>
      <if test="groupBsiCount != null">
        group_bsi_count = #{groupBsiCount,jdbcType=INTEGER},
      </if>
      <if test="groupVisitCount != null">
        group_visit_count = #{groupVisitCount,jdbcType=INTEGER},
      </if>
      <if test="groupSignProcess != null">
        group_sign_process = #{groupSignProcess,jdbcType=VARCHAR},
      </if>
      <if test="groupYearTarget != null">
        group_year_target = #{groupYearTarget,jdbcType=BIGINT},
      </if>
      <if test="groupSignMoney != null">
        group_sign_money = #{groupSignMoney,jdbcType=BIGINT},
      </if>
      <if test="groupSignCompleteMoney != null">
        group_sign_complete_money = #{groupSignCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaQuarterTarget != null">
        ska_quarter_target = #{skaQuarterTarget,jdbcType=BIGINT},
      </if>
      <if test="dataEnv != null">
        data_env = #{dataEnv,jdbcType=VARCHAR},
      </if>
      <if test="groupContactsRepeatFlag != null">
        group_contacts_repeat_flag = #{groupContactsRepeatFlag,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    update crm_ska_dashboard_item
    set sale_name = #{saleName,jdbcType=VARCHAR},
      sale_id = #{saleId,jdbcType=INTEGER},
      sale_group_id = #{saleGroupId,jdbcType=INTEGER},
      ska_sub_tag = #{skaSubTag,jdbcType=VARCHAR},
      ska_sub_tag_source = #{skaSubTagSource,jdbcType=VARCHAR},
      ska_group_Id = #{skaGroupId,jdbcType=INTEGER},
      ska_group_name = #{skaGroupName,jdbcType=VARCHAR},
      ska_complete_money = #{skaCompleteMoney,jdbcType=BIGINT},
      ska_brand_complete_money = #{skaBrandCompleteMoney,jdbcType=BIGINT},
      ska_rtb_complete_money = #{skaRtbCompleteMoney,jdbcType=BIGINT},
      ska_pickup_complete_money = #{skaPickupCompleteMoney,jdbcType=BIGINT},
      query_date = #{queryDate,jdbcType=VARCHAR},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      ska_before_complete_money = #{skaBeforeCompleteMoney,jdbcType=BIGINT},
      group_profile_flag = #{groupProfileFlag,jdbcType=INTEGER},
      group_contacts_flag = #{groupContactsFlag,jdbcType=INTEGER},
      group_bsi_count = #{groupBsiCount,jdbcType=INTEGER},
      group_visit_count = #{groupVisitCount,jdbcType=INTEGER},
      group_sign_process = #{groupSignProcess,jdbcType=VARCHAR},
      group_year_target = #{groupYearTarget,jdbcType=BIGINT},
      group_sign_money = #{groupSignMoney,jdbcType=BIGINT},
      group_sign_complete_money = #{groupSignCompleteMoney,jdbcType=BIGINT},
      ska_quarter_target = #{skaQuarterTarget,jdbcType=BIGINT},
      data_env = #{dataEnv,jdbcType=VARCHAR},
      group_contacts_repeat_flag = #{groupContactsRepeatFlag,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_ska_dashboard_item (sale_name, sale_id, sale_group_id, 
      ska_sub_tag, ska_sub_tag_source, ska_group_Id, 
      ska_group_name, ska_complete_money, ska_brand_complete_money, 
      ska_rtb_complete_money, ska_pickup_complete_money, 
      query_date, is_deleted, ctime, 
      mtime, ska_before_complete_money, group_profile_flag, 
      group_contacts_flag, group_bsi_count, group_visit_count, 
      group_sign_process, group_year_target, group_sign_money, 
      group_sign_complete_money, ska_quarter_target, 
      data_env, group_contacts_repeat_flag)
    values (#{saleName,jdbcType=VARCHAR}, #{saleId,jdbcType=INTEGER}, #{saleGroupId,jdbcType=INTEGER}, 
      #{skaSubTag,jdbcType=VARCHAR}, #{skaSubTagSource,jdbcType=VARCHAR}, #{skaGroupId,jdbcType=INTEGER}, 
      #{skaGroupName,jdbcType=VARCHAR}, #{skaCompleteMoney,jdbcType=BIGINT}, #{skaBrandCompleteMoney,jdbcType=BIGINT}, 
      #{skaRtbCompleteMoney,jdbcType=BIGINT}, #{skaPickupCompleteMoney,jdbcType=BIGINT}, 
      #{queryDate,jdbcType=VARCHAR}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, 
      #{mtime,jdbcType=TIMESTAMP}, #{skaBeforeCompleteMoney,jdbcType=BIGINT}, #{groupProfileFlag,jdbcType=INTEGER}, 
      #{groupContactsFlag,jdbcType=INTEGER}, #{groupBsiCount,jdbcType=INTEGER}, #{groupVisitCount,jdbcType=INTEGER}, 
      #{groupSignProcess,jdbcType=VARCHAR}, #{groupYearTarget,jdbcType=BIGINT}, #{groupSignMoney,jdbcType=BIGINT}, 
      #{groupSignCompleteMoney,jdbcType=BIGINT}, #{skaQuarterTarget,jdbcType=BIGINT}, 
      #{dataEnv,jdbcType=VARCHAR}, #{groupContactsRepeatFlag,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      sale_name = values(sale_name),
      sale_id = values(sale_id),
      sale_group_id = values(sale_group_id),
      ska_sub_tag = values(ska_sub_tag),
      ska_sub_tag_source = values(ska_sub_tag_source),
      ska_group_Id = values(ska_group_Id),
      ska_group_name = values(ska_group_name),
      ska_complete_money = values(ska_complete_money),
      ska_brand_complete_money = values(ska_brand_complete_money),
      ska_rtb_complete_money = values(ska_rtb_complete_money),
      ska_pickup_complete_money = values(ska_pickup_complete_money),
      query_date = values(query_date),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      ska_before_complete_money = values(ska_before_complete_money),
      group_profile_flag = values(group_profile_flag),
      group_contacts_flag = values(group_contacts_flag),
      group_bsi_count = values(group_bsi_count),
      group_visit_count = values(group_visit_count),
      group_sign_process = values(group_sign_process),
      group_year_target = values(group_year_target),
      group_sign_money = values(group_sign_money),
      group_sign_complete_money = values(group_sign_complete_money),
      ska_quarter_target = values(ska_quarter_target),
      data_env = values(data_env),
      group_contacts_repeat_flag = values(group_contacts_repeat_flag),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_ska_dashboard_item
      (sale_name,sale_id,sale_group_id,ska_sub_tag,ska_sub_tag_source,ska_group_Id,ska_group_name,ska_complete_money,ska_brand_complete_money,ska_rtb_complete_money,ska_pickup_complete_money,query_date,is_deleted,ctime,mtime,ska_before_complete_money,group_profile_flag,group_contacts_flag,group_bsi_count,group_visit_count,group_sign_process,group_year_target,group_sign_money,group_sign_complete_money,ska_quarter_target,data_env,group_contacts_repeat_flag)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.saleName,jdbcType=VARCHAR},
        #{item.saleId,jdbcType=INTEGER},
        #{item.saleGroupId,jdbcType=INTEGER},
        #{item.skaSubTag,jdbcType=VARCHAR},
        #{item.skaSubTagSource,jdbcType=VARCHAR},
        #{item.skaGroupId,jdbcType=INTEGER},
        #{item.skaGroupName,jdbcType=VARCHAR},
        #{item.skaCompleteMoney,jdbcType=BIGINT},
        #{item.skaBrandCompleteMoney,jdbcType=BIGINT},
        #{item.skaRtbCompleteMoney,jdbcType=BIGINT},
        #{item.skaPickupCompleteMoney,jdbcType=BIGINT},
        #{item.queryDate,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.skaBeforeCompleteMoney,jdbcType=BIGINT},
        #{item.groupProfileFlag,jdbcType=INTEGER},
        #{item.groupContactsFlag,jdbcType=INTEGER},
        #{item.groupBsiCount,jdbcType=INTEGER},
        #{item.groupVisitCount,jdbcType=INTEGER},
        #{item.groupSignProcess,jdbcType=VARCHAR},
        #{item.groupYearTarget,jdbcType=BIGINT},
        #{item.groupSignMoney,jdbcType=BIGINT},
        #{item.groupSignCompleteMoney,jdbcType=BIGINT},
        #{item.skaQuarterTarget,jdbcType=BIGINT},
        #{item.dataEnv,jdbcType=VARCHAR},
        #{item.groupContactsRepeatFlag,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_ska_dashboard_item
      (sale_name,sale_id,sale_group_id,ska_sub_tag,ska_sub_tag_source,ska_group_Id,ska_group_name,ska_complete_money,ska_brand_complete_money,ska_rtb_complete_money,ska_pickup_complete_money,query_date,is_deleted,ctime,mtime,ska_before_complete_money,group_profile_flag,group_contacts_flag,group_bsi_count,group_visit_count,group_sign_process,group_year_target,group_sign_money,group_sign_complete_money,ska_quarter_target,data_env,group_contacts_repeat_flag)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.saleName,jdbcType=VARCHAR},
        #{item.saleId,jdbcType=INTEGER},
        #{item.saleGroupId,jdbcType=INTEGER},
        #{item.skaSubTag,jdbcType=VARCHAR},
        #{item.skaSubTagSource,jdbcType=VARCHAR},
        #{item.skaGroupId,jdbcType=INTEGER},
        #{item.skaGroupName,jdbcType=VARCHAR},
        #{item.skaCompleteMoney,jdbcType=BIGINT},
        #{item.skaBrandCompleteMoney,jdbcType=BIGINT},
        #{item.skaRtbCompleteMoney,jdbcType=BIGINT},
        #{item.skaPickupCompleteMoney,jdbcType=BIGINT},
        #{item.queryDate,jdbcType=VARCHAR},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.skaBeforeCompleteMoney,jdbcType=BIGINT},
        #{item.groupProfileFlag,jdbcType=INTEGER},
        #{item.groupContactsFlag,jdbcType=INTEGER},
        #{item.groupBsiCount,jdbcType=INTEGER},
        #{item.groupVisitCount,jdbcType=INTEGER},
        #{item.groupSignProcess,jdbcType=VARCHAR},
        #{item.groupYearTarget,jdbcType=BIGINT},
        #{item.groupSignMoney,jdbcType=BIGINT},
        #{item.groupSignCompleteMoney,jdbcType=BIGINT},
        #{item.skaQuarterTarget,jdbcType=BIGINT},
        #{item.dataEnv,jdbcType=VARCHAR},
        #{item.groupContactsRepeatFlag,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      sale_name = values(sale_name),
      sale_id = values(sale_id),
      sale_group_id = values(sale_group_id),
      ska_sub_tag = values(ska_sub_tag),
      ska_sub_tag_source = values(ska_sub_tag_source),
      ska_group_Id = values(ska_group_Id),
      ska_group_name = values(ska_group_name),
      ska_complete_money = values(ska_complete_money),
      ska_brand_complete_money = values(ska_brand_complete_money),
      ska_rtb_complete_money = values(ska_rtb_complete_money),
      ska_pickup_complete_money = values(ska_pickup_complete_money),
      query_date = values(query_date),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      ska_before_complete_money = values(ska_before_complete_money),
      group_profile_flag = values(group_profile_flag),
      group_contacts_flag = values(group_contacts_flag),
      group_bsi_count = values(group_bsi_count),
      group_visit_count = values(group_visit_count),
      group_sign_process = values(group_sign_process),
      group_year_target = values(group_year_target),
      group_sign_money = values(group_sign_money),
      group_sign_complete_money = values(group_sign_complete_money),
      ska_quarter_target = values(ska_quarter_target),
      data_env = values(data_env),
      group_contacts_repeat_flag = values(group_contacts_repeat_flag),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmSkaDashboardItemPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_ska_dashboard_item
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleName != null">
        sale_name,
      </if>
      <if test="saleId != null">
        sale_id,
      </if>
      <if test="saleGroupId != null">
        sale_group_id,
      </if>
      <if test="skaSubTag != null">
        ska_sub_tag,
      </if>
      <if test="skaSubTagSource != null">
        ska_sub_tag_source,
      </if>
      <if test="skaGroupId != null">
        ska_group_Id,
      </if>
      <if test="skaGroupName != null">
        ska_group_name,
      </if>
      <if test="skaCompleteMoney != null">
        ska_complete_money,
      </if>
      <if test="skaBrandCompleteMoney != null">
        ska_brand_complete_money,
      </if>
      <if test="skaRtbCompleteMoney != null">
        ska_rtb_complete_money,
      </if>
      <if test="skaPickupCompleteMoney != null">
        ska_pickup_complete_money,
      </if>
      <if test="queryDate != null">
        query_date,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="skaBeforeCompleteMoney != null">
        ska_before_complete_money,
      </if>
      <if test="groupProfileFlag != null">
        group_profile_flag,
      </if>
      <if test="groupContactsFlag != null">
        group_contacts_flag,
      </if>
      <if test="groupBsiCount != null">
        group_bsi_count,
      </if>
      <if test="groupVisitCount != null">
        group_visit_count,
      </if>
      <if test="groupSignProcess != null">
        group_sign_process,
      </if>
      <if test="groupYearTarget != null">
        group_year_target,
      </if>
      <if test="groupSignMoney != null">
        group_sign_money,
      </if>
      <if test="groupSignCompleteMoney != null">
        group_sign_complete_money,
      </if>
      <if test="skaQuarterTarget != null">
        ska_quarter_target,
      </if>
      <if test="dataEnv != null">
        data_env,
      </if>
      <if test="groupContactsRepeatFlag != null">
        group_contacts_repeat_flag,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleName != null">
        #{saleName,jdbcType=VARCHAR},
      </if>
      <if test="saleId != null">
        #{saleId,jdbcType=INTEGER},
      </if>
      <if test="saleGroupId != null">
        #{saleGroupId,jdbcType=INTEGER},
      </if>
      <if test="skaSubTag != null">
        #{skaSubTag,jdbcType=VARCHAR},
      </if>
      <if test="skaSubTagSource != null">
        #{skaSubTagSource,jdbcType=VARCHAR},
      </if>
      <if test="skaGroupId != null">
        #{skaGroupId,jdbcType=INTEGER},
      </if>
      <if test="skaGroupName != null">
        #{skaGroupName,jdbcType=VARCHAR},
      </if>
      <if test="skaCompleteMoney != null">
        #{skaCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaBrandCompleteMoney != null">
        #{skaBrandCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaRtbCompleteMoney != null">
        #{skaRtbCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaPickupCompleteMoney != null">
        #{skaPickupCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="queryDate != null">
        #{queryDate,jdbcType=VARCHAR},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="skaBeforeCompleteMoney != null">
        #{skaBeforeCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="groupProfileFlag != null">
        #{groupProfileFlag,jdbcType=INTEGER},
      </if>
      <if test="groupContactsFlag != null">
        #{groupContactsFlag,jdbcType=INTEGER},
      </if>
      <if test="groupBsiCount != null">
        #{groupBsiCount,jdbcType=INTEGER},
      </if>
      <if test="groupVisitCount != null">
        #{groupVisitCount,jdbcType=INTEGER},
      </if>
      <if test="groupSignProcess != null">
        #{groupSignProcess,jdbcType=VARCHAR},
      </if>
      <if test="groupYearTarget != null">
        #{groupYearTarget,jdbcType=BIGINT},
      </if>
      <if test="groupSignMoney != null">
        #{groupSignMoney,jdbcType=BIGINT},
      </if>
      <if test="groupSignCompleteMoney != null">
        #{groupSignCompleteMoney,jdbcType=BIGINT},
      </if>
      <if test="skaQuarterTarget != null">
        #{skaQuarterTarget,jdbcType=BIGINT},
      </if>
      <if test="dataEnv != null">
        #{dataEnv,jdbcType=VARCHAR},
      </if>
      <if test="groupContactsRepeatFlag != null">
        #{groupContactsRepeatFlag,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="saleName != null">
        sale_name = values(sale_name),
      </if>
      <if test="saleId != null">
        sale_id = values(sale_id),
      </if>
      <if test="saleGroupId != null">
        sale_group_id = values(sale_group_id),
      </if>
      <if test="skaSubTag != null">
        ska_sub_tag = values(ska_sub_tag),
      </if>
      <if test="skaSubTagSource != null">
        ska_sub_tag_source = values(ska_sub_tag_source),
      </if>
      <if test="skaGroupId != null">
        ska_group_Id = values(ska_group_Id),
      </if>
      <if test="skaGroupName != null">
        ska_group_name = values(ska_group_name),
      </if>
      <if test="skaCompleteMoney != null">
        ska_complete_money = values(ska_complete_money),
      </if>
      <if test="skaBrandCompleteMoney != null">
        ska_brand_complete_money = values(ska_brand_complete_money),
      </if>
      <if test="skaRtbCompleteMoney != null">
        ska_rtb_complete_money = values(ska_rtb_complete_money),
      </if>
      <if test="skaPickupCompleteMoney != null">
        ska_pickup_complete_money = values(ska_pickup_complete_money),
      </if>
      <if test="queryDate != null">
        query_date = values(query_date),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="skaBeforeCompleteMoney != null">
        ska_before_complete_money = values(ska_before_complete_money),
      </if>
      <if test="groupProfileFlag != null">
        group_profile_flag = values(group_profile_flag),
      </if>
      <if test="groupContactsFlag != null">
        group_contacts_flag = values(group_contacts_flag),
      </if>
      <if test="groupBsiCount != null">
        group_bsi_count = values(group_bsi_count),
      </if>
      <if test="groupVisitCount != null">
        group_visit_count = values(group_visit_count),
      </if>
      <if test="groupSignProcess != null">
        group_sign_process = values(group_sign_process),
      </if>
      <if test="groupYearTarget != null">
        group_year_target = values(group_year_target),
      </if>
      <if test="groupSignMoney != null">
        group_sign_money = values(group_sign_money),
      </if>
      <if test="groupSignCompleteMoney != null">
        group_sign_complete_money = values(group_sign_complete_money),
      </if>
      <if test="skaQuarterTarget != null">
        ska_quarter_target = values(ska_quarter_target),
      </if>
      <if test="dataEnv != null">
        data_env = values(data_env),
      </if>
      <if test="groupContactsRepeatFlag != null">
        group_contacts_repeat_flag = values(group_contacts_repeat_flag),
      </if>
    </trim>
  </insert>
</mapper>