<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.AdaBizAccountDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="bid" jdbcType="BIGINT" property="bid" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="role" jdbcType="TINYINT" property="role" />
    <result column="is_admin" jdbcType="TINYINT" property="isAdmin" />
    <result column="creator" jdbcType="VARCHAR" property="creator" />
    <result column="remark" jdbcType="VARCHAR" property="remark" />
    <result column="description" jdbcType="VARCHAR" property="description" />
    <result column="system_type_source" jdbcType="INTEGER" property="systemTypeSource" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="org_cert_status" jdbcType="INTEGER" property="orgCertStatus" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    <id column="ada_biz_account_id" jdbcType="INTEGER" property="id" />
    <result column="ada_biz_account_bid" jdbcType="BIGINT" property="bid" />
    <result column="ada_biz_account_status" jdbcType="TINYINT" property="status" />
    <result column="ada_biz_account_customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="ada_biz_account_role" jdbcType="TINYINT" property="role" />
    <result column="ada_biz_account_is_admin" jdbcType="TINYINT" property="isAdmin" />
    <result column="ada_biz_account_creator" jdbcType="VARCHAR" property="creator" />
    <result column="ada_biz_account_remark" jdbcType="VARCHAR" property="remark" />
    <result column="ada_biz_account_description" jdbcType="VARCHAR" property="description" />
    <result column="ada_biz_account_system_type_source" jdbcType="INTEGER" property="systemTypeSource" />
    <result column="ada_biz_account_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="ada_biz_account_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="ada_biz_account_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ada_biz_account_org_cert_status" jdbcType="INTEGER" property="orgCertStatus" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as ada_biz_account_id, ${alias}.bid as ada_biz_account_bid, ${alias}.status as ada_biz_account_status, 
    ${alias}.customer_id as ada_biz_account_customer_id, ${alias}.role as ada_biz_account_role, 
    ${alias}.is_admin as ada_biz_account_is_admin, ${alias}.creator as ada_biz_account_creator, 
    ${alias}.remark as ada_biz_account_remark, ${alias}.description as ada_biz_account_description, 
    ${alias}.system_type_source as ada_biz_account_system_type_source, ${alias}.ctime as ada_biz_account_ctime, 
    ${alias}.mtime as ada_biz_account_mtime, ${alias}.is_deleted as ada_biz_account_is_deleted, 
    ${alias}.org_cert_status as ada_biz_account_org_cert_status
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, bid, status, customer_id, role, is_admin, creator, remark, description, system_type_source, 
    ctime, mtime, is_deleted, org_cert_status
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from ada_biz_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from ada_biz_account
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from ada_biz_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPoExample">
    delete from ada_biz_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ada_biz_account (bid, status, customer_id, 
      role, is_admin, creator, 
      remark, description, system_type_source, 
      ctime, mtime, is_deleted, 
      org_cert_status)
    values (#{bid,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{customerId,jdbcType=INTEGER}, 
      #{role,jdbcType=TINYINT}, #{isAdmin,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{systemTypeSource,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{orgCertStatus,jdbcType=INTEGER})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ada_biz_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bid != null">
        bid,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="systemTypeSource != null">
        system_type_source,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="orgCertStatus != null">
        org_cert_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bid != null">
        #{bid,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="role != null">
        #{role,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="systemTypeSource != null">
        #{systemTypeSource,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="orgCertStatus != null">
        #{orgCertStatus,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPoExample" resultType="java.lang.Long">
    select count(*) from ada_biz_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update ada_biz_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.bid != null">
        bid = #{record.bid,jdbcType=BIGINT},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.role != null">
        role = #{record.role,jdbcType=TINYINT},
      </if>
      <if test="record.isAdmin != null">
        is_admin = #{record.isAdmin,jdbcType=TINYINT},
      </if>
      <if test="record.creator != null">
        creator = #{record.creator,jdbcType=VARCHAR},
      </if>
      <if test="record.remark != null">
        remark = #{record.remark,jdbcType=VARCHAR},
      </if>
      <if test="record.description != null">
        description = #{record.description,jdbcType=VARCHAR},
      </if>
      <if test="record.systemTypeSource != null">
        system_type_source = #{record.systemTypeSource,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.orgCertStatus != null">
        org_cert_status = #{record.orgCertStatus,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update ada_biz_account
    set id = #{record.id,jdbcType=INTEGER},
      bid = #{record.bid,jdbcType=BIGINT},
      status = #{record.status,jdbcType=TINYINT},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      role = #{record.role,jdbcType=TINYINT},
      is_admin = #{record.isAdmin,jdbcType=TINYINT},
      creator = #{record.creator,jdbcType=VARCHAR},
      remark = #{record.remark,jdbcType=VARCHAR},
      description = #{record.description,jdbcType=VARCHAR},
      system_type_source = #{record.systemTypeSource,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      org_cert_status = #{record.orgCertStatus,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    update ada_biz_account
    <set>
      <if test="bid != null">
        bid = #{bid,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="role != null">
        role = #{role,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        is_admin = #{isAdmin,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        creator = #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        remark = #{remark,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        description = #{description,jdbcType=VARCHAR},
      </if>
      <if test="systemTypeSource != null">
        system_type_source = #{systemTypeSource,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="orgCertStatus != null">
        org_cert_status = #{orgCertStatus,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    update ada_biz_account
    set bid = #{bid,jdbcType=BIGINT},
      status = #{status,jdbcType=TINYINT},
      customer_id = #{customerId,jdbcType=INTEGER},
      role = #{role,jdbcType=TINYINT},
      is_admin = #{isAdmin,jdbcType=TINYINT},
      creator = #{creator,jdbcType=VARCHAR},
      remark = #{remark,jdbcType=VARCHAR},
      description = #{description,jdbcType=VARCHAR},
      system_type_source = #{systemTypeSource,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      org_cert_status = #{orgCertStatus,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ada_biz_account (bid, status, customer_id, 
      role, is_admin, creator, 
      remark, description, system_type_source, 
      ctime, mtime, is_deleted, 
      org_cert_status)
    values (#{bid,jdbcType=BIGINT}, #{status,jdbcType=TINYINT}, #{customerId,jdbcType=INTEGER}, 
      #{role,jdbcType=TINYINT}, #{isAdmin,jdbcType=TINYINT}, #{creator,jdbcType=VARCHAR}, 
      #{remark,jdbcType=VARCHAR}, #{description,jdbcType=VARCHAR}, #{systemTypeSource,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=TINYINT}, 
      #{orgCertStatus,jdbcType=INTEGER})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      bid = values(bid),
      status = values(status),
      customer_id = values(customer_id),
      role = values(role),
      is_admin = values(is_admin),
      creator = values(creator),
      remark = values(remark),
      description = values(description),
      system_type_source = values(system_type_source),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      org_cert_status = values(org_cert_status),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      ada_biz_account
      (bid,status,customer_id,role,is_admin,creator,remark,description,system_type_source,ctime,mtime,is_deleted,org_cert_status)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bid,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.customerId,jdbcType=INTEGER},
        #{item.role,jdbcType=TINYINT},
        #{item.isAdmin,jdbcType=TINYINT},
        #{item.creator,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.systemTypeSource,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.orgCertStatus,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      ada_biz_account
      (bid,status,customer_id,role,is_admin,creator,remark,description,system_type_source,ctime,mtime,is_deleted,org_cert_status)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.bid,jdbcType=BIGINT},
        #{item.status,jdbcType=TINYINT},
        #{item.customerId,jdbcType=INTEGER},
        #{item.role,jdbcType=TINYINT},
        #{item.isAdmin,jdbcType=TINYINT},
        #{item.creator,jdbcType=VARCHAR},
        #{item.remark,jdbcType=VARCHAR},
        #{item.description,jdbcType=VARCHAR},
        #{item.systemTypeSource,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.orgCertStatus,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      bid = values(bid),
      status = values(status),
      customer_id = values(customer_id),
      role = values(role),
      is_admin = values(is_admin),
      creator = values(creator),
      remark = values(remark),
      description = values(description),
      system_type_source = values(system_type_source),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      org_cert_status = values(org_cert_status),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.AdaBizAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into ada_biz_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="bid != null">
        bid,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="role != null">
        role,
      </if>
      <if test="isAdmin != null">
        is_admin,
      </if>
      <if test="creator != null">
        creator,
      </if>
      <if test="remark != null">
        remark,
      </if>
      <if test="description != null">
        description,
      </if>
      <if test="systemTypeSource != null">
        system_type_source,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="orgCertStatus != null">
        org_cert_status,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="bid != null">
        #{bid,jdbcType=BIGINT},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="role != null">
        #{role,jdbcType=TINYINT},
      </if>
      <if test="isAdmin != null">
        #{isAdmin,jdbcType=TINYINT},
      </if>
      <if test="creator != null">
        #{creator,jdbcType=VARCHAR},
      </if>
      <if test="remark != null">
        #{remark,jdbcType=VARCHAR},
      </if>
      <if test="description != null">
        #{description,jdbcType=VARCHAR},
      </if>
      <if test="systemTypeSource != null">
        #{systemTypeSource,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="orgCertStatus != null">
        #{orgCertStatus,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="bid != null">
        bid = values(bid),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="customerId != null">
        customer_id = values(customer_id),
      </if>
      <if test="role != null">
        role = values(role),
      </if>
      <if test="isAdmin != null">
        is_admin = values(is_admin),
      </if>
      <if test="creator != null">
        creator = values(creator),
      </if>
      <if test="remark != null">
        remark = values(remark),
      </if>
      <if test="description != null">
        description = values(description),
      </if>
      <if test="systemTypeSource != null">
        system_type_source = values(system_type_source),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="orgCertStatus != null">
        org_cert_status = values(org_cert_status),
      </if>
    </trim>
  </insert>
</mapper>