<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmAgentCustomerProductSaleSeaMappingDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="sale_id" jdbcType="INTEGER" property="saleId" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="product_id" jdbcType="INTEGER" property="productId" />
    <result column="customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="sale_sea_product_category" jdbcType="TINYINT" property="saleSeaProductCategory" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    <id column="crm_agent_customer_product_sale_sea_mapping_id" jdbcType="BIGINT" property="id" />
    <result column="crm_agent_customer_product_sale_sea_mapping_sale_id" jdbcType="INTEGER" property="saleId" />
    <result column="crm_agent_customer_product_sale_sea_mapping_account_id" jdbcType="INTEGER" property="accountId" />
    <result column="crm_agent_customer_product_sale_sea_mapping_product_id" jdbcType="INTEGER" property="productId" />
    <result column="crm_agent_customer_product_sale_sea_mapping_customer_id" jdbcType="INTEGER" property="customerId" />
    <result column="crm_agent_customer_product_sale_sea_mapping_sale_sea_product_category" jdbcType="TINYINT" property="saleSeaProductCategory" />
    <result column="crm_agent_customer_product_sale_sea_mapping_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_agent_customer_product_sale_sea_mapping_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_agent_customer_product_sale_sea_mapping_mtime" jdbcType="TIMESTAMP" property="mtime" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_agent_customer_product_sale_sea_mapping_id, ${alias}.sale_id as crm_agent_customer_product_sale_sea_mapping_sale_id, 
    ${alias}.account_id as crm_agent_customer_product_sale_sea_mapping_account_id, ${alias}.product_id as crm_agent_customer_product_sale_sea_mapping_product_id, 
    ${alias}.customer_id as crm_agent_customer_product_sale_sea_mapping_customer_id, 
    ${alias}.sale_sea_product_category as crm_agent_customer_product_sale_sea_mapping_sale_sea_product_category, 
    ${alias}.is_deleted as crm_agent_customer_product_sale_sea_mapping_is_deleted, ${alias}.ctime as crm_agent_customer_product_sale_sea_mapping_ctime, 
    ${alias}.mtime as crm_agent_customer_product_sale_sea_mapping_mtime
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, sale_id, account_id, product_id, customer_id, sale_sea_product_category, is_deleted, 
    ctime, mtime
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_agent_customer_product_sale_sea_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_agent_customer_product_sale_sea_mapping
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_agent_customer_product_sale_sea_mapping
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPoExample">
    delete from crm_agent_customer_product_sale_sea_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_customer_product_sale_sea_mapping (sale_id, account_id, product_id, 
      customer_id, sale_sea_product_category, is_deleted, 
      ctime, mtime)
    values (#{saleId,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{productId,jdbcType=INTEGER}, 
      #{customerId,jdbcType=INTEGER}, #{saleSeaProductCategory,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_customer_product_sale_sea_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleId != null">
        sale_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="saleSeaProductCategory != null">
        sale_sea_product_category,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleId != null">
        #{saleId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="saleSeaProductCategory != null">
        #{saleSeaProductCategory,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPoExample" resultType="java.lang.Long">
    select count(*) from crm_agent_customer_product_sale_sea_mapping
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_agent_customer_product_sale_sea_mapping
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.saleId != null">
        sale_id = #{record.saleId,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.productId != null">
        product_id = #{record.productId,jdbcType=INTEGER},
      </if>
      <if test="record.customerId != null">
        customer_id = #{record.customerId,jdbcType=INTEGER},
      </if>
      <if test="record.saleSeaProductCategory != null">
        sale_sea_product_category = #{record.saleSeaProductCategory,jdbcType=TINYINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_agent_customer_product_sale_sea_mapping
    set id = #{record.id,jdbcType=BIGINT},
      sale_id = #{record.saleId,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      product_id = #{record.productId,jdbcType=INTEGER},
      customer_id = #{record.customerId,jdbcType=INTEGER},
      sale_sea_product_category = #{record.saleSeaProductCategory,jdbcType=TINYINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    update crm_agent_customer_product_sale_sea_mapping
    <set>
      <if test="saleId != null">
        sale_id = #{saleId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        product_id = #{productId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        customer_id = #{customerId,jdbcType=INTEGER},
      </if>
      <if test="saleSeaProductCategory != null">
        sale_sea_product_category = #{saleSeaProductCategory,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    update crm_agent_customer_product_sale_sea_mapping
    set sale_id = #{saleId,jdbcType=INTEGER},
      account_id = #{accountId,jdbcType=INTEGER},
      product_id = #{productId,jdbcType=INTEGER},
      customer_id = #{customerId,jdbcType=INTEGER},
      sale_sea_product_category = #{saleSeaProductCategory,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_customer_product_sale_sea_mapping (sale_id, account_id, product_id, 
      customer_id, sale_sea_product_category, is_deleted, 
      ctime, mtime)
    values (#{saleId,jdbcType=INTEGER}, #{accountId,jdbcType=INTEGER}, #{productId,jdbcType=INTEGER}, 
      #{customerId,jdbcType=INTEGER}, #{saleSeaProductCategory,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      sale_id = values(sale_id),
      account_id = values(account_id),
      product_id = values(product_id),
      customer_id = values(customer_id),
      sale_sea_product_category = values(sale_sea_product_category),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_agent_customer_product_sale_sea_mapping
      (sale_id,account_id,product_id,customer_id,sale_sea_product_category,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.saleId,jdbcType=INTEGER},
        #{item.accountId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.customerId,jdbcType=INTEGER},
        #{item.saleSeaProductCategory,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_agent_customer_product_sale_sea_mapping
      (sale_id,account_id,product_id,customer_id,sale_sea_product_category,is_deleted,ctime,mtime)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.saleId,jdbcType=INTEGER},
        #{item.accountId,jdbcType=INTEGER},
        #{item.productId,jdbcType=INTEGER},
        #{item.customerId,jdbcType=INTEGER},
        #{item.saleSeaProductCategory,jdbcType=TINYINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      sale_id = values(sale_id),
      account_id = values(account_id),
      product_id = values(product_id),
      customer_id = values(customer_id),
      sale_sea_product_category = values(sale_sea_product_category),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmAgentCustomerProductSaleSeaMappingPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_agent_customer_product_sale_sea_mapping
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="saleId != null">
        sale_id,
      </if>
      <if test="accountId != null">
        account_id,
      </if>
      <if test="productId != null">
        product_id,
      </if>
      <if test="customerId != null">
        customer_id,
      </if>
      <if test="saleSeaProductCategory != null">
        sale_sea_product_category,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="saleId != null">
        #{saleId,jdbcType=INTEGER},
      </if>
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="productId != null">
        #{productId,jdbcType=INTEGER},
      </if>
      <if test="customerId != null">
        #{customerId,jdbcType=INTEGER},
      </if>
      <if test="saleSeaProductCategory != null">
        #{saleSeaProductCategory,jdbcType=TINYINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="saleId != null">
        sale_id = values(sale_id),
      </if>
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="productId != null">
        product_id = values(product_id),
      </if>
      <if test="customerId != null">
        customer_id = values(customer_id),
      </if>
      <if test="saleSeaProductCategory != null">
        sale_sea_product_category = values(sale_sea_product_category),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
    </trim>
  </insert>
</mapper>