<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmProjectCheckMoneyDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="seat_type" jdbcType="INTEGER" property="seatType" />
    <result column="money" jdbcType="BIGINT" property="money" />
    <result column="project_item_id" jdbcType="INTEGER" property="projectItemId" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="INTEGER" property="isDeleted" />
    <result column="seat_money" jdbcType="BIGINT" property="seatMoney" />
    <result column="seat_count" jdbcType="BIGINT" property="seatCount" />
    <result column="version" jdbcType="INTEGER" property="version" />
    <result column="top_version_enable" jdbcType="TINYINT" property="topVersionEnable" />
    <result column="seat_type_desc" jdbcType="VARCHAR" property="seatTypeDesc" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, seat_type, money, project_item_id, ctime, mtime, is_deleted, seat_money, seat_count, 
    version, top_version_enable, seat_type_desc
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_project_check_money
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_project_check_money
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_project_check_money
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPoExample">
    delete from crm_project_check_money
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_check_money (seat_type, money, project_item_id, 
      ctime, mtime, is_deleted, 
      seat_money, seat_count, version, 
      top_version_enable, seat_type_desc)
    values (#{seatType,jdbcType=INTEGER}, #{money,jdbcType=BIGINT}, #{projectItemId,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, 
      #{seatMoney,jdbcType=BIGINT}, #{seatCount,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, 
      #{topVersionEnable,jdbcType=TINYINT}, #{seatTypeDesc,jdbcType=VARCHAR})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_check_money
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seatType != null">
        seat_type,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="projectItemId != null">
        project_item_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="seatMoney != null">
        seat_money,
      </if>
      <if test="seatCount != null">
        seat_count,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="topVersionEnable != null">
        top_version_enable,
      </if>
      <if test="seatTypeDesc != null">
        seat_type_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seatType != null">
        #{seatType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=BIGINT},
      </if>
      <if test="projectItemId != null">
        #{projectItemId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="seatMoney != null">
        #{seatMoney,jdbcType=BIGINT},
      </if>
      <if test="seatCount != null">
        #{seatCount,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="topVersionEnable != null">
        #{topVersionEnable,jdbcType=TINYINT},
      </if>
      <if test="seatTypeDesc != null">
        #{seatTypeDesc,jdbcType=VARCHAR},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPoExample" resultType="java.lang.Long">
    select count(*) from crm_project_check_money
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_project_check_money
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.seatType != null">
        seat_type = #{record.seatType,jdbcType=INTEGER},
      </if>
      <if test="record.money != null">
        money = #{record.money,jdbcType=BIGINT},
      </if>
      <if test="record.projectItemId != null">
        project_item_id = #{record.projectItemId,jdbcType=INTEGER},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      </if>
      <if test="record.seatMoney != null">
        seat_money = #{record.seatMoney,jdbcType=BIGINT},
      </if>
      <if test="record.seatCount != null">
        seat_count = #{record.seatCount,jdbcType=BIGINT},
      </if>
      <if test="record.version != null">
        version = #{record.version,jdbcType=INTEGER},
      </if>
      <if test="record.topVersionEnable != null">
        top_version_enable = #{record.topVersionEnable,jdbcType=TINYINT},
      </if>
      <if test="record.seatTypeDesc != null">
        seat_type_desc = #{record.seatTypeDesc,jdbcType=VARCHAR},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_project_check_money
    set id = #{record.id,jdbcType=BIGINT},
      seat_type = #{record.seatType,jdbcType=INTEGER},
      money = #{record.money,jdbcType=BIGINT},
      project_item_id = #{record.projectItemId,jdbcType=INTEGER},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=INTEGER},
      seat_money = #{record.seatMoney,jdbcType=BIGINT},
      seat_count = #{record.seatCount,jdbcType=BIGINT},
      version = #{record.version,jdbcType=INTEGER},
      top_version_enable = #{record.topVersionEnable,jdbcType=TINYINT},
      seat_type_desc = #{record.seatTypeDesc,jdbcType=VARCHAR}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    update crm_project_check_money
    <set>
      <if test="seatType != null">
        seat_type = #{seatType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        money = #{money,jdbcType=BIGINT},
      </if>
      <if test="projectItemId != null">
        project_item_id = #{projectItemId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="seatMoney != null">
        seat_money = #{seatMoney,jdbcType=BIGINT},
      </if>
      <if test="seatCount != null">
        seat_count = #{seatCount,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        version = #{version,jdbcType=INTEGER},
      </if>
      <if test="topVersionEnable != null">
        top_version_enable = #{topVersionEnable,jdbcType=TINYINT},
      </if>
      <if test="seatTypeDesc != null">
        seat_type_desc = #{seatTypeDesc,jdbcType=VARCHAR},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    update crm_project_check_money
    set seat_type = #{seatType,jdbcType=INTEGER},
      money = #{money,jdbcType=BIGINT},
      project_item_id = #{projectItemId,jdbcType=INTEGER},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=INTEGER},
      seat_money = #{seatMoney,jdbcType=BIGINT},
      seat_count = #{seatCount,jdbcType=BIGINT},
      version = #{version,jdbcType=INTEGER},
      top_version_enable = #{topVersionEnable,jdbcType=TINYINT},
      seat_type_desc = #{seatTypeDesc,jdbcType=VARCHAR}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_check_money (seat_type, money, project_item_id, 
      ctime, mtime, is_deleted, 
      seat_money, seat_count, version, 
      top_version_enable, seat_type_desc)
    values (#{seatType,jdbcType=INTEGER}, #{money,jdbcType=BIGINT}, #{projectItemId,jdbcType=INTEGER}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{isDeleted,jdbcType=INTEGER}, 
      #{seatMoney,jdbcType=BIGINT}, #{seatCount,jdbcType=BIGINT}, #{version,jdbcType=INTEGER}, 
      #{topVersionEnable,jdbcType=TINYINT}, #{seatTypeDesc,jdbcType=VARCHAR})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      seat_type = values(seat_type),
      money = values(money),
      project_item_id = values(project_item_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      seat_money = values(seat_money),
      seat_count = values(seat_count),
      version = values(version),
      top_version_enable = values(top_version_enable),
      seat_type_desc = values(seat_type_desc),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_project_check_money
      (seat_type,money,project_item_id,ctime,mtime,is_deleted,seat_money,seat_count,version,top_version_enable,seat_type_desc)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.seatType,jdbcType=INTEGER},
        #{item.money,jdbcType=BIGINT},
        #{item.projectItemId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.seatMoney,jdbcType=BIGINT},
        #{item.seatCount,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER},
        #{item.topVersionEnable,jdbcType=TINYINT},
        #{item.seatTypeDesc,jdbcType=VARCHAR},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_project_check_money
      (seat_type,money,project_item_id,ctime,mtime,is_deleted,seat_money,seat_count,version,top_version_enable,seat_type_desc)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.seatType,jdbcType=INTEGER},
        #{item.money,jdbcType=BIGINT},
        #{item.projectItemId,jdbcType=INTEGER},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=INTEGER},
        #{item.seatMoney,jdbcType=BIGINT},
        #{item.seatCount,jdbcType=BIGINT},
        #{item.version,jdbcType=INTEGER},
        #{item.topVersionEnable,jdbcType=TINYINT},
        #{item.seatTypeDesc,jdbcType=VARCHAR},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      seat_type = values(seat_type),
      money = values(money),
      project_item_id = values(project_item_id),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      seat_money = values(seat_money),
      seat_count = values(seat_count),
      version = values(version),
      top_version_enable = values(top_version_enable),
      seat_type_desc = values(seat_type_desc),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectCheckMoneyPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_check_money
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="seatType != null">
        seat_type,
      </if>
      <if test="money != null">
        money,
      </if>
      <if test="projectItemId != null">
        project_item_id,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="seatMoney != null">
        seat_money,
      </if>
      <if test="seatCount != null">
        seat_count,
      </if>
      <if test="version != null">
        version,
      </if>
      <if test="topVersionEnable != null">
        top_version_enable,
      </if>
      <if test="seatTypeDesc != null">
        seat_type_desc,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="seatType != null">
        #{seatType,jdbcType=INTEGER},
      </if>
      <if test="money != null">
        #{money,jdbcType=BIGINT},
      </if>
      <if test="projectItemId != null">
        #{projectItemId,jdbcType=INTEGER},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=INTEGER},
      </if>
      <if test="seatMoney != null">
        #{seatMoney,jdbcType=BIGINT},
      </if>
      <if test="seatCount != null">
        #{seatCount,jdbcType=BIGINT},
      </if>
      <if test="version != null">
        #{version,jdbcType=INTEGER},
      </if>
      <if test="topVersionEnable != null">
        #{topVersionEnable,jdbcType=TINYINT},
      </if>
      <if test="seatTypeDesc != null">
        #{seatTypeDesc,jdbcType=VARCHAR},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="seatType != null">
        seat_type = values(seat_type),
      </if>
      <if test="money != null">
        money = values(money),
      </if>
      <if test="projectItemId != null">
        project_item_id = values(project_item_id),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="seatMoney != null">
        seat_money = values(seat_money),
      </if>
      <if test="seatCount != null">
        seat_count = values(seat_count),
      </if>
      <if test="version != null">
        version = values(version),
      </if>
      <if test="topVersionEnable != null">
        top_version_enable = values(top_version_enable),
      </if>
      <if test="seatTypeDesc != null">
        seat_type_desc = values(seat_type_desc),
      </if>
    </trim>
  </insert>
</mapper>