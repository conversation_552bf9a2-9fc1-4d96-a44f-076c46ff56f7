<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmSaleGroupDao">
    <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmSaleGroupPo">
        <id column="id" jdbcType="INTEGER" property="id"/>
        <result column="name" jdbcType="VARCHAR" property="name"/>
        <result column="sale_type" jdbcType="TINYINT" property="saleType"/>
        <result column="leader" jdbcType="INTEGER" property="leader"/>
        <result column="status" jdbcType="TINYINT" property="status"/>
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted"/>
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime"/>
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime"/>
        <result column="sale_director_id" jdbcType="INTEGER" property="saleDirectorId"/>
        <result column="parent_id" jdbcType="INTEGER" property="parentId"/>
    </resultMap>
    <sql id="Example_Where_Clause">
        <where>
            <foreach collection="oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Update_By_Example_Where_Clause">
        <where>
            <foreach collection="example.oredCriteria" item="criteria" separator="or">
                <if test="criteria.valid">
                    <trim prefix="(" prefixOverrides="and" suffix=")">
                        <foreach collection="criteria.criteria" item="criterion">
                            <choose>
                                <when test="criterion.noValue">
                                    and ${criterion.condition}
                                </when>
                                <when test="criterion.singleValue">
                                    and ${criterion.condition} #{criterion.value}
                                </when>
                                <when test="criterion.betweenValue">
                                    and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                                </when>
                                <when test="criterion.listValue">
                                    and ${criterion.condition}
                                    <foreach close=")" collection="criterion.value" item="listItem" open="("
                                             separator=",">
                                        #{listItem}
                                    </foreach>
                                </when>
                            </choose>
                        </foreach>
                    </trim>
                </if>
            </foreach>
        </where>
    </sql>
    <sql id="Base_Column_List">
    id, name, sale_type, leader, status, is_deleted, ctime, mtime, sale_director_id , parent_id
  </sql>
    <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPoExample"
            resultMap="BaseResultMap">
        select
        <if test="distinct">
            distinct
        </if>
        <include refid="Base_Column_List"/>
        from crm_sale_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
        <if test="orderByClause != null">
            order by ${orderByClause}
        </if>
        <if test="limit != null">
            <if test="offset != null">
                limit ${offset}, ${limit}
            </if>
            <if test="offset == null">
                limit ${limit}
            </if>
        </if>
    </select>
    <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
        select
        <include refid="Base_Column_List"/>
        from crm_sale_group
        where id = #{id,jdbcType=INTEGER}
    </select>
    <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_sale_group
    where id = #{id,jdbcType=INTEGER}
  </delete>
    <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPoExample">
        delete from crm_sale_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </delete>
    <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into crm_sale_group (name, sale_type, leader,
        status, is_deleted, ctime,
        mtime, sale_director_id, parent_id)
        values (#{name,jdbcType=VARCHAR}, #{saleType,jdbcType=TINYINT}, #{leader,jdbcType=INTEGER},
        #{status,jdbcType=TINYINT}, #{isDeleted,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP},
        #{mtime,jdbcType=TIMESTAMP}, #{saleDirectorId,jdbcType=INTEGER}, #{parentId, jdbcType=INTEGER})
    </insert>
    <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPo">
        <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
            SELECT LAST_INSERT_ID()
        </selectKey>
        insert into crm_sale_group
        <trim prefix="(" suffix=")" suffixOverrides=",">
            <if test="name != null">
                name,
            </if>
            <if test="saleType != null">
                sale_type,
            </if>
            <if test="leader != null">
                leader,
            </if>
            <if test="status != null">
                status,
            </if>
            <if test="isDeleted != null">
                is_deleted,
            </if>
            <if test="ctime != null">
                ctime,
            </if>
            <if test="mtime != null">
                mtime,
            </if>
            <if test="saleDirectorId != null">
                sale_director_id,
            </if>
            <if test="parentId != null">
                parent_id,
            </if>
        </trim>
        <trim prefix="values (" suffix=")" suffixOverrides=",">
            <if test="name != null">
                #{name,jdbcType=VARCHAR},
            </if>
            <if test="saleType != null">
                #{saleType,jdbcType=TINYINT},
            </if>
            <if test="leader != null">
                #{leader,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                #{status,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="ctime != null">
                #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="saleDirectorId != null">
                #{saleDirectorId,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                #{parentId , jdbcType=INTEGER},
            </if>
        </trim>
    </insert>
    <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPoExample"
            resultType="java.lang.Long">
        select count(*) from crm_sale_group
        <if test="_parameter != null">
            <include refid="Example_Where_Clause"/>
        </if>
    </select>

    <update id="updateByExampleSelective" parameterType="map">
        update crm_sale_group
        <set>
            <if test="record.id != null">
                id = #{record.id,jdbcType=INTEGER},
            </if>
            <if test="record.name != null">
                name = #{record.name,jdbcType=VARCHAR},
            </if>
            <if test="record.saleType != null">
                sale_type = #{record.saleType,jdbcType=TINYINT},
            </if>
            <if test="record.leader != null">
                leader = #{record.leader,jdbcType=INTEGER},
            </if>
            <if test="record.status != null">
                status = #{record.status,jdbcType=TINYINT},
            </if>
            <if test="record.isDeleted != null">
                is_deleted = #{record.isDeleted,jdbcType=TINYINT},
            </if>
            <if test="record.ctime != null">
                ctime = #{record.ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.mtime != null">
                mtime = #{record.mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="record.saleDirectorId != null">
                sale_director_id = #{record.saleDirectorId,jdbcType=INTEGER},
            </if>
            <if test="record.parentId != null">
                parent_id = #{record.parentId,jdbcType=INTEGER},
            </if>
        </set>
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByExample" parameterType="map">
        update crm_sale_group
        set id = #{record.id,jdbcType=INTEGER},
        name = #{record.name,jdbcType=VARCHAR},
        sale_type = #{record.saleType,jdbcType=TINYINT},
        leader = #{record.leader,jdbcType=INTEGER},
        status = #{record.status,jdbcType=TINYINT},
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
        sale_director_id = #{record.saleDirectorId,jdbcType=INTEGER},
        parent_id = #{record.parentId,jdbcType=INTEGER}
        <if test="_parameter != null">
            <include refid="Update_By_Example_Where_Clause"/>
        </if>
    </update>
    <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPo">
        update crm_sale_group
        <set>
            <if test="name != null">
                name = #{name,jdbcType=VARCHAR},
            </if>
            <if test="saleType != null">
                sale_type = #{saleType,jdbcType=TINYINT},
            </if>
            <if test="leader != null">
                leader = #{leader,jdbcType=INTEGER},
            </if>
            <if test="status != null">
                status = #{status,jdbcType=TINYINT},
            </if>
            <if test="isDeleted != null">
                is_deleted = #{isDeleted,jdbcType=TINYINT},
            </if>
            <if test="ctime != null">
                ctime = #{ctime,jdbcType=TIMESTAMP},
            </if>
            <if test="mtime != null">
                mtime = #{mtime,jdbcType=TIMESTAMP},
            </if>
            <if test="saleDirectorId != null">
                sale_director_id = #{saleDirectorId,jdbcType=INTEGER},
            </if>
            <if test="parentId != null">
                parent_id = #{parentId,jdbcType=INTEGER},
            </if>
        </set>
        where id = #{id,jdbcType=INTEGER}
    </update>
    <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmSaleGroupPo">
    update crm_sale_group
    set name = #{name,jdbcType=VARCHAR},
      sale_type = #{saleType,jdbcType=TINYINT},
      leader = #{leader,jdbcType=INTEGER},
      status = #{status,jdbcType=TINYINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      sale_director_id = #{saleDirectorId,jdbcType=INTEGER},
    parent_id = #{parentId,jdbcType=INTEGER}
    where id = #{id,jdbcType=INTEGER}
  </update>
</mapper>