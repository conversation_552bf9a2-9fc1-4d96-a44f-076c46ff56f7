<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmBankVirtualAccountDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    <id column="id" jdbcType="INTEGER" property="id" />
    <result column="account_id" jdbcType="INTEGER" property="accountId" />
    <result column="bank_account_no" jdbcType="VARCHAR" property="bankAccountNo" />
    <result column="bank_account_name" jdbcType="VARCHAR" property="bankAccountName" />
    <result column="sub_bank_account_no" jdbcType="VARCHAR" property="subBankAccountNo" />
    <result column="sub_bank_account_name" jdbcType="VARCHAR" property="subBankAccountName" />
    <result column="bank_name" jdbcType="VARCHAR" property="bankName" />
    <result column="status" jdbcType="TINYINT" property="status" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="body_code" jdbcType="TINYINT" property="bodyCode" />
  </resultMap>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, account_id, bank_account_no, bank_account_name, sub_bank_account_no, sub_bank_account_name, 
    bank_name, status, ctime, mtime, is_deleted, body_code
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_bank_virtual_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Integer" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_bank_virtual_account
    where id = #{id,jdbcType=INTEGER}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Integer">
    delete from crm_bank_virtual_account
    where id = #{id,jdbcType=INTEGER}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPoExample">
    delete from crm_bank_virtual_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_bank_virtual_account (account_id, bank_account_no, bank_account_name, 
      sub_bank_account_no, sub_bank_account_name, bank_name, 
      status, ctime, mtime, 
      is_deleted, body_code)
    values (#{accountId,jdbcType=INTEGER}, #{bankAccountNo,jdbcType=VARCHAR}, #{bankAccountName,jdbcType=VARCHAR}, 
      #{subBankAccountNo,jdbcType=VARCHAR}, #{subBankAccountName,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{bodyCode,jdbcType=TINYINT})
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_bank_virtual_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankAccountName != null">
        bank_account_name,
      </if>
      <if test="subBankAccountNo != null">
        sub_bank_account_no,
      </if>
      <if test="subBankAccountName != null">
        sub_bank_account_name,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bodyCode != null">
        body_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="subBankAccountNo != null">
        #{subBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="subBankAccountName != null">
        #{subBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bodyCode != null">
        #{bodyCode,jdbcType=TINYINT},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPoExample" resultType="java.lang.Long">
    select count(*) from crm_bank_virtual_account
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_bank_virtual_account
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=INTEGER},
      </if>
      <if test="record.accountId != null">
        account_id = #{record.accountId,jdbcType=INTEGER},
      </if>
      <if test="record.bankAccountNo != null">
        bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.bankAccountName != null">
        bank_account_name = #{record.bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.subBankAccountNo != null">
        sub_bank_account_no = #{record.subBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="record.subBankAccountName != null">
        sub_bank_account_name = #{record.subBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="record.bankName != null">
        bank_name = #{record.bankName,jdbcType=VARCHAR},
      </if>
      <if test="record.status != null">
        status = #{record.status,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.bodyCode != null">
        body_code = #{record.bodyCode,jdbcType=TINYINT},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_bank_virtual_account
    set id = #{record.id,jdbcType=INTEGER},
      account_id = #{record.accountId,jdbcType=INTEGER},
      bank_account_no = #{record.bankAccountNo,jdbcType=VARCHAR},
      bank_account_name = #{record.bankAccountName,jdbcType=VARCHAR},
      sub_bank_account_no = #{record.subBankAccountNo,jdbcType=VARCHAR},
      sub_bank_account_name = #{record.subBankAccountName,jdbcType=VARCHAR},
      bank_name = #{record.bankName,jdbcType=VARCHAR},
      status = #{record.status,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      body_code = #{record.bodyCode,jdbcType=TINYINT}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    update crm_bank_virtual_account
    <set>
      <if test="accountId != null">
        account_id = #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="subBankAccountNo != null">
        sub_bank_account_no = #{subBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="subBankAccountName != null">
        sub_bank_account_name = #{subBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        bank_name = #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        status = #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bodyCode != null">
        body_code = #{bodyCode,jdbcType=TINYINT},
      </if>
    </set>
    where id = #{id,jdbcType=INTEGER}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    update crm_bank_virtual_account
    set account_id = #{accountId,jdbcType=INTEGER},
      bank_account_no = #{bankAccountNo,jdbcType=VARCHAR},
      bank_account_name = #{bankAccountName,jdbcType=VARCHAR},
      sub_bank_account_no = #{subBankAccountNo,jdbcType=VARCHAR},
      sub_bank_account_name = #{subBankAccountName,jdbcType=VARCHAR},
      bank_name = #{bankName,jdbcType=VARCHAR},
      status = #{status,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      body_code = #{bodyCode,jdbcType=TINYINT}
    where id = #{id,jdbcType=INTEGER}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_bank_virtual_account (account_id, bank_account_no, bank_account_name, 
      sub_bank_account_no, sub_bank_account_name, bank_name, 
      status, ctime, mtime, 
      is_deleted, body_code)
    values (#{accountId,jdbcType=INTEGER}, #{bankAccountNo,jdbcType=VARCHAR}, #{bankAccountName,jdbcType=VARCHAR}, 
      #{subBankAccountNo,jdbcType=VARCHAR}, #{subBankAccountName,jdbcType=VARCHAR}, #{bankName,jdbcType=VARCHAR}, 
      #{status,jdbcType=TINYINT}, #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, 
      #{isDeleted,jdbcType=TINYINT}, #{bodyCode,jdbcType=TINYINT})
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      bank_account_no = values(bank_account_no),
      bank_account_name = values(bank_account_name),
      sub_bank_account_no = values(sub_bank_account_no),
      sub_bank_account_name = values(sub_bank_account_name),
      bank_name = values(bank_name),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      body_code = values(body_code),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_bank_virtual_account
      (account_id,bank_account_no,bank_account_name,sub_bank_account_no,sub_bank_account_name,bank_name,status,ctime,mtime,is_deleted,body_code)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.bankAccountNo,jdbcType=VARCHAR},
        #{item.bankAccountName,jdbcType=VARCHAR},
        #{item.subBankAccountNo,jdbcType=VARCHAR},
        #{item.subBankAccountName,jdbcType=VARCHAR},
        #{item.bankName,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.bodyCode,jdbcType=TINYINT},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_bank_virtual_account
      (account_id,bank_account_no,bank_account_name,sub_bank_account_no,sub_bank_account_name,bank_name,status,ctime,mtime,is_deleted,body_code)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.accountId,jdbcType=INTEGER},
        #{item.bankAccountNo,jdbcType=VARCHAR},
        #{item.bankAccountName,jdbcType=VARCHAR},
        #{item.subBankAccountNo,jdbcType=VARCHAR},
        #{item.subBankAccountName,jdbcType=VARCHAR},
        #{item.bankName,jdbcType=VARCHAR},
        #{item.status,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.bodyCode,jdbcType=TINYINT},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      account_id = values(account_id),
      bank_account_no = values(bank_account_no),
      bank_account_name = values(bank_account_name),
      sub_bank_account_no = values(sub_bank_account_no),
      sub_bank_account_name = values(sub_bank_account_name),
      bank_name = values(bank_name),
      status = values(status),
      ctime = values(ctime),
      mtime = values(mtime),
      is_deleted = values(is_deleted),
      body_code = values(body_code),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmBankVirtualAccountPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Integer">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_bank_virtual_account
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        account_id,
      </if>
      <if test="bankAccountNo != null">
        bank_account_no,
      </if>
      <if test="bankAccountName != null">
        bank_account_name,
      </if>
      <if test="subBankAccountNo != null">
        sub_bank_account_no,
      </if>
      <if test="subBankAccountName != null">
        sub_bank_account_name,
      </if>
      <if test="bankName != null">
        bank_name,
      </if>
      <if test="status != null">
        status,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="bodyCode != null">
        body_code,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="accountId != null">
        #{accountId,jdbcType=INTEGER},
      </if>
      <if test="bankAccountNo != null">
        #{bankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="bankAccountName != null">
        #{bankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="subBankAccountNo != null">
        #{subBankAccountNo,jdbcType=VARCHAR},
      </if>
      <if test="subBankAccountName != null">
        #{subBankAccountName,jdbcType=VARCHAR},
      </if>
      <if test="bankName != null">
        #{bankName,jdbcType=VARCHAR},
      </if>
      <if test="status != null">
        #{status,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="bodyCode != null">
        #{bodyCode,jdbcType=TINYINT},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="accountId != null">
        account_id = values(account_id),
      </if>
      <if test="bankAccountNo != null">
        bank_account_no = values(bank_account_no),
      </if>
      <if test="bankAccountName != null">
        bank_account_name = values(bank_account_name),
      </if>
      <if test="subBankAccountNo != null">
        sub_bank_account_no = values(sub_bank_account_no),
      </if>
      <if test="subBankAccountName != null">
        sub_bank_account_name = values(sub_bank_account_name),
      </if>
      <if test="bankName != null">
        bank_name = values(bank_name),
      </if>
      <if test="status != null">
        status = values(status),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="bodyCode != null">
        body_code = values(body_code),
      </if>
    </trim>
  </insert>
</mapper>