<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.bilibili.crm.platform.biz.dao.CrmProjectYearMoneyConfigDao">
  <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    <id column="id" jdbcType="BIGINT" property="id" />
    <result column="year_name" jdbcType="VARCHAR" property="yearName" />
    <result column="year_money" jdbcType="BIGINT" property="yearMoney" />
    <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="config_type" jdbcType="INTEGER" property="configType" />
  </resultMap>
  <resultMap id="AliasBaseResultMap" type="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    <id column="crm_project_year_money_config_id" jdbcType="BIGINT" property="id" />
    <result column="crm_project_year_money_config_year_name" jdbcType="VARCHAR" property="yearName" />
    <result column="crm_project_year_money_config_year_money" jdbcType="BIGINT" property="yearMoney" />
    <result column="crm_project_year_money_config_is_deleted" jdbcType="TINYINT" property="isDeleted" />
    <result column="crm_project_year_money_config_ctime" jdbcType="TIMESTAMP" property="ctime" />
    <result column="crm_project_year_money_config_mtime" jdbcType="TIMESTAMP" property="mtime" />
    <result column="crm_project_year_money_config_config_type" jdbcType="INTEGER" property="configType" />
  </resultMap>
  <sql id="Alias_Base_Column_List">
    ${alias}.id as crm_project_year_money_config_id, ${alias}.year_name as crm_project_year_money_config_year_name, 
    ${alias}.year_money as crm_project_year_money_config_year_money, ${alias}.is_deleted as crm_project_year_money_config_is_deleted, 
    ${alias}.ctime as crm_project_year_money_config_ctime, ${alias}.mtime as crm_project_year_money_config_mtime, 
    ${alias}.config_type as crm_project_year_money_config_config_type
  </sql>
  <sql id="Example_Where_Clause">
    <where>
      <foreach collection="oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Update_By_Example_Where_Clause">
    <where>
      <foreach collection="example.oredCriteria" item="criteria" separator="or">
        <if test="criteria.valid">
          <trim prefix="(" prefixOverrides="and" suffix=")">
            <foreach collection="criteria.criteria" item="criterion">
              <choose>
                <when test="criterion.noValue">
                  and ${criterion.condition}
                </when>
                <when test="criterion.singleValue">
                  and ${criterion.condition} #{criterion.value}
                </when>
                <when test="criterion.betweenValue">
                  and ${criterion.condition} #{criterion.value} and #{criterion.secondValue}
                </when>
                <when test="criterion.listValue">
                  and ${criterion.condition}
                  <foreach close=")" collection="criterion.value" item="listItem" open="(" separator=",">
                    #{listItem}
                  </foreach>
                </when>
              </choose>
            </foreach>
          </trim>
        </if>
      </foreach>
    </where>
  </sql>
  <sql id="Base_Column_List">
    id, year_name, year_money, is_deleted, ctime, mtime, config_type
  </sql>
  <select id="selectByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPoExample" resultMap="BaseResultMap">
    select
    <if test="distinct">
      distinct
    </if>
    <include refid="Base_Column_List" />
    from crm_project_year_money_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
    <if test="orderByClause != null">
      order by ${orderByClause}
    </if>
    <if test="limit != null">
      <if test="offset != null">
        limit ${offset}, ${limit}
      </if>
      <if test="offset == null">
        limit ${limit}
      </if>
    </if>
  </select>
  <select id="selectByPrimaryKey" parameterType="java.lang.Long" resultMap="BaseResultMap">
    select 
    <include refid="Base_Column_List" />
    from crm_project_year_money_config
    where id = #{id,jdbcType=BIGINT}
  </select>
  <delete id="deleteByPrimaryKey" parameterType="java.lang.Long">
    delete from crm_project_year_money_config
    where id = #{id,jdbcType=BIGINT}
  </delete>
  <delete id="deleteByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPoExample">
    delete from crm_project_year_money_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </delete>
  <insert id="insert" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_year_money_config (year_name, year_money, is_deleted, 
      ctime, mtime, config_type
      )
    values (#{yearName,jdbcType=VARCHAR}, #{yearMoney,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{configType,jdbcType=INTEGER}
      )
  </insert>
  <insert id="insertSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_year_money_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="yearName != null">
        year_name,
      </if>
      <if test="yearMoney != null">
        year_money,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="configType != null">
        config_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="yearName != null">
        #{yearName,jdbcType=VARCHAR},
      </if>
      <if test="yearMoney != null">
        #{yearMoney,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="configType != null">
        #{configType,jdbcType=INTEGER},
      </if>
    </trim>
  </insert>
  <select id="countByExample" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPoExample" resultType="java.lang.Long">
    select count(*) from crm_project_year_money_config
    <if test="_parameter != null">
      <include refid="Example_Where_Clause" />
    </if>
  </select>
  <update id="updateByExampleSelective" parameterType="map">
    update crm_project_year_money_config
    <set>
      <if test="record.id != null">
        id = #{record.id,jdbcType=BIGINT},
      </if>
      <if test="record.yearName != null">
        year_name = #{record.yearName,jdbcType=VARCHAR},
      </if>
      <if test="record.yearMoney != null">
        year_money = #{record.yearMoney,jdbcType=BIGINT},
      </if>
      <if test="record.isDeleted != null">
        is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      </if>
      <if test="record.ctime != null">
        ctime = #{record.ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.mtime != null">
        mtime = #{record.mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="record.configType != null">
        config_type = #{record.configType,jdbcType=INTEGER},
      </if>
    </set>
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByExample" parameterType="map">
    update crm_project_year_money_config
    set id = #{record.id,jdbcType=BIGINT},
      year_name = #{record.yearName,jdbcType=VARCHAR},
      year_money = #{record.yearMoney,jdbcType=BIGINT},
      is_deleted = #{record.isDeleted,jdbcType=TINYINT},
      ctime = #{record.ctime,jdbcType=TIMESTAMP},
      mtime = #{record.mtime,jdbcType=TIMESTAMP},
      config_type = #{record.configType,jdbcType=INTEGER}
    <if test="_parameter != null">
      <include refid="Update_By_Example_Where_Clause" />
    </if>
  </update>
  <update id="updateByPrimaryKeySelective" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    update crm_project_year_money_config
    <set>
      <if test="yearName != null">
        year_name = #{yearName,jdbcType=VARCHAR},
      </if>
      <if test="yearMoney != null">
        year_money = #{yearMoney,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        is_deleted = #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        ctime = #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        mtime = #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="configType != null">
        config_type = #{configType,jdbcType=INTEGER},
      </if>
    </set>
    where id = #{id,jdbcType=BIGINT}
  </update>
  <update id="updateByPrimaryKey" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    update crm_project_year_money_config
    set year_name = #{yearName,jdbcType=VARCHAR},
      year_money = #{yearMoney,jdbcType=BIGINT},
      is_deleted = #{isDeleted,jdbcType=TINYINT},
      ctime = #{ctime,jdbcType=TIMESTAMP},
      mtime = #{mtime,jdbcType=TIMESTAMP},
      config_type = #{configType,jdbcType=INTEGER}
    where id = #{id,jdbcType=BIGINT}
  </update>
  <insert id="insertUpdate" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_year_money_config (year_name, year_money, is_deleted, 
      ctime, mtime, config_type
      )
    values (#{yearName,jdbcType=VARCHAR}, #{yearMoney,jdbcType=BIGINT}, #{isDeleted,jdbcType=TINYINT}, 
      #{ctime,jdbcType=TIMESTAMP}, #{mtime,jdbcType=TIMESTAMP}, #{configType,jdbcType=INTEGER}
      )
    <trim prefix="on duplicate key update" suffixOverrides=",">
      year_name = values(year_name),
      year_money = values(year_money),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      config_type = values(config_type),
    </trim>
  </insert>
  <insert id="insertBatch" parameterType="java.util.List">
    insert into 
      crm_project_year_money_config
      (year_name,year_money,is_deleted,ctime,mtime,config_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.yearName,jdbcType=VARCHAR},
        #{item.yearMoney,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.configType,jdbcType=INTEGER},
      </trim>
    </foreach>
  </insert>
  <insert id="insertUpdateBatch" parameterType="java.util.List">
    insert into 
      crm_project_year_money_config
      (year_name,year_money,is_deleted,ctime,mtime,config_type)
    values
    <foreach collection="list" item="item" separator=",">
      <trim prefix="(" suffix=")" suffixOverrides=",">
        #{item.yearName,jdbcType=VARCHAR},
        #{item.yearMoney,jdbcType=BIGINT},
        #{item.isDeleted,jdbcType=TINYINT},
        #{item.ctime,jdbcType=TIMESTAMP},
        #{item.mtime,jdbcType=TIMESTAMP},
        #{item.configType,jdbcType=INTEGER},
      </trim>
    </foreach>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      year_name = values(year_name),
      year_money = values(year_money),
      is_deleted = values(is_deleted),
      ctime = values(ctime),
      mtime = values(mtime),
      config_type = values(config_type),
    </trim>
  </insert>
  <insert id="insertUpdateSelective" parameterType="com.bilibili.crm.platform.biz.po.CrmProjectYearMoneyConfigPo">
    <selectKey keyProperty="id" order="AFTER" resultType="java.lang.Long">
      SELECT LAST_INSERT_ID()
    </selectKey>
    insert into crm_project_year_money_config
    <trim prefix="(" suffix=")" suffixOverrides=",">
      <if test="yearName != null">
        year_name,
      </if>
      <if test="yearMoney != null">
        year_money,
      </if>
      <if test="isDeleted != null">
        is_deleted,
      </if>
      <if test="ctime != null">
        ctime,
      </if>
      <if test="mtime != null">
        mtime,
      </if>
      <if test="configType != null">
        config_type,
      </if>
    </trim>
    <trim prefix="values (" suffix=")" suffixOverrides=",">
      <if test="yearName != null">
        #{yearName,jdbcType=VARCHAR},
      </if>
      <if test="yearMoney != null">
        #{yearMoney,jdbcType=BIGINT},
      </if>
      <if test="isDeleted != null">
        #{isDeleted,jdbcType=TINYINT},
      </if>
      <if test="ctime != null">
        #{ctime,jdbcType=TIMESTAMP},
      </if>
      <if test="mtime != null">
        #{mtime,jdbcType=TIMESTAMP},
      </if>
      <if test="configType != null">
        #{configType,jdbcType=INTEGER},
      </if>
    </trim>
    <trim prefix="on duplicate key update" suffixOverrides=",">
      <if test="yearName != null">
        year_name = values(year_name),
      </if>
      <if test="yearMoney != null">
        year_money = values(year_money),
      </if>
      <if test="isDeleted != null">
        is_deleted = values(is_deleted),
      </if>
      <if test="ctime != null">
        ctime = values(ctime),
      </if>
      <if test="mtime != null">
        mtime = values(mtime),
      </if>
      <if test="configType != null">
        config_type = values(config_type),
      </if>
    </trim>
  </insert>
</mapper>