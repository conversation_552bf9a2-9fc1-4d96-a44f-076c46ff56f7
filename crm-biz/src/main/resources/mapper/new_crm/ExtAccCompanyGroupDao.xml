<?xml version="1.0" encoding="UTF-8" ?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN"
        "http://mybatis.org/dtd/mybatis-3-mapper.dtd" >
<mapper namespace="com.bilibili.crm.platform.biz.dao.ExtAccCompanyGroupDao">
    <resultMap id="BaseResultMap" type="com.bilibili.crm.platform.biz.po.AccCompanyGroupPo">
        <id column="id" jdbcType="INTEGER" property="id" />
        <result column="name" jdbcType="VARCHAR" property="name" />
        <result column="tag" jdbcType="VARCHAR" property="tag" />
        <result column="is_deleted" jdbcType="TINYINT" property="isDeleted" />
        <result column="ctime" jdbcType="TIMESTAMP" property="ctime" />
        <result column="mtime" jdbcType="TIMESTAMP" property="mtime" />
        <result column="create_user" jdbcType="VARCHAR" property="createUser" />
        <result column="update_user" jdbcType="VARCHAR" property="updateUser" />
        <result column="status" jdbcType="TINYINT" property="status" />
        <result column="quarter_first_industry" jdbcType="INTEGER" property="quarterFirstIndustry" />
        <result column="quarter_second_industry" jdbcType="INTEGER" property="quarterSecondIndustry" />
        <result column="actual_top_first_industry" jdbcType="INTEGER" property="actualTopFirstIndustry" />
        <result column="actual_top_second_industry" jdbcType="INTEGER" property="actualTopSecondIndustry" />
        <result column="industry_change" jdbcType="TINYINT" property="industryChange" />
        <result column="last_quarter_first_industry" jdbcType="INTEGER" property="lastQuarterFirstIndustry" />
        <result column="last_quarter_second_industry" jdbcType="INTEGER" property="lastQuarterSecondIndustry" />
        <result column="quarter_second_industry_ratio" jdbcType="DECIMAL" property="quarterSecondIndustryRatio" />
        <result column="cactual_top_second_industry_ratio" jdbcType="DECIMAL" property="cactualTopSecondIndustryRatio" />
    </resultMap>
    <select id="getAllGroupWithNameStatus" resultMap="BaseResultMap">
        select id,name,status from acc_company_group where is_deleted = 0 order by id ASC;
    </select>

    <select id="getGroupIdWithNameByIds" resultType="com.bilibili.crm.platform.biz.bean.CommonIdAndNameExtPO">
        select id,name from acc_company_group where id IN
        <foreach item="id" collection="groupIds" open="(" separator="," close=")">
            #{id}
        </foreach>
        ;
    </select>

<!--    <select id="getGroupNameAndIdForAgent" resultType="com.bilibili.crm.platform.biz.bean.CommonIdAndNameExtPO">-->
<!--        SELECT t2.id as id,t2.name as name-->
<!--        FROM-->
<!--        (-->
<!--        SELECT group_id FROM acc_account-->
<!--        where-->
<!--        is_deleted = 0  and group_id > 0 and user_type IN (0,1)-->
<!--        <if test="accountIds != null and accountIds.size()>0">-->
<!--            AND account_id IN-->
<!--            <foreach item="item" index="index" collection="accountIds" open="(" close=")"-->
<!--                     separator=",">-->
<!--                #{item, jdbcType=INTEGER}-->
<!--            </foreach>-->
<!--        </if>-->
<!--        <if test="agentId != null">-->
<!--            AND dependency_agent_id = #{agentId}-->
<!--        </if>-->
<!--        ) AS t1-->
<!--        JOIN acc_company_group as t2-->
<!--        ON t1.group_id = t2.id-->
<!--        AND t2.is_deleted = 0-->
<!--        <if test="groupId != null and groupId > 0">-->
<!--            AND t2.id = #{groupId}-->
<!--        </if>-->
<!--        <if test="nameLike != null">-->
<!--            AND t2.name like concat('%',#{nameLike},'%')-->
<!--        </if>-->
<!--        GROUP BY t2.id-->
<!--    </select>-->
</mapper>