package com.bilibili.crm.biz_common.olap;

import com.bilibili.crm.biz_common.olap.config.IEsAppConfig;
import com.bilibili.crm.biz_common.olap.constants.EsIndexEnum;
import com.bilibili.crm.common.AlarmHelper;
import org.slf4j.MDC;

import java.sql.Timestamp;
import java.util.Collection;
import java.util.List;

public abstract class AbstractOlapWithPaladinService<Q extends CommonOlapQuery, T> extends AbstractOlapService<Q, T> {

    private final EsIndexEnum index;
    private final Class<T> cls;

    public AbstractOlapWithPaladinService(EsIndexEnum index, Class<T> cls) {
        this.index = index;
        this.cls = cls;
    }

    @Override
    protected IEsAppConfig appConfig() {
        String appName = "es.app.name." + index.name();
        String token = "es.token." + index.name();
        return new IEsAppConfig() {
            @Override
            public String getIndexes(List<Timestamp> dateList) {
                String val = paladinConfig.getString(appName);
                AlarmHelper.log("EsAppName", index, val);
                return val;
            }

            @Override
            public String getToken() {
                String val = paladinConfig.getString(token);
                AlarmHelper.log("EsToken", index, val);
                return val;
            }
        };
    }

    @Override
    protected Class<T> getCls() {
        return cls;
    }

    public boolean openEsSwitch() {
        String openSwitch = "es.open.switch." + index.name();
        String open = paladinConfig.getString(openSwitch);
        AlarmHelper.log("OpenSwitch", openSwitch, open);
        return Boolean.TRUE.toString().equals(open);
    }
}
