package com.bilibili.crm.biz_common.olap.exception;

import lombok.Getter;

/**
 * <AUTHOR>
 * @date 2024-12-17 11:34
 * olap接口相应异常类
 */
public class OlapResponseException extends RuntimeException {

    private static final long serialVersionUID = 6798044961521082953L;

    @Getter
    private final Integer code;

    @Getter
    private final String message;


    public OlapResponseException (String message) {
        super(message);
        this.message = message;
        this.code = 0;
    }

    public OlapResponseException (Throwable cause, String message) {
        super(cause);
        this.message = message;
        this.code = 0;

    }
}
