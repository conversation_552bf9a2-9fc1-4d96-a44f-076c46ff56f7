package com.bilibili.crm.biz_common.olap.migrate.util;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class GenericComparator {

    private static final int LOG_LEN = 1024 * 16;

    @Autowired
    private PaladinConfig paladinConfig;

    /**
     * 比较两个不同类型对象列表中的字段。
     *
     * @param list1 第一个对象列表
     * @param list2 第二个对象列表
     * @param bizScene 场景码
     * @param <T1> 第一个对象类型
     * @param <T2> 第二个对象类型
     * @param uniqueIdentifierExtractor1 提取第一个对象唯一标识的函数
     * @param uniqueIdentifierExtractor2 提取第二个对象唯一标识的函数
     * @param dateExtractor1 提取第一个对象日期字段的函数（返回 Timestamp）
     * @param dateExtractor2 提取第二个对象日期字段的函数（返回 Timestamp）
     */
    public <T1, T2> void compareAllPairs(List<T1> list1, List<T2> list2, String bizScene,
                                         Function<? super T1, String> uniqueIdentifierExtractor1,
                                         Function<? super T2, String> uniqueIdentifierExtractor2,
                                         Function<? super T1, Timestamp> dateExtractor1,
                                         Function<? super T2, Timestamp> dateExtractor2) {
        log.info("{} GenericComparator start", bizScene);
        if (CollectionUtils.isEmpty(list1) && CollectionUtils.isEmpty(list2)) {
            return;
        }
        try {
            FieldComparisonResult result = doCompare(list1, list2, bizScene, uniqueIdentifierExtractor1, uniqueIdentifierExtractor2, dateExtractor1, dateExtractor2);
            logComparisonResults(result, bizScene);
        } catch (Exception e) {
            String logInfo = String.format("%s GenericComparator error, list1:%s, list2:%s", bizScene, JSON.toJSONString(list1), JSON.toJSONString(list2));
            if (logInfo.length() > LOG_LEN) {
                logInfo = logInfo.substring(0, LOG_LEN);
            }
            log.error(logInfo, e);
        }
    }

    /**
     * 比较两个列表中所有唯一标识字段相等的对象，并记录字段差异和缺失的唯一标识字段。
     *
     * @param list1 第一个对象列表
     * @param list2 第二个对象列表
     * @param bizScene 场景码
     * @param <T1> 第一个对象类型
     * @param <T2> 第二个对象类型
     * @return 包含字段差异和缺失的唯一标识字段的结果对象。
     */
    private <T1, T2> FieldComparisonResult doCompare(List<T1> list1, List<T2> list2, String bizScene,
                                                     Function<? super T1, String> uniqueIdentifierExtractor1,
                                                     Function<? super T2, String> uniqueIdentifierExtractor2,
                                                     Function<? super T1, Timestamp> dateExtractor1,
                                                     Function<? super T2, Timestamp> dateExtractor2) {
        // 获取今天的零点时间戳
        Timestamp todayStartOfDay = Utils.getBeginOfDay(new Timestamp(System.currentTimeMillis()));

        // 过滤掉今天的数据，即只保留日期部分早于今天的记录，通过各自的唯一标识构建两个list各自的map
        Map<String, T1> map1 = list1.stream()
                .filter(item -> shouldIncludeItem(item, dateExtractor1, todayStartOfDay))
                .collect(Collectors.toMap(uniqueIdentifierExtractor1, Function.identity()));
        Map<String, T2> map2 = list2.stream()
                .filter(item -> shouldIncludeItem(item, dateExtractor2, todayStartOfDay))
                .collect(Collectors.toMap(uniqueIdentifierExtractor2, Function.identity()));

        List<FieldInfo> differences = new ArrayList<>();
        List<String> missingIdentifiers = new ArrayList<>();

        // 遍历 map1 中的对象
        for (String identifier : map1.keySet()) {
            if (!map2.containsKey(identifier)) {
                // list1 中存在唯一标识字段，但 list2 中不存在 记录下来
                missingIdentifiers.add(identifier);
                continue;
            }

            // 如果都存在，再比较唯一标识字段相等的对象的字段
            T1 itemFromList1 = map1.get(identifier);
            T2 itemFromList2 = map2.get(identifier);

            differences.addAll(getFieldDifferences(itemFromList1, itemFromList2, bizScene, identifier));
        }
        return new FieldComparisonResult(differences, missingIdentifiers);
    }

    // 判断是否应包含该项
    private <T> boolean shouldIncludeItem(T item, Function<? super T, Timestamp> dateExtractor, Timestamp todayStartOfDay) {
        if (dateExtractor == null) {
            return true; // 如果没有提供日期提取函数，则不排除任何项
        }
        Timestamp itemDate = dateExtractor.apply(item);
        return itemDate != null && itemDate.before(todayStartOfDay);
    }

    /**
     * 获取两个对象之间的字段差异。
     *
     * @param item1 第一个对象实例
     * @param item2 第二个对象实例
     * @param bizScene 场景码
     * @param <T1> 第一个对象类型
     * @param <T2> 第二个对象类型
     * @return 字段差异列表
     */
    private <T1, T2> List<FieldInfo> getFieldDifferences(T1 item1, T2 item2, String bizScene, String identifier) {
        List<FieldInfo> differences = new ArrayList<>();
        Class<?> clazz1 = item1.getClass();
        Class<?> clazz2 = item2.getClass();

        // 获取公共字段（即两个对象中都存在的字段）
        Set<String> commonFields = Arrays.stream(clazz1.getDeclaredFields())
                .map(Field::getName)
                .collect(Collectors.toSet());
        commonFields.retainAll(Arrays.stream(clazz2.getDeclaredFields())
                .map(Field::getName)
                .collect(Collectors.toSet()));

        try {
            for (String fieldName : commonFields) {
                if (paladinConfig.getStringOrDefault(bizScene + "SimulationBlackList", "-").contains(fieldName)) {
                    continue;
                }

                Field field1 = clazz1.getDeclaredField(fieldName);
                Field field2 = clazz2.getDeclaredField(fieldName);

                field1.setAccessible(true); // 允许访问私有字段
                field2.setAccessible(true);

                Object value1 = field1.get(item1);
                Object value2 = field2.get(item2);

                if (value1 != null && !value1.equals(value2)) {
                    differences.add(new FieldInfo(identifier, fieldName, value1, value2));
                }
            }
        } catch (Exception e) {
            throw new RuntimeException("Error accessing field values", e);
        }

        return differences;
    }

    /**
     * 通过反射调用 getter 方法。
     *
     * @param obj 对象实例
     * @param methodName 方法名
     * @return 方法返回值
     * @throws IllegalAccessException 如果无法访问方法
     */
    private <T> String invokeGetterMethod(T obj, String methodName) {
        try {
            Method method = obj.getClass().getMethod(methodName);
            Object result = method.invoke(obj);
            return result != null ? result.toString() : null;
        } catch (Exception e) {
            throw new RuntimeException("Error invoking method: " + methodName, e);
        }
    }

    /**
     * 日志记录比较结果。
     *
     * @param result 比较结果
     * @param bizScene 场景码
     */
    private void logComparisonResults(FieldComparisonResult result, String bizScene) {
        if (!CollectionUtils.isEmpty(result.getMissingIdentifiers()) && !paladinConfig.switchOn(bizScene + "missingIdentifiersDegrade")) {
            String logInfo = bizScene
                    + " GenericComparator, 老es存在但新es不存在, 缺失的 Identifiers:"
                    + result.getMissingIdentifiers();
            if (logInfo.length() > LOG_LEN) {
                logInfo = logInfo.substring(0, LOG_LEN);
            }
            log.info(logInfo);
        }
        if (!CollectionUtils.isEmpty(result.getDifferences()) && !paladinConfig.switchOn(bizScene + "differencesDegrade")) {
            String logInfo = bizScene
                    + " GenericComparator, 老es和新es字段不相等, differences:"
                    + result.getDifferences();
            if (logInfo.length() > LOG_LEN) {
                logInfo = logInfo.substring(0, LOG_LEN);
            }
            log.info(logInfo);
        }
    }

    public class FieldComparisonResult {

        private final List<FieldInfo> differences;
        private final List<String> missingIdentifiers;

        public FieldComparisonResult(List<FieldInfo> differences, List<String> missingIdentifiers) {
            this.differences = differences;
            this.missingIdentifiers = missingIdentifiers;
        }

        public List<FieldInfo> getDifferences() {
            return differences;
        }

        public List<String> getMissingIdentifiers() {
            return missingIdentifiers;
        }

        @Override
        public String toString() {
            return GsonUtils.toJson(this);
        }
    }

    public class FieldInfo {

        private final String id;
        private final String n;
        private final Object v1;
        private final Object v2;

        public FieldInfo(String id, String n, Object v1, Object v2) {
            this.id = id;
            this.n = n;
            this.v1 = v1;
            this.v2 = v2;
        }

        public String getId() {
            return id;
        }

        public String getN() {
            return n;
        }

        public Object getV1() {
            return v1;
        }

        public Object getV2() {
            return v2;
        }

        @Override
        public String toString() {
            return GsonUtils.toJson(this);
        }
    }
}
