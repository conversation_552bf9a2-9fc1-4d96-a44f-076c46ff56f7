package com.bilibili.crm.biz.merchants.bo.oa;

import com.alibaba.fastjson.annotation.JSONField;
import com.bilibili.crm.biz.oa.bo.OaDocModel;
import com.bilibili.crm.biz.oa.bo.OaRadioButtonModel;
import com.bilibili.crm.biz.oa.bo.OaStaffModel;
import lombok.Data;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/9
 */
@Data
public class MerchantsOaForm {


    @JSONField(name = "input_d71sm1k8")
    private String projectName;

    @JSONField(name = "input_mats03qg")
    private String projectId;

    @JSONField(name = "input_865k7gk")
    private String projectType;

    @JSONField(name = "radio_2_nr4ul7k8")
    private OaRadioButtonModel projectGeneralType;

    @JSONField(name = "radio_2_qq8hu6lo")
    private OaRadioButtonModel merchantMarketingTeam;

    @JSONField(name = "radio_2_13q6b13o")
    private OaRadioButtonModel projectCreateType;

    @JSONField(name = "input_dav3bhk8")
    private String areaInfo;

    @JSONField(name = "input_2ktaa4vo")
    private String productionId;

    @JSONField(name = "input_ssk71nag")
    private String productionName;

    @JSONField(name = "radio_2_sv2pvli8")
    private OaRadioButtonModel investmentLevelType;

    @JSONField(name = "datepicker_i28ig76")
    private String investmentBeginTime;

    @JSONField(name = "datepicker_s8d5ge8o")
    private String investmentEndTime;

    @JSONField(name = "datepicker_hl5im25o")
    private String validBeginDay;

    @JSONField(name = "datepicker_1k6btm4o")
    private String validEndDay;

    @JSONField(name = "input_un0f9ufg")
    private String investmentTargetMoney;

    @JSONField(name = "input_m4ealmi")
    private String estimateProjectCost;

    @JSONField(name = "input_e7fmribo")
    private String businessContact;

    @JSONField(name = "textarea_luirgtj8")
    private String mainContact;

    @JSONField(name = "input_1d2556r8")
    private String reportedSaleName;

    @JSONField(name = "textarea_kiicjjvo")
    private String briefIntroduction;

    @JSONField(name = "img_preview_n7gl9kno")
    private List<OaDocModel> projectPicture;

    @JSONField(name = "rich_text_n1ebe22o")
    private String investmentPlanLink;

    @JSONField(name = "rich_text_7ikicsug")
    private String investmentAttachmentsLink;

    @JSONField(name = "table_pm9d17m")
    private List<MerchantsTableForm> pricingInfos;

    @JSONField(name = "input_hu3a1bko")
    private Long stockSumMoney;

    @JSONField(name = "rich_text_7jn7fhu")
    private String projectResourceAttachment;

    @JSONField(name = "staffpicker_1psao25g")
    private List<OaStaffModel> approver;

    @JSONField(name = "staffpicker_2q4vpug")
    private List<OaStaffModel> carbonCopy;

    @JSONField(name = "input_2jjf6g8g")
    private String saleGroupName;

    @Data
    public static class MerchantsTableForm {

        @JSONField(name = "input_eae3jqo")
        private String seatType;

        @JSONField(name = "input_number_47mojpq")
        private Long seatCount;

        @JSONField(name = "input_ulq7bffg")
        private String seatTypeDesc;

        @JSONField(name = "input_number_p7gt1ieg")
        private Long seatMoney;

        @JSONField(name = "input_number_l5rqp0d")
        private Long seatSumMoney;
    }
}
