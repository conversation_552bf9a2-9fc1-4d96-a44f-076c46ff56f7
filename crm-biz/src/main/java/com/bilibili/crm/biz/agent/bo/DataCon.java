package com.bilibili.crm.biz.agent.bo;

import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;

import java.util.List;

@Data
public class DataCon {
    private List<Integer> customerIds;
    private List<Integer> accountIds;

    public static DataCon empty() {
        return new DataCon();
    }

    public boolean isEmpty() {
        return CollectionUtils.isEmpty(customerIds) && CollectionUtils.isEmpty(accountIds);
    }
}
