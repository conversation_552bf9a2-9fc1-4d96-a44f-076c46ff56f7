package com.bilibili.crm.biz.merchants.service;

import com.bilibili.crm.biz.merchants.dto.MerchantsBsiInfoDto;
import com.bilibili.crm.biz.merchants.dto.MerchantsBsiProjectInfoDto;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/12/15
 */
public interface MerchantsBsiService {

    List<MerchantsBsiProjectInfoDto> queryMerchantsBsiInfo(String username, List<Integer> projectIds);

    List<MerchantsBsiInfoDto> queryBsiInfo(String username, List<Integer> projectIds);
}
