package com.bilibili.crm.biz.wallet.olap;

import com.bilibili.crm.biz.wallet.bean.WalletLogEsBean;
import com.bilibili.crm.biz.wallet.bean.WalletLogQuery;
import com.bilibili.crm.biz.wallet.config.WalletLogEsConfig;
import com.bilibili.crm.biz_common.olap.AbstractOlapService;
import com.bilibili.crm.biz_common.olap.config.IEsAppConfig;
import com.bilibili.crm.biz_common.olap.util.OlapUtil;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.List;

import static org.elasticsearch.index.query.QueryBuilders.boolQuery;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;

@Slf4j
@Service
public class WalletLogOlapService extends AbstractOlapService<WalletLogQuery, WalletLogEsBean> {

    @Autowired
    private WalletLogEsConfig walletLogEsConfig;
    @Autowired
    private OlapUtil olapUtil;

    @Override
    protected IEsAppConfig appConfig() {
        return walletLogEsConfig;
    }

    @Override
    protected Class<WalletLogEsBean> getCls() {
        return WalletLogEsBean.class;
    }

    @Override
    protected BoolQueryBuilder buildBoolQuery(WalletLogQuery query) {

        // 综合查询条件
        BoolQueryBuilder boolQuery = boolQuery();
        // 默认条件
//        olapUtil.termFilter(IsDeleted.VALID::getCode, "is_deleted", boolQuery);

        // 日期
        olapUtil.termDateFilter(query::getDate, "date", boolQuery);

        // 日期范围
        olapUtil.rangeDateFilter(query::getDateBegin, query::getDateEnd, "date", boolQuery);

        // 账号id
        olapUtil.termFilter(query::getAccountId, "account_id", boolQuery);

        // 账号ids
        olapUtil.termFilterList(query::getAccountIds, "account_id", boolQuery);

        // 交易类型
        olapUtil.rangeIntegerFilter(query::getOperationType, query::getOperationType, "operation_type", boolQuery);

        // 交易类型list
        if (!CollectionUtils.isEmpty(query.getOperationTypes())) {
            BoolQueryBuilder operationQueryShouldBuilder = boolQuery();
            for (Integer operationType : query.getOperationTypes()) {
                operationQueryShouldBuilder.should(rangeQuery("operation_type").from(operationType).to(operationType));
            }
            boolQuery.filter(operationQueryShouldBuilder);
        }

        // 用户类型list
        if (!CollectionUtils.isEmpty(query.getUserTypes())) {
            BoolQueryBuilder userTypeQueryShouldBuilder = boolQuery();
            for (Integer userType : query.getUserTypes()) {
                userTypeQueryShouldBuilder.should(rangeQuery("user_type").from(userType).to(userType));
            }
            boolQuery.filter(userTypeQueryShouldBuilder);
        }

        // 起飞托管专用条件，场景码：getFlyWithholdMoneyByDate
        if (query.isFlyWithhold()) {
            BoolQueryBuilder flyQuery = boolQuery();
            flyQuery.should(rangeQuery("red_packet").gt(0));
            flyQuery.should(rangeQuery("trust_fund").gt(0));
            flyQuery.should(rangeQuery("withhold_fund").gt(0));
            flyQuery.should(rangeQuery("trust_incentive").gt(0));
            flyQuery.should(rangeQuery("trust_cash").gt(0));
            flyQuery.should(rangeQuery("trust_fly_coin").gt(0));
            boolQuery.must(flyQuery);
        }


        // range
        olapUtil.gteLong(query.getIdBegin(), "id", boolQuery);
        olapUtil.lteLong(query.getIdEnd(), "id", boolQuery);

        return boolQuery;
    }

    public List<WalletLogEsBean> queryForTest (WalletLogQuery query) {

        return doBatchSearch(query);
    }
}
