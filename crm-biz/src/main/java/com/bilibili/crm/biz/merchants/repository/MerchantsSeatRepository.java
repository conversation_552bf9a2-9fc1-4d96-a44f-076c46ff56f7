package com.bilibili.crm.biz.merchants.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bilibili.crm.biz.merchants.convert.MerchantsSeatConvert;
import com.bilibili.crm.biz.merchants.dao.ad.MerchantsMainSeatMapper;
import com.bilibili.crm.biz.merchants.dto.ProjectPricingDto;
import com.bilibili.crm.biz.merchants.po.MerchantsMainSeatPo;
import com.bilibili.crm.biz.merchants.po.MerchantsProjectFlowRecordsPo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/3/25
 */
@Slf4j
@Repository
public class MerchantsSeatRepository {

    @Resource
    private MerchantsMainSeatMapper merchantsMainSeatMapper;


    public List<MerchantsMainSeatPo> queryByProjectId(Integer projectId) {
        LambdaQueryWrapper<MerchantsMainSeatPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MerchantsMainSeatPo::getProjectId, projectId);
        wrapper.eq(MerchantsMainSeatPo::getIsDeleted, 0);
        return merchantsMainSeatMapper.selectList(wrapper);
    }

    public void update(List<Long> seatIds, Byte status) {
        LambdaQueryWrapper<MerchantsMainSeatPo> wrapper = Wrappers.lambdaQuery();
        wrapper.in(MerchantsMainSeatPo::getId, seatIds);
        MerchantsMainSeatPo merchantsMainSeatPo = new MerchantsMainSeatPo();
        merchantsMainSeatPo.setStatus(status);
        merchantsMainSeatMapper.update(merchantsMainSeatPo, wrapper);
    }

    public void batchInsert(List<ProjectPricingDto> pricingDtos) {
        if (CollectionUtils.isEmpty(pricingDtos)) {
            return;
        }

        pricingDtos.forEach(dto -> {
            MerchantsMainSeatPo convert = MerchantsSeatConvert.convert(dto);
            merchantsMainSeatMapper.insert(convert);
        });
    }

    public void deletedById(MerchantsMainSeatPo po) {
        po.setIsDeleted(1);
        merchantsMainSeatMapper.updateById(po);
    }

    public void batchUpdate(List<MerchantsMainSeatPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }
        pos.forEach(po -> merchantsMainSeatMapper.updateById(po));
    }

    public void updateStatusByProjectId(Integer projectId, Byte status) {
        LambdaQueryWrapper<MerchantsMainSeatPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(MerchantsMainSeatPo::getProjectId, projectId);
        wrapper.eq(MerchantsMainSeatPo::getIsDeleted, 0);
        MerchantsMainSeatPo merchantsMainSeatPo = new MerchantsMainSeatPo();
        merchantsMainSeatPo.setStatus(status);
        merchantsMainSeatMapper.update(merchantsMainSeatPo, wrapper);
    }
}
