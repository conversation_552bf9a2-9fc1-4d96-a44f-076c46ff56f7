package com.bilibili.crm.biz.merchants.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.biz.merchants.bo.QueryPackageWorkOrderParam;
import com.bilibili.crm.biz.merchants.convert.WorkOrderConvert;
import com.bilibili.crm.biz.merchants.dao.ad.PackageWorkOrderDetailMapper;
import com.bilibili.crm.biz.merchants.dao.ad.PackageWorkOrderMapper;
import com.bilibili.crm.biz.merchants.dto.PackageInfoDto;
import com.bilibili.crm.biz.merchants.dto.PackageWorkOrderDto;
import com.bilibili.crm.biz.merchants.dto.WorkOrderResourceDto;
import com.bilibili.crm.biz.merchants.enums.PackageWorkOrderStatusEnum;
import com.bilibili.crm.biz.merchants.enums.PackageWorkOrderTypeEnum;
import com.bilibili.crm.biz.merchants.po.MerchantsProjectInfoPo;
import com.bilibili.crm.biz.merchants.po.PackageWorkOrderDetailPo;
import com.bilibili.crm.biz.merchants.po.PackageWorkOrderPo;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/26
 */
@Repository
public class PackageWorkOrderRepository {

    @Resource
    private PackageWorkOrderMapper packageWorkOrderMapper;

    @Resource
    private PackageWorkOrderDetailMapper packageWorkOrderDetailMapper;

    @Resource
    private MerchantsRepository merchantsRepository;


    public PackageWorkOrderPo queryById(Long workOrderId) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PackageWorkOrderPo::getId, workOrderId);
        List<PackageWorkOrderPo> pos = packageWorkOrderMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.get(0);
    }

    public PackageInfoDto queryDtoById(Long workOrderId) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PackageWorkOrderPo::getId, workOrderId);
        List<PackageWorkOrderPo> pos = packageWorkOrderMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        PackageInfoDto packageInfoDto = new PackageInfoDto();
        PackageWorkOrderPo workOrderPo = pos.get(0);
        Integer projectId = workOrderPo.getProjectId();
        MerchantsProjectInfoPo projectInfoPo = merchantsRepository.queryMainById(projectId);
        packageInfoDto.setProjectInfoPo(projectInfoPo);
        packageInfoDto.setPackageWorkOrderPo(workOrderPo);

        LambdaQueryWrapper<PackageWorkOrderDetailPo> detailWrapper = Wrappers.lambdaQuery();
        detailWrapper.eq(PackageWorkOrderDetailPo::getWorkOrderId, workOrderId);
        List<PackageWorkOrderDetailPo> detailPos = packageWorkOrderDetailMapper.selectList(detailWrapper);
        packageInfoDto.setDetailPos(detailPos);
        return packageInfoDto;
    }


    public Pagination<List<PackageWorkOrderPo>> pageQuery(QueryPackageWorkOrderParam param) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = buildWrapper(param);
        Page<PackageWorkOrderPo> result = packageWorkOrderMapper.selectPage(new Page<>(param.getPage(), param.getSize()), wrapper);
        List<PackageWorkOrderPo> records = result.getRecords();
        return new Pagination<>((int) result.getPages(), (int) result.getTotal(), records);
    }

    public List<PackageInfoDto> batchQuery(QueryPackageWorkOrderParam param) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = buildWrapper(param);
        List<PackageWorkOrderPo> packageWorkOrderPos = packageWorkOrderMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(packageWorkOrderPos)) {
            return Collections.emptyList();
        }

        List<PackageInfoDto> result = new ArrayList<>();
        List<Long> workOrderIds = packageWorkOrderPos.stream().map(PackageWorkOrderPo::getId).collect(Collectors.toList());

        Map<Long, List<PackageWorkOrderDetailPo>> map = new HashMap<>();
        if (param.getQueryResource() != null && param.getQueryResource()) {
            LambdaQueryWrapper<PackageWorkOrderDetailPo> detailWrapper = Wrappers.lambdaQuery();
            detailWrapper.in(PackageWorkOrderDetailPo::getWorkOrderId, workOrderIds);
            List<PackageWorkOrderDetailPo> packageWorkOrderDetailPos = packageWorkOrderDetailMapper.selectList(detailWrapper);
            map = packageWorkOrderDetailPos.stream().collect(Collectors.groupingBy(PackageWorkOrderDetailPo::getWorkOrderId));
        }

        for (PackageWorkOrderPo po : packageWorkOrderPos) {
            PackageInfoDto packageInfoDto = new PackageInfoDto();
            packageInfoDto.setPackageWorkOrderPo(po);
            List<PackageWorkOrderDetailPo> detailPos = map.get(po.getId());
            packageInfoDto.setDetailPos(detailPos);
            result.add(packageInfoDto);
        }
        return result;
    }

    public void updateDisable(PackageWorkOrderPo po) {
        po.setTopVersionEnable((byte) 0);
        if (PackageWorkOrderStatusEnum.WAITING_APPROVE.getCode().equals(po.getStatus())) {
            po.setStatus(PackageWorkOrderStatusEnum.DISCARD.getCode());
        }
        packageWorkOrderMapper.updateById(po);
    }

    public void updatePo(PackageWorkOrderPo po) {
        packageWorkOrderMapper.updateById(po);
    }

    public void batchUpdateByParam(Integer projectId, Byte type, Byte status, Byte currentStatus) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PackageWorkOrderPo::getProjectId, projectId);
        wrapper.eq(PackageWorkOrderPo::getTopVersionEnable, 1);
        wrapper.eq(PackageWorkOrderPo::getType, type);
        wrapper.eq(PackageWorkOrderPo::getStatus, currentStatus);

        PackageWorkOrderPo workOrderPo = new PackageWorkOrderPo();
        workOrderPo.setStatus(status);
        packageWorkOrderMapper.update(workOrderPo, wrapper);
    }

    public void batchUpdatePo(List<PackageWorkOrderPo> pos) {
        pos.forEach(po -> packageWorkOrderMapper.updateById(po));
    }

    public Long insert(PackageWorkOrderDto packageWorkOrderDto) {
        PackageWorkOrderPo packageWorkOrderPo = WorkOrderConvert.convert(packageWorkOrderDto);
        packageWorkOrderMapper.insert(packageWorkOrderPo);
        Long workOrderId = packageWorkOrderPo.getId();

        List<WorkOrderResourceDto> resources = packageWorkOrderDto.getResources();
        if (CollectionUtils.isNotEmpty(resources)) {
            resources.forEach(d -> {
                PackageWorkOrderDetailPo workOrderDetailPo = WorkOrderConvert.convert(d, workOrderId);
                packageWorkOrderDetailMapper.insert(workOrderDetailPo);
            });
        }

        List<WorkOrderResourceDto> defineResources = packageWorkOrderDto.getDefineResources();
        if (CollectionUtils.isNotEmpty(defineResources)) {
            defineResources.forEach(d -> {
                PackageWorkOrderDetailPo workOrderDetailPo = WorkOrderConvert.convert(d, workOrderId);
                packageWorkOrderDetailMapper.insert(workOrderDetailPo);
            });
        }
        return workOrderId;
    }

    public List<PackageWorkOrderPo> batchQueryPo(QueryPackageWorkOrderParam param) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = buildWrapper(param);
        return packageWorkOrderMapper.selectList(wrapper);
    }

    public List<PackageWorkOrderDetailPo> queryResourceById(Long workOrderId) {
        LambdaQueryWrapper<PackageWorkOrderDetailPo> wrapper = Wrappers.lambdaQuery();
        wrapper.eq(PackageWorkOrderDetailPo::getId, workOrderId);
        return packageWorkOrderDetailMapper.selectList(wrapper);
    }


    private LambdaQueryWrapper<PackageWorkOrderPo> buildWrapper(QueryPackageWorkOrderParam param) {
        LambdaQueryWrapper<PackageWorkOrderPo> wrapper = Wrappers.lambdaQuery();

        if (null != param.getProjectId()) {
            wrapper.eq(PackageWorkOrderPo::getProjectId, param.getProjectId());
        }

        if (StringUtils.isNotEmpty(param.getWorkOrderType())) {
            Byte code = PackageWorkOrderTypeEnum.getCode(param.getWorkOrderType());
            wrapper.eq(PackageWorkOrderPo::getType, code);
        }

        if (null != param.getSeatId()) {
            wrapper.eq(PackageWorkOrderPo::getSeatId, param.getSeatId());
        }

        if (null != param.getCustomerId()) {
            wrapper.eq(PackageWorkOrderPo::getCustomerId, param.getCustomerId());
        }

        if (CollectionUtils.isNotEmpty(param.getProjectIds())) {
            wrapper.in(PackageWorkOrderPo::getProjectId, param.getProjectIds());
        }

        if (CollectionUtils.isNotEmpty(param.getSeatIds())) {
            wrapper.in(PackageWorkOrderPo::getSeatId, param.getSeatIds());
        }

        if (null != param.getWorkOrderId()) {
            wrapper.eq(PackageWorkOrderPo::getId, param.getWorkOrderId());
        }

        if (null != param.getStatus()) {
            wrapper.eq(PackageWorkOrderPo::getStatus, param.getStatus());
        }

        // 默认只查生效的
        if (null == param.getQueryAll()) {
            wrapper.eq(PackageWorkOrderPo::getTopVersionEnable, 1);
        }

        wrapper.orderByDesc(PackageWorkOrderPo::getMtime);
        return wrapper;
    }
}
