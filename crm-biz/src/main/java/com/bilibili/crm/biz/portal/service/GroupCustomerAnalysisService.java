package com.bilibili.crm.biz.portal.service;

import com.bilibili.crm.platform.biz.crm_ck.dto.CustomerGroupAnalysisListResultInfoPO;
import com.bilibili.crm.platform.biz.crm_ck.dto.CustomerGroupAnalysisListResultKpiPO;
import com.bilibili.crm.platform.biz.crm_ck.dto.CrmKpiAmountDataWithCustomerSaleParam;

import java.util.List;

/**
 * <AUTHOR>
 * date 2025/1/6 15:22.
 * Contact: <EMAIL>.
 */
public interface GroupCustomerAnalysisService {

    List<CustomerGroupAnalysisListResultKpiPO> getCustomerKpiList(CrmKpiAmountDataWithCustomerSaleParam queryParam);
    List<CustomerGroupAnalysisListResultInfoPO> getCustomerInfoList(CrmKpiAmountDataWithCustomerSaleParam queryParam);

    List<CustomerGroupAnalysisListResultKpiPO> exportCustomerKpiList(CrmKpiAmountDataWithCustomerSaleParam queryParam);
    List<CustomerGroupAnalysisListResultInfoPO> exportCustomerInfoList(CrmKpiAmountDataWithCustomerSaleParam queryParam);

    List<CustomerGroupAnalysisListResultKpiPO> getGroupKpiList(CrmKpiAmountDataWithCustomerSaleParam queryParam);
    List<CustomerGroupAnalysisListResultInfoPO> getGroupInfoList(CrmKpiAmountDataWithCustomerSaleParam queryParam);

    List<CustomerGroupAnalysisListResultKpiPO> exportGroupKpiList(CrmKpiAmountDataWithCustomerSaleParam queryParam);
    List<CustomerGroupAnalysisListResultInfoPO> exportGroupInfoList(CrmKpiAmountDataWithCustomerSaleParam queryParam);

    List<CustomerGroupAnalysisListResultKpiPO> getCustomerProductKpiList(CrmKpiAmountDataWithCustomerSaleParam queryParam);
    List<CustomerGroupAnalysisListResultInfoPO> getCustomerProductInfoList(CrmKpiAmountDataWithCustomerSaleParam queryParam);
}

