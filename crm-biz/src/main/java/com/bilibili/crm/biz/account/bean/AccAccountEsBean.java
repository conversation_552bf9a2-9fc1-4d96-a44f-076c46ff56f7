package com.bilibili.crm.biz.account.bean;

import com.bilibili.crm.biz_common.olap.UniqKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024-08-27 22:10
 * 账号宽表ES Bean
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AccAccountEsBean implements Serializable {

    private static final long serialVersionUID = -4210064356000173924L;

    @UniqKey(esFieldName = "account_id", esSortOrder = SortOrder.DESC)
    private Integer accountId;
    private Integer adStatus;
    private Integer accountStatus;
    private Integer accountType;
    private Integer customerId;
    private String creator;
    private Integer groupId;
    private Integer isAgent;
    private Integer isInner;
    private Integer productId;
    private Integer productLineId;
    private Integer status;
    private Integer userType;
    private Integer autoUpdateLabel;
    private Integer agentType;
    private Integer dependencyAgentId;
    private Integer departmentId;
    private Integer commerceCategoryFirstId;
    private Integer commerceCategorySecondId;
    private Integer allowCashPay;
    private Integer allowFlowTicketPay;
    private Integer allowFlyCoinPay;
    private Integer allowSigningBonusPay;
    private Integer isSupportCluePass;
    private Integer isSupportDpa;
    private Integer isSupportContent;
    private Integer isSupportFly;
    private Integer isSupportGame;
    private Integer isSupportLocalAd;
    private Integer isSupportMas;
    private Integer isSupportPickup;
    private Integer isSupportSeller;
    private Integer gdStatus;
    private Long mid;
    private Timestamp ctime;
    private Timestamp mtime;
    private String name;
    private String username;
    private String companyName;
    private Integer showToCustomer;
    /**
     * 今日花费（单位 毫分）
     */
    private Long todayCost;
    /**
     * 现金余额（单位 分）
     */
    private Long cash;
    /**
     * 返货余额（单位 分）
     */
    private Long redPacket;
    /**
     * 专返余额（单位 分）
     */
    private Long specialRedPacket;
    /**
     * 授信余额（单位 分）
     */
    private Long credit;
    /**
     * 广告主客户名称
     */
    private String customerUserName;
    /**
     * 统一一级行业分类
     */
    private Integer unitedFirstIndustryId;
    private String unitedFirstIndustryName;
    /**
     * 统一二级行业分类
     */
    private Integer unitedSecondIndustryId;
    private String unitedSecondIndustryName;
    /**
     * 统一三级行业分类
     */
    private Integer unitedThirdIndustryId;
    private String unitedThirdIndustryName;
    /**
     * 客户运营标签类型 1-直签 2-效果服务商
     */
    private Integer customerOperateLabelType;
    /**
     * 客户运营标签服务开始时间
     */
    private Long customerOperateLabelBeginTime;
    /**
     * 客户运营标签服务结束时间
     */
    private Long customerOperateLabelEndTime;

    /**
     * 直客销售
     */
    private List<Integer> directSaleId;

    /**
     * 渠道销售
     */
    private List<Integer> channelSaleId;

    /**
     * customer表的一级行业分类ID
     */
    private Integer customerBizIndustryCategoryFirstId;

    /**
     * customer表的二级行业分类ID
     */
    private Integer customerBizIndustryCategorySecondId;

    /**
     * customer表的统一一级行业分类ID
     */
    private Integer customerUnitedFirstIndustryId;

    /**
     * customer表的统一二级行业分类ID
     */
    private Integer customerUnitedSecondIndustryId;

    /**
     * customer表的统一三级行业分类ID
     */
    private Integer customerUnitedThirdIndustryId;

    /**
     * 产品名称
     */
    private String productName;
    /**
     * 公司组名称
     */
    private String companyGroupName;
}
