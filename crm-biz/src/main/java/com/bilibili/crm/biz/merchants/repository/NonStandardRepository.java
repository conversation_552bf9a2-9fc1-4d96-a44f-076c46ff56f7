package com.bilibili.crm.biz.merchants.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.bilibili.crm.biz.merchants.bo.QueryNonStandardReq;
import com.bilibili.crm.biz.merchants.convert.NonStandardConvert;
import com.bilibili.crm.biz.merchants.dao.ad.NonStandardResourceMapper;
import com.bilibili.crm.biz.merchants.dto.OffStandardResourceDto;
import com.bilibili.crm.biz.merchants.po.NonStandardResourcePo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/3/3
 */
@Repository
@Slf4j
public class NonStandardRepository {

    @Resource
    private NonStandardResourceMapper nonStandardResourceMapper;


    public void batchInsert(List<OffStandardResourceDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            log.info("NonStandardRepository-batchInsert size is empty");
            return;
        }
        dtos.forEach(dto -> {
            NonStandardResourcePo po = NonStandardConvert.convert(dto);
            nonStandardResourceMapper.insert(po);
            po.setResourceId("FB" + po.getId());
            nonStandardResourceMapper.updateById(po);
        });
    }

    public void updateStatus(String resourceId, Byte status) {
        NonStandardResourcePo nonStandardResourcePo = new NonStandardResourcePo();
        nonStandardResourcePo.setStatus(status);
        LambdaQueryWrapper<NonStandardResourcePo> wrapper = Wrappers.lambdaQuery(NonStandardResourcePo.class);
        wrapper.eq(NonStandardResourcePo::getResourceId, resourceId);
        nonStandardResourceMapper.update(nonStandardResourcePo, wrapper);
    }

    public List<OffStandardResourceDto> query(QueryNonStandardReq queryNonStandardReq) {
        LambdaQueryWrapper<NonStandardResourcePo> wrapper = Wrappers.lambdaQuery(NonStandardResourcePo.class);
        if (null != queryNonStandardReq.getStatus()) {
            wrapper.eq(NonStandardResourcePo::getStatus, queryNonStandardReq.getStatus());
        } else {
            wrapper.ne(NonStandardResourcePo::getStatus, 2);
        }

        if (StringUtils.isNotBlank(queryNonStandardReq.getResourceId())) {
            wrapper.eq(NonStandardResourcePo::getResourceId, queryNonStandardReq.getResourceId());
        }

        if (StringUtils.isNotBlank(queryNonStandardReq.getResourceName())) {
            wrapper.like(NonStandardResourcePo::getResourceName, queryNonStandardReq.getResourceName());
        }

        if (StringUtils.isNotBlank(queryNonStandardReq.getTabType())) {
            wrapper.eq(NonStandardResourcePo::getType, queryNonStandardReq.getTabType());
        }

        if (CollectionUtils.isNotEmpty(queryNonStandardReq.getResourceIds())) {
            wrapper.in(NonStandardResourcePo::getResourceId, queryNonStandardReq.getResourceIds());
        }

        if (StringUtils.isNotBlank(queryNonStandardReq.getProjectType())) {
            wrapper.and(wq -> wq.like(NonStandardResourcePo::getProjectType, "/" + queryNonStandardReq.getProjectType() + "/")
                    .or().eq(NonStandardResourcePo::getProjectType, ""));
        }

        if (StringUtils.isNotBlank(queryNonStandardReq.getProjectLevel())) {
            wrapper.like(NonStandardResourcePo::getProjectLevel, "/" + queryNonStandardReq.getProjectLevel() + "/");
        }

        List<NonStandardResourcePo> pos = nonStandardResourceMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(NonStandardConvert::convert).collect(Collectors.toList());
    }

    public OffStandardResourceDto queryByResourceId(String resourceId) {
        LambdaQueryWrapper<NonStandardResourcePo> wrapper = Wrappers.lambdaQuery(NonStandardResourcePo.class);
        wrapper.eq(NonStandardResourcePo::getResourceId, resourceId);
        List<NonStandardResourcePo> pos = nonStandardResourceMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return NonStandardConvert.convert(pos.get(0));
    }

    public List<OffStandardResourceDto> queryByResourceIds(List<String> resourceIds) {
        LambdaQueryWrapper<NonStandardResourcePo> wrapper = Wrappers.lambdaQuery(NonStandardResourcePo.class);
        wrapper.in(NonStandardResourcePo::getResourceId, resourceIds);
        List<NonStandardResourcePo> pos = nonStandardResourceMapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.stream().map(NonStandardConvert::convert).collect(Collectors.toList());
    }
}
