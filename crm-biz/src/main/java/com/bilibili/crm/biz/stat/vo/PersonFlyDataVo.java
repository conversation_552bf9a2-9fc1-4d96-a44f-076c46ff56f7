package com.bilibili.crm.biz.stat.vo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonFlyDataVo {
    @ExcelResources(title = "投放日期")
    @ApiModelProperty("投放日期")
    private String date;

    @ExcelResources(title = "ID")
    @ApiModelProperty("账号ID")
    private Integer account_id;

    @ExcelResources(title = "广告主名称")
    @ApiModelProperty("账号名称")
    private String account_name;

    @ExcelResources(title = "所属部门")
    @ApiModelProperty("所属部门")
    private String department_name;

    @ExcelResources(title = "支付方式描述")
    @ApiModelProperty("支付方式描述")
    private String pay_type_desc;

    @ApiModelProperty("支付方式")
    private Integer pay_type;

    @ExcelResources(title = "是否托管")
    @ApiModelProperty("是否托管")
    /**
     * 是否托管资金池
     */
    private String is_withhold;

    @ExcelResources(title = "投放目标")
    @ApiModelProperty("投放目标")
    private String target;

//    @ExcelResources(title = "支付方式描述")
//    @ApiModelProperty("支付方式描述")
//    private String pay_channel_desc;

    @ExcelResources(title = "订单号")
    @ApiModelProperty("订单号")
    private String order_id;

    @ExcelResources(title = "整体总消耗(元)")
    @ApiModelProperty("整体总消耗(元)")
    private BigDecimal total_consume;
    @ExcelResources(title = "扶持前现金消耗(元)")
    @ApiModelProperty("扶持前现金消耗(元)")
    private BigDecimal total_cash_consume;
    @ExcelResources(title = "扶持返货现金消耗(元)")
    @ApiModelProperty("扶持返货现金消耗(元)")
    private BigDecimal support_cash_consume;
    @ExcelResources(title = "券消耗(元)")
    @ApiModelProperty("券消耗(元)")
    private BigDecimal total_coupon_consume;
    @ExcelResources(title = "总充值(元)")
    @ApiModelProperty("总充值(元)")
    private BigDecimal recharge_amount;
    @ExcelResources(title = "现金充值(元)")
    @ApiModelProperty("现金充值(元)")
    private BigDecimal cash_recharge_amount;
    @ExcelResources(title = "券充值(元)")
    @ApiModelProperty("券充值(元)")
    private BigDecimal coupon_recharge_amount;
    @ApiModelProperty("扶持返货消耗系数")
    private Float fly_support_factor;

    @ExcelResources(title = "总退款(元)")
    @ApiModelProperty("总退款(元)")
    private BigDecimal refund_amount;
    @ExcelResources(title = "现金退款(元)")
    @ApiModelProperty("现金退款(元)")
    private BigDecimal cash_refund_amount;
    @ExcelResources(title = "券退款(元)")
    @ApiModelProperty("券退款(元)")
    private BigDecimal coupon_refund_amount;
    @ExcelResources(title = "曝光量（次）")
    @ApiModelProperty("曝光量（次）")
    private Integer show_count;

    @ExcelResources(title = "点击量（次）")
    @ApiModelProperty("点击量（次）")
    private Integer click_count;

    @ExcelResources(title = "CTR（%）")
    @ApiModelProperty("CTR（%）")
    private String click_rate;

    @ExcelResources(title = "CPC（元）")
    @ApiModelProperty("CPC")
    private BigDecimal cost_per_click;

    @ExcelResources(title = "ECPM（元）")
    @ApiModelProperty("ECPM（元）")
    private String average_cost_per_thousand;

    @ExcelResources(title = "粉丝增长数")
    @ApiModelProperty("粉丝关注增长数")
    private Integer fan_follow_count;

    @ExcelResources(title = "涨粉成本（元）")
    @ApiModelProperty("涨粉成本（元）")
    private String fans_increase_cost;

    @ExcelResources(title = "花火广告主帐户ID")
    @ApiModelProperty("花火广告主帐户ID")
    private Integer ad_pick_up_account_id;

    @ExcelResources(title = "花火广告主帐户名称")
    @ApiModelProperty("花火广告主帐户名称")
    private String ad_pick_up_account_name;

    @ExcelResources(title = "花火广告主客户ID")
    @ApiModelProperty("花火广告主客户ID")
    private Integer ad_pick_up_customer_id;

    @ExcelResources(title = "花火广告主客户名称")
    @ApiModelProperty("花火广告主客户名称")
    private String ad_pick_up_customer_name;

    @ExcelResources(title = "花火代理商帐户ID")
    @ApiModelProperty("花火代理商帐户ID")
    private Integer agent_pick_up_account_id;

    @ExcelResources(title = "花火代理商帐户名称")
    @ApiModelProperty("花火代理商帐户名称")
    private String agent_pick_up_account_name;

    @ExcelResources(title = "花火代理商客户ID")
    @ApiModelProperty("花火代理商客户ID")
    private Integer agent_pick_up_customer_id;

    @ExcelResources(title = "花火代理商客户名称")
    @ApiModelProperty("花火代理商客户名称")
    private String agent_pick_up_customer_name;

    @ExcelResources(title = "花火统一一级行业")
    @ApiModelProperty("花火统一一级行业")
    private String united_first_industry_name;

    @ExcelResources(title = "花火统一二级行业")
    @ApiModelProperty("花火统一二级行业")
    private String united_second_industry_name;

    @ExcelResources(title = "花火统一三级行业")
    @ApiModelProperty("花火统一三级行业")
    private String united_third_industry_name;

    @ExcelResources(title = "直客销售")
    @ApiModelProperty("直客销售")
    private String direct_sales_name;

    @ExcelResources(title = "渠道销售")
    @ApiModelProperty("渠道销售")
    private String channel_sales_name;
}
