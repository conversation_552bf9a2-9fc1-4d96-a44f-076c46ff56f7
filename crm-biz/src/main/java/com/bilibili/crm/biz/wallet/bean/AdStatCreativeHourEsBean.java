package com.bilibili.crm.biz.wallet.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.bilibili.crm.biz_common.olap.migrate.util.AdStatCreativeHourDeserializer;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Date;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdStatCreativeHourEsBean implements Serializable {

    private static final long serialVersionUID = 1L;
    private String id;

    /**
     * 账户ID
     */
    private Integer accountId;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 计划ID
     */
    private Integer campaignId;

    /**
     * 单元ID
     */
    private Integer unitId;

    /**
     * 创意id
     */
    private Integer creativeId;

    /**
     * 售卖类型
     */
    private Integer salesType;

    /**
     * 时间序列-小时
     */
    @JSONField(deserializeUsing = AdStatCreativeHourDeserializer.class)
    private Date groupTime;

    /**
     * 曝光数量
     */
    private Integer showCount;

    /**
     * 点击次数
     */
    private Integer clickCount;

    /**
     * 应扣费计费（单位：毫分）
     */
    private Long chargedCostMilli;

    /**
     * 未扣费计费（单位：毫分）
     */
    private Long unchargedCostMilli;

    /**
     * 反作弊曝光量
     */
    private Integer acShowCount;

    /**
     * 反作弊点击次数
     */
    private Integer acClickCount;

    /**
     * 反作弊计费（单位：毫分）
     */
    private Long acCostMilli;

    /**
     * 推广目的
     */
    private Integer promotionPurposeType;

    /**
     * 累计出价 单位（毫分）
     */
    private Long bidCostMilli;

    /**
     * 反作弊累计出价 单位（毫分）
     */
    private Long acBidCostMilli;

    /**
     * 稿件视频ID
     */
    private Long videoId;

    /**
     * 稿件视频ID
     */
    private Integer isNewFly;

    /**
     * 智能创意计费（单位：毫分）
     */
    private Long intelligentChargedCostMilli;
}
