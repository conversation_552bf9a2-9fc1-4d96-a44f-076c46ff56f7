package com.bilibili.crm.biz.wallet.bean;

import com.bilibili.crm.biz_common.olap.UniqKey;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class AdsSparkTradeOrderDetailInfoEsBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @UniqKey(esFieldName = "order_no", esSortOrder = SortOrder.DESC)
    private String order_no;
    private Long out_task_cm;
    private String out_task_no;
    private String tag;
    private String bvid;
    private String account_id;
    private String account_name;
    private String actual_compl_date;
    private Long agent_discount_rate;
    private String av_pub_date;
    private String av_title;
    private String avid;
    private Long avid_fans;
    private Long avid_fans_level;
    private String avid_fans_level_name;
    private String brand_name;
    private Long client_pay_amount;
    private Long client_refund;
    private Long compl_gmv_d;
    private Long compl_gmv_m;
    private Long compl_gmv_q;
    private Long compl_status_flag;
    private String create_date;
    private Long create_fans;
    private Long create_fans_level;
    private String create_fans_level_name;
    private Long create_status_flag;
    private String crm_account_status;
    private String crm_agent_attribution;
    private Long crm_cash;
    private String crm_category_first_id;
    private String crm_category_first_name;
    private String crm_category_second_id;
    private String crm_category_second_name;
    private String crm_ctime;
    private String crm_customer_id;
    private String crm_customer_name;
    private String crm_group_id;
    private String crm_group_name;
    private String crm_product_id;
    private String crm_product_line_id;
    private String crm_product_line_name;
    private String crm_product_name;
    private Long current_fans;
    private Long current_fans_level;
    private String current_fans_level_name;
    private String department_id;
    private String department_name;
    private String expected_compl_date;
    private String final_agent_id;
    private String final_agent_name;
    private String final_agent_type;
    private String final_center_name;
    private String first_industry_tag;
    private Long fly_cost_milli;
    private Long gmv_amount;
    private String gmv_brand;
    private String gmv_industry;
    private Long is_fly;
    private Long is_invited;
    private Long is_mcn_up;
    private Long is_new_huahuo;
    private Long is_staff_av;
    private String l1_track_list;
    private String l2_track_list;
    private String l3_track_list;
    private Long labor_service_fee;
    private Long labor_service_fee_rate;
    private String log_date;
    private String mcn_id;
    private String mcn_name;
    private Long mcn_type_id;
    private String mcn_type_name;
    private String opportunity_from;
    private Long order_amount;
    private Integer order_id;
    private Long order_status_id;
    private String order_status_name;
    private String partition_id;
    private String partition_name;
    private Long pay_gmv_d;
    private Long pay_gmv_m;
    private Long pay_gmv_q;
    private String payment_confirm;
    private String payment_time;
    private Long pending_status_flag;
    private Long platform_actual_cost;
    private Long platform_actual_income;
    private Long platform_allowance;
    private Long platform_allowance_rate;
    private Long platform_service_fee_rate;
    private String second_industry_tag;
    private Long service_provider_fee;
    private Long service_provider_fee_rate;
    private String service_provider_id;
    private String service_provider_name;
    private String sub_partition_id;
    private String sub_partition_name;
    private String sub_tid;
    private String sub_tid_name;
    private String task_create_date;
    private String task_name;
    private String task_no;
    private Long task_type_id;
    private String task_type_name;
    private String third_industry_tag;
    private String tid;
    private String tid_name;
    private String up_mid;
    private String up_name;
    private Long upper_base_price;
    private Long upper_income;
    private Long upper_income_rate;
    private Long upper_platform_price;
}
