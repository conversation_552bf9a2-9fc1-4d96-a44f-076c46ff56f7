package com.bilibili.crm.biz.merchants.component;

import com.alibaba.excel.EasyExcel;
import com.alibaba.excel.annotation.ExcelProperty;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.merchants.bo.CommonCustomerExcelBo;
import com.bilibili.crm.biz.merchants.bo.QueryNonStandardReq;
import com.bilibili.crm.biz.merchants.bo.ResourceLibraryExcelBo;
import com.bilibili.crm.biz.merchants.config.MerchantsConfig;
import com.bilibili.crm.biz.merchants.dto.OffStandardResourceDto;
import com.bilibili.crm.biz.merchants.dto.PackageInfoDto;
import com.bilibili.crm.biz.merchants.enums.PackageWorkOrderTypeEnum;
import com.bilibili.crm.biz.merchants.po.PackageWorkOrderDetailPo;
import com.bilibili.crm.biz.merchants.po.PackageWorkOrderPo;
import com.bilibili.crm.biz.merchants.repository.PackageWorkOrderRepository;
import com.bilibili.crm.biz.merchants.service.NonStandardService;
import com.bilibili.crm.biz_common.excel.CustomRowHeightHandler;
import com.bilibili.crm.biz_common.excel.ExcelFillCellMergeStrategy;
import com.bilibili.crm.platform.biz.repo.attachment.CrmCommonAttachmentDto;
import com.bilibili.crm.platform.biz.util.MailUtils;
import com.bilibili.crm.platform.biz.util.excel.EasyExcelUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.io.File;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.net.URL;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2025/4/11
 */
@Slf4j
@Component
public class PricingExcelComponent {

    @Resource
    private PackageWorkOrderRepository workOrderRepository;

    @Resource
    private MailUtils mailUtils;

    @Resource
    private NonStandardService nonStandardService;

    public void exportExcel(Long workOrderId, String operator) {
        PackageInfoDto packageInfoDto = workOrderRepository.queryDtoById(workOrderId);
        if (null == packageInfoDto) {
            log.warn("wordOrderId = {} not exists", workOrderId);
            mailUtils.sendEmail("资源包下载", "数据为空", Lists.newArrayList(operator + "@bilibili.com"));
            return;
        }

        PackageWorkOrderPo workOrderPo = packageInfoDto.getPackageWorkOrderPo();
        Byte type = workOrderPo.getType();
        List<PackageWorkOrderDetailPo> detailPos = packageInfoDto.getDetailPos();
        if (CollectionUtils.isEmpty(detailPos)) {
            log.error("数据为空");
            mailUtils.sendEmail("资源包下载", "数据为空", Lists.newArrayList(operator + "@bilibili.com"));
            return;
        }

        if (Objects.equals(type, PackageWorkOrderTypeEnum.LIBRARY.getCode())) {
            List<ResourceLibraryExcelBo> bos = detailPos.stream().map(this::buildLibraryExcelBo).collect(Collectors.toList());
            File library = EasyExcelUtils.export(bos, "library", ResourceLibraryExcelBo.class);
            mailUtils.sendMail(Lists.newArrayList(operator + "@bilibili.com"), library, "资源包下载", "");
            return;
        }

        Map<String, String> demoMap = getDemoMap(detailPos);
        List<CommonCustomerExcelBo> commonCustomerExcelBos = buildBos(detailPos, demoMap, workOrderPo.getPackageSumNetPrice());

        File tmpExportFile = EasyExcelUtils.createTmpExportFile("commonCustomerPackage");
        List<String> exportFields = getExportFields(workOrderPo);
        log.info("exportFields = {}", exportFields);
        EasyExcel.write(tmpExportFile)
                .includeColumnFiledNames(exportFields)
                .registerWriteHandler(new CustomerSheetWriteHandler(packageInfoDto))
                .registerWriteHandler(new ExcelFillCellMergeStrategy(5, new int[]{0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17}))
                .registerWriteHandler(new CustomRowHeightHandler())
                .sheet("sheet1")
                .relativeHeadRowIndex(5)
                .doWrite(commonCustomerExcelBos);
        mailUtils.sendMail(Lists.newArrayList(operator + "@bilibili.com"), tmpExportFile, "资源包下载", "");
    }

    private List<String> getExportFields(PackageWorkOrderPo workOrderPo) {
        List<String> exportFields = new ArrayList<>();
        if (workOrderPo.getType().equals(PackageWorkOrderTypeEnum.CUSTOMER_PACKAGE.getCode())) {
            exportFields = MerchantsConfig.customerExportFields;
        } else if (StringUtils.isNotBlank(workOrderPo.getExportFields())) {
            exportFields = Arrays.asList(workOrderPo.getExportFields().split(","));
        }


        List<String> result = new ArrayList<>();
        Field[] declaredFields = CommonCustomerExcelBo.class.getDeclaredFields();
        for (Field field : declaredFields) {
            ExcelProperty declaredAnnotation = field.getDeclaredAnnotation(ExcelProperty.class);
            String[] value = declaredAnnotation.value();
            if (exportFields.contains(value[0])) {
                result.add(field.getName());
            }
        }
        return result;
    }

    private Map<String, String> getDemoMap(List<PackageWorkOrderDetailPo> pos) {
        List<String> resourceIds = pos.stream().map(PackageWorkOrderDetailPo::getResourceId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(resourceIds)) {
            return Collections.emptyMap();
        }
        QueryNonStandardReq standardReq = new QueryNonStandardReq();
        standardReq.setResourceIds(resourceIds);
        List<OffStandardResourceDto> dtos = nonStandardService.query(standardReq);
        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyMap();
        }

        Map<String, OffStandardResourceDto> map = dtos.stream().collect(Collectors.toMap(OffStandardResourceDto::getResourceId, Function.identity()));

        Map<String, String> result = new HashMap<>();
        for (PackageWorkOrderDetailPo po : pos) {
            String resourceId = po.getResourceId();
            if (StringUtils.isBlank(resourceId)) {
                continue;
            }

            OffStandardResourceDto resourceDto = map.get(resourceId);
            if (null == resourceDto || CollectionUtils.isEmpty(resourceDto.getDemos())) {
                continue;
            }

            CrmCommonAttachmentDto attachmentDto = resourceDto.getDemos().get(0);
            result.put(resourceId, attachmentDto.getUrl());
        }
        return result;
    }


    private List<CommonCustomerExcelBo> buildBos(List<PackageWorkOrderDetailPo> pos, Map<String, String> map, Long packageSumNetPrice) {
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(po -> convert(po, map, packageSumNetPrice)).collect(Collectors.toList());

    }

    private CommonCustomerExcelBo convert(PackageWorkOrderDetailPo po, Map<String, String> map, Long packageSumNetPrice) {
        if (null == po) {
            return null;
        }

        CommonCustomerExcelBo commonCustomerExcelBo = new CommonCustomerExcelBo();
        commonCustomerExcelBo.setResourceId(po.getResourceId());
        commonCustomerExcelBo.setResourceLocation(po.getResourceLocation());
        commonCustomerExcelBo.setResourceType(po.getResourceType());
        commonCustomerExcelBo.setResourcePlatform(po.getResourcePlatform());
        commonCustomerExcelBo.setResourceName(po.getResourceName());
        commonCustomerExcelBo.setNum(po.getNum());
        commonCustomerExcelBo.setUnit(po.getUnit());
        commonCustomerExcelBo.setRightsDesc(po.getRightsDesc());
        commonCustomerExcelBo.setDataType(po.getDataType());
        commonCustomerExcelBo.setResourceDuration(po.getResourceDuration());

        if (null != packageSumNetPrice) {
            BigDecimal bigDecimal = new BigDecimal(packageSumNetPrice);
            String packageSumNetPriceStr = bigDecimal.divide(new BigDecimal(1000000), 2, RoundingMode.HALF_UP).toString();
            commonCustomerExcelBo.setPackageSumNetPrice(packageSumNetPriceStr + "万元");
        }

        if (null != po.getResourceId()) {
            String url = map.get(po.getResourceId());
            try {
                URL demoUrl = new URL(url);
                commonCustomerExcelBo.setDemo(demoUrl);
            } catch (Exception e) {
                log.error("convert url error, url = {}", url, e);
            }
        }

        if (null != po.getPublishUnitPrice()) {
            commonCustomerExcelBo.setSelectedPublishPrice(Utils.fromFenToYuan(po.getPublishUnitPrice()).toString());
        }

        if (null != po.getPublishSumPrice()) {
            commonCustomerExcelBo.setPublishSumPrice(Utils.fromFenToYuan(po.getPublishSumPrice()).toString());
        }

        if (null != po.getNetUnitPrice()) {
            commonCustomerExcelBo.setNetUnitPrice(Utils.fromFenToYuan(po.getNetUnitPrice()).toString());
        }

        if (null != po.getNetSumPrice()) {
            commonCustomerExcelBo.setNetSumPrice(Utils.fromFenToYuan(po.getNetSumPrice()).toString());
        }

        if (null != po.getDataEstimate()) {
            commonCustomerExcelBo.setDataEstimate(String.valueOf(po.getDataEstimate()));
        }
        return commonCustomerExcelBo;
    }


    private ResourceLibraryExcelBo buildLibraryExcelBo(PackageWorkOrderDetailPo po) {
        if (null == po) {
            return null;
        }
        ResourceLibraryExcelBo resourceLibraryExcelBo = new ResourceLibraryExcelBo();
        resourceLibraryExcelBo.setTabType(po.getTabType());
        resourceLibraryExcelBo.setResourceId(po.getResourceId());
        resourceLibraryExcelBo.setResourceLocation(po.getResourceLocation());
        resourceLibraryExcelBo.setResourceType(po.getResourceType());
        resourceLibraryExcelBo.setResourceName(po.getResourceName());
        resourceLibraryExcelBo.setRightsDesc(po.getRightsDesc());
        resourceLibraryExcelBo.setResourcePlatform(po.getResourcePlatform());
        resourceLibraryExcelBo.setUnit(po.getUnit());

        if (null != po.getPublishUnitPriceLow()) {
            resourceLibraryExcelBo.setPublishUnitPriceLow(Utils.fromFenToYuan(po.getPublishUnitPriceLow()).toString());
        }

        if (null != po.getPublishUnitPriceHigh()) {
            resourceLibraryExcelBo.setPublishUnitPriceHigh(Utils.fromFenToYuan(po.getPublishUnitPriceHigh()).toString());
        }

        if (null != po.getSelectedPublishPrice()) {
            resourceLibraryExcelBo.setSelectedPublishPrice(Utils.fromFenToYuan(po.getSelectedPublishPrice()).toString());
        }

        resourceLibraryExcelBo.setDataType(po.getDataType());

        if (null != po.getDataEstimate()) {
            resourceLibraryExcelBo.setDataEstimate(String.valueOf(po.getDataEstimate()));
        }
        resourceLibraryExcelBo.setNum(po.getNum());
        resourceLibraryExcelBo.setResourceDuration(po.getResourceDuration());

        if (null != po.getCost()) {
            resourceLibraryExcelBo.setCost(Utils.fromFenToYuan(po.getCost()).toString());
        }

        if (null != po.getPublishUnitPrice()) {
            resourceLibraryExcelBo.setPublishUnitPrice(Utils.fromFenToYuan(po.getPublishUnitPrice()).toString());
        }

        if (null != po.getNetUnitPrice()) {
            resourceLibraryExcelBo.setUnitNetPrice(Utils.fromFenToYuan(po.getNetUnitPrice()).toString());
        }

        if (null != po.getPublishSumPrice()) {
            resourceLibraryExcelBo.setPublishSumPrice(Utils.fromFenToYuan(po.getPublishSumPrice()).toString());
        }

        if (null != po.getNetSumPrice()) {
            resourceLibraryExcelBo.setNetSumPrice(Utils.fromFenToYuan(po.getNetSumPrice()).toString());
        }
        return resourceLibraryExcelBo;
    }
}
