package com.bilibili.crm.biz.merchants.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import lombok.Builder;
import lombok.Data;

import java.sql.Timestamp;
import java.time.LocalDate;
import java.time.LocalDateTime;

/**
 * <AUTHOR>
 * @date 2024/11/4
 */
@Data
@TableName("crm_project_item_package")
public class MerchantsProjectInfoPo {

    @TableId(value = "id", type = IdType.AUTO)
    private Integer id;

    /**
     * 资源包名称
     */
    private String name;

    /**
     * 状态: 1-有效 0-无效
     */
    private Integer status;

    /**
     * 生效日期
     */
    private LocalDate validBeginDay;

    /**
     * 失效日期
     */
    private LocalDate validEndDay;

    /**
     * 软删除: 0-有效 1-删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private LocalDateTime ctime;

    /**
     * 更新时间
     */
    private LocalDateTime mtime;

    /**
     * 麦哲伦作品名称
     */
    private String productionName;

    /**
     * 麦哲伦作品ID, 0表示不存在 或者未绑定
     */
    private Long productionId;

    /**
     * 招商周期开始日期
     */
    private LocalDateTime investmentBeginTime;

    /**
     * 招商周期结束日期
     */
    private LocalDateTime investmentEndTime;

    /**
     * 招商周期是否长期有效1是0否
     */
    private Integer investmentLongTimeFlag;

    /**
     * 执行周期是否长期有效1是0否
     */
    private Integer validLongTimeFlag;

    /**
     * 招商等级:值有S+,S,A,B
     */
    private String investmentLevel;

    /**
     * 招商目标（分）
     */
    private Long investmentTargetMoney;

    /**
     * 创建人域账号 比如wanggang03
     */
    private String creator;

    /**
     * 新营销团队
     */
    private String marketingTeam;

    /**
     * 商业对接人
     */
    private String businessContact;

    /**
     * 项目简介
     */
    private String briefIntroduction;


    private Integer projectPropertyId;

    private Integer projectTypeId;

    private Integer projectFirstCategoryId;

    private Integer projectSecondCategoryId;

    private Byte projectGeneralType;

    private Byte merchantMarketingTeamId;

    private Byte projectCreateType;

    private Integer projectFirstAreaId;

    private Integer projectSecondAreaId;

    private Byte originProjectType;

    private Byte merchantStatus;

    private Byte rackStatus;

    private Byte pricingStatus;

    private Long estimateProjectCost;

    private String mainContact;

    private String reportSaleIds;

    private Integer version;

    private Long stockSumMoney;

    private String operator;

    private String extInfo;

    private Byte hisRackStatus;

    private String costDesc;

    private Integer unshelveDays;

    private String saleGroupIds;

    private Byte approveEnable;
}
