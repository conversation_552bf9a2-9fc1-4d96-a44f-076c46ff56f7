package com.bilibili.crm.biz.merchants.bo;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/6
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class QueryMerchantsProjectParam {

    private Integer id;

    private String projectName;

    private Integer projectPropertyId;

    private Integer projectTypeId;

    private Integer projectFirstCategoryId;

    private Integer projectSecondCategoryId;

    private Integer projectFirstAreaId;

    private Integer projectSecondAreaId;

    private String creator;

    private List<Byte> merchantStatus;

    private Byte rackStatus;

    private Byte pricingStatus;

    private Byte merchantMarketingTeamId;

    private Integer version;

    private Integer page;

    private Integer size;

    private Byte topVersionEnable;

    private String investmentLevelType;

    private boolean easyQuery;

    private LocalDateTime investmentBeginTimeLe;

    private LocalDateTime investmentEndTimeLt;

    private boolean mainQuery;

    private List<Integer> ids;

    private List<Integer> projectIds;

    private boolean reportSaleIdEmptyQuery;

    private Byte approveEnable;
}
