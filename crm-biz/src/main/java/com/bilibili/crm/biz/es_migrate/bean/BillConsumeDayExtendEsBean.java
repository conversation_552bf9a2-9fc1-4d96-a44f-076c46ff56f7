package com.bilibili.crm.biz.es_migrate.bean;

import com.alibaba.fastjson.annotation.JSONField;
import com.bilibili.crm.biz_common.olap.UniqKey;
import com.bilibili.crm.biz_common.olap.migrate.util.UTCStrToUTC8TimestampDeserializer;
import com.bilibili.crm.platform.api.achievement.dto.AchieveType;
import com.bilibili.crm.platform.common.AttractInvestmentType;
import com.bilibili.crm.platform.common.CrmOrderResourceType;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.UserType;
import com.bilibili.crm.platform.common.income.ExtendBillType;
import com.bilibili.crm.platform.common.income.IncomeProductEnum;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;
import java.sql.Timestamp;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class BillConsumeDayExtendEsBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @UniqKey(esFieldName = "id", esSortOrder = SortOrder.DESC)
    private String id;

    /**
     * 账单类型 0 合约广告账单 1 花火账单
     *
     * @see ExtendBillType
     */
    
    private Integer extendBillType;

    /**
     * 账单ID
     */
    
    private Integer billId;

    /**
     * 日期 (launchDate)
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp groupTime;

    /**
     * 消耗金额 (dailyPackageAmount)
     */
    private Long consumeAmount;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 订单id
     */
    private Integer orderId;


    /**
     * 花火绑定的订单号 默认为0
     */
    private Integer pickupCrmOrderId;

    /**
     * 订单类型
     *
     * @see CrmOrderType
     */
    private Integer orderType;

    /**
     * 订单资源类型 0其他 1内部、2售卖、3配送、4补量
     *
     * @see CrmOrderResourceType
     */
    private Integer resourceType;

    /**
     * 是否需要记收 0-不需要 1-需要
     */
    private Integer isChecking;

    /**
     * 关账日期（账单关账的具体时间点）
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp closingDate;
    /**
     * 账单操作来源 (0系统自动 1手动添加)
     */
    private Integer billSource;

    /**
     * 账单操作来源等于合同实结的时候 是系统实结1 还是人工实结0
     */
    
    private Integer billCloseType;

    /**
     * 广告主账号ID (合同绑定广告主)
     */
    
    private Integer accountId;

    /**
     * 代理商id  (合同绑定代理商ID)
     */
    
    private Integer agentId;

    /**
     * 商机代理ID （合同绑定商机代理ID）
     */
    
    private Integer optAgentId;

    /**
     * 商机代理名称
     */
    
    private String optAgentName;

    /**
     * 产品类型
     *
     * @see IncomeProductEnum
     */
    
    private Integer productType;

    /**
     * 归属销售
     */
    
    private List<Integer> sales;

    /**
     * 直客销售
     */
    
    private List<Integer> directSales;

    /**
     * 渠道销售
     */
    
    private List<Integer> channelSales;

    /**
     * 归属部门
     */
    
    private List<Integer> deps;

    //广告主账号扩展
    /**
     * 是否是内部账号 0否 1是
     */
    
    private Integer isInner;

    /**
     * 所属客户id
     */
    
    private Integer customerId;

    /**
     * 用户属性 0个人用户 1机构用户 2个人起飞用户
     *
     * @see UserType
     */
    
    private Integer userType;
    /**
     * 是否是代理商 0否 1是
     */
    
    private Integer isAgent;

    /**
     * 行业一级分类id
     */
    
    private Integer categoryFirstId;

    /**
     * 行业二级分类id
     */
    
    private Integer categorySecondId;

    /**
     * 业务行业二级分类id
     */
    
    private Integer bizIndustryCategorySecondId;

    /**
     * 业务行业一级分类id
     */
    
    private Integer bizIndustryCategoryFirstId;

    /**
     * 集团id（对应acc_company_group的ID）
     */
    
    private Integer groupId;

    /**
     * 账户所属集团id（对应账户上的集团ID）
     */
    
    private Integer accountGroupId;

    /**
     * 公司组id
     */
    
    private Integer companyGroupId;

    /**
     * 产品线id
     */
    
    private Integer productLineId;

    /**
     * 产品id
     */
    
    private Integer productId;

    /**
     * 排期id
     */
    
    private Integer scheduleId;

    /**
     * 添加时间
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp ctime;

    /**
     * order 项目id
     */
    
    private Integer projectItemId;

    /**
     * 招商项目类型
     *
     * @see AttractInvestmentType
     */
    
    private Integer attractInvestmentType;

    /**
     * 曝光量
     */
    private Long showCount;
    /**
     * 点击量
     */
    private Long clickCount;


    /**
     * 是否已关账 0-未关账 1-已关账
     */
    private Integer isClosed;

    /**
     * 归属团队类型
     *
     * @see AchieveType
     */
    
    private List<Integer> achieveTypes;

    //花火新增字段

    /**
     * 花火订单编号
     */
    private String orderNo;

    /**
     * 花火订单合作类型
     *
     * @see com.bilibili.commercialorder.api.order.enums.CooperationType
     */
    private Integer pickupCooperationType;

    /**
     * 花火订单订单类型(花火/邀约)
     *
     * @see com.bilibili.crm.platform.biz.common.PickupOrderTypeEnum
     */
    private Integer pickupOrderType;

    /**
     * 花火预计订单完成时间
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp pickupExpectFinishTime;


    /**
     * 花火视频上线时间
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp pickupOnlineTime;

    /**
     * 花火订单状态
     *
     * @see com.bilibili.commercialorder.api.order.enums.OrderStatus
     * <p>
     * BOOSTING
     * @see com.bilibili.commercialorder.api.order.enums.BoostingStatus
     */
    
    private Integer pickupOrderStatus;

    /**
     * 视频发布时间
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp pickupPubTime;


    /**
     * 支付时间
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp payDate;


    /**
     * crm合同模块订单非标一级产品
     */
    
    private Integer orderFirstCategoryId;


    /**
     * crm合同模块订单非标二级产品
     */
    
    private Integer orderSecondCategoryId;


    /**
     * 是否企业认证 0 未知 1 认证 2 未认证
     */
    
    private Integer isVerifiedMember;

    /**
     * 广告主资金类型
     */
    private Integer financeType;

    /**
     * 代理商是否内广
     */
    private Integer agentIsInner;

    /**
     * 订单完成时间
     */
    @JSONField(deserializeUsing = UTCStrToUTC8TimestampDeserializer.class)
    private Timestamp completedTime;
}
