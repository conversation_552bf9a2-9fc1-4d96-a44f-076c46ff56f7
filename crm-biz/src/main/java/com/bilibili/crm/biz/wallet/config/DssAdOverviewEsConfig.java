package com.bilibili.crm.biz.wallet.config;

import com.bilibili.crm.biz_common.olap.config.IEsAppConfig;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Component
@Data
public class DssAdOverviewEsConfig implements IEsAppConfig {

    @Autowired
    private PaladinConfig paladinConfig;

    @Override
    public String getIndexes(List<Timestamp> dateList) {
        String indexPrefix = paladinConfig.getString("olapEsDssAdOverviewAppName");
        if (CollectionUtils.isEmpty(dateList)) {
            return indexPrefix;
        }

        List<String> indexNames = new ArrayList<>();

        for (Timestamp timestamp : dateList) {
            String formattedDate = CrmUtils.formatDate(timestamp, CrmUtils.YYYY_DOT_MM);
            String indexName = indexPrefix + "-" + formattedDate;
            indexNames.add(indexName);
        }

        indexNames = indexNames.stream().distinct().collect(Collectors.toList());

        return String.join(",", indexNames);
    }
    @Override
    public String getToken() {
        return paladinConfig.getString("olapEsDssAdOverviewToken");
    }

}
