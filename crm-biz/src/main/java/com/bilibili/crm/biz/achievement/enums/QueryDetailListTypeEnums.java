package com.bilibili.crm.biz.achievement.enums;

import lombok.Getter;

@Getter
public enum QueryDetailListTypeEnums {
    BRAND_COMPLETED_PAYED(1, "合约广告已执行已交付明细表"),
    BRAND_OLD_COMPLETED_UN_CLOSE(2, "合约广告历史季度已交付未关账明细表"),
    PICKUP_COMPLETED(3, "花火商单已执行金额明细表"),
    CPC_CPM_SUM(4, "必选累计收入明细表"),
    DPA_SUM(5, "DPA累计收入明细表"),
    ADX_SUM(6, "ADX累计收入明细表"),
    BUSINESS_FLY_SUM(7, "商业起飞累计收入明细表"),
    CONTENT_FLY_SUM(8, "内容起飞累计收入明细表"),
    CLUE_PASS(9, "合伙人收入明细表"),
    BRAND_EXECUTED_CONTRACT_UNCHECK(10, "合约广告待执行待交付明细表"),
    BRAND_RECORDED_CONTRACT_FORECAST(11, "合约广告已录点位未审核明细表"),
    BRAND_ORDERED_UN_RECORD(12, "合约广告已下单未录点位明细表"),
    PICKUP_UNCONFIRMED(13, "花火商单待执行明细表"),
    PICKUP_UNCONFIRMED_BIZ(14, "花火商单待执行(销售口径)明细表"),
    SAN_LIAN_OTHER_SUM(15, "三连其他累计收入明细表"),
    UKONWN(99, "未知"),
    ;

    private final Integer code;
    private final String desc;

    QueryDetailListTypeEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

}
