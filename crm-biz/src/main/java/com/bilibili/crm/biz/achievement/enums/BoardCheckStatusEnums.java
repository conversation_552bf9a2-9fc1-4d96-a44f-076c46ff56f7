package com.bilibili.crm.biz.achievement.enums;

import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

public enum BoardCheckStatusEnums {


    BRAND_EXECUTED_UN_DELIVERY(1, "合约-待交付"),

    BRAND_WAIT_EXECUTE(2, "合约-待执行"),

    BRAND_WAIT_AUDIT(3, "合约-未过审"),

    BRAND_UN_RECORD(4, "合约-未录点位"),

    PICK_UP_PAYED_WAIT_COMPLETE(5, "花火-已支付待完成"),

    PICK_UP_UN_PAY_WAIT_ORDER(6, "花火-待支付待接单"),
    PICK_UP_AUDIT_REJECT(7, "花火-审核驳回"),
    PICK_UP_COMPLETE_CENTER(8, "花火待完成（中台口径）"),
    UKONWN(99, "未知"),
    ;

    private final Integer code;

    private final String desc;

    BoardCheckStatusEnums(Integer code, String desc) {
        this.code = code;
        this.desc = desc;
    }

    public Integer getCode() {
        return code;
    }

    public String getDesc() {
        return desc;
    }

    public static BoardCheckStatusEnums queryByCode(Integer code) {
        if (code == null) {
            return UKONWN;
        }
        for (BoardCheckStatusEnums status : BoardCheckStatusEnums.values()) {
            if (Objects.equals(status.getCode(), code)) {
                return status;
            }
        }
        return UKONWN;
    }

    public static List<BoardCheckStatusEnums> getAllEnumsList() {
        return Arrays.stream(BoardCheckStatusEnums.values()).filter(e -> !Objects.equals(e.getCode(), 99)).collect(Collectors.toList());
    }
}
