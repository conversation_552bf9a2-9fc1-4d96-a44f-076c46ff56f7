package com.bilibili.crm.biz.es_migrate.config;

import com.bilibili.crm.biz_common.olap.config.IEsAppConfig;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import lombok.Data;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

@Component
@Data
public class NewEsAccountBreakTapeDayEsConfig implements IEsAppConfig {

    @Autowired
    private PaladinConfig paladinConfig;

    @Override
    public String getIndexes(List<Timestamp> dateList) {
        return paladinConfig.getString("olapEsNewEsAccountBreakTapeDayAppName");
    }
    @Override
    public String getToken() {
        return paladinConfig.getString("olapEsNewEsAccountBreakTapeDayToken");
    }

}
