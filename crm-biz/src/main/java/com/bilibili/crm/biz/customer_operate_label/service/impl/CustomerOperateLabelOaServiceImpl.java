package com.bilibili.crm.biz.customer_operate_label.service.impl;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.crm.biz.customer_operate_label.dto.CustomerOperateLabelDto;
import com.bilibili.crm.biz.customer_operate_label.enums.CustomerOperateLabelTypeEnum;
import com.bilibili.crm.biz.customer_operate_label.oa.bo.OaCustomerOperateLabelForm;
import com.bilibili.crm.biz.customer_operate_label.service.CustomerOperateLabelOaService;
import com.bilibili.crm.biz.oa.bo.OaCommonData;
import com.bilibili.crm.platform.api.exception.CrmAppException;
import com.bilibili.crm.platform.api.oa.dto.OaNewCommonResponse;
import com.bilibili.crm.platform.api.oa.dto.StartOaFlowOpenRequest;
import com.bilibili.crm.biz.oa.config.OaConfig;
import com.bilibili.crm.platform.biz.service.oa.component.OaNewTokenUtils;
import com.bilibili.crm.platform.biz.service.oa.component.OaSoaManager;
import com.bilibili.crm.platform.biz.util.DateUtils;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.UUID;

/**
 * <AUTHOR>
 * @date 2024/8/19
 */
@Service
@Slf4j
public class CustomerOperateLabelOaServiceImpl implements CustomerOperateLabelOaService {

    private static final String MEMBER_TYPE = "1";

    @Resource
    private OaConfig oaConfig;

    @Resource
    private OaNewTokenUtils oaNewTokenUtils;

    @Override
    public Triple<String, String, String> startCustomerOperateLabelOaFlow(CustomerOperateLabelDto operateLabelDto, String operatorName,
                                                                          List<String> saleApprove, List<String> saleManagerApprove) {
        try {
            StartOaFlowOpenRequest request = buildOaFlowStartRequest(operateLabelDto, operatorName, saleApprove, saleManagerApprove);
            log.info("startCustomerOperateLabelOaFlow-req = {}", JSON.toJSONString(request));
            TypeToken<OaNewCommonResponse<OaCommonData>> type = new TypeToken<OaNewCommonResponse<OaCommonData>>() {
            };
            String token = oaNewTokenUtils.buildOaFlowOpenApiToken(3);
            // header 需要设置 X-Account: 申请人域账号
            OaNewCommonResponse<OaCommonData> oaResponse = OkHttpUtils.bodyPost(
                            oaConfig.getOaFlowOpenApiUrl())
                    .header(OaSoaManager.OA_X_ACCOUNT_HEADER, operatorName)
                    .header("BEE-AppAccessToken", "Bearer " + token)
                    .header("Content-Type", "application/json; charset=utf-8")
                    .json(JSON.toJSONString(request)).callForObject(type);
            log.info("startCustomerOperateLabelOaFlow-resp = {}", JSON.toJSONString(oaResponse));

            if (oaResponse.getCode() != OaNewCommonResponse.SUCCESS_CODE) {
                throw new CrmAppException("oa返回code不为1");
            }
            OaCommonData data = oaResponse.getData();
            if (data == null || null == data.getArgs() || null == data.getArgs().getBasicInfo()) {
                throw new CrmAppException("oa返回结果为空");
            }
            OaCommonData.BasicInfo basicInfo = data.getArgs().getBasicInfo();
            return Triple.of(basicInfo.getOrderId(), basicInfo.getOrderNo(), request.getArgs().getIdempotentId());
        } catch (Exception e) {
            log.error("startCustomerOperateLabelOaFlow error", e);
            throw new CrmAppException(e.getMessage());
        }
    }

    private StartOaFlowOpenRequest buildOaFlowStartRequest(CustomerOperateLabelDto dto, String operatorName,
                                                           List<String> saleApprove, List<String> saleManagerApprove) {
        String txId = UUID.randomUUID().toString();
        OaCustomerOperateLabelForm oaCustomerOperateLabelForm = buildOaForm(dto, saleApprove, saleManagerApprove);
        JSONObject form = (JSONObject) JSONObject.toJSON(oaCustomerOperateLabelForm);
        StartOaFlowOpenRequest.ArgsBean argsBean = StartOaFlowOpenRequest.ArgsBean.builder()
                .form(form)
                .idempotentId(txId)
                .build();
        return StartOaFlowOpenRequest.builder()
                .processName(oaConfig.getOaCustomerOperateLabelName())
                .orderTitle(oaConfig.getOaCustomerOperateLabelOrderTitle() + "-" + operatorName)
                .args(argsBean)
                .build();

    }

    private OaCustomerOperateLabelForm buildOaForm(CustomerOperateLabelDto dto, List<String> saleApprove, List<String> saleManagerApprove) {
        OaCustomerOperateLabelForm form = new OaCustomerOperateLabelForm();
        form.setCustomerId(String.valueOf(dto.getCustomerId()));
        form.setCustomerName(dto.getCustomerName());
        form.setServiceProviderId(String.valueOf(dto.getServiceProviderId()));
        form.setServiceProviderName(dto.getServiceProviderName());
        form.setType(CustomerOperateLabelTypeEnum.getByCode(dto.getType()).getDesc());

        String applyServiceProviderBeginTime = DateUtils.convert(dto.getApplyServiceBeginTime());
        String applyServiceProviderEndTime = DateUtils.convert(dto.getApplyServiceEndTime());
        form.setServiceProviderTime(Lists.newArrayList(applyServiceProviderBeginTime, applyServiceProviderEndTime));

        if (CollectionUtils.isNotEmpty(saleApprove)) {
            List<OaCustomerOperateLabelForm.ApproveInfo> approves = buildApproves(saleApprove);
            form.setSaleApprove(approves);
        }

        if (CollectionUtils.isNotEmpty(saleManagerApprove)) {
            List<OaCustomerOperateLabelForm.ApproveInfo> approveManagers = buildApproves(saleManagerApprove);
            form.setSaleManagerApprove(approveManagers);
        }
        return form;
    }

    private List<OaCustomerOperateLabelForm.ApproveInfo> buildApproves(List<String> saleApprove) {
        List<OaCustomerOperateLabelForm.ApproveInfo> approves = new ArrayList<>();
        saleApprove.forEach(sale -> {
            OaCustomerOperateLabelForm.ApproveInfo approveInfo = new OaCustomerOperateLabelForm.ApproveInfo();
            approveInfo.setMembertype(MEMBER_TYPE);
            String[] saleNameAndNickName = sale.split("-");
            approveInfo.setKey(saleNameAndNickName[0]);
            approveInfo.setTitle(saleNameAndNickName[1]);
            approves.add(approveInfo);
        });
        return approves;
    }
}
