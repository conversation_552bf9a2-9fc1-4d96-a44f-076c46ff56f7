package com.bilibili.crm.biz.achievement.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.sql.Timestamp;
import java.util.List;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class SaleSnapshotDto {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 销售名称
     */
    private String name;

    /**
     * 公司邮箱
     */
    private String email;

    /**
     * 销售类型
     */
    private Integer type;

    /**
     * 销售小组ID
     */
    private List<Integer> groupId;

    /**
     * 状态（1-有效，2-无效）
     */
    private Integer status;


    /**
     * 角色，0销售1组长2总监
     */
    private Integer level;
    /**
     * 昵称
     */
    private String nickName;
    /**
     * 是否离职1是0否
     */
    private Integer saleIsQuit;

    /**
     * 销售离职时间
     */
    private Timestamp saleQuitTime;
}
