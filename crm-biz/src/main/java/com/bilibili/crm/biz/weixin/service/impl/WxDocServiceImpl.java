package com.bilibili.crm.biz.weixin.service.impl;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.crm.biz.weixin.bo.WxCommonResp;
import com.bilibili.crm.biz.weixin.bo.WxCreateSheetResp;
import com.bilibili.crm.biz.weixin.config.WeixinConfig;
import com.bilibili.crm.biz.weixin.service.WxCommonService;
import com.bilibili.crm.biz.weixin.service.WxDocService;
import com.bilibili.crm.biz.weixin.bo.WxCreateDocResp;
import com.bilibili.crm.platform.api.exception.CrmAppException;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.util.LinkedMultiValueMap;
import org.springframework.util.MultiValueMap;
import org.springframework.web.client.RestTemplate;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/11/7
 */
@Component
@Slf4j
public class WxDocServiceImpl implements WxDocService {


    private static final String MERCHANT_BATCH_CREATE = "招商模版";

    @Resource
    private RestTemplate restTemplate;

    @Resource
    private WxCommonService wxCommonService;

    @Resource
    private WeixinConfig weixinConfig;

    @Override
    public WxCreateDocResp createDoc(int docType, String docName) {

        String url = generatorUrl(weixinConfig.getCreateDocUrl(), wxCommonService.getToken(""));
        log.info("WxDocServiceImpl-createDoc, ulr={}", url);

        JSONObject jsonObject = new JSONObject();
        jsonObject.put("doc_name", docName);
        jsonObject.put("doc_type", docType);
        String resp = restTemplate.postForObject(url, jsonObject, String.class);
        log.info("WxDocServiceImpl-createDoc, result={}", resp);
        WxCreateDocResp result = JSONObject.parseObject(resp, WxCreateDocResp.class);

        if (null == result || StringUtils.isBlank(result.getDocId()) || StringUtils.isBlank(result.getUrl())) {
            throw new CrmAppException("创建微信文档失败");
        }

        String sheetId = createSheet(result.getDocId());
        createFields(result.getDocId(), sheetId);
        result.setSheetId(sheetId);
        return result;
    }

    @Override
    public List<JSONObject> queryDocRecords(String docId, String sheetId) {
        JSONObject req = new JSONObject();
        String url = generatorUrl(weixinConfig.getQueryRecordsUrl(), wxCommonService.getToken(""));
        log.info("WxDocServiceImpl-queryDocRecors, ulr={}", url);

        req.put("docid", docId);
        req.put("sheet_id", sheetId);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        String resp = restTemplate.postForObject(url, new HttpEntity<>(req.toJSONString(), httpHeaders), String.class);
        log.info("WxDocServiceImpl-queryDocRecors, result={}", resp);

        JSONObject jsonObject = JSONObject.parseObject(resp);
        if (null == jsonObject || (int) jsonObject.get("errcode") != 0) {
            throw new CrmAppException("查询微信文档数据失败");

        }
        JSONArray array = (JSONArray) jsonObject.get("records");
        List<JSONObject> result = new ArrayList<>();
        array.forEach(a -> result.add(((JSONObject) a).getJSONObject("values")));
        return result;
    }


    public String createSheet(String docId) {
        JSONObject req = new JSONObject();
        String url = generatorUrl(weixinConfig.getCreateSheetUrl(), wxCommonService.getToken(""));
        log.info("WxDocServiceImpl-createSheet, ulr={}", url);

        req.put("docid", docId);
        JSONObject propertiesMap = new JSONObject();
        propertiesMap.put("title", MERCHANT_BATCH_CREATE);
        propertiesMap.put("index", 0);
        req.put("properties", propertiesMap);

        String jsonString = req.toJSONString();
        log.info("createSheet body = {}", jsonString);

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        String resp = restTemplate.postForObject(url, new HttpEntity<>(jsonString, httpHeaders), String.class);
        log.info("WxDocServiceImpl-createSheet, result={}", resp);

        WxCreateSheetResp wxCreateSheetResp = JSONObject.parseObject(resp, WxCreateSheetResp.class);
        if (null == wxCreateSheetResp || null == wxCreateSheetResp.getProperties() || null == wxCreateSheetResp.getProperties().getSheetId()) {
            throw new CrmAppException("创建微信文档子表失败");
        }
        return wxCreateSheetResp.getProperties().getSheetId();
    }

    public void createFields(String docId, String sheetId) {
        JSONObject req = new JSONObject();
        String url = generatorUrl(weixinConfig.getCreateFieldsUrl(), wxCommonService.getToken(""));
        log.info("WxDocServiceImpl-createFields, ulr={}", url);

        req.put("docid", docId);
        req.put("sheet_id", sheetId);
        req.put("fields", buildMerchantsFields());

        HttpHeaders httpHeaders = new HttpHeaders();
        httpHeaders.setContentType(MediaType.APPLICATION_JSON_UTF8);
        log.info("createFields req ={}", req);
        String resp = restTemplate.postForObject(url, new HttpEntity<>(req.toJSONString(), httpHeaders), String.class);
        log.info("WxDocServiceImpl-createFields, result={}", resp);
        WxCommonResp result = JSONObject.parseObject(resp, WxCommonResp.class);
        if (null == result || result.getErrcode() != 0) {
            throw new CrmAppException("创建微信文档字段失败");
        }
    }


    private String generatorUrl(String path, Object... args) {

        return String.format(weixinConfig.getBaseUrl().concat(path), args);
    }

    private JSONArray buildMerchantsFields() {
        JSONArray array = new JSONArray();

        JSONObject costDesc = new JSONObject();
        costDesc.put("field_title", "成本说明(必填)");
        costDesc.put("field_type", "FIELD_TYPE_TEXT");
        array.add(costDesc);

        JSONObject projectResourceAttachment = new JSONObject();
        projectResourceAttachment.put("field_title", "项目资源包(传excel)");
        projectResourceAttachment.put("field_type", "FIELD_TYPE_ATTACHMENT");
        array.add(projectResourceAttachment);

        JSONObject seatInfo = new JSONObject();
        seatInfo.put("field_title", "席位类型,席位数,席位单价");
        seatInfo.put("field_type", "FIELD_TYPE_TEXT");
        array.add(seatInfo);

        JSONObject investmentAttachmentsLink = new JSONObject();
        investmentAttachmentsLink.put("field_title", "招商附件");
        investmentAttachmentsLink.put("field_type", "FIELD_TYPE_ATTACHMENT");
        array.add(investmentAttachmentsLink);

        JSONObject investmentPlanLink = new JSONObject();
        investmentPlanLink.put("field_title", "招商方案(必传PDF)");
        investmentPlanLink.put("field_type", "FIELD_TYPE_ATTACHMENT");
        array.add(investmentPlanLink);

        JSONObject projectPicture = new JSONObject();
        projectPicture.put("field_title", "项目图片(必填)");
        projectPicture.put("field_type", "FIELD_TYPE_IMAGE");
        array.add(projectPicture);

        JSONObject briefIntroduction = new JSONObject();
        briefIntroduction.put("field_title", "项目简介(必填)");
        briefIntroduction.put("field_type", "FIELD_TYPE_TEXT");
        array.add(briefIntroduction);

        JSONObject reportedSale = new JSONObject();
        reportedSale.put("field_title", "提报销售");
        reportedSale.put("field_type", "FIELD_TYPE_TEXT");
        array.add(reportedSale);

        JSONObject mainContact = new JSONObject();
        mainContact.put("field_title", "主站对接人");
        mainContact.put("field_type", "FIELD_TYPE_TEXT");
        array.add(mainContact);

        JSONObject businessContact = new JSONObject();
        businessContact.put("field_title", "商业对接人");
        businessContact.put("field_type", "FIELD_TYPE_TEXT");
        array.add(businessContact);

        JSONObject estimateProjectCost = new JSONObject();
        estimateProjectCost.put("field_title", "预估项目成本(万)(必填)");
        estimateProjectCost.put("field_type", "FIELD_TYPE_TEXT");
        array.add(estimateProjectCost);

        JSONObject merchantsTarget = new JSONObject();
        merchantsTarget.put("field_title", "招商目标(万)(必填)");
        merchantsTarget.put("field_type", "FIELD_TYPE_TEXT");
        array.add(merchantsTarget);

        JSONObject projectEndTime = new JSONObject();
        projectEndTime.put("field_title", "上线周期结束时间(本字段空,表示长期有效)");
        projectEndTime.put("field_type", "FIELD_TYPE_TEXT");
        array.add(projectEndTime);

        JSONObject projectBeginTime = new JSONObject();
        projectBeginTime.put("field_title", "上线周期开始时间(必填格式:2024-01-01)");
        projectBeginTime.put("field_type", "FIELD_TYPE_TEXT");
        array.add(projectBeginTime);

        JSONObject merchantsEnd = new JSONObject();
        merchantsEnd.put("field_title", "招商周期结束时间(本字段空,表示长期有效)");
        merchantsEnd.put("field_type", "FIELD_TYPE_TEXT");
        array.add(merchantsEnd);

        JSONObject merchantsBegin = new JSONObject();
        merchantsBegin.put("field_title", "招商周期开始时间(必填:格式:2024-01-01)");
        merchantsBegin.put("field_type", "FIELD_TYPE_TEXT");
        array.add(merchantsBegin);

        JSONObject merchantsLevel = new JSONObject();
        merchantsLevel.put("field_title", "招商等级(必填)");
        merchantsLevel.put("field_type", "FIELD_TYPE_TEXT");
        array.add(merchantsLevel);

        JSONObject secondArea = new JSONObject();
        secondArea.put("field_title", "二级分区");
        secondArea.put("field_type", "FIELD_TYPE_TEXT");
        array.add(secondArea);

        JSONObject firstArea = new JSONObject();
        firstArea.put("field_title", "一级分区");
        firstArea.put("field_type", "FIELD_TYPE_TEXT");
        array.add(firstArea);

        JSONObject shareType = new JSONObject();
        shareType.put("field_title", "共创类型(必填:共创or非共创)");
        shareType.put("field_type", "FIELD_TYPE_TEXT");
        array.add(shareType);

        JSONObject marketingTeam = new JSONObject();
        marketingTeam.put("field_title", "营销团队(必填:垂类生态组or平台内容组or创新赋能组)");
        marketingTeam.put("field_type", "FIELD_TYPE_TEXT");
        array.add(marketingTeam);

        JSONObject general = new JSONObject();
        general.put("field_title", "常规/定制(必填:常规or定制)");
        general.put("field_type", "FIELD_TYPE_TEXT");
        array.add(general);

        JSONObject secondCategory = new JSONObject();
        secondCategory.put("field_title", "二级类目(必填)");
        secondCategory.put("field_type", "FIELD_TYPE_TEXT");
        array.add(secondCategory);

        JSONObject firstCategory = new JSONObject();
        firstCategory.put("field_title", "一级类目(必填)");
        firstCategory.put("field_type", "FIELD_TYPE_TEXT");
        array.add(firstCategory);

        JSONObject type = new JSONObject();
        type.put("field_title", "项目类型(必填)");
        type.put("field_type", "FIELD_TYPE_TEXT");
        array.add(type);

        JSONObject property = new JSONObject();
        property.put("field_title", "项目属性(必填)");
        property.put("field_type", "FIELD_TYPE_TEXT");
        array.add(property);

        JSONObject projectName = new JSONObject();
        projectName.put("field_title", "项目名称(必填)");
        projectName.put("field_type", "FIELD_TYPE_TEXT");
        array.add(projectName);
        return array;
    }
}
