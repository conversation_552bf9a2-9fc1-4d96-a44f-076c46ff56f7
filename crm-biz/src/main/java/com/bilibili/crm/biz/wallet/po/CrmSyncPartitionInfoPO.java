package com.bilibili.crm.biz.wallet.po;

import com.baomidou.mybatisplus.annotation.TableName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
@TableName("crm_sync_partition_info")
public class CrmSyncPartitionInfoPO {
    private String logDate;

    private String scene;

    private String logHour;

    private Integer isDeleted;

    private LocalDateTime ctime;

    private LocalDateTime mtime;
}
