package com.bilibili.crm.biz.merchants.component;

import com.alibaba.excel.write.handler.SheetWriteHandler;
import com.alibaba.excel.write.metadata.holder.WriteSheetHolder;
import com.alibaba.excel.write.metadata.holder.WriteWorkbookHolder;
import com.bilibili.crm.biz.merchants.config.MerchantsConfig;
import com.bilibili.crm.biz.merchants.dto.PackageInfoDto;
import com.bilibili.crm.biz.merchants.enums.MerchantsSeatTypeEnum;
import com.bilibili.crm.biz.merchants.enums.PackageWorkOrderTypeEnum;
import com.bilibili.crm.biz.merchants.po.PackageWorkOrderPo;
import com.bilibili.crm.platform.biz.util.DateUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.poi.ss.usermodel.*;
import org.apache.poi.ss.util.CellRangeAddress;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2025/4/13
 */
@Slf4j
public class CustomerSheetWriteHandler implements SheetWriteHandler {

    public CustomerSheetWriteHandler() {

    }

    public CustomerSheetWriteHandler(PackageInfoDto packageInfoDto) {
        this.packageInfoDto = packageInfoDto;
    }

    private PackageInfoDto packageInfoDto;

    @Override
    public void beforeSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

    }

    @Override
    public void afterSheetCreate(WriteWorkbookHolder writeWorkbookHolder, WriteSheetHolder writeSheetHolder) {

        // -- 定义第一行
        Workbook workbook = writeWorkbookHolder.getWorkbook();
        Sheet sheetAt = workbook.getSheetAt(0);
        Font font = workbook.createFont();
        font.setColor(IndexedColors.WHITE1.getIndex());
        Row row1 = sheetAt.createRow(0);
        row1.setHeightInPoints(30);
        Cell cell1 = row1.createCell(0);

        String projectName = packageInfoDto.getProjectInfoPo().getName();
        PackageWorkOrderPo workOrderPo = packageInfoDto.getPackageWorkOrderPo();
        List<String> exportFields = new ArrayList<>();
        if (workOrderPo.getType().equals(PackageWorkOrderTypeEnum.CUSTOMER_PACKAGE.getCode())) {
            exportFields = MerchantsConfig.customerExportFields;
        } else if (StringUtils.isNotBlank(workOrderPo.getExportFields())) {
            exportFields = Arrays.asList(workOrderPo.getExportFields().split(","));
        }

        if (CollectionUtils.isEmpty(exportFields)) {
            log.error("afterSheetCreate exportFields is empty");
            return;
        }

        String seatTypeStr = MerchantsSeatTypeEnum.getType(Byte.valueOf(workOrderPo.getSeatType()));
        String seatType = StringUtils.isBlank(workOrderPo.getSeatTypeDesc()) ? seatTypeStr : seatTypeStr + "-" + workOrderPo.getSeatTypeDesc();
        String type = PackageWorkOrderTypeEnum.getDesc(workOrderPo.getType());
        String commitDate = DateUtils.convert(workOrderPo.getCtime());
        cell1.setCellValue(projectName + "-" + seatType + "-" + type + commitDate);

        CellStyle cellStyle = workbook.createCellStyle();
        cellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        cellStyle.setAlignment(HorizontalAlignment.CENTER);

        cellStyle.setFillForegroundColor(IndexedColors.SKY_BLUE.getIndex());
        cellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        cell1.setCellStyle(cellStyle);

        sheetAt.addMergedRegionUnsafe(new CellRangeAddress(0, 0, 0, exportFields.size() - 1));
        // --

        // --  第二行-项目上线周期
        Row row2 = sheetAt.createRow(1);
        row2.setHeightInPoints(30);
        Cell cell = row2.createCell(0);
        cell.setCellValue("项目上线周期");
        CellStyle projectTimeStyle = workbook.createCellStyle();
        projectTimeStyle.setFillForegroundColor(IndexedColors.GREY_80_PERCENT.getIndex());
        projectTimeStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        projectTimeStyle.setFont(font);
        cell.setCellStyle(projectTimeStyle);

        Cell cell2 = row2.createCell(1);
        cell2.setCellValue(workOrderPo.getProjectTime());
        // --

        // --  第三行-项目打包价格
        Row row3 = sheetAt.createRow(2);
        row3.setHeightInPoints(30);
        Cell cell3 = row3.createCell(0);
        cell3.setCellValue("项目打包价格");
        cell3.setCellStyle(projectTimeStyle);

        Cell cell4 = row3.createCell(1);

        Long packageSumNetPrice = workOrderPo.getPackageSumNetPrice();
        String packageSumNetPriceStr = "";
        if (null != packageSumNetPrice) {
            BigDecimal bigDecimal = new BigDecimal(packageSumNetPrice);
            packageSumNetPriceStr = bigDecimal.divide(new BigDecimal(1000000), 2, RoundingMode.HALF_UP).toString();
        }
        cell4.setCellValue(packageSumNetPriceStr + "万元");
        // --

        // -- 第四行
        Row row4 = sheetAt.createRow(3);
        row4.setHeightInPoints(30);
        Cell blankCell = row4.createCell(0);
        blankCell.setCellStyle(cellStyle);
        sheetAt.addMergedRegionUnsafe(new CellRangeAddress(3, 3, 0, exportFields.size() - 1));


        // -- 开始构建数据
        Row dataRow = sheetAt.createRow(4);
        dataRow.setHeightInPoints(30);
        CellStyle dataCellStyle = workbook.createCellStyle();
        dataCellStyle.setFillForegroundColor(IndexedColors.GREY_80_PERCENT.getIndex());
        dataCellStyle.setFillPattern(FillPatternType.SOLID_FOREGROUND);
        dataCellStyle.setVerticalAlignment(VerticalAlignment.CENTER);
        dataCellStyle.setAlignment(HorizontalAlignment.CENTER);
        dataCellStyle.setFont(font);

        for (int i = 0; i < exportFields.size(); i++) {
            Cell dataCell = dataRow.createCell(i);
            dataCell.setCellValue(exportFields.get(i));
            dataCell.setCellStyle(dataCellStyle);
            sheetAt.setColumnWidth(i, 256 * 20);
        }

        int size = packageInfoDto.getDetailPos().size();

        // 构建最后一行
        int lastRowSize = 6 + size;
        Row lastRow = sheetAt.createRow(lastRowSize);
        lastRow.setHeightInPoints(30);
        Cell attentionKey = lastRow.createCell(0);
        attentionKey.setCellValue("注意事项");

        Cell attentionValue = lastRow.createCell(1);
        List<String> strings = parseAttention(packageInfoDto.getPackageWorkOrderPo().getAttention());
        if (CollectionUtils.isEmpty(strings)) {
            attentionValue.setCellValue("");
            return;
        }
        attentionValue.setCellValue(strings.get(0));
        if (strings.size() < 2) {
            return;
        }
        for (int i = 1; i < strings.size(); i++) {
            Row attentionRow = sheetAt.createRow(lastRowSize + i);
            attentionRow.setHeightInPoints(30);
            Cell attentionRowFirstCell = attentionRow.createCell(0);
            attentionRowFirstCell.setCellValue("");
            Cell attentionRowSecondCell = attentionRow.createCell(1);
            attentionRowSecondCell.setCellValue(strings.get(i));
        }

    }

    private List<String> parseAttention(String attention) {
        if (StringUtils.isBlank(attention)) {
            return Collections.emptyList();
        }
        try {
            String[] parts = attention.split("\\r?\\n");
            return Arrays.asList(parts);
        } catch (Exception e) {
            log.error("parseAttention error", e);
        }
        return Collections.emptyList();
    }
}
