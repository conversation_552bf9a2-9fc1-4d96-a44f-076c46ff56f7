package com.bilibili.crm.biz.portal.dto;

import io.swagger.annotations.ApiModelProperty;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * <AUTHOR>
 * date 2024/12/19 19:25.
 * Contact: <EMAIL>.
 */
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class GroupCustomerPortraitBaseDTO implements Serializable {

    private static final long serialVersionUID = 6830665036650027079L;

    private String name;

    private String customerFromGroup;

    private String customerOpenTime;

    private String brandSales;

    private String rtbSales;

    private String pickupSales;

    private String categoryNames;


    private String brandFirstConsumeDate;

    private String brandNewConsumeDate;

    private String brandNewConsumeDiffDate;


    private String hardFirstConsumeDate;

    private String hardNewConsumeDate;

    private String hardNewConsumeDiffDate;


    private String rtbFirstConsumeDate;

    private String rtbNewConsumeDate;

    private String rtbNewConsumeDiffDate;

    private String pickupFirstConsumeDate;

    private String pickupNewConsumeDate;

    private String pickupNewConsumeDiffDate;

    private String groupFirstConsumeDate;
    private String groupNewConsumeDate;
    private String groupNewConsumeDiffDate;
}

