package com.bilibili.crm.biz.wallet.bean;

import com.bilibili.crm.biz_common.olap.UniqKey;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.elasticsearch.search.sort.SortOrder;

import java.io.Serializable;
import java.sql.Timestamp;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class DssAdOverviewEsBean implements Serializable {

    private static final long serialVersionUID = 1L;

    @UniqKey(esFieldName = "id", esSortOrder = SortOrder.DESC)
    private String id;

    /**
     * 日期
     */
    @JsonFormat(timezone = "Asia/Shanghai", pattern="yyyy-MM-dd")
    private Timestamp date;
    /**
     * 可见曝光量
     */
    private Integer pv;
    /**
     * 广告点击量
     */
    private Integer click;
    /**
     * 客户ID
     */
    private Integer accountId;
    /**
     *  是否是内部账号 -1其他 0外部广告 1内部广告
     */
    private Integer isInner;
    /**
     * 部门id
     */
    private Integer departmentId;

    /**
     * 广告位类型 1-信息流 2-播放页相关推荐 3-up互选位 4-移动端banner 5-闪屏 6-焦点图 7-通栏 8-播放页横幅 9-推广视频位
     * 10-右侧焦点图  11-弹幕资源位 12-动态信息流 13-播放页浮层	由src_id映射过来
     */
    private Integer srcType;

    private String accountName;
    private Integer active;
    private Integer adType;
    private Integer bidderId;
    private String bidderName;
    private Integer businessSideId;
    private String businessSideName;
    private Integer businessType;
    private Integer campaignId;
    private String campaignName;
    private Integer categoryFirstId;
    private String categoryFirstName;
    private Integer categorySecondId;
    private String categorySecondName;
    private String companyAccountId;
    private String companyAccountName;
    private Integer companyGroupId;
    private String companyGroupName;
    private String companyName;
    private Integer convAddToCart;
    private Integer convFormSubmit;
    private Integer convOrderPlace;
    private Integer convUserCost;
    private Integer convUserCostValue;
    private Integer convUserRegister;
    private Integer convWant;
    private Integer creativeId;
    private Integer crmAgentId;
    private String crmAgentName;
    private Integer crmOrderId;
    private Integer crmOrderResourceType;
    private String customerFirstId;
    private String customerFirstName;
    private String customerSecondId;
    private String customerSecondName;
    private String departmentName;
    private Integer dislikeComplaint;
    private Integer dislikeIrrelevant;
    private Integer dislikeSimilarContent;
    private Integer dislikeUninterested;
    private Integer externalCost;
    private String firstIndustryTagId;
    private String firstIndustryTagName;
    private Integer internalCost;
    private Integer isAdLoc;
    private Integer isProgram;
    private Integer orderId;
    private String orderName;
    private Integer orderResourceType;
    private Integer pageId;
    private String pageName;
    private Integer platformId;
    private String platformName;
    private Integer productType;
    private Integer qingyinComplaint;
    private Integer reserve;
    private Integer resourceId;
    private String resourceName;
    private Integer salesCategory;
    private Integer salesChannel;
    private Long salesModel;
    private Integer salesType;
    private Integer scheduleId;
    private String scheduleName;
    private String secondIndustryTagId;
    private String secondIndustryTagName;
    private Integer srcCategory;
    private Integer srcId;
    private Integer srcIndex;
    private String srcName;
    private Integer srcPlatform;
    private Integer styleAbility;
    private Long taskId;
    private String taskName;
    private String templateId;
    private String templateName;
    private String thirdIndustryTagId;
    private String thirdIndustryTagName;
    private Integer unitId;
    private String unitName;
}
