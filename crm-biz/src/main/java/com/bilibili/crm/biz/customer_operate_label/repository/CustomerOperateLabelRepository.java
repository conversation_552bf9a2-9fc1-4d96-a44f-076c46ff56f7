package com.bilibili.crm.biz.customer_operate_label.repository;

import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.baomidou.mybatisplus.extension.plugins.pagination.Page;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.biz.customer_operate_label.bo.QueryCustomerOperateLabelParam;
import com.bilibili.crm.biz.customer_operate_label.convert.CustomerOperateLabelConvert;
import com.bilibili.crm.biz.customer_operate_label.dao.CustomerOperateLabelMapper;
import com.bilibili.crm.biz.customer_operate_label.dto.CustomerOperateLabelDto;
import com.bilibili.crm.biz.customer_operate_label.dto.EasyCustomerOperateLabelDto;
import com.bilibili.crm.biz.customer_operate_label.enums.CustomerOperateLabelStatusEnum;
import com.bilibili.crm.biz.customer_operate_label.enums.LabelCustomerTypeEnum;
import com.bilibili.crm.biz.customer_operate_label.po.CustomerOperateLabelPo;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/19
 */
@Repository
public class CustomerOperateLabelRepository {

    @Resource
    private CustomerOperateLabelMapper mapper;

    public List<CustomerOperateLabelDto> queryByCustomerIdType(Integer customerId, LabelCustomerTypeEnum labelCustomerTypeEnum,
                                                               CustomerOperateLabelStatusEnum status) {
        List<CustomerOperateLabelPo> pos = null;
        LambdaQueryWrapper<CustomerOperateLabelPo> wrapper = Wrappers.lambdaQuery(CustomerOperateLabelPo.class);

        if (null != status) {
            wrapper.eq(CustomerOperateLabelPo::getStatus, status.getCode());
        }

        if (labelCustomerTypeEnum == LabelCustomerTypeEnum.ADVERTISER) {
            pos = mapper.selectList(wrapper.eq(CustomerOperateLabelPo::getCustomerId, customerId));
        } else if (labelCustomerTypeEnum == LabelCustomerTypeEnum.SERVICE_PROVIDER) {
            pos = mapper.selectList(wrapper.eq(CustomerOperateLabelPo::getServiceProviderId, customerId));
        }

        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(CustomerOperateLabelConvert::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    public Pagination<List<CustomerOperateLabelDto>> pageQuery(QueryCustomerOperateLabelParam param) {
        LambdaQueryWrapper<CustomerOperateLabelPo> wrapper = Wrappers.lambdaQuery(CustomerOperateLabelPo.class);
        buildQueryWrapper(wrapper, param);
        Page<CustomerOperateLabelPo> result = mapper.selectPage(new Page<>(param.getPage(), param.getPageSize()), wrapper);
        List<CustomerOperateLabelPo> pos = result.getRecords();
        List<CustomerOperateLabelDto> dtos;
        if (CollectionUtils.isEmpty(pos)) {
            dtos = Collections.emptyList();
        } else {
            dtos = pos.stream().map(CustomerOperateLabelConvert::convert).filter(Objects::nonNull).collect(Collectors.toList());
        }
        return new Pagination<>((int) result.getPages(), (int) result.getTotal(), dtos);
    }


    public List<CustomerOperateLabelDto> query(QueryCustomerOperateLabelParam param) {
        LambdaQueryWrapper<CustomerOperateLabelPo> wrapper = Wrappers.lambdaQuery(CustomerOperateLabelPo.class);
        buildQueryWrapper(wrapper, param);
        List<CustomerOperateLabelPo> pos = mapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(CustomerOperateLabelConvert::convert).filter(Objects::nonNull).collect(Collectors.toList());
    }

    private void buildQueryWrapper(LambdaQueryWrapper<CustomerOperateLabelPo> wrapper, QueryCustomerOperateLabelParam param) {
        if (null != param.getCustomerId()) {
            wrapper.eq(CustomerOperateLabelPo::getCustomerId, param.getCustomerId());
        }

        if (null != param.getServiceProviderId()) {
            wrapper.eq(CustomerOperateLabelPo::getServiceProviderId, param.getServiceProviderId());
        }

        if (null != param.getCustomerOperateLabelType()) {
            wrapper.eq(CustomerOperateLabelPo::getType, param.getCustomerOperateLabelType().getCode());
        }

        if (CollectionUtils.isNotEmpty(param.getStatus())) {
            wrapper.in(CustomerOperateLabelPo::getStatus, param.getStatus());
        }

        if (CollectionUtils.isNotEmpty(param.getCustomerIds())) {
            wrapper.in(CustomerOperateLabelPo::getCustomerId, param.getCustomerIds());
        }

        if (null != param.getGeApplyCtime()) {
            wrapper.ge(CustomerOperateLabelPo::getCtime, param.getGeApplyCtime());
        }
        if (null != param.getLeApplyCtime()) {
            wrapper.le(CustomerOperateLabelPo::getCtime, param.getLeApplyCtime());
        }

        if (null != param.getGeServiceBeginTime()) {
            wrapper.ge(CustomerOperateLabelPo::getServiceBeginTime, param.getGeServiceBeginTime());
        }
        if (null != param.getLeServiceEndTime()) {
            wrapper.le(CustomerOperateLabelPo::getServiceEndTime, param.getLeServiceEndTime());
        }

        if (null != param.getLtServiceEndTime()) {
            wrapper.lt(CustomerOperateLabelPo::getServiceEndTime, param.getLtServiceEndTime());
        }

        if (null != param.getLeServiceBeginTime()) {
            wrapper.le(CustomerOperateLabelPo::getServiceBeginTime, param.getLeServiceBeginTime());
        }

        if (null != param.getGtServiceEndTime()) {
            wrapper.gt(CustomerOperateLabelPo::getServiceEndTime, param.getGtServiceEndTime());
        }

        if (null != param.getGeServiceEndTimeOr() && null != param.getLeServiceBeginTimeOr()) {
            wrapper.and(wp -> wp.ge(CustomerOperateLabelPo::getServiceEndTime, param.getGeServiceEndTimeOr())
                    .or().le(CustomerOperateLabelPo::getServiceBeginTime, param.getLeServiceBeginTimeOr()));
        }
    }

    public void insertCustomerOperateLabel(CustomerOperateLabelDto dto, boolean applyEnable) {
        LocalDateTime applyServiceBeginTime = dto.getApplyServiceBeginTime();
        LocalDateTime applyServiceEndTime = dto.getApplyServiceEndTime();
        LocalDateTime now = LocalDateTime.now();
        if (applyEnable) {
            dto.setStatus(CustomerOperateLabelStatusEnum.APPROVE_ING.getCode());
        }

        if (!applyEnable && (now.isAfter(applyServiceBeginTime) || now.equals(applyServiceBeginTime)) && now.isBefore(applyServiceEndTime)) {
            dto.setStatus(CustomerOperateLabelStatusEnum.EFFECT_ING.getCode());
        }

        if (!applyEnable && now.isBefore(applyServiceBeginTime)) {
            dto.setStatus(CustomerOperateLabelStatusEnum.EFFECT_PENDING.getCode());
        }

        if (!applyEnable && now.isAfter(applyServiceEndTime)) {
            dto.setStatus(CustomerOperateLabelStatusEnum.EXPIRED.getCode());
        }

        CustomerOperateLabelPo po = CustomerOperateLabelConvert.convert(dto);
        mapper.insert(po);
    }

    public void discard(Long id, Byte oldStatus, Byte status) {
        CustomerOperateLabelPo po = new CustomerOperateLabelPo();
        po.setId(id);
        po.setStatus(status);
        if (oldStatus == CustomerOperateLabelStatusEnum.APPROVE_ING.getCode() || oldStatus == CustomerOperateLabelStatusEnum.EFFECT_PENDING.getCode()) {
            po.setServiceEndTime(LocalDateTime.of(LocalDate.of(1970, 1, 1), LocalTime.of(0, 0, 0)));
        } else {
            po.setServiceEndTime(LocalDateTime.of(LocalDate.now(), LocalTime.of(23, 59, 59)));
        }
        mapper.updateById(po);
    }

    public void updateById(CustomerOperateLabelDto customerOperateLabelDto) {
        if (null == customerOperateLabelDto) {
            return;
        }
        CustomerOperateLabelPo po = new CustomerOperateLabelPo();
        po.setId(customerOperateLabelDto.getId());
        if (null != customerOperateLabelDto.getStatus()) {
            po.setStatus(customerOperateLabelDto.getStatus());
        }
        mapper.updateById(po);
    }

    public CustomerOperateLabelPo selectForUpdateById(Long id) {
        LambdaQueryWrapper<CustomerOperateLabelPo> wrapper = Wrappers.lambdaQuery(CustomerOperateLabelPo.class);
        wrapper.eq(CustomerOperateLabelPo::getId, id).last("for update");
        return mapper.selectOne(wrapper);
    }

    public CustomerOperateLabelDto queryByOrderId(String orderId) {
        LambdaQueryWrapper<CustomerOperateLabelPo> wrapper = Wrappers.lambdaQuery(CustomerOperateLabelPo.class);
        wrapper.eq(CustomerOperateLabelPo::getOaFlowOrderId, orderId);
        return CustomerOperateLabelConvert.convert(mapper.selectOne(wrapper));
    }

    public List<EasyCustomerOperateLabelDto> batchEasyDto(QueryCustomerOperateLabelParam param) {
        LambdaQueryWrapper<CustomerOperateLabelPo> wrapper = Wrappers.lambdaQuery(CustomerOperateLabelPo.class);
        buildQueryWrapper(wrapper, param);
        List<CustomerOperateLabelPo> pos = mapper.selectList(wrapper);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(CustomerOperateLabelConvert::convertEasyDto).filter(Objects::nonNull).collect(Collectors.toList());
    }
}
