package com.bilibili.crm.common;

import lombok.extern.slf4j.Slf4j;

import java.util.function.Supplier;

@Slf4j
public class SupplierLogEx<T> implements Supplier<T> {

    private final String scene;

    private final Supplier<T> supplier;

    public SupplierLogEx(String scene, Supplier<T> supplier) {
        this.scene = scene;
        this.supplier = supplier;
    }

    static public <T> Supplier<T> wrap(String scene, Supplier<T> supplier) {
        return new SupplierLogEx<>(scene, supplier);
    }

    @Override
    public T get() {
        try {
            return supplier.get();
        } catch (Throwable t) {
            AlarmHelper.alarmEx("SupplierLog", t, scene);
            throw t;
        }
    }
}
