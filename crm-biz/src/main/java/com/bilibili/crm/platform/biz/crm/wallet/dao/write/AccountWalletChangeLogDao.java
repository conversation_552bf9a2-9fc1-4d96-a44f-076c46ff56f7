package com.bilibili.crm.platform.biz.crm.wallet.dao.write;

import com.bilibili.crm.platform.biz.po.AccountWalletChangeLogPo;
import com.bilibili.crm.platform.biz.po.AccountWalletChangeLogPoExample;
import java.util.List;

import org.apache.ibatis.annotations.Param;

public interface AccountWalletChangeLogDao {
    long countByExample(AccountWalletChangeLogPoExample example);

    int deleteByExample(AccountWalletChangeLogPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(AccountWalletChangeLogPo record);

    int insertBatch(List<AccountWalletChangeLogPo> records);

    int insertUpdateBatch(List<AccountWalletChangeLogPo> records);

    int insert(AccountWalletChangeLogPo record);

    int insertUpdateSelective(AccountWalletChangeLogPo record);

    int insertSelective(AccountWalletChangeLogPo record);

    List<AccountWalletChangeLogPo> selectByExample(AccountWalletChangeLogPoExample example);

    AccountWalletChangeLogPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AccountWalletChangeLogPo record, @Param("example") AccountWalletChangeLogPoExample example);

    int updateByExample(@Param("record") AccountWalletChangeLogPo record, @Param("example") AccountWalletChangeLogPoExample example);

    int updateByPrimaryKeySelective(AccountWalletChangeLogPo record);

    int updateByPrimaryKey(AccountWalletChangeLogPo record);
}