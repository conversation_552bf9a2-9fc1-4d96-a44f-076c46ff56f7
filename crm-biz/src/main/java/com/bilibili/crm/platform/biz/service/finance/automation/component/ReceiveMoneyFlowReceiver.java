package com.bilibili.crm.platform.biz.service.finance.automation.component;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.SafeStopWatch;
import com.bilibili.crm.platform.api.finance.dto.automation.CrmRevenueExpenditureFlowDto;
import com.bilibili.crm.platform.api.finance.dto.automation.MainStationReceiveMoneyFlowInfo;
import com.bilibili.crm.platform.api.finance.enums.IsProxyPayEnum;
import com.bilibili.crm.platform.api.finance.enums.ReceiveMoneyFlowTypeEnum;
import com.bilibili.crm.platform.biz.convertor.RevenueExpenditureFlowDtoConvertor;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.repo.finance.CrmFlowAdditionInfoRepo;
import com.bilibili.crm.platform.biz.repo.finance.CrmRevenueExpenditureFlowRepo;
import com.bilibili.crm.platform.biz.repo.workflow.CrmWorkFlowRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.flowtype.INotProxyPayProcessor;
import com.bilibili.crm.platform.biz.service.finance.automation.component.flowtype.NotProxyProcessorFactory;
import com.bilibili.crm.platform.biz.service.finance.automation.component.flowtype.ReceiveMoneyFlowTypeJudgeProcessor;
import com.bilibili.crm.platform.biz.service.finance.automation.component.other.FlowAdditionInfoProcessor;
import com.bilibili.crm.platform.biz.service.finance.automation.component.other.ReceiveMoneyFlowProcessor;
import com.bilibili.crm.platform.biz.service.finance.automation.component.proxypay.ProxyPayProcessor;
import com.bilibili.crm.platform.biz.service.finance.automation.component.statemachine.FlowAdditionInfoStateMachine;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.CrmConstant;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.site.lookup.util.StringUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Objects;

/**
 * 回款流水接收处器
 *
 * <AUTHOR>
 * @date 2021/1/25 11:27 上午
 */
@Slf4j
@Component
public class ReceiveMoneyFlowReceiver {

    @Autowired
    protected CrmRevenueExpenditureFlowRepo revenueExpenditureFlowRepo;
    @Autowired
    protected CrmWorkFlowRepo crmWorkFlowRepo;
    @Autowired
    protected CrmFlowAdditionInfoRepo crmFlowAdditionInfoRepo;

    @Autowired
    private ReceiveMoneyFlowTypeJudgeProcessor receiveMoneyFlowTypeJudgeProcessor;
    @Autowired
    private FlowRepeatProcessor flowRepeatProcessor;
    @Autowired
    private ProxyPayProcessor proxyPayProcessor;
    @Autowired
    protected ReceiveMoneyFlowProcessor receiveMoneyFlowProcessor;
    @Autowired
    protected FlowAdditionInfoProcessor flowAdditionInfoProcessor;
    @Autowired
    protected NotProxyProcessorFactory notProxyProcessorFactory;
    @Autowired
    private FlowAdditionInfoStateMachine flowAdditionInfoStateMachine;
    @Autowired
    private RedisLock redisLock;

    @Value("#{'${receive.money.flow.test.receivers:张健哲;汪俊峰}'.split(';')}")
    private List<String> testReceivers;

    @Value("${crm.portal.env:prd}")
    private String env;

    /**
     * 回款流水处理，按 billId 进行了并发控制
     */
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public CrmRevenueExpenditureFlowDto onReceivedFlowMsg(MainStationReceiveMoneyFlowInfo mainStationReceiveMoneyFlowInfo) {
        log.info("=====> onReceivedFlowMsg, mainStationReceiveMoneyFlowInfo:{}", mainStationReceiveMoneyFlowInfo);

        if (CollectionUtils.isNotEmpty(testReceivers) && Objects.equals(env, "prd") &&
                StringUtils.isNotEmpty(mainStationReceiveMoneyFlowInfo.getCustomerName()) && testReceivers.contains(mainStationReceiveMoneyFlowInfo.getCustomerName())) {
            log.warn("线上测试流水不处理, billId:{}", mainStationReceiveMoneyFlowInfo.getBillId());
            // 线上测试流水不处理
            return CrmRevenueExpenditureFlowDto.builder().build();
        }

        // 获取分布式锁
        String receiveFlowLockKey = mainStationReceiveMoneyFlowInfo.getBillId() + "_" + CrmConstant.RECEIVE_FLOW_LOCK_SUFFIX;
        redisLock.getLock(receiveFlowLockKey, 5000);

        CrmRevenueExpenditureFlowDto crmRevenueExpenditureFlowDto = CrmRevenueExpenditureFlowDto.builder().build();
        try {
            MDC.put("bill_id", Objects.toString(mainStationReceiveMoneyFlowInfo.getBillId(), "--"));
            // 业务处理
            crmRevenueExpenditureFlowDto = CatUtils.newTransactionAndReturn("financeReceiveMoneyFlow",
                    this.getClass().getSimpleName() + "_onReceivedFlowMsg",
                    transaction -> {
                        SafeStopWatch stopWatch = new SafeStopWatch("ExpFlow");
                        stopWatch.pushTask("ConvertDto");
                        CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto =
                                RevenueExpenditureFlowDtoConvertor.convertMainStationFlowInfo2FlowDto(mainStationReceiveMoneyFlowInfo);
                        log.info("=====> onReceivedFlowMsg, flowDto:{}", JSON.toJSONString(receiveMoneyFlowDataDto));
                        // 接收到回款流水，参数判断
                        checkParams(receiveMoneyFlowDataDto);

                        if (StringUtils.isEmpty(receiveMoneyFlowDataDto.getPayAccountName())) {
                            receiveMoneyFlowDataDto.setPayAccountName("汇款公司为空");
                        }

                        // 流水号判重处理
                        Cat.logEvent("onReceivedFlowMsg", "流水判重", Event.SUCCESS, String.format("flowNo=%s&billId=%s",
                                receiveMoneyFlowDataDto.getFlowNo(), receiveMoneyFlowDataDto.getBillId()));
                        stopWatch.pushTask("CheckRepeat");
                        AlarmHelper.log("CheckRepeat", receiveMoneyFlowDataDto);
                        if (!flowRepeatProcessor.checkRepeat(receiveMoneyFlowDataDto)) {
                            // 创建流水主表与副表
                            Cat.logEvent("onReceivedFlowMsg", "创建流水");
                            stopWatch.pushTask("EnterUnrecognized");
                            flowAdditionInfoStateMachine.enterUnrecognized(receiveMoneyFlowDataDto);
                            stopWatch.pushTask("ProcessType");
                            // 后续处理
                            processFlowType(receiveMoneyFlowDataDto);
                            AlarmHelper.log("ProcessDone");
                        } else {
                            AlarmHelper.log("Repeat");
                        }
                        stopWatch.log();

                        receiveMoneyFlowDataDto.setIsProcessOk(true);
                        receiveMoneyFlowDataDto.setProcessResultMsg("ok");
                        return receiveMoneyFlowDataDto;
                    });
        } catch (Exception e) {
            AlarmHelper.alarmEx("=====> onReceivedFlowMsg error, billId:", e, mainStationReceiveMoneyFlowInfo.getBillId(),
                    mainStationReceiveMoneyFlowInfo.getStatementId());
            crmRevenueExpenditureFlowDto.setIsProcessOk(false);
            crmRevenueExpenditureFlowDto.setProcessResultMsg(e.getMessage());
            throw e;
        } finally {
            // 释放锁
            redisLock.releaseLock(receiveFlowLockKey);
            MDC.remove("bill_id");
        }
        return crmRevenueExpenditureFlowDto;
    }

    /**
     * 处理流水类型
     * 要求 dto 中的脱敏字段是密文
     *
     * @param receiveMoneyFlowDataDto
     */
    public void processFlowType(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto) {
        // 收支流水类型判断
        judgeFlowType(receiveMoneyFlowDataDto, receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum());

        // 根据确定的流水类型处理
        processByDefiniteFlowType(receiveMoneyFlowDataDto);
    }

    /**
     * 根据确定的流水类型处理，
     * 比如：是否代打款判断以及各个类型的后续处理
     *
     * @param receiveMoneyFlowDataDto
     */
    public CrmRevenueExpenditureFlowDto processByDefiniteFlowType(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto) {
        log.info("=====> processByDefiniteFlowType, flowId:{}, type:{}", receiveMoneyFlowDataDto.getId(),
                receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum());
        if (ReceiveMoneyFlowTypeEnum.UNKNOWN.equals(receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum())) {
            Cat.logEvent("onReceivedFlowMsg", "流水类型未知");
            return receiveMoneyFlowDataDto;
        }
        // 合约类型 & 没有绑定的 customerId
        if (ReceiveMoneyFlowTypeEnum.getNeedBindCustomerFlowTypeList().contains(receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum())
                && !Utils.isPositive(receiveMoneyFlowDataDto.getBindCustomerId())) {
            log.info("=====> processByDefiniteFlowType[contract type, not has bind id], flowId:{}, type:{}",
                    receiveMoneyFlowDataDto.getId(), receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum());
            Cat.logEvent("onReceivedFlowMsg", "合约流水没有绑定的客户id", Event.SUCCESS, String.format("flowId=%s&flowNo=%s" +
                    "&billId=%s", receiveMoneyFlowDataDto.getId(), receiveMoneyFlowDataDto.getFlowNo(), receiveMoneyFlowDataDto.getBillId()));
            return receiveMoneyFlowDataDto;
        }

        Cat.logEvent("financeReceiveMoneyFlow", "processByDefiniteFlowType", Event.SUCCESS, "1");
        // 判断是否代打款，进入处理中流程(确定了类型)
        flowAdditionInfoStateMachine.enterToJudgeProxyPay(receiveMoneyFlowDataDto.getId());

        IsProxyPayEnum isProxyPayEnum = proxyPayProcessor.judgeIsProxyPay(receiveMoneyFlowDataDto.getId(),
                receiveMoneyFlowDataDto.getPayAccountName(), receiveMoneyFlowDataDto.getBindCustomerName());
        log.info("=====> processFlow[is proxy pay], flowNo:{}, isProxyPayEnum:{}", receiveMoneyFlowDataDto.getFlowNo(), JSON.toJSONString(isProxyPayEnum));
        receiveMoneyFlowDataDto.setIsProxyPayEnum(isProxyPayEnum);

        receiveMoneyFlowProcessor.updateFlowIsProxyPay(receiveMoneyFlowDataDto.getId(), isProxyPayEnum.getCode());
        // 进入 代打款待处理
        flowAdditionInfoStateMachine.enterToProcessProxyPay(receiveMoneyFlowDataDto.getId());

        if (IsProxyPayEnum.NOT_PROXY_PAY.equals(isProxyPayEnum)) { // 非代打款
            Cat.logEvent("onReceivedFlowMsg", "非代打款");
            INotProxyPayProcessor notProxyPayProcessor =
                    notProxyProcessorFactory.getByType(receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum());
            // 状态：待处理
            flowAdditionInfoStateMachine.enterToProcess(receiveMoneyFlowDataDto.getId());
            // 充值与效果回款触发相应操作，并完成
            notProxyPayProcessor.process(receiveMoneyFlowDataDto);
        } else {
            Cat.logEvent("onReceivedFlowMsg", "代打款");
            if (receiveMoneyFlowDataDto.getReceiveMoneyFlowTypeEnum().equals(ReceiveMoneyFlowTypeEnum.MENG_PAI)) {
                flowAdditionInfoStateMachine.enterToProcess(receiveMoneyFlowDataDto.getId());
                flowAdditionInfoStateMachine.enterProcessCompleted(receiveMoneyFlowDataDto.getId());
            } else {
                // 代打款，进入代打款判断阶段(收款流水，仍然是 处理中)
                proxyPayProcessor.process(receiveMoneyFlowDataDto);
            }
        }
        return receiveMoneyFlowDataDto;
    }

    /**
     * 判断流水类型 同时匹配对应客户与账户 同时 指定对应流水状态
     *
     * @param receiveMoneyFlowDataDto
     * @param pointFlowTypeEnum       指定的流水类型，如果为 null 则判断类型，否则，就是该类型(手动指定类型的情况)
     * @return
     */
    public ReceiveMoneyFlowTypeEnum judgeFlowType(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto,
                                                  ReceiveMoneyFlowTypeEnum pointFlowTypeEnum) {
        Cat.logEvent("financeReceiveMoneyFlow", "judgeFlowType start...", Event.SUCCESS, "1");
        ReceiveMoneyFlowTypeEnum receiveMoneyFlowTypeEnum = null;
        if (pointFlowTypeEnum == null) {
            receiveMoneyFlowTypeEnum = receiveMoneyFlowTypeJudgeProcessor.autoJudgeFlowType(receiveMoneyFlowDataDto);
        } else {
            receiveMoneyFlowTypeEnum = pointFlowTypeEnum;
            log.info("=====> judgeFlowType[manual set type], flowId:{}, type:{}", receiveMoneyFlowDataDto.getId(),
                    pointFlowTypeEnum.getDesc());
        }
        if (receiveMoneyFlowTypeEnum == null) {
            throw new ServiceRuntimeException("判断出的流水类型不能为空！");
        }
        receiveMoneyFlowDataDto.setReceiveMoneyFlowTypeEnum(receiveMoneyFlowTypeEnum);

        log.info("=====> processFlow[judgeFlowType], flowNo:{}, receiveMoneyFlowTypeEnum:{}", receiveMoneyFlowDataDto.getFlowNo(),
                JSON.toJSONString(receiveMoneyFlowTypeEnum));

        if (ReceiveMoneyFlowTypeEnum.UNKNOWN.equals(receiveMoneyFlowTypeEnum)) {
            flowAdditionInfoStateMachine.enterToManualSetType(receiveMoneyFlowDataDto.getId(),
                    receiveMoneyFlowTypeEnum.getCode(), receiveMoneyFlowDataDto.getBindAccountId(),
                    receiveMoneyFlowDataDto.getBindCustomerId());
        } else if (ReceiveMoneyFlowTypeEnum.getNeedBindCustomerFlowTypeList().contains(receiveMoneyFlowTypeEnum)) {
            // 充值与效果回款类型，一定绑定了账号；合约回款如果匹配到了客户，则有 bindCustomerId，否则没有；未知类型一定没有 bindRefId
            if (receiveMoneyFlowDataDto.getBindCustomerId() == null) {
                // 收款流水，仍为待处理
                log.info("=====> processFlow[contract flow type, but no refId], flowNo:{}", receiveMoneyFlowDataDto.getFlowNo());
                // 修改业务状态为手工设置类型
                flowAdditionInfoStateMachine.enterToManualSetType(receiveMoneyFlowDataDto.getId(),
                        receiveMoneyFlowTypeEnum.getCode(), receiveMoneyFlowDataDto.getBindAccountId(),
                        receiveMoneyFlowDataDto.getBindCustomerId());
            } else {
                if (pointFlowTypeEnum == null) {
                    flowAdditionInfoStateMachine.enterCompareSuccess(receiveMoneyFlowDataDto.getId(),
                            receiveMoneyFlowTypeEnum.getCode(), receiveMoneyFlowDataDto.getBindAccountId(),
                            receiveMoneyFlowDataDto.getBindCustomerId());
                } else {
                    log.info("=====> judgeFlowType[合约类型绑定客户id], flowId:{}, flowType:{}, bindAccountId:{}, " +
                                    "bindCustomerId:{}",
                            receiveMoneyFlowDataDto.getId(), receiveMoneyFlowTypeEnum.getDesc(), receiveMoneyFlowDataDto.getBindAccountId(), receiveMoneyFlowDataDto.getBindCustomerId());
                    flowAdditionInfoStateMachine.enterCompareSuccess(receiveMoneyFlowDataDto.getId(),
                            receiveMoneyFlowTypeEnum.getCode(), receiveMoneyFlowDataDto.getBindAccountId(),
                            receiveMoneyFlowDataDto.getBindCustomerId());
                }
            }
        } else {
            flowAdditionInfoStateMachine.enterCompareSuccess(receiveMoneyFlowDataDto.getId(),
                    receiveMoneyFlowTypeEnum.getCode(), receiveMoneyFlowDataDto.getBindAccountId(),
                    receiveMoneyFlowDataDto.getBindCustomerId());
        }
        Cat.logEvent("financeReceiveMoneyFlow", "judgeFlowType end...", Event.SUCCESS, "1");
        return receiveMoneyFlowTypeEnum;
    }

    private void checkParams(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto) {
        StringBuilder sbMsg = new StringBuilder();
        if (StringUtils.isEmpty(receiveMoneyFlowDataDto.getFlowNo())) {
            sbMsg.append("流水编号不能为空！");
        }
        if (receiveMoneyFlowDataDto.getAmount() == null) {
            sbMsg.append("流水金额不能为空！");
        }
        if (StringUtils.isEmpty(receiveMoneyFlowDataDto.getReceiveAccountNo()) || StringUtils.isEmpty(receiveMoneyFlowDataDto.getReceiveAccountName())) {
            sbMsg.append("流水收款账号信息不能为空！");
        }
//        if (StringUtils.isEmpty(receiveMoneyFlowDataDto.getReceiveSubAccountNo()) || StringUtils.isEmpty(receiveMoneyFlowDataDto.getReceiveSubAccountName())) {
//            sbMsg.append("流水收款子账号信息不能为空！");
//        }
        if (sbMsg.length() > 0) {
            log.error("=====> checkParams, billId:{}, flowNo:{}, errorMsg:{}", receiveMoneyFlowDataDto.getBillId(), receiveMoneyFlowDataDto.getFlowNo(), sbMsg);
            throw new ServiceRuntimeException(sbMsg.toString());
        }
    }
}
