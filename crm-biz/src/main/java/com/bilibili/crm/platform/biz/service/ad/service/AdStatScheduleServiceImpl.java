package com.bilibili.crm.platform.biz.service.ad.service;


import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.crm.platform.api.ad.dto.AdStatScheduleDto;
import com.bilibili.crm.platform.api.ad.dto.QueryAdStatScheduleParam;
import com.bilibili.crm.platform.biz.adstat.AdStatScheduleDayDao;
import com.bilibili.crm.platform.biz.po.adstat.AdStatScheduleDayPo;
import com.bilibili.crm.platform.biz.po.adstat.AdStatScheduleDayPoExample;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

@Service
public class AdStatScheduleServiceImpl {

    public void test(){}
//
//    @Autowired
//    private AdStatScheduleDayDao adStatScheduleDayDao;
//
//    //@Override
//    public List<AdStatScheduleDto> queryAdStatScheduleDayByParam(QueryAdStatScheduleParam queryParam) {
//        AdStatScheduleDayPoExample example = buildExample(queryParam);
//        List<AdStatScheduleDayPo> pos = adStatScheduleDayDao.selectByExample(example);
//        if (CollectionUtils.isEmpty(pos)) {
//            return Collections.EMPTY_LIST;
//        }
//        return pos.stream().map(this::po2Dto).collect(Collectors.toList());
//    }
//
//    private AdStatScheduleDto po2Dto(AdStatScheduleDayPo po) {
//        AdStatScheduleDto dto = AdStatScheduleDto.builder().build();
//        BeanUtils.copyProperties(po, dto);
//        return dto;
//    }
//
//    private AdStatScheduleDayPoExample buildExample(QueryAdStatScheduleParam queryParam) {
//        AdStatScheduleDayPoExample example = new AdStatScheduleDayPoExample();
//        AdStatScheduleDayPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//
//        ObjectUtils.setList(queryParam::getScheduleIds, criteria::andScheduleIdIn);
//        ObjectUtils.setObject(queryParam::getSalesType, criteria::andSalesTypeEqualTo);
//
//        ObjectUtils.setObject(queryParam::getBeginTime, criteria::andGroupTimeGreaterThanOrEqualTo);
//        ObjectUtils.setObject(queryParam::getEndTime, criteria::andGroupTimeLessThan);
//
//        return example;
//    }
}
