package com.bilibili.crm.platform.biz.service.customer;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.achievement.IAchievementContractService;
import com.bilibili.crm.platform.api.achievement.IAchievementPickUpService;
import com.bilibili.crm.platform.api.achievement.dto.AchievementContractCheckQuotaDto;
import com.bilibili.crm.platform.api.achievement.dto.AchievementDto;
import com.bilibili.crm.platform.api.achievement.dto.AchievementRtbData;
import com.bilibili.crm.platform.api.achievement.dto.QueryAchieveDto;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromWalletService;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.customer.dto.CustomerTaskDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerTaskQueryDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerUpdateDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerTaskService;
import com.bilibili.crm.platform.api.enums.PickupAchieveTypeEnum;
import com.bilibili.crm.platform.api.income.dto.AdIncomeQueryParam;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.biz.dao.CrmSaleKpiDao;
import com.bilibili.crm.platform.biz.po.CrmSaleKpiPo;
import com.bilibili.crm.platform.biz.po.CrmSaleKpiPoExample;
import com.bilibili.crm.platform.biz.repo.LogOperationRepo;
import com.bilibili.crm.platform.biz.service.achievement.config.beta.DepAchieveConfigBeta;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchieveConsumeParamHelper;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.Module;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.income.IncomeProductEnum;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.bilibili.crm.platform.utils.MathUtils;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.crm.platform.api.achievement.IAchieveConfigBeta.CLUE_PASS_SALE_TYPES;
import static com.bilibili.crm.platform.biz.service.achievement.config.beta.DepAchieveConfigBeta.EXCLUDE_ACC_GROUP_IDS;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-04-13 15:40:08
 * @description:
 **/

@Slf4j
@Service
public class CustomerTaskService implements ICustomerTaskService {

    @Resource
    private CrmSaleKpiDao crmSaleKpiDao;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Autowired
    private IAgentService agentService;

    @Autowired
    private LogOperationRepo logOperationRepo;

    @Resource
    private IFastQueryAccountService fastQueryAccountService;

    @Resource
    private DepAchieveConfigBeta depAchieveConfigBeta;

    @Resource
    private IAchieveFromWalletService achieveFromWalletService;

    @Autowired
    private IAchievementPickUpService achievementPickUpService;

    @Resource
    private IAchievementContractService achievementContractService;

    @Resource
    private AchieveConsumeParamHelper achieveConsumeParamHelper;

    public static List<Integer> EFFECT_TYPE = Lists.newArrayList(
            IncomeProductEnum.CPC.getCode(),
            IncomeProductEnum.CPM.getCode(),
            IncomeProductEnum.BUSINESS_FLY_ADVANCE_PAY.getCode(),
            IncomeProductEnum.ADX.getCode(),
            IncomeProductEnum.SAN_LIAN_OTHER.getCode(),
            IncomeProductEnum.DPA.getCode()
    );


    @Override
    public List<CustomerTaskDto> queryTaskInfoByQuarter(CustomerTaskQueryDto dto, Operator operator) {
        List<String> timeDescList = generateMonthDescByQuarter(dto.getTimeDesc());
        CrmSaleKpiPoExample example = new CrmSaleKpiPoExample();
        CrmSaleKpiPoExample.Criteria criteria = example.createCriteria();
        criteria.andBizIdEqualTo(dto.getCustomerId());
        criteria.andBizTypeIn(Lists.newArrayList(SaleKpiTypeEnum.AGENT_CUSTOMER_EFFECT.getBizId(), SaleKpiTypeEnum.AGENT_CUSTOMER_FIREWORK.getBizId()));
        criteria.andQuarterStrIn(timeDescList);
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleKpiPo> saleKpiPos = crmSaleKpiDao.selectByExample(example);
        Map<String, List<CrmSaleKpiPo>> saleKpiMap = Optional.ofNullable(saleKpiPos).orElse(new ArrayList<>()).stream()
                .collect(Collectors.groupingBy(CrmSaleKpiPo::getQuarterStr));

        List<CustomerTaskDto> taskDtoList = new ArrayList<>();
        for (String timeDesc : timeDescList) {
            List<CrmSaleKpiPo> pos = saleKpiMap.getOrDefault(timeDesc, new ArrayList<>());
            CustomerTaskDto taskDto = new CustomerTaskDto();
            taskDto.setCustomerId(dto.getCustomerId());
            taskDto.setTimeDesc(timeDesc);
            for (CrmSaleKpiPo po : pos) {
                if (po.getBizType().equals(SaleKpiTypeEnum.AGENT_CUSTOMER_EFFECT.getBizId())) {
                    taskDto.setEffectTaskAmount(Utils.fromFenToYuan(po.getQuarterKpi()));
                } else if (po.getBizType().equals(SaleKpiTypeEnum.AGENT_CUSTOMER_FIREWORK.getBizId())) {
                    taskDto.setFireworkTaskAmount(Utils.fromFenToYuan(po.getQuarterKpi()));
                }
            }
            taskDto.setEffectTaskAmount(Optional.ofNullable(taskDto.getEffectTaskAmount()).orElse(new BigDecimal(0)));
            taskDto.setFireworkTaskAmount(Optional.ofNullable(taskDto.getFireworkTaskAmount()).orElse(new BigDecimal(0)));
            taskDtoList.add(taskDto);
        }
        taskDtoList = buildRatioInfoWithQuarter(taskDtoList, dto);
        return taskDtoList;
    }

    @Override
    public CustomerUpdateDto queryTaskInfoByMonth(CustomerTaskQueryDto dto, Operator operator) {
        CrmSaleKpiPoExample example = new CrmSaleKpiPoExample();
        CrmSaleKpiPoExample.Criteria criteria = example.createCriteria();
        criteria.andBizIdEqualTo(dto.getCustomerId());
        criteria.andBizTypeIn(Lists.newArrayList(SaleKpiTypeEnum.AGENT_CUSTOMER_EFFECT.getBizId(), SaleKpiTypeEnum.AGENT_CUSTOMER_FIREWORK.getBizId()));
        criteria.andQuarterStrEqualTo(dto.getTimeDesc());
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleKpiPo> saleKpiPos = crmSaleKpiDao.selectByExample(example);
        CustomerUpdateDto taskDto = new CustomerUpdateDto();
        taskDto.setCustomerId(dto.getCustomerId());
        taskDto.setTimeDesc(dto.getTimeDesc());
        for (CrmSaleKpiPo po : Optional.ofNullable(saleKpiPos).orElse(new ArrayList<>())) {
            if (po.getBizType().equals(SaleKpiTypeEnum.AGENT_CUSTOMER_EFFECT.getBizId())) {
                taskDto.setEffectTaskAmount(Utils.fromFenToYuan(po.getQuarterKpi()));
            } else if (po.getBizType().equals(SaleKpiTypeEnum.AGENT_CUSTOMER_FIREWORK.getBizId())) {
                taskDto.setFireworkTaskAmount(Utils.fromFenToYuan(po.getQuarterKpi()));
            }
        }
        return taskDto;
    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Boolean updateTaskInfo(CustomerUpdateDto dto, Operator operator) {

        if (dto.getEffectTaskAmount() != null && dto.getEffectTaskAmount().compareTo(new BigDecimal(0)) >= 0) {
            CrmSaleKpiPo po = new CrmSaleKpiPo();
            po.setQuarterStr(dto.getTimeDesc());
            po.setBizType(SaleKpiTypeEnum.AGENT_CUSTOMER_EFFECT.getBizId());
            po.setBizId(dto.getCustomerId());
            po.setQuarterKpi(Utils.fromYuanToFen(dto.getEffectTaskAmount()));
            po.setOperator(operator.getOperatorName());
            po.setCtime(null);
            po.setMtime(new Timestamp(System.currentTimeMillis()));
            crmSaleKpiDao.insertUpdateSelective(po);
        }
        if (dto.getFireworkTaskAmount() != null && dto.getFireworkTaskAmount().compareTo(new BigDecimal(0)) >= 0) {
            CrmSaleKpiPo po = new CrmSaleKpiPo();
            po.setQuarterStr(dto.getTimeDesc());
            po.setBizType(SaleKpiTypeEnum.AGENT_CUSTOMER_FIREWORK.getBizId());
            po.setBizId(dto.getCustomerId());
            po.setQuarterKpi(Utils.fromYuanToFen(dto.getFireworkTaskAmount()));
            po.setOperator(operator.getOperatorName());
            po.setCtime(null);
            po.setMtime(new Timestamp(System.currentTimeMillis()));
            crmSaleKpiDao.insertUpdateSelective(po);
        }
        return Boolean.TRUE;
    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Boolean batchUpdateTaskInfo(List<CustomerUpdateDto> dtoList, Operator operator) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Boolean.TRUE;
        }
        for (CustomerUpdateDto dto : dtoList) {
            updateTaskInfo(dto, operator);
        }
        List<CustomerTaskDto> oldDto = queryTaskInfoByQuarter(CustomerTaskQueryDto.builder()
                .customerId(dtoList.get(0).getCustomerId())
                .timeDesc(dtoList.get(0).getTimeDesc())
                .build(), operator);
        logOperationRepo.insertLog(operator, NewLogOperatorDto.builder()
                .objId(dtoList.get(0).getCustomerId())
                .oldObj(oldDto)
                .obj(oldDto)
                .module(Module.CUSTOMER_ORDER)
                .systemType(SystemType.CRM)
                .modifyType(ModifyType.WORK_ORDER_CUSTOMER_UPDATE_TASK)
                .build());
        return Boolean.TRUE;
    }


    private List<String> generateMonthDescByQuarter(String quarter) {
        List<String> timeDescList = Lists.newArrayList();
        Timestamp time = incomeTimeUtil.getQuarterFirstDate(incomeTimeUtil.buildTimeByQuarterStr(quarter));
        timeDescList.add(quarter);
        timeDescList.add(quarter + "-" + buildMonth(time.getMonth() + 1));
        timeDescList.add(quarter + "-" + buildMonth(time.getMonth() + 2));
        timeDescList.add(quarter + "-" + buildMonth(time.getMonth() + 3));
        return timeDescList;
    }

    public String buildMonth(Integer month) {
        if (month < 10) {
            return "0" + month;
        } else {
            return String.valueOf(month);
        }
    }

    private List<CustomerTaskDto> buildRatioInfoWithQuarter(List<CustomerTaskDto> dtoList, CustomerTaskQueryDto queryDto) {
        List<CustomerTaskDto> result = new ArrayList<>();
        Timestamp time = incomeTimeUtil.getQuarterFirstDate(incomeTimeUtil.buildTimeByQuarterStr(queryDto.getTimeDesc()));
        List<AccountBaseDto> baseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().customerId(queryDto.getCustomerId()).build(), AccountFieldMapping.accountId);
        List<Integer> agentAccountIds = baseDtoList.stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList());
        Map<Integer, Integer> agentIdsMap = agentService.getAccountId2AgentIdMapInAccountIds(agentAccountIds);
        BigDecimal effectTotal = new BigDecimal(0);
        BigDecimal fireworkTotal = new BigDecimal(0);
        for (int i = 1; i < dtoList.size(); i++) {
            CustomerTaskDto dto = dtoList.get(i);
            Timestamp beginTime = incomeTimeUtil.getSomeMonthAfterBeginTime(i - 1, time);
            Timestamp endTime = IncomeTimeUtil.getEndTimeOfMonth(beginTime);
            dto.setEffectAmount(new BigDecimal(0));
            dto.setFireworkAmount(new BigDecimal(0));
            if (beginTime.before(new Timestamp(System.currentTimeMillis()))) {
                CompletableFuture<BigDecimal> effectFuture = CompletableFuture.supplyAsync(() -> queryEffectIncome(beginTime, endTime, Lists.newArrayList(agentIdsMap.values())));
                CompletableFuture<BigDecimal> fireWorkFuture = CompletableFuture.supplyAsync(() -> queryFireWorkIncome(beginTime, endTime, Lists.newArrayList(agentIdsMap.values()), false));
                dto.setEffectAmount(effectFuture.join());
                dto.setFireworkAmount(fireWorkFuture.join());
            }
            effectTotal = effectTotal.add(dto.getEffectAmount());
            fireworkTotal = fireworkTotal.add(dto.getFireworkAmount());
            dto.setEffectTaskRatio(MathUtils.genRate(dto.getEffectAmount(), dto.getEffectTaskAmount(), 4));
            dto.setFireworkTaskRatio(MathUtils.genRate(dto.getFireworkAmount(), dto.getFireworkTaskAmount(), 4));
            result.add(dto);
        }
        CustomerTaskDto quarterDto = dtoList.get(0);
        quarterDto.setEffectAmount(effectTotal);
        quarterDto.setFireworkAmount(fireworkTotal);
        quarterDto.setEffectTaskRatio(MathUtils.genRate(quarterDto.getEffectAmount(), quarterDto.getEffectTaskAmount(), 4));
        quarterDto.setFireworkTaskRatio(MathUtils.genRate(quarterDto.getFireworkAmount(), quarterDto.getFireworkTaskAmount(), 4));
        result.add(0, quarterDto);
        return result;
    }

    public BigDecimal queryEffectIncome(Timestamp startTime, Timestamp endTime, List<Integer> agentIds) {
        Map<Integer, String> agentNameMap = agentService.getAgentId2NameMapInIds(agentIds);
        agentIds = agentNameMap.entrySet().stream().filter(entry -> !entry.getValue().contains("米哈游")).map(entry -> entry.getKey()).collect(Collectors.toList());
        List<Integer> sellGoodAgentIds = agentNameMap.entrySet().stream().filter(entry -> entry.getValue().contains("带货")).map(entry -> entry.getKey()).collect(Collectors.toList());
        AdIncomeQueryParam queryParam = AdIncomeQueryParam.builder()
                .productTypes(EFFECT_TYPE)
                .isInner(IsInnerEnum.OUTER.getCode())
                .agentId(agentIds)
                .dateBegin(startTime)
                .dateEnd(endTime)
                .excludeAccountIds(DepAchieveConfigBeta.CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(DepAchieveConfigBeta.FINICAL_EXCLUDE_DEPIDS)
                .excludeSaleIds(depAchieveConfigBeta.buildExcludeSaleIds())
                .excludeAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .build();
        List<AchievementRtbData> rtbDataList = achieveFromWalletService.getWalletSumByWideES(queryParam);
        // 内容起飞
        List<AchievementRtbData> contentData = Lists.newArrayList();
        if (!CollectionUtils.isEmpty(sellGoodAgentIds)) {
            queryParam.setAgentId(sellGoodAgentIds);
            queryParam.setProductTypes(Lists.newArrayList(IncomeProductEnum.CONTENT_FLY.getCode()));
            contentData = achieveFromWalletService.getWalletSumByWideES(queryParam);
        }
        // 线索通
        queryParam.setAgentId(agentIds);
        queryParam.setSalesType(CLUE_PASS_SALE_TYPES);
        queryParam.setProductTypes(null);
        List<AchievementRtbData> clueData = achieveFromWalletService.getWalletSumByWideES(queryParam);
        return Stream.of(rtbDataList, contentData, clueData)
                .flatMap(Collection::stream)
                .map(AchievementRtbData::getTotalConsume)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    public BigDecimal queryFireWorkIncome(Timestamp startTime, Timestamp endTime, List<Integer> agentIds, boolean historyQuery) {
        // 其他商单 + 直播商单
        AchievementContractCheckQuotaDto dto = achievementContractService.getCheckedBillQuotaDto(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                .dateBegin(startTime)
                .dateEnd(endTime)
                .agentIds(agentIds)
                .build(), historyQuery);
        // 花火商单已执行
        QueryAchieveDto queryAchieveDto = QueryAchieveDto.builder()
                .dateBegin(startTime)
                .dateEnd(endTime)
                .agentIds(agentIds)
                .pickAchieveTypeEnum(PickupAchieveTypeEnum.CONFIRMED)
                .build();
        List<AchievementDto> pickUpBillDetailByWideEs = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(IncomeUtil.buildSuperAdmin(), queryAchieveDto, IncomeComposition.PICK_UP_CONFIRMED);
        BigDecimal totalConsume = pickUpBillDetailByWideEs.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        List<AchievementDto> boostingPickUpBillDetailByWideEs = achievementPickUpService.getPickUpBillDetailAggByAccByWideEs(IncomeUtil.buildSuperAdmin(), queryAchieveDto, IncomeComposition.BOOSTING_CONFIRMED);
        BigDecimal boostingTotalConsume = boostingPickUpBillDetailByWideEs.stream().map(AchievementDto::getTotalAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        return Utils.fromFenToYuan(dto.getOtherPickUpAmount().add(dto.getLivePickUpAmount())).add(totalConsume).add(boostingTotalConsume);
    }


}
