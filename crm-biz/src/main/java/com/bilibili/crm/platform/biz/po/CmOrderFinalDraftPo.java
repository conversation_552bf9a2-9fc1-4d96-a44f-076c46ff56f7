package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CmOrderFinalDraftPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 订单id
     */
    private Integer orderId;

    /**
     * 标题
     */
    private String title;

    /**
     * 终稿avId
     */
    private Long avId;

    /**
     * 投稿时间
     */
    private Timestamp uploadTime;

    /**
     * 6-已通过 7-待主站内容确认 8-主站内容拒绝 9-待业务终审确认 10-业务终审拒绝
     */
    private Integer draftStatus;

    /**
     * 软删除，0是有效,1是删除
     */
    private Integer isDeleted;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 稿件实际上线时间
     */
    private Timestamp actualOnlineTime;

    /**
     * 客户确认终稿状态（0-无 1-待客户确认 2-客户已确认 3-客户驳回 4-客户撤销驳回 5-系统自动确认）
     */
    private Integer customerConfirmFinalDraftStatus;

    /**
     * 客户驳回原因
     */
    private String customerRejectReason;

    /**
     * 操作状态占位（利用二进制占位）
     */
    private Integer operateStatus;

    /**
     * 客户驳回稿件时间
     */
    private Timestamp customerRejectTime;

    private static final long serialVersionUID = 1L;
}