package com.bilibili.crm.platform.biz.service;


import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.sales.bo.CrmSaleBO;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.biz.sales.repo.SaleGroupMappingRepo;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.constants.SaleGroupRoleType;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.UnitedIndustryDto;
import com.bilibili.crm.platform.api.account.service.IBizIndustryService;
import com.bilibili.crm.platform.api.enums.CategoryLevelEnum;
import com.bilibili.crm.platform.api.finance.enums.SaleTypeEnum;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.oa.dto.OaCoreUserInfo;
import com.bilibili.crm.platform.api.oa.service.IOaUserInfoService;
import com.bilibili.crm.platform.api.rbac.IDomainUserService;
import com.bilibili.crm.platform.api.rbac.common.DataStrategyScope;
import com.bilibili.crm.platform.api.rbac.dto.DataStrategy;
import com.bilibili.crm.platform.api.sale.dto.*;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupSimpleItem;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale.type.dto.SaleTypeDto;
import com.bilibili.crm.platform.api.sale.type.service.ISaleTypeService;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.dao.CrmSaleDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleIndustryMappingDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleKpiDao;
import com.bilibili.crm.platform.biz.hive.api.InvestmentHiveService;
import com.bilibili.crm.platform.biz.industry.service.UnitedIndustryService;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.service.contract.component.ContractBiiPeriodAlertComponent;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.common.sale.SaleKpiNoticeEnum;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2017年5月18日
 */
@Slf4j
@Service
public class SaleService implements ISaleService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CrmSaleDao crmSaleDao;
    @Autowired
    private CrmSaleIndustryMappingDao crmSaleIndustryMappingDao;
    @Autowired
    private IBizIndustryService iBizIndustryService;
    @Autowired
    private ISaleTypeService saleTypeService;
    @Autowired
    private SaleGroupService saleGroupService;
    @Autowired
    private ILogOperatorService logOperatorService;
    @Autowired
    private IDomainUserService domainUserService;
    @Autowired
    private IOaUserInfoService oaUserInfoService;
    @Autowired
    private CrmSaleKpiDao crmSaleKpiDao;
    @Resource
    private CrmCacheManager cacheManager;
    @Value("${crm.portal.env:prd}")
    private String env;
    @Resource
    private UnitedIndustryService unitedIndustryService;

    @Resource
    private InvestmentHiveService investmentHiveService;

    @Resource
    private ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent;

    @Value("${crm.wechat.estimate.url:http://cm.bilibili.com/ldad/daily-paper-new/main.html?mock_login=no&code=&state=#/}")
    private String wechatUrl;

    private static final String saleEfficiencyUrl = "https://cm-mng.bilibili.co/crm#/employee-productivity/sales";
    public static final String SALE_REDIS_KEY = "SALE_DTO_MAP_DETAIL_REDIS_KEY";
    @Autowired
    private SaleGroupMappingRepo saleGroupMappingRepo;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Override
    public List<Integer> getSaleIdByQueryDto(QuerySaleDto querySaleDto) {
        CrmSalePoExample example = createExampleFromQueryDto(querySaleDto);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        return salePos.stream().map(CrmSalePo::getId).collect(Collectors.toList());
    }

    @Override
    public List<String> getEmailByQueryDto(QuerySaleDto querySaleDto) {
        CrmSalePoExample example = createExampleFromQueryDto(querySaleDto);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(CrmSalePo::getEmail).distinct().collect(Collectors.toList());
    }

    @Override
    public List<SaleDto> getListByQueryDto(QuerySaleDto querySaleDto) {
        LOGGER.info("SaleService.getListByQueryDto param : {}", querySaleDto);
        CrmSalePoExample example = createExampleFromQueryDto(querySaleDto);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return buildSaleDtos(salePos);
    }

    @Override
    public Map<Integer, String> getIdAndName(QuerySaleDto querySaleDto) {
        LOGGER.info("SaleService.getIdAndName param : {}", querySaleDto);
        CrmSalePoExample example = createExampleFromQueryDto(querySaleDto);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        return salePos.stream().collect(Collectors.toMap(CrmSalePo::getId, CrmSalePo::getName));
    }

    private CrmSalePoExample createExampleFromQueryDto(QuerySaleDto queryDto) {
        CrmSalePoExample example = new CrmSalePoExample();
        CrmSalePoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (!CollectionUtils.isEmpty(queryDto.getSaleTypes())) {
            criteria.andTypeIn(queryDto.getSaleTypes());
        }
        if (Utils.isPositive(queryDto.getStatus())) {
            criteria.andStatusEqualTo(queryDto.getStatus());
        }
        if (queryDto.getSaleIsQuit() != null) {
            criteria.andSaleIsQuitEqualTo(queryDto.getSaleIsQuit());
        }
        if (StringUtils.isNotEmpty(queryDto.getSaleNameLike())) {
            criteria.andNameLike("%" + queryDto.getSaleNameLike() + "%");
        }
        if (!Utils.isEmpty(queryDto.getSaleIds())) {
            criteria.andIdIn(queryDto.getSaleIds());
        }
        ObjectUtils.setString(queryDto::getName, criteria::andNameEqualTo);
        ObjectUtils.setString(queryDto::getEmail, criteria::andEmailEqualTo);
        ObjectUtils.setList(queryDto::getEmails, criteria::andEmailIn);
        ObjectUtils.setObject(queryDto::getSaleId, criteria::andIdEqualTo);
        ObjectUtils.setObject(queryDto::getSaleQuitTimeBegin, criteria::andSaleQuitTimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getSaleQuitTimeEnd, criteria::andSaleQuitTimeLessThanOrEqualTo);

        //oa昵称
        ObjectUtils.setObject(queryDto::getNickName, criteria::andNickNameEqualTo);

        if (queryDto.isOrderByMtime()) {
            example.setOrderByClause("mtime desc");
        }

        return example;
    }

    @Override
    public List<SaleDto> getByName(String name) {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andNameEqualTo(name);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        List<SaleDto> saleDtos = new ArrayList<>();
        for (CrmSalePo po : salePos) {
            saleDtos.add(SaleDto.builder()
                    .id(po.getId())
                    .name(po.getName())
                    .build());
        }
        return saleDtos;
    }

    @Override
    public List<Integer> getSaleIdListInEmails(List<String> saleEmails) {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andEmailIn(saleEmails);
        List<CrmSalePo> sales = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(sales)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        return sales.stream().map(CrmSalePo::getId).collect(Collectors.toList());
    }

    @Override
    public List<SaleDto> getSalesInEmails(List<String> saleEmails) {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andEmailIn(saleEmails);
        List<CrmSalePo> sales = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(sales)) {
            return Lists.newArrayList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        return sales.stream().map(po -> SaleDto.builder().id(po.getId())
                .name(po.getName()).email(po.getEmail())
                .groupId(po.getGroupId()).type(po.getType())
                .build()).collect(Collectors.toList());
    }

    @Override
    public List<SaleDto> getByLikeName(String likeName) {

        CrmSalePoExample example = new CrmSalePoExample();

        CrmSalePoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode());

        if (!Strings.isNullOrEmpty(likeName)) {
            criteria.andNameLike("%" + likeName + "%");
        }
        example.setOrderByClause("mtime desc");
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        List<SaleDto> saleDtos = new ArrayList<>();
        for (CrmSalePo po : salePos) {
            saleDtos.add(SaleDto.builder()
                    .id(po.getId())
                    .name(po.getName())
                    .build());
        }
        return saleDtos;
    }

    @Override
    public Map<Integer, SaleDto> getSaleMapInIds(List<Integer> saleIds) {
        List<SaleDto> saleDtos = this.getSalesInIds(saleIds);
        return saleDtos.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));
    }

    @Override
    public Map<Integer, SaleBaseDto> getSaleBaseDtoMapInIds(List<Integer> saleIds) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return new HashMap<>();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.createCriteria().andIdIn(saleIds);
        List<CrmSalePo> poList = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return new HashMap<>();
        }
        return poList.stream().map(this::po2SaleBaseDto).collect(Collectors.toMap(SaleBaseDto::getId, t -> t));
    }

    @Override
    public List<SaleBaseDto> getTopNBelowLeaderSalesBySaleId(Integer saleId, Integer level) {
        SaleBaseDto saleBaseDto = this.getSaleBaseDto(saleId);
        if (saleBaseDto == null || saleBaseDto.getId() == null) {
            return Lists.newArrayList();
        }
        List<Integer> bindGroupIds = saleGroupMappingServiceImpl.queryGroupIdBySaleId(saleId, null);

        if (CollectionUtils.isEmpty(bindGroupIds)) {
            return Lists.newArrayList();
        }
        List<SaleBaseDto> leaderSales = Lists.newArrayList();
        List<Integer> groupIds = new ArrayList<>();

        for (Integer bindGroupId : bindGroupIds) {
            List<SaleGroupDto> parentGroupInfoList = saleGroupService.getAllParentGroupInfoListByGroupId(bindGroupId);
            for (int i = 0; i < parentGroupInfoList.size(); i++) {
                if (parentGroupInfoList.size() >= level && (i + 1) < level) {
                    continue;
                }
                SaleGroupDto saleGroupDto = parentGroupInfoList.get(i);
                groupIds.add(saleGroupDto.getId());
            }
            List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(groupIds);

            leaderIds.forEach(leaderId -> {
                SaleBaseDto leaderSaleDto = this.getSaleBaseDto(leaderId);
                leaderSales.add(leaderSaleDto);
            });
        }
        return leaderSales;
    }

    @Override
    public Map<Integer, SaleDto> getAllSaleMap() {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyMap();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        Map<Integer, SaleDto> saleDtoMap = new HashMap<>();
        for (CrmSalePo po : salePos) {
            saleDtoMap.put(po.getId(), SaleDto.builder().id(po.getId())
                    .name(po.getName()).email(po.getEmail())
                    .groupId(po.getGroupId()).type(po.getType())
                    .level(po.getLevel())
                    .status(po.getStatus())
                    .build());
        }
        return saleDtoMap;
    }

    @Override
    public Map<Integer, SaleDto> getAllSaleMapWithDetail() {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyMap();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return getSaleMapInIds(salePos.stream().map(CrmSalePo::getId).collect(Collectors.toList()));
    }

    @Override
    public Map<Integer, SaleDto> getAllSaleMapWithCache() {
        Map<Integer, SaleDto> result = (Map<Integer, SaleDto>) cacheManager.getValue(SALE_REDIS_KEY);
        if (result != null) {
            return result;
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyMap();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        List<SaleDto> saleDtoList = buildSaleDtos(salePos);
        result = saleDtoList.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));
        cacheManager.setValueWithTime(SALE_REDIS_KEY, result, TimeUnit.MINUTES, 10);
        return result;
    }

    @Override
    public List<Integer> getAllSaleId() {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(CrmSalePo::getId).collect(Collectors.toList());
    }

    @Override
    public List<SaleDto> getSalesInIds(List<Integer> saleIds) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return new ArrayList<>();
        }
        saleIds = saleIds.stream().distinct().collect(Collectors.toList());
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(saleIds);

        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return this.buildSaleDtos(salePos);
    }


    @Override
    public Map<Integer, String> getSalesEmailInIds(List<Integer> saleIds) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return Collections.emptyMap();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(saleIds);

        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().collect(Collectors.toMap(CrmSalePo::getId, CrmSalePo::getEmail));
    }

    @Override
    public SaleDto load(Integer saleId) {
        Assert.notNull(saleId, "销售ID不可为空");
        CrmSalePo salePo = crmSaleDao.selectByPrimaryKey(saleId);
        Assert.notNull(salePo, "该销售不存在");
        saleGroupMappingServiceImpl.fillSalePoLevel(salePo);
        SaleDto saleDto = SaleDto.builder().build();
        BeanUtils.copyProperties(salePo, saleDto);
        CrmSaleBO saleAuth = saleGroupMappingServiceImpl.querySale(saleId);
        List<SimpleSaleDto> leaders = saleGroupMappingServiceImpl
                .queryLeaders(new ArrayList<>(saleAuth.getRoleMap().keySet()))
                .values()
                .stream().flatMap(List::stream).collect(Collectors.toList());
        saleAuth = saleAuth == null ? CrmSaleBO.EMPTY : saleAuth;
        Map<Integer, SaleGroupDto> groupDtos = saleGroupService.getSaleGroupMapInIds(Lists.newArrayList(saleAuth.getRoleMap().keySet()));
        SaleGroupDto groupDto = groupDtos.getOrDefault(saleDto.getGroupId(), SaleGroupDto.builder().saleTypeName("未知").name("未分配")
                .saleDirectorId(0).saleDirectorName("未分配").build());
        saleDto.setTypeName(groupDto.getSaleTypeName());
        saleDto.setGroupName(groupDto.getName());
        saleDto.setLeaders(leaders);
        saleDto.setLeaderNames(leaders.stream().map(SimpleSaleDto::getName).collect(Collectors.toList()));
        saleDto.setLeaderIds(leaders.stream().map(SimpleSaleDto::getId).collect(Collectors.toList()));
        saleDto.setLeaderEmails(leaders.stream().map(SimpleSaleDto::getEmail).collect(Collectors.toList()));
        saleDto.setSaleDirectorId(groupDto.getSaleDirectorId());
        saleDto.setSaleDirectorName(groupDto.getSaleDirectorName());
        saleDto.setDefaultClaimCustomerLimit(salePo.getDefaultClaimNum());
        return saleDto;
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public Integer create(Operator operator, NewSaleDto newSaleDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不可为空");
        Assert.notNull(newSaleDto, "销售信息不可为空");
        Assert.hasText(newSaleDto.getName(), "销售名称不可为空");
        Assert.hasText(newSaleDto.getEmail(), "销售邮箱前缀不可为空");
        Assert.notNull(IsAble.getByCode(newSaleDto.getStatus()), "销售状态有误");
        Assert.isTrue(!validateEmailIsExist(newSaleDto.getEmail()), "该邮箱已存在");
        LOGGER.info("SaleService.create operator {}, newSaleDto {}", operator, newSaleDto);

        CrmSalePo salePo = new CrmSalePo();
        BeanUtils.copyProperties(newSaleDto, salePo);

        //域账号oa昵称
        String nickName = oaUserInfoService.getNickNameByDomain(newSaleDto.getEmail());
        salePo.setNickName(nickName);


        try {
            salePo.setGroupId(null);
            salePo.setLevel(null);
            crmSaleDao.insertSelective(salePo);
        } catch (DuplicateKeyException e) {
            LOGGER.error("SaleService.create operator {}, newSaleDto {}, Exception", operator, newSaleDto, e);
            throw new IllegalArgumentException("该邮箱已存在");
        }
        Integer groupId = newSaleDto.getGroupId();
        boolean bindGroup = groupId != null && groupId > 0;
        if (bindGroup) {
            saleGroupMappingRepo.addMapping(groupId, salePo.getId(), SaleGroupRoleType.MEMBER.getVal());
        }

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.CREATE_SALE)
                .module(Module.SALE)
                .obj(newSaleDto)
                .objId(salePo.getId())
                .systemType(SystemType.CRM)
                .build();

        logOperatorService.insertLog(operator, logDto);
        return salePo.getId();
    }

    private boolean validateEmailIsExist(String email) {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andEmailEqualTo(email);
        return crmSaleDao.countByExample(example) > 0;
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void update(Operator operator, UpdateSaleDto updateSaleDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "操作人信息不可为空");
        Assert.notNull(updateSaleDto, "销售信息不可为空");
        Assert.hasText(updateSaleDto.getName(), "销售名称不可为空");
        Assert.hasText(updateSaleDto.getEmail(), "销售邮箱前缀不可为空");
        Assert.notNull(IsAble.getByCode(updateSaleDto.getStatus()), "销售状态有误");
        LOGGER.info("SaleService.update operator {}, updateSaleDto {}", operator, updateSaleDto);
        if (Objects.equals(updateSaleDto.getEmail(), "wanggang03")) {
            Assert.isTrue(Objects.equals(operator.getOperatorName(), "wanggang03"), "阿钢销售不支持更新");
        }
        CrmSalePo exist = crmSaleDao.selectByPrimaryKey(updateSaleDto.getId());
        saleGroupMappingServiceImpl.fillSalePoLevel(exist);
        Assert.notNull(exist, "该销售不存在");
        if (!updateSaleDto.getEmail().equals(exist.getEmail())) {
            Assert.isTrue(!validateEmailIsExist(updateSaleDto.getEmail()), "该邮箱已存在");
        }

        CrmSalePo updateRecord = new CrmSalePo();
        BeanUtils.copyProperties(updateSaleDto, updateRecord);

        //域账号oa昵称
        String nickName = oaUserInfoService.getNickNameByDomain(updateRecord.getEmail());
        updateRecord.setNickName(nickName);

        crmSaleDao.updateByPrimaryKeySelective(updateRecord);

        saleGroupMappingRepo.resetSaleMapping(updateSaleDto.getId(), updateSaleDto.getGroupIds());

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.UPDATE_SALE)
                .module(Module.SALE)
                .obj(updateSaleDto)
                .objId(updateSaleDto.getId())
                .systemType(SystemType.CRM)
                .build();

        logOperatorService.insertLog(operator, logDto);
    }

    //销售状态更新
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateStatus(Operator operator, UpdateSaleDto updateSaleDto) {
        CrmSalePo crmSalePo = crmSaleDao.selectByPrimaryKey(updateSaleDto.getId());
        if (Objects.equals(crmSalePo.getEmail(), "wanggang03")) {
            Assert.isTrue(Objects.equals(operator.getOperatorName(), "wanggang03"), "阿钢销售不支持启用");
        }

        if (Objects.equals(crmSalePo.getStatus(), updateSaleDto.getStatus())) {
            AlarmHelper.log("StatusSame", crmSalePo);
            return;
        }

        CrmSalePo updateRecord = new CrmSalePo();
        updateRecord.setId(crmSalePo.getId());
        updateRecord.setStatus(updateSaleDto.getStatus());
        AlarmHelper.log("updateStatus", updateSaleDto);
        crmSaleDao.updateByPrimaryKeySelective(updateRecord);

        if (Objects.equals(updateSaleDto.getStatus(), IsAble.BANNED.getCode())) {
            //禁用销售，将销售角色改为普通成员
            saleGroupMappingRepo.updateMappingToMemberBySaleId(updateRecord.getId());
        }
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateSaleIsQuit(Operator operator, UpdateSaleDto updateSaleDto) {
        CrmSalePo salePo = new CrmSalePo();
        BeanUtils.copyProperties(updateSaleDto, salePo);
        crmSaleDao.updateByPrimaryKeySelective(salePo);
        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.BATCH_UPDATE_SALE_QUIT)
                .module(Module.SALE)
                .obj(updateSaleDto)
                .objId(updateSaleDto.getId())
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logDto);
    }

    @Override
    public void validateSaleIds(List<Integer> saleIds) {
        Set<Integer> saleIdSet = new HashSet<>(saleIds);
        Assert.isTrue(this.getSalesInIds(new ArrayList<>(saleIdSet)).size() == saleIdSet.size(), "销售id不合法");
    }

    @Override
    public PageResult<SaleDto> getSales(Integer status, Integer saleIsQuit, String likeName, String saleEmail, String nickName, Integer currentPage, Integer size) {

        Assert.notNull(currentPage, "页码不可为空");
        Assert.notNull(size, "页长不可为空");
        LOGGER.info("SaleService.getSales currentPage {}, size {}", currentPage, size);

        Page pageBean = Page.valueOf(currentPage, size);

        CrmSalePoExample example = this.buildQuerySaleExmaple(status, saleIsQuit, likeName, saleEmail, nickName);
        long total = crmSaleDao.countByExample(example);

        example.setOffset(pageBean.getOffset());
        example.setLimit(pageBean.getLimit());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return PageResult.<SaleDto>builder().records(this.buildSaleDtos(salePos)).total((int) total).build();
    }

    private CrmSalePoExample buildQuerySaleExmaple(Integer status, Integer saleIsQuit, String likeName, String saleEmail, String nickName) {
        CrmSalePoExample example = new CrmSalePoExample();
        CrmSalePoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (status != null && status > 0) {
            IsAble.getByCode(status);
            criteria.andStatusEqualTo(status);
        }
        if (saleIsQuit != null) {
            criteria.andSaleIsQuitEqualTo(saleIsQuit);
        }
        if (!Strings.isNullOrEmpty(likeName)) {
            criteria.andNameLike("%" + likeName + "%");
        }
        if (StringUtils.isNotBlank(saleEmail)) {
            criteria.andEmailEqualTo(saleEmail);
        }
        if (StringUtils.isNotBlank(nickName)) {
            criteria.andNickNameLike("%" + nickName + "%");
        }

        example.setOrderByClause("mtime desc");
        return example;
    }

    public List<SaleDto> buildSaleDtos(List<CrmSalePo> salePos) {
        if (CollectionUtils.isEmpty(salePos)) {
            return new ArrayList<>();
        }
        Map<Integer, SaleGroupSimpleItem> saleGroupMap = saleGroupService.getGroupIdParentGroupsMap();
        Map<Integer, SaleTypeDto> saleTypeDtoMap = saleTypeService.getSaleTypeMap();
        List<Integer> saleIds = salePos.stream().map(CrmSalePo::getId).collect(Collectors.toList());
        Map<Integer, CrmSaleBO> saleAuthMap = saleGroupMappingServiceImpl.querySales(saleIds);
        Map<Integer, List<SimpleSaleDto>> leaderMap = saleGroupMappingServiceImpl
                .queryLeadersBySaleIds(saleIds);
        AlarmHelper.log("BuildDto");
        return salePos.stream().map(po -> salePo2Dto(po, saleGroupMap, saleTypeDtoMap, saleAuthMap, leaderMap)).collect(Collectors.toList());
    }

    private SaleDto salePo2Dto(CrmSalePo po,
                               Map<Integer, SaleGroupSimpleItem> saleGroupMap,
                               Map<Integer, SaleTypeDto> saleTypeDtoMap,
                               Map<Integer, CrmSaleBO> saleAuthMap,
                               Map<Integer, List<SimpleSaleDto>> leaderMap) {
        CrmSaleBO saleAuth = saleAuthMap.getOrDefault(po.getId(), CrmSaleBO.EMPTY);
        // 如果为销售组长，则查询该销售负责的所有销售组；否则只保留当前销售所在组
        List<Integer> groupIds = new ArrayList<>(saleAuth.getRoleMap().keySet());
        List<List<String>> parentGroupNames = new ArrayList<>();
        for (Integer groupId : groupIds) {
            List<SaleGroupSimpleItem> orgTrees = saleGroupMap.getOrDefault(groupId, SaleGroupSimpleItem.UNKNOWN).getParents();
            for (int i = 0; i < orgTrees.size(); i++) {
                SaleGroupSimpleItem orgTree = orgTrees.get(i);
                if (parentGroupNames.size() <= i) {
                    List<String> parentNames = new ArrayList<>();
                    parentNames.add(orgTree.getName());
                    parentGroupNames.add(parentNames);
                } else {
                    parentGroupNames.get(i).add(orgTree.getName());
                }
            }
        }

        SaleDto saleDto = SaleDto.builder().build();
        BeanUtils.copyProperties(po, saleDto);
        SaleTypeDto saleTypeDto = saleTypeDtoMap.get(po.getType());
        if (null != saleTypeDto) {
            saleDto.setTypeName(saleTypeDto.getName());
        }

        SaleGroupSimpleItem groupDto;
        if (saleGroupMap == null || saleGroupMap.isEmpty()) {
            groupDto = SaleGroupSimpleItem.UNKNOWN;
        } else {
            groupDto = saleGroupMap.getOrDefault(saleAuth.getDefaultGroupId(), SaleGroupSimpleItem.UNKNOWN);
        }
        List<SimpleSaleDto> leaders = leaderMap
                .getOrDefault(saleDto.getId(), new ArrayList<>());
        try {
            saleDto.setMaxClaimCustomerLimit(po.getDefaultClaimNum());
            if (StringUtils.isNotBlank(po.getClaimNumConfigDay())) {
                ClaimNumConfigDto claimNumConfigDto = JSON.parseObject(po.getClaimNumConfigDay(), ClaimNumConfigDto.class);
                Long curTime = System.currentTimeMillis();
                if (Objects.nonNull(claimNumConfigDto) && claimNumConfigDto.getStartTime() < curTime && claimNumConfigDto.getEndTime() > curTime) {
                    saleDto.setMaxClaimCustomerLimit(claimNumConfigDto.getNumOfDay());
                }
            }
        } catch (Exception e) {
            log.warn("parse ClaimNumConfigDto error ClaimNumConfigDay = {}", po.getClaimNumConfigDay());
        }
        saleDto.setCtime(po.getCtime());
        saleDto.setLeaders(leaders);
        saleDto.setLeaderNames(leaders.stream().map(SimpleSaleDto::getName).collect(Collectors.toList()));
        saleDto.setLeaderIds(leaders.stream().map(SimpleSaleDto::getId).collect(Collectors.toList()));
        saleDto.setLeaderEmails(leaders.stream().map(SimpleSaleDto::getEmail).collect(Collectors.toList()));
        saleDto.setLevel(saleAuth.getLevel());
        saleDto.setGroupId(saleAuth.getDefaultGroupId());
        saleDto.setGroupName(groupDto.getName());
        saleDto.setGroupIds(groupIds);
        saleDto.setGroupNames(groupIds.stream()
                .map(groupId -> saleGroupMap.getOrDefault(groupId, SaleGroupSimpleItem.UNKNOWN).getName())
                .collect(Collectors.toList()));
        // 销售所在组的上级销售组的路径 比如：["渠道组", "效果综合组"]
        // 销售为销售组长，且负责多个销售组，此时的上级销售组的路径格式为 ["渠道组,直客", "效果综合组,网教金组"]
        saleDto.setParentGroups(parentGroupNames.stream().map(names -> String.join(",", names)).collect(Collectors.toList()));
        saleDto.setPIds(groupDto.getParents().stream().map(SaleGroupSimpleItem::getId).collect(Collectors.toList()));
        saleDto.setDefaultClaimCustomerLimit(po.getDefaultClaimNum());

        return saleDto;
    }

    @Override
    public void createOrUpdateSaleCategory(Operator operator, SaleCategoryConfigDto dto) {
        // create or update  redis 锁  一级行业查询
        Assert.isTrue(null != dto.getSaleId(), "销售id不能为空");

        CrmSaleIndustryMappingPoExample example = new CrmSaleIndustryMappingPoExample();
        CrmSaleIndustryMappingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andSaleIdEqualTo(dto.getSaleId());
        //添加的有二级行业 对一级行业校验 并写入
        if (null != dto.getSecondCategory()) {
            CategoryDto secondaryCategoryDto = iBizIndustryService.getCategoryDtoById(null == dto.getSecondCategory() ? 0 : dto.getSecondCategory());
            criteria.andCommerceCategoryIdEqualTo(secondaryCategoryDto.getPId());
            List<CrmSaleIndustryMappingPo> secondaryPidCrmSaleIndustryMappingPos = crmSaleIndustryMappingDao.selectByExample(example);
            Assert.isTrue(CollectionUtils.isEmpty(secondaryPidCrmSaleIndustryMappingPos), "存在相同的二级行业 " + secondaryCategoryDto.getName());

            crmSaleIndustryMappingDao.insertUpdateSelective(CrmSaleIndustryMappingPo.builder()
                    .saleId(dto.getSaleId())
                    .commerceCategoryId(dto.getSecondCategory())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
            return;
        }
        if (null != dto.getFirstCategory()) {
            //老的一级校验
            CategoryDto firstCategoryDto = iBizIndustryService.getCategoryDtoById(dto.getFirstCategory());
            criteria.andCommerceCategoryIdEqualTo(dto.getFirstCategory());
            List<CrmSaleIndustryMappingPo> firstCrmSaleIndustryMappingPos = crmSaleIndustryMappingDao.selectByExample(example);
            Assert.isTrue(CollectionUtils.isEmpty(firstCrmSaleIndustryMappingPos), "存在相同的一级行业 " + firstCategoryDto.getName());

            List<CategoryDto> dtosByPid = iBizIndustryService.getValidCategoryDtosByPid(dto.getFirstCategory());
            List<Integer> subCategoryIds = dtosByPid.stream().map(CategoryDto::getId).collect(Collectors.toList());

            CrmSaleIndustryMappingPoExample updateExample = new CrmSaleIndustryMappingPoExample();
            CrmSaleIndustryMappingPoExample.Criteria updateCriteria = updateExample.or();
            updateCriteria.andSaleIdEqualTo(dto.getSaleId());
            updateCriteria.andCommerceCategoryIdIn(subCategoryIds);

            //写入一级行业映射前 删除该一级行业下的二级行业
            crmSaleIndustryMappingDao.updateByExampleSelective(CrmSaleIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build(), updateExample);

            //写入一级行业映射前 删除该二级行业下的一级行业
            crmSaleIndustryMappingDao.insertUpdateSelective(CrmSaleIndustryMappingPo.builder()
                    .saleId(dto.getSaleId())
                    .commerceCategoryId(dto.getFirstCategory())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
        }
        //处理新-三级行业
        if (dto.getUnitedFirstIndustryId() != null || dto.getUnitedSecondIndustryId() != null || dto.getUnitedThirdIndustryId() != null) {
            this.createOrUpdateSaleUnitedIndustry(operator, dto);
        }
    }

    /*
    配置的时候，我们可以选一二三级，如果配的是二级，代表有二级下所有三级的权限；配的一级代表有一级下所有三级的权限，最细到三级
     */
    public void createOrUpdateSaleUnitedIndustry(Operator operator, SaleCategoryConfigDto dto) {
        //create or update  redis 锁  一级行业查询
        //传到了三级行业
        if (null != dto.getUnitedThirdIndustryId()) {
            crmSaleIndustryMappingDao.insertUpdateSelective(CrmSaleIndustryMappingPo.builder()
                    .saleId(dto.getSaleId())
                    .unitedIndustryId(dto.getUnitedThirdIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
            return;
        }
        //传到了二级行业
        if (null != dto.getUnitedSecondIndustryId()) {
            //把二级行业下的三级行业全部清空
            //写入二级行业映射前 删除该二级行业下的三级行业
            List<UnitedIndustryDto> unitedSecondIndustryDtoByLevel = unitedIndustryService.queryAllIndustryDtoByLevel(CategoryLevelEnum.THIRD.getLevel());
            List<Integer> subCategoryIds = unitedSecondIndustryDtoByLevel.stream().filter(v -> v.getPId().equals(dto.getUnitedSecondIndustryId())).map(UnitedIndustryDto::getId).collect(Collectors.toList());


            CrmSaleIndustryMappingPoExample updateExample = new CrmSaleIndustryMappingPoExample();
            CrmSaleIndustryMappingPoExample.Criteria updateCriteria = updateExample.or();
            updateCriteria.andSaleIdEqualTo(dto.getSaleId());
            updateCriteria.andUnitedIndustryIdIn(subCategoryIds);
            crmSaleIndustryMappingDao.updateByExampleSelective(CrmSaleIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), updateExample);

            crmSaleIndustryMappingDao.insertUpdateSelective(CrmSaleIndustryMappingPo.builder()
                    .saleId(dto.getSaleId())
                    .unitedIndustryId(dto.getUnitedSecondIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
            return;
        }
        //传到了一级行业
        //写入一级行业映射前 删除该一级行业下的二级和三级行业

        if (null != dto.getUnitedFirstIndustryId()) {
            List<UnitedIndustryDto> unitedSecondIndustryDtoByLevel = unitedIndustryService.queryAllIndustryDtoByLevel(CategoryLevelEnum.SECOND.getLevel());

            //删除该一级行业下的二级
            List<Integer> subSecondCategoryIds = unitedSecondIndustryDtoByLevel.stream().filter(v -> v.getPId().equals(dto.getUnitedFirstIndustryId())).map(UnitedIndustryDto::getId).collect(Collectors.toList());

            CrmSaleIndustryMappingPoExample updateExample = new CrmSaleIndustryMappingPoExample();
            CrmSaleIndustryMappingPoExample.Criteria updateCriteria = updateExample.or();
            updateCriteria.andSaleIdEqualTo(dto.getSaleId());
            updateCriteria.andUnitedIndustryIdIn(subSecondCategoryIds);
            crmSaleIndustryMappingDao.updateByExampleSelective(CrmSaleIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), updateExample);

            //删除该一级行业下的三级
            List<UnitedIndustryDto> unitedThirdIndustryDtoByLevel = unitedIndustryService.queryAllIndustryDtoByLevel(CategoryLevelEnum.THIRD.getLevel());
            List<Integer> subThirdCategoryIds = unitedThirdIndustryDtoByLevel.stream().filter(v -> subSecondCategoryIds.contains(v.getPId())).map(UnitedIndustryDto::getId).collect(Collectors.toList());

            CrmSaleIndustryMappingPoExample updateExampleThird = new CrmSaleIndustryMappingPoExample();
            CrmSaleIndustryMappingPoExample.Criteria updateCriteriaThird = updateExample.or();
            updateCriteria.andSaleIdEqualTo(dto.getSaleId());
            updateCriteria.andUnitedIndustryIdIn(subThirdCategoryIds);
            crmSaleIndustryMappingDao.updateByExampleSelective(CrmSaleIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), updateExampleThird);

            crmSaleIndustryMappingDao.insertUpdateSelective(CrmSaleIndustryMappingPo.builder()
                    .saleId(dto.getSaleId())
                    .unitedIndustryId(dto.getUnitedFirstIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
        }
    }

    @Override
    public void deleteSaleCategory(Operator operator, SaleCategoryConfigDto saleCategoryConfigDto) {

        CrmSaleIndustryMappingPoExample example = new CrmSaleIndustryMappingPoExample();
        CrmSaleIndustryMappingPoExample.Criteria criteria = example.or();
        criteria.andSaleIdEqualTo(saleCategoryConfigDto.getSaleId());

        Integer categoryId = saleCategoryConfigDto.getFirstCategory();
        if (null != saleCategoryConfigDto.getSecondCategory()) {
            categoryId = saleCategoryConfigDto.getSecondCategory();
        }
        if (null != categoryId) {
            criteria.andCommerceCategoryIdEqualTo(categoryId);
        }
        //新行业
        Integer industryId = null;
        if (null != saleCategoryConfigDto.getUnitedFirstIndustryId()) {
            industryId = saleCategoryConfigDto.getUnitedFirstIndustryId();
        }
        if (null != saleCategoryConfigDto.getUnitedSecondIndustryId()) {
            industryId = saleCategoryConfigDto.getUnitedSecondIndustryId();
        }
        if (null != saleCategoryConfigDto.getUnitedThirdIndustryId()) {
            industryId = saleCategoryConfigDto.getUnitedThirdIndustryId();
        }
        if (null != industryId) {
            criteria.andUnitedIndustryIdEqualTo(industryId);
        }
        crmSaleIndustryMappingDao.updateByExampleSelective(CrmSaleIndustryMappingPo.builder()
                .mtime(new Timestamp(System.currentTimeMillis()))
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    @Override
    public List<SaleCategoryMappingDto> querySaleCategoryMapping(SaleCategoryMappingQueryDto queryDto) {
        CrmSaleIndustryMappingPoExample example = new CrmSaleIndustryMappingPoExample();
        CrmSaleIndustryMappingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.setList(queryDto::getCategoryIds, criteria::andCommerceCategoryIdIn);
        ObjectUtils.setList(queryDto::getUnitedIndustryId, criteria::andUnitedIndustryIdIn);
        ObjectUtils.setList(queryDto::getSaleIds, criteria::andSaleIdIn);

        List<CrmSaleIndustryMappingPo> mappingPos = crmSaleIndustryMappingDao.selectByExample(example);
        return mappingPos.stream().map(item -> {
            SaleCategoryMappingDto saleCategoryMappingDto = new SaleCategoryMappingDto();
            BeanUtils.copyProperties(item, saleCategoryMappingDto);
            return saleCategoryMappingDto;
        }).collect(Collectors.toList());
    }

    // 已修改
    @Override
    public List<SaleDto> getSalesByGroupId(Integer groupId) {
        Assert.notNull(groupId, "销售组Id不可为空");
        List<Integer> bindSaleIds = saleGroupMappingServiceImpl
                .querySaleIdsByGroupIds(Collections.singletonList(groupId), SaleGroupRoleType.MEMBER);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return new ArrayList<>();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andIdIn(bindSaleIds);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return this.buildSaleDtos(salePos);
    }

    // 已修改
    @Override
    public List<SaleDto> getSalesByGroupIds(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        List<Integer> bindSaleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Lists.newArrayList();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return this.buildSaleDtos(salePos);
    }

    // 已修改
    @Override
    public List<Integer> getSalesIdByGroupIdsNew(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Lists.newArrayList();
        }
        List<Integer> bindSaleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupIds(groupIds);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Lists.newArrayList();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> crmSalePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePos);
        CrmSaleGroupPoExample exampleGroup = new CrmSaleGroupPoExample();
        exampleGroup.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode())
                .andIdIn(groupIds);

        List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(groupIds).stream()
                .distinct().filter(a -> a > 0).collect(Collectors.toList());
        List<Integer> saleIds = crmSalePos.stream().map(CrmSalePo::getId).collect(Collectors.toList());
        saleIds.addAll(leaderIds);
        saleIds = saleIds.stream().distinct().filter(a -> a > 0).collect(Collectors.toList());
        return saleIds;
    }

    @Override
    public List<Integer> getSalesIdByGroupIdsWithAbandon(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }

        List<Integer> bindSaleIds = saleGroupMappingRepo.querySaleIdsByGroupIdsAndLevels(groupIds, null);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> crmSalePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePos);

        List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(groupIds).stream()
                .distinct().filter(a -> a > 0).collect(Collectors.toList());
        List<Integer> saleIds = crmSalePos.stream().map(CrmSalePo::getId).collect(Collectors.toList());
        saleIds.addAll(leaderIds);
        saleIds = saleIds.stream().distinct().filter(a -> a > 0).collect(Collectors.toList());
        return saleIds;
    }

    @Override
    public List<Integer> getSalesIdBySaleIdsWithTime(List<Integer> bindSaleIds, Timestamp base) {
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return new ArrayList<>();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andSaleIsQuitEqualTo(IsValid.FALSE.getCode())
                .andIdIn(bindSaleIds);
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andSaleIsQuitEqualTo(IsValid.TRUE.getCode())
                .andSaleQuitTimeGreaterThanOrEqualTo(base).andIdIn(bindSaleIds);
        List<CrmSalePo> crmSalePos = crmSaleDao.selectByExample(example);
        return crmSalePos.stream()
                .map(CrmSalePo::getId)
                .filter(a -> a > 0).distinct()
                .collect(Collectors.toList());
    }

    @Override
    public List<SaleBaseDto> getSaleBaseDto(QuerySaleDto querySaleDto) {
        CrmSalePoExample example = createExampleFromQueryDto(querySaleDto);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).collect(Collectors.toList());
    }

    /**
     * 根据ID查询sale
     *
     * @param saleId
     * @return 若查询无结果，则返回SaleBaseDto的空对象SaleBaseDto.builder().build();
     */
    @Override
    public SaleBaseDto getSaleBaseDto(Integer saleId) {
        if (null == saleId) {
            return SaleBaseDto.builder().build();
        }
        CrmSalePo crmSalePo = crmSaleDao.selectByPrimaryKey(saleId);
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePo);
        if (null == crmSalePo) {
            return SaleBaseDto.builder().build();
        }
        return this.po2SaleBaseDto(crmSalePo);
    }

    @Override
    public SaleDto getSaleByEmail(String email) {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andEmailEqualTo(email);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        List<SaleDto> saleDtos = this.buildSaleDtos(salePos);
        if (CollectionUtils.isEmpty(saleDtos)) {
            return null;
        }
        return saleDtos.get(0);
    }

    public SaleBaseDto getSaleByEmailExcludeBanned(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andEmailEqualTo(email);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).findFirst().orElse(null);
    }

    @Override
    public Map<Integer, SaleBaseDto> getAllSaleBaseDtoMap() {
        CrmSalePoExample example = new CrmSalePoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).collect(Collectors.toMap(SaleBaseDto::getId, Function.identity()));
    }

    @Override
    public Map<Integer, SaleInfoWithHiveDTO> getAllOperationBaseDtoMapForHive(String logDate) {
        Map<Integer, SaleInfoWithHiveDTO> hiveDTOMap = getAllSaleMapForHive(logDate);
        Map<Integer, SaleInfoWithHiveDTO> result = new HashMap<>();
        for (Map.Entry<Integer, SaleInfoWithHiveDTO> entry : hiveDTOMap.entrySet()) {
            SaleInfoWithHiveDTO hiveDTO = entry.getValue();
            if (SaleTypeEnum.CHANNEL_OPERATE.getCode().toString().equals(hiveDTO.getSale_type()) || SaleTypeEnum.DIRECT_OPERATE.getCode().toString().equals(hiveDTO.getSale_type())) {
                result.put(strToInteger(hiveDTO.getSale_id()), hiveDTO);
            }
        }
        return result;
    }

    private Integer strToInteger(String a) {
        if (org.springframework.util.StringUtils.isEmpty(a)) {
            return null;
        }
        return Integer.valueOf(a);
    }

    @Override
    public Map<Integer, SaleInfoWithHiveDTO> getAllSaleMapForHive(String logDate) {
        List<SaleInfoWithHiveDTO> saleInfoWithHiveDTOList = investmentHiveService.queryAllSaleInfoWithHiveDTO(logDate);
        return saleInfoWithHiveDTOList.stream().collect(Collectors.toMap(e -> Integer.valueOf(e.getSale_id()), Function.identity()));
    }

    @Override
    public SaleInfoWithHiveDTO querySaleInfoWithHiveByEmail(String email, String logDate) {
        List<SaleInfoWithHiveDTO> saleInfoWithHiveDTOList = investmentHiveService.querySaleInfoWithHiveByEmail(logDate, email);
        return saleInfoWithHiveDTOList.stream()
                .filter(r -> Objects.equals(r.getSale_status(), SaleTypeStatus.VALID.getCode().toString()))
                .findFirst().orElse(null);
    }

    @Override
    public Map<Integer, SaleBaseDto> getSaleBaseDtoMapByIds(List<Integer> saleIds) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return new HashMap<>();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(saleIds);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).collect(Collectors.toMap(SaleBaseDto::getId, Function.identity()));
    }

    @Override
    public SaleBaseDto getValidSaleByEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andEmailEqualTo(email).andStatusEqualTo(IsAble.NORMAL.getCode());
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).findFirst().orElse(null);
    }

    @Override
    public Map<String, SaleDto> getSaleMapByEmailList(List<String> emailList) {
        if (CollectionUtils.isEmpty(emailList)) {
            return Collections.emptyMap();
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andEmailIn(emailList);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(salePos)) {
            return Collections.emptyMap();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        List<SaleDto> saleDtoList = buildSaleDtos(salePos);
        return saleDtoList.stream().collect(Collectors.toMap(SaleDto::getEmail, Function.identity()));
    }

    //根据操作者返回当前组的成员和子组织节点
    @Override
    @Deprecated
    public List<SaleDto> getGroupIdsByOperator(Operator operator) {
        return Collections.emptyList();
    }

    //查询销售员列表
    @Override
    public List<SaleDto> getSalesIdsByOperator(Operator operator, List<Integer> saleGroupIds) {
        DataStrategy dataStrategy = getDataStrategy(operator);
        List<Integer> saleIdsFromOperator = dataStrategy.getCondition();

        if (CollectionUtils.isEmpty(saleIdsFromOperator)) {
            return Collections.emptyList();
        }
        if (!CollectionUtils.isEmpty(saleGroupIds)) {
            List<Integer> saleIdsByGroupId = saleGroupService.getSaleIdByGroupId(saleGroupIds);
            saleIdsFromOperator.retainAll(saleIdsByGroupId);
        }
        List<SaleDto> sales = getSalesInIds(saleIdsFromOperator);

        Map<Integer, SaleDto> saleDtoMapForSale = sales.stream().filter(r -> SaleGroupStatus.VALID.getCode().equals(r.getStatus())).collect(Collectors.toMap(SaleDto::getId, Function.identity(), (o1, o2) -> o2));
        return saleDtoMapForSale.values().stream().collect(Collectors.toList());
    }

    @Override
    public Map<String, Integer> getEmail2SaleIdMap(List<String> saleEmails) {
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andEmailIn(saleEmails);
        List<CrmSalePo> crmSalePos = crmSaleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmSalePos)) {
            return Collections.emptyMap();
        }

        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePos);
        return crmSalePos.stream().collect(Collectors.toMap(CrmSalePo::getEmail, CrmSalePo::getId));
    }

    /**
     * 销售策略 ,部门角色获取所有销售，非部门角色，获取角色下的销售
     * 部门角色所有销售临时没有用到， 功能下掉
     *
     * @param operator
     * @return
     */
    @Override
    public DataStrategy getDataStrategy(Operator operator) {
        return domainUserService.dataStrategyControl(operator.getOperatorName(),
                // 部门权限控制,
                (dataStrategy) -> {
                    if (dataStrategy.getScope().equals(DataStrategyScope.EMPTY)) {
                        return dataStrategy;
                    }
                    return DataStrategy.builder()
                            .orgType(dataStrategy.getOrgType())
                            .scope(dataStrategy.getScope())
                            .condition(getAllSaleId())
                            .build();
                },
                // org权限控制 saleIds
                Function.identity()
        );
    }

    /**
     * Map<email,name>,查询域账号与姓名之间的映射关系
     *
     * @return
     */
    @Override
    public Map<String, String> querySaleName(QuerySaleDto query) {
        CrmSalePoExample example = createExampleFromQueryDto(query);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().collect(Collectors.toMap(CrmSalePo::getEmail, CrmSalePo::getName));
    }

    @Override
    public Map<Integer, String> querySaleNameMap(QuerySaleDto query) {
        CrmSalePoExample example = createExampleFromQueryDto(query);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().collect(Collectors.toMap(CrmSalePo::getId, CrmSalePo::getName));
    }

    public Map<Integer, List<CrmSaleGroupPo>> getSaleGroupMap(List<Integer> saleIds) {
        Map<Integer, List<CrmSaleGroupPo>> result = new HashMap<>();
        List<SaleDto> saleGroups = this.getSalesInIds(saleIds);
        List<Integer> groupIds = saleGroups.stream().map(SaleDto::getGroupId).collect(Collectors.toList());
        Map<Integer, List<CrmSaleGroupPo>> saleGroupOrg = saleGroupService.getParentSaleGroup(groupIds);
        for (Map.Entry<Integer, List<CrmSaleGroupPo>> entry : saleGroupOrg.entrySet()) {
            if (!CollectionUtils.isEmpty(entry.getValue())) {
                entry.getValue().add(CrmSaleGroupPo.builder().name("bilibili").build());
            }
        }
        for (SaleDto saleDto : saleGroups) {
            List<CrmSaleGroupPo> tmpSaleGroupList = saleGroupOrg.getOrDefault(saleDto.getGroupId(), Collections.emptyList());
            Collections.reverse(tmpSaleGroupList);
            result.put(saleDto.getId(), tmpSaleGroupList);
        }
        return result;
    }


    private SaleBaseDto po2SaleBaseDto(CrmSalePo po) {
        SaleBaseDto dto = SaleBaseDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    public static String buildSaleName(List<Integer> saleIds, Map<Integer, SaleDto> saleDtoMap) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return "";
        }
        saleIds = saleIds.stream().filter(Utils::isPositive).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(saleIds)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (Integer saleId : saleIds) {
            String name = saleDtoMap.getOrDefault(saleId, SaleDto.builder().name("").build()).getName();
            stringBuilder.append(name).append(",");
        }
        String result = stringBuilder.toString();
        return result.length() > 0 ? result.substring(0, result.length() - 1) : result;
    }

    public static String getSaleGroup(List<Integer> saleIds, Map<Integer, SaleDto> saleMap) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return null;
        }
        return saleIds.stream().map(sale -> {
            SaleDto saleDto = saleMap.getOrDefault(sale, SaleDto.builder().build());
            if (!CollectionUtils.isEmpty(saleDto.getParentGroups()) && saleDto.getParentGroups().size() >= 1) {
                return saleDto.getParentGroups().get(saleDto.getParentGroups().size() - 1);
            }
            return "";
        }).collect(Collectors.joining(","));
    }

    public static String getSaleGroup(List<SaleDto> saleDtos) {
        if (CollectionUtils.isEmpty(saleDtos)) {
            return null;
        }
        return saleDtos.stream()
                .map(SaleDto::getGroupNames)
                .flatMap(List::stream)
                .collect(Collectors.joining(","));
    }

    @Override
    public String getLastSaleGroup(List<SaleDto> saleDtos) {
        if (CollectionUtils.isEmpty(saleDtos)) {
            return null;
        }
        List<Integer> groupIds = saleDtos.stream().map(SaleDto::getGroupIds)
                .flatMap(List::stream)
                .distinct().collect(Collectors.toList());
        return saleGroupService.getGroupNameAndParentGroupNames(groupIds).getValue();
    }

    public static String getLastSaleGroup(List<Integer> saleIds, Map<Integer, SaleDto> saleMap) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return null;
        }
        return saleIds.stream().map(sale -> {
            SaleDto saleDto = saleMap.getOrDefault(sale, SaleDto.builder().build());
            if (!CollectionUtils.isEmpty(saleDto.getParentGroups()) && saleDto.getParentGroups().size() >= 2) {
                return saleDto.getParentGroups().get(saleDto.getParentGroups().size() - 2);
            }
            return "";
        }).collect(Collectors.joining(","));
    }

    @Override
    public Map<Integer, String> getSaleNicknameMap(QuerySaleDto queryDto) {
        CrmSalePoExample example = createExampleFromQueryDto(queryDto);

        List<CrmSalePo> crmSalePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePos);
        return crmSalePos.stream().collect(Collectors.toMap(CrmSalePo::getId, CrmSalePo::getNickName));
    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public int updateSaleKpi(CrmSaleKpiDto crmSaleKpiDto) {

        if (null != crmSaleKpiDto.getOperatorObj() && null != crmSaleKpiDto.getNewLogOperatorDto()) {
            logOperatorService.insertLog(crmSaleKpiDto.getOperatorObj(), crmSaleKpiDto.getNewLogOperatorDto());
        }

        CrmSaleKpiPo po = new CrmSaleKpiPo();
        BeanUtils.copyProperties(crmSaleKpiDto, po);
        po.setQuarterStr(crmSaleKpiDto.getQuarter());
        po.setCtime(null);
        po.setCorrectionCoefficient(crmSaleKpiDto.getCorrectionCoefficient());
        return crmSaleKpiDao.insertUpdateSelective(po);
    }

    @Override
    public void updateSaleKpiNotice(List<CrmSaleKpiDto> crmSaleKpiDtos, Integer scene) {
        if (scene.equals(SaleKpiNoticeEnum.NON_NOTICE.getId())) {
            return;
        }
        crmSaleKpiDtos = crmSaleKpiDtos.stream().filter(a -> a.getBizType().equals(SaleKpiTypeEnum.SALE.getBizId())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmSaleKpiDtos)) {
            return;
        }
        List<Integer> saleIds = crmSaleKpiDtos.stream().map(CrmSaleKpiDto::getBizId).filter(a -> a > 0).distinct().collect(Collectors.toList());
        Map<Integer, CrmSaleKpiDto> oldMap = new HashMap<>();
        if (scene.equals(SaleKpiNoticeEnum.ADJUST_NOTICE.getId())) {
            List<CrmSaleKpiDto> oldList = querySaleKpi(saleIds, crmSaleKpiDtos.get(0).getQuarter(), SaleKpiTypeEnum.SALE.getBizId());
            oldMap = oldList.stream().collect(Collectors.toMap(CrmSaleKpiDto::getBizId, Function.identity(), (a, b) -> a));
        }
        List<SaleDto> saleDtos = getSalesInIds(saleIds);
        List<OaCoreUserInfo> oaCoreUserInfos = oaUserInfoService.getOaCoreUserInfos(saleDtos.stream().map(SaleDto::getEmail).collect(Collectors.toList()));
        Map<String, String> oaUserMap = oaCoreUserInfos.stream().collect(Collectors.toMap(OaCoreUserInfo::getAdAccount, OaCoreUserInfo::getNickName));
        Map<Integer, SaleDto> saleDtoMap = saleDtos.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));
        Map<Integer, CrmSaleKpiDto> crmSaleKpiDtoMap = crmSaleKpiDtos.stream().collect(Collectors.toMap(CrmSaleKpiDto::getBizId, Function.identity(), (a, b) -> a));
        String[] yearList = crmSaleKpiDtos.get(0).getQuarter().split("Q");
        for (CrmSaleKpiDto crmSaleKpiDto : crmSaleKpiDtos) {
            SaleDto saleDto = saleDtoMap.get(crmSaleKpiDto.getBizId());
            if (Objects.isNull(saleDto)) {
                continue;
            }
            String wxContent;
            if (scene.equals(SaleKpiNoticeEnum.FIRST_NOTICE.getId())) {
                wxContent = String.format("【销售目标下发通知】%s你好，%s年第%s季度个人目标已在CRM下发，后续若涉及调整以CRM为准。<br/>" +
                                "个人目标值：%s元<br/>" +
                                "请登陆CRM进行查看，具体位置：CRM-业绩收入-业绩监控（<a href=\"%s\">访问产品</a>）。如有疑问，请在2个工作日内与直属leader、销管沟通。",
                        oaUserMap.getOrDefault(saleDto.getEmail(), saleDto.getName()),
                        yearList[0],
                        "Q" + yearList[1],
                        Utils.fromFenToYuan(crmSaleKpiDto.getQuarterKpi()),
                        wechatUrl);
            } else {
                CrmSaleKpiDto oldKpi = oldMap.get(crmSaleKpiDto.getBizId());
                wxContent = String.format("【销售目标调整通知】%s你好，经与总监/leader确认，%s年第%s季度个人目标已更新，后续若涉及调整以CRM为准。<br/>" +
                                "调整前目标：%s元<br/>" +
                                "调整后目标：%s元<br/>" +
                                "请登陆CRM进行查看，具体位置：CRM-业绩收入-业绩监控（<a href=\"%s\">访问产品</a>）。如有疑问，请在2个工作日内与直属leader、销管沟通。",
                        oaUserMap.getOrDefault(saleDto.getEmail(), saleDto.getName()),
                        yearList[0],
                        "Q" + yearList[1],
                        Utils.fromFenToYuan(oldKpi.getQuarterKpi()),
                        Utils.fromFenToYuan(crmSaleKpiDto.getQuarterKpi()),
                        wechatUrl);
            }
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(saleDto.getEmail()), wxContent);
        }
        // 对应 leader 整理
        List<Integer> groupIds = saleDtos.stream().map(SaleDto::getPIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Integer, SaleGroupDto> saleGroupDtoMap = saleGroupService.getSaleGroupMapInIds(groupIds);
        Map<Integer, List<Integer>> groupIdLeaderIds = saleGroupMappingServiceImpl.queryLeaderIdsMapByGroupIds(new ArrayList<>(saleGroupDtoMap.keySet()));
        List<Integer> leaderIds = groupIdLeaderIds.values().stream().filter(Objects::nonNull).flatMap(List::stream).distinct().collect(Collectors.toList());
        List<SaleDto> leaderDtos = getSalesInIds(leaderIds);
        List<OaCoreUserInfo> oaCoreLeaderInfos = oaUserInfoService.getOaCoreUserInfos(leaderDtos.stream().map(SaleDto::getEmail).collect(Collectors.toList()));
        Map<String, String> oaUserLeaderMap = oaCoreLeaderInfos.stream().collect(Collectors.toMap(OaCoreUserInfo::getAdAccount, OaCoreUserInfo::getNickName));
        Map<Integer, SaleDto> leaderMap = leaderDtos.stream().collect(Collectors.toMap(SaleDto::getId, Function.identity()));

        for (Integer groupId : groupIds) {
            SaleGroupDto saleGroupDto = saleGroupDtoMap.get(groupId);
            List<Integer> groupLeaderIds = groupIdLeaderIds.get(groupId);
            if (Objects.isNull(saleGroupDto) || CollectionUtils.isEmpty(groupLeaderIds)) {
                continue;
            }
            List<SaleDto> theSaleDtoList = saleDtos.stream().filter(a -> Optional.ofNullable(a.getPIds()).orElse(new ArrayList<>()).contains(groupId)).collect(Collectors.toList());
            List<Integer> theSaleIds = theSaleDtoList.stream().map(SaleDto::getId).collect(Collectors.toList());
            if (CollectionUtils.isEmpty(theSaleIds)) {
                continue;
            }
            for (Integer groupLeaderId : groupLeaderIds) {
                String wxContent;

                SaleDto leader = leaderMap.get(groupLeaderId);
                if (scene.equals(SaleKpiNoticeEnum.FIRST_NOTICE.getId())) {
                    String title = String.format("【销售目标下发通知】%s你好，以下人员%s年第%s季度个人目标已在CRM下发，后续若涉及调整以CRM为准。<br/>",
                            oaUserLeaderMap.getOrDefault(leader.getEmail(), leader.getName()),
                            yearList[0],
                            "Q" + yearList[1]);
                    StringBuilder saleInfo = new StringBuilder();
                    int i = 0;
                    for (SaleDto saleDto : theSaleDtoList) {
                        saleInfo.append(String.format("销售：%s 个人目标值：%s元<br/>", oaUserMap.getOrDefault(saleDto.getEmail(), saleDto.getName()), Utils.fromFenToYuan(crmSaleKpiDtoMap.getOrDefault(saleDto.getId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi())));
                        if (++i >= 10) {
                            saleInfo.append("......（未显示完整）<br/>");
                            break;
                        }
                    }
                    wxContent = title + saleInfo + String.format("请登陆CRM进行查看，具体位置：CRM-业绩收入-业绩监控（<a href=\"%s\">访问产品</a>）", wechatUrl);
                } else {
                    String title = String.format("【销售目标调整通知】%s你好，经与总监/leader确认，以下人员%s年第%s季度个人目标已更新，后续若涉及调整以CRM为准。<br/>",
                            oaUserLeaderMap.getOrDefault(leader.getEmail(), leader.getName()),
                            yearList[0],
                            "Q" + yearList[1]);
                    StringBuilder saleInfo = new StringBuilder();
                    int i = 0;
                    for (SaleDto saleDto : theSaleDtoList) {
                        saleInfo.append(String.format("销售：%s 调整前目标：%s元 调整后目标：%s元<br/>", oaUserMap.getOrDefault(saleDto.getEmail(), saleDto.getName()),
                                Utils.fromFenToYuan(oldMap.getOrDefault(saleDto.getId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi()),
                                Utils.fromFenToYuan(crmSaleKpiDtoMap.getOrDefault(saleDto.getId(), CrmSaleKpiDto.builder().quarterKpi(0L).build()).getQuarterKpi())));
                        if (++i >= 10) {
                            saleInfo.append("......（未显示完整）<br/>");
                            break;
                        }
                    }
                    wxContent = title + saleInfo + String.format("请登陆CRM进行查看，具体位置：CRM-业绩收入-业绩监控（<a href=\"%s\">访问产品</a>）", wechatUrl);
                }
                contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(leader.getEmail()), wxContent);
            }
        }
    }

    @Override
    public void updateSaleKpiNoticeForBasic(List<CrmSaleKpiDto> oldDtoList, CrmSaleBasicKpiAddDTO newDto, Integer scene) {
        if (scene.equals(SaleKpiNoticeEnum.NON_NOTICE.getId())) {
            return;
        }
        if (oldDtoList == null) {
            oldDtoList = new ArrayList<>();
        }

        if (Objects.equals(newDto.getIsSale(), IsValid.TRUE.getCode())) {
            handleIndividualSaleNotice(oldDtoList, newDto, scene);
        } else {
            handleGroupSaleNotice(oldDtoList, newDto, scene);
        }
    }

    private void handleIndividualSaleNotice(List<CrmSaleKpiDto> oldDtoList, CrmSaleBasicKpiAddDTO newDto, Integer scene) {
        SaleBaseDto saleBaseDto = getSaleBaseDto(newDto.getBizId());
        String nickName = oaUserInfoService.getNickNameByDomain(saleBaseDto.getEmail());
        String newStr = buildNewStr(newDto);

        if (scene.equals(SaleKpiNoticeEnum.ADJUST_NOTICE.getId())) {
            String oldStr = buildOldStr(oldDtoList, newDto);
            String pushText = String.format("【过程指标下发通知】%s你好，经与总监/leader确认，你的%s年第%s季度个人过程指标已更新，后续若涉及调整以CRM为准。\n" +
                            "调整前过程指标：%s\n" +
                            "调整后过程指标：%s\n" +
                            "请登录CRM进行查看，具体位置：CRM-人效监控-销售人效看板（<a href=\"%s\">访问产品</a>）",
                    nickName, newDto.getQuarter().split("Q")[0], newDto.getQuarter().split("Q")[1],
                    oldStr, newStr, saleEfficiencyUrl);
            sendWxMessage(saleBaseDto, pushText);
        } else {
            String pushText = String.format("【过程指标下发通知】%s你好，%s年第%s季度个人过程指标已在CRM下发，后续若涉及调整以CRM为准。\n" +
                            "%s\n" +
                            "请登录CRM进行查看，具体位置：CRM-人效监控-销售人效看板（<a href=\"%s\">访问产品</a>），如有疑问，请在2个工作日内与直属leader、销管沟通",
                    nickName, newDto.getQuarter().split("Q")[0], newDto.getQuarter().split("Q")[1],
                    newStr, saleEfficiencyUrl);
            sendWxMessage(saleBaseDto, pushText);
        }
    }

    private void handleGroupSaleNotice(List<CrmSaleKpiDto> oldDtoList, CrmSaleBasicKpiAddDTO newDto, Integer scene) {
        SaleGroupDto saleGroupDto = saleGroupService.queryBaseSaleGroupByGroupId(newDto.getBizId());
        List<Integer> leaders = saleGroupMappingServiceImpl.queryLeaderIdsByGroupId(saleGroupDto.getId());
        if (CollectionUtils.isEmpty(leaders)) {
            return;
        }
        List<SaleBaseDto> saleBaseDtoList = getSaleBaseDto(QuerySaleDto.builder().saleIds(leaders).build());

        saleBaseDtoList.forEach(t -> {
            String nickName = oaUserInfoService.getNickNameByDomain(t.getEmail());
            String newStr = buildNewStr(newDto);

            if (scene.equals(SaleKpiNoticeEnum.ADJUST_NOTICE.getId())) {
                String oldStr = buildOldStr(oldDtoList, newDto);
                String pushText = String.format("【过程指标下发通知】%s你好，%s年第%s季度%s过程指标已更新，后续若涉及调整以CRM为准。\n" +
                                "调整前过程指标：%s\n" +
                                "调整后过程指标：%s\n" +
                                "请登录CRM进行查看，具体位置：CRM-人效监控-销售人效看板（<a href=\"%s\">访问产品</a>）",
                        nickName, newDto.getQuarter().split("Q")[0], newDto.getQuarter().split("Q")[1], saleGroupDto.getName(),
                        oldStr, newStr, saleEfficiencyUrl);
                sendWxMessage(t, pushText);
            } else {
                String pushText = String.format("【过程指标下发通知】%s你好，%s年第%s季度%s过程指标已更新，后续若涉及调整以CRM为准。\n" +
                                "%s\n" +
                                "请登录CRM进行查看，具体位置：CRM-人效监控-销售人效看板（<a href=\"%s\">访问产品</a>）",
                        nickName, newDto.getQuarter().split("Q")[0], newDto.getQuarter().split("Q")[1], saleGroupDto.getName(),
                        newStr, saleEfficiencyUrl);
                sendWxMessage(t, pushText);
            }
        });
    }

    private void sendWxMessage(SaleBaseDto saleBaseDto, String pushText) {
        if (!Objects.equals(env, "prd")) {
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList("xumeng03", "wanggang03", "wangyuan03", "gongwenxuan"), pushText);
        } else {
            contractBiiPeriodAlertComponent.sendQiwe(saleBaseDto.getEmail(), pushText);
        }
    }

    private String buildNewStr(CrmSaleBasicKpiAddDTO newDto) {
        StringBuilder newStr = new StringBuilder();
        appendIfPositive(newStr, newDto.getLine60Amount(), newDto.getLine60());
        appendIfPositive(newStr, newDto.getLine100Amount(), newDto.getLine100());
        appendIfPositive(newStr, newDto.getBsiTargetAmount(), newDto.getBsiTarget());
        appendIfPositive(newStr, newDto.getBsiTransAmount(), newDto.getBsiTrans());
        appendIfPositive(newStr, newDto.getOfflineVisitAmountTarget(), newDto.getOfflineVisitAmountEnum());
        appendIfPositive(newStr, newDto.getActiveCustomerCountTarget(), newDto.getActiveCustomerCountEnum());
        appendIfPositive(newStr, newDto.getActiveProductCountTarget(), newDto.getActiveProductCountEnum());
        appendIfPositive(newStr, newDto.getNewCustomerCountTarget(), newDto.getNewCustomerCountEnum());
        appendIfPositive(newStr, newDto.getProjectSalesTarget(), newDto.getProjectSalesEnum());
        return newStr.toString();
    }

    private String buildOldStr(List<CrmSaleKpiDto> oldDtoList, CrmSaleBasicKpiAddDTO newDto) {
        StringBuilder oldStr = new StringBuilder();
        appendIfMatch(oldStr, oldDtoList, newDto.getLine60().getBizId(), newDto.getLine60());
        appendIfMatch(oldStr, oldDtoList, newDto.getLine100().getBizId(), newDto.getLine100());
        appendIfMatch(oldStr, oldDtoList, newDto.getBsiTarget().getBizId(), newDto.getBsiTarget());
        appendIfMatch(oldStr, oldDtoList, newDto.getBsiTrans().getBizId(), newDto.getBsiTrans());
        appendIfMatch(oldStr, oldDtoList, newDto.getOfflineVisitAmountEnum().getBizId(), newDto.getOfflineVisitAmountEnum());
        appendIfMatch(oldStr, oldDtoList, newDto.getActiveCustomerCountEnum().getBizId(), newDto.getActiveCustomerCountEnum());
        appendIfMatch(oldStr, oldDtoList, newDto.getActiveProductCountEnum().getBizId(), newDto.getActiveProductCountEnum());
        appendIfMatch(oldStr, oldDtoList, newDto.getNewCustomerCountEnum().getBizId(), newDto.getNewCustomerCountEnum());
        appendIfMatch(oldStr, oldDtoList, newDto.getProjectSalesEnum().getBizId(), newDto.getProjectSalesEnum());
        return oldStr.toString();
    }

    private void appendIfPositive(StringBuilder builder, BigDecimal amount, SaleKpiTypeEnum typeEnum) {
        if (Utils.isPositive(amount)) {
            if (builder.length() > 0) {
                builder.append("，");
            }
            builder.append(typeEnum.getDesc()).append(": ").append(amount.longValue());
            if (EnumSet.of(
                    SaleKpiTypeEnum.BASIC_SALE_BSI_TRANS_TARGET,
                    SaleKpiTypeEnum.BASIC_GROUP_BSI_TRANS_TARGET,
                    SaleKpiTypeEnum.BASIC_SALE_OFFLINE_VISIT_AMOUNT_TARGET,
                    SaleKpiTypeEnum.BASIC_GROUP_OFFLINE_VISIT_AMOUNT_TARGET
            ).contains(typeEnum)) {
                builder.append("%");
            }
        }
    }

    private void appendIfMatch(StringBuilder builder, List<CrmSaleKpiDto> oldDtoList, Integer bizId, SaleKpiTypeEnum typeEnum) {
        oldDtoList.stream()
                .filter(e -> Objects.equals(e.getBizType(), bizId))
                .findFirst()
                .ifPresent(e -> {
                    if (builder.length() > 0) {
                        builder.append("，");
                    }
                    builder.append(typeEnum.getDesc()).append(": ").append(e.getQuarterKpi());
                    if (EnumSet.of(
                            SaleKpiTypeEnum.BASIC_SALE_BSI_TRANS_TARGET,
                            SaleKpiTypeEnum.BASIC_GROUP_BSI_TRANS_TARGET,
                            SaleKpiTypeEnum.BASIC_SALE_OFFLINE_VISIT_AMOUNT_TARGET,
                            SaleKpiTypeEnum.BASIC_GROUP_OFFLINE_VISIT_AMOUNT_TARGET
                    ).contains(typeEnum)) {
                        builder.append("%");
                    }
                });
    }

    @Override
    public int updateSaleKpi(String quarter, Long amount, Integer bizType, Integer bizId, Integer isSale, Integer isEstimate, Operator operator) {
        CrmSaleKpiDto crmSaleKpiDto = CrmSaleKpiDto.builder()
                .quarterKpi(amount)
                .quarter(quarter)
                .operator(operator.getOperatorName())
                .bizId(bizId)
                .bizType(bizType)
                .build();
        crmSaleKpiDto.setNewLogOperatorDto(NewLogOperatorDto.builder()
                .oldObj("")
                .obj(crmSaleKpiDto)
                .objId(bizId)
                .module(Objects.equals(isSale, IsValid.TRUE.getCode()) ? Module.SALE : Module.SALE_GROUP)
                .modifyType(Objects.equals(isEstimate, IsValid.TRUE.getCode()) ? ModifyType.BATCH_UPDATE_SALE_ESTIMATE : ModifyType.BATCH_UPDATE_SALE_KPI)
                .systemType(SystemType.CRM)
                .build());
        return updateSaleKpi(crmSaleKpiDto);
    }

    @Override
    public int updateOffset(String quarter, Long amount, Integer bizType, Integer bizId, Operator operator) {
        CrmSaleKpiDto crmSaleKpiDto = CrmSaleKpiDto.builder()
                .quarterKpi(amount)
                .quarter(quarter)
                .operator(operator.getOperatorName())
                .bizId(bizId)
                .bizType(bizType)
                .build();
        crmSaleKpiDto.setNewLogOperatorDto(NewLogOperatorDto.builder()
                .oldObj("")
                .obj(crmSaleKpiDto)
                .objId(bizId)
                .module(Module.SALE_GROUP)
                .modifyType(ModifyType.BATCH_UPDATE_SALE_OFFSET)
                .systemType(SystemType.CRM)
                .build());
        return updateSaleKpi(crmSaleKpiDto);
    }

    @Override
    public List<CrmSaleKpiDto> querySaleKpi(List<Integer> bizIds, String quarter, Integer bizType) {
        List<CrmSaleKpiDto> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(bizIds)) {
            return result;
        }
        CrmSaleKpiPoExample crmSaleKpiPoExample = new CrmSaleKpiPoExample();
        crmSaleKpiPoExample.or().andBizIdIn(bizIds).andBizTypeEqualTo(bizType)
                .andQuarterStrEqualTo(quarter);

        List<CrmSaleKpiPo> crmSaleKpiPos = crmSaleKpiDao.selectByExample(crmSaleKpiPoExample);

        crmSaleKpiPos.forEach(
                item -> {
                    CrmSaleKpiDto dto = new CrmSaleKpiDto();
                    BeanUtils.copyProperties(item, dto);
                    result.add(dto);
                }
        );
        return result;
    }

    @Override
    public List<CrmSaleKpiDto> queryAllKpiByQuarter(String quarter) {
        List<CrmSaleKpiDto> result = new ArrayList<>();
        if (StringUtils.isEmpty(quarter)) {
            return result;
        }
        CrmSaleKpiPoExample crmSaleKpiPoExample = new CrmSaleKpiPoExample();
        crmSaleKpiPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andQuarterStrEqualTo(quarter);
        List<CrmSaleKpiPo> crmSaleKpiPos = crmSaleKpiDao.selectByExample(crmSaleKpiPoExample);
        crmSaleKpiPos.forEach(
                item -> {
                    CrmSaleKpiDto dto = new CrmSaleKpiDto();
                    BeanUtils.copyProperties(item, dto);
                    dto.setId(item.getId().intValue());
                    dto.setQuarter(item.getQuarterStr());
                    result.add(dto);
                }
        );
        return result;
    }

    @Override
    public List<CrmSaleKpiDto> queryAllKpiByQuarterAndBizId(String quarter, List<Integer> bizIds) {
        List<CrmSaleKpiDto> result = new ArrayList<>();
        if (StringUtils.isEmpty(quarter) || CollectionUtils.isEmpty(bizIds)) {
            return result;
        }
        CrmSaleKpiPoExample crmSaleKpiPoExample = new CrmSaleKpiPoExample();
        crmSaleKpiPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andQuarterStrEqualTo(quarter).andBizIdIn(bizIds);
        List<CrmSaleKpiPo> crmSaleKpiPos = crmSaleKpiDao.selectByExample(crmSaleKpiPoExample);
        crmSaleKpiPos.forEach(
                item -> {
                    CrmSaleKpiDto dto = new CrmSaleKpiDto();
                    BeanUtils.copyProperties(item, dto);
                    dto.setId(item.getId().intValue());
                    dto.setQuarter(item.getQuarterStr());
                    result.add(dto);
                }
        );
        return result;
    }

    @Override
    public List<SaleBaseDto> querySaleDirectLeader(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        SaleBaseDto sale = queryBaseSaleByEmail(email);
        if (null == sale) {
            return null;
        }

        if (Objects.equals(sale.getLevel(), SaleLevelEnum.LEADER.getCode())) {
            SaleGroupDto directGroup = saleGroupService.queryDirectBaseSaleGroupByGroupId(sale.getGroupId());
            if (null == directGroup) {
                return null;
            }
            List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(Collections.singletonList(directGroup.getId()));
            return queryBaseSaleById(leaderIds);
        }

        SaleGroupDto saleGroupDto = saleGroupService.queryBaseSaleGroupByGroupId(sale.getGroupId());
        if (null == saleGroupDto) {
            return null;
        }
        List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(Collections.singletonList(saleGroupDto.getId()));
        return queryBaseSaleById(leaderIds);
    }

    @Override
    public List<SaleBaseDto> queryBaseSaleById(List<Integer> id) {
        if (null == id) {
            return null;
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andIdIn(id);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).collect(Collectors.toList());
    }

    @Override
    public SaleBaseDto queryBaseSaleByEmail(String email) {
        if (StringUtils.isBlank(email)) {
            return null;
        }
        CrmSalePoExample example = new CrmSalePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andEmailEqualTo(email);
        List<CrmSalePo> salePos = crmSaleDao.selectByExample(example);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
        return salePos.stream().map(this::po2SaleBaseDto).findFirst().orElse(null);
    }

    @Override
    public void updateMaxClaimNum(Operator operator, Integer maxNum, Map<Integer, Integer> saleClaimMap, Boolean isReset) {
        CrmSalePoExample example = new CrmSalePoExample();
        CrmSalePoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        CrmSalePo po = CrmSalePo.builder()
                .defaultClaimNum(maxNum)
                .build();

        if (isReset) {
            po.setClaimNumConfigDay("");
        }
        int num = crmSaleDao.updateByExampleSelective(po, example);
        log.info("updateMaxClaimNum num = {}", num);

        if (!saleClaimMap.isEmpty()) {
            saleClaimMap.entrySet().stream().forEach(t -> {
                // 这里要算下Q时间，写一个Q结束时间
                Timestamp startTime = incomeTimeUtil.getQuarterFirstDate(new Timestamp(System.currentTimeMillis()));
                Timestamp endTime = incomeTimeUtil.getQuarterEndDate(new Timestamp(System.currentTimeMillis()));
                ClaimNumConfigDto claimNumConfigDto = ClaimNumConfigDto.builder()
                        .startTime(startTime.getTime())
                        .endTime(endTime.getTime())
                        .numOfDay(t.getValue())
                        .build();
                CrmSalePo crmSalePo = CrmSalePo.builder()
                        .id(t.getKey())
                        .claimNumConfigDay(JSON.toJSONString(claimNumConfigDto))
                        .build();
                crmSaleDao.updateByPrimaryKeySelective(crmSalePo);
                log.info("updateMaxClaimNum saleId = {}", t.getKey());
            });
        }

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.UPDATE_SALE_CLAIM_NUM)
                .module(Module.SALE_CLAIM_NUM)
                .oldObj(saleClaimMap)
                .obj(maxNum)
                .objId(operator.getOperatorId())
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logDto);
    }
}
