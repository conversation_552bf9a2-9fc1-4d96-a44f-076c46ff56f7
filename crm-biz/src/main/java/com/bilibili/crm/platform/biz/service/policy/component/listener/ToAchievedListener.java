package com.bilibili.crm.platform.biz.service.policy.component.listener;

import com.bilibili.crm.platform.biz.po.CrmReturnApplyPo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.backredpacket.AutoBackRedPacketProcessorForReturnOnline;
import com.bilibili.crm.platform.biz.util.SpringContextUtil;
import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.TaskListener;
import org.flowable.task.service.delegate.BaseTaskListener;
import org.flowable.task.service.delegate.DelegateTask;

/**
 * 待归档监听器
 *
 * <AUTHOR>
 * @date 2021/8/15 上午1:52
 */
// 此处加 @Component 没用
@Slf4j
public class ToAchievedListener implements TaskListener {

    @Override
    public void notify(DelegateTask execution) {
        log.info("=====> flow end, cur activity id:{}, processInstanceId:{}, eventName:{}",
                execution.getId(), execution.getProcessInstanceId(), execution.getEventName());

        if (!BaseTaskListener.EVENTNAME_COMPLETE.equals(execution.getEventName())) {
            return;
        }

//        // 返点流程，最后一个审核通过的时候，需要自动触发财务返点
//        CrmReturnApplyRepo crmReturnApplyRepo = SpringContextUtil.getBean("crmReturnApplyRepo");
//        AutoBackRedPacketProcessorForReturnOnline autoBackRedPacketProcessorForReturnOnline = SpringContextUtil.getBean("autoBackRedPacketProcessorForReturnOnline");
//        CrmReturnApplyPo crmReturnApplyPo = crmReturnApplyRepo.queryByProcessInstanceId(execution.getProcessInstanceId());
//        if (crmReturnApplyPo != null) {
//            autoBackRedPacketProcessorForReturnOnline.createBackRedPacket(execution.getProcessInstanceId());
//        }


//        CrmContractPolicyMappingRepo crmContractPolicyMappingRepo = SpringContextUtil.getBean("crmContractPolicyMappingRepo");
//        CrmContractPolicyInfoRepo crmContractPolicyInfoRepo = SpringContextUtil.getBean("crmContractPolicyInfoRepo");
//        ToAchievedAdoptProcessor toAchievedAdoptProcessor = SpringContextUtil.getBean("toAchievedAdoptProcessor");
//
//        // 判断是否被合同绑定，如果绑定了，则修改流程状态为已归档；没有绑定修改流程状态为
//        List<CrmContractPolicyMappingPo> contractPolicyMappingPos = crmContractPolicyMappingRepo.queryListByProcInstanceId(execution.getProcessInstanceId());
//        if (CollectionUtils.isEmpty(contractPolicyMappingPos)) {
//            log.info("=====> flow end, cur activity id:{}, processInstanceId:{}, eventName:{}, no contract bind!",
//                    execution.getId(), execution.getProcessInstanceId(), execution.getEventName());
//            crmContractPolicyInfoRepo.updateStatusByProcessInstanceId(execution.getProcessInstanceId(),
//                    PolicyFlowStatusEnum.ADOPTED.getCode());
//        } else {
//            log.info("=====> flow end, cur activity id:{}, processInstanceId:{}, eventName:{}, has contract bind!",
//                    execution.getId(), execution.getProcessInstanceId(), execution.getEventName());
//            crmContractPolicyInfoRepo.updateStatusByProcessInstanceId(execution.getProcessInstanceId(),
//                    PolicyFlowStatusEnum.COMPLETED.getCode());
//
//            // 直接完成待归档节点
//            toAchievedAdoptProcessor.adopt(execution.getProcessInstanceId());
//        }
    }
}
