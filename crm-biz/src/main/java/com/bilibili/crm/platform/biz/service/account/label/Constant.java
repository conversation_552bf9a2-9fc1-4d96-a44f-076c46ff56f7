package com.bilibili.crm.platform.biz.service.account.label;

import java.util.HashMap;
import java.util.Map;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/2/2
 **/
public class Constant {

    public static Map<String, Map<Object, String>> ACCOUNT_LABEL = new HashMap<>();

    static {

        ACCOUNT_LABEL.put("account-label-userType", new HashMap<Object, String>() {{
            put(0, "个人");
            put(1, "机构");
            put(2, "不限");
            put(3, "个人起飞用");
        }});
        ACCOUNT_LABEL.put("account-label-isInner",new HashMap<Object, String>() {{
            put(0, "外部");
            put(1, "内部");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportFly",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportPickup",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportContent",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportDpa",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-adStatus",new HashMap<Object, String>() {{
            put(0, "个人用户");
            put(1, "机构用户");
            put(2, "不限");
            put(3, "个人起飞用");
        }});
        ACCOUNT_LABEL.put("account-label-gdStatus",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportGame",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportSeller",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportMas",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
        ACCOUNT_LABEL.put("account-label-isSupportCluePass",new HashMap<Object, String>() {{
            put(0, "不支持");
            put(1, "支持");
            put(2, "不限");
        }});
    }


}
