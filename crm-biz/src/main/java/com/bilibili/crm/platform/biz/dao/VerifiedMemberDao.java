package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.VerifiedMemberPo;
import com.bilibili.crm.platform.biz.po.VerifiedMemberPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2021/12/8
 **/
public interface VerifiedMemberDao {
    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    long countByExample(VerifiedMemberPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int deleteByExample(VerifiedMemberPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int deleteByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertUpdate(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertBatch(List<VerifiedMemberPo> records);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertUpdateBatch(List<VerifiedMemberPo> records);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertIgnore(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertIgnoreBatch(List<VerifiedMemberPo> records);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insert(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertUpdateSelective(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertIgnoreSelective(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int insertSelective(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    List<VerifiedMemberPo> selectByExample(VerifiedMemberPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    VerifiedMemberPo selectByPrimaryKey(Long id);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int updateByExampleSelective(@Param("record") VerifiedMemberPo record, @Param("example") VerifiedMemberPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int updateByExample(@Param("record") VerifiedMemberPo record, @Param("example") VerifiedMemberPoExample example);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int updateByPrimaryKeySelective(VerifiedMemberPo record);

    /**
     * This method was generated by MyBatis Generator.
     * This method corresponds to the database table verified_member
     *
     * @mbg.generated
     */
    int updateByPrimaryKey(VerifiedMemberPo record);
}
