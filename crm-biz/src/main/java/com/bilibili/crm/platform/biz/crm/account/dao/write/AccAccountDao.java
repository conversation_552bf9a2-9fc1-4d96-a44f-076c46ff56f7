package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.po.AccAccountPoExample;
import com.bilibili.crm.platform.common.DbId;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AccAccountDao {
    long countByExample(AccAccountPoExample example);

    int deleteByExample(AccAccountPoExample example);

    int deleteByPrimaryKey(Integer accountId);

    int insertUpdate(AccAccountPo record);

    int insertBatch(List<AccAccountPo> records);

    int insertUpdateBatch(List<AccAccountPo> records);

    int insert(AccAccountPo record);

    int insertUpdateSelective(AccAccountPo record);

    int insertSelective(AccAccountPo record);

    List<AccAccountPo> selectByExample(AccAccountPoExample example);

    AccAccountPo selectByPrimaryKey(Integer accountId);

    int updateByExampleSelective(@Param("record") AccAccountPo record, @Param("example") AccAccountPoExample example);

    int updateByExample(@Param("record") AccAccountPo record, @Param("example") AccAccountPoExample example);

    int updateByPrimaryKeySelective(AccAccountPo record);

    int updateByPrimaryKey(AccAccountPo record);
}
