package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmKaBelongingPo;
import com.bilibili.crm.platform.biz.po.CrmKaBelongingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmKaBelongingDao {
    long countByExample(CrmKaBelongingPoExample example);

    int deleteByExample(CrmKaBelongingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmKaBelongingPo record);

    int insertBatch(List<CrmKaBelongingPo> records);

    int insertUpdateBatch(List<CrmKaBelongingPo> records);

    int insert(CrmKaBelongingPo record);

    int insertUpdateSelective(CrmKaBelongingPo record);

    int insertSelective(CrmKaBelongingPo record);

    List<CrmKaBelongingPo> selectByExample(CrmKaBelongingPoExample example);

    CrmKaBelongingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmKaBelongingPo record, @Param("example") CrmKaBelongingPoExample example);

    int updateByExample(@Param("record") CrmKaBelongingPo record, @Param("example") CrmKaBelongingPoExample example);

    int updateByPrimaryKeySelective(CrmKaBelongingPo record);

    int updateByPrimaryKey(CrmKaBelongingPo record);
}