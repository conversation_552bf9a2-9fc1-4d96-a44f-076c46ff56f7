package com.bilibili.crm.platform.biz.common;

import java.util.HashMap;
import java.util.Map;

import org.apache.commons.lang3.StringUtils;

public enum IntentEffect {
	FORM(1, "表单"),
    DOWNLOAD(2, "下载"),
    ACTIVE(3, "激活"),
    RECOVERY(4, "回收"),
    EXPOSURE(5, "曝光"),
    CLICK(6, "点击"),
    OTHER(7, "其他"),
    ;

    private Integer code;
    private String desc;
    
    private IntentEffect(Integer code, String desc) {
		this.code = code;
		this.desc = desc;
	}

    public Integer getCode() {
		return code;
	}

	private void setCode(Integer code) {
		this.code = code;
	}

	public String getDesc(String extraInfo) {
		if(this == OTHER && StringUtils.isNotBlank(extraInfo)) {
			return desc + ":" + extraInfo;
		}
		return desc;
	}

	private void setDesc(String desc) {
		this.desc = desc;
	}

	public static IntentEffect getByCode(int code) {
        for (IntentEffect bean : values()) {
            if (bean.getCode() == code) {
                return bean;
            }
        }
        throw new IllegalArgumentException("unknown code IntentEffect " + code);
    }
    
    public static Map<Integer, String> getCodeMapDesc() {
    	Map<Integer, String> map = new HashMap<>();
    	for (IntentEffect bean : values()) {
    		map.put(bean.getCode(), bean.getDesc(null));
    	}
    	
    	return map;
    }
    
    public static Map<String, Integer> getdescMapCode() {
    	Map<String, Integer> map = new HashMap<>();
    	for (IntentEffect bean : values()) {
    		map.put(bean.getDesc(null), bean.getCode());
    	}
    	
    	return map;
    }
}
