package com.bilibili.crm.platform.biz.dto;

import com.bilibili.crm.platform.common.BillSaleMappingSceneEnum;
import lombok.*;

import java.util.Date;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@EqualsAndHashCode
public class ContractBizSaleExtDto {

    private Long bizId;

    private Date validDate;

    private String operator;

    /**
     * 业务类型，0-批量配置业绩销售；1-客销关系；2-合同提成销售；3-专项PM；4-刷数；5-变更记录迁移；6-账单系统业绩拆分；7-账单手动业绩拆分
     * {@link BillSaleMappingSceneEnum}
     */
    private Integer bizType;
}
