package com.bilibili.crm.platform.biz.clickhouse.dao;

import com.bilibili.crm.platform.api.persona.dto.QueryNewTargetSummaryDTO;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmTargetCostNewLabelADPO;
import org.apache.ibatis.annotations.Param;

import java.util.List;

/**
 * ack客户分析，新客的接口
 *
 * <AUTHOR>
 * @date 2023/12/13
 */
public interface AdsCrmCustomerCostNewLabelADDao {

    /**
     * 查看分析周期内的分 品效花 年度和季度新客户
     */
    List<AdsCrmTargetCostNewLabelADPO> selectNewCustomerListForYearAndQuarterly(@Param("param") QueryNewTargetSummaryDTO param);

    Integer countNewCustomer(@Param("param") QueryNewTargetSummaryDTO param);

    List<AdsCrmTargetCostNewLabelADPO> pageQueryNewCustomer(@Param("param") QueryNewTargetSummaryDTO param);
}
