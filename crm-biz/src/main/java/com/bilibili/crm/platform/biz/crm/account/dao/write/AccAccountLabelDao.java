package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.AccAccountLabelPo;
import com.bilibili.crm.platform.biz.po.AccAccountLabelPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AccAccountLabelDao {
    long countByExample(AccAccountLabelPoExample example);

    int deleteByExample(AccAccountLabelPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AccAccountLabelPo record);

    int insertBatch(List<AccAccountLabelPo> records);

    int insertUpdateBatch(List<AccAccountLabelPo> records);

    int insert(AccAccountLabelPo record);

    int insertUpdateSelective(AccAccountLabelPo record);

    int insertSelective(AccAccountLabelPo record);

    List<AccAccountLabelPo> selectByExampleWithBLOBs(AccAccountLabelPoExample example);

    List<AccAccountLabelPo> selectByExample(AccAccountLabelPoExample example);

    AccAccountLabelPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccAccountLabelPo record, @Param("example") AccAccountLabelPoExample example);

    int updateByExampleWithBLOBs(@Param("record") AccAccountLabelPo record, @Param("example") AccAccountLabelPoExample example);

    int updateByExample(@Param("record") AccAccountLabelPo record, @Param("example") AccAccountLabelPoExample example);

    int updateByPrimaryKeySelective(AccAccountLabelPo record);

    int updateByPrimaryKeyWithBLOBs(AccAccountLabelPo record);

    int updateByPrimaryKey(AccAccountLabelPo record);
}