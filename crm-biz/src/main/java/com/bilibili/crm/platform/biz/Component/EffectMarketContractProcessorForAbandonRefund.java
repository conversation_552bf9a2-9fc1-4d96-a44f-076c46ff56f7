package com.bilibili.crm.platform.biz.Component;

import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.service.wallet.trade.AccountWalletTradeServiceAdapter;
import com.bilibili.crm.platform.common.WalletOperationType;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 营销中心合同废弃订单现金退还处理
 *
 * <AUTHOR>
 * @date 2020/12/30 11:13 上午
 */
@Component(value = "effectMarketContractProcessorForAbandonRefund")
public class EffectMarketContractProcessorForAbandonRefund extends AbstractEffectMarketContractProcessor {

    @Override
    protected void setWalletOperateType() {
        this.walletOperateType = WalletOperationType.CASH_REFUND.getType();
    }

    @Override
    protected void setIfUpdateOrder() {
        this.ifUpdateOrder = false;
    }

    @Override
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    protected void effectMarketContractOrderProcess(Integer orderId, Integer contractId, Long newFenAmount, Long originFenAmount, AccAccountPo accAccountPo) {
        // 进行现金退还操作
        //Long walletLogId = accountWalletService.refundForOrder(accAccountPo.getAccountId(), newFenAmount);
        //新入口
        Long walletLogId = tradeAdapter.refundForOrder(accAccountPo.getAccountId(), newFenAmount);

        if (walletLogId >= 0L) {
            // 保存订单现金退还记录
            crmOrderCashDeductRecordRepo.createOrderCashDeductRecord(accAccountPo.getAccountId(), orderId,
                    contractId, walletLogId, newFenAmount, WalletOperationType.getByCode(this.getWalletOperateType()));
        }
    }
}
