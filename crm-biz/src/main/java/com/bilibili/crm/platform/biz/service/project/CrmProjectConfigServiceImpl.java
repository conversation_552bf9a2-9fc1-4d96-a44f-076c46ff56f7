package com.bilibili.crm.platform.biz.service.project;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.project.ICrmProjectConfigService;
import com.bilibili.crm.platform.api.project.dto.ProjectInvestmentTypeDTO;
import com.bilibili.crm.platform.api.project.dto.ProjectPeriodicityConfigDTO;
import com.bilibili.crm.platform.api.project.dto.ProjectYearMoneyConfigDTO;
import com.bilibili.crm.platform.biz.dao.CrmPeriodicityProjectConfigDao;
import com.bilibili.crm.platform.biz.dao.CrmProjectTypeConfigDao;
import com.bilibili.crm.platform.biz.dao.CrmProjectYearMoneyConfigDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.api.project.enums.YearConfigType;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2023/10/16 11:53.
 * Contact: <EMAIL>.
 */
@Service
@Slf4j
public class CrmProjectConfigServiceImpl implements ICrmProjectConfigService {

    @Autowired
    private CrmProjectTypeConfigDao crmProjectTypeConfigDao;
    @Autowired
    private CrmProjectYearMoneyConfigDao crmProjectYearMoneyConfigDao;
    @Autowired
    private CrmPeriodicityProjectConfigDao crmPeriodicityProjectConfigDao;

    public ProjectInvestmentTypeDTO po2Dto(CrmProjectTypeConfigPo po){
        return ProjectInvestmentTypeDTO.builder()
                .ctime(po.getCtime())
                .mtime(po.getMtime())
                .id(po.getId())
                .typeName(po.getTypeName())
                .typeStatus(po.getTypeStatus())
                .typeParentId(po.getTypeParentId())
                .note(po.getNote()).build();
    }

    public ProjectYearMoneyConfigDTO po2Dto(CrmProjectYearMoneyConfigPo po) {
        return ProjectYearMoneyConfigDTO.builder()
                .ctime(po.getCtime())
                .mtime(po.getMtime())
                .id(po.getId())
                .yearName(po.getYearName())
                .yearMoney(po.getYearMoney())
                .build();
    }

    public ProjectPeriodicityConfigDTO po2Dto(CrmPeriodicityProjectConfigPo po) {
        return ProjectPeriodicityConfigDTO.builder()
                .ctime(po.getCtime())
                .mtime(po.getMtime())
                .id(po.getId())
                .projectStatus(po.getProjectStatus())
                .periodicityType(po.getPeriodicityType())
                .isDeleted(po.getIsDeleted())
                .note(po.getNote())
                .projectName(po.getProjectName())
                .periodicityBeginTime(po.getPeriodicityBeginTime())
                .periodicityEndTime(po.getPeriodicityEndTime())
                .build();
    }

    @Override
    public int addInvestmentTypeConfig(ProjectInvestmentTypeDTO dto) {
        Assert.notNull(dto, "ProjectInvestmentTypeDTO is null");
        return crmProjectTypeConfigDao.insertSelective(CrmProjectTypeConfigPo.builder()
                .typeName(dto.getTypeName())
                .typeStatus(dto.getTypeStatus())
                .typeParentId(dto.getTypeParentId())
                .note(dto.getNote()).build());
    }

    @Override
    public int updateInvestmentTypeConfig(ProjectInvestmentTypeDTO dto) {
        Assert.notNull(dto, "ProjectInvestmentTypeDTO  is null");
        Assert.notNull(dto.getId(), "ProjectInvestmentTypeDTO ID is null");
        CrmProjectTypeConfigPo updatePo = new CrmProjectTypeConfigPo();
        updatePo.setId(dto.getId());
        ObjectUtils.setString(dto::getTypeName, updatePo::setTypeName);
        ObjectUtils.setString(dto::getNote, updatePo::setNote);
        ObjectUtils.setInteger(dto::getTypeParentId, updatePo::setTypeParentId);
        return crmProjectTypeConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public int disableInvestmentTypeConfig(Long id) {
        Assert.notNull(id, "ID is null");
        CrmProjectTypeConfigPo updatePo = CrmProjectTypeConfigPo.builder().id(id).typeStatus(IsDeleted.DELETED.getCode()).build();
        return crmProjectTypeConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public int enableInvestmentTypeConfig(Long id) {
        Assert.notNull(id, "ID is null");
        CrmProjectTypeConfigPo updatePo = CrmProjectTypeConfigPo.builder().id(id).typeStatus(IsDeleted.VALID.getCode()).build();
        return crmProjectTypeConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public List<ProjectInvestmentTypeDTO> queryInvestmentTypeList(String typeNameLike,Integer typeStatus) {
        CrmProjectTypeConfigPoExample example = new CrmProjectTypeConfigPoExample();
        CrmProjectTypeConfigPoExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (StringUtils.isNotEmpty(typeNameLike)) {
            criteria.andTypeNameLike("%" + typeNameLike + "%");
        }
        if (typeStatus != null) {
            criteria.andTypeStatusEqualTo(typeStatus);
        }
        example.setOrderByClause("id desc");
        List<CrmProjectTypeConfigPo> poList = crmProjectTypeConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream().map(this::po2Dto).collect(Collectors.toList());
    }

    @Override
    public PageResult<ProjectInvestmentTypeDTO> queryInvestmentTypePage(String typeNameLike, Integer typeStatus, Integer page, Integer size) {
        CrmProjectTypeConfigPoExample example = new CrmProjectTypeConfigPoExample();
        CrmProjectTypeConfigPoExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (StringUtils.isNotEmpty(typeNameLike)) {
            criteria.andTypeNameLike("%" + typeNameLike + "%");
        }
        if (typeStatus != null) {
            criteria.andTypeStatusEqualTo(typeStatus);
        }
        Long total = crmProjectTypeConfigDao.countByExample(example);
        if (total == 0L) {
            return new PageResult<>(total.intValue(), Collections.emptyList());
        }
        example.setOrderByClause("id desc");
        example.setOffset((page - 1) * size);
        example.setLimit(size);
        List<CrmProjectTypeConfigPo> poList = crmProjectTypeConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return new PageResult<>(total.intValue(), Collections.emptyList());
        }
        return new PageResult<>(total.intValue(), poList.stream().map(this::po2Dto).collect(Collectors.toList()));
    }

    @Override
    public int addProjectYearMoneyConfig(ProjectYearMoneyConfigDTO dto) {
        Assert.notNull(dto, "ProjectYearMoneyConfigDTO is null");
        Assert.isTrue(Utils.isPositive(dto.getYearMoney()), "年度招商目标必须为正数");
        if(Objects.isNull(dto.getConfigType())){
            dto.setConfigType(YearConfigType.PROJECT.getCode());
        }
        CrmProjectYearMoneyConfigPoExample example = new CrmProjectYearMoneyConfigPoExample();
        example.createCriteria()
                .andYearNameEqualTo(dto.getYearName())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andConfigTypeEqualTo(dto.getConfigType());
        List<CrmProjectYearMoneyConfigPo> poList = crmProjectYearMoneyConfigDao.selectByExample(example);
        Assert.isTrue(CollectionUtils.isEmpty(poList), "年份不能与已有重复");
        return crmProjectYearMoneyConfigDao.insertSelective(CrmProjectYearMoneyConfigPo.builder()
                .yearName(dto.getYearName())
                .yearMoney(dto.getYearMoney())
                .configType(dto.getConfigType())
                .build());
    }

    @Override
    public int updateProjectYearMoneyConfig(ProjectYearMoneyConfigDTO dto) {
        Assert.notNull(dto, "ProjectYearMoneyConfigDTO is null");
        Assert.notNull(dto.getId(), "ProjectYearMoneyConfigDTO ID is null");
        Assert.isTrue(Utils.isPositive(dto.getYearMoney()), "年度招商目标必须为正数");
        CrmProjectYearMoneyConfigPo updatePo = new CrmProjectYearMoneyConfigPo();
        updatePo.setId(dto.getId());
        if(Objects.isNull(dto.getConfigType())){
            dto.setConfigType(YearConfigType.PROJECT.getCode());
        }
        updatePo.setConfigType(dto.getConfigType());
        ObjectUtils.setObject(dto::getYearMoney, updatePo::setYearMoney);
        return crmProjectYearMoneyConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public List<ProjectYearMoneyConfigDTO> queryProjectYearMoneyList(String yearName, Integer configType) {
        CrmProjectYearMoneyConfigPoExample example = new CrmProjectYearMoneyConfigPoExample();
        CrmProjectYearMoneyConfigPoExample.Criteria criteria = example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (StringUtils.isNotEmpty(yearName)) {
            criteria.andYearNameEqualTo(yearName);
        }
        if(Objects.nonNull(configType)){
            criteria.andConfigTypeEqualTo(configType);
        }
        example.setOrderByClause("year_name desc");
        List<CrmProjectYearMoneyConfigPo> poList = crmProjectYearMoneyConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream().map(this::po2Dto).collect(Collectors.toList());
    }

    @Override
    public int deleteProjectYearMoneyConfig(Long id) {
        Assert.notNull(id, "ID is null");
        return crmProjectYearMoneyConfigDao.deleteByPrimaryKey(id);
    }

    @Override
    public int addProjectPeriodicityConfig(ProjectPeriodicityConfigDTO dto) {
        Assert.notNull(dto, "ProjectPeriodicityConfigDTO is null");
        CrmPeriodicityProjectConfigPoExample example = new CrmPeriodicityProjectConfigPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andProjectNameEqualTo(dto.getProjectName());
        List<CrmPeriodicityProjectConfigPo> poList = crmPeriodicityProjectConfigDao.selectByExample(example);
        Assert.isTrue(poList.isEmpty(), "周期性项目类型名称已有重复");
        return crmPeriodicityProjectConfigDao.insertSelective(CrmPeriodicityProjectConfigPo.builder()
                .projectName(dto.getProjectName())
                .periodicityType(dto.getPeriodicityType())
                .note(dto.getNote())
                .periodicityBeginTime(dto.getPeriodicityBeginTime())
                .periodicityEndTime(dto.getPeriodicityEndTime())
                .build());
    }

    @Override
    public int updateProjectPeriodicityConfig(ProjectPeriodicityConfigDTO dto) {
        Assert.notNull(dto, "ProjectPeriodicityConfigDTO  is null");
        Assert.notNull(dto.getId(), "ProjectPeriodicityConfigDTO ID is null");
        CrmPeriodicityProjectConfigPo updatePo = new CrmPeriodicityProjectConfigPo();
        updatePo.setId(dto.getId());
        ObjectUtils.setString(dto::getProjectName, updatePo::setProjectName);
        ObjectUtils.setString(dto::getNote, updatePo::setNote);
        ObjectUtils.setString(dto::getPeriodicityType, updatePo::setPeriodicityType);
        ObjectUtils.setObject(dto::getPeriodicityBeginTime, updatePo::setPeriodicityBeginTime);
        ObjectUtils.setObject(dto::getPeriodicityEndTime, updatePo::setPeriodicityEndTime);
        CrmPeriodicityProjectConfigPoExample example = new CrmPeriodicityProjectConfigPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andProjectNameEqualTo(updatePo.getProjectName()).andIdNotEqualTo(updatePo.getId());
        List<CrmPeriodicityProjectConfigPo> poList = crmPeriodicityProjectConfigDao.selectByExample(example);
        Assert.isTrue(poList.isEmpty(), "周期性项目类型名称已有重复");
        return crmPeriodicityProjectConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public int disableProjectPeriodicityConfig(Long id) {
        Assert.notNull(id, "ID is null");
        CrmPeriodicityProjectConfigPo updatePo = CrmPeriodicityProjectConfigPo.builder().id(id).projectStatus(IsDeleted.DELETED.getCode()).build();
        return crmPeriodicityProjectConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public int enableProjectPeriodicityConfig(Long id) {
        Assert.notNull(id, "ID is null");
        CrmPeriodicityProjectConfigPo updatePo = CrmPeriodicityProjectConfigPo.builder().id(id).projectStatus(IsDeleted.VALID.getCode()).build();
        return crmPeriodicityProjectConfigDao.updateByPrimaryKeySelective(updatePo);
    }

    @Override
    public List<ProjectPeriodicityConfigDTO> queryProjectPeriodicityList(String periodicityType, String projectNameLike, Integer projectStatus) {
        CrmPeriodicityProjectConfigPoExample example = new CrmPeriodicityProjectConfigPoExample();
        CrmPeriodicityProjectConfigPoExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (StringUtils.isNotEmpty(projectNameLike)) {
            criteria.andProjectNameLike("%" + projectNameLike + "%");
        }
        if (StringUtils.isNotEmpty(periodicityType)) {
            criteria.andPeriodicityTypeEqualTo(periodicityType);
        }
        if (projectStatus != null) {
            criteria.andProjectStatusEqualTo(projectStatus);
        }
        example.setOrderByClause("id desc");
        List<CrmPeriodicityProjectConfigPo> poList = crmPeriodicityProjectConfigDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }
        return poList.stream().map(this::po2Dto).collect(Collectors.toList());
    }

}

