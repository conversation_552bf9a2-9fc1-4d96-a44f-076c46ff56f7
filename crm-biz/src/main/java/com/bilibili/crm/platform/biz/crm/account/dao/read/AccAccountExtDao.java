package com.bilibili.crm.platform.biz.crm.account.dao.read;

import com.bilibili.crm.platform.biz.po.AccAccountExtPo;
import com.bilibili.crm.platform.biz.po.AccAccountPoExample;
import com.bilibili.crm.platform.common.DbId;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/8
 */
public interface AccAccountExtDao {

    @Select("<script>" + "select customer_id as customerId, account_id as accountId,  dependency_agent_id as  dependencyAgentId from acc_account" +
            " where account_id in <foreach item = 'item' index = 'index' collection='accountIds' open='(' separator=',' close=')'> #{item} </foreach>" +
            " </script>")
    List<AccAccountExtPo> queryAccountId(@Param("accountIds") List<Integer> accountIds);

    DbId minMaxIdsByExample(AccAccountPoExample example);
}
