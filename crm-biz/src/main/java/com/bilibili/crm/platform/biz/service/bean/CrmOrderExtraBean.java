package com.bilibili.crm.platform.biz.service.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class CrmOrderExtraBean {

    private Integer crmOrderId;

    private Integer firstProductId;

    private Integer secondProductId;

    private String projectItemName;

    private Integer projectItemId;

    private Integer attractInvestmentType;

    /**
     * 一级产品型号
     */
    private String firstProductModel;

    /**
     * 二级产品型号
     */
    private String secondProductModel;

    private Long firstProductModelId;

    private Long secondProductModelId;

    @Deprecated
    private Integer productModelType;
}
