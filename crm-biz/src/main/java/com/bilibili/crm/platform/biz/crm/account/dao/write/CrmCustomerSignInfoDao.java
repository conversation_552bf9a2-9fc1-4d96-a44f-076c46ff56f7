package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCustomerSignInfoPo;
import com.bilibili.crm.platform.biz.po.CrmCustomerSignInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmCustomerSignInfoDao {
    long countByExample(CrmCustomerSignInfoPoExample example);

    int deleteByExample(CrmCustomerSignInfoPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmCustomerSignInfoPo record);

    int insertBatch(List<CrmCustomerSignInfoPo> records);

    int insertUpdateBatch(List<CrmCustomerSignInfoPo> records);

    int insert(CrmCustomerSignInfoPo record);

    int insertUpdateSelective(CrmCustomerSignInfoPo record);

    int insertSelective(CrmCustomerSignInfoPo record);

    List<CrmCustomerSignInfoPo> selectByExample(CrmCustomerSignInfoPoExample example);

    CrmCustomerSignInfoPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmCustomerSignInfoPo record, @Param("example") CrmCustomerSignInfoPoExample example);

    int updateByExample(@Param("record") CrmCustomerSignInfoPo record, @Param("example") CrmCustomerSignInfoPoExample example);

    int updateByPrimaryKeySelective(CrmCustomerSignInfoPo record);

    int updateByPrimaryKey(CrmCustomerSignInfoPo record);
}