package com.bilibili.crm.platform.biz.common;


import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;

@Component
public class CommonUtils {

    /**
     * 获取前一个月的最后一天
     *
     * @return
     */
    public static Timestamp getLastDayLastMouth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cale = Calendar.getInstance();
        cale.set(Calendar.DAY_OF_MONTH, 0);//设置为1号,当前日期既为本月第一天
        String lastDay = format.format(cale.getTime());
        Timestamp ts = null;
        try {
            ts = new Timestamp(format.parse(lastDay).getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ts;
    }

    /**
     * 获取前一个月的第一天
     *
     * @return
     */
    public static Timestamp getFirstDayLastMouth() {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //获取前月的第一天
        Calendar calendar = Calendar.getInstance();//获取当前日期
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
        String firstDay = format.format(calendar.getTime());
        Timestamp ts = null;
        try {
            ts = new Timestamp(format.parse(firstDay).getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ts;
    }

    /**
     * 将传入的时间戳转换为当前时间戳所指向的月份的上一个月的第一天
     *
     * @return
     */
    public static Timestamp getFirstDayLastMouth(long time) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        //获取前月的第一天
        Calendar calendar = Calendar.getInstance();//获取当前日期
        calendar.setTimeInMillis(time);
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);//设置为1号,当前日期既为本月第一天
        String firstDay = format.format(calendar.getTime());
        Timestamp ts = null;
        try {
            ts = new Timestamp(format.parse(firstDay).getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ts;
    }

    /**
     * 获取传入日期所在月份的最后一天
     *
     * @return
     */
    public static Timestamp getLastDayThisMouth(long fromtime) {
        SimpleDateFormat format = new SimpleDateFormat("yyyy-MM-dd");
        Calendar cale = Calendar.getInstance();
        cale.setTimeInMillis(fromtime);
//        cale.set(Calendar.DAY_OF_MONTH,0);//设置为1号,当前日期既为本月第一天
        cale.set(Calendar.DAY_OF_MONTH, cale.getActualMaximum(Calendar.DAY_OF_MONTH));
        String lastDay = format.format(cale.getTime());
        Timestamp ts = null;
        try {
            ts = new Timestamp(format.parse(lastDay).getTime());
        } catch (Exception e) {
            e.printStackTrace();
        }
        return ts;
    }

}
