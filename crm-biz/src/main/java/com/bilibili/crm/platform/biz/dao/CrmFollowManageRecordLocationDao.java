package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmFollowManageRecordLocationPo;
import com.bilibili.crm.platform.biz.po.CrmFollowManageRecordLocationPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmFollowManageRecordLocationDao {
    long countByExample(CrmFollowManageRecordLocationPoExample example);

    int deleteByExample(CrmFollowManageRecordLocationPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmFollowManageRecordLocationPo record);

    int insertBatch(List<CrmFollowManageRecordLocationPo> records);

    int insertUpdateBatch(List<CrmFollowManageRecordLocationPo> records);

    int insert(CrmFollowManageRecordLocationPo record);

    int insertUpdateSelective(CrmFollowManageRecordLocationPo record);

    int insertSelective(CrmFollowManageRecordLocationPo record);

    List<CrmFollowManageRecordLocationPo> selectByExample(CrmFollowManageRecordLocationPoExample example);

    CrmFollowManageRecordLocationPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmFollowManageRecordLocationPo record, @Param("example") CrmFollowManageRecordLocationPoExample example);

    int updateByExample(@Param("record") CrmFollowManageRecordLocationPo record, @Param("example") CrmFollowManageRecordLocationPoExample example);

    int updateByPrimaryKeySelective(CrmFollowManageRecordLocationPo record);

    int updateByPrimaryKey(CrmFollowManageRecordLocationPo record);
}