package com.bilibili.crm.platform.biz.service.account.label;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.accountlabel.*;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.biz.Component.attachment.AttachmentUploadFacade;
import com.bilibili.crm.platform.biz.Component.upload.AsyncTaskComponent;
import com.bilibili.crm.platform.biz.bo.accountlabel.AccountLabelBindFailExcelBo;
import com.bilibili.crm.platform.biz.boss.service.BossFileService;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPo;
import com.bilibili.crm.platform.biz.po.AccAccountLabelPo;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.po.CrmCommonnAttachmentPo;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.repo.CrmCommonAttachmentRepo;
import com.bilibili.crm.platform.biz.repo.account_label.AccountLabelRepo;
import com.bilibili.crm.platform.biz.service.LogOperatorService;
import com.bilibili.crm.platform.biz.util.FileUtils;
import com.bilibili.crm.platform.common.AttachmentUploadBizModuleEnum;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.Module;
import com.bilibili.crm.platform.common.UploadAttachmentType;
import com.bilibili.crm.platform.common.account.label.AccountLabelBatchOptType;
import com.bilibili.crm.platform.common.account.label.AccountLabelMappingType;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.File;
import java.util.*;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * 账号标签批量上传处理
 *
 * <AUTHOR>
 * @date 2022/7/30
 */
@Component
@Slf4j
public class AccountLabelBatchUploadProc {

    /**
     * 限制的最大条数
     */
    @Value("${accountLabel.batchUpload.maxCount:1000}")
    public int MAX_OPT_COUNT;
    /**
     * 保存一批的大小
     */
    public static final int PARTITION_SIZE = 100;

    @Autowired
    private AccAccountRepo accAccountRepo;
    @Autowired
    private AccountLabelRepo accountLabelRepo;
    @Autowired
    private AccountLabelExcelParser accountLabelExcelParser;
    @Autowired
    private CrmCommonAttachmentRepo crmCommonAttachmentRepo;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;
    @Autowired
    private AttachmentUploadFacade attachmentUploadFacade;
    @Autowired
    private LogOperatorService logOperatorService;
    @Autowired
    private RedisLock redisLock;

    @Resource
    private BossFileService bossFileService;

    @Resource
    private AsyncTaskComponent asyncTaskComponent;


    /**
     * 批量上传账号标签
     *
     * @param uploadBindDto
     * @return
     */
    public String batchUploadAccountLabels(AccountLabelBatchUploadBindDto uploadBindDto) {
        log.info("=====> batchUploadAccountLabels, uploadBindDto:{}", JSON.toJSONString(uploadBindDto));
        // 读取 excel 到 dto
        List<AccountLabelExcelItemDto> excelItemDtos = accountLabelExcelParser.parseExcel(uploadBindDto.getUploadDto());

        // 并发控制 key 操作人:文件名
        String result = "";
        String key = uploadBindDto.getOperator().getOperatorName() + ":" + uploadBindDto.getUploadDto().getFileName();
        redisLock.getLock(key, 3000);
        try {
            result = batchBindAccountLabels(uploadBindDto, excelItemDtos);
        } catch (Exception e) {
            log.error("batchUploadAccountLabels error", e);
            throw e;
        } finally {
            redisLock.releaseLock(key);
        }
        return result;
    }

    public AccountLabelBatchUploadBindResp newBatchUploadAccountLabels(AccountLabelBatchUploadBindDto uploadBindDto) {
        log.info("=====> batchUploadAccountLabels, uploadBindDto:{}", JSON.toJSONString(uploadBindDto));
        // 读取 excel 到 dto
        List<AccountLabelExcelItemDto> excelItemDtos = accountLabelExcelParser.parseExcel(uploadBindDto.getUploadDto());

        // 并发控制 key 操作人:文件名
        AccountLabelBatchUploadBindResp result;
        String key = uploadBindDto.getOperator().getOperatorName() + ":" + uploadBindDto.getUploadDto().getFileName();
        redisLock.getLock(key, 3000);
        try {
            result = newBatchBindAccountLabels(uploadBindDto, excelItemDtos);
        } catch (Exception e) {
            log.error("batchUploadAccountLabels error", e);
            throw e;
        } finally {
            redisLock.releaseLock(key);
        }
        return result;
    }

    /**
     * 批量绑定账号标签
     * 1. 过滤无效数据
     * 2. 切分细粒度
     * 3. ids 存在性检查
     * 4. 获取账号已有所有的标签
     * 5. 过滤新增已有的，删除不存在的情况，去重
     * 6. 保存
     *
     * @param uploadBindDto
     * @param excelItemDtos
     * @return
     */
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    public String batchBindAccountLabels(AccountLabelBatchUploadBindDto uploadBindDto,
                                         List<AccountLabelExcelItemDto> excelItemDtos) {
        log.info("=====> batchBindAccountLabels, excel rows:{}", excelItemDtos.size());

        // 过滤无效数据
        List<AccountLabelExcelItemDto> itemDtoListOfFilter = filterNullData(excelItemDtos);
        Cat.logEvent("filterNullData", "filterNullData", Event.SUCCESS, String.format("过滤前size=%s,过滤后size=%s",
                excelItemDtos.size(), itemDtoListOfFilter.size()));
        if (CollectionUtils.isEmpty(itemDtoListOfFilter)) {
            throw new ServiceRuntimeException("文档不能为空，请修改后再上传");
        }

        // 切分数据为最细粒度
        List<AccountLabelBatchBindItemDto> splitedBindItemDtos = spliceData(itemDtoListOfFilter);
        log.info("=====> batchBindAccountLabels, 切分之后 rows:{}", splitedBindItemDtos.size());
        Cat.logEvent("spliceData", "spliceData", Event.SUCCESS, String.format("切分前size=%s,切分后size=%s",
                itemDtoListOfFilter.size(), splitedBindItemDtos.size()));

        if (CollectionUtils.isEmpty(splitedBindItemDtos)) {
            throw new ServiceRuntimeException("文档不能为空，请修改后再上传");
        }

        // 将需要处理的按账号 id 分组
        Map<Integer, List<AccountLabelBatchBindItemDto>> accountBindItemsMapByAccountId = splitedBindItemDtos.stream().collect(Collectors.groupingBy(t -> t.getAccountId()));

        // ids 存在性校验
        List<Integer> allAccountIds = validateIdIfExist(splitedBindItemDtos);

        // 获取账号的所有的标签
        List<AccAccountLabelMappingPo> mappingPos = accountLabelRepo.queryAccountLabelMappingListByAccountIds(allAccountIds);
        // 按账号, 标签类型, mappings 分组
        Map<Integer, Map<Integer, List<AccAccountLabelMappingPo>>> accountAndTypeMappingsMap = mappingPos.stream().collect(Collectors.groupingBy(mappingPo -> mappingPo.getAccountId(),
                Collectors.groupingBy(t -> t.getMappingType())));

        // 构建最终需要处理的账号标签 items
        List<AccountLabelBatchBindItemDto> finalBatchBindItemDtos = buildNeedOptAccountLabelItemDtos(accountBindItemsMapByAccountId, accountAndTypeMappingsMap);
        log.info("=====> batchBindAccountLabels, filter after rows:{}", finalBatchBindItemDtos.size());
        if (CollectionUtils.isEmpty(finalBatchBindItemDtos)) {
            return "update count:0";
        }
        if (finalBatchBindItemDtos.size() > MAX_OPT_COUNT) {
            throw new ServiceRuntimeException("不允许超过" + MAX_OPT_COUNT + "行");
        }

        // 合并新增与删除的数据，然后批量保存
        return mergeDataThenBatchAdd(uploadBindDto, accountAndTypeMappingsMap,
                finalBatchBindItemDtos);
    }

    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    public AccountLabelBatchUploadBindResp newBatchBindAccountLabels(AccountLabelBatchUploadBindDto uploadBindDto,
                                                                     List<AccountLabelExcelItemDto> excelItemDtos) {
        log.info("=====> batchBindAccountLabels, excel rows:{}", excelItemDtos.size());
        log.info("splitedBindItemDtos = {}", JSON.toJSONString(excelItemDtos));

        // 过滤无效数据
        List<AccountLabelExcelItemDto> itemDtoListOfFilter = filterNullData(excelItemDtos);
        Cat.logEvent("filterNullData", "filterNullData", Event.SUCCESS, String.format("过滤前size=%s,过滤后size=%s",
                excelItemDtos.size(), itemDtoListOfFilter.size()));
        if (CollectionUtils.isEmpty(itemDtoListOfFilter)) {
            throw new ServiceRuntimeException("文档不能为空，请修改后再上传");
        }

        // 切分数据为最细粒度
        List<AccountLabelBatchBindItemDto> splitedBindItemDtos = spliceData(itemDtoListOfFilter);
        log.info("=====> batchBindAccountLabels, 切分之后 rows:{}", splitedBindItemDtos.size());
        Cat.logEvent("spliceData", "spliceData", Event.SUCCESS, String.format("切分前size=%s,切分后size=%s",
                itemDtoListOfFilter.size(), splitedBindItemDtos.size()));

        if (CollectionUtils.isEmpty(splitedBindItemDtos)) {
            throw new ServiceRuntimeException("文档不能为空，请修改后再上传");
        }

        // 将需要处理的按账号 id 分组
        Map<Integer, List<AccountLabelBatchBindItemDto>> accountBindItemsMapByAccountId = splitedBindItemDtos.stream().collect(Collectors.groupingBy(AccountLabelBatchBindItemDto::getAccountId));

        // ids 存在性校验
        List<AccountLabelBindFailExcelBo> failExcelDtos = new ArrayList<>();
        Pair<List<Integer>, List<Integer>> pair = newValidateIdIfExist(splitedBindItemDtos, failExcelDtos, accountBindItemsMapByAccountId);
        AccountLabelBatchUploadBindResp result = new AccountLabelBatchUploadBindResp();
        if (CollectionUtils.isEmpty(pair.getLeft()) || CollectionUtils.isEmpty(pair.getRight())) {
            String taskId = generateUploadFileTask(failExcelDtos);
            result.setTaskId(taskId);
            result.setFailedCounts(failExcelDtos.size());
            return result;
        }

        // 获取账号的所有的标签
        List<AccAccountLabelMappingPo> mappingPos = accountLabelRepo.queryAccountLabelMappingListByAccountIds(pair.getLeft());
        // 按账号, 标签类型, mappings 分组
        Map<Integer, Map<Integer, List<AccAccountLabelMappingPo>>> accountAndTypeMappingsMap = mappingPos.stream().collect(Collectors.groupingBy(AccAccountLabelMappingPo::getAccountId,
                Collectors.groupingBy(AccAccountLabelMappingPo::getMappingType)));

        // 构建最终需要处理的账号标签 items
        List<AccountLabelBatchBindItemDto> finalBatchBindItemDtos = newBuildNeedOptAccountLabelItemDtos(accountBindItemsMapByAccountId, accountAndTypeMappingsMap, failExcelDtos);
        if (!CollectionUtils.isEmpty(failExcelDtos)) {
            String taskId = generateUploadFileTask(failExcelDtos);
            result.setTaskId(taskId);
            result.setFailedCounts(failExcelDtos.size());
            return result;
        }


        log.info("=====> batchBindAccountLabels, filter after rows:{}", finalBatchBindItemDtos.size());
        if (CollectionUtils.isEmpty(finalBatchBindItemDtos)) {
            return AccountLabelBatchUploadBindResp.builder().allDeleteCounts(0).allAddCounts(0).build();
        }
        if (finalBatchBindItemDtos.size() > MAX_OPT_COUNT) {
            throw new ServiceRuntimeException("不允许超过" + MAX_OPT_COUNT + "行");
        }

        // 合并新增与删除的数据，然后批量保存
        return newMergeDataThenBatchAdd(uploadBindDto, accountAndTypeMappingsMap,
                finalBatchBindItemDtos);
    }

    private List<Integer> validateIdIfExist(List<AccountLabelBatchBindItemDto> batchBindItemDtos) {
        List<Integer> allAccountIds = batchBindItemDtos.stream()
                .map(AccountLabelBatchBindItemDto::getAccountId).distinct().collect(Collectors.toList());
        List<Integer> allLabelIds = batchBindItemDtos.stream()
                .map(AccountLabelBatchBindItemDto::getLabelId).distinct().collect(Collectors.toList());

        // 存在的 ids
        List<AccAccountPo> accAccountPos = accAccountRepo.queryAccountListByIds(allAccountIds);
        Set<Integer> existAccountIdSet = accAccountPos.stream().map(t -> t.getAccountId()).collect(Collectors.toSet());
        List<AccAccountLabelPo> labelPos = accountLabelRepo.queryAccountLabelListByLabelIds(allLabelIds);
        Set<Integer> existLabelIdSet = labelPos.stream().map(t -> t.getId()).collect(Collectors.toSet());

        // 不存在的 ids
        List<Integer> notExistAccountIds = allAccountIds.stream().filter(t -> !existAccountIdSet.contains(t)).collect(Collectors.toList());
        List<Integer> notExistLabelIds = allLabelIds.stream().filter(t -> !existLabelIdSet.contains(t)).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(notExistAccountIds)) {
            String errMsg = String.format("账号ID:%s不存在，请修改后再上传", notExistAccountIds.stream().map(t -> String.valueOf(t)).collect(Collectors.joining(",")));
            throw new ServiceRuntimeException(errMsg);
        }
        if (!CollectionUtils.isEmpty(notExistLabelIds)) {
            String errMsg = String.format("标签ID:%s不存在，请修改后再上传", notExistLabelIds.stream().map(t -> String.valueOf(t)).collect(Collectors.joining(",")));
            throw new ServiceRuntimeException(errMsg);
        }
        return allAccountIds;
    }

    private Pair<List<Integer>, List<Integer>> newValidateIdIfExist(List<AccountLabelBatchBindItemDto> batchBindItemDtos,
                                                                    List<AccountLabelBindFailExcelBo> failExcelDtos,
                                                                    Map<Integer, List<AccountLabelBatchBindItemDto>> accountLabelMap) {
        List<Integer> allAccountIds = batchBindItemDtos.stream()
                .map(AccountLabelBatchBindItemDto::getAccountId).distinct().collect(Collectors.toList());
        List<Integer> allLabelIds = batchBindItemDtos.stream()
                .map(AccountLabelBatchBindItemDto::getLabelId).distinct().collect(Collectors.toList());

        // 存在的 ids
        List<AccAccountPo> accAccountPos = accAccountRepo.queryAccountListByIds(allAccountIds);
        Set<Integer> existAccountIdSet = accAccountPos.stream().map(t -> t.getAccountId()).collect(Collectors.toSet());
        List<AccAccountLabelPo> labelPos = accountLabelRepo.queryAccountLabelListByLabelIds(allLabelIds);
        Set<Integer> existLabelIdSet = labelPos.stream().map(t -> t.getId()).collect(Collectors.toSet());

        // 不存在的 ids
        List<Integer> notExistAccountIds = allAccountIds.stream().filter(t -> !existAccountIdSet.contains(t)).collect(Collectors.toList());
        List<Integer> notExistLabelIds = allLabelIds.stream().filter(t -> !existLabelIdSet.contains(t)).collect(Collectors.toList());
        Set<String> accountLabelStr = new HashSet<>();
        if (!CollectionUtils.isEmpty(notExistAccountIds)) {
            notExistAccountIds.forEach(accountId -> {
                String failReason = "账号不存在";
                List<AccountLabelBatchBindItemDto> binds = accountLabelMap.get(accountId);
                binds.forEach(bind -> {
                    failExcelDtos.add(buildExcelFailBo(failReason, accountId, bind.getLabelId(), bind.getOperateType()));
                    accountLabelStr.add(accountId + "_" + bind.getLabelId());
                });
            });
        }

        Map<Integer, List<AccountLabelBatchBindItemDto>> labelMap = batchBindItemDtos.stream().collect(Collectors.groupingBy(AccountLabelBatchBindItemDto::getLabelId));
        if (!CollectionUtils.isEmpty(notExistLabelIds)) {
            notExistLabelIds.forEach(labelId -> {
                List<AccountLabelBatchBindItemDto> binds = labelMap.get(labelId);
                binds.forEach(bind -> {
                    if (!accountLabelStr.contains(bind.getAccountId() + "_" + labelId)) {
                        String failReason = "标签不存在";
                        failExcelDtos.add(buildExcelFailBo(failReason, bind.getAccountId(), labelId, bind.getOperateType()));
                    }
                });
            });
        }
        allAccountIds.removeAll(notExistAccountIds);
        allLabelIds.removeAll(notExistLabelIds);
        return Pair.of(allAccountIds, allLabelIds);
    }

    private AccountLabelBindFailExcelBo buildExcelFailBo(String reason, Integer accountId, Integer labelId, Integer operatorType) {
        AccountLabelBindFailExcelBo result = new AccountLabelBindFailExcelBo();
        result.setFailedReason(reason);
        result.setAccountId(accountId);
        result.setLabelId(labelId);
        result.setType(operatorType == 1 ? "新增" : "删除");
        return result;
    }

    /**
     * 过滤掉空数据
     *
     * @param excelItemDtos
     * @return
     */
    private List<AccountLabelExcelItemDto> filterNullData(List<AccountLabelExcelItemDto> excelItemDtos) {
        return excelItemDtos.stream()
                // 都不为空的
                .filter(itemDto -> !(StringUtils.isEmpty(itemDto.getAccountIdsStr()) || StringUtils.isEmpty(itemDto.getLabelIdsStr())))
                .collect(Collectors.toList());
    }


    /**
     * 合并新增与删除的数据，然后批量保存
     *
     * @param accountAndTypeMappingsMap
     * @param batchBindItemDtosOk
     * @return
     */
    private String mergeDataThenBatchAdd(AccountLabelBatchUploadBindDto uploadBindDto, Map<Integer, Map<Integer,
            List<AccAccountLabelMappingPo>>> accountAndTypeMappingsMap, List<AccountLabelBatchBindItemDto> batchBindItemDtosOk) {
        // 标签批量绑定删除处理
        Map<Integer, List<AccountLabelBatchBindItemDto>> bindItemMapByAccount = batchBindItemDtosOk.stream().collect(Collectors.groupingBy(t -> t.getAccountId()));
        Iterator<Map.Entry<Integer, List<AccountLabelBatchBindItemDto>>> iterator = bindItemMapByAccount.entrySet().iterator();

        // 最终要保存的(原有的+新增+删除)
        List<AccAccountLabelMappingPo> finalSaveMappingsWithOrigin = new ArrayList<>();
        // 最终要保存的(新增+删除)，不包含原有的
        List<AccAccountLabelMappingPo> finalSaveMappingsWithoutOrigin = new ArrayList<>();
        List<AccAccountLabelMappingPo> needAddPos = new ArrayList<>();
        List<AccAccountLabelMappingPo> needDeletePos = new ArrayList<>();

        Integer needDeleteCount = 0;
        Integer needAddCount = 0;

        // 保存附件
        Integer attachmentId = saveAttachment(uploadBindDto);

        // 迭代处理
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<AccountLabelBatchBindItemDto>> next = iterator.next();
            Integer accountId = next.getKey();
            Map<Integer, List<AccountLabelBatchBindItemDto>> bindItemByOptType = next.getValue().stream().collect(Collectors.groupingBy(t -> t.getOperateType()));

            // 需要新增与删除的
            List<AccountLabelBatchBindItemDto> addItems = bindItemByOptType.getOrDefault(AccountLabelBatchOptType.ADD.getCode(),
                    Collections.EMPTY_LIST);
            List<AccountLabelBatchBindItemDto> deleteItems = bindItemByOptType.getOrDefault(AccountLabelBatchOptType.DELETE.getCode(),
                    Collections.EMPTY_LIST);
            // 既没有要新增也没有删除
            if (CollectionUtils.isEmpty(addItems) && CollectionUtils.isEmpty(deleteItems)) {
                continue;
            }

            Set<Integer> addLabelIdSet = addItems.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());
            Set<Integer> deleteLabelIdSet = deleteItems.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());

            // 如果新增 = 删除，则跳过
            if (addLabelIdSet.containsAll(deleteLabelIdSet) && deleteLabelIdSet.containsAll(addLabelIdSet)) {
                Cat.logEvent("mergeDataThenBatchAdd", "新增=删除,过滤", Event.SUCCESS, String.format(
                        "accountId=%s,addLabelIdSet=%s,deleteLabelIdSet=%s", accountId, JSON.toJSONString(addLabelIdSet), JSON.toJSONString(deleteLabelIdSet)));
                continue;
            }
            needDeleteCount += deleteLabelIdSet.size();
            needAddCount += addLabelIdSet.size();

            // 获取账号的原有手动标签
            Map<Integer, List<AccAccountLabelMappingPo>> originMappingPosOfAccount = accountAndTypeMappingsMap.getOrDefault(accountId, new HashMap<>());

            List<AccAccountLabelMappingPo> originManualMappingPosOfAccount = originMappingPosOfAccount.getOrDefault(AccountLabelMappingType.MANUAL.getCode(), new ArrayList<>());
            List<AccAccountLabelMappingPo> originManualMappingOfAccountCopy = originManualMappingPosOfAccount.stream().collect(Collectors.toList());

            // 处理需要删除的部分
            originManualMappingPosOfAccount.stream().forEach(t -> {
                // 需要删除的，直接修改 isDeleted
                if (deleteLabelIdSet.contains(t.getLabelId())) {
                    t.setIsDeleted(IsDeleted.DELETED.getCode());
                    needDeletePos.add(t);
                }
            });

            // 处理需要新增的
            needAddPos = addItems.stream().map(t -> AccAccountLabelMappingPo.builder()
                    .accountId(accountId)
                    .labelId(t.getLabelId())
                    .mappingType(AccountLabelMappingType.MANUAL.getCode())
                    .ctime(Utils.getNow())
                    .mtime(Utils.getNow())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .build()).collect(Collectors.toList());
            // 最终标签: 原有标签 - 需删除的 + 新增的
            originManualMappingPosOfAccount.addAll(needAddPos);
            finalSaveMappingsWithOrigin.addAll(originManualMappingPosOfAccount);

            // 新增 + 删除的(原有的软删)
            finalSaveMappingsWithoutOrigin.addAll(needAddPos);
            finalSaveMappingsWithoutOrigin.addAll(needDeletePos);

            List<Integer> originManualLabelIds = originManualMappingOfAccountCopy.stream().map(t -> t.getLabelId()).collect(Collectors.toList());
            // 过滤出删除之后的(即新增的)，包含原有的
            List<Integer> finalManualLabelIdsWithOrigin = originManualMappingPosOfAccount.stream()
                    .filter(t -> IsDeleted.VALID.getCode() == t.getIsDeleted())
                    .map(t -> t.getLabelId()).collect(Collectors.toList());

            // 添加操作日志
            addOptLog(uploadBindDto, attachmentId, accountId, originManualLabelIds, finalManualLabelIdsWithOrigin);
        }

        // 新增 + 删除的(原有的软删) 批量保存
        List<List<AccAccountLabelMappingPo>> partitions = Lists.partition(finalSaveMappingsWithoutOrigin, PARTITION_SIZE);
        for (List<AccAccountLabelMappingPo> partition : partitions) {
            accountLabelRepo.batchAddAccountLabelMappings(partition);
        }

        String result = String.format("需要删除count:%s,需要新增count:%s,db实际删除count:%s,db实际新增count:%s", needDeleteCount, needAddCount,
                needDeletePos.size(), needAddPos.size());
        return result;
    }

    private AccountLabelBatchUploadBindResp newMergeDataThenBatchAdd(AccountLabelBatchUploadBindDto uploadBindDto, Map<Integer, Map<Integer,
            List<AccAccountLabelMappingPo>>> accountAndTypeMappingsMap, List<AccountLabelBatchBindItemDto> batchBindItemDtosOk) {
        // 标签批量绑定删除处理
        Map<Integer, List<AccountLabelBatchBindItemDto>> bindItemMapByAccount = batchBindItemDtosOk.stream().collect(Collectors.groupingBy(t -> t.getAccountId()));
        Iterator<Map.Entry<Integer, List<AccountLabelBatchBindItemDto>>> iterator = bindItemMapByAccount.entrySet().iterator();

        // 最终要保存的(原有的+新增+删除)
        List<AccAccountLabelMappingPo> finalSaveMappingsWithOrigin = new ArrayList<>();
        // 最终要保存的(新增+删除)，不包含原有的
        List<AccAccountLabelMappingPo> finalSaveMappingsWithoutOrigin = new ArrayList<>();
        List<AccAccountLabelMappingPo> needAddPos = new ArrayList<>();
        List<AccAccountLabelMappingPo> needDeletePos = new ArrayList<>();

        Integer needDeleteCount = 0;
        Integer needAddCount = 0;

        // 保存附件
        Integer attachmentId = saveAttachment(uploadBindDto);

        // 迭代处理
        while (iterator.hasNext()) {
            Map.Entry<Integer, List<AccountLabelBatchBindItemDto>> next = iterator.next();
            Integer accountId = next.getKey();
            Map<Integer, List<AccountLabelBatchBindItemDto>> bindItemByOptType = next.getValue().stream().collect(Collectors.groupingBy(t -> t.getOperateType()));

            // 需要新增与删除的
            List<AccountLabelBatchBindItemDto> addItems = bindItemByOptType.getOrDefault(AccountLabelBatchOptType.ADD.getCode(),
                    Collections.EMPTY_LIST);
            List<AccountLabelBatchBindItemDto> deleteItems = bindItemByOptType.getOrDefault(AccountLabelBatchOptType.DELETE.getCode(),
                    Collections.EMPTY_LIST);
            // 既没有要新增也没有删除
            if (CollectionUtils.isEmpty(addItems) && CollectionUtils.isEmpty(deleteItems)) {
                continue;
            }

            Set<Integer> addLabelIdSet = addItems.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());
            Set<Integer> deleteLabelIdSet = deleteItems.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());

            // 如果新增 = 删除，则跳过
            if (addLabelIdSet.containsAll(deleteLabelIdSet) && deleteLabelIdSet.containsAll(addLabelIdSet)) {
                Cat.logEvent("mergeDataThenBatchAdd", "新增=删除,过滤", Event.SUCCESS, String.format(
                        "accountId=%s,addLabelIdSet=%s,deleteLabelIdSet=%s", accountId, JSON.toJSONString(addLabelIdSet), JSON.toJSONString(deleteLabelIdSet)));
                continue;
            }
            needDeleteCount += deleteLabelIdSet.size();
            needAddCount += addLabelIdSet.size();

            // 获取账号的原有手动标签
            Map<Integer, List<AccAccountLabelMappingPo>> originMappingPosOfAccount = accountAndTypeMappingsMap.getOrDefault(accountId, new HashMap<>());

            List<AccAccountLabelMappingPo> originManualMappingPosOfAccount = originMappingPosOfAccount.getOrDefault(AccountLabelMappingType.MANUAL.getCode(), new ArrayList<>());
            List<AccAccountLabelMappingPo> originManualMappingOfAccountCopy = originManualMappingPosOfAccount.stream().collect(Collectors.toList());

            // 处理需要删除的部分
            originManualMappingPosOfAccount.stream().forEach(t -> {
                // 需要删除的，直接修改 isDeleted
                if (deleteLabelIdSet.contains(t.getLabelId())) {
                    t.setIsDeleted(IsDeleted.DELETED.getCode());
                    needDeletePos.add(t);
                }
            });

            // 处理需要新增的
            needAddPos = addItems.stream().map(t -> AccAccountLabelMappingPo.builder()
                    .accountId(accountId)
                    .labelId(t.getLabelId())
                    .mappingType(AccountLabelMappingType.MANUAL.getCode())
                    .ctime(Utils.getNow())
                    .mtime(Utils.getNow())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .build()).collect(Collectors.toList());
            // 最终标签: 原有标签 - 需删除的 + 新增的
            originManualMappingPosOfAccount.addAll(needAddPos);
            finalSaveMappingsWithOrigin.addAll(originManualMappingPosOfAccount);

            // 新增 + 删除的(原有的软删)
            finalSaveMappingsWithoutOrigin.addAll(needAddPos);
            finalSaveMappingsWithoutOrigin.addAll(needDeletePos);

            List<Integer> originManualLabelIds = originManualMappingOfAccountCopy.stream().map(t -> t.getLabelId()).collect(Collectors.toList());
            // 过滤出删除之后的(即新增的)，包含原有的
            List<Integer> finalManualLabelIdsWithOrigin = originManualMappingPosOfAccount.stream()
                    .filter(t -> IsDeleted.VALID.getCode() == t.getIsDeleted())
                    .map(t -> t.getLabelId()).collect(Collectors.toList());

            // 添加操作日志
            addOptLog(uploadBindDto, attachmentId, accountId, originManualLabelIds, finalManualLabelIdsWithOrigin);
        }

        // 新增 + 删除的(原有的软删) 批量保存
        List<List<AccAccountLabelMappingPo>> partitions = Lists.partition(finalSaveMappingsWithoutOrigin, PARTITION_SIZE);
        for (List<AccAccountLabelMappingPo> partition : partitions) {
            accountLabelRepo.batchAddAccountLabelMappings(partition);
        }

        return AccountLabelBatchUploadBindResp.builder().allAddCounts(needAddCount).allDeleteCounts(needDeleteCount)
                .successDeleteCounts(needDeletePos.size())
                .successAddCounts(needAddPos.size()).build();
    }

    private void addOptLog(AccountLabelBatchUploadBindDto uploadBindDto, Integer attachmentId, Integer accountId, List<Integer> originManualLabelIds, List<Integer> finalManualLabelIdsWithOrigin) {
        // 不会改动自动标签
        AccountLabelBindingDto oldBindingDto = new AccountLabelBindingDto();
        // 原有的手动标签
        oldBindingDto.setManualMappingLabelIds(originManualLabelIds);
        AccountLabelBindingDto newBindingDto = new AccountLabelBindingDto();
        // 最终的手动标签
        newBindingDto.setManualMappingLabelIds(finalManualLabelIdsWithOrigin);
        newBindingDto.setAccountId(accountId);
        saveAccountLabelBindingLog(uploadBindDto.getOperator(), oldBindingDto, newBindingDto,
                ModifyType.ACCOUNT_LABEL_MANUALLY_BATCH_UPDATE, attachmentId);
    }

    private Integer saveAttachment(AccountLabelBatchUploadBindDto uploadBindDto) {
        CrmCommonnAttachmentPo attachmentPo = new CrmCommonnAttachmentPo();
        attachmentPo.setBizModule(AttachmentUploadBizModuleEnum.ACCOUNT_LABEL_UPLOAD.getModuleCode());
        attachmentPo.setSubBizType(AttachmentUploadBizModuleEnum.ACCOUNT_LABEL_UPLOAD.getSubBizCode());
        attachmentPo.setRefId(0);
        attachmentPo.setUploadType(UploadAttachmentType.OSS.getCode());
        attachmentPo.setOssKey(uploadBindDto.getUploadDto().getOssKey());
        attachmentPo.setFileName(uploadBindDto.getUploadDto().getFileName());
        attachmentPo.setUrl(uploadBindDto.getUploadDto().getUrl());
        attachmentPo.setNumber(snowflakeIdWorker.nextId());
        crmCommonAttachmentRepo.addSelective(attachmentPo);
        return attachmentPo.getId();
    }

    /**
     * 保存日志
     *
     * @param operator
     * @param oldInfo
     * @param newInfo
     * @param modifyType
     * @param attachmentId 批量上传附件的 id
     */
    private void saveAccountLabelBindingLog(Operator operator, AccountLabelBindingDto oldInfo,
                                            AccountLabelBindingDto newInfo, ModifyType modifyType,
                                            Integer attachmentId) {
        AccountLabelBindingLog bindingLog = AccountLabelBindingLog.builder().build();

        // 自动标签不变
        // 手动标签变化
        List<Integer> newManualLabelIds = Objects.isNull(newInfo.getManualMappingLabelIds()) ? Collections.emptyList() : newInfo.getManualMappingLabelIds();
        List<Integer> oldManualLabelIds = Objects.isNull(oldInfo.getManualMappingLabelIds()) ? Collections.emptyList() : oldInfo.getManualMappingLabelIds();

        // !(手动标新包含整个手动标旧 && 手动标旧包含整个手动标新) => 手动标不完全一样 => 则记录前后
        if (!(newManualLabelIds.containsAll(oldManualLabelIds) && oldManualLabelIds.containsAll(newManualLabelIds))) {
            bindingLog.setOldManualLabelIds(oldManualLabelIds);
            bindingLog.setNewManualLabelIds(newManualLabelIds);
        }

        NewLogOperatorDto logOperator = NewLogOperatorDto.builder()
                .module(Module.ACCOUNT_LABEL)
                .modifyType(modifyType)
                .obj(bindingLog)
                .objId(newInfo.getAccountId())
                .oldObj(attachmentId)
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logOperator);
    }

    /**
     * 构建最终需要处理的账号标签 items
     *
     * @param accountBindItemsMapByAccountId
     * @param accountAndTypeMappingsMap
     * @return
     */
    private List<AccountLabelBatchBindItemDto> buildNeedOptAccountLabelItemDtos(Map<Integer, List<AccountLabelBatchBindItemDto>> accountBindItemsMapByAccountId, Map<Integer, Map<Integer, List<AccAccountLabelMappingPo>>> accountAndTypeMappingsMap) {
        Iterator<Map.Entry<Integer, List<AccountLabelBatchBindItemDto>>> itr = accountBindItemsMapByAccountId.entrySet().iterator();
        List<AccountLabelBatchBindItemDto> batchBindItemDtosOk = new ArrayList<>();
        // 用作去重，格式: accountId_labelId
        Set<String> tmpAccountIdLabelIdSet = new HashSet<>();
        while (itr.hasNext()) {
            Map.Entry<Integer, List<AccountLabelBatchBindItemDto>> next = itr.next();

            for (AccountLabelBatchBindItemDto bindItemDto : next.getValue()) {
                Map<Integer, List<AccAccountLabelMappingPo>> mappingPosOfAccount = accountAndTypeMappingsMap.getOrDefault(bindItemDto.getAccountId(), new HashMap<>());
                // 账户有打过标签，都是手动的
                List<AccAccountLabelMappingPo> autoMappingPos =
                        mappingPosOfAccount.getOrDefault(AccountLabelMappingType.AUTO.getCode(), new ArrayList<>());
                if (!CollectionUtils.isEmpty(autoMappingPos)) {
                    Set<Integer> autoLabelIdsOfAccount = autoMappingPos.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());
                    if (autoLabelIdsOfAccount.contains(bindItemDto.getLabelId())) {
                        AccAccountLabelMappingPo mappingPo = autoMappingPos.get(0);
                        String errMsg = String.format("标签ID:%s在账号ID:%s的自动标签里；暂不支持添加，请修改后再上传",
                                bindItemDto.getLabelId(), mappingPo.getAccountId());
                        throw new ServiceRuntimeException(errMsg);
                    }
                }

                List<AccAccountLabelMappingPo> manualMappingPos = mappingPosOfAccount.getOrDefault(AccountLabelMappingType.MANUAL.getCode(), Collections.EMPTY_LIST);
                Set<Integer> manualLabelIdsOfAccount = manualMappingPos.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());

                // 新增: 过滤掉已经存在的手动标签
                if (AccountLabelBatchOptType.ADD.getCode().equals(bindItemDto.getOperateType())) {
                    if (manualLabelIdsOfAccount.contains(bindItemDto.getLabelId())) {
                        Cat.logEvent("buildNeedOptAccountLabelItemDtos", "新增的存在过滤", Event.SUCCESS, String.format(
                                "accountId=%s,labelId=%s", bindItemDto.getAccountId(), bindItemDto.getLabelId()));
                        continue;
                    }
                }
                // 删除: 过滤掉不存在的手动标签
                if (AccountLabelBatchOptType.DELETE.getCode().equals(bindItemDto.getOperateType())) {
                    if (!manualLabelIdsOfAccount.contains(bindItemDto.getLabelId())) {
                        Cat.logEvent("buildNeedOptAccountLabelItemDtos", "删除的不存在过滤", Event.SUCCESS, String.format(
                                "accountId=%s,labelId=%s", bindItemDto.getAccountId(), bindItemDto.getLabelId()));
                        continue;
                    }
                }

                // 去重
                String key = bindItemDto.getAccountId() + "_" + bindItemDto.getLabelId();
                if (tmpAccountIdLabelIdSet.contains(key)) {
                    Cat.logEvent("buildNeedOptAccountLabelItemDtos", "去除重复的", Event.SUCCESS, String.format(
                            "accountId=%s,labelId=%s", bindItemDto.getAccountId(), bindItemDto.getLabelId()));
                    continue;
                }

                tmpAccountIdLabelIdSet.add(key);
                batchBindItemDtosOk.add(bindItemDto);
            }
        }
        return batchBindItemDtosOk;
    }

    private List<AccountLabelBatchBindItemDto> newBuildNeedOptAccountLabelItemDtos(Map<Integer, List<AccountLabelBatchBindItemDto>> accountBindItemsMapByAccountId,
                                                                                   Map<Integer, Map<Integer, List<AccAccountLabelMappingPo>>> accountAndTypeMappingsMap,
                                                                                   List<AccountLabelBindFailExcelBo> excelBos) {
        Iterator<Map.Entry<Integer, List<AccountLabelBatchBindItemDto>>> itr = accountBindItemsMapByAccountId.entrySet().iterator();
        List<AccountLabelBatchBindItemDto> batchBindItemDtosOk = new ArrayList<>();
        // 用作去重，格式: accountId_labelId
        Set<String> tmpAccountIdLabelIdSet = new HashSet<>();
        while (itr.hasNext()) {
            Map.Entry<Integer, List<AccountLabelBatchBindItemDto>> next = itr.next();

            for (AccountLabelBatchBindItemDto bindItemDto : next.getValue()) {
                Map<Integer, List<AccAccountLabelMappingPo>> mappingPosOfAccount = accountAndTypeMappingsMap.getOrDefault(bindItemDto.getAccountId(), new HashMap<>());
                List<AccAccountLabelMappingPo> autoMappingPos =
                        mappingPosOfAccount.getOrDefault(AccountLabelMappingType.AUTO.getCode(), new ArrayList<>());
                if (!CollectionUtils.isEmpty(autoMappingPos)) {
                    Set<Integer> autoLabelIdsOfAccount = autoMappingPos.stream().map(AccAccountLabelMappingPo::getLabelId).collect(Collectors.toSet());
                    if (autoLabelIdsOfAccount.contains(bindItemDto.getLabelId())) {
                        AccountLabelBindFailExcelBo failExcelBo = new AccountLabelBindFailExcelBo();
                        failExcelBo.setAccountId(bindItemDto.getAccountId());
                        failExcelBo.setLabelId(bindItemDto.getLabelId());
                        failExcelBo.setFailedReason("标签已在自动打标中");
                        failExcelBo.setType(bindItemDto.getOperateType() == 1 ? "新增" : "删除");
                        excelBos.add(failExcelBo);
                        continue;
                    }
                }

                List<AccAccountLabelMappingPo> manualMappingPos = mappingPosOfAccount.getOrDefault(AccountLabelMappingType.MANUAL.getCode(), Collections.EMPTY_LIST);
                Set<Integer> manualLabelIdsOfAccount = manualMappingPos.stream().map(t -> t.getLabelId()).collect(Collectors.toSet());

                // 新增: 过滤掉已经存在的手动标签
                if (AccountLabelBatchOptType.ADD.getCode().equals(bindItemDto.getOperateType())) {
                    if (manualLabelIdsOfAccount.contains(bindItemDto.getLabelId())) {
                        Cat.logEvent("buildNeedOptAccountLabelItemDtos", "新增的存在过滤", Event.SUCCESS, String.format(
                                "accountId=%s,labelId=%s", bindItemDto.getAccountId(), bindItemDto.getLabelId()));
                        continue;
                    }
                }
                // 删除: 过滤掉不存在的手动标签
                if (AccountLabelBatchOptType.DELETE.getCode().equals(bindItemDto.getOperateType())) {
                    if (!manualLabelIdsOfAccount.contains(bindItemDto.getLabelId())) {
                        Cat.logEvent("buildNeedOptAccountLabelItemDtos", "删除的不存在过滤", Event.SUCCESS, String.format(
                                "accountId=%s,labelId=%s", bindItemDto.getAccountId(), bindItemDto.getLabelId()));
                        continue;
                    }
                }

                // 去重
                String key = bindItemDto.getAccountId() + "_" + bindItemDto.getLabelId();
                if (tmpAccountIdLabelIdSet.contains(key)) {
                    Cat.logEvent("buildNeedOptAccountLabelItemDtos", "去除重复的", Event.SUCCESS, String.format(
                            "accountId=%s,labelId=%s", bindItemDto.getAccountId(), bindItemDto.getLabelId()));
                    continue;
                }

                tmpAccountIdLabelIdSet.add(key);
                batchBindItemDtosOk.add(bindItemDto);
            }
        }
        return batchBindItemDtosOk;
    }

    /**
     * 将 exce 的列按逗号分隔
     *
     * @param excelItemDtoList
     * @return
     */
    public List<AccountLabelBatchBindItemDto> spliceData(List<AccountLabelExcelItemDto> excelItemDtoList) {
        log.info("=====> spliceData start");
        List<AccountLabelBatchBindItemDto> batchBindItemDtos = new ArrayList<>();

        for (AccountLabelExcelItemDto excelItemDto : excelItemDtoList) {
            // 如果有中文逗号，替换为英文逗号
            if (StringUtils.isNotEmpty(excelItemDto.getAccountIdsStr()) && excelItemDto.getAccountIdsStr().contains(
                    "，")) {
                excelItemDto.setAccountIdsStr(excelItemDto.getAccountIdsStr().replace("，", ","));
            }
            if (StringUtils.isNotEmpty(excelItemDto.getLabelIdsStr()) && excelItemDto.getLabelIdsStr().contains(
                    "，")) {
                excelItemDto.setLabelIdsStr(excelItemDto.getLabelIdsStr().replace("，", ","));
            }

            List<Integer> accountIds = null;
            List<Integer> labelIds = null;

            try {
                accountIds = Arrays.stream(excelItemDto.getAccountIdsStr().split(","))
                        .map(t -> Integer.parseInt(t.trim())).distinct().collect(Collectors.toList());
            } catch (Exception e) {
                log.error("=====> spliceData, 解析ids失败, excelItemDto:{}", JSON.toJSONString(excelItemDto));
                throw new ServiceRuntimeException("账户ID输入错误，解析失败:" + excelItemDto.getAccountIdsStr());
            }

            try {
                labelIds = Arrays.stream(excelItemDto.getLabelIdsStr().split(","))
                        .map(t -> Integer.parseInt(t.trim())).distinct().collect(Collectors.toList());
            } catch (Exception e) {
                log.error("=====> spliceData, 解析ids失败, excelItemDto:{}", JSON.toJSONString(excelItemDto));
                throw new ServiceRuntimeException("标签ID输入错误，解析失败:" + excelItemDto.getLabelIdsStr());
            }

            for (Integer accountId : accountIds) {
                for (Integer labelId : labelIds) {
                    AccountLabelBatchBindItemDto bindItemDto = AccountLabelBatchBindItemDto.builder()
                            .accountId(accountId)
                            .labelId(labelId)
                            .operateType(excelItemDto.getOperateType())
                            .build();
                    batchBindItemDtos.add(bindItemDto);
                }
            }

        }
        return batchBindItemDtos;
    }

    /**
     * 获取账号标签批量上传的模板
     *
     * @return
     */
    public AttachmentResultDto queryAccountLabelBatchTemplate() {
        List<AttachmentResultDto> attachmentResultDtos = attachmentUploadFacade.queryAttachmentInfo(1, AttachmentUploadBizModuleEnum.ACCOUNT_LABEL_TEMPLATE, UploadAttachmentType.OSS);
        if (CollectionUtils.isEmpty(attachmentResultDtos)) {
            return AttachmentResultDto.builder().build();
        }
        return attachmentResultDtos.get(0);
    }

    private String generateUploadFileTask(List<AccountLabelBindFailExcelBo> excelBos) {
        log.info("generateUploadFileTask start, size = {}", excelBos.size());
        String taskId = "taskId" + "_" + System.currentTimeMillis();
        Supplier<String> supplier = () -> {
            File file = createTmpExportFile();
            ExcelWriter excelWriter = null;
            try {
                excelWriter = EasyExcelFactory.write(file, AccountLabelBindFailExcelBo.class).build();
                WriteSheet sheet = EasyExcelFactory.writerSheet("sheet0").build();
                excelWriter.write(excelBos, sheet);
                excelWriter.finish();
                bossFileService.directUpload(taskId, FileUtils.getMultipartFile(file, taskId + ".xlsx"));
            } catch (Exception e) {
                log.error("generateUploadFileTask fail", e);
                throw new RuntimeException("上传任务失败");
            } finally {
                if (null != excelWriter) {
                    excelWriter.finish();
                }
            }
            return taskId;
        };
        asyncTaskComponent.start(taskId, supplier);
        return taskId;
    }

    private File createTmpExportFile() {
        File file;
        try {
            String fileName = "/data/file/" + "_" + "task" + System.currentTimeMillis() + ".xlsx";
            file = new File(fileName);
            if (!file.exists()) {
                if (!file.getParentFile().exists() && !file.getParentFile().mkdirs()) {
                    throw new RuntimeException("{} 文件夹创建失败");
                }
                if (!file.createNewFile()) {
                    throw new RuntimeException("{} 文件创建失败");
                }
            }
        } catch (Exception e) {
            log.error("createIfNecessary failed", e);
            throw new RuntimeException("上传任务失败");
        }
        return file;
    }
}
