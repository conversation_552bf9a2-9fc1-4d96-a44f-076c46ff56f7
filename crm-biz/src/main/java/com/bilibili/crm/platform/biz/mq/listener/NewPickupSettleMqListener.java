package com.bilibili.crm.platform.biz.mq.listener;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.commercialorder.api.order.enums.OrderStatus;
import com.bilibili.commercialorder.soa.order.dto.OrderInfoForCrmSynDto;
import com.bilibili.commercialorder.soa.order.dto.QueryOrderInfoForCrmReqDto;
import com.bilibili.commercialorder.soa.order.service.ISoaCmOrderService;
import com.bilibili.crm.platform.api.pickup.INewPickupSettleService;
import com.bilibili.crm.platform.api.pickup.INoStandardDeliveryService;
import com.bilibili.crm.platform.biz.common.PickupSettleTypeEnum;
import com.dianping.cat.Cat;
import com.google.common.collect.Lists;
import com.rabbitmq.client.Channel;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.amqp.core.Message;
import org.springframework.amqp.rabbit.listener.api.ChannelAwareMessageListener;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.nio.charset.StandardCharsets;
import java.util.List;

/**
 * <AUTHOR>
 * description：花火账单（订单）监听(新方案)
 * date ：2020/9/17 8:05 下午
 */
@Slf4j
@Service
//public class NewPickupSettleMqListener implements ChannelAwareMessageListener {
public class NewPickupSettleMqListener {

    @Autowired
    private INewPickupSettleService newPickupSettleService;
    @Autowired
    private INoStandardDeliveryService noStandardDeliveryService;
    @Autowired
    private ISoaCmOrderService soaCmOrderService;


//    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
//    @Override
//    public void onMessage(Message message, Channel channel) throws Exception {
//
//        long deliveryTag = message.getMessageProperties().getDeliveryTag();
//
//        Integer orderId = null;
//        try {
//            orderId = convert(message);
//            syncPickupSettle(orderId);
//            channel.basicAck(deliveryTag, false);
//        } catch (Exception e) {
//            Cat.logError(e);
//            log.info("NewPickupSettleMqListener.error, orderId: {}, e : {}", orderId, e);
//        }
//    }

    public void syncPickupSettle(Integer orderId) {
        log.info("NewPickupSettleMqListener, orderId: {}", orderId);
        List<OrderInfoForCrmSynDto> pickupOrderInfo = soaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder().orderIds(Lists.newArrayList(orderId)).isQueryTask(true).isQueryUpper(true).isQuerySettle(true).build());
        if (!CollectionUtils.isEmpty(pickupOrderInfo) && pickupOrderInfo.get(0).getOrderStatus().equals(OrderStatus.PENDING_CRM_AUDIT_FINAL_DRAFT_FILE.getCode())) {
            noStandardDeliveryService.saveNoStandardDelivery(Operator.SYSTEM, pickupOrderInfo.get(0));
        }
        newPickupSettleService.syncPickupSettle(Operator.SYSTEM, orderId, PickupSettleTypeEnum.PICKUP_ORDER.getCode());
    }

    private Integer convert(Message message) {

        String jsonStr = new String(message.getBody(), StandardCharsets.UTF_8);
        NewPickupSettleMessage pickupMessage = JSON.parseObject(jsonStr, NewPickupSettleMessage.class);
        log.info("PickupSettleMqListener.message: {}", pickupMessage);
        return pickupMessage.getOrderId();
    }


    /**
     * 花火订单消息实体
     */
    @Data
    public static class NewPickupSettleMessage {

        private Integer orderId;

    }
}
