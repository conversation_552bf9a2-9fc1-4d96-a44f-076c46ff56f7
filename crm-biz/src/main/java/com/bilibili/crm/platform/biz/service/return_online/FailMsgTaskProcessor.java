package com.bilibili.crm.platform.biz.service.return_online;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.NewBackRedPacketDto;
import com.bilibili.crm.platform.api.return_online.dto.ReturnApplyTaskExecDto;
import com.bilibili.crm.platform.biz.po.AdaMsgTaskPo;
import com.bilibili.crm.platform.biz.repo.AdaMsgTaskRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.backredpacket.AutoBackRedPacketProcessorForReturnOnline;
import com.bilibili.crm.platform.biz.service.return_online.task.ReturnApplyDoTaskProc;
import com.bilibili.crm.platform.biz.service.return_online.task.ReturnApplyTaskProcessor;
import com.bilibili.sycpb.acc.api.dict.common.MsgTaskStatusEnum;
import com.bilibili.sycpb.acc.api.dict.common.MsgTaskTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 失败的消息任务处理器
 *
 * <AUTHOR>
 * @date 2021/11/2 上午12:45
 */
@Slf4j
@Component
public class FailMsgTaskProcessor {

    @Autowired
    private AdaMsgTaskRepo adaMsgTaskRepo;

    @Autowired
    private AutoBackRedPacketProcessorForReturnOnline autoBackRedPacketProcessorForReturnOnline;
    @Autowired
    private ReturnApplyTaskProcessor returnApplyTaskProcessor;
    @Autowired
    private ReturnApplyDoTaskProc returnApplyDoTaskProc;

    public String queryBatchFailTasksThenDo(List<Integer> ids) {
        Timestamp endDate = Utils.getNow();
        Timestamp beginDate = Utils.getSomeDayAgo(endDate, 30);

        // 获取失败的 && 未超过重试最大次数的任务 list
        List<AdaMsgTaskPo> failTaskPos = adaMsgTaskRepo.queryFailedProcessTasks(beginDate, endDate, ids,
                null);

        log.info("=====> queryBatchFailTasksThenDo, queryFailedProcessTasks, size:{}", failTaskPos.size());
        List<Integer> taskIds = failTaskPos.stream().map(t -> t.getId()).collect(Collectors.toList());

        log.info("=====> process, need to process task size:{}", failTaskPos.size());
        if (CollectionUtils.isEmpty(failTaskPos)) {
            return "没有失败的msg任务需要处理";
        }

        // 重新执行流水接收动作
        Integer failCount = 0;
        Integer successCount = 0;
        for (AdaMsgTaskPo msgTaskPo : failTaskPos) {
            if (MsgTaskTypeEnum.CRM_CAL_RETURN_AMOUNT_TASK.getCode().equals(msgTaskPo.getType())) {
                ReturnApplyTaskExecDto returnApplyTaskExecDto = JSON.parseObject(msgTaskPo.getMsgContent(), ReturnApplyTaskExecDto.class);
                try {
                    returnApplyDoTaskProc.doTaskWrapper(returnApplyTaskExecDto);
                    successCount++;
                    adaMsgTaskRepo.updateMsgTaskStatus(msgTaskPo.getId(),
                            MsgTaskStatusEnum.COMPLETE_AND_SUCCESS.getCode(), "ok");
                } catch (Exception e) {
                    failCount++;
                    adaMsgTaskRepo.addTaskFailRetryCount(msgTaskPo.getId());

                    log.error("=====> process, 处理失败的msg任务, taskId:{}, e:{}", msgTaskPo.getId(), e);
                }
            } else if (MsgTaskTypeEnum.CRM_RETURN_AMOUNT_TASK_CREATE.getCode().equals(msgTaskPo.getType())) {
                try {
                    NewBackRedPacketDto newBackRedPacketDto = JSON.parseObject(msgTaskPo.getMsgContent(), NewBackRedPacketDto.class);
                    autoBackRedPacketProcessorForReturnOnline.createBackRedPacketForSystemCal(Operator.SYSTEM, newBackRedPacketDto);
                    successCount++;
                } catch (Exception e) {
                    failCount++;
                    adaMsgTaskRepo.addTaskFailRetryCount(msgTaskPo.getId());

                    log.error("=====> process, 处理失败的msg任务, taskId:{}, e:{}", msgTaskPo.getId(), e);
                }
            }
        }

        return String.format("执行成功count:%s, 执行失败count:%s", successCount, failCount);
    }

}
