package com.bilibili.crm.platform.biz.kingdee.service;

import com.amazonaws.util.StringUtils;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.commercialorder.api.order.enums.PayFinanceType;
import com.bilibili.commercialorder.api.session.enums.ReqSourceType;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.kingdee.dto.*;
import com.bilibili.crm.platform.api.trading.flow.dto.AccountTradingFlowDto;
import com.bilibili.crm.platform.api.trading.flow.dto.AgentTradingFlowBean;
import com.bilibili.crm.platform.api.trading.flow.dto.QueryTradingFlowParam;
import com.bilibili.crm.platform.api.trading.flow.service.ITradingFlowDayService;
import com.bilibili.crm.platform.biz.common.GroupType;
import com.bilibili.crm.platform.biz.kingdee.enums.KingDeeBizType;
import com.bilibili.crm.platform.biz.kingdee.service.conf.KingDeeConfig;
import com.bilibili.crm.platform.biz.kingdee.service.conf.RecTypeEnum;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.service.AgentService;
import com.bilibili.crm.platform.biz.service.finance.automation.component.config.ReceiveMoneyConfig;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.CrmConstant;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.kingdee.KingDeeCrmBizType;
import com.bilibili.crm.platform.common.kingdee.KingDeeInvoiceType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.BooleanUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: brady
 * @time: 2021/11/9 5:36 下午
 */
@Slf4j
@Component
public class KingDeeFinanceHelper {
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    @Resource
    private AgentService agentService;

    @Resource
    private IFastQueryAccountService fastQueryAccountService;

    @Resource
    private CustomerRepo customerRepo;

    @Resource
    private ITradingFlowDayService tradingFlowDayService;

    @Resource
    private ReceiveMoneyConfig receiveMoneyConfig;

    /**
     * 随机唯一码
     *
     * @return
     */
    public String generateUnicode() {
        long number = snowflakeIdWorker.nextId();
        return String.valueOf(number);
    }

    public List<ArOtherBill> modifyArOtherBill(List<ArOtherBill> bills) {
        List<Integer> customerIds = bills.stream().map(ArOtherBill::getCustomerId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(customerIds);
        return Optional.ofNullable(bills).orElse(new ArrayList<>()).stream().filter(item -> null != item.getAsstActName()
                        && !item.getAsstActName().contains(KingDeeConfig.KING_DEE_TEST))
                .filter(bill -> {
                    if (Objects.isNull(bill.getCustomerId()) || bill.getCustomerId() == 0) {
                        return Boolean.TRUE;
                    }
                    CustomerPo customerPo = customerPoMap.getOrDefault(bill.getCustomerId(), CustomerPo.builder().groupId(0).build());
                    return !KingDeeConfig.EXCLUDE_GROUP_IDS.contains(customerPo.getGroupId());
                })
                .map(item -> {
                    LicenseCodeName licenseCodeName = convertLicenseCodeName(item.getAsstActNumber(), item.getAsstActName());
                    if (org.apache.commons.lang3.StringUtils.equals(item.getCrmContractNumber(), item.getAsstActNumber())) {
                        item.setCrmContractNumber(licenseCodeName.getCode());
                    }
                    item.setAsstActNumber(licenseCodeName.getCode());
                    item.setAsstActName(licenseCodeName.getName());
                    return item;
                }).collect(Collectors.toList());
    }

    public List<ReceiveBill> modifyReceiveBill(List<ReceiveBill> bills) {
        return Optional.ofNullable(bills).orElse(new ArrayList<>()).stream().filter(item -> null != item.getPayerName()
                        && !item.getPayerName().contains(KingDeeConfig.KING_DEE_TEST))
                .map(item -> {
                    LicenseCodeName licenseCodeName = convertLicenseCodeName(item.getPayerNumber(), item.getPayerName());
                    Assert.isTrue(!StringUtils.isNullOrEmpty(item.getCrmContractNumber()), String.format("客户:{}", item.getSrcUnicode()) + "营业执照编码编码缺失");
                    if (item.getCrmContractNumber().equals(item.getPayerNumber())) {
                        item.setCrmContractNumber(licenseCodeName.getCode());
                    }
                    item.setPayerNumber(licenseCodeName.getCode());
                    item.setPayerName(licenseCodeName.getName());
                    return item;
                }).collect(Collectors.toList());
    }

    public List<SettleBill> modifySettleBill(List<SettleBill> bills) {
        return Optional.ofNullable(bills).orElse(new ArrayList<>()).stream().filter(item -> null != item.getCustomerName()
                        && !item.getCustomerName().contains(KingDeeConfig.KING_DEE_TEST))
                .map(item -> {
                    LicenseCodeName licenseCodeName = convertLicenseCodeName(item.getCustomerNumber(), item.getCustomerName());
                    if (item.getCrmContractNumber().equals(item.getCustomerNumber())) {
                        item.setCrmContractNumber(licenseCodeName.getCode());
                    }
                    item.setCustomerNumber(licenseCodeName.getCode());
                    item.setCustomerName(licenseCodeName.getName());
                    return item;
                }).collect(Collectors.toList());
    }


    private LicenseCodeName convertLicenseCodeName(String code, String name) {
        if ("9131000033263865X2-6".equals(code)) {
            return LicenseCodeName.builder().code("9131000033263865X2").name("舶乐蜜电子商务（上海）有限公司").build();
        }
        if ("91310115312541754M-2".equals(code)) {
            return LicenseCodeName.builder().code("91310115312541754M").name("幻电整合营销（后付）").build();
        }
        if ("91450500MA5NK2X529-虚拟代理商".equals(code)) {
            return LicenseCodeName.builder().code("91450500MA5NK2X529").name("广西京东新杰电子商务有限公司").build();
        }
        return LicenseCodeName.builder().code(code).name(name).build();
    }

    /**
     * 发票类型转换
     *
     * @param invoiceType 开票类型的名称
     * @return
     */
    public KingDeeInvoiceType invoiceType2KingDee(String invoiceType) {
        //注意排除形式发票
//        if (InvoiceType.ORDINARY_PAPER.getTypeName().equals(invoiceType)) {
//            return KingDeeInvoiceType.Normal_Invoice;
//        }
//        if (InvoiceType.ORDINARY_ELEC.getTypeName().equals(invoiceType)) {
//            return KingDeeInvoiceType.Normal_Invoice;
//        }
//        if (InvoiceType.getSpecialTypesName().equals(invoiceType)) {
//            return KingDeeInvoiceType.Dedicated_Invoice;
//        }
//        return KingDeeInvoiceType.Normal_Invoice;
        // 兼容花火开票 InvoiceType枚举变更
        if (invoiceType.contains("专票")) {
            return KingDeeInvoiceType.Dedicated_Invoice;
        }
        //形式发票 普票 都是普票
        return KingDeeInvoiceType.Normal_Invoice;
    }

    /**
     * 应收单唯一码
     *
     * @param dto
     * @return
     */
    public String generateArOtherUnicode(KingArOtherDataDto dto, KingDeeCrmBizType bizType, Timestamp date) {
        String dataString = CrmUtils.formatDate(date, "yyyyMM");
        //品牌
        if (KingDeeCrmBizType.BandAdv.equals(bizType)) {
            return genCommonUnicode("CRM_AR", bizType.name(), dataString, String.valueOf(dto.getContractNo()));
        }
        if (KingDeeCrmBizType.EffectPrepaid.equals(bizType)) {
            return genCommonUnicode("CRM_AR", bizType.name(), dataString, String.valueOf(dto.getAccountId()));
        }
        if (KingDeeCrmBizType.EffectCredit.equals(bizType)) {
            return genCommonUnicode("CRM_CREDIT", bizType.name(), dataString, String.valueOf(dto.getAccountId()));
        }
        if (KingDeeCrmBizType.EffectPostpaid.equals(bizType)) {
            if (dto.getIsClose() == null) {
                dto.setIsClose(IsValid.FALSE.getCode());
                dto.setBillId("0");
            }
            return genCommonUnicode("CRM_AR", bizType.name(), dataString, String.valueOf(dto.getAccountId()), String.valueOf(dto.getBillId()), String.valueOf(dto.getIsClose()));
        }
        if (KingDeeCrmBizType.FireworksPrepaid.equals(bizType) || KingDeeCrmBizType.FireworksPostpaid.equals(bizType)) {
            if (Objects.equals(dto.getReqSource(), ReqSourceType.TB_SPARK)) {
                //唯一编码传为：客户ID+账户ID+业务类型+订单编号+日期
                return genCommonUnicode("CRM_AR", String.valueOf(dto.getCustomerId()), String.valueOf(dto.getAccountId()), bizType.name(), String.valueOf(dto.getPickUpOrderNo()), dataString);
            }
            return genCommonUnicode("CRM_AR", bizType.name(), dataString, String.valueOf(dto.getAccountId()));
        }
        return genCommonUnicode("CRM_AR_RANDOM", bizType.name(), generateUnicode());
    }

    /**
     * 收款单唯一码
     *
     * @param dto
     * @return
     */
    public String generateReceiveUnicode(KingReceiveDataDto dto, KingDeeCrmBizType bizType) {
        String receiveType = null != dto.getKingReceiveType() ? dto.getKingReceiveType().getDesc() : null;
        if (Utils.isPositive(dto.getFlowId())) {
            return genCommonUnicode("CRM_REC_ID", bizType.name(), receiveType, String.valueOf(dto.getFlowId()),
                    String.valueOf(dto.getContractNo()), dto.getUniCode(), String.valueOf(dto.getDeductId()));
        }

        if (!Strings.isNullOrEmpty(dto.getFlowNo())) {
            return genCommonUnicode("CRM_REC_NO", bizType.name(), receiveType, dto.getFlowNo(),
                    String.valueOf(dto.getContractNo()), dto.getUniCode(), String.valueOf(dto.getDeductId()));
        }

        return genCommonUnicode("CRM_REC_RANDOM", bizType.name(), receiveType, generateUnicode(), String.valueOf(dto.getDeductId()));
    }

    public String generateReceiveUnicode(KingReceiveDataDto dto, KingDeeCrmBizType bizType, RecTypeEnum recTypeEnum, String period) {
        String receiveType = null != dto.getKingReceiveType() ? dto.getKingReceiveType().getDesc() : null;
        if (Utils.isPositive(dto.getFlowId())) {
            return genCommonUnicode("CRM_REC_ID", bizType.name(), receiveType, recTypeEnum.name(), String.valueOf(dto.getFlowId()),
                    String.valueOf(dto.getContractNo()), dto.getUniCode(), String.valueOf(dto.getDeductId()));
        }
        if (!Strings.isNullOrEmpty(dto.getFlowNo())) {
            return genCommonUnicode("CRM_REC_NO", bizType.name(), receiveType, recTypeEnum.name(), dto.getFlowNo(),
                    String.valueOf(dto.getContractNo()), dto.getUniCode(), String.valueOf(dto.getDeductId()));
        }
        if (Utils.isPositive(dto.getAccountId())) {
            return genCommonUnicode("CRM_REC_NO", bizType.name(), receiveType, recTypeEnum.name(), String.valueOf(dto.getAccountId()),
                    String.valueOf(dto.getContractNo()), dto.getUniCode(), String.valueOf(dto.getDeductId()), period);
        }
        return genCommonUnicode("CRM_REC_RANDOM", bizType.name(), receiveType, recTypeEnum.name(), generateUnicode(), String.valueOf(dto.getDeductId()), period);
    }

    /**
     * 结算单唯一码
     *
     * @param dto
     * @return
     */
    public String generateSettleUnicode(KingSettleDataDto dto, KingDeeCrmBizType bizType) {
        if (!Strings.isNullOrEmpty(dto.getFlowNo())) {
            return genCommonUnicode("CRM_SET_NO", bizType.name(), dto.getFlowNo(),
                    String.valueOf(dto.getContractNo()), dto.getUniCode());
        }

        if (Utils.isPositive(dto.getFlowId())) {
            return genCommonUnicode("CRM_SET_ID", bizType.name(),
                    String.valueOf(dto.getFlowId()), String.valueOf(dto.getContractNo()), dto.getUniCode());
        }

        return genCommonUnicode("CRM_SET_RANDOM", bizType.name(), generateUnicode());
    }

    public BigDecimal fen2Yuan(BigDecimal amount) {
        if (amount == null) {
            return BigDecimal.ZERO;
        }
        return amount.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
    }

    public String genCommonUnicode(String... args) {
        return Stream.of(args).filter(item ->
                !StringUtils.isNullOrEmpty(item) && !"null".equals(item)
        ).collect(Collectors.joining("_"));
    }

    /**
     * 消耗按照代理商归总
     *
     * @param dtoList
     * @return
     */
    public List<KingArOtherDataDto> aggByAgent(List<KingArOtherDataDto> dtoList, Boolean isPre) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Lists.newArrayList();
        }
        List<AgentDto> agentDtos = agentService.getAgentsInIds(dtoList.stream().filter(t-> Utils.isPositive(t.getAgentId()))
                .map(KingArOtherDataDto::getAgentId).collect(Collectors.toList()));
        Map<Integer, Integer> agentMap = agentDtos.stream().collect(Collectors.toMap(AgentDto::getId, AgentDto::getSysAgentId));
        dtoList.forEach(dto -> {
            if (Utils.isPositive(dto.getAgentId())) {
                dto.setAccountId(agentMap.get(dto.getAgentId()));
            }
        });
        List<Integer> accountIds = dtoList.stream().map(KingArOtherDataDto::getAccountId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList();
        }
        List<AccountBaseDto> baseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds).build(),
                AccountFieldMapping.accountId, AccountFieldMapping.financeType);
        Map<Integer, AccountBaseDto> accountBaseDtoMap = baseDtoList.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));

        List<KingArOtherDataDto> filterList = new ArrayList<>();

        dtoList.stream().forEach(dto -> {
            KingArOtherDataDto useDataDto = KingArOtherDataDto.builder().build();
            AccountBaseDto accountBaseDto = accountBaseDtoMap.getOrDefault(dto.getAccountId(), AccountBaseDto.builder().build());
            if (!Objects.equals(accountBaseDto.getFinanceType(), dto.getAccountFinanceType())) {
                log.info("accountBaseDto finance accountId={} acc type ={} dto type={}", accountBaseDto.getAccountId(), accountBaseDto.getFinanceType(), dto.getAccountFinanceType());
            }
            if (isPre) {
                if (!Objects.equals(accountBaseDto.getFinanceType(), 2) && !Objects.equals(dto.getPickUpFinanceType(), 1)) {
                    BeanUtils.copyProperties(dto, useDataDto);
                    log.info("pre aggByAgent = {}", useDataDto);
                    filterList.add(useDataDto);
                }
            } else {
                if (Objects.equals(accountBaseDto.getFinanceType(), 2)
                        || (Objects.equals(dto.getPickUpFinanceType(), 1)) && Objects.equals(accountBaseDto.getFinanceType(), 1)) {
                    BeanUtils.copyProperties(dto, useDataDto);
                    log.info("pre aggByAgent = {}", useDataDto);
                    filterList.add(useDataDto);
                }
            }
        });

        Map<Integer, List<KingArOtherDataDto>> accountMap = filterList.stream().collect(Collectors.groupingBy(KingArOtherDataDto::getAccountId));

        return accountMap.keySet().stream().map(accountId -> {
            List<KingArOtherDataDto> temp = accountMap.get(accountId);
            KingArOtherDataDto dataDto = new KingArOtherDataDto();
            BeanUtils.copyProperties(temp.get(0), dataDto);
            for (int i = 1; i < temp.size(); i++) {
                KingArOtherDataDto tempDto = temp.get(i);
                dataDto.setConsumeAmount(dataDto.getConsumeAmount().add(tempDto.getConsumeAmount()));
            }
            return dataDto;
        }).collect(Collectors.toList());

    }

    /**
     * 淘宝联盟-星火计划消耗按照订单归总
     *
     * @param dtoList
     * @return
     */
    public List<KingArOtherDataDto> aggByOrder(List<KingArOtherDataDto> dtoList, Boolean isPre) {
        if (CollectionUtils.isEmpty(dtoList)) {
            return Lists.newArrayList();
        }
        List<AgentDto> agentDtos = agentService.getAgentsInIds(dtoList.stream().filter(t-> Utils.isPositive(t.getAgentId()))
                .map(KingArOtherDataDto::getAgentId).collect(Collectors.toList()));
        Map<Integer, Integer> agentMap = agentDtos.stream().collect(Collectors.toMap(AgentDto::getId, AgentDto::getSysAgentId));
        dtoList.stream().forEach(dto -> {
            if (Utils.isPositive(dto.getAgentId())) {
                dto.setAccountId(agentMap.get(dto.getAgentId()));
            }
        });
        List<Integer> accountIds = dtoList.stream().map(KingArOtherDataDto::getAccountId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(accountIds)) {
            return Lists.newArrayList();
        }
        List<AccountBaseDto> baseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds).build(),
                AccountFieldMapping.accountId, AccountFieldMapping.financeType);
        Map<Integer, AccountBaseDto> accountBaseDtoMap = baseDtoList.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));

        List<KingArOtherDataDto> filterList = new ArrayList<>();

        dtoList.stream().forEach(dto -> {
            KingArOtherDataDto useDataDto = KingArOtherDataDto.builder().build();
            AccountBaseDto accountBaseDto = accountBaseDtoMap.getOrDefault(dto.getAccountId(), AccountBaseDto.builder().build());
            if (!Objects.equals(accountBaseDto.getFinanceType(), dto.getAccountFinanceType())) {
                log.info("accountBaseDto finance accountId={} acc type ={} dto type={}", accountBaseDto.getAccountId(), accountBaseDto.getFinanceType(), dto.getAccountFinanceType());
            }
            if (isPre) {
                if (!Objects.equals(accountBaseDto.getFinanceType(), 2) && !Objects.equals(dto.getPickUpFinanceType(), 1)) {
                    BeanUtils.copyProperties(dto, useDataDto);
                    filterList.add(useDataDto);
                }
            } else {
                if (Objects.equals(accountBaseDto.getFinanceType(), 2)
                        || (Objects.equals(dto.getPickUpFinanceType(), 1)) && Objects.equals(accountBaseDto.getFinanceType(), 1)) {
                    BeanUtils.copyProperties(dto, useDataDto);
                    filterList.add(useDataDto);
                }
            }
        });

        Map<Integer, List<KingArOtherDataDto>> pickUpMap = filterList.stream().collect(Collectors.groupingBy(KingArOtherDataDto::getPickUpId));

        List<KingArOtherDataDto> data = new ArrayList<>(pickUpMap.size());
        pickUpMap.forEach((pickUpId, dataList) -> {
            KingArOtherDataDto dataDto = new KingArOtherDataDto();
            BeanUtils.copyProperties(dataList.get(0), dataDto);
            dataDto.setConsumeAmount(BigDecimal.ZERO);
            dataDto.setConsumeAmount(dataList.stream().map(KingArOtherDataDto::getConsumeAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO));
            data.add(dataDto);
        });

        return data;
    }

    public Map<RecTypeEnum, List<KingReceiveDataDto>> queryTurnReceiveBill(Timestamp begin, Timestamp end, Integer type) {

        List<AgentTradingFlowBean> agentTradingFlowBeans = tradingFlowDayService.exportAgentTradingFlowList(Operator.builder().operatorName(CrmConstant.ADMIN_DEV).build(),
                QueryTradingFlowParam.builder()
                        .beginDate(begin)
                        .endDate(end)
                        .groupType(GroupType.MONTH.getCode())
                        .build());
        List<AccountTradingFlowDto> agentFlowList = agentTradingFlowBeans.stream().map(AgentTradingFlowBean::getAgentFlow).collect(Collectors.toList());
        List<Integer> accountIds = agentFlowList.stream().map(AccountTradingFlowDto::getAccountId).distinct().collect(Collectors.toList());
        List<AccountBaseDto> baseDtoList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(accountIds)) {
            baseDtoList = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(accountIds).build(), AccountFieldMapping.accountId, AccountFieldMapping.customerId, AccountFieldMapping.isSupportPickup);
        }
        List<Integer> customerIds = baseDtoList.stream().map(AccountBaseDto::getCustomerId).distinct().collect(Collectors.toList());
        // 产品需求1.9版本
        Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(customerIds);
        if (type.equals(KingDeeBizType.FIRE_WORK.getCode())) {
            Map<Integer, AccountBaseDto> baseDtoMap = baseDtoList.stream().filter(dto -> IsValid.TRUE.getCode().equals(dto.getIsSupportPickup())).collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));
            return buildTurnList(agentFlowList, baseDtoMap, customerPoMap, type, end);
        }
        Map<Integer, AccountBaseDto> baseDtoMap = baseDtoList.stream().filter(dto -> IsValid.FALSE.getCode().equals(dto.getIsSupportPickup())).collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));
        return buildTurnList(agentFlowList, baseDtoMap, customerPoMap, type, end);
    }

    private  Map<RecTypeEnum, List<KingReceiveDataDto>> buildTurnList(List<AccountTradingFlowDto> agentFlowList,
                                                                      Map<Integer, AccountBaseDto> baseDtoMap,
                                                                      Map<Integer, CustomerPo> customerPoMap,
                                                                      Integer type,
                                                                      Timestamp end) {

        Map<RecTypeEnum, List<KingReceiveDataDto>> result = new HashMap<>();
        List<KingReceiveDataDto> turnInList = new ArrayList<>();
        List<KingReceiveDataDto> turnOutList = new ArrayList<>();
        String receiveNo = KingDeeConfig.RECEIVE_REBATE_BANK_NO;
        if (type.equals(KingDeeBizType.FIRE_WORK.getCode())) {
            receiveNo = receiveMoneyConfig.getSuperPowerBankAccount();
        }
        for (AccountTradingFlowDto flowDto : agentFlowList) {
            if (!baseDtoMap.containsKey(flowDto.getAccountId())) {
                continue;
            }
            KingReceiveDataDto turnInDto = new KingReceiveDataDto();
            KingReceiveDataDto turnOutDto = new KingReceiveDataDto();
            turnInDto.setAccountId(flowDto.getAccountId());
            turnInDto.setCustomerId(baseDtoMap.getOrDefault(flowDto.getAccountId(), new AccountBaseDto()).getCustomerId());
            turnInDto.setCustomerName(customerPoMap.getOrDefault(turnInDto.getCustomerId(), new CustomerPo()).getUsername());
            turnInDto.setFlowReceiveTime(end);
            turnInDto.setReceiveAccountNo(receiveNo);
            turnInDto.setReceiveSubAccountNo(receiveNo);
            turnInDto.setPayerAccountNo(org.apache.logging.log4j.util.Strings.EMPTY);
            BeanUtils.copyProperties(turnInDto, turnOutDto);
            turnInDto.setAmount(flowDto.getCashTransferIn());
            turnOutDto.setAmount(flowDto.getCashTransferOut().negate());
            turnInList.add(turnInDto);
            turnOutList.add(turnOutDto);
        }
        result.put(RecTypeEnum.FIRE_WORK_TURN_IN, turnInList);
        result.put(RecTypeEnum.FIRE_WORK_TURN_OUT, turnOutList);
        result.put(RecTypeEnum.EFFECT_TURN_IN, turnInList);
        result.put(RecTypeEnum.EFFECT_TURN_OUT, turnOutList);
        return result;
    }
}