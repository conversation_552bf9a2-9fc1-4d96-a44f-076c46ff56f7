package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.po.CustomerPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CustomerDao {
    long countByExample(CustomerPoExample example);

    int deleteByExample(CustomerPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerPo record);

    int insertBatch(List<CustomerPo> records);

    int insertUpdateBatch(List<CustomerPo> records);

    int insert(CustomerPo record);

    int insertUpdateSelective(CustomerPo record);

    int insertSelective(CustomerPo record);

    List<CustomerPo> selectByExample(CustomerPoExample example);

    CustomerPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerPo record, @Param("example") CustomerPoExample example);

    int updateByExample(@Param("record") CustomerPo record, @Param("example") CustomerPoExample example);

    int updateByPrimaryKeySelective(CustomerPo record);

    int updateByPrimaryKey(CustomerPo record);
}