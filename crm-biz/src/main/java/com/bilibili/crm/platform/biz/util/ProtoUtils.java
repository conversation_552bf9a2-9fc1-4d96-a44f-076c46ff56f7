package com.bilibili.crm.platform.biz.util;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.serializer.SerializerFeature;
import com.google.protobuf.Message;
import com.google.protobuf.util.JsonFormat;
import lombok.extern.slf4j.Slf4j;

import java.io.IOException;

/**
 * <AUTHOR>
 * @date 2024/1/31
 */
@Slf4j
public class ProtoUtils {

    public static <T> T convert(Class<T> target, Message sourceMessage)
            throws IOException {
        if (target == null) {
            throw new IllegalArgumentException
                    ("No destination pojo class specified");
        }
        if (sourceMessage == null) {
            throw new IllegalArgumentException("No source message specified");
        }
        String json = JsonFormat.printer().print(sourceMessage);
        return JSONObject.parseObject(json, target);
    }

    public static String convert(Message sourceMessage) {
        try {
            if (sourceMessage == null) {
                return "";
            }
            String source = JsonFormat.printer().print(sourceMessage);
            return toString(source);
        } catch (Exception e) {
            log.error("ProtoUtils convert error", e);
        }
        return "";
    }

    // JSON转换成一行
    private static String toString(String source) {
        JSONObject parse = JSONObject.parseObject(source);
        return JSON.toJSONString(parse, SerializerFeature.SortField);
    }

}
