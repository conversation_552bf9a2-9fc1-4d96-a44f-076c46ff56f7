package com.bilibili.crm.platform.biz.extractor;

import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.springframework.data.elasticsearch.core.ResultsExtractor;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

public class DssAdOverviewExtractor implements ResultsExtractor<BigDecimal> {
    @Override
    public BigDecimal extract(SearchResponse searchResponse) {
        List<Aggregation> list = searchResponse.getAggregations().asList();
        if (CollectionUtils.isEmpty(list)) {
            return BigDecimal.ZERO;
        }
        Sum sum = searchResponse.getAggregations().get("pvAgg");
        return BigDecimal.valueOf(sum.getValue());
    }
}
