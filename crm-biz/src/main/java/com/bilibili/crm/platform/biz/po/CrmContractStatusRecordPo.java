package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmContractStatusRecordPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 合同id
     */
    private Integer crmContractId;

    /**
     * 0编辑中、1待审核、2已拒绝、3审核通过、4执行中、5可执行完成、6待收款、7已完成、8禁用
     */
    private Integer status;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * 操作人姓名
     */
    private String operator;

    private Timestamp ctime;

    private Timestamp mtime;

    /**
     * 软删除0未删除1 删除
     */
    private Integer isDeleted;

    /**
     * 0:等待中, 1:执行中, 2:可执行完成, 3:待收款, 4:已完成, 5:已关闭
     */
    private Integer busStatus;

    /**
     * 0:编辑中, 1:待审核, 2:审核通过, 3:审核拒绝, 4:已关闭
     */
    private Integer auditStatus;

    private static final long serialVersionUID = 1L;
}