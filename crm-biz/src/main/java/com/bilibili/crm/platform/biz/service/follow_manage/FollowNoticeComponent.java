package com.bilibili.crm.platform.biz.service.follow_manage;

import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityQueryDto;
import com.bilibili.crm.platform.api.enums.FollowPlannerStatus;
import com.bilibili.crm.platform.api.follow_manage.dto.*;
import com.bilibili.crm.platform.api.follow_manage.enums.PlannerTypeEnum;
import com.bilibili.crm.platform.api.non.standard.dto.ProjectItemPackageDto;
import com.bilibili.crm.platform.api.non.standard.service.INonStandardPackageService;
import com.bilibili.crm.platform.biz.repo.follow_record.FollowManageCustomerRepo;
import com.bilibili.crm.platform.biz.repo.follow_record.FollowManageRecordRepo;
import com.bilibili.crm.platform.biz.repo.follow_record.FollowRecordBizMappingRepo;
import com.bilibili.crm.platform.biz.service.opportunity.BsiOpportunityServiceImpl;
import com.bilibili.crm.platform.biz.service.contract.component.ContractBiiPeriodAlertComponent;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections.MapUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-10-31 19:41:34
 * @description:
 **/

@Component
public class FollowNoticeComponent {

    @Resource
    private ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent;

    @Resource
    private FollowManageRecordRepo followManageRecordRepo;

    @Resource
    private FollowManageCustomerRepo followManageCustomerRepo;

    @Resource
    private FollowRecordBizMappingRepo followRecordBizMappingRepo;

    @Resource
    private INonStandardPackageService nonStandardPackageService;

    @Resource
    private BsiOpportunityServiceImpl bsiOpportunityService;

    @Value("${follow.record.planner.wechat.url:http://cm.bilibili.com/ldad/opportunity/main.html#/follow/action/view?id=%s}")
    private String wechatUrl;

    public void confirmSend(Map<Integer, List<FollowManagePlannerDto>> oldPlanner, Map<Integer, List<FollowManagePlannerDto>> newPlanner, Long recordId, FollowPlannerStatus bsiOppPlannerStatus, String rejectReason) {
        if (MapUtils.isEmpty(oldPlanner) && MapUtils.isEmpty(newPlanner)) {
            return;
        }
        List<BsiOpportunityDto> bsiOpsDtoList = new ArrayList<>();
        Map<Integer, ProjectItemPackageDto> packageDtoMap = Maps.newHashMap();
        List<FollowManageRecordDto> recordDtoList = followManageRecordRepo.queryList(FollowManageQueryDto.builder().recordId(recordId).build());
        Map<Long, List<BsiOppBriefDto>> bsiMap = followRecordBizMappingRepo.queryBsiOps(Lists.newArrayList(recordId));
        Map<Long, List<Integer>> projectMap = followRecordBizMappingRepo.queryProjectItem(Lists.newArrayList(recordId));
        String packageName = "";
        String bsiName = "";
        if (!CollectionUtils.isEmpty(projectMap.get(recordId))) {
            packageDtoMap = nonStandardPackageService.queryPackageByIdList(projectMap.get(recordId));
            packageName = packageDtoMap.values().stream().map(ProjectItemPackageDto::getName).collect(Collectors.joining(","));
        }
        if (!CollectionUtils.isEmpty(bsiMap.get(recordId))) {
            bsiOpsDtoList = bsiOpportunityService.queryBsiOpportunityList(BsiOpportunityQueryDto.builder()
                    .ids(bsiMap.get(recordId).stream().map(BsiOppBriefDto::getBsiOpportunityId).collect(Collectors.toList()))
                    .build());
            bsiName = bsiOpsDtoList.stream().map(a -> String.format("%s(%s)", a.getName(), a.getFollowStage() + "%")).collect(Collectors.joining(","));
        }
        FollowManageRecordDto recordDto = recordDtoList.get(0);
        List<FollowManageCustomerDto> customerDtoList = followManageCustomerRepo.queryList(FollowManageQueryDto.builder().customerRecordIds(Lists.newArrayList(recordDto.getCustomerRecordId())).build());
        FollowManageCustomerDto customerDto = customerDtoList.get(0);
        List<String> specialEmailList = newPlanner.getOrDefault(PlannerTypeEnum.SPECIALIST.getCode(), new ArrayList<>()).stream().map(FollowManagePlannerDto::getEmail).collect(Collectors.toList());
        List<String> leaderEmailList = newPlanner.getOrDefault(PlannerTypeEnum.LEADER.getCode(), new ArrayList<>()).stream().map(FollowManagePlannerDto::getEmail).collect(Collectors.toList());
        List<String> oldSpecialEmailList = oldPlanner.getOrDefault(PlannerTypeEnum.SPECIALIST.getCode(), new ArrayList<>()).stream().map(FollowManagePlannerDto::getEmail).collect(Collectors.toList());
        String leaderName = newPlanner.getOrDefault(PlannerTypeEnum.LEADER.getCode(), new ArrayList<>()).stream().map(FollowManagePlannerDto::getName).collect(Collectors.joining(","));
        String specialName = newPlanner.getOrDefault(PlannerTypeEnum.SPECIALIST.getCode(), new ArrayList<>()).stream().map(FollowManagePlannerDto::getName).collect(Collectors.joining(","));
        String oldSpecialName = oldPlanner.getOrDefault(PlannerTypeEnum.SPECIALIST.getCode(), new ArrayList<>()).stream().map(FollowManagePlannerDto::getName).collect(Collectors.joining(","));
        if (bsiOppPlannerStatus.equals(FollowPlannerStatus.RECEIPT_ORDER)) {
            if (CollectionUtils.isEmpty(oldPlanner.get(PlannerTypeEnum.SPECIALIST.getCode()))) {
                String changeWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                                "您好，您有1条新的策划需求确认接单<br/>" +
                                "<br/>" +
                                "跟进记录ID：%s<br/>" +
                                "提交人：%s<br/>" +
                                "客户名称：%s<br/>" +
                                "跟进项目：%s<br/>" +
                                "跟进商机：%s<br/>" +
                                "分配人：%s<br/>" +
                                "策划专员：%s<br/>" +
                                "<br/>" +
                                "<a href=\"%s\">【查看详情】</a>",
                        recordId,
                        recordDto.getCreatorName(),
                        customerDto.getCustomerName(),
                        packageName,
                        bsiName,
                        leaderName,
                        specialName,
                        String.format(wechatUrl, recordId));
                List<String> sendEmail = Lists.newArrayList();
                sendEmail.addAll(specialEmailList);
                sendEmail.add(recordDto.getCreatorEmail());
                contractBiiPeriodAlertComponent.sendQiwe(sendEmail, changeWxContent);
                String leaderWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                                "您好，以下客户已成功分配至:%s<br/>" +
                                "<br/>" +
                                "跟进记录ID：%s<br/>" +
                                "提交人：%s<br/>" +
                                "客户名称：%s<br/>" +
                                "跟进项目：%s<br/>" +
                                "跟进商机：%s<br/>" +
                                "<br/>" +
                                "<a href=\"%s\">【查看详情】</a>",
                        specialName,
                        recordId,
                        recordDto.getCreatorName(),
                        customerDto.getCustomerName(),
                        packageName,
                        bsiName,
                        String.format(wechatUrl, recordId));
                contractBiiPeriodAlertComponent.sendQiwe(leaderEmailList, leaderWxContent);
            } else {
                List<String> specialEmailCopy = new ArrayList<>();
                specialEmailCopy.addAll(specialEmailList);
                List<String> oldSpecialEmailCopy = new ArrayList<>();
                oldSpecialEmailCopy.addAll(oldSpecialEmailList);
                oldSpecialEmailCopy.removeAll(specialEmailCopy);
                specialEmailCopy.removeAll(oldSpecialEmailCopy);
                List<String> repeatList = Lists.newArrayList();
                repeatList.addAll(specialEmailCopy);
                repeatList.retainAll(oldSpecialEmailCopy);
                specialEmailCopy.removeAll(repeatList);
                oldSpecialEmailCopy.removeAll(repeatList);
                if (!CollectionUtils.isEmpty(specialEmailCopy)) {
                    String changeWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                                    "您好，您有1条新的策划需求确认接单<br/>" +
                                    "<br/>" +
                                    "跟进记录ID：%s<br/>" +
                                    "提交人：%s<br/>" +
                                    "客户名称：%s<br/>" +
                                    "跟进项目：%s<br/>" +
                                    "跟进商机：%s<br/>" +
                                    "分配人：%s<br/>" +
                                    "策划专员：%s<br/>" +
                                    "<br/>" +
                                    "<a href=\"%s\">【查看详情】</a>",
                            recordId,
                            recordDto.getCreatorName(),
                            customerDto.getCustomerName(),
                            packageName,
                            bsiName,
                            leaderName,
                            specialName,
                            String.format(wechatUrl, recordId));
                    contractBiiPeriodAlertComponent.sendQiwe(specialEmailCopy, changeWxContent);
                }
                if (!CollectionUtils.isEmpty(oldSpecialEmailCopy)) {
                    String changeWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                                    "您好，您有1条策划需求取消接单，该条记录将从您的【客户跟进＆拜访管理】模块中删除，请知晓<br/>" +
                                    "<br/>" +
                                    "跟进记录ID：%s<br/>" +
                                    "提交人：%s<br/>" +
                                    "客户名称：%s<br/>" +
                                    "跟进项目：%s<br/>" +
                                    "跟进商机：%s<br/>" +
                                    "分配人：%s<br/>" +
                                    "<br/>" +
                                    "<a href=\"%s\">【查看详情】</a>",
                            recordId,
                            recordDto.getCreatorName(),
                            customerDto.getCustomerName(),
                            packageName,
                            bsiName,
                            leaderName,
                            String.format(wechatUrl, recordId));
                    contractBiiPeriodAlertComponent.sendQiwe(oldSpecialEmailList, changeWxContent);
                }
                String creatorWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                                "您好，您有1条的跟进记录变更策划，请知晓<br/>" +
                                "<br/>" +
                                "跟进记录ID：%s<br/>" +
                                "提交人：%s<br/>" +
                                "客户名称：%s<br/>" +
                                "跟进项目：%s<br/>" +
                                "跟进商机：%s<br/>" +
                                "分配人：%s<br/>" +
                                "策划专员：%s<br/>" +
                                "<br/>" +
                                "<a href=\"%s\">【查看详情】</a>",
                        recordId,
                        recordDto.getCreatorName(),
                        customerDto.getCustomerName(),
                        packageName,
                        bsiName,
                        leaderName,
                        specialName,
                        String.format(wechatUrl, recordId));
                contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(recordDto.getCreatorEmail()), creatorWxContent);
                String leaderWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                                "您好，以下客户的协作人，已从%s修改为至%s<br/>" +
                                "<br/>" +
                                "跟进记录ID：%s<br/>" +
                                "提交人：%s<br/>" +
                                "客户名称：%s<br/>" +
                                "跟进项目：%s<br/>" +
                                "跟进商机：%s<br/>" +
                                "<br/>" +
                                "<a href=\"%s\">【查看详情】</a>",
                        oldSpecialName,
                        specialName,
                        recordId,
                        recordDto.getCreatorName(),
                        customerDto.getCustomerName(),
                        packageName,
                        bsiName,
                        String.format(wechatUrl, recordId));
                contractBiiPeriodAlertComponent.sendQiwe(leaderEmailList, leaderWxContent);
            }
        } else if (bsiOppPlannerStatus.equals(FollowPlannerStatus.HAS_REJECTED)) {
            String leaderWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                            "您好，以下策划需求已被驳回，驳回原因：%s<br/>" +
                            "<br/>" +
                            "跟进记录ID：%s<br/>" +
                            "提交人：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "跟进项目：%s<br/>" +
                            "跟进商机：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    rejectReason,
                    recordId,
                    recordDto.getCreatorName(),
                    customerDto.getCustomerName(),
                    packageName,
                    bsiName,
                    String.format(wechatUrl, recordId));
            contractBiiPeriodAlertComponent.sendQiwe(leaderEmailList, leaderWxContent);
            String creatorWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                            "您好，您有1条待分配策划的跟进记录被驳回，请及时处理<br/>" +
                            "<br/>" +
                            "跟进记录ID：%s<br/>" +
                            "提交人：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "跟进项目：%s<br/>" +
                            "跟进商机：%s<br/>" +
                            "分配人：%s<br/>" +
                            "驳回原因：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    recordId,
                    recordDto.getCreatorName(),
                    customerDto.getCustomerName(),
                    packageName,
                    bsiName,
                    leaderName,
                    recordDto.getPlannerRejectReason(),
                    String.format(wechatUrl, recordId));
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(recordDto.getCreatorEmail()), creatorWxContent);
        } else if (bsiOppPlannerStatus.equals(FollowPlannerStatus.TO_BE_ALLOCATE)) {
            String creatorWxContent = String.format("【跟进记录-策划协作状态更新】<br/>" +
                            "您好，您有1条新的策划需求待分配<br/>" +
                            "<br/>" +
                            "跟进记录ID：%s<br/>" +
                            "提交人：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "跟进项目：%s<br/>" +
                            "跟进商机：%s<br/>" +
                            "分配人：%s<br/>" +
                            "策划专员：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    recordId,
                    recordDto.getCreatorName(),
                    customerDto.getCustomerName(),
                    packageName,
                    bsiName,
                    leaderName,
                    specialName,
                    String.format(wechatUrl, recordId));
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(leaderEmailList), creatorWxContent);
        }
    }
}
