package com.bilibili.crm.platform.biz.repo;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerQualificationInfoDao;
import com.bilibili.crm.platform.biz.po.CustomerQualificationInfoPo;
import com.bilibili.crm.platform.biz.po.CustomerQualificationInfoPoExample;
import com.google.common.base.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;

/**
 * 客户营业执照相关 repo
 *
 * <AUTHOR>
 * @date 2020/9/27 9:33 下午
 */
@Repository
public class CustomerQualificationInfoRepo {

    @Autowired
    private CustomerQualificationInfoDao customerQualificationInfoDao;

    /**
     * 根据客户id查询执照信息
     *
     * @param customerId
     * @return
     */
    public List<CustomerQualificationInfoPo> queryQualificationInfosByCustomerId(Integer customerId) {
        if (customerId == null) {
            throw new ServiceRuntimeException("customer id can't be null!");
        }
        CustomerQualificationInfoPoExample qualificationInfoPoExample = new CustomerQualificationInfoPoExample();
        CustomerQualificationInfoPoExample.Criteria criteria =
                qualificationInfoPoExample.or().andCustomerIdEqualTo(customerId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<CustomerQualificationInfoPo> customerQualificationInfoPos = customerQualificationInfoDao.selectByExample(qualificationInfoPoExample);
        return customerQualificationInfoPos;
    }

    /**
     * 根据客户id查询执照信息
     *
     * @param customerIds
     * @return
     */
    public List<CustomerQualificationInfoPo> queryQualificationInfosByCustomerIds(List<Integer> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            throw new ServiceRuntimeException("customer id can't be null!");
        }
        CustomerQualificationInfoPoExample qualificationInfoPoExample = new CustomerQualificationInfoPoExample();
        CustomerQualificationInfoPoExample.Criteria criteria =
                qualificationInfoPoExample.or().andCustomerIdIn(customerIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<CustomerQualificationInfoPo> customerQualificationInfoPos = customerQualificationInfoDao.selectByExample(qualificationInfoPoExample);
        return customerQualificationInfoPos;
    }

    /**
     * 根据营业执照 code 查询所有的列表
     *
     * @param businessLicenceCode
     * @return
     */
    public List<CustomerQualificationInfoPo> queryAllByLikeBusinessLicenceCode(String businessLicenceCode) {
        if (StringUtils.isEmpty(businessLicenceCode)) {
            throw new ServiceRuntimeException("营业执照编号不能为空！");
        }
        CustomerQualificationInfoPoExample qualificationInfoPoExample = new CustomerQualificationInfoPoExample();
        qualificationInfoPoExample.or().andBusinessLicenceCodeLike("%" + businessLicenceCode + "%");
        List<CustomerQualificationInfoPo> qualificationInfoPos = customerQualificationInfoDao.selectByExample(qualificationInfoPoExample);
        return qualificationInfoPos;
    }

    /**
     * 根据个人身份证号 查询所有的列表
     *
     * @param personalIdCardNumber
     * @return
     */
    public List<CustomerQualificationInfoPo> queryAllByLikePersonalIdCardNumber(String personalIdCardNumber) {
        if (StringUtils.isEmpty(personalIdCardNumber)) {
            throw new ServiceRuntimeException("身份证号不能为空！");
        }
        CustomerQualificationInfoPoExample qualificationInfoPoExample = new CustomerQualificationInfoPoExample();
        qualificationInfoPoExample.or().andPersonalIdCardNumberLike("%" + personalIdCardNumber + "%");
        List<CustomerQualificationInfoPo> qualificationInfoPos = customerQualificationInfoDao.selectByExample(qualificationInfoPoExample);
        return qualificationInfoPos;
    }

    /**
     * 根据 business code 查询客户资质列表(根据营业执照code或身份证号)
     * 内部逻辑：如果 营业执照 code 存在，则根据 营业执照code 查询；否则根据身份证号查询
     *
     * @param businessLicenceCode
     * @param personalIdCardNumber
     * @return
     */
    public List<CustomerQualificationInfoPo> queryByCode(String businessLicenceCode, String personalIdCardNumber) {
        if (StringUtils.isEmpty(businessLicenceCode) && StringUtils.isEmpty(personalIdCardNumber)) {
            throw new ServiceRuntimeException("营业执照编号与身份证号不能同时为空！");
        }

        CustomerQualificationInfoPoExample qualificationInfoPoExample = new CustomerQualificationInfoPoExample();
        CustomerQualificationInfoPoExample.Criteria criteria = qualificationInfoPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        // 如果 营业执照 code 存在，则根据 营业执照code 查询；否则根据身份证号查询
        boolean isPersonFlag = Strings.isNullOrEmpty(businessLicenceCode);
        if (isPersonFlag) {
            criteria.andPersonalIdCardNumberEqualTo(personalIdCardNumber);
        } else {
            criteria.andBusinessLicenceCodeEqualTo(businessLicenceCode);
        }

        List<CustomerQualificationInfoPo> qualificationInfoPos = customerQualificationInfoDao.selectByExample(qualificationInfoPoExample);
        return qualificationInfoPos;
    }
}
