package com.bilibili.crm.platform.biz.service.return_online.effect.dto;

import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentUploadDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2023/1/29 14:32
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UploadReturnMoneyDto {

    /**
     * 返现录入文件
     */
    private CommonAttachmentUploadDto attachment;
}
