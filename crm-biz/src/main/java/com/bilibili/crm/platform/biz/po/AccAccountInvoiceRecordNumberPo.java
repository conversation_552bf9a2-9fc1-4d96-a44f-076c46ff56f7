package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
public class AccAccountInvoiceRecordNumberPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 开票记录id
     */
    private Integer invoiceRecordId;

    /**
     * 发票号码
     */
    private String invoiceNumber;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除0未删除1 删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getInvoiceRecordId() {
        return invoiceRecordId;
    }

    public void setInvoiceRecordId(Integer invoiceRecordId) {
        this.invoiceRecordId = invoiceRecordId;
    }

    public String getInvoiceNumber() {
        return invoiceNumber;
    }

    public void setInvoiceNumber(String invoiceNumber) {
        this.invoiceNumber = invoiceNumber;
    }

    public Timestamp getCtime() {
        return ctime;
    }

    public void setCtime(Timestamp ctime) {
        this.ctime = ctime;
    }

    public Timestamp getMtime() {
        return mtime;
    }

    public void setMtime(Timestamp mtime) {
        this.mtime = mtime;
    }

    public Integer getIsDeleted() {
        return isDeleted;
    }

    public void setIsDeleted(Integer isDeleted) {
        this.isDeleted = isDeleted;
    }
}