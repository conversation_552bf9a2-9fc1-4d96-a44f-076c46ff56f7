package com.bilibili.crm.platform.biz.exception.wx;

import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/4/1
 **/
@NoArgsConstructor
@AllArgsConstructor
public class WxAppException extends RuntimeException {

    @Getter
    private Integer code;

    @Getter
    private String message;

    @Getter
    private Integer source;

    public WxAppException (WxAppCrmExceptionCode exceptionCode) {

        this.code = exceptionCode.getCode();
        this.message = exceptionCode.getMessage();
        this.source = WxAppExceptionSource.CRM.getCode();
    }

    public WxAppException (WxAppCrmExceptionCode exceptionCode,String message) {

        this.code = exceptionCode.getCode();
        this.message =message;
        this.source = WxAppExceptionSource.CRM.getCode();
    }
}
