package com.bilibili.crm.platform.biz.service.wallet.trade.cash;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.wallet.dto.AccountWalletTradeDto;
import com.bilibili.crm.platform.biz.po.AccAccountWalletPo;
import com.bilibili.crm.platform.biz.service.wallet.trade.AbstractAccountWalletTradeService;
import com.bilibili.crm.platform.common.wallet.WalletTradeAction;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

/**
 * @author: brady
 * @time: 2021/9/1 11:39 上午
 */
@Service
public class CashRefundService extends AbstractAccountWalletTradeService {
    @Override
    public WalletTradeAction tradeRoute() {
        return WalletTradeAction.CASH_REFUND;
    }

    @Override
    protected void validateParams(AccountWalletTradeDto tradeDto) {
        Assert.isTrue(Utils.isPositive(tradeDto.getAmount()), "退款金额必须大于0");
    }

    @Override
    public AccAccountWalletPo buildWalletChange(AccAccountWalletPo originWalletPo, AccountWalletTradeDto tradeDto) {
        Long cash = originWalletPo.getCash() - tradeDto.getAmount();
        Long totalCashRecharge = originWalletPo.getTotalCashRecharge() - tradeDto.getAmount();
        Assert.isTrue(cash >= 0, "现金余额不足, 无法退款");

        AccAccountWalletPo record = AccAccountWalletPo.builder()
                .accountId(tradeDto.getAccountId())
                .cash(cash)
                .totalCashRecharge(totalCashRecharge)
                .version(originWalletPo.getVersion() + 1)
                .build();
        return record;
    }
}
