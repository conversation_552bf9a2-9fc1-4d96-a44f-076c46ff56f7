package com.bilibili.crm.platform.biz.repo;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.SnowflakeIdWorker;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.dao.CrmCommonnAttachmentDao;
import com.bilibili.crm.platform.biz.po.CrmCommonnAttachmentPo;
import com.bilibili.crm.platform.biz.po.CrmCommonnAttachmentPoExample;
import com.bilibili.crm.platform.biz.repo.attachment.CrmCommonAttachmentDto;
import com.bilibili.crm.platform.biz.repo.attachment.convert.CrmCommonAttachmentConvert;
import com.bilibili.crm.platform.common.AttachmentUploadBizModuleEnum;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/3/11 1:39 下午
 */
@Repository
public class CrmCommonAttachmentRepo {

    @Autowired
    private CrmCommonnAttachmentDao crmCommonnAttachmentDao;
    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    /**
     * 新增
     *
     * @param attachmentPo
     */
    public void addSelective(CrmCommonnAttachmentPo attachmentPo) {
        crmCommonnAttachmentDao.insertSelective(attachmentPo);
    }

    public void addSelective(CrmCommonAttachmentDto dto) {
        dto.setNumber(snowflakeIdWorker.nextId());
        crmCommonnAttachmentDao.insertSelective(CrmCommonAttachmentConvert.convert(dto));
    }

    /**
     * 批量新增
     *
     * @param attachmentPos
     */
    public Integer batchAdd(List<CrmCommonnAttachmentPo> attachmentPos) {
        if (CollectionUtils.isEmpty(attachmentPos)) {
            return 0;
        }
        // 设置 number
        attachmentPos.forEach(t -> {
                    t.setNumber(snowflakeIdWorker.nextId());
                    if (null == t.getVersion()) {
                        t.setVersion(1);
                    }
                }
        );
        return crmCommonnAttachmentDao.insertBatch(attachmentPos);
    }

    public void batchAddDtos(List<CrmCommonAttachmentDto> attachmentDtos) {
        if (CollectionUtils.isEmpty(attachmentDtos)) {
            return;
        }
        // 设置 number
        List<CrmCommonnAttachmentPo> attachmentPos = attachmentDtos.stream().map(t -> {
            t.setNumber(snowflakeIdWorker.nextId());
            return CrmCommonAttachmentConvert.convert(t);
        }).collect(Collectors.toList());
        crmCommonnAttachmentDao.insertBatch(attachmentPos);
    }

    /**
     * 批量更新
     *
     * @param attachmentPos
     * @return
     */
    public Integer batchUpdate(List<CrmCommonnAttachmentPo> attachmentPos) {
        if (CollectionUtils.isEmpty(attachmentPos)) {
            return 0;
        }
        for (CrmCommonnAttachmentPo commonnAttachmentPo : attachmentPos) {
            commonnAttachmentPo.setMtime(Utils.getNow());
            commonnAttachmentPo.setVersion(1);
        }
        Integer count = crmCommonnAttachmentDao.insertUpdateBatch(attachmentPos);
        return count;
    }

    public CrmCommonnAttachmentPo queryInfoById(Integer attachmentId) {
        Assert.isTrue(Utils.isPositive(attachmentId), "attachment id 不能为空！");

        CrmCommonnAttachmentPo crmCommonnAttachmentPo = crmCommonnAttachmentDao.selectByPrimaryKey(attachmentId);
        return crmCommonnAttachmentPo;
    }

    public List<CrmCommonnAttachmentPo> queryList(Integer refId, AttachmentUploadBizModuleEnum bizModuleType) {
        return queryList(Lists.newArrayList(refId), bizModuleType.getModuleCode(), bizModuleType.getSubBizCode());
    }

    public List<CrmCommonnAttachmentPo> queryList(List<Integer> refIds, Integer bizModuleType, Integer subBizType) {
        Assert.isTrue(Utils.isPositive(bizModuleType), "biz module type 不能为空！");
        Assert.isTrue(Utils.isPositive(subBizType), "sub biz type 不能为空！");
        if (CollectionUtils.isEmpty(refIds)) {
            return Collections.EMPTY_LIST;
        }

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdIn(refIds).andBizModuleEqualTo(bizModuleType)
                .andSubBizTypeEqualTo(subBizType);
        return crmCommonnAttachmentDao.selectByExample(example);
    }

    public List<CrmCommonnAttachmentPo> queryList(Integer refId, Integer bizModuleType, Integer subBizType, Integer uploadType) {
        Assert.isTrue(Utils.isPositive(refId), "ref id 不能为空！");
        Assert.isTrue(Utils.isPositive(bizModuleType), "biz module type 不能为空！");
        Assert.isTrue(Utils.isPositive(subBizType), "sub biz type 不能为空！");
        Assert.isTrue(Utils.isPositive(uploadType), "upload type 不能为空！");

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdEqualTo(refId).andBizModuleEqualTo(bizModuleType)
                .andSubBizTypeEqualTo(subBizType)
                .andUploadTypeEqualTo(uploadType);
        return crmCommonnAttachmentDao.selectByExample(example);
    }

    public List<CrmCommonnAttachmentPo> queryList(Integer refIds, Integer bizModuleType) {
        Map<Integer, List<CrmCommonnAttachmentPo>> attachmentMap = queryList(Lists.newArrayList(refIds), bizModuleType);

        return attachmentMap.getOrDefault(refIds, Collections.emptyList());
    }

    public Map<Integer, List<CrmCommonnAttachmentPo>> queryList(List<Integer> refIds, Integer bizModuleType) {
        Assert.isTrue(Utils.isPositive(bizModuleType), "biz module type 不能为空！");
        if (CollectionUtils.isEmpty(refIds)) {
            return Collections.emptyMap();
        }

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdIn(refIds).andBizModuleEqualTo(bizModuleType).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<CrmCommonnAttachmentPo> crmCommonAttachmentPos = crmCommonnAttachmentDao.selectByExample(example);

        if (CollectionUtils.isEmpty(crmCommonAttachmentPos)) {
            return Collections.emptyMap();
        }

        return crmCommonAttachmentPos.stream().collect(Collectors.groupingBy(CrmCommonnAttachmentPo::getRefId));
    }

    public List<CrmCommonnAttachmentPo> queryListByIds(List<Integer> ids, Integer bizModuleType, Integer subBizType,
                                                       Integer uploadType) {
        Assert.isTrue(Utils.isPositive(bizModuleType), "biz module type 不能为空！");
        Assert.isTrue(Utils.isPositive(subBizType), "sub biz type 不能为空！");
        Assert.isTrue(Utils.isPositive(uploadType), "upload type 不能为空！");
        if (CollectionUtils.isEmpty(ids)) {
            return Collections.EMPTY_LIST;
        }

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andIdIn(ids).andBizModuleEqualTo(bizModuleType)
                .andSubBizTypeEqualTo(subBizType)
                .andUploadTypeEqualTo(uploadType);
        return crmCommonnAttachmentDao.selectByExample(example);
    }

    public Integer delete(Integer refId, Integer bizModuleType, Integer subBizType) {
        Assert.isTrue(Utils.isPositive(refId), "ref id 不能为空！");
        Assert.isTrue(Utils.isPositive(bizModuleType), "biz module type 不能为空！");
        Assert.isTrue(Utils.isPositive(subBizType), "sub biz type 不能为空！");

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdEqualTo(refId).andBizModuleEqualTo(bizModuleType)
                .andSubBizTypeEqualTo(subBizType);
        return crmCommonnAttachmentDao.deleteByExample(example);
    }

    public Integer deleteForMultiRefIds(List<Integer> refIds, Integer bizModuleType, Integer subBizType) {
        Assert.isTrue(!CollectionUtils.isEmpty(refIds), "refIds 不能为空！");
        Assert.isTrue(Utils.isPositive(bizModuleType), "biz module type 不能为空！");
        Assert.isTrue(Utils.isPositive(subBizType), "sub biz type 不能为空！");

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdIn(refIds).andBizModuleEqualTo(bizModuleType)
                .andSubBizTypeEqualTo(subBizType);
        return crmCommonnAttachmentDao.deleteByExample(example);
    }

    public Integer delete(Integer refId, AttachmentUploadBizModuleEnum bizModuleType) {
        return this.delete(refId, bizModuleType.getModuleCode(), bizModuleType.getSubBizCode());
    }

    public List<CrmCommonAttachmentDto> queryByIdVersion(Integer refId, Integer version, List<Integer> moduleCodes) {
        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        CrmCommonnAttachmentPoExample.Criteria criteria = example.createCriteria().andRefIdEqualTo(refId);

        if (null != version) {
            criteria.andVersionEqualTo(version);
        }

        if (!CollectionUtils.isEmpty(moduleCodes)) {
            criteria.andBizModuleIn(moduleCodes);
        }

        List<CrmCommonnAttachmentPo> pos = crmCommonnAttachmentDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(CrmCommonAttachmentConvert::convert).collect(Collectors.toList());
    }

    public void updateById(List<CrmCommonAttachmentDto> dtos) {
        if (CollectionUtils.isEmpty(dtos)) {
            return;
        }
        dtos.forEach(dto -> {
            CrmCommonnAttachmentPo attachmentPo = CrmCommonAttachmentConvert.convert(dto);
            crmCommonnAttachmentDao.updateByPrimaryKeySelective(attachmentPo);
        });
    }

    public List<CrmCommonnAttachmentPo> queryByRefIdVersion(Integer refId, List<Integer> versions, AttachmentUploadBizModuleEnum uploadBizModuleEnum) {
        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdEqualTo(refId)
                .andVersionIn(versions)
                .andBizModuleEqualTo(uploadBizModuleEnum.getModuleCode())
                .andSubBizTypeEqualTo(uploadBizModuleEnum.getSubBizCode());
        return crmCommonnAttachmentDao.selectByExample(example);
    }

    public Integer batchDelete(List<Integer> refIds, List<Integer> subBizModule, Integer version) {
        Assert.isTrue(!CollectionUtils.isEmpty(refIds), "refIds 不能为空！");
        Assert.isTrue(!CollectionUtils.isEmpty(subBizModule), "subBiz module type 不能为空！");
        Assert.isTrue(null != version, "version 不能为空！");


        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdIn(refIds).andVersionEqualTo(version).andSubBizTypeIn(subBizModule);
        return crmCommonnAttachmentDao.deleteByExample(example);
    }

    public void deleteByVersion(Integer refId, AttachmentUploadBizModuleEnum bizModuleType, Integer version) {
        Assert.isTrue(Utils.isPositive(refId), "ref id 不能为空！");

        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        example.createCriteria().andRefIdEqualTo(refId).andBizModuleEqualTo(bizModuleType.getModuleCode())
                .andSubBizTypeEqualTo(bizModuleType.getSubBizCode())
                .andVersionEqualTo(version);
        crmCommonnAttachmentDao.deleteByExample(example);
    }

    public List<CrmCommonAttachmentDto> queryByIdVersionModule(Integer refId, Integer version, Integer module, Integer subBizType) {
        CrmCommonnAttachmentPoExample example = new CrmCommonnAttachmentPoExample();
        CrmCommonnAttachmentPoExample.Criteria criteria = example.createCriteria().andRefIdEqualTo(refId);

        if (null != version) {
            criteria.andVersionEqualTo(version);
        }

        if (null != module) {
            criteria.andBizModuleEqualTo(module);
        }

        if (null != subBizType) {
            criteria.andSubBizTypeEqualTo(subBizType);
        }

        List<CrmCommonnAttachmentPo> pos = crmCommonnAttachmentDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(CrmCommonAttachmentConvert::convert).collect(Collectors.toList());
    }
}
