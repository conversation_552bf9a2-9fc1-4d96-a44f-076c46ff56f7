package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmSaleSeaMappingLogPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 销售ID
     */
    private Integer saleId;

    /**
     * 绑定类型(0:集团, 1:客户, 2:帐号)
     */
    private Integer mappingType;

    /**
     * 绑定ID
     */
    private Integer mappingId;

    /**
     * 公私海产品分类 (0:品牌, 1:效果, 2:其他, 详见SaleSeaProductCategory)
     */
    private Integer saleSeaProductCategory;

    /**
     * 绑定日期
     */
    private Timestamp bindingDate;

    /**
     * 软删除 0 否 1是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}