package com.bilibili.crm.platform.biz.service.cost_management;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.*;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.commercialorder.soa.order.dto.OrderInfoForCrmSynDto;
import com.bilibili.commercialorder.soa.order.dto.QueryOrderInfoForCrmReqDto;
import com.bilibili.commercialorder.soa.order.service.ISoaCmOrderService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.cost_management.dto.*;
import com.bilibili.crm.platform.api.cost_management.service.IBTPIntegrationService;
import com.bilibili.crm.platform.api.expenditure.dto.*;
import com.bilibili.crm.platform.api.expenditure.service.IExpenditureService;
import com.bilibili.crm.platform.api.expenditure.service.IExpenditureTypeService;
import com.bilibili.crm.platform.api.non.standard.dto.ProjectItemPackageDto;
import com.bilibili.crm.platform.api.non.standard.service.INonStandardPackageService;
import com.bilibili.crm.platform.biz.annotation.ObjectParamCache;
import com.bilibili.crm.platform.biz.dao.*;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmExpenditureDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.po.expenditure.CrmExpenditurePo;
import com.bilibili.crm.platform.biz.po.expenditure.CrmExpenditurePoExample;
import com.bilibili.crm.platform.biz.service.expenditure.ExpenditureTypeService;
import com.bilibili.crm.platform.biz.util.BatchUtil;
import com.bilibili.crm.platform.biz.util.ValUtil;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.ExpenditureSourceType;
import com.bilibili.crm.platform.common.ExpenditureStatus;
import com.bilibili.crm.platform.common.NewExpenditureAuditStatus;
import com.bilibili.crm.platform.utils.PriceMathUtils;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.DeserializationFeature;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.google.common.base.Joiner;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.SneakyThrows;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.PredicateUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.apache.commons.lang3.tuple.Triple;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.MediaType;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Nullable;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.ZonedDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
public class BTPIntegrationService implements IBTPIntegrationService {
    private static final ObjectMapper OBJECT_MAPPER = new ObjectMapper().configure(DeserializationFeature.FAIL_ON_UNKNOWN_PROPERTIES, false);
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Value("${crm.btp.expenditure.second.type.code:79}")
    private Integer btpExpenditureSecondTypeCode;

    @Value("${crm.btp.game.department.id:4}")
    private Integer btpGameDepartmentId;

    @Value("${crm.btp.business.department.id:1}")
    private Integer btpBusinessDepartmentId;

    @Value("${crm.btp.contract.link.prefix:https://uat-cm-mng.bilibili.co/crm/#/contract/list/show/general/}")
    private String crmContractLinkPrefix;

    @Value("${crm.btp.excluded.expenditure.second.type.id: 20}")
    private Integer excludedExpenditureSecondTypeId;

    @Autowired
    private BTPConfig btpConfig;
    @Autowired
    private IExpenditureService iExpenditureService;
    @Autowired
    private CrmExpenditureDao crmExpenditureDao;
    @Autowired
    private IExpenditureTypeService iExpenditureTypeService;
    @Autowired
    private CrmOrderDao crmOrderDao;
    @Autowired
    private CrmOrderExtraDao crmOrderExtraDao;
    @Autowired
    private CrmProjectItemPackageDao crmProjectItemPackageDao;
    @Autowired
    private CrmContractDao crmContractDao;
    @Autowired
    private PrePayPickupOrderDao prePayPickupOrderDao;
    @Autowired
    private AccAccountDao accAccountDao;
    @Autowired
    private ExpenditureTypeService expenditureTypeService;
    @Autowired
    private ISoaCmOrderService iSoaCmOrderService;
    @Autowired
    private ExtCrmOrderDao extCrmOrderDao;
    @Autowired
    private INonStandardPackageService nonStandardPackageService;

    @SneakyThrows
    @Override
    public List<BTPProjectDto> getAllBTPProjectsByParam(BTPProjectPageRequestDto btpProjectPageRequestDto) {
        long pageSize = 50L;

        // 先查询一次，获取总分页数
        btpProjectPageRequestDto.setPageNum(1L);
        btpProjectPageRequestDto.setPageSize(pageSize);

        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getProjectListUrl())
                .json(GsonUtils.toJson(btpProjectPageRequestDto));
        BTPProjectPageResponseDto btpProjectPageResponseDto = OBJECT_MAPPER.readValue(bodyPoster.callForString(), BTPProjectPageResponseDto.class);

        List<BTPProjectDto> projectDtoList = new ArrayList<>(btpProjectPageResponseDto.getData().getBtpProjects());

        // 总分页数
        int pages = btpProjectPageResponseDto.getData().getPages();

        List<BTPProjectPageRequestDto> btpProjectPageRequestDtoList = new ArrayList<>();
        if (pages > 1) {
            for (int i = 2; i <= pages; i++) {
                BTPProjectPageRequestDto copiedReqDto = ValUtil.copyBean(btpProjectPageRequestDto, new BTPProjectPageRequestDto());
                copiedReqDto.setPageNum((long) i);
                copiedReqDto.setPageSize(pageSize);
                btpProjectPageRequestDtoList.add(copiedReqDto);
            }
        }

        List<BTPProjectDto> projectDtos = BatchUtil.parallel(3, btpProjectPageRequestDtoList, (reqDto) -> {
            OkHttpUtils.BodyPoster poster = OkHttpUtils.bodyPost(btpConfig.getProjectListUrl())
                    .json(GsonUtils.toJson(reqDto));
            try {
                BTPProjectPageResponseDto responseDto = OBJECT_MAPPER.readValue(poster.callForString(), BTPProjectPageResponseDto.class);
                AlarmHelper.log("parallel get btpProjects", reqDto, responseDto);
                return responseDto.getData().getBtpProjects();
            } catch (JsonProcessingException e) {
                throw new RuntimeException(e);
            }
        });
        projectDtoList.addAll(projectDtos);
        return projectDtoList;
    }

    @SneakyThrows
    @Override
    public List<BTPProjectDto> getAllApprovedBTPProjects() {
        return this.getAllBTPProjectsByParam(BTPProjectPageRequestDto.builder().build()).stream().filter(
                btpProjectDto -> BTPProjectDto.ProjectStatus.ONGOING.getCode() == Integer.parseInt(btpProjectDto.getProjectStatus())
        ).collect(Collectors.toList());
    }

    @SneakyThrows
    @Override
    public List<BTPPurchaseBatchDto> getBTPPurchaseBatchByCodeAndVersion(String btpProjectCode, String code, @Nullable Integer version) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("projectCode", btpProjectCode);
        requestBody.put("prCode", code);
        requestBody.put("prVersion", version);

        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getPrListUrl())
                .json(GsonUtils.toJson(requestBody));
        BTPPurchaseBatchResponseDto btpPurchaseBatchResponseDto = OBJECT_MAPPER.readValue(bodyPoster.callForString(), BTPPurchaseBatchResponseDto.class);

        btpPurchaseBatchResponseDto.getBtpPurchaseBatches().forEach(
                btpPurchaseBatchDto -> btpPurchaseBatchDto.setFlowOrderUrl(
                        buildBtpOaFlowOrderUrl(btpPurchaseBatchDto.getCode(), Integer.valueOf(btpPurchaseBatchDto.getPrVersion())))
        );
        return btpPurchaseBatchResponseDto.getBtpPurchaseBatches();
    }

    @Override
    public void syncBTPOaAuditResult(BTPOaAuditResultDto btpOaAuditResultDto) {
        log.info("btp pr audit result sync req: {}", btpOaAuditResultDto);

        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getPrAuditResultSyncUrl())
                .json(GsonUtils.toJson(btpOaAuditResultDto));
        String response = bodyPoster.callForString();
        log.info("btp pr audit result sync response: {}", response);
    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void updateBTPPurchaseBatch(BTPPurchaseBatchDto btpPurchaseBatchDto) {
        List<BTPPurchaseDetailDto> allDetails = btpPurchaseBatchDto.getBtpPurchaseDetails();
        List<BTPPurchaseDetailDto> addedDetails = new ArrayList<>();
        Map<BTPPurchaseDetailDto, ExpenditureDto> updatedDetails = new HashMap<>();

        String projectCode = btpPurchaseBatchDto.getProjectCode();
        String code = btpPurchaseBatchDto.getCode();
        Integer batchVersion = Integer.valueOf(btpPurchaseBatchDto.getPrVersion());

        allDetails.forEach(detail -> {
            Optional<ExpenditureDto> expenditureDtoOptional = iExpenditureService.getBTPExpenditureById(
                    BTPSourceCodeParam.builder()
                            .source(ExpenditureSourceType.BTP.getCode())
                            .btpProjectCode(projectCode)
                            .btpBatchCode(code)
                            .btpDetailCode(detail.getCode())
                            .build());

            if (expenditureDtoOptional.isPresent()) {
                updatedDetails.put(detail, expenditureDtoOptional.get());
            } else {
                addedDetails.add(detail);
            }
        });

        log.info("added: {}, updated: {}, btpPurchaseBatchDto: {}",
                addedDetails.stream().map(BTPPurchaseDetailDto::getCode).distinct().collect(Collectors.toList()),
                updatedDetails.keySet().stream().map(BTPPurchaseDetailDto::getCode).distinct().collect(Collectors.toList()),
                btpPurchaseBatchDto);

        Integer supplierDepartmentId = null;
        if (!CollectionUtils.isEmpty(btpPurchaseBatchDto.getSuppliers())) {
            if (btpPurchaseBatchDto.getSuppliers().stream().anyMatch(supplierDto -> supplierDto.getName().contains("游戏内容部")) ||
                    btpPurchaseBatchDto.getSuppliers().stream().anyMatch(supplierDto -> supplierDto.getName().contains("游戏商业生态部"))) {
                supplierDepartmentId = btpGameDepartmentId;
            } else {
                supplierDepartmentId = btpBusinessDepartmentId;
            }
        }
        if (StringUtils.isNotEmpty(btpPurchaseBatchDto.getCreatorDeptName())) {
            if (btpPurchaseBatchDto.getCreatorDeptName().contains("游戏内容部") ||
                    btpPurchaseBatchDto.getCreatorDeptName().contains("游戏商业生态部")) {
                supplierDepartmentId = btpGameDepartmentId;
            } else {
                supplierDepartmentId = btpBusinessDepartmentId;
            }
        }

        final Integer mappedCrmDepartmentId = supplierDepartmentId;
        addedDetails.forEach(
                detail -> {
                    ExpenditureDto expenditureDto = initBTPExpenditureDto(projectCode, code, batchVersion, mappedCrmDepartmentId, detail);
                    iExpenditureService.createBySource(expenditureDto, Operator.SYSTEM);
                }
        );

        updatedDetails.forEach(
                (detail, expenditure) -> {
                    Long totalCny = StringUtils.isBlank(detail.getTotalCny()) ? null : new BigDecimal(detail.getTotalCny()).multiply(BigDecimal.valueOf(100L)).longValue();
                    Pair<ExpenditureStatus, NewExpenditureAuditStatus> statuses = statusTransition(
                            expenditure.getId(),
                            expenditure.getBtpBatchCode(),
                            expenditure.getBtpBatchVersion(),
                            Integer.parseInt(btpPurchaseBatchDto.getPrVersion()),
                            expenditure.getExpenditureStatus(),
                            expenditure.getAuditStatus(),
                            btpPurchaseBatchDto.getStatus()
                    );

                    expenditure.setRemark(detail.getRequestExplain());
                    expenditure.setBtpTime(Timestamp.valueOf(ZonedDateTime.now().toLocalDateTime()));
                    expenditure.setPayFlag(null);
                    expenditure.setAdCostFlag(null);
                    expenditure.setBtpBatchVersion(batchVersion);
                    expenditure.setExpenditureStatus(statuses.getLeft().getCode());
                    expenditure.setAuditStatus(statuses.getRight().getCode());
                    expenditure.setBtpDetailTotalCny(totalCny);
                    expenditure.setPrice(totalCny);
                    expenditure.setBtpCategoryName(detail.getCategoryName()); //采购品类
                    expenditure.setBtpBudgetPurposeName(detail.getBudgetPurposeName()); //用途

                    // 更新绑定的crm order id
                    if (Utils.isPositive(detail.getCrmOrderId())) {
                        int crmOrderId = detail.getCrmOrderId().intValue();
                        expenditure.setCrmOrderId(crmOrderId);
                        Integer crmContractId = crmOrderDao.selectByPrimaryKey(crmOrderId).getCrmContractId();
                        expenditure.setCrmContractId(crmContractId);
                    }

                    iExpenditureService.edit(expenditure);
                }
        );

        // 处理删除的
        handleDeletedBtpDetails(btpPurchaseBatchDto);

    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void repairBTPPurchaseBatch(BTPPurchaseBatchDto btpPurchaseBatchDto) {
        List<BTPPurchaseDetailDto> allDetails = btpPurchaseBatchDto.getBtpPurchaseDetails();
        List<BTPPurchaseDetailDto> addedDetails = new ArrayList<>();
        Map<BTPPurchaseDetailDto, ExpenditureDto> updatedDetails = new HashMap<>();

        String projectCode = btpPurchaseBatchDto.getProjectCode();
        String code = btpPurchaseBatchDto.getCode();
        Integer batchVersion = Integer.valueOf(btpPurchaseBatchDto.getPrVersion());

        allDetails.forEach(detail -> {
            Optional<ExpenditureDto> expenditureDtoOptional = iExpenditureService.getBTPExpenditureById(
                    BTPSourceCodeParam.builder()
                            .source(ExpenditureSourceType.BTP.getCode())
                            .btpProjectCode(projectCode)
                            .btpBatchCode(code)
                            .btpDetailCode(detail.getCode())
                            .build());

            if (expenditureDtoOptional.isPresent()) {
                updatedDetails.put(detail, expenditureDtoOptional.get());
            } else {
                addedDetails.add(detail);
            }
        });

        log.info("added: {}, updated: {}, btpPurchaseBatchDto: {}",
                addedDetails.stream().map(BTPPurchaseDetailDto::getCode).distinct().collect(Collectors.toList()),
                updatedDetails.keySet().stream().map(BTPPurchaseDetailDto::getCode).distinct().collect(Collectors.toList()),
                btpPurchaseBatchDto);

        Integer supplierDepartmentId = null;
        if (!CollectionUtils.isEmpty(btpPurchaseBatchDto.getSuppliers())) {
            if (btpPurchaseBatchDto.getSuppliers().stream().anyMatch(supplierDto -> supplierDto.getName().contains("游戏内容部")) ||
                    btpPurchaseBatchDto.getSuppliers().stream().anyMatch(supplierDto -> supplierDto.getName().contains("游戏商业生态部"))) {
                supplierDepartmentId = btpGameDepartmentId;
            } else {
                supplierDepartmentId = btpBusinessDepartmentId;
            }
        }
        if (StringUtils.isNotEmpty(btpPurchaseBatchDto.getCreatorDeptName())) {
            if (btpPurchaseBatchDto.getCreatorDeptName().contains("游戏内容部") ||
                    btpPurchaseBatchDto.getCreatorDeptName().contains("游戏商业生态部")) {
                supplierDepartmentId = btpGameDepartmentId;
            } else {
                supplierDepartmentId = btpBusinessDepartmentId;
            }
        }

        final Integer mappedCrmDepartmentId = supplierDepartmentId;
        addedDetails.forEach(
                detail -> {
                    ExpenditureDto expenditureDto = initBTPExpenditureDto(projectCode, code, batchVersion, mappedCrmDepartmentId, detail);
                    Pair<ExpenditureStatus, NewExpenditureAuditStatus> statuses = repairStatusTransition(
                            detail.getCode(),
                            expenditureDto.getBtpBatchCode(),
                            expenditureDto.getBtpBatchVersion(),
                            Integer.parseInt(btpPurchaseBatchDto.getPrVersion()),
                            expenditureDto.getExpenditureStatus(),
                            expenditureDto.getAuditStatus(),
                            btpPurchaseBatchDto.getStatus()
                    );
                    expenditureDto.setExpenditureStatus(statuses.getLeft().getCode());
                    expenditureDto.setAuditStatus(statuses.getRight().getCode());
                    iExpenditureService.createBySource(expenditureDto, Operator.SYSTEM);
                }
        );

        updatedDetails.forEach(
                (detail, expenditure) -> {
                    Long totalCny = StringUtils.isBlank(detail.getTotalCny()) ? null : new BigDecimal(detail.getTotalCny()).multiply(BigDecimal.valueOf(100L)).longValue();
                    Pair<ExpenditureStatus, NewExpenditureAuditStatus> statuses = repairStatusTransition(
                            detail.getCode(),
                            expenditure.getBtpBatchCode(),
                            expenditure.getBtpBatchVersion(),
                            Integer.parseInt(btpPurchaseBatchDto.getPrVersion()),
                            expenditure.getExpenditureStatus(),
                            expenditure.getAuditStatus(),
                            btpPurchaseBatchDto.getStatus()
                    );

                    expenditure.setRemark(detail.getRequestExplain());
                    expenditure.setBtpTime(Timestamp.valueOf(ZonedDateTime.now().toLocalDateTime()));
                    expenditure.setPayFlag(null);
                    expenditure.setAdCostFlag(null);
                    expenditure.setBtpBatchVersion(batchVersion);
                    expenditure.setExpenditureStatus(statuses.getLeft().getCode());
                    expenditure.setAuditStatus(statuses.getRight().getCode());
                    expenditure.setBtpDetailTotalCny(totalCny);
                    expenditure.setBtpCategoryName(detail.getCategoryName());
                    expenditure.setBtpBudgetPurposeName(detail.getBudgetPurposeName());
                    expenditure.setPrice(totalCny);

                    // 更新绑定的crm order id
                    if (Utils.isPositive(detail.getCrmOrderId())) {
                        int crmOrderId = detail.getCrmOrderId().intValue();
                        expenditure.setCrmOrderId(crmOrderId);
                        Integer crmContractId = crmOrderDao.selectByPrimaryKey(crmOrderId).getCrmContractId();
                        expenditure.setCrmContractId(crmContractId);
                    }

                    iExpenditureService.edit(expenditure);
                }
        );

        // 处理删除的
        handleDeletedBtpDetails(btpPurchaseBatchDto);

    }

    @SneakyThrows
    @Override
    public List<BTPProjectDto> getAllBTPProjectsByCodes(List<String> btpProjectCodes) {
        BTPProjectPageRequestDto btpProjectPageRequestDto = BTPProjectPageRequestDto.builder()
                .projectCodeList(btpProjectCodes)
                .build();

        return this.getAllBTPProjectsByParam(btpProjectPageRequestDto);
    }

    @SneakyThrows
    @Override
    public List<BTPProjectDto> getApprovedBTPProject(BTPProjectPageRequestDto btpProjectPageRequestDto) {
        if (btpProjectPageRequestDto.getPageNum() == null || btpProjectPageRequestDto.getPageSize() == null) {
            btpProjectPageRequestDto.setPageNum(1L);
            btpProjectPageRequestDto.setPageSize((long) Integer.MAX_VALUE);
        }
        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getProjectListUrl())
                .json(GsonUtils.toJson(btpProjectPageRequestDto));

        //String s = bodyPoster.callForString();
        BTPProjectPageResponseDto btpProjectPageResponseDto = OBJECT_MAPPER.readValue(bodyPoster.callForString(), BTPProjectPageResponseDto.class);
        return btpProjectPageResponseDto.getData().getBtpProjects();
    }


    public void testM(){
        try {
            getBtpProjectBudgetForPreCmOrder("测试", 1, 10);
            System.out.println("d");
        } catch (JsonProcessingException e) {
            throw new RuntimeException(e);
        }
    }

    @DynamicValue
    @Value("${btp.huahuo.order.catergory.code:C3673}")  //生产 C3673 uat C1296
    private String huaHuoOrderCategoryCode;


    @Override
    public PageResult<BtpProjectBudgetDto> getBtpProjectBudgetForPreCmOrder(String btpProjectName, Integer page, Integer pageSize) throws JsonProcessingException {
        BTPProjectPageRequestDto req = BTPProjectPageRequestDto.builder()
                .keyword(btpProjectName)
                .pageNum(page.longValue())
                .pageSize(pageSize.longValue())
                .build();

        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getProjectListUrl())
                .json(GsonUtils.toJson(req));
        BTPProjectPageResponseDto res = OBJECT_MAPPER.readValue(bodyPoster.callForString(), BTPProjectPageResponseDto.class);

        return new PageResult<>(res.getData().getTotal().intValue(), convertBudgetDto(res));
    }

    @SneakyThrows
    @Override
    public List<BtpProjectBudgetDto> getBtpProjectBudgetForPreCmOrder(List<String> btpProjectCodeList) {
        if (CollectionUtils.isEmpty(btpProjectCodeList)) {
            return Collections.emptyList();
        }
        BTPProjectPageRequestDto req = BTPProjectPageRequestDto.builder()
                .projectCodeList(btpProjectCodeList)
                .pageNum(1L)
                .pageSize(100l)
                .build();
        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getProjectListUrl())
                .json(GsonUtils.toJson(req));
        BTPProjectPageResponseDto res = OBJECT_MAPPER.readValue(bodyPoster.callForString(), BTPProjectPageResponseDto.class);
        List<BtpProjectBudgetDto> resList = convertBudgetDto(res);

        log.info("preIncomeChangeNotifyPrInfo req {}, btpList:{}", btpProjectCodeList, JSON.toJSONString(resList));
        return resList;
    }

    private List<BtpProjectBudgetDto> convertBudgetDto(BTPProjectPageResponseDto res) {
        List<BtpProjectBudgetDto> resList = res.getData().getBtpProjects().stream().map(it -> {
            BtpProjectBudgetDto dto = BtpProjectBudgetDto.builder()
                    .projectCode(it.getProjectCode())
                    .projectName(it.getProjectName())
                    .budgetAmount(BigDecimal.ZERO)
                    .crmProjectId(it.getCrmProjectId())
                    .build();
            it.getBudgetLineList().stream().filter(budget -> huaHuoOrderCategoryCode.equals(budget.getCategoryCode()))
                    .findFirst().ifPresent(budget -> {
                        BigDecimal amount = Strings.isNullOrEmpty(budget.getAmount()) ? BigDecimal.ZERO : new BigDecimal(budget.getAmount());
                        dto.setBudgetAmount(amount);
                    });
            return dto;
        }).collect(Collectors.toList());
        return resList;
    }


    @Override
    public Integer getTheLastApprovedVersion(String btpBatchCode) {
        List<BTPPurchaseBatchDto> btpPurchaseBatchList = this.getBTPPurchaseBatchByCodeAndVersion(null, btpBatchCode, null);
        if (!CollectionUtils.isEmpty(btpPurchaseBatchList)) {
            BTPPurchaseBatchDto btpPurchaseBatchDto = btpPurchaseBatchList.get(0);
            return Integer.parseInt(btpPurchaseBatchDto.getPrVersion());
        } else {
            return null;
        }
    }

    @Override
    public Map<String, Boolean> isAllAssociatedExpendituresManuallyApproved(List<Integer> expenditureIds) {
        List<ExpenditureDto> expenditureDtoList = iExpenditureService.queryExpenditureByParam(QueryExpenditureParam.builder().ids(expenditureIds).build());
        Set<String> btpBatchCodes = expenditureDtoList.stream().map(ExpenditureDto::getBtpBatchCode).filter(v -> !StringUtils.isBlank(v) && !v.equals("0")).collect(Collectors.toSet());
        if (btpBatchCodes.isEmpty()) {
            return new HashMap<>();
        }
        CrmExpenditurePoExample example = new CrmExpenditurePoExample();
        CrmExpenditurePoExample.Criteria criteria = example.createCriteria();
        criteria.andBtpBatchCodeIn(new ArrayList<>(btpBatchCodes)).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmExpenditurePo> pos = crmExpenditureDao.selectByExample(example);
        //过滤废弃的
        List<CrmExpenditurePo> filterPos = pos.stream().filter(v -> !v.getExpenditureStatus().equals(ExpenditureStatus.ABORT.getCode())).collect(Collectors.toList());
        List<CrmExpenditurePo> filterPos2 = filterPos.stream().filter(v -> !v.getAuditStatus().equals(NewExpenditureAuditStatus.ABORT.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterPos2)) {
            return new HashMap<>();
        }
        Map<String, List<CrmExpenditurePo>> crmExpenditurePoMap = filterPos2.stream()
                .collect(Collectors.groupingBy(CrmExpenditurePo::getBtpBatchCode));

        return crmExpenditurePoMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().allMatch(crmExpenditurePo -> crmExpenditurePo.getAuditStatus() == NewExpenditureAuditStatus.APPROVED.getCode())
                ));
    }

    @Override
    public Map<String, Boolean> isAnyAssociatedExpendituresAutoApprovedAndAllPassed(List<Integer> expenditureIds) {
        List<ExpenditureDto> expenditureDtoList = iExpenditureService.queryExpenditureByParam(QueryExpenditureParam.builder().ids(expenditureIds).build());
        Set<String> btpBatchCodes = expenditureDtoList.stream().map(ExpenditureDto::getBtpBatchCode).filter(v -> !StringUtils.isBlank(v) && !v.equals("0")).collect(Collectors.toSet());
        if (btpBatchCodes.isEmpty()) {
            return new HashMap<>();
        }
        CrmExpenditurePoExample example = new CrmExpenditurePoExample();
        CrmExpenditurePoExample.Criteria criteria = example.createCriteria();
        criteria.andBtpBatchCodeIn(new ArrayList<>(btpBatchCodes)).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmExpenditurePo> pos = crmExpenditureDao.selectByExample(example);
        //过滤废弃的
        List<CrmExpenditurePo> filterPos = pos.stream().filter(v -> !v.getExpenditureStatus().equals(ExpenditureStatus.ABORT.getCode())).collect(Collectors.toList());
        List<CrmExpenditurePo> filterPos2 = filterPos.stream().filter(v -> !v.getAuditStatus().equals(NewExpenditureAuditStatus.ABORT.getCode())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterPos2)) {
            return new HashMap<>();
        }
        Map<String, List<CrmExpenditurePo>> crmExpenditurePoMap = filterPos2.stream()
                .collect(Collectors.groupingBy(CrmExpenditurePo::getBtpBatchCode));

        return crmExpenditurePoMap.entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().anyMatch(crmExpenditurePo -> crmExpenditurePo.getAuditStatus() == NewExpenditureAuditStatus.NONE_AUDIT.getCode()) &&
                                entry.getValue().stream().allMatch(crmExpenditurePo -> crmExpenditurePo.getAuditStatus() == NewExpenditureAuditStatus.NONE_AUDIT.getCode() ||
                                        crmExpenditurePo.getAuditStatus() == NewExpenditureAuditStatus.APPROVED.getCode()
                                )));
    }

    @SneakyThrows
    @Override
    public List<BTPPurchaseBatchDto> getBTPPurchaseBatchByCodes(List<String> codes) {
        JSONObject requestBody = new JSONObject();
        requestBody.put("prCodeList", codes);

        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getPrListUrl())
                .json(GsonUtils.toJson(requestBody));
        String res = bodyPoster.callForString();
        BTPPurchaseBatchResponseDto btpPurchaseBatchResponseDto = OBJECT_MAPPER.readValue(res, BTPPurchaseBatchResponseDto.class);

        log.info("getBTPPurchaseBatchByCodes res {}", JSONObject.toJSONString(btpPurchaseBatchResponseDto));

        btpPurchaseBatchResponseDto.getBtpPurchaseBatches().forEach(
                btpPurchaseBatchDto -> btpPurchaseBatchDto.setFlowOrderUrl(
                        buildBtpOaFlowOrderUrl(btpPurchaseBatchDto.getCode(), Integer.valueOf(btpPurchaseBatchDto.getPrVersion())))
        );
        return btpPurchaseBatchResponseDto.getBtpPurchaseBatches();
    }

    public void test(){
        //249698
        Pair<Long, List<CrmOrderDto>> test = getCrmOrders(1, 10, 0L, "",
                0, Lists.newArrayList(10042501060003l));
        System.out.println();
    }

    @Override
    public Pair<Long, List<CrmOrderDto>> getCrmOrders(Integer page, Integer pageSize, Long crmOrderId, String crmOrderNameLike,
                                                      Integer merchantsProjectId, List<Long> crmContractNos) {
        Integer queryOrderId = Utils.isPositive(crmOrderId) ? crmOrderId.intValue() : null;
        long count = extCrmOrderDao.countOrdersForBtp(queryOrderId, crmContractNos, merchantsProjectId, crmOrderNameLike);
        Page pageValue = Page.valueOf(page, pageSize);
        List<CrmOrderDto> ordersForBtp = extCrmOrderDao.getOrdersForBtp(queryOrderId, crmContractNos, merchantsProjectId,
                crmOrderNameLike, pageValue.getLimit(), pageValue.getOffset());


        List<Integer> projectIds = ordersForBtp.stream().filter(it->Utils.isPositive(it.getCrmOrderId()))
                .map(it -> it.getCrmProjectId().intValue()).distinct().collect(Collectors.toList());
        Map<Integer, ProjectItemPackageDto> projectItemPackageBaseMap = nonStandardPackageService.getProjectItemPackageBaseMap(projectIds);
        ordersForBtp.forEach(it -> {
            Integer project_id = null != it.getCrmProjectId() ? it.getCrmProjectId().intValue() : 0;
            ProjectItemPackageDto projectItem = projectItemPackageBaseMap.getOrDefault(project_id, ProjectItemPackageDto.builder().build());
            it.setCrmProjectName(projectItem.getName());
        });

        //PageResult<CrmOrderDto> pageResult = new PageResult<>(count.intValue(), ordersForBtp);
        return Pair.of(count, ordersForBtp);
    }

    @Override
    public Pair<Long, List<CrmOrderDto>> getCrmOrders(Integer page, Integer pageSize, Long crmOrderId, String crmOrderNameLike) {

        Set<CrmOrderDto> results = new HashSet<>();
        if (crmOrderId != null) {
            String crmOrderName = null;
            Long crmContractId = null;
            String crmContractName = null;
            Long crmProjectId = null;
            String crmProjectName = null;

            CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
            CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();

            CrmOrderPo crmOrderPo = crmOrderDao.selectByPrimaryKey(crmOrderId.intValue());
            if (crmOrderPo != null) {
                // 订单信息
                crmOrderName = crmOrderPo.getExplanation();

                // 合同信息
                crmContractId = crmOrderPo.getCrmContractId() == null ? null : crmOrderPo.getCrmContractId().longValue();
                if (crmContractId != null) {
                    CrmContractPo crmContractPo = crmContractDao.selectByPrimaryKey(crmContractId.intValue());
                    if (crmContractPo != null) {
                        crmContractName = crmContractPo.getName();
                    }
                }


                // 项目信息
                crmOrderExtraPoExampleCriteria.andCrmOrderIdEqualTo(crmOrderId.intValue()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                List<CrmOrderExtraPo> crmOrderExtraPoList = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample);

                if (!CollectionUtils.isEmpty(crmOrderExtraPoList)) {
                    crmProjectId = crmOrderExtraPoList.get(0).getProjectItemId() == null ? null : crmOrderExtraPoList.get(0).getProjectItemId().longValue();
                    if (crmProjectId != null) {
                        CrmProjectItemPackagePo crmProjectItemPackagePo = crmProjectItemPackageDao.selectByPrimaryKey(crmProjectId.intValue());
                        if (crmProjectItemPackagePo != null) {
                            crmProjectName = crmProjectItemPackagePo.getName();
                        }
                    }
                }
            }

            // 过滤
            if (crmProjectId != null && crmProjectId != 0L && crmProjectId != 1L && crmOrderPo.getStatus() != 4 && crmOrderPo.getType() != 10) {
                results.add(CrmOrderDto.builder()
                        .crmOrderId(crmOrderId)
                        .crmOrderName(crmOrderName)
                        .crmProjectId(crmProjectId)
                        .crmProjectName(crmProjectName)
                        .crmContractId(crmContractId)
                        .crmContractName(crmContractName)
                        .build());
            }

        }


        // crm order name模糊搜索
        if (StringUtils.isNotBlank(crmOrderNameLike)) {
            CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
            CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();

//            CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
//            CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();

            CrmProjectItemPackagePoExample crmProjectItemPackagePoExample = new CrmProjectItemPackagePoExample();
            CrmProjectItemPackagePoExample.Criteria crmProjectItemPackagePoExampleCriteria = crmProjectItemPackagePoExample.createCriteria();

            CrmContractPoExample crmContractPoExample = new CrmContractPoExample();
            CrmContractPoExample.Criteria crmContractPoExampleCriteria = crmContractPoExample.createCriteria();


            crmOrderPoExampleCriteria.andExplanationLike("%" + crmOrderNameLike + "%").andStatusNotEqualTo(4)
                    .andTypeNotEqualTo(10).andIsDeletedEqualTo(IsDeleted.VALID.getCode());  // 非花火订单
            // count total
//            total = crmOrderDao.countByExample(crmOrderPoExample);

//            crmOrderPoExample.setOffset((page - 1) * pageSize);
//            crmOrderPoExample.setLimit(pageSize);

            crmOrderPoExample.setOrderByClause("ctime desc");

            List<CrmOrderPo> crmOrderPoList = crmOrderDao.selectByExample(crmOrderPoExample);

            if (CollectionUtils.isEmpty(crmOrderPoList)) {
                return Pair.of(Math.min(pageSize.longValue(), results.size()), new ArrayList<>(results));
            }


            // 批量查询获取信息
            List<Integer> crmOrderIds = crmOrderPoList.stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

            // 项目信息
//            crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(crmOrderIds);
//            List<CrmOrderExtraPo> crmOrderExtraPoList = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample);

            Map<Integer, Integer> crmOrderProjectIdMap = getCrmOrderProjectIdMapFromOrderExtra(crmOrderIds);

            // 项目
            List<Integer> crmProjectIds = new ArrayList<>(crmOrderProjectIdMap.values());
            Map<Integer, String> crmProjectMap = new HashMap<>();

            if (!CollectionUtils.isEmpty(crmProjectIds)) {
                crmProjectItemPackagePoExampleCriteria.andIdIn(crmProjectIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                crmProjectMap = crmProjectItemPackageDao.selectByExample(crmProjectItemPackagePoExample).stream().collect(Collectors.toMap(
                        CrmProjectItemPackagePo::getId,
                        CrmProjectItemPackagePo::getName,
                        (k1, k2) -> k1
                ));
            }

            crmOrderPoList = crmOrderPoList.stream().filter(crmOrderPo -> crmOrderProjectIdMap.containsKey(crmOrderPo.getId())).collect(Collectors.toList());


            Map<Integer, String> crmOrderNameMap = crmOrderPoList.stream().collect(Collectors.toMap(
                    CrmOrderPo::getId,
                    CrmOrderPo::getExplanation,
                    (k1, k2) -> k1
            ));

            Map<Integer, Integer> crmOrderContractIdMap = crmOrderPoList.stream().collect(Collectors.toMap(
                    CrmOrderPo::getId,
                    CrmOrderPo::getCrmContractId,
                    (k1, k2) -> k1
            ));


            // 合同
            Map<Integer, String> crmContractMap = new HashMap<>();
            List<Integer> crmContractIds = crmOrderPoList.stream().map(CrmOrderPo::getCrmContractId).distinct().collect(Collectors.toList());


            if (!CollectionUtils.isEmpty(crmContractIds)) {
                crmContractPoExampleCriteria.andIdIn(crmContractIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                crmContractMap = crmContractDao.selectByExample(crmContractPoExample).stream().collect(Collectors.toMap(
                        CrmContractPo::getId,
                        CrmContractPo::getName,
                        (k1, k2) -> k1
                ));
            }

            // 拼装
            for (Integer eachCrmOrderId : crmOrderProjectIdMap.keySet()) {
                Integer crmProjectId = crmOrderProjectIdMap.get(eachCrmOrderId);


                String crmProjectName = crmProjectMap.get(crmProjectId);
                Integer crmContractId = crmOrderContractIdMap.get(eachCrmOrderId);
                String crmContractName = crmContractMap.get(crmContractId);

                results.add(CrmOrderDto.builder()
                        .crmOrderId(eachCrmOrderId.longValue())
                        .crmOrderName(crmOrderNameMap.get(eachCrmOrderId))
                        .crmProjectId(crmProjectId.longValue())
                        .crmProjectName(crmProjectName)
                        .crmContractId(crmContractId == null ? null : crmContractId.longValue())
                        .crmContractName(crmContractName)
                        .build()
                );
            }

            // 分页
            return Pair.of((long) results.size(), getPageList(new ArrayList<>(results), page, pageSize));
        }


        if (crmOrderId == null && StringUtils.isBlank(crmOrderNameLike)) {

            CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
            CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();

//            CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
//            CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();

            CrmProjectItemPackagePoExample crmProjectItemPackagePoExample = new CrmProjectItemPackagePoExample();
            CrmProjectItemPackagePoExample.Criteria crmProjectItemPackagePoExampleCriteria = crmProjectItemPackagePoExample.createCriteria();

            CrmContractPoExample crmContractPoExample = new CrmContractPoExample();
            CrmContractPoExample.Criteria crmContractPoExampleCriteria = crmContractPoExample.createCriteria();


            crmOrderPoExampleCriteria.andStatusNotEqualTo(4).andTypeNotEqualTo(10).andIsDeletedEqualTo(IsDeleted.VALID.getCode());  // 非花火订单
            // count total
//            total = crmOrderDao.countByExample(crmOrderPoExample);

//            crmOrderPoExample.setOffset((page - 1) * pageSize);
//            crmOrderPoExample.setLimit(pageSize);

            crmOrderPoExample.setOrderByClause("ctime desc");

            List<CrmOrderPo> crmOrderPoList = crmOrderDao.selectByExample(crmOrderPoExample);

            if (CollectionUtils.isEmpty(crmOrderPoList)) {
                return Pair.of(Math.min(pageSize.longValue(), results.size()), new ArrayList<>(results));
            }


            // 批量查询获取信息
            List<Integer> crmOrderIds = crmOrderPoList.stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());


            // 项目信息
//            crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(crmOrderIds);
//            List<CrmOrderExtraPo> crmOrderExtraPoList = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample);

            Map<Integer, Integer> crmOrderProjectIdMap = getCrmOrderProjectIdMapFromOrderExtra(crmOrderIds);

            // 项目
            List<Integer> crmProjectIds = new ArrayList<>(crmOrderProjectIdMap.values());
            Map<Integer, String> crmProjectMap = new HashMap<>();

            if (!CollectionUtils.isEmpty(crmProjectIds)) {
                crmProjectItemPackagePoExampleCriteria.andIdIn(crmProjectIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                crmProjectMap = crmProjectItemPackageDao.selectByExample(crmProjectItemPackagePoExample).stream().collect(Collectors.toMap(
                        CrmProjectItemPackagePo::getId,
                        CrmProjectItemPackagePo::getName,
                        (k1, k2) -> k1
                ));
            }

            // 更新
            crmOrderPoList = crmOrderPoList.stream().filter(crmOrderPo -> crmOrderProjectIdMap.containsKey(crmOrderPo.getId())).collect(Collectors.toList());

            Map<Integer, String> crmOrderNameMap = crmOrderPoList.stream().collect(Collectors.toMap(
                    CrmOrderPo::getId,
                    CrmOrderPo::getExplanation,
                    (k1, k2) -> k1
            ));

            Map<Integer, Integer> crmOrderContractIdMap = crmOrderPoList.stream().collect(Collectors.toMap(
                    CrmOrderPo::getId,
                    CrmOrderPo::getCrmContractId,
                    (k1, k2) -> k1
            ));

            // 合同
            Map<Integer, String> crmContractMap = new HashMap<>();
            List<Integer> crmContractIds = crmOrderPoList.stream().map(CrmOrderPo::getCrmContractId).distinct().collect(Collectors.toList());


            if (!CollectionUtils.isEmpty(crmContractIds)) {
                crmContractPoExampleCriteria.andIdIn(crmContractIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                crmContractMap = crmContractDao.selectByExample(crmContractPoExample).stream().collect(Collectors.toMap(
                        CrmContractPo::getId,
                        CrmContractPo::getName,
                        (k1, k2) -> k1
                ));
            }

            // 拼装
            for (Integer eachCrmOrderId : crmOrderProjectIdMap.keySet()) {
                Integer crmProjectId = crmOrderProjectIdMap.get(eachCrmOrderId);


                String crmProjectName = crmProjectMap.get(crmProjectId);
                Integer crmContractId = crmOrderContractIdMap.get(eachCrmOrderId);
                String crmContractName = crmContractMap.get(crmContractId);

                results.add(CrmOrderDto.builder()
                        .crmOrderId(eachCrmOrderId.longValue())
                        .crmOrderName(crmOrderNameMap.get(eachCrmOrderId))
                        .crmProjectId(crmProjectId.longValue())
                        .crmProjectName(crmProjectName)
                        .crmContractId(crmContractId == null ? null : crmContractId.longValue())
                        .crmContractName(crmContractName)
                        .build()
                );
            }

            // 分页
            return Pair.of((long) results.size(), getPageList(new ArrayList<>(results), page, pageSize));

        }

        return Pair.of((long) results.size(), new ArrayList<>(results));
    }

    @Override
    @ObjectParamCache(value = 32, timeUnit = TimeUnit.DAYS)
    public BTPAuditingStatisticsDto getAuditingStatistics(BTPAuditingRequestDto btpAuditingRequestDto) {
        log.info("getAuditingStatistics param: {}", btpAuditingRequestDto);

        String prCode = btpAuditingRequestDto.getPrCode();
        Long queryTime = btpAuditingRequestDto.getQueryTime();
        Long crmContractId = btpAuditingRequestDto.getCrmContractId();
        Long crmProjectId = btpAuditingRequestDto.getCrmProjectId();

//        List<Integer> crmOrderIds = btpAuditingRequestDto.getPrLines().stream().map(BTPAuditingRequestDto.PrLineAuditingRequestDto::getCrmOrderId).map(Long::intValue).distinct().collect(Collectors.toList());
        Triple<CrmContractPo, CrmProjectItemPackagePo, List<CrmOrderPo>> triple = getCrmOrderByOneContractXProject(queryTime, crmContractId, crmProjectId);

        if (triple == null) {
            BTPAuditingStatisticsDto nullObject = new BTPAuditingStatisticsDto();
            nullObject.setPrLines(new ArrayList<>());
            return nullObject;
        }

        final List<Integer> finalCrmOrderIds = triple.getRight().stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());
        log.info("getAuditingStatistics found crm order ids of crmContractId = {}, crmProjectId = {}, crmOrderIds = {}", crmContractId, crmProjectId, finalCrmOrderIds);

        // 成本
        List<CrmExpenditurePo> allCrmExpenditures = getCrmExpenditures(queryTime).stream()
                .filter(crmExpenditurePo -> finalCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                .collect(Collectors.toList());

        log.info("getAuditingStatistics found crm expenditures of crmContractId = {}, crmProjectId = {}, crmExpenditures = {}", crmContractId, crmProjectId, allCrmExpenditures);


        // btp来源的成本
//        Map<String, List<CrmExpenditurePo>> btpPrCodeExpenditureMap = getCrmBTPApprovedExpenditures(allCrmExpenditures);

        // 预付费花火订单成本
//        Map<Long, CrmExpenditurePo> prePaidPickUpOrderNoExpenditureMap = getCrmPrePaidPickUpOrderExpenditures(allCrmExpenditures);

        // 后付费花火订单成本
        Map<Long, CrmExpenditurePo> postPaidPickUpOrderNoExpenditureMap = getCrmPostPaidPickUpOrderExpenditures(allCrmExpenditures);

        Map<Integer, List<CrmExpenditurePo>> crmOrderExpenditureOfPostPaidPickUpOrderMap = postPaidPickUpOrderNoExpenditureMap.entrySet().stream().collect(Collectors.groupingBy(
                entry -> entry.getValue().getCrmOrderId()
        )).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream().map(Map.Entry::getValue).collect(Collectors.toList())
        ));

        List<Long> postPaidPickUpOrderNos = crmOrderExpenditureOfPostPaidPickUpOrderMap.values().stream().flatMap(List::stream).map(CrmExpenditurePo::getPickUpOrderNo)
                .filter(pickUpOrderNo -> pickUpOrderNo != 0L)
                .distinct().collect(Collectors.toList());

        // 预付费花火订单成本
        List<Long> prePaidPickUpOrderNos = getPrePaidPickUpOrderNos(crmContractId.intValue(), crmProjectId.intValue());

        List<Long> allPickUpOrderNos = new ArrayList<>();
        allPickUpOrderNos.addAll(prePaidPickUpOrderNos);
        allPickUpOrderNos.addAll(postPaidPickUpOrderNos);

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderDataMap = getPickUpOrderData(allPickUpOrderNos);

        // 总收入
        long crmOrderTotalRevenue = 0L;

        // crm订单收入
        crmOrderTotalRevenue += triple.getRight().stream()
                .filter(crmOrderPo -> crmOrderPo.getType() != 10)  // 排除花火订单
                .map(crmOrderPo -> new BigDecimal(crmOrderPo.getAmount()).multiply(crmOrderPo.getDiscount()).divide(new BigDecimal(100), RoundingMode.UNNECESSARY))
                .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                .setScale(0, RoundingMode.HALF_UP).longValue();

        // 花火订单收入
        crmOrderTotalRevenue += pickUpOrderDataMap.values().stream().map(OrderInfoForCrmSynDto::getActualTotalPaidPrice)
                .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                .multiply(new BigDecimal(100))
                .setScale(0, RoundingMode.HALF_UP).longValue();


        final BigDecimal totalRevenue = new BigDecimal(crmOrderTotalRevenue);
        // 待审批的成本
        List<BTPAuditingStatisticsDto.PrLineAuditingStatisticsDto> results = new ArrayList<>();

        btpAuditingRequestDto.getPrLines()
                .stream()
                .filter(prLineAuditingRequestDto -> finalCrmOrderIds.contains(prLineAuditingRequestDto.getCrmOrderId().intValue()))
                .forEach(prLine -> {
                            Long crmOrderId = prLine.getCrmOrderId();
                            long price = prLine.getPrice() == null ? 0L : prLine.getPrice();

                            results.add(BTPAuditingStatisticsDto.PrLineAuditingStatisticsDto.builder()
                                    .code(prLine.getCode())
                                    .crmOrderId(crmOrderId)
                                    .crmOrderName(triple.getRight().stream().filter(crmOrderPo -> crmOrderId.equals(crmOrderPo.getId().longValue())).findFirst().get().getExplanation())
                                    .crmContractId(crmContractId)
                                    .crmContractName(triple.getLeft().getName())
                                    .crmProjectId(crmProjectId)
                                    .crmProjectName(triple.getMiddle().getName())
                                    .expenditureRate(new BigDecimal(price).divide(totalRevenue, 4, RoundingMode.HALF_UP))
                                    .build());
                        }
                );


        return BTPAuditingStatisticsDto.builder()
                .prCode(prCode)
                .prLines(results)
                .build();
    }

    @Override
    @ObjectParamCache(value = 32, timeUnit = TimeUnit.DAYS)
    public ApprovedExpenditureStatisticsDto getApprovedStatistics(BTPAuditingRequestDto btpAuditingRequestDto) {
        log.info("getApprovedStatistics param: {}", btpAuditingRequestDto);

        String prCode = btpAuditingRequestDto.getPrCode();
        Long queryTime = btpAuditingRequestDto.getQueryTime();
        Long crmContractId = btpAuditingRequestDto.getCrmContractId();
        Long crmProjectId = btpAuditingRequestDto.getCrmProjectId();

//        List<Integer> crmOrderIds = btpAuditingRequestDto.getPrLines().stream().map(BTPAuditingRequestDto.PrLineAuditingRequestDto::getCrmOrderId).map(Long::intValue).distinct().collect(Collectors.toList());
        Triple<CrmContractPo, CrmProjectItemPackagePo, List<CrmOrderPo>> triple = getCrmOrderByOneContractXProject(queryTime, crmContractId, crmProjectId);

        if (triple == null) {
            ApprovedExpenditureStatisticsDto approvedExpenditureStatisticsDto = new ApprovedExpenditureStatisticsDto();
            approvedExpenditureStatisticsDto.setCrmExpenditures(new ArrayList<>());
            return approvedExpenditureStatisticsDto;
        }

        final List<Integer> finalCrmOrderIds = triple.getRight().stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

        log.info("getApprovedStatistics found crm order ids of crmContractId = {}, crmProjectId = {}, crmOrderIds = {}", crmContractId, crmProjectId, finalCrmOrderIds);

        // 成本
        List<CrmExpenditurePo> allCrmExpenditures = getCrmExpenditures(queryTime);

        List<CrmExpenditurePo> allCrmExpendituresFilteredByCrmOrderIds = allCrmExpenditures.stream()
                .filter(crmExpenditurePo -> finalCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                .collect(Collectors.toList());

        log.info("getApprovedStatistics found crm expenditures of crmContractId = {}, crmProjectId = {}, crmExpenditures = {}", crmContractId, crmProjectId, allCrmExpendituresFilteredByCrmOrderIds);


        // btp来源的成本
        Map<String, List<CrmExpenditurePo>> btpPrCodeExpenditureMap = getCrmBTPApprovedExpenditures(allCrmExpendituresFilteredByCrmOrderIds);

        log.info("getApprovedStatistics found crm expenditures of crmContractId = {}, crmProjectId = {}, btpPrCodeExpenditureMap = {}", crmContractId, crmProjectId, btpPrCodeExpenditureMap);

        // 预付费花火订单成本
        // Map<Long, CrmExpenditurePo> prePaidPickUpOrderNoExpenditureMap = getCrmPrePaidPickUpOrderExpenditures(allCrmExpendituresFilteredByCrmOrderIds);


        // 后付费花火订单成本
        Map<Long, CrmExpenditurePo> postPaidPickUpOrderNoExpenditureMap = getCrmPostPaidPickUpOrderExpenditures(allCrmExpendituresFilteredByCrmOrderIds);

        Map<Integer, List<CrmExpenditurePo>> crmOrderExpenditureOfPostPaidPickUpOrderMap = postPaidPickUpOrderNoExpenditureMap.entrySet().stream().collect(Collectors.groupingBy(
                entry -> entry.getValue().getCrmOrderId()
        )).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream().map(Map.Entry::getValue).collect(Collectors.toList())
        ));

        List<Long> postPaidPickUpOrderNos = crmOrderExpenditureOfPostPaidPickUpOrderMap.values().stream().flatMap(List::stream).map(CrmExpenditurePo::getPickUpOrderNo)
                .filter(pickUpOrderNo -> pickUpOrderNo != 0L)
                .distinct().collect(Collectors.toList());

        // 预付费花火订单成本
        List<Long> prePaidPickUpOrderNos = getPrePaidPickUpOrderNos(crmContractId.intValue(), crmProjectId.intValue());

        List<Long> allPickUpOrderNos = new ArrayList<>();
        allPickUpOrderNos.addAll(prePaidPickUpOrderNos);
        allPickUpOrderNos.addAll(postPaidPickUpOrderNos);

        log.info("getApprovedStatistics found pick up order nos: {}", allPickUpOrderNos);

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderDataMap = getPickUpOrderData(allPickUpOrderNos);

        // 总收入
        long crmOrderTotalRevenue = 0L;

        // crm订单收入
        crmOrderTotalRevenue += triple.getRight().stream()
                .filter(crmOrderPo -> crmOrderPo.getType() != 10)  // 排除花火订单
                .map(crmOrderPo -> new BigDecimal(crmOrderPo.getAmount()).multiply(crmOrderPo.getDiscount()).divide(new BigDecimal(100), RoundingMode.UNNECESSARY))
                .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                .setScale(0, RoundingMode.HALF_UP).longValue();

        // 花火订单收入
        crmOrderTotalRevenue += pickUpOrderDataMap.values().stream().map(OrderInfoForCrmSynDto::getActualTotalPaidPrice)
                .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                .multiply(new BigDecimal(100))
                .setScale(0, RoundingMode.HALF_UP).longValue();


        // 过审的成本

        // btp来源的成本
        List<String> prCodes = new ArrayList<>(btpPrCodeExpenditureMap.keySet());
        List<String> statusList = new ArrayList<>();
        statusList.add("2");
        statusList.add("4");
        List<String> inFlowList = new ArrayList<>();
        inFlowList.add("0");


        // 使用最后一次过审时间过滤
        LocalDateTime limitTime = new Timestamp(queryTime).toLocalDateTime();
        log.info("getApprovedStatistics query datetime: {}", limitTime);

        Map<String, BTPPurchaseBatchDto> prMap = getBTPPrsByStatus(prCodes, statusList, inFlowList).entrySet().stream()
                .filter(entry -> StringUtils.isBlank(entry.getValue().getApprovedTime()) || LocalDateTime.from(DATE_TIME_FORMATTER.parse(entry.getValue().getApprovedTime())).isBefore(limitTime))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (k1, k2) -> k1
                ));


        // 拼装
        List<Integer> crmExpenditureFirstTypeIds = allCrmExpenditures.stream().map(CrmExpenditurePo::getType).distinct().collect(Collectors.toList());
        List<Integer> crmExpenditureSecondTypeIds = allCrmExpenditures.stream().map(CrmExpenditurePo::getSecondType).distinct().collect(Collectors.toList());
        crmExpenditureFirstTypeIds.addAll(crmExpenditureSecondTypeIds);
        Map<Integer, String> crmExpenditureSecondTypeIdNameMap = getExpenditureType(crmExpenditureFirstTypeIds);


        final BigDecimal totalRevenue = new BigDecimal(crmOrderTotalRevenue);
        List<ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto> results = new ArrayList<>();

        // btp
        btpPrCodeExpenditureMap.values().stream().flatMap(List::stream).forEach(
                crmExpenditurePo -> {
                    String prLineCode = crmExpenditurePo.getBtpDetailCode();
                    BTPPurchaseDetailDto prLine = prMap.values().stream().map(BTPPurchaseBatchDto::getBtpPurchaseDetails).flatMap(List::stream)
                            .filter(btpPurchaseDetailDto -> prLineCode.equals(btpPurchaseDetailDto.getCode()))
                            .findFirst().orElse(null);

                    if (prLine != null) {
                        Long crmOrderId = prLine.getCrmOrderId();
                        long price = StringUtils.isBlank(prLine.getTotalCny()) ? 0L : Long.parseLong(prLine.getTotalCny()) * 100;

                        String type = crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getSecondType());

                        results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                                .id(crmExpenditurePo.getId().longValue())
                                .code(prLineCode)
                                .name(crmExpenditurePo.getName())
                                .type(type)
                                .price(price)
                                .rate(new BigDecimal(price).divide(totalRevenue, 4, RoundingMode.HALF_UP))
                                .crmOrderId(crmOrderId)
                                .crmOrderName(triple.getRight().stream().filter(crmOrderPo -> crmOrderId.equals(crmOrderPo.getId().longValue())).findFirst().get().getExplanation())
                                .crmContractId(crmContractId)
                                .crmContractName(triple.getLeft().getName())
                                .crmProjectId(crmProjectId)
                                .crmProjectName(triple.getMiddle().getName())
                                .build());
                    }

                }
        );

        // 花火

        // 预付费花火
        List<CrmExpenditurePo> prePaidPickUpCrmExpenditures = allCrmExpenditures.stream().filter(crmExpenditurePo -> prePaidPickUpOrderNos.contains(crmExpenditurePo.getPickUpOrderNo())).collect(Collectors.toList());

        prePaidPickUpCrmExpenditures.stream().forEach(
                crmExpenditurePo -> {
                    Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                    OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpOrderDataMap.get(pickUpOrderNo);
                    long price = orderInfoForCrmSynDto.getPlatformTotalExpenses().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();

                    String type = crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getSecondType());

                    results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                            .id(crmExpenditurePo.getId().longValue())
                            .code(null)
                            .name(crmExpenditurePo.getName())
                            .type(type)
                            .price(price)
                            .rate(new BigDecimal(price).divide(totalRevenue, 4, RoundingMode.HALF_UP))
                            .crmOrderId(pickUpOrderNo)
                            .crmOrderName(null)
                            .crmContractId(crmContractId)
                            .crmContractName(triple.getLeft().getName())
                            .crmProjectId(crmProjectId)
                            .crmProjectName(triple.getMiddle().getName())
                            .build());
                }
        );


        // 后付费花火
        postPaidPickUpOrderNoExpenditureMap.values().stream().forEach(
                crmExpenditurePo -> {
                    Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                    Long crmOrderId = crmExpenditurePo.getCrmOrderId().longValue();
                    OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpOrderDataMap.get(pickUpOrderNo);
                    long price = orderInfoForCrmSynDto.getPlatformTotalExpenses().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();

                    String type = crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getSecondType());

                    results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                            .id(crmExpenditurePo.getId().longValue())
                            .code(null)
                            .name(crmExpenditurePo.getName())
                            .type(type)
                            .price(price)
                            .rate(new BigDecimal(price).divide(totalRevenue, 4, RoundingMode.HALF_UP))
                            .crmOrderId(crmOrderId)
                            .crmOrderName(triple.getRight().stream().filter(crmOrderPo -> crmOrderId.equals(crmOrderPo.getId().longValue())).findFirst().get().getExplanation())
                            .crmContractId(crmContractId)
                            .crmContractName(triple.getLeft().getName())
                            .crmProjectId(crmProjectId)
                            .crmProjectName(triple.getMiddle().getName())
                            .build());
                }
        );


        // crm其他
        allCrmExpendituresFilteredByCrmOrderIds.stream()
                .filter(crmExpenditurePo -> crmExpenditurePo.getSource() != 0 && crmExpenditurePo.getSource() != ExpenditureSourceType.BTP.getCode() && crmExpenditurePo.getSource() != ExpenditureSourceType.PICKUP_PLATFORM.getCode())
                .forEach(crmExpenditurePo -> {
                            Long crmOrderId = crmExpenditurePo.getCrmOrderId().longValue();
                            Long price = crmExpenditurePo.getPrice();

                            String type = crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureSecondTypeIdNameMap.get(crmExpenditurePo.getSecondType());

                            results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                                    .id(crmExpenditurePo.getId().longValue())
                                    .code(null)
                                    .name(crmExpenditurePo.getName())
                                    .type(type)
                                    .price(price)
                                    .rate(new BigDecimal(price).divide(totalRevenue, 4, RoundingMode.HALF_UP))
                                    .crmOrderId(crmOrderId)
                                    .crmOrderName(triple.getRight().stream().filter(crmOrderPo -> crmOrderId.equals(crmOrderPo.getId().longValue())).findFirst().get().getExplanation())
                                    .crmContractId(crmContractId)
                                    .crmContractName(triple.getLeft().getName())
                                    .crmProjectId(crmProjectId)
                                    .crmProjectName(triple.getMiddle().getName())
                                    .build());
                        }
                );


        return ApprovedExpenditureStatisticsDto.builder()
                .prCode(prCode)
                .crmExpenditures(results)
                .build();
    }

    @Override
    @ObjectParamCache(value = 32, timeUnit = TimeUnit.DAYS)
    public List<CrmOrderRevenueStatisticsDto> getCrmOrderRevenueStatistics(BTPAuditingRequestDto btpAuditingRequestDto) {
        log.info("getCrmOrderRevenueStatistics param: {}", btpAuditingRequestDto);

        String prCode = btpAuditingRequestDto.getPrCode();
        Long queryTime = btpAuditingRequestDto.getQueryTime();
        Long crmContractId = btpAuditingRequestDto.getCrmContractId();
        Long crmProjectId = btpAuditingRequestDto.getCrmProjectId();

//        List<Integer> crmOrderIds = btpAuditingRequestDto.getPrLines().stream().map(BTPAuditingRequestDto.PrLineAuditingRequestDto::getCrmOrderId).map(Long::intValue).distinct().collect(Collectors.toList());
        Triple<CrmContractPo, CrmProjectItemPackagePo, List<CrmOrderPo>> triple = getCrmOrderByOneContractXProject(queryTime, crmContractId, crmProjectId);

        if (triple == null) {
            return new ArrayList<>();
        }

        String crmContractLink = crmContractLinkPrefix + crmContractId;
        CrmContractPo crmContract = triple.getLeft();
        Integer accountId = crmContract.getAccountId();
        String userName = null;

        if (accountId != null) {
            AccAccountPo accAccountPo = accAccountDao.selectByPrimaryKey(accountId);
            if (accAccountPo != null) {
                userName = accAccountPo.getUsername();
            }
        }


        final List<Integer> finalCrmOrderIds = triple.getRight().stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

        log.info("getCrmOrderRevenueStatistics found crm order ids of crmContractId = {}, crmProjectId = {}, crmOrderIds = {}", crmContractId, crmProjectId, finalCrmOrderIds);

        // 成本
        List<CrmExpenditurePo> allCrmExpenditures = getCrmExpenditures(queryTime).stream()
                .filter(crmExpenditurePo -> finalCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                .collect(Collectors.toList());

        log.info("getCrmOrderRevenueStatistics found crm expenditures of crmContractId = {}, crmProjectId = {}, crmExpenditures = {}", crmContractId, crmProjectId, allCrmExpenditures);

        // 预付费花火订单成本
//        Map<Long, CrmExpenditurePo> prePaidPickUpOrderNoExpenditureMap = getCrmPrePaidPickUpOrderExpenditures(allCrmExpenditures);

        // 后付费花火订单成本
        Map<Long, CrmExpenditurePo> postPaidPickUpOrderNoExpenditureMap = getCrmPostPaidPickUpOrderExpenditures(allCrmExpenditures);
        Map<Long, Integer> postPaidPickUpOrderNoCrmOrderIdMap = postPaidPickUpOrderNoExpenditureMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().getCrmOrderId(),
                (k1, k2) -> k1
        ));


        Map<Integer, List<CrmExpenditurePo>> crmOrderExpenditureOfPostPaidPickUpOrderMap = postPaidPickUpOrderNoExpenditureMap.entrySet().stream().collect(Collectors.groupingBy(
                entry -> entry.getValue().getCrmOrderId()
        )).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream().map(Map.Entry::getValue).collect(Collectors.toList())
        ));

        List<Long> postPaidPickUpOrderNos = crmOrderExpenditureOfPostPaidPickUpOrderMap.values().stream().flatMap(List::stream).map(CrmExpenditurePo::getPickUpOrderNo)
                .filter(pickUpOrderNo -> pickUpOrderNo != 0L)
                .distinct().collect(Collectors.toList());

        // 预付费花火订单成本
        List<Long> prePaidPickUpOrderNos = getPrePaidPickUpOrderNos(crmContractId.intValue(), crmProjectId.intValue());

        List<Long> allPickUpOrderNos = new ArrayList<>();
        allPickUpOrderNos.addAll(prePaidPickUpOrderNos);
        allPickUpOrderNos.addAll(postPaidPickUpOrderNos);

        log.info("getCrmOrderRevenueStatistics found pick up order nos: {}", allPickUpOrderNos);

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderDataMap = getPickUpOrderData(allPickUpOrderNos);

        // 收入
        List<CrmOrderRevenueStatisticsDto> results = new ArrayList<>();

        final String customerName = userName;
        // crm订单收入
        results.addAll(triple.getRight().stream()
                .filter(crmOrderPo -> crmOrderPo.getType() != 10)  // 排除花火订单
                .map(crmOrderPo -> {
                            Integer orderTypeCode = crmOrderPo.getType();
                            String crmOrderTypeDesc = null;
                            if (CrmOrderType.getByCode(orderTypeCode) != null) {
                                crmOrderTypeDesc = CrmOrderType.getByCode(orderTypeCode).getDesc();
                            }

                            return CrmOrderRevenueStatisticsDto.builder()
                                    .prCode(prCode)
                                    .id(crmOrderPo.getId().longValue())
                                    .name(crmOrderPo.getExplanation())
                                    .price(new BigDecimal(crmOrderPo.getAmount()).multiply(crmOrderPo.getDiscount()).divide(new BigDecimal(100), RoundingMode.UNNECESSARY).setScale(0, RoundingMode.HALF_UP).longValue())
                                    .type(crmOrderTypeDesc)
                                    .beginTime(crmOrderPo.getBeginTime().getTime())
                                    .endTime(crmOrderPo.getEndTime().getTime())
                                    .customerId(accountId == null ? null : accountId.longValue())
                                    .customerName(customerName)
                                    .crmContractId(crmContractId)
                                    .crmContractName(triple.getLeft().getName())
                                    .crmContractLink(crmContractLink)
                                    .crmProjectId(crmProjectId)
                                    .crmProjectName(triple.getMiddle().getName())
                                    .build();
                        }
                ).collect(Collectors.toList()));

        // 花火订单收入

        // 预付费花火订单
        results.addAll(
                pickUpOrderDataMap.values().stream()
                        .filter(orderInfoForCrmSynDto -> prePaidPickUpOrderNos.contains(orderInfoForCrmSynDto.getOrderNo()))
                        .map(orderInfoForCrmSynDto ->
                                CrmOrderRevenueStatisticsDto.builder()
                                        .prCode(prCode)
                                        .id(orderInfoForCrmSynDto.getOrderNo())
                                        .name(null)
                                        .price(orderInfoForCrmSynDto.getActualTotalPaidPrice().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue())
                                        .type(CrmOrderType.PICK_UP.getDesc())
                                        .beginTime(null)
                                        .endTime(null)
                                        .customerId(accountId == null ? null : accountId.longValue())
                                        .customerName(customerName)
                                        .crmContractId(crmContractId)
                                        .crmContractName(triple.getLeft().getName())
                                        .crmContractLink(crmContractLink)
                                        .crmProjectId(crmProjectId)
                                        .crmProjectName(triple.getMiddle().getName())
                                        .build()
                        ).collect(Collectors.toList())
        );


        // 后付费花火订单
        results.addAll(pickUpOrderDataMap.values().stream()
                .filter(orderInfoForCrmSynDto -> postPaidPickUpOrderNos.contains(orderInfoForCrmSynDto.getOrderNo()))
                .map(orderInfoForCrmSynDto -> {
                            Long pickUpOrderNo = orderInfoForCrmSynDto.getOrderNo();
                            Integer crmOrderId = postPaidPickUpOrderNoCrmOrderIdMap.get(pickUpOrderNo);
                            CrmOrderPo crmOrderPo = triple.getRight().stream().filter(cop -> crmOrderId.equals(cop.getId())).findFirst().get();


                            Integer orderTypeCode = crmOrderPo.getType();
                            String crmOrderTypeDesc = null;
                            if (CrmOrderType.getByCode(orderTypeCode) != null) {
                                crmOrderTypeDesc = CrmOrderType.getByCode(orderTypeCode).getDesc();
                            }


                            return CrmOrderRevenueStatisticsDto.builder()
                                    .prCode(prCode)
                                    .id(crmOrderId.longValue())
                                    .name(crmOrderPo.getExplanation())
                                    .price(orderInfoForCrmSynDto.getActualTotalPaidPrice().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue())
                                    .type(crmOrderTypeDesc)
                                    .beginTime(crmOrderPo.getBeginTime().getTime())
                                    .endTime(crmOrderPo.getEndTime().getTime())
                                    .customerId(accountId == null ? null : accountId.longValue())
                                    .customerName(customerName)
                                    .crmContractId(crmContractId)
                                    .crmContractName(triple.getLeft().getName())
                                    .crmContractLink(crmContractLink)
                                    .crmProjectId(crmProjectId)
                                    .crmProjectName(triple.getMiddle().getName())
                                    .build();
                        }
                )
                .collect(Collectors.toList()));


        return results;
    }

    @Override
    @ObjectParamCache(value = 32, timeUnit = TimeUnit.DAYS)
    public CrmProjectStatisticsDto getCrmProjectStatistics(BTPAuditingRequestDto btpAuditingRequestDto) {
        log.info("getCrmProjectStatistics param: {}", btpAuditingRequestDto);

        String prCode = btpAuditingRequestDto.getPrCode();
        Long queryTime = btpAuditingRequestDto.getQueryTime();
        Long crmContractId = btpAuditingRequestDto.getCrmContractId();
        Long crmProjectId = btpAuditingRequestDto.getCrmProjectId();

//        List<Integer> crmOrderIds = btpAuditingRequestDto.getPrLines().stream().map(BTPAuditingRequestDto.PrLineAuditingRequestDto::getCrmOrderId).map(Long::intValue).distinct().collect(Collectors.toList());
        Pair<CrmProjectItemPackagePo, List<CrmOrderPo>> crmProjectOrderPair = getCrmOrderByOneProject(queryTime, crmProjectId);

        if (crmProjectOrderPair == null) {
            CrmProjectStatisticsDto nullObject = new CrmProjectStatisticsDto();
            nullObject.setCrmProject(new CrmProjectStatisticsDto.CrmProject());
            return nullObject;
        }


        final List<Integer> finalCrmOrderIds = crmProjectOrderPair.getRight().stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

        log.info("getCrmProjectStatistics found crm order ids of crmContractId = {}, crmProjectId = {}, crmOrderIds = {}", crmContractId, crmProjectId, finalCrmOrderIds);

        if (CollectionUtils.isEmpty(finalCrmOrderIds)) {
            CrmProjectStatisticsDto nullObject = new CrmProjectStatisticsDto();
            nullObject.setCrmProject(new CrmProjectStatisticsDto.CrmProject());
            return nullObject;
        }

        // 成本
        List<CrmExpenditurePo> allCrmExpenditures = getCrmExpenditures(queryTime);

        List<CrmExpenditurePo> allCrmExpendituresFilteredByCrmOrderIds = allCrmExpenditures.stream()
                .filter(crmExpenditurePo -> finalCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                .collect(Collectors.toList());

        log.info("getCrmProjectStatistics found crm expenditures of crmContractId = {}, crmProjectId = {}, crmExpenditures = {}", crmContractId, crmProjectId, allCrmExpendituresFilteredByCrmOrderIds);

        // btp来源的成本
        Map<String, List<CrmExpenditurePo>> btpPrCodeExpenditureMap = getCrmBTPApprovedExpenditures(allCrmExpendituresFilteredByCrmOrderIds);

        // 预付费花火订单成本
        //Map<Long, CrmExpenditurePo> prePaidPickUpOrderNoExpenditureMap = getCrmPrePaidPickUpOrderExpenditures(allCrmExpendituresFilteredByCrmOrderIds);

        // 后付费花火订单成本
        Map<Long, CrmExpenditurePo> postPaidPickUpOrderNoExpenditureMap = getCrmPostPaidPickUpOrderExpenditures(allCrmExpendituresFilteredByCrmOrderIds);

        Map<Integer, List<CrmExpenditurePo>> crmOrderExpenditureOfPostPaidPickUpOrderMap = postPaidPickUpOrderNoExpenditureMap.entrySet().stream().collect(Collectors.groupingBy(
                entry -> entry.getValue().getCrmOrderId()
        )).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream().map(Map.Entry::getValue).collect(Collectors.toList())
        ));

        List<Long> postPaidPickUpOrderNos = crmOrderExpenditureOfPostPaidPickUpOrderMap.values().stream().flatMap(List::stream).map(CrmExpenditurePo::getPickUpOrderNo)
                .filter(pickUpOrderNo -> pickUpOrderNo != 0L)
                .distinct().collect(Collectors.toList());

        // 预付费花火订单成本
        List<Long> prePaidPickUpOrderNos = getPrePaidPickUpOrderNos(crmContractId.intValue(), crmProjectId.intValue());

        List<Long> allPickUpOrderNos = new ArrayList<>();
        allPickUpOrderNos.addAll(prePaidPickUpOrderNos);
        allPickUpOrderNos.addAll(postPaidPickUpOrderNos);

        log.info("getCrmProjectStatistics found pick up order nos: {}", allPickUpOrderNos);

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderDataMap = getPickUpOrderData(allPickUpOrderNos);

        // 总收入
        long crmOrderTotalRevenue = 0L;

        // crm订单收入
        crmOrderTotalRevenue += crmProjectOrderPair.getRight().stream()
                .filter(crmOrderPo -> crmOrderPo.getType() != 10)  // 排除花火订单
                .map(crmOrderPo -> new BigDecimal(crmOrderPo.getAmount()).multiply(crmOrderPo.getDiscount()).divide(new BigDecimal(100), RoundingMode.UNNECESSARY))
                .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                .setScale(0, RoundingMode.HALF_UP).longValue();

        // 花火订单收入
        crmOrderTotalRevenue += pickUpOrderDataMap.values().stream().map(OrderInfoForCrmSynDto::getActualTotalPaidPrice)
                .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                .multiply(new BigDecimal(100))
                .setScale(0, RoundingMode.HALF_UP).longValue();


        // 成本
        long totalExpenditure = 0L;

        // 待审批的成本
        totalExpenditure += btpAuditingRequestDto.getPrLines().stream()
                .filter(prLineAuditingRequestDto -> finalCrmOrderIds.contains(prLineAuditingRequestDto.getCrmOrderId().intValue()))
                .map(BTPAuditingRequestDto.PrLineAuditingRequestDto::getPrice).filter(Objects::nonNull)
                .mapToLong(it -> it).sum();


        // 过审的成本

        // btp来源的成本
        List<String> prCodes = new ArrayList<>(btpPrCodeExpenditureMap.keySet());
        List<String> statusList = new ArrayList<>();
        statusList.add("2");
        statusList.add("4");
        List<String> inFlowList = new ArrayList<>();
        inFlowList.add("0");


        // 使用最后一次过审时间过滤
        LocalDateTime limitTime = new Timestamp(queryTime).toLocalDateTime();
        log.info("getCrmProjectStatistics query datetime: {}", limitTime);


        Map<String, BTPPurchaseBatchDto> prMap = getBTPPrsByStatus(prCodes, statusList, inFlowList).entrySet().stream()
                .filter(entry -> StringUtils.isBlank(entry.getValue().getApprovedTime()) || LocalDateTime.from(DATE_TIME_FORMATTER.parse(entry.getValue().getApprovedTime())).isBefore(limitTime))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        Map.Entry::getValue,
                        (k1, k2) -> k1
                ));


        List<ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto> results = new ArrayList<>();

        // btp
        btpPrCodeExpenditureMap.values().stream().flatMap(List::stream).forEach(
                crmExpenditurePo -> {
                    String prLineCode = crmExpenditurePo.getBtpDetailCode();
                    BTPPurchaseDetailDto prLine = prMap.values().stream().map(BTPPurchaseBatchDto::getBtpPurchaseDetails).flatMap(List::stream)
                            .filter(btpPurchaseDetailDto -> prLineCode.equals(btpPurchaseDetailDto.getCode()))
                            .findFirst().orElse(null);

                    if (prLine != null) {
                        Long crmOrderId = prLine.getCrmOrderId();
                        long price = StringUtils.isBlank(prLine.getTotalCny()) ? 0L : Long.parseLong(prLine.getTotalCny()) * 100;

                        results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                                .price(price)
                                .build());
                    }

                }
        );

        // 花火

        // 预付费花火
        List<CrmExpenditurePo> prePaidPickUpCrmExpenditures = allCrmExpenditures.stream().filter(crmExpenditurePo -> prePaidPickUpOrderNos.contains(crmExpenditurePo.getPickUpOrderNo())).collect(Collectors.toList());

        prePaidPickUpCrmExpenditures.stream().forEach(
                crmExpenditurePo -> {
                    Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                    OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpOrderDataMap.get(pickUpOrderNo);
                    long price = orderInfoForCrmSynDto.getPlatformTotalExpenses().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();

                    results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                            .price(price)
                            .build());
                }
        );


        // 后付费花火
        postPaidPickUpOrderNoExpenditureMap.values().stream().forEach(
                crmExpenditurePo -> {
                    Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                    Long crmOrderId = crmExpenditurePo.getCrmOrderId().longValue();
                    OrderInfoForCrmSynDto orderInfoForCrmSynDto = pickUpOrderDataMap.get(pickUpOrderNo);
                    long price = orderInfoForCrmSynDto.getPlatformTotalExpenses().multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();

                    results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                            .price(price)
                            .build());
                }
        );


        // crm其他
        allCrmExpendituresFilteredByCrmOrderIds.stream()
                .filter(crmExpenditurePo -> crmExpenditurePo.getSource() != 0 && crmExpenditurePo.getSource() != ExpenditureSourceType.BTP.getCode() && crmExpenditurePo.getSource() != ExpenditureSourceType.PICKUP_PLATFORM.getCode())
                .forEach(crmExpenditurePo -> {
                            Long crmOrderId = crmExpenditurePo.getCrmOrderId().longValue();
                            Long price = crmExpenditurePo.getPrice();

                            results.add(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto.builder()
                                    .price(price)
                                    .build());
                        }
                );

        totalExpenditure += results.stream().mapToLong(ApprovedExpenditureStatisticsDto.CrmExpenditureStatisticsDto::getPrice).sum();


        return CrmProjectStatisticsDto.builder()
                .prCode(prCode)
                .crmProject(
                        CrmProjectStatisticsDto.CrmProject.builder()
                                .id(crmProjectId)
                                .name(crmProjectOrderPair.getLeft().getName())
                                .expenditureRate(new BigDecimal(totalExpenditure).divide(new BigDecimal(crmOrderTotalRevenue), 4, RoundingMode.HALF_UP))
                                .build())
                .build();
    }

    @Override
    @ObjectParamCache(value = 32, timeUnit = TimeUnit.DAYS)
    public CrmContractProjectStatisticsDto getCrmContractProjectStatistics(BTPAuditingRequestDto btpAuditingRequestDto) {
        log.info("getCrmContractProjectStatistics param: {}", btpAuditingRequestDto);

        Long queryTime = btpAuditingRequestDto.getQueryTime();
        String prCode = btpAuditingRequestDto.getPrCode();
        List<Integer> crmOrderIds = btpAuditingRequestDto.getPrLines().stream().map(BTPAuditingRequestDto.PrLineAuditingRequestDto::getCrmOrderId).map(Long::intValue).distinct().collect(Collectors.toList());

        // 新增关联btp的项目x合同过滤
        List<Pair<Integer, Integer>> btpInvolvedContractXProjectIds = getBTPInvolvedContractXProjectId(crmOrderIds);

        // 订单收入
        // crm订单收入
        List<CrmOrderPo> crmOrderPoList = getCrmOrderByContractXProject(queryTime, crmOrderIds);
        List<Integer> allCrmOrderIds = crmOrderPoList.stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());
        Triple<Map<Integer, CrmOrderPo>, Map<Integer, CrmContractPo>, Map<Integer, CrmProjectItemPackagePo>> triple = getCrmOrderContractXProjectMap(allCrmOrderIds);

        Map<Integer, CrmOrderPo> crmOrderMap = triple.getLeft();
        Map<Integer, CrmContractPo> crmContractMap = triple.getMiddle();
        Map<Integer, CrmProjectItemPackagePo> crmProjectMap = triple.getRight();

        // group by crm contract id, value: crm order ids
        Map<Integer, List<Integer>> crmContractOrderIdsMap = crmContractMap.entrySet().stream().collect(Collectors.groupingBy(entry -> entry.getValue().getId()))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(Map.Entry::getKey).distinct().collect(Collectors.toList())
                ));

        Map<Integer, List<Integer>> crmProjectOrderIdsMap = crmProjectMap.entrySet().stream().collect(Collectors.groupingBy(entry -> entry.getValue().getId()))
                .entrySet().stream()
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().stream().map(Map.Entry::getKey).distinct().collect(Collectors.toList())
                ));

        // 合同 x 项目
        Map<Pair<Integer, Integer>, List<Integer>> crmContractProjectOrderIdsMap = new HashMap<>();

        for (Map.Entry<Integer, List<Integer>> contractEntry : crmContractOrderIdsMap.entrySet()) {
            Integer crmContractId = contractEntry.getKey();
            for (Map.Entry<Integer, List<Integer>> projectEntry : crmProjectOrderIdsMap.entrySet()) {
                Integer crmProjectId = projectEntry.getKey();
                // 过滤无效的项目
                if (crmProjectId == 0 || crmProjectId == 1) {
                    continue;
                }

                // 新增关联btp的项目x合同过滤
                Pair<Integer, Integer> key = Pair.of(crmContractId, crmProjectId);
                if (!btpInvolvedContractXProjectIds.contains(key)) {
                    continue;
                }

                List<Integer> crmContractProjectOrderIds = crmContractProjectOrderIdsMap.get(key);
                if (crmContractProjectOrderIds == null) {
                    List<Integer> initCrmOrderIds = new ArrayList<>(contractEntry.getValue().stream().filter(coi -> projectEntry.getValue().contains(coi)).distinct().collect(Collectors.toList()));
                    crmContractProjectOrderIdsMap.put(key, initCrmOrderIds);
                } else {
                    // 交集
                    crmContractProjectOrderIds.addAll(contractEntry.getValue().stream().filter(coi -> projectEntry.getValue().contains(coi)).distinct().collect(Collectors.toList()));
                }
            }
        }


        final List<Integer> finalCrmOrderIds = crmContractProjectOrderIdsMap.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList());

        log.info("getCrmContractProjectStatistics found crm order ids of crmContractProjectOrderIdsMap = {}", crmContractProjectOrderIdsMap);


        // 成本
        List<CrmExpenditurePo> allCrmExpenditures = getCrmExpenditures(queryTime)
                .stream().filter(crmExpenditurePo -> finalCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                .collect(Collectors.toList());

        log.info("getCrmContractProjectStatistics found crm expenditures: {}", allCrmExpenditures);


        // btp来源的成本
        Map<String, List<CrmExpenditurePo>> btpCrmApprovedExpenditureMap = getCrmBTPApprovedExpenditures(allCrmExpenditures);

        // 后付费花火订单成本
        Map<Long, CrmExpenditurePo> postPaidPickUpOrderNoExpenditureMap = getCrmPostPaidPickUpOrderExpenditures(allCrmExpenditures);
        Map<Integer, List<CrmExpenditurePo>> crmOrderExpenditureOfPostPaidPickUpOrderMap = postPaidPickUpOrderNoExpenditureMap.entrySet().stream().collect(Collectors.groupingBy(
                entry -> entry.getValue().getCrmOrderId()
        )).entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> entry.getValue().stream().map(Map.Entry::getValue).collect(Collectors.toList())
        ));


        // 花火订单
        // 后付费花火订单
        List<Long> postPaidPickUpOrderNos = crmOrderExpenditureOfPostPaidPickUpOrderMap.values().stream().flatMap(List::stream).map(CrmExpenditurePo::getPickUpOrderNo)
                .filter(pickUpOrderNo -> pickUpOrderNo != 0L).distinct().collect(Collectors.toList());

        // 预付费花火订单
        Set<Pair<Integer, Integer>> crmContractProjectIdPairs = crmContractProjectOrderIdsMap.keySet();
        final Map<Pair<Integer, Integer>, List<Long>> contractProjectPrePaidPickUpOrderMap = new ConcurrentHashMap<>();

        CollectionWithDefaultPoolHelper.callInBatchesAsync(crmContractProjectIdPairs, 1, eachCrmContractProjectIdPairs ->
                contractProjectPrePaidPickUpOrderMap.put(Pair.of(eachCrmContractProjectIdPairs.get(0).getLeft(), eachCrmContractProjectIdPairs.get(0).getRight()), getPrePaidPickUpOrderNos(eachCrmContractProjectIdPairs.get(0).getLeft(), eachCrmContractProjectIdPairs.get(0).getRight()))
        );

        List<Long> allPickUpOrderNos = new ArrayList<>();
        allPickUpOrderNos.addAll(contractProjectPrePaidPickUpOrderMap.values().stream().flatMap(List::stream).distinct().collect(Collectors.toList()));
        allPickUpOrderNos.addAll(postPaidPickUpOrderNos);

        log.info("getCrmContractProjectStatistics found pick up order nos: {}", allPickUpOrderNos);

        Map<Long, OrderInfoForCrmSynDto> pickUpOrderDataMap = getPickUpOrderData(allPickUpOrderNos);


        List<String> statusList = new ArrayList<>();
//        statusList.add(0);
//        statusList.add(1);
        statusList.add("2");
        statusList.add("4");
        List<String> inFlowList = new ArrayList<>();
        inFlowList.add("0");

        List<String> allPrCodes = new ArrayList<>(btpCrmApprovedExpenditureMap.keySet());
//        allPrCodes.add(prCode);
        Map<String, BTPPurchaseBatchDto> prMap = getBTPPrsByStatus(allPrCodes, statusList, inFlowList);

        log.info("getCrmContractProjectStatistics found pr map: {}", prMap);


        // 计算
        List<CrmContractProjectStatisticsDto.CrmContractProject> dataList = new ArrayList<>();

        crmContractProjectOrderIdsMap.entrySet().stream()
                .filter(entry -> !CollectionUtils.isEmpty(entry.getValue()))
                .forEach(entry -> {

                    List<Integer> eachCrmOrderIds = entry.getValue();
                    Integer crmContractId = entry.getKey().getLeft();
                    Integer crmProjectId = entry.getKey().getRight();


                    log.info("getCrmContractProjectStatistics found eachCrmOrderIds of crmContractId = {}, crmProjectId = {}, eachCrmOrderIds = {}", crmContractId, crmProjectId, eachCrmOrderIds);

                    long crmOrderTotalRevenue = 0L;

                    // crm订单收入
                    crmOrderTotalRevenue += crmOrderMap.values().stream().filter(crmOrderPo -> eachCrmOrderIds.contains(crmOrderPo.getId()))
                            .filter(crmOrderPo -> crmOrderPo.getType() != 10)  // 排除花火的订单
                            .map(crmOrderPo -> new BigDecimal(crmOrderPo.getAmount()).multiply(crmOrderPo.getDiscount()).divide(new BigDecimal(100), RoundingMode.UNNECESSARY))
                            .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                            .setScale(0, RoundingMode.HALF_UP).longValue();

                    // 花火订单收入
                    // 预付费花火订单
                    List<Long> eachPrePaidPickUpOrderNos = contractProjectPrePaidPickUpOrderMap.get(Pair.of(crmContractId, crmProjectId));

                    log.info("getCrmContractProjectStatistics found eachPrePaidPickUpOrderNos of crmContractId = {}, crmProjectId = {}, eachPrePaidPickUpOrderNos = {}", crmContractId, crmProjectId, eachPrePaidPickUpOrderNos);

                    crmOrderTotalRevenue += pickUpOrderDataMap.values().stream().filter(orderInfoForCrmSynDto -> eachPrePaidPickUpOrderNos.contains(orderInfoForCrmSynDto.getOrderNo()))
                            .map(OrderInfoForCrmSynDto::getActualTotalPaidPrice)
                            .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                            .multiply(new BigDecimal(100))
                            .setScale(0, RoundingMode.HALF_UP).longValue();


                    // 后付费花火订单收入
                    List<Long> eachPostPaidPickUpOrderNos = crmOrderExpenditureOfPostPaidPickUpOrderMap.values().stream().flatMap(List::stream)
                            .filter(crmExpenditurePo -> eachCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                            .map(CrmExpenditurePo::getPickUpOrderNo)
                            .filter(pickUpOrderNo -> pickUpOrderNo != 0L)
                            .distinct().collect(Collectors.toList());


                    log.info("getCrmContractProjectStatistics found eachPostPaidPickUpOrderNos of crmContractId = {}, crmProjectId = {}, eachPostPaidPickUpOrderNos = {}", crmContractId, crmProjectId, eachPostPaidPickUpOrderNos);


                    crmOrderTotalRevenue += pickUpOrderDataMap.values().stream().filter(orderInfoForCrmSynDto -> eachPostPaidPickUpOrderNos.contains(orderInfoForCrmSynDto.getOrderNo()))
                            .map(OrderInfoForCrmSynDto::getActualTotalPaidPrice)
                            .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd)
                            .multiply(new BigDecimal(100))
                            .setScale(0, RoundingMode.HALF_UP).longValue();


                    // 成本（待审批）
                    long auditingTotalExpenditure = 0L;
//                    BTPPurchaseBatchDto btpPurchaseBatchDto = prMap.get(prCode);


                    auditingTotalExpenditure += btpAuditingRequestDto.getPrLines().stream()
                            .filter(prLineAuditingRequestDto -> prLineAuditingRequestDto.getCrmOrderId() != null)
                            .filter(prLineAuditingRequestDto -> eachCrmOrderIds.contains(prLineAuditingRequestDto.getCrmOrderId().intValue()))
                            .mapToLong(BTPAuditingRequestDto.PrLineAuditingRequestDto::getPrice).sum();


                    // 成本（审批通过）
                    long approvedTotalExpenditure = 0L;
                    // 预付费花火订单
                    approvedTotalExpenditure += pickUpOrderDataMap.values().stream().filter(orderInfoForCrmSynDto -> eachPrePaidPickUpOrderNos.contains(orderInfoForCrmSynDto.getOrderNo()))
                            .map(OrderInfoForCrmSynDto::getPlatformTotalExpenses)
                            .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd).multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();

                    // 后付费花火订单
                    approvedTotalExpenditure += pickUpOrderDataMap.values().stream().filter(orderInfoForCrmSynDto -> eachPostPaidPickUpOrderNos.contains(orderInfoForCrmSynDto.getOrderNo()))
                            .map(OrderInfoForCrmSynDto::getPlatformTotalExpenses)
                            .reduce(BigDecimal.ZERO, PriceMathUtils::nullSafeAdd).multiply(new BigDecimal(100)).setScale(0, RoundingMode.HALF_UP).longValue();

                    // 非花火订单、非BTP来源的成本
                    approvedTotalExpenditure += allCrmExpenditures.stream()
                            .filter(crmExpenditurePo -> eachCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                            .filter(crmExpenditurePo -> crmExpenditurePo.getSource() != ExpenditureSourceType.PICKUP_PLATFORM.getCode())
                            .filter(crmExpenditurePo -> crmExpenditurePo.getSource() != ExpenditureSourceType.BTP.getCode())
                            .filter(crmExpenditurePo -> crmExpenditurePo.getAuditStatus() == 4)
                            .mapToLong(CrmExpenditurePo::getPrice).sum();

                    // BTP来源的成本
                    LocalDateTime limitTime = new Timestamp(queryTime).toLocalDateTime();
                    log.info("getCrmContractProjectStatistics query datetime: {}", limitTime);

                    approvedTotalExpenditure += allCrmExpenditures.stream()
                            .filter(crmExpenditurePo -> crmExpenditurePo.getAuditStatus() == 4)
                            .filter(crmExpenditurePo -> eachCrmOrderIds.contains(crmExpenditurePo.getCrmOrderId()))
                            .filter(crmExpenditurePo -> crmExpenditurePo.getSource() == ExpenditureSourceType.BTP.getCode())
                            .filter(crmExpenditurePo -> prMap.get(crmExpenditurePo.getBtpBatchCode()) != null)
                            .filter(crmExpenditurePo -> StringUtils.isBlank(prMap.get(crmExpenditurePo.getBtpBatchCode()).getApprovedTime()) ||
                                    LocalDateTime.from(DATE_TIME_FORMATTER.parse(prMap.get(crmExpenditurePo.getBtpBatchCode()).getApprovedTime())).isBefore(limitTime))
                            .mapToLong(crmExpenditurePo -> {
                                BTPPurchaseBatchDto btpPurchaseBatchDto = prMap.get(crmExpenditurePo.getBtpBatchCode());
                                String totalCny = btpPurchaseBatchDto.getBtpPurchaseDetails().stream().filter(btpPurchaseDetailDto -> crmExpenditurePo.getBtpDetailCode().equals(btpPurchaseDetailDto.getCode())).findFirst().get().getTotalCny();
                                return StringUtils.isBlank(totalCny) ? 0L : Long.parseLong(totalCny) * 100;
                            })
                            .sum();


                    // 拼装
                    dataList.add(
                            CrmContractProjectStatisticsDto.CrmContractProject.builder()
                                    .crmContractId(crmContractId.longValue())
                                    .crmContractName(crmContractMap.get(eachCrmOrderIds.get(0)).getName())
                                    .crmProjectId(crmProjectId.longValue())
                                    .crmProjectName(crmProjectMap.get(eachCrmOrderIds.get(0)).getName())
                                    .crmOrderTotalRevenue(crmOrderTotalRevenue)
                                    .approvedTotalExpenditure(approvedTotalExpenditure)
                                    .approvedExpenditureRate(crmOrderTotalRevenue == 0L ? BigDecimal.ZERO.setScale(4, RoundingMode.UNNECESSARY) :
                                            new BigDecimal(approvedTotalExpenditure).divide(new BigDecimal(crmOrderTotalRevenue), 4, RoundingMode.HALF_UP))
                                    .auditingTotalExpenditure(auditingTotalExpenditure)
                                    .auditingExpenditureRate(crmOrderTotalRevenue == 0L ? BigDecimal.ZERO.setScale(4, RoundingMode.UNNECESSARY) :
                                            new BigDecimal(auditingTotalExpenditure).divide(new BigDecimal(crmOrderTotalRevenue), 4, RoundingMode.HALF_UP))
                                    .totalExpenditure(auditingTotalExpenditure + approvedTotalExpenditure)
                                    .totalExpenditureRate(crmOrderTotalRevenue == 0L ? BigDecimal.ZERO.setScale(4, RoundingMode.UNNECESSARY) :
                                            new BigDecimal(auditingTotalExpenditure + approvedTotalExpenditure).divide(new BigDecimal(crmOrderTotalRevenue), 4, RoundingMode.HALF_UP))
                                    .build()
                    );

                });


        return CrmContractProjectStatisticsDto.builder()
                .prCode(prCode)
                .crmContractProjects(dataList)
                .build();
    }

    private ExpenditureDto initBTPExpenditureDto(
            String btpProjectCode, String btpBatchCode,
            Integer btpBatchVersion, Integer supplierDepartmentId,
            BTPPurchaseDetailDto btpPurchaseDetailDto) {

        Long totalCny = StringUtils.isBlank(btpPurchaseDetailDto.getTotalCny()) ? null : new BigDecimal(btpPurchaseDetailDto.getTotalCny()).multiply(BigDecimal.valueOf(100L)).longValue();
        Timestamp now = Timestamp.valueOf(ZonedDateTime.now().toLocalDateTime());

        Integer crmOrderId = Utils.isPositive(btpPurchaseDetailDto.getCrmOrderId()) ? btpPurchaseDetailDto.getCrmOrderId().intValue() : null;
        Integer crmContractId = null;
        if (crmOrderId != null) {
            crmContractId = crmOrderDao.selectByPrimaryKey(crmOrderId).getCrmContractId();
        }

        return ExpenditureDto.builder()
                .name(btpPurchaseDetailDto.getGoodsName() + "-" + btpBatchCode + "-" + now)
                .departmentId(supplierDepartmentId)
                .beginTime(null)
                .endTime(null)
                .remark(btpPurchaseDetailDto.getRequestExplain())
                .operator(Operator.SYSTEM.getOperatorName())
                .status(null)
                .payFlag(null)
                .adCostFlag(null)
                .type(iExpenditureTypeService.getTypeIdBySecondTypeId(btpExpenditureSecondTypeCode))
                .secondType(btpExpenditureSecondTypeCode)
                .btpDetailCode(btpPurchaseDetailDto.getCode())
                .source(ExpenditureSourceType.BTP.getCode())
                .btpTime(now)
                .auditStatus(NewExpenditureAuditStatus.WAITING_FOR_AUDIT.getCode())
                .expenditureStatus(ExpenditureStatus.WAITING_FOR_APPROVAL.getCode())
                .btpBatchCode(btpBatchCode)
                .btpProjectCode(btpProjectCode)
                .btpBatchVersion(btpBatchVersion)
                .btpDetailTotalCny(totalCny)
                .btpCategoryName(btpPurchaseDetailDto.getCategoryName())
                .btpBudgetPurposeName(btpPurchaseDetailDto.getBudgetPurposeName())
                .price(totalCny)
                .crmOrderId(crmOrderId)
                .crmContractId(crmContractId)
                .build();
    }

    private Pair<ExpenditureStatus, NewExpenditureAuditStatus> statusTransition(
            Integer expenditureId, String btpBatchCode, Integer currentBtpBatchVersion, Integer theLatestBtpBatchVersion,
            Integer currentExpenditureStatusCode, Integer currentNewExpenditureAuditStatusCode,
            Integer btpPurchaseBatchStatusCode) {

        ExpenditureStatus currentExpenditureStatus = ExpenditureStatus.getByCode(currentExpenditureStatusCode);
        NewExpenditureAuditStatus currentNewExpenditureAuditStatus = NewExpenditureAuditStatus.getByCode(currentNewExpenditureAuditStatusCode);
        BTPPurchaseBatchDto.Status btpPurchaseBatchStatus = BTPPurchaseBatchDto.Status.getByCode(btpPurchaseBatchStatusCode);

        Pair<ExpenditureStatus, NewExpenditureAuditStatus> statuses;
        switch (Objects.requireNonNull(btpPurchaseBatchStatus)) {
            case WAITING_FOR_COMMIT:
                statuses = Pair.of(ExpenditureStatus.WAITING_FOR_APPROVAL, NewExpenditureAuditStatus.WAITING_FOR_AUDIT);
                break;
            case EXECUTING:
                if (theLatestBtpBatchVersion > currentBtpBatchVersion) {
                    statuses = Pair.of(ExpenditureStatus.WAITING_FOR_APPROVAL, NewExpenditureAuditStatus.WAITING_FOR_AUDIT);
                } else {
                    statuses = Pair.of(currentExpenditureStatus, currentNewExpenditureAuditStatus);
                }
                break;
            case ABORTED:
                if (currentExpenditureStatus != ExpenditureStatus.EXECUTION_COMPLETED) {
                    statuses = Pair.of(ExpenditureStatus.ABORT, NewExpenditureAuditStatus.ABORT);
                } else {
                    statuses = Pair.of(currentExpenditureStatus, NewExpenditureAuditStatus.ABORT);
                }
                break;
            case AUDITING:
                statuses = Pair.of(ExpenditureStatus.WAITING_FOR_APPROVAL, NewExpenditureAuditStatus.WAITING_FOR_AUDIT);
                break;
            case CLOSED:
                statuses = Pair.of(currentExpenditureStatus, currentNewExpenditureAuditStatus);
                break;
            default:
                statuses = Pair.of(currentExpenditureStatus, currentNewExpenditureAuditStatus);
                break;
        }

        log.info("received btp batch update: the expenditure {} of btp batch code {} status transition to status={}, auditStatus={}, currentBtpBatchVersion={}, theLatestBtpBatchVersion={}",
                expenditureId, btpBatchCode, statuses.getLeft().getDescription(), statuses.getRight().getDescription(), currentBtpBatchVersion, theLatestBtpBatchVersion);
        return statuses;
    }

    private Pair<ExpenditureStatus, NewExpenditureAuditStatus> repairStatusTransition(
            String btpDetailCode, String btpBatchCode, Integer currentBtpBatchVersion, Integer theLatestBtpBatchVersion,
            Integer currentExpenditureStatusCode, Integer currentNewExpenditureAuditStatusCode,
            Integer btpPurchaseBatchStatusCode) {

        ExpenditureStatus currentExpenditureStatus = ExpenditureStatus.getByCode(currentExpenditureStatusCode);
        NewExpenditureAuditStatus currentNewExpenditureAuditStatus = NewExpenditureAuditStatus.getByCode(currentNewExpenditureAuditStatusCode);
        BTPPurchaseBatchDto.Status btpPurchaseBatchStatus = BTPPurchaseBatchDto.Status.getByCode(btpPurchaseBatchStatusCode);

        Pair<ExpenditureStatus, NewExpenditureAuditStatus> statuses;
        switch (Objects.requireNonNull(btpPurchaseBatchStatus)) {
            case WAITING_FOR_COMMIT: //待提交
            case AUDITING: //审批中
                statuses = Pair.of(ExpenditureStatus.WAITING_FOR_APPROVAL, NewExpenditureAuditStatus.WAITING_FOR_AUDIT);
                break;
            case EXECUTING: //执行中
            case CLOSED: //关闭
                statuses = Pair.of(ExpenditureStatus.EXECUTING, NewExpenditureAuditStatus.APPROVED);
                break;
            case ABORTED: //废弃
                statuses = Pair.of(ExpenditureStatus.ABORT, NewExpenditureAuditStatus.ABORT);
                break;
            default:
                statuses = Pair.of(currentExpenditureStatus, currentNewExpenditureAuditStatus);
                break;
        }

        log.info("received btp batch update: the expenditure {} of btp batch code {} status transition to status={}, auditStatus={}, currentBtpBatchVersion={}, theLatestBtpBatchVersion={}",
                btpDetailCode, btpBatchCode, statuses.getLeft().getDescription(), statuses.getRight().getDescription(), currentBtpBatchVersion, theLatestBtpBatchVersion);
        return statuses;
    }

    private String buildBtpOaFlowOrderUrl(String btpBatchCode, Integer btpBatchVersion) {
        return btpConfig.getBtpOaFlowUrlPrefix() + "?pt=" + btpBatchVersion + "&pc=" + btpBatchCode;
    }

    private void handleDeletedBtpDetails(BTPPurchaseBatchDto btpPurchaseBatchDto) {
        String btpBatchCode = btpPurchaseBatchDto.getCode();
        List<String> currentTotalDetailCodes = btpPurchaseBatchDto.getBtpPurchaseDetails().stream().map(BTPPurchaseDetailDto::getCode)
                .distinct().collect(Collectors.toList());

        CrmExpenditurePoExample example = new CrmExpenditurePoExample();
        CrmExpenditurePoExample.Criteria criteria = example.createCriteria();
        criteria.andBtpBatchCodeEqualTo(btpBatchCode).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmExpenditurePo> existedCrmExpenditurePos = crmExpenditureDao.selectByExample(example);
        List<CrmExpenditurePo> deletedCrmExpenditurePos = existedCrmExpenditurePos.stream()
                .filter(po -> !currentTotalDetailCodes.contains(po.getBtpDetailCode())).collect(Collectors.toList());

        for (CrmExpenditurePo crmExpenditurePo : deletedCrmExpenditurePos) {
            crmExpenditurePo.setExpenditureStatus(ExpenditureStatus.ABORT.getCode());
            crmExpenditurePo.setAuditStatus(NewExpenditureAuditStatus.ABORT.getCode());
            crmExpenditureDao.updateByPrimaryKeySelective(crmExpenditurePo);
        }

        log.info("deleted: {}, btpPurchaseBatchDto: {}", deletedCrmExpenditurePos.stream().map(CrmExpenditurePo::getId)
                .distinct().collect(Collectors.toList()), btpPurchaseBatchDto);
    }

    private Map<Long, OrderInfoForCrmSynDto> getPickUpOrderData(List<Long> pickUpOrderNos) {
        if (CollectionUtils.isEmpty(pickUpOrderNos)) {
            return new HashMap<>();
        }

        List<OrderInfoForCrmSynDto> pickUpOrderDataList = CollectionWithDefaultPoolHelper.callInBatchesAsync(pickUpOrderNos, 100, eachPickUpOrderNos ->
                iSoaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder().orderNos(eachPickUpOrderNos).build())
        );

        return pickUpOrderDataList.stream()
                .filter(orderInfoForCrmSynDto -> orderInfoForCrmSynDto.getOrderStatus() != null && orderInfoForCrmSynDto.getOrderStatus() != 1 && orderInfoForCrmSynDto.getOrderStatus() != -1)
                .collect(Collectors.toMap(
                        OrderInfoForCrmSynDto::getOrderNo,
                        Function.identity(),
                        (k1, k2) -> k1
                ));
    }

    private List<Long> getPrePaidPickUpOrderNos(Integer crmContractId, Integer crmProjectId) {
        PrePayPickupOrderPoExample prePayPickupOrderPoExample = new PrePayPickupOrderPoExample();
        PrePayPickupOrderPoExample.Criteria prePayPickupOrderPoExampleCriteria = prePayPickupOrderPoExample.createCriteria();
        if (crmContractId != null) {
            prePayPickupOrderPoExampleCriteria.andCrmContractIdEqualTo(crmContractId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        }

        if (crmProjectId != null) {
            prePayPickupOrderPoExampleCriteria.andCrmProjectIdEqualTo(crmProjectId.longValue()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        }

        return prePayPickupOrderDao.selectByExample(prePayPickupOrderPoExample).stream().map(PrePayPickupOrderPo::getOrderNo).distinct().collect(Collectors.toList());
    }

    private List<Long> getPrePaidPickUpOrderNos(Integer crmProjectId) {
        return this.getPrePaidPickUpOrderNos(null, crmProjectId);
    }

    @SneakyThrows
    public Map<String, BTPPurchaseBatchDto> getBTPPrsByStatus(List<String> prCodes, List<String> statusList, List<String> inFlowList) {
        if (CollectionUtils.isEmpty(prCodes)) {
            return new HashMap<>();
        }


        JSONObject requestBody = new JSONObject();
        requestBody.put("prCodeList", prCodes);
        requestBody.put("statusList", statusList);
        requestBody.put("inFlowList", inFlowList);

        String requestBodyString = GsonUtils.toJson(requestBody);

        log.info("get prs by status param: {}", requestBodyString);

        OkHttpUtils.BodyPoster bodyPoster = OkHttpUtils.bodyPost(btpConfig.getPrListUrl())
                .json(requestBodyString);

        String responseString = bodyPoster.callForString();
        log.info("get prs by status response: {}", responseString);

        BTPPurchaseBatchResponseDto btpPurchaseBatchResponseDto = OBJECT_MAPPER.readValue(responseString, BTPPurchaseBatchResponseDto.class);
        return btpPurchaseBatchResponseDto.getBtpPurchaseBatches().stream().collect(Collectors.toMap(
                BTPPurchaseBatchDto::getCode,
                Function.identity(),
                (k1, k2) -> k1
        ));
    }

    public List<CrmOrderPo> getCrmOrderByContractXProject(Long queryTime, List<Integer> crmOrderIds) {
        if (queryTime == null || CollectionUtils.isEmpty(crmOrderIds)) {
            return new ArrayList<>();
        }

        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();
        // 过滤掉废弃的状态
        crmOrderPoExampleCriteria.andIdIn(crmOrderIds).andStatusNotEqualTo(4)
                .andCtimeLessThanOrEqualTo(new Timestamp(queryTime))
                .andResourceTypeEqualTo(2)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        List<Integer> crmContractIds = crmOrderDao.selectByExample(crmOrderPoExample).stream().map(CrmOrderPo::getCrmContractId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(crmContractIds)) {
            return new ArrayList<>();
        }

        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
        crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(crmOrderIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<Integer> crmProjectIds = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream().map(CrmOrderExtraPo::getProjectItemId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(crmProjectIds)) {
            return new ArrayList<>();
        }

        // 扩大
        CrmOrderExtraPoExample crmOrderExtraPoExample2 = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria2 = crmOrderExtraPoExample2.createCriteria();
        crmOrderExtraPoExampleCriteria2.andProjectItemIdIn(crmProjectIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<Integer> crmOrderIdsByProject = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample2).stream().map(CrmOrderExtraPo::getCrmOrderId).distinct().collect(Collectors.toList());

        CrmOrderPoExample crmOrderPoExample2 = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria2 = crmOrderPoExample2.createCriteria();
        crmOrderPoExampleCriteria2.andCrmContractIdIn(crmContractIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<Integer> crmOrderIdsByContract = crmOrderDao.selectByExample(crmOrderPoExample2).stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

        // 合同 x 项目维度
        crmOrderIdsByProject.addAll(crmOrderIdsByContract);
        List<Integer> finalCrmOrderIds = crmOrderIdsByProject;

        CrmOrderPoExample crmOrderPoExample3 = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria3 = crmOrderPoExample3.createCriteria();
        // 过滤掉废弃的状态
        crmOrderPoExampleCriteria3.andIdIn(finalCrmOrderIds).andStatusNotEqualTo(4)
                .andCtimeLessThanOrEqualTo(new Timestamp(queryTime))
                .andResourceTypeEqualTo(2)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmOrderDao.selectByExample(crmOrderPoExample3);
    }

    public Triple<CrmContractPo, CrmProjectItemPackagePo, List<CrmOrderPo>> getCrmOrderByOneContractXProject(Long queryTime, Long crmContractId, Long crmProjectId) {
        if (queryTime == null || crmContractId == null || crmProjectId == null) {
            throw new IllegalArgumentException("queryTime == null || crmContractId == null || crmProjectId == null");
        }

        // 合同
        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();
        crmOrderPoExampleCriteria.andCrmContractIdEqualTo(crmContractId.intValue())
                .andCtimeLessThanOrEqualTo(new Timestamp(queryTime))
                .andStatusNotEqualTo(4).andResourceTypeEqualTo(2)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmOrderPo> crmOrderPoList = crmOrderDao.selectByExample(crmOrderPoExample);

        List<Integer> crmOrderIdsByContract = crmOrderPoList.stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(crmOrderIdsByContract)) {
            return null;
        }

        // 项目
        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
        crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(crmOrderIdsByContract).andProjectItemIdEqualTo(crmProjectId.intValue()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<Integer> finalCrmOrderIds = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream().map(CrmOrderExtraPo::getCrmOrderId).distinct().collect(Collectors.toList());

        // 订单
        CrmOrderPoExample crmOrderPoExample2 = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria2 = crmOrderPoExample2.createCriteria();
        crmOrderPoExampleCriteria2.andIdIn(finalCrmOrderIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmOrderPo> crmOrders = crmOrderDao.selectByExample(crmOrderPoExample2);

        // 合同
        CrmContractPo crmContract = crmContractDao.selectByPrimaryKey(crmContractId.intValue());

        // 项目
        CrmProjectItemPackagePo crmProjectItemPackage = crmProjectItemPackageDao.selectByPrimaryKey(crmProjectId.intValue());

        return Triple.of(crmContract, crmProjectItemPackage, crmOrders);
    }

    public Pair<CrmProjectItemPackagePo, List<CrmOrderPo>> getCrmOrderByOneProject(Long queryTime, Long crmProjectId) {
        if (queryTime == null || crmProjectId == null) {
            throw new IllegalArgumentException("queryTime == null || crmProjectId == null");
        }

        // 项目
        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
        crmOrderExtraPoExampleCriteria.andProjectItemIdEqualTo(crmProjectId.intValue()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<Integer> finalCrmOrderIds = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream().map(CrmOrderExtraPo::getCrmOrderId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(finalCrmOrderIds)) {
            return null;
        }

        // 订单
        CrmOrderPoExample crmOrderPoExample2 = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria2 = crmOrderPoExample2.createCriteria();
        crmOrderPoExampleCriteria2.andIdIn(finalCrmOrderIds)
                .andCtimeLessThanOrEqualTo(new Timestamp(queryTime))
                .andStatusNotEqualTo(4).andResourceTypeEqualTo(2)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmOrderPo> crmOrders = crmOrderDao.selectByExample(crmOrderPoExample2);

        // 项目
        CrmProjectItemPackagePo crmProjectItemPackage = crmProjectItemPackageDao.selectByPrimaryKey(crmProjectId.intValue());

        return Pair.of(crmProjectItemPackage, crmOrders);
    }

    /**
     * 不包含订单条件的过滤
     *
     * @param crmOrderIds
     * @return
     */
    public Triple<Map<Integer, CrmOrderPo>, Map<Integer, CrmContractPo>, Map<Integer, CrmProjectItemPackagePo>> getCrmOrderContractXProjectMap(List<Integer> crmOrderIds) {
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return Triple.of(new HashMap<>(), new HashMap<>(), new HashMap<>());
        }

//        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
//        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();
//        crmOrderPoExampleCriteria.andIdIn(crmOrderIds);
//        List<CrmOrderPo> crmOrderPoList = crmOrderDao.selectByExample(crmOrderPoExample);

        List<CrmOrderPo> crmOrderPoList = getCrmOrdersByAsync(crmOrderIds);

        Map<Integer, CrmOrderPo> crmOrderMap = crmOrderPoList.stream().collect(Collectors.toMap(
                CrmOrderPo::getId,
                Function.identity(),
                (k1, k2) -> k1
        ));

        List<Integer> finalCrmOrderIds = crmOrderPoList.stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());

        // 项目信息
//        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
//        CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
//        crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(finalCrmOrderIds);
//        List<CrmOrderExtraPo> crmOrderExtraPoList = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample);

        Map<Integer, Integer> crmOrderProjectIdMap = getCrmOrderProjectIdMapFromOrderExtra(finalCrmOrderIds);

        List<Integer> crmProjectIds = new ArrayList<>(crmOrderProjectIdMap.values());

        if (CollectionUtils.isEmpty(crmProjectIds)) {
            return Triple.of(new HashMap<>(), new HashMap<>(), new HashMap<>());
        }

        CrmProjectItemPackagePoExample crmProjectItemPackagePoExample = new CrmProjectItemPackagePoExample();
        CrmProjectItemPackagePoExample.Criteria crmProjectItemPackagePoExampleCriteria = crmProjectItemPackagePoExample.createCriteria();
        crmProjectItemPackagePoExampleCriteria.andIdIn(crmProjectIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Map<Integer, CrmProjectItemPackagePo> crmProjectMap = crmProjectItemPackageDao.selectByExample(crmProjectItemPackagePoExample).stream().collect(Collectors.toMap(
                CrmProjectItemPackagePo::getId,
                Function.identity(),
                (k1, k2) -> k1
        ));


        // 合同信息
        Map<Integer, Integer> crmOrderContractIdMap = crmOrderPoList.stream().collect(Collectors.toMap(
                CrmOrderPo::getId,
                CrmOrderPo::getCrmContractId,
                (k1, k2) -> k1
        ));

        List<Integer> crmContractIds = crmOrderPoList.stream().map(CrmOrderPo::getCrmContractId).distinct().collect(Collectors.toList());

        CrmContractPoExample crmContractPoExample = new CrmContractPoExample();
        CrmContractPoExample.Criteria crmContractPoExampleCriteria = crmContractPoExample.createCriteria();
        crmContractPoExampleCriteria.andIdIn(crmContractIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Map<Integer, CrmContractPo> crmContractMap = crmContractDao.selectByExample(crmContractPoExample).stream().collect(Collectors.toMap(
                CrmContractPo::getId,
                Function.identity(),
                (k1, k2) -> k1
        ));


        // 拼装
        Map<Integer, CrmProjectItemPackagePo> crmOrderProjectMap = crmOrderProjectIdMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> crmProjectMap.get(entry.getValue()),
                (k1, k2) -> k1
        ));

        Map<Integer, CrmContractPo> crmOrderContractMap = crmOrderContractIdMap.entrySet().stream().collect(Collectors.toMap(
                Map.Entry::getKey,
                entry -> crmContractMap.get(entry.getValue()),
                (k1, k2) -> k1
        ));

        return Triple.of(crmOrderMap, crmOrderContractMap, crmOrderProjectMap);
    }

    public List<CrmExpenditurePo> getCrmExpenditures(Long queryTime) {
        CrmExpenditurePoExample crmExpenditurePoExample = new CrmExpenditurePoExample();
        CrmExpenditurePoExample.Criteria crmExpenditurePoExampleCriteria = crmExpenditurePoExample.createCriteria();
        crmExpenditurePoExampleCriteria.andSourceNotEqualTo(0).andExpenditureStatusNotEqualTo(ExpenditureStatus.ABORT.getCode())
                .andCtimeLessThanOrEqualTo(new Timestamp(queryTime))
                .andSecondTypeNotEqualTo(excludedExpenditureSecondTypeId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmExpenditureDao.selectByExample(crmExpenditurePoExample);
    }

    // group by btp pr code
    private Map<String, List<CrmExpenditurePo>> getCrmBTPApprovedExpenditures(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<CrmExpenditurePo> crmBTPExpenditures = crmExpenditurePoList.stream()
                .filter(crmExpenditurePo -> ExpenditureSourceType.BTP.getCode() == crmExpenditurePo.getSource())
                .filter(crmExpenditurePo -> crmExpenditurePo.getAuditStatus() == 4)  // 4审核通过
                .filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0)
                .collect(Collectors.toList());
        return crmBTPExpenditures.stream().collect(Collectors.groupingBy(CrmExpenditurePo::getBtpBatchCode));
    }

    private Map<Long, CrmExpenditurePo> getCrmPostPaidPickUpOrderExpenditures(List<CrmExpenditurePo> crmExpenditurePoList) {
        return crmExpenditurePoList.stream().filter(crmExpenditurePo -> ExpenditureSourceType.PICKUP_PLATFORM.getCode() == crmExpenditurePo.getSource())
                .filter(crmExpenditurePo -> crmExpenditurePo.getSecondType() != 81)  // 不是预付费商单
                .filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0)
                .collect(Collectors.toMap(
                        CrmExpenditurePo::getPickUpOrderNo,
                        Function.identity(),
                        (k1, k2) -> k1
                ));
    }

    private Map<Long, CrmExpenditurePo> getCrmPrePaidPickUpOrderExpenditures(List<CrmExpenditurePo> crmExpenditurePoList) {
        return crmExpenditurePoList.stream().filter(crmExpenditurePo -> ExpenditureSourceType.PICKUP_PLATFORM.getCode() == crmExpenditurePo.getSource())
                .filter(crmExpenditurePo -> crmExpenditurePo.getSecondType() == 81)  // 是预付费商单
                .collect(Collectors.toMap(
                        CrmExpenditurePo::getPickUpOrderNo,
                        Function.identity(),
                        (k1, k2) -> k1
                ));
    }

    public Map<Integer, String> getExpenditureType(List<Integer> secondTypeIds) {
        // remove null
        org.apache.commons.collections4.CollectionUtils.filter(secondTypeIds, PredicateUtils.notNullPredicate());

        if (org.apache.commons.collections4.CollectionUtils.isEmpty(secondTypeIds)) {
            return new HashMap<>();
        }

        return expenditureTypeService.query(QueryExpenditureTypeDto.builder().ids(secondTypeIds).build()).stream()
                .collect(Collectors.toMap(
                        ExpenditureTypeDto::getId,
                        ExpenditureTypeDto::getName,
                        (k1, k2) -> k1
                ));
    }

    public Map<Integer, Integer> getCrmOrderProjectIdMapFromOrderExtra(List<Integer> crmOrderIds) {
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return new HashMap<>();
        }

        Map<Integer, Integer> crmOrderProjectIdMap = new ConcurrentHashMap<>();

        CollectionWithDefaultPoolHelper.callInBatchesAsync(crmOrderIds, 2000, eachCrmOrderIds -> {
            CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
            CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
            crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(eachCrmOrderIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            crmOrderProjectIdMap.putAll(crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream()
                    .filter(crmOrderExtraPo -> crmOrderExtraPo.getProjectItemId() != 0 && crmOrderExtraPo.getProjectItemId() != 1)
                    .collect(Collectors.toMap(
                            CrmOrderExtraPo::getCrmOrderId,
                            CrmOrderExtraPo::getProjectItemId,
                            (k1, k2) -> k1
                    )));

            return Collections.emptyList();
        });

        return crmOrderProjectIdMap;
    }

    public List<CrmOrderPo> getCrmOrdersByAsync(List<Integer> crmOrderIds) {
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return new ArrayList<>();
        }

        List<CrmOrderPo> crmOrders = CollectionWithDefaultPoolHelper.callInBatchesAsync(crmOrderIds, 2000, eachCrmOrderIds -> {
            CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
            CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();
            crmOrderPoExampleCriteria.andIdIn(eachCrmOrderIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            return crmOrderDao.selectByExample(crmOrderPoExample);
        });

        return crmOrders;
    }

    public List<Pair<Integer, Integer>> getBTPInvolvedContractXProjectId(List<Integer> crmOrderIds) {
        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria crmOrderPoExampleCriteria = crmOrderPoExample.createCriteria();
        crmOrderPoExampleCriteria.andIdIn(crmOrderIds);
        Map<Integer, Integer> crmOrderContractIdMap = crmOrderDao.selectByExample(crmOrderPoExample).stream().collect(Collectors.toMap(
                CrmOrderPo::getId,
                CrmOrderPo::getCrmContractId,
                (k1, k2) -> k1
        ));

        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
        crmOrderExtraPoExampleCriteria.andCrmOrderIdIn(crmOrderIds);
        Map<Integer, Integer> crmOrderProjectIdMap = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream().collect(Collectors.toMap(
                CrmOrderExtraPo::getCrmOrderId,
                CrmOrderExtraPo::getProjectItemId,
                (k1, k2) -> k1
        ));


        List<Pair<Integer, Integer>> results = new ArrayList<>();

        for (Integer crmOrderId : crmOrderIds) {
            Integer crmContractId = crmOrderContractIdMap.get(crmOrderId);
            Integer crmProjectId = crmOrderProjectIdMap.get(crmOrderId);
            if (crmProjectId != 1) {
                results.add(Pair.of(crmContractId, crmProjectId));
            }
        }

        return results;
    }

    private static <T> List<T> getPageList(List<T> sourceList, int page, int pageSize) {
        if (pageSize <= 0 || page <= 0) {
            throw new IllegalArgumentException("invalid page size: " + pageSize);
        }

        int fromIndex = (page - 1) * pageSize;
        if (sourceList == null || sourceList.size() <= fromIndex) {
            return Collections.emptyList();
        }

        // toIndex exclusive
        return sourceList.subList(fromIndex, Math.min(fromIndex + pageSize, sourceList.size()));
    }

    public String getSign(JSONObject jsonObject, String salt) {
        List<String> paramList = new ArrayList<>();
        for (Map.Entry<String, Object> entry : jsonObject.entrySet()) {
            String key = entry.getKey();
            Object value = entry.getValue();
            if (!"sign".equals(key)) {
                paramList.add(key + "=" + value);
            }
        }
        return getSign(paramList, salt);
    }

    public String getSign(List<String> params, String salt) {
        Collections.sort(params);
        StringBuilder sb = new StringBuilder();
        sb.append(Joiner.on("&").join(params));
        sb.append(salt);
        return Md5Util.md5Hash(sb.toString());
    }


    public void getCrmContractProjectIncome(Integer merchantsProjectId, List<Long> crmContractIds){
        //

    }
}
