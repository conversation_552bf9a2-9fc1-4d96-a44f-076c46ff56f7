package com.bilibili.crm.platform.biz.service.finance.automation.component.process;

import com.bilibili.crm.platform.api.finance.dto.automation.ContractReceiveMoneyProcessDto;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * 合约回款进度查询器
 *
 * <AUTHOR>
 * @date 2021/7/26 上午11:42
 */
@Slf4j
@Component
public class ContractReceiveMoneyProcessQuerier {

    @Resource(name = "commonRedisTemplate")
    private RedisTemplate<String, Object> commonRedisTemplate;
    @Autowired
    private ContractReceiveMoneyProcessQuerierByDetails contractReceiveMoneyProcessQuerierByDetails;
    @Autowired
    private ContractReceiveMoneyProcessQuerierByTotalSql contractReceiveMoneyProcessQuerierByTotalSql;

    public static final String CONTRACT_RECEIVE_MONEY_PROCESS = "CONTRACT_RECEIVE_MONEY_PROCESS";

    public Boolean releadDataToCache() {
        // 删除缓存
        Boolean result = true;// commonRedisTemplate.delete(CONTRACT_RECEIVE_MONEY_PROCESS);

        // 重新加载数据到缓存
        this.queryRecMoneyProcessFromCacheIfCan(1);
        return result;
    }

    /**
     * 如果有缓存，去缓存取；否则直接查询
     *
     * @param needRefreshCache
     * @return
     */
    public ContractReceiveMoneyProcessDto queryRecMoneyProcessFromCacheIfCan(Integer needRefreshCache) {
        ContractReceiveMoneyProcessDto contractReceiveMoneyProcessDto = null;
        // 强制刷新缓存
        if (needRefreshCache == 1) {
            contractReceiveMoneyProcessDto = doQueryContractReceiveMoneyProcessFromDb();
            return contractReceiveMoneyProcessDto;
        }

        // 先取缓存
        contractReceiveMoneyProcessDto = (ContractReceiveMoneyProcessDto) commonRedisTemplate.opsForValue().get(CONTRACT_RECEIVE_MONEY_PROCESS);
        if (contractReceiveMoneyProcessDto == null) {
            contractReceiveMoneyProcessDto = doQueryContractReceiveMoneyProcessFromDb();
        }
        return contractReceiveMoneyProcessDto;
    }

    public ContractReceiveMoneyProcessDto doQueryContractReceiveMoneyProcessFromDb() {
        ContractReceiveMoneyProcessDto contractReceiveMoneyProcessDto = contractReceiveMoneyProcessQuerierByDetails.doQueryContractReceiveMoneyProcessForWechat();
        // 放入缓存
        commonRedisTemplate.opsForValue().set(CONTRACT_RECEIVE_MONEY_PROCESS, contractReceiveMoneyProcessDto);
        return contractReceiveMoneyProcessDto;
    }
}
