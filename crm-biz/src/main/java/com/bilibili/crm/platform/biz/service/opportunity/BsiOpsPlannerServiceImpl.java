package com.bilibili.crm.platform.biz.service.opportunity;

import com.bapis.cpm.bdata.service.*;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpsPlannerService;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityPlannerQueryDTO;
import com.bilibili.crm.platform.api.bsiopportunity.dto.OpsPlannerSaveDto;
import com.bilibili.crm.platform.api.clue.dto.BsiOpportunityPlannerDto;
import com.bilibili.crm.platform.api.clue.dto.ProductModelLabel;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppPlannerHandleEnums;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppPlannerStatus;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.biz.dao.BsiOpportunityPlannerDao;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPlannerPo;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPlannerPoExample;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.repo.LogOperationRepo;
import com.bilibili.crm.platform.biz.repo.bsi.BsiOpportunityPlannerRepo;
import com.bilibili.crm.platform.biz.service.customer.CustomerQueryService;
import com.bilibili.crm.platform.biz.service.follow_manage.config.BsiOppConfig;
import com.bilibili.crm.platform.biz.util.ThreadPoolManager;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.Module;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-07-25 16:25:57
 * @description:
 **/

@Slf4j
@Component
public class BsiOpsPlannerServiceImpl implements IBsiOpsPlannerService {
    @Resource
    private BsiOpportunityPlannerRepo bsiOpportunityPlannerRepo;
    @Resource
    private BsiOpportunityPlannerDao bsiOpportunityPlannerDao;
    @Resource
    private LogOperationRepo logOperationRepo;
    @Autowired
    private BsiOpsWeChatHelper bsiOpsWeChatHelper;
    @Autowired
    private IBsiOpportunityService bsiOpportunityService;

    @Resource
    private BDataServiceGrpc.BDataServiceBlockingStub bDataServiceBlockingStub;

    @Resource
    private CustomerQueryService customerQueryService;

    @Resource
    private BsiOppConfig bsiOppConfig;
    @Autowired
    private AccAccountRepo accAccountRepo;

    public void checkPlannerSaveDto(OpsPlannerSaveDto dto) {
        if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_1.getCode())
                || Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_2.getCode())
                || Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_5.getCode())) {
            Assert.isTrue(StringUtils.isNotBlank(dto.getPlannerEmail()), "plannerEmail is null");
            Assert.isTrue(StringUtils.isNotBlank(dto.getPlannerName()), "plannerName is null");
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_3.getCode())) {
            Assert.isTrue(StringUtils.isNotBlank(dto.getRejectReason()), "rejectReason is null");
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_4.getCode())) {
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_0.getCode())) {
        } else {
            throw new IllegalArgumentException("assignType不太对");
        }
    }

    /**
     * 1：销售指定分配给策划Leader 2：策划Leader指定分配给策划专员(就是确认接单) 3：策划Leader驳回 4：策划专业上传了策划案 5:策划专员自己修改策划专员
     */
    @Override
    public void plannerHandle(OpsPlannerSaveDto dto, Operator operator) {
        checkPlannerSaveDto(dto);
        BsiOpportunityPlannerPo oldPo = getPlannerPOByBsiId(dto.getOpsId());
        if (!Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_0.getCode()) && Objects.isNull(oldPo)) {
            throw new IllegalArgumentException("商机id有错误，查不到对应的策划信息");
        }
        BsiOpportunityPlannerPo updatePo = BsiOpportunityPlannerPo.builder().id(Objects.isNull(oldPo) ? 0L : oldPo.getId()).build();
        ModifyType modifyType = null;
        if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_0.getCode())) {
            if (Objects.isNull(oldPo)) {
                BsiOpportunityPlannerPo insertPo = BsiOpportunityPlannerPo.builder()
                        .bsiOpportunityId(dto.getOpsId())
                        .status(BsiOppPlannerStatus.INIT.getCode())
                        .bsiOpportunityCreator(operator.getOperatorName())
                        .build();
                if (StringUtils.isNotBlank(dto.getPlannerEmail())) {
                    insertPo.setPlannerLeaderEmail(dto.getPlannerEmail());
                    insertPo.setPlannerLeaderName(dto.getPlannerName());
                    insertPo.setStatus(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode());
                    modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_ALLOCATE;
                    dto.setHandleType(BsiOppPlannerHandleEnums.HANDLE_1.getCode());
                }
                bsiOpportunityPlannerDao.insertSelective(insertPo);
            } else if (StringUtils.isNotBlank(dto.getPlannerEmail()) && !Objects.equals(dto.getPlannerEmail(), oldPo.getPlannerLeaderEmail()) && BsiOppPlannerHandleEnums.HANDLE_0.getToOldStatusList().contains(oldPo.getStatus())) {
                updatePo.setPlannerLeaderEmail(dto.getPlannerEmail());
                updatePo.setPlannerLeaderName(dto.getPlannerName());
                updatePo.setStatus(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode());
                bsiOpportunityPlannerDao.updateByPrimaryKeySelective(updatePo);
                modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_ALLOCATE;
                dto.setHandleType(BsiOppPlannerHandleEnums.HANDLE_1.getCode());
            }
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_1.getCode()) && BsiOppPlannerHandleEnums.HANDLE_1.getToOldStatusList().contains(oldPo.getStatus())) {
            updatePo.setPlannerLeaderEmail(dto.getPlannerEmail());
            updatePo.setPlannerLeaderName(dto.getPlannerName());
            updatePo.setStatus(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode());
            bsiOpportunityPlannerDao.updateByPrimaryKeySelective(updatePo);
            modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_ALLOCATE;
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_2.getCode()) && BsiOppPlannerHandleEnums.HANDLE_2.getToOldStatusList().contains(oldPo.getStatus())) {
            updatePo.setPlannerSpecialistEmail(dto.getPlannerEmail());
            updatePo.setPlannerSpecialistName(dto.getPlannerName());
            updatePo.setStatus(BsiOppPlannerStatus.RECEIPT_ORDER.getCode());
            bsiOpportunityPlannerDao.updateByPrimaryKeySelective(updatePo);
            modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_CONFIRM;
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_3.getCode()) && BsiOppPlannerHandleEnums.HANDLE_3.getToOldStatusList().contains(oldPo.getStatus())) {
            updatePo.setStatus(BsiOppPlannerStatus.HAS_REJECTED.getCode());
            updatePo.setRejectReason(dto.getRejectReason());
            bsiOpportunityPlannerDao.updateByPrimaryKeySelective(updatePo);
            modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_REJECT;
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_4.getCode()) && BsiOppPlannerHandleEnums.HANDLE_4.getToOldStatusList().contains(oldPo.getStatus())) {
            updatePo.setStatus(BsiOppPlannerStatus.HAVE_UPLOAD_SCHEME.getCode());
            bsiOpportunityPlannerDao.updateByPrimaryKeySelective(updatePo);
            modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_UP_PLAN_SCHEME;
        } else if (Objects.equals(dto.getHandleType(), BsiOppPlannerHandleEnums.HANDLE_5.getCode()) && BsiOppPlannerHandleEnums.HANDLE_5.getToOldStatusList().contains(oldPo.getStatus())) {
            updatePo.setPlannerSpecialistEmail(dto.getPlannerEmail());
            updatePo.setPlannerSpecialistName(dto.getPlannerName());
            bsiOpportunityPlannerDao.updateByPrimaryKeySelective(updatePo);
            modifyType = ModifyType.BSI_OPPORTUNITY_PLANNER_CHANGE_PLANNER;
        }
        if (modifyType != null) {
            BsiOpportunityPlannerPo newPo = getPlannerPOByBsiId(dto.getOpsId());
            Map<Integer, BsiOpportunityPlannerDto> newDtoMap = bsiOpportunityPlannerRepo.queryPlanMap(Lists.newArrayList(dto.getOpsId()));
            BsiOpportunityPlannerDto plannerDto = newDtoMap.getOrDefault(dto.getOpsId(), BsiOpportunityPlannerDto.builder().build());
            plannerDto.setOperatorName(operator.getOperatorName());
            logOperationRepo.insertLog(operator, NewLogOperatorDto.builder()
                    .objId(dto.getOpsId())
                    .obj(plannerDto)
                    .module(Module.BSI_OPPORTUNITY)
                    .systemType(SystemType.CRM)
                    .modifyType(modifyType)
                    .build());
            BsiOpportunityPlannerPo finalOldPo = Objects.isNull(oldPo) ? BsiOpportunityPlannerPo.builder().status(BsiOppPlannerStatus.INIT.getCode()).build() : oldPo;
            BsiOpportunityDto bsiBaseDto = bsiOpportunityService.getBsiOpportunityById(newPo.getBsiOpportunityId());
            CompletableFuture.runAsync(() -> bsiOpsWeChatHelper.sendWeChat(finalOldPo, newPo,bsiBaseDto), ThreadPoolManager.DEFAULT_THREAD_POOL_DEPRECATED);
        }
    }


    public BsiOpportunityPlannerPo getPlannerPOByBsiId(Integer bsiId) {
        if (Objects.isNull(bsiId)) {
            return null;
        }
        BsiOpportunityPlannerPoExample example = new BsiOpportunityPlannerPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andBsiOpportunityIdEqualTo(bsiId);
        List<BsiOpportunityPlannerPo> poList = bsiOpportunityPlannerDao.selectByExample(example);
        return poList.stream().findFirst().orElse(null);
    }

    @Override
    public Map<Integer, BsiOpportunityPlannerDto> queryPlanMap(List<Integer> bsiOppIds) {
        return bsiOpportunityPlannerRepo.queryPlanMap(bsiOppIds);
    }

    @Override
    public List<BsiOpportunityPlannerDto> queryPlanByUser(String user){
        return bsiOpportunityPlannerRepo.queryPlanByUser(user);
    }

    @Override
    public List<Integer> queryBsiIdListByCondition(BsiOpportunityPlannerQueryDTO queryDTO) {
        return bsiOpportunityPlannerRepo.queryBsiIdListByCondition(queryDTO);
    }

    @Deprecated
    @Override
    public List<ProductModelLabel> queryAllProductModel(Integer dataProductId) {
        BdataProductListReply listReply;
        if (dataProductId.equals(IndustryEnum.IndustryMobile.getNumber())) {
            listReply = bDataServiceBlockingStub.bdataProductList(BdataProductListReq.newBuilder()
                    .setIndustryId(dataProductId)
                    .setCrmIndustryId(BsiOppConfig.COMMERCE_SECOND_MOBILE_TEST)
                    .setCrmNewMapping(false)
                    .build());
        } else {
            listReply = bDataServiceBlockingStub.bdataProductList(BdataProductListReq.newBuilder()
                    .setIndustryId(dataProductId)
                    .setCrmIndustryId(BsiOppConfig.INDUSTRY_SECOND_CAR_TEST)
                    .setCrmNewMapping(false)
                    .build());
        }
        List<ProductModelLabel> result = new ArrayList<>();
        for (BdataBrandItem bdataBrandItem : listReply.getDataList()) {
            ProductModelLabel modelLabel = new ProductModelLabel();
            modelLabel.setProductModelLabel(bdataBrandItem.getBrandName());
            modelLabel.setProductModelId(bdataBrandItem.getBrandId());
            List<ProductModelLabel> secondProductModelLabels = new ArrayList<>();
            for (BdataProductItem productItem : bdataBrandItem.getProductsList()) {
                ProductModelLabel secondModel = new ProductModelLabel();
                secondModel.setProductModelId(productItem.getProductId());
                secondModel.setProductModelLabel(productItem.getProductName());
                secondProductModelLabels.add(secondModel);
            }
            modelLabel.setSecondProductModelLabels(secondProductModelLabels);
            result.add(modelLabel);
        }
        return result;
    }

    @Override
    public List<ProductModelLabel> queryAllProductModels(Integer accountId) {
        Assert.notNull(accountId, "广告主帐户id不能为空");
        if (!Utils.isPositive(accountId)) {
            return Lists.newArrayList();
        }
        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(accountId);
        Assert.notNull(accAccountPo, "广告主帐户不存在");
        if (Objects.isNull(accAccountPo.getUnitedThirdIndustryId()) || accAccountPo.getUnitedThirdIndustryId() == 0) {
            log.info("广告主帐户未指定三级行业, accountId:{}", accountId);
            return Lists.newArrayList();
        }
        BdataProductListReply listReply = bDataServiceBlockingStub.bdataProductList(BdataProductListReq.newBuilder()
                .setCrmIndustryId(accAccountPo.getUnitedThirdIndustryId())
                .setCrmNewMapping(true)
                .build());
        if (Objects.isNull(listReply) || CollectionUtils.isEmpty(listReply.getDataList())) {
            return Lists.newArrayList();
        }

        List<ProductModelLabel> result = listReply.getDataList().stream().map(bdataBrandItem -> {
            ProductModelLabel modelLabel = new ProductModelLabel();
            modelLabel.setProductModelLabel(bdataBrandItem.getBrandName());
            modelLabel.setProductModelId(bdataBrandItem.getBrandId());

            List<ProductModelLabel> secondProductModelLabels = new ArrayList<>();
            if (!CollectionUtils.isEmpty(bdataBrandItem.getProductsList())) {
                secondProductModelLabels = bdataBrandItem.getProductsList().stream().map(productItem -> {
                    ProductModelLabel secondModel = new ProductModelLabel();
                    secondModel.setProductModelId(productItem.getProductId());
                    secondModel.setProductModelLabel(productItem.getProductName());
                    return secondModel;
                }).collect(Collectors.toList());
            }
            modelLabel.setSecondProductModelLabels(secondProductModelLabels);
            return modelLabel;
        }).collect(Collectors.toList());
        log.info("广告主帐户 {} 三级行业所对应产品型号列表为 {}", accAccountPo, result);
        return result;
    }

    @Override
    public void importBsiProductLabel(Integer opsId, String firstProductModel, String secondProductModel) {
        BsiOpportunityDto bsiOpportunity = bsiOpportunityService.getBsiOpportunityById(opsId);
        List<ProductModelLabel> modelLabels = queryAllProductModels(bsiOpportunity.getAccountId());
        if (CollectionUtils.isEmpty(modelLabels)) {
            return;
        }
        Map<String, ProductModelLabel> modelLabelMap = modelLabels.stream().collect(Collectors.toMap(ProductModelLabel::getProductModelLabel, Function.identity(), (a, b) -> a));
        Assert.isTrue(modelLabelMap.containsKey(firstProductModel), "一级产品标签不存在");
        ProductModelLabel modelLabel = modelLabelMap.get(firstProductModel);
        ProductModelLabel secondModel = new ProductModelLabel();
        if (!StringUtils.isEmpty(secondProductModel)) {
            Map<String, ProductModelLabel> secondProductModelMap = modelLabel.getSecondProductModelLabels().stream().collect(Collectors.toMap(ProductModelLabel::getProductModelLabel, Function.identity(), (a, b) -> a));
            Assert.isTrue(secondProductModelMap.containsKey(secondProductModel), "二级产品标签不存在");
            secondModel = secondProductModelMap.get(secondProductModel);
        }
        bsiOpportunityService.updateOpsModel(BsiOpportunityDto.builder()
                .id(opsId)
                .firstProductModelId(modelLabel.getProductModelId())
                .firstProductModelLabel(modelLabel.getProductModelLabel())
                .secondProductModelId(secondModel.getProductModelId())
                .secondProductModelLabel(secondModel.getProductModelLabel())
                .build());
    }
}
