package com.bilibili.crm.platform.biz.service.wallet.trade.coupon;

import com.bilibili.crm.platform.api.wallet.dto.AccountWalletTradeDto;
import com.bilibili.crm.platform.biz.po.AccAccountWalletPo;
import com.bilibili.crm.platform.biz.service.wallet.trade.AbstractAccountWalletTradeService;
import com.bilibili.crm.platform.biz.service.wallet.trade.AccountWalletTradeHelper;
import com.bilibili.crm.platform.common.wallet.WalletTradeAction;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/4/20
 **/
@Service
public class CouponUnLockTradeService extends AbstractAccountWalletTradeService {

    @Autowired
    private AccountWalletTradeHelper accountWalletTradeHelper;

    @Override
    protected WalletTradeAction tradeRoute() {
        return WalletTradeAction.COUPON_UNLOCK;
    }

    @Override
    protected void validateParams(AccountWalletTradeDto tradeDto) {

    }

    @Override
    protected AccAccountWalletPo buildWalletChange(AccAccountWalletPo originWalletPo, AccountWalletTradeDto tradeDto) {
        AccAccountWalletPo record = new AccAccountWalletPo();
        record.setAccountId(tradeDto.getAccountId());
        record.setCash(tradeDto.getCash() + originWalletPo.getCash()); // 总现金
        record.setRedPacket(tradeDto.getRedPacket() + originWalletPo.getRedPacket());
        record.setSpecialRedPacket(tradeDto.getSpecialRedPacket() + originWalletPo.getSpecialRedPacket());
        record.setTotalCashRecharge(tradeDto.getCash() + originWalletPo.getTotalCashRecharge()); // 总的现金充值
        record.setTotalRedPacketRecharge(tradeDto.getRedPacket() + originWalletPo.getTotalRedPacketRecharge());
        record.setTotalSpecialRedPacketRecharge(tradeDto.getSpecialRedPacket() + originWalletPo.getTotalSpecialRedPacketRecharge());
        record.setVersion(originWalletPo.getVersion() + 1);
        return record;
    }
}
