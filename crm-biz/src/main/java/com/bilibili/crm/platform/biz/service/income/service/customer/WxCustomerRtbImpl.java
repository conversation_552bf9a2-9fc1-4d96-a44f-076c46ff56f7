package com.bilibili.crm.platform.biz.service.income.service.customer;

import com.bilibili.crm.platform.api.achievement.dto.AchievementRtbData;
import com.bilibili.crm.platform.api.achievement.dto.QueryAchieveDto;
import com.bilibili.crm.platform.api.income.bo.customer.CategoryCustomerStatBo;
import com.bilibili.crm.platform.api.income.dto.*;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.api.income.service.IIncomeQueryService;
import com.bilibili.crm.platform.api.income.service.IWxCustomerService;
import com.bilibili.crm.platform.biz.service.income.config.customer.CustomerIncomeConfig;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeEnhanceHelper;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeServiceParamHelper;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeServiceQueryHelper;
import com.bilibili.crm.platform.biz.service.income.service.IncomeQueryService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class WxCustomerRtbImpl implements IWxCustomerService {
    @Autowired
    private CustomerIncomeConfig customerIncomeConfig;
    @Autowired
    private IncomeServiceParamHelper incomeServiceParamHelper;
    @Autowired
    private IncomeServiceQueryHelper incomeServiceQueryHelper;
    @Autowired
    private CustomerIncomeServiceHelper customerServiceHelper;
    @Autowired
    private IncomeEnhanceHelper incomeEnhanceHelper;
    @Autowired
    private IncomeQueryService incomeQueryService;


    @Override
    public TeamType team() {
        return TeamType.RTB;
    }

//    /**
//     * 活跃新客-效果 ，满足近30日效果产品（不含ADX）日均消耗大于500元的外部客户，计为一个活跃客户
//     */
//    @Override
//    public List<CategoryCustomerStatBo> queryCategoryCustomerStat(IncomeQueryParam param) {
//        //近30日,放在前面
//        customerServiceHelper.buildQueryThirtyDayParam(param);
//
//        List<RtbAdIncome> adIncomes = getRtbInCome(param);
//
//        List<CustomerAdIncome> customerAdIncomes = incomeEnhanceHelper.rtb2CustomerAdIncome(adIncomes);
//
//        //[-180,30] 的历史客户
//        List<Integer> historyCustomer = queryHistoryCustomer(param);
//
//        List<CategoryCustomerStatBo> statList = customerServiceHelper.buildCategoryCustomerStatByConsume(customerAdIncomes, historyCustomer);
//
//        return statList;
//    }


//    private List<Integer> queryHistoryCustomer(IncomeQueryParam param) {
//
//        List<RtbAdIncome> adIncomes = getRtbInCome(customerServiceHelper.buildQueryHistoryParam(param));
//
//        List<CustomerAdIncome> customerAdIncomes = incomeEnhanceHelper.rtb2CustomerAdIncome(adIncomes);
//        //补充客户信息
//        //customerServiceHelper.patchCustomer(customerAdIncomes, customerServiceHelper::enhanceRtbAccount);
//        //补充公司组
//        customerServiceHelper.patchCompanyGroupId(customerAdIncomes);
//
//        return customerAdIncomes.stream().map(CustomerAdIncome::getCompanyGroupId).distinct().collect(Collectors.toList());
//    }

//    /**
//     * 行业收入
//     *
//     * @param param
//     * @return
//     */
//    @Override
//    public List<ClassifyIncome> queryCategoryIncomeList(IncomeQueryParam param) {
//
//        List<RtbAdIncome> adIncomes = getRtbInCome(param);
//
//        List<CustomerAdIncome> customerAdIncomes = incomeEnhanceHelper.rtb2CustomerAdIncome(adIncomes);
//        customerServiceHelper.patchCategory(customerAdIncomes, incomeEnhanceHelper::enhanceRtbAccount);
//
//        List<ClassifyIncome> classifyIncomes = groupRtbAdIncome(customerAdIncomes, CustomerAdIncome::getCategoryFirstId);
//
//        return classifyIncomes;
//    }

    /**
     * 行业收入
     *
     * @param param
     * @return
     */
    @Override
    public List<ClassifyIncome> queryRiskCategoryIncomeList(IncomeQueryParam param) {

        List<RtbAdIncome> adIncomes = getRtbInCome(param);

        List<CustomerAdIncome> customerAdIncomes = incomeEnhanceHelper.rtb2CustomerAdIncome(adIncomes);
        customerServiceHelper.patchRiskCategory(customerAdIncomes, incomeEnhanceHelper::enhanceRtbAccount);

        List<ClassifyIncome> classifyIncomes = groupRtbAdIncome(customerAdIncomes, CustomerAdIncome::getCategoryFirstId);

        return classifyIncomes;
    }


//    /**
//     * 客户收入 按公司组维度聚合
//     *
//     * @param param
//     * @return
//     */
//    @Override
//    public List<ClassifyIncome> queryCustomerIncomeList(IncomeQueryParam param) {
//
//        List<RtbAdIncome> adIncomes = getRtbInCome(param);
//
//        List<CustomerAdIncome> customerAdIncomes = incomeEnhanceHelper.rtb2CustomerAdIncome(adIncomes);
//
//        //account->公司组
////        customerServiceHelper.patchCompanyGroupId(customerAdIncomes);
////        List<ClassifyIncome> classifyIncomes = groupRtbAdIncome(customerAdIncomes, CustomerAdIncome::getCompanyGroupId);
//
//        //account->品牌
//        customerServiceHelper.patchProductId(customerAdIncomes);
//        List<ClassifyIncome> classifyIncomes = groupRtbAdIncome(customerAdIncomes, CustomerAdIncome::getProductId);
//
//        return classifyIncomes;
//
//    }


    /**
     * 效果分组成部分收入
     */
//    private List<RtbAdIncome> getRtbInCome(IncomeQueryParam param) {
//        //composition ,cpc,cpm, dpa
//        List<CustomerIncomeComposition> compositions = customerIncomeConfig.getRtbIncomeComposition();
//        List<RtbAdIncome> rtbAdIncomes = new ArrayList<>();
//        compositions.forEach(composition -> {
//            List<RtbAdIncome> adIncomes = getRtbInCome(composition, param);
//            rtbAdIncomes.addAll(adIncomes);
//        });
//        return rtbAdIncomes;
//
//    }
    private List<RtbAdIncome> getRtbInCome(IncomeQueryParam param) {
        List<RtbAdIncome> rtbAdIncomes = new ArrayList<>();
        for (IncomeComposition incomeComposition : IncomeQueryService.CONVERSION_TYPE) {
            List<AchievementRtbData> achievementRtbData = incomeQueryService.queryFinicalIncome(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
                    .dateBegin(param.getDateBegin())
                    .dateEnd(param.getDateEnd())
                    .customerIds(null == param.getCustomerId() ? null : Lists.newArrayList(param.getCustomerId()))
                    .groupIds(null == param.getCompanyGroupId() ? null : Lists.newArrayList(param.getCompanyGroupId()))
                    .build(), incomeComposition);
            List<RtbAdIncome> data = achievementRtbData.stream().map(r -> {
                RtbAdIncome rtbAdIncome = new RtbAdIncome();
                rtbAdIncome.setAccountId(r.getAccountId());
                rtbAdIncome.setAgentId(r.getAgentId());
                rtbAdIncome.setCustomerId(r.getCustomerId());
                rtbAdIncome.setCategoryFirstId(r.getCategoryFirstId());
                rtbAdIncome.setAmount(r.getTotalConsume());
                return rtbAdIncome;
            }).collect(Collectors.toList());
            rtbAdIncomes.addAll(data);
        }
        return rtbAdIncomes;
    }

//    private List<RtbAdIncome> getRtbInCome(CustomerIncomeComposition composition, IncomeQueryParam param) {
//        IncomeCompositionQueryParam incomeParam = incomeServiceParamHelper.buildIncomeCompositionQueryParam(param);
//        switch (composition) {
//            case RTB_CPC:
//                //因为口径跟产品保持一致，这里复用产品的config,后续可统一
//                return incomeServiceQueryHelper.getConsumeAdIncome(incomeParam, () -> {
//                    try {
//                        return customerIncomeConfig.rtbCpcCondition(param);
//                    } catch (IncomeConditionInvalidException e) {
//                        return null;
//                    }
//                });
//            case RTB_CPM:
//                return incomeServiceQueryHelper.getConsumeAdIncome(incomeParam, () -> {
//                    try {
//                        return customerIncomeConfig.rtbCpmCondition(param);
//                    } catch (IncomeConditionInvalidException e) {
//                        return null;
//                    }
//                });
//            case RTB_DPA:
//                return incomeServiceQueryHelper.getConsumeAdIncome(incomeParam, () -> {
//                    try {
//                        return customerIncomeConfig.rtbDpaCondition(param);
//                    } catch (IncomeConditionInvalidException e) {
//                        return null;
//                    }
//                });
//
//        }
//        return Collections.emptyList();
//    }

    private List<ClassifyIncome> groupRtbAdIncome(List<CustomerAdIncome> adIncomes, Function<CustomerAdIncome, Integer> classifier) {
        if (CollectionUtils.isEmpty(adIncomes)) {
            return Collections.emptyList();
        }

        Map<Integer, List<CustomerAdIncome>> adIncomeMap = adIncomes.stream().collect(Collectors.groupingBy(classifier));

        List<ClassifyIncome> result = adIncomeMap.entrySet().stream().map(entry -> {
            List<CustomerAdIncome> incomeList = entry.getValue();
            BigDecimal income = incomeList.stream().map(CustomerAdIncome::getAmount).reduce(BigDecimal.ZERO, BigDecimal::add);
            return ClassifyIncome.builder()
                    .id(entry.getKey())
                    .amount(income)
                    .build();
        }).collect(Collectors.toList());
        return result;
    }

}
