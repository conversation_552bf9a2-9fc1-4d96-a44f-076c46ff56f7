package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.brand.api.common.exception.StatExceptionCode;
import com.bilibili.brand.platform.report.api.dto.StatActiveDto;
import com.bilibili.brand.platform.report.api.dto.StatQueryBean;
import com.bilibili.cpt.platform.common.GROUP_TYPE;
import com.bilibili.crm.platform.biz.po.statistic.StatPo;
import com.bilibili.crm.platform.biz.po.statistic.StatPojo;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.google.common.collect.Sets;
import lombok.AllArgsConstructor;
import lombok.Getter;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 16/9/26.
 */
public class StatBaseService {
    protected final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    public void validateParam(Integer paramId, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        if (null == paramId || null == fromTime || null == toTime) {
            LOGGER.info("param can not be null");
            throw new ServiceException(StatExceptionCode.REQUIRED_PARAM);
        }

        LOGGER.info("{} param id{} fromtime{} totime {}", this.getClass().getName(), paramId, fromTime, toTime);
    }

    public void validateParam(Timestamp fromTime, Timestamp toTime, Integer salesType) throws ServiceException {
        if (null == fromTime || null == toTime || null == salesType) {
            LOGGER.info("param can not be null");
            throw new ServiceException(StatExceptionCode.REQUIRED_PARAM);
        }

        LOGGER.info("{} param fromtime {}, totime {}, salesType {}", this.getClass().getName(), fromTime, toTime, salesType);
    }
    
    public void validateParam(Timestamp fromTime, Timestamp toTime, List<Integer> salesTypes) throws ServiceException {
        if (null == fromTime || null == toTime || CollectionUtils.isEmpty(salesTypes)) {
            LOGGER.info("param can not be null");
            throw new ServiceException(StatExceptionCode.REQUIRED_PARAM);
        }
        
        salesTypes.stream().forEach(SalesType::getByCode);

        LOGGER.info("{} param fromtime{} totime {} salesTypes {}", this.getClass().getName(), fromTime, toTime, salesTypes);
    }

    public void validateParam(List<Integer> paramIds, Timestamp fromTime, Timestamp toTime) throws ServiceException {
        if (CollectionUtils.isEmpty(paramIds) /*|| null == fromTime || null == toTime*/) {
            LOGGER.info("param can not be null");
            throw new ServiceException(StatExceptionCode.REQUIRED_PARAM);
        }

        LOGGER.info("{} param ids{} fromtime{} totime {}", this.getClass().getName(), Arrays.toString(paramIds.toArray()), fromTime, toTime);
    }


    private final static BigDecimal HUNDRED = new BigDecimal(100);
    private final static BigDecimal THOUSAND = new BigDecimal(1000);

    public static String getDate(Long timeMiles) {
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        return sdf.format(timeMiles);
    }

    public BigDecimal getCost(Long cost) {
        if (null == cost) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(cost).divide(HUNDRED);
    }

    public BigDecimal getClickRate(Integer clickCount, Integer showCount) {
        if (null == showCount || 0 == showCount) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(clickCount).divide(new BigDecimal(showCount), 4, BigDecimal.ROUND_HALF_UP).multiply(HUNDRED);
    }

    public BigDecimal getCostPerClick(Long cost, Integer clickCount) {
        if (null == clickCount || 0 == clickCount) {
            return BigDecimal.ZERO;
        }
        return this.getCost(cost).divide(new BigDecimal(clickCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getAverageCostPerThousand(Long cost, Integer showCount) {
        if (null == showCount || 0 == showCount) {
            return BigDecimal.ZERO;
        }
        return this.getCost(cost).multiply(THOUSAND).divide(new BigDecimal(showCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    protected boolean isActual(Timestamp timestamp) {
        return timestamp.getTime() > Utils.getToday().getTime();
    }

    public List<StatPojo> groupBy(List<StatPo> statPos, GROUP_TYPE groupType, GROUP_KEY groupKey, Timestamp fromTime, Timestamp toTime) {
        if (CollectionUtils.isEmpty(statPos) || null == groupType || null == groupKey || null == fromTime || null == toTime) {
            return Collections.emptyList();
        }
        Map<Integer, List<StatPo>> statMap = new HashMap<>();
        for (StatPo po : statPos) {
            List<StatPo> tempList = null;
            if (statMap.containsKey(groupKey.getGroupByKey(po))) {
                tempList = statMap.get(groupKey.getGroupByKey(po));
            } else {
                tempList = new ArrayList<>();
            }
            tempList.add(po);
            statMap.put(groupKey.getGroupByKey(po), tempList);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        List<StatPojo> result = new ArrayList<>();
        for (Map.Entry<Integer, List<StatPo>> entry : statMap.entrySet()) {
            Map<String, StatPojo> map = new HashMap<>();
            for (StatPo po : entry.getValue()) {
                String key = groupType.generateTimeKey(calendar, po.getTimestamp().getTime(), fromTime, toTime);
                if (!map.containsKey(key)) {
                    map.put(key, StatPojo.builder()
                            .accountId(po.getAccountId())
                            .orderId(po.getOrderId())
                            .campaignId(po.getCampaignId())
                            .unitId(po.getUnitId())
                            .creativeId(po.getCreativeId())
                            .date(key)
                            .clickCount(po.getClickCount())
                            .clickRate(this.getClickRate(po.getClickCount(), po.getShowAccount()))
                            .costPerClick(this.getCostPerClick(po.getCost(), po.getClickCount()))
                            .showAccount(po.getShowAccount())
                            .cost(this.getCost(po.getCost()))
                            .averageCostPerThousand(this.getAverageCostPerThousand(po.getCost(), po.getShowAccount()))
                            .build());
                } else {
                    StatPojo pojo = map.get(key);
                    pojo.setClickCount(pojo.getClickCount() + po.getClickCount());
                    pojo.setCost(pojo.getCost().add(this.getCost(po.getCost())));
                    pojo.setShowAccount(pojo.getShowAccount() + po.getShowAccount());
                    pojo.setClickRate(this.getClickRate(pojo.getClickCount(), pojo.getShowAccount()));
                    pojo.setAverageCostPerThousand(this.getAverageCostPerThousand(Utils.fromYuanToFen(pojo.getCost()), pojo.getShowAccount()));
                    pojo.setCostPerClick(this.getCostPerClick(Utils.fromYuanToFen(pojo.getCost()), pojo.getClickCount()));
                    map.put(key, pojo);
                }
            }
            result.addAll(map.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
            map.clear();
        }
        return result;
    }


    public List<StatPojo> getActiveGroupBy(List<StatPo> statPos, GROUP_TYPE groupType, GROUP_KEY groupKey,
                                           COUNT_KEY countKey,
                                           Timestamp fromTime, Timestamp toTime) {
        if (CollectionUtils.isEmpty(statPos) || null == groupType || null == groupKey || countKey == null || null == fromTime || null == toTime) {
            return Collections.emptyList();
        }
        Map<Integer, List<StatPo>> statMap = new HashMap<>();
        for (StatPo po : statPos) {
            List<StatPo> tempList = null;
            if (statMap.containsKey(groupKey.getGroupByKey(po))) {
                tempList = statMap.get(groupKey.getGroupByKey(po));
            } else {
                tempList = new ArrayList<>();
            }
            tempList.add(po);
            statMap.put(groupKey.getGroupByKey(po), tempList);
        }

        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
        List<StatPojo> result = new ArrayList<>();
        for (Map.Entry<Integer, List<StatPo>> entry : statMap.entrySet()) {
            Map<String, StatPojo> map = new HashMap<>();
            Map<String, Set<Integer>> countKeyMap = Maps.newHashMap();
            for (StatPo po : entry.getValue()) {
                String key = groupType.generateTimeKey(calendar, po.getTimestamp().getTime(), fromTime, toTime);
                StatPojo statPojo = StatPojo.builder()
                        .accountId(po.getAccountId())
                        .campaignId(po.getCampaignId())
                        .unitId(po.getUnitId())
                        .creativeId(po.getCreativeId())
                        .orderId(po.getOrderId())
                        .salesType(po.getSalesType())
                        .activeCount(1)
                        .date(key)
                        .build();
                String uniqueKey = key + "_" + po.getSalesType();
                if (!map.containsKey(uniqueKey)) {
                    map.put(uniqueKey, statPojo);
                    countKeyMap.put(uniqueKey, Sets.newHashSet(countKey.getCountByKey(statPojo)));
                } else {
                    StatPojo pojo = map.get(uniqueKey);
                    Set<Integer> countKeySet = countKeyMap.get(uniqueKey);

                    if (!countKeySet.contains(countKey.getCountByKey(statPojo))){
                        pojo.setActiveCount(pojo.getActiveCount() + 1);
                        countKeySet.add(countKey.getCountByKey(statPojo));
                    }
                    map.put(uniqueKey, pojo);
                    countKeyMap.put(uniqueKey, countKeySet);

                }
            }
            result.addAll(map.entrySet().stream().map(Map.Entry::getValue).collect(Collectors.toList()));
            map.clear();
            countKeyMap.clear();

        }
        return result;
    }

    public List<StatActiveDto> convertPojoToActiveDto(List<StatPojo> statPojos) {
        if (CollectionUtils.isEmpty(statPojos)) {
            return Collections.emptyList();
        }
        return statPojos.stream().map(pojo -> StatActiveDto.builder()
                .accountId(pojo.getAccountId())
                .date(pojo.getDate())
                .activeCount(pojo.getActiveCount())
                .salesType(pojo.getSalesType())
                .build()).collect(Collectors.toList());
    }
    
    public List<StatPojo> groupBy(List<StatPo> statPos, GROUP_TYPE groupType, GROUP_KEY groupKey1, GROUP_KEY groupKey2, Timestamp fromTime, Timestamp toTime) {
        Calendar calendar = Calendar.getInstance();
        calendar.setFirstDayOfWeek(Calendar.MONDAY);
       Map<Integer, Map<String, Map<Integer, Optional<StatPo>>>> statPoOptional = statPos.stream().collect(
           Collectors.groupingBy(groupKey1::getGroupByKey, 
               Collectors.groupingBy(po -> groupType.generateTimeKey(calendar, po.getTimestamp().getTime(), fromTime, toTime), 
                      Collectors.groupingBy(groupKey2::getGroupByKey, Collectors.reducing((po1, po2)->this.buildStatPo(po1, po2))))));
        List<StatPo> statPos2 = Lists.newArrayList();
        statPoOptional.values().stream().forEach(v -> {
            v.values().stream().forEach(p -> {
                p.values().stream().forEach(o -> statPos2.add(o.get()));
            });
        });
        
        return statPos2.stream().map(po -> this.buildStatPojo(po, groupType, calendar, fromTime, toTime)).collect(Collectors.toList());
    }
    
    private StatPojo buildStatPojo(StatPo po, GROUP_TYPE groupType, Calendar calendar, Timestamp fromTime, Timestamp toTime){
        String date = groupType.generateTimeKey(calendar, po.getTimestamp().getTime(), fromTime, toTime);
        StatPojo pojo = StatPojo.builder().build();
        BeanUtils.copyProperties(po, pojo);
        pojo.setDate(date);
        pojo.setCost(this.getCost(po.getCost()));
        pojo.setClickRate(this.getClickRate(pojo.getClickCount(), pojo.getShowAccount()));
        pojo.setAverageCostPerThousand(this.getAverageCostPerThousand(Utils.fromYuanToFen(pojo.getCost()), pojo.getShowAccount()));
        pojo.setCostPerClick(this.getCostPerClick(Utils.fromYuanToFen(pojo.getCost()), pojo.getClickCount()));
        return pojo;
    }
    
    private StatPo buildStatPo(StatPo po1, StatPo po2){
        po1.setClickCount(po1.getClickCount() + po2.getClickCount());
        po1.setCost(po1.getCost()+po2.getCost());
        po1.setShowAccount(po1.getShowAccount() + po2.getShowAccount());
        return po1;
    }

    public enum COUNT_KEY {
        ALL {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return 0;
            }
        },
        SALES_TYPE {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return statPojo.getSalesType();
            }
        },
        ACCOUNT_ID {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return statPojo.getAccountId();
            }
        },
        ORDER_ID {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return statPojo.getOrderId();
            }
        },
        CAMPAIGN_ID {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return statPojo.getCampaignId();
            }
        },
        UNIT_ID {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return statPojo.getUnitId();
            }
        },
        CREATIVE_ID {
            @Override
            public Integer getCountByKey(StatPojo statPojo) {
                return statPojo.getCreativeId();
            }
        };

        public abstract Integer getCountByKey(StatPojo statPojo);
    }

    public enum GROUP_KEY {
        ALL {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return 0;
            }
        },
        SALES_TYPE {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return statPojo.getSalesType();
            }
        },
        ACCOUNT_ID {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return statPojo.getAccountId();
            }
        },
        ORDER_ID {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return statPojo.getOrderId();
            }
        },
        CAMPAIGN_ID {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return statPojo.getCampaignId();
            }
        },
        UNIT_ID {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return statPojo.getUnitId();
            }
        },
        CREATIVE_ID {
            @Override
            public Integer getGroupByKey(StatPo statPojo) {
                return statPojo.getCreativeId();
            }
        };

        public abstract Integer getGroupByKey(StatPo statPojo);
    }


    
    @SuppressWarnings("rawtypes")
    @AllArgsConstructor
    @Getter
    public enum OrderBy {
    	SHOW_COUNT("show_count", StatPojo::getShowAccount), 
    	CLICK_COUNT("click_count", StatPojo::getClickCount), 
    	CLICK_RATE("click_rate", StatPojo::getClickRate), 
    	COST_PER_CLICK("cost_per_click", StatPojo::getCostPerClick), 
    	AVERAGE_COST_PER_THOUSAND("average_cost_per_thousand", StatPojo::getAverageCostPerThousand), 
    	COST("cost", StatPojo::getCost);
    	
    	private String name;
		private Function<StatPojo, Comparable> orderByFunction;
		
		public static OrderBy getByName(String name) {
			for(OrderBy sb: values()) {
				if(sb.getName().equals(name)) {
					return sb;
				}
			}
			
			throw new IllegalArgumentException("未知的排序列名");
		}
	}
    
    public List<StatPojo> groupBy(List<StatPo> statPos, GROUP_TYPE groupType, GROUP_KEY groupKey, Timestamp fromTime, Timestamp toTime,
                                  String order, int by) {
    	List<StatPojo> pojos = this.groupBy(statPos, groupType, groupKey, fromTime, toTime);
    	
    	if(!StringUtils.isEmpty(order)) {
    		sortBy(pojos, order, by);            
    	}
    	
    	return pojos;
    }
    
    private void sortBy(List<StatPojo> pojos, String order, int by) {
    	OrderBy sb = OrderBy.getByName(order);
    	boolean isDesc = by == 0 ? false : true;

    	StatPoComparator c = new StatPoComparator(sb.orderByFunction, isDesc);
    	
    	Collections.sort(pojos, c);
    }
    
    @SuppressWarnings("rawtypes")
	protected void setListToCriteria(Supplier<List> f1, Consumer<List> f2) {
    	List list = f1.get();
    	
    	if(!CollectionUtils.isEmpty(list)) {
    		f2.accept(list);
    	}
    }
    
	protected void setObjectToCriteria(Supplier<Object> f1, Consumer<? super Object> f2) {
    	Object o = f1.get();
    	
    	if(o != null) {
    		f2.accept(o);
    	}
    }
    
    protected void statQueryBeanToCriteria(StatQueryBean query, Object c) {
    	Class<?> cc = c.getClass();
    	Method m = null;
    	try {
	    	if(!CollectionUtils.isEmpty(query.getAccountIds())) {
	    		m = cc.getDeclaredMethod("andAccountIdIn", List.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getAccountIds());
	    	}
	    	
	    	if(!CollectionUtils.isEmpty(query.getCampaignIds())) {
	    		m = cc.getDeclaredMethod("andCampaignIdIn", List.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getCampaignIds());
	    	}
	    	
	    	if(!CollectionUtils.isEmpty(query.getOrderIds())) {
	    		m = cc.getDeclaredMethod("andOrderIdIn", List.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getOrderIds());
	    	}
	    	
	    	if(!CollectionUtils.isEmpty(query.getUnitIds())) {
	    		m = cc.getDeclaredMethod("andUnitIdIn", List.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getUnitIds());
	    	}
	    	
	    	if(!CollectionUtils.isEmpty(query.getCreativeIds())) {
	    		m = cc.getDeclaredMethod("andCreativeIdIn", List.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getCreativeIds());
	    	}
	    	
	    	if(!CollectionUtils.isEmpty(query.getSalesTypes())) {
	    		m = cc.getDeclaredMethod("andSalesTypeIn", List.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getSalesTypes());
	    	}
	    	
	    	if(query.getFromTime() != null) {
	    		m = cc.getDeclaredMethod("andTimestampGreaterThanOrEqualTo", Timestamp.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getFromTime());
	    	}
	    	
	    	if(query.getToTime() != null) {
	    		m = cc.getDeclaredMethod("andTimestampLessThanOrEqualTo", Timestamp.class);
	    		m.setAccessible(true);
	    		m.invoke(c, query.getToTime());
	    	}
	    	
	    	m = cc.getDeclaredMethod("andIsDeletedEqualTo", Integer.class);
    		m.setAccessible(true);
    		m.invoke(c, IsDeleted.VALID.getCode());
    	}
    	catch(NoSuchMethodException | IllegalAccessException | InvocationTargetException e) {
    		LOGGER.error("statQueryBeanToCriteria: " + e.getMessage());
    	}
    }
    
    @SuppressWarnings("rawtypes")
	protected void setList(Supplier<List> s, Consumer<List> c) {
    	List list = s.get();
    	
    	if(!CollectionUtils.isEmpty(list)) {
    		c.accept(list);
    	}
    }
    
    protected void setTimestamp(Supplier<Timestamp> s, Consumer<Timestamp> c) {
    	Timestamp v = s.get();
    	
    	if(v != null) {
    		c.accept(v);
    	}
    }
}

@SuppressWarnings("rawtypes")
@AllArgsConstructor
class StatPoComparator implements Comparator<StatPojo> {

	private Function<StatPojo, Comparable> sortByFunction;
	private boolean isDesc;
	
	@SuppressWarnings("unchecked")
	@Override
	public int compare(StatPojo o1, StatPojo o2) {
		Comparable a = sortByFunction.apply(o1);
		Comparable b = sortByFunction.apply(o2);
		int result = a.compareTo(b);
		
		return isDesc ? 0 - result : result;
	}
}