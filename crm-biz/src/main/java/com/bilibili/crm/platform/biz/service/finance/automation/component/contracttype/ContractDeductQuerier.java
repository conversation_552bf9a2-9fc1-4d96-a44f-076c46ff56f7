package com.bilibili.crm.platform.biz.service.finance.automation.component.contracttype;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.util.sensitive.SensitiveHandler;
import com.bilibili.crm.platform.api.achievement.dto.SimpleCrmInterlayerMoneyPo;
import com.bilibili.crm.platform.api.contract.dto.*;
import com.bilibili.crm.platform.api.contract.service.ContractBillPeriodService;
import com.bilibili.crm.platform.api.contract.service.IContractFinanceRebateRecordService;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.enums.contract.ContractBillRuleLevel;
import com.bilibili.crm.platform.api.executive_order.dto.ExecutiveOrderBasicDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CanDeductContractDto;
import com.bilibili.crm.platform.api.finance.dto.automation.RevenueExpenditureContractDeductDto;
import com.bilibili.crm.platform.api.finance.enums.*;
import com.bilibili.crm.platform.api.sale.dto.SaleBaseDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractAuditStatus;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.common.OaFlowTypeEnum;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.*;
import com.bilibili.crm.platform.biz.repo.contract.CrmContractArchiveRecordRepo;
import com.bilibili.crm.platform.biz.repo.executive_order.CrmContractExecutiveOrderRepo;
import com.bilibili.crm.platform.biz.repo.finance.*;
import com.bilibili.crm.platform.biz.service.ContractService;
import com.bilibili.crm.platform.biz.service.contract.ContractQueryServiceHelper;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.DecimalFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 合同抵扣信息查询器
 *
 * <AUTHOR>
 * @date 2021/3/1 7:20 下午
 */
@Slf4j
@Component
public class ContractDeductQuerier {

    @Autowired
    private CrmRevenueExpenditureFlowRepo crmRevenueExpenditureFlowRepo;
    @Autowired
    private CrmFlowAdditionInfoRepo crmFlowAdditionInfoRepo;
    @Autowired
    private CrmContractSaleMappingRepo crmContractSaleMappingRepo;
    @Autowired
    private CrmContractRepo crmContractRepo;
    @Autowired
    private AccAccountRepo accAccountRepo;
    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private CrmAgentRepo crmAgentRepo;
    @Autowired
    private CrmInterlayerMoneyRepo crmInterlayerMoneyRepo;
    @Autowired
    private CrmOaFlowRepo crmOaFlowRepo;
    @Autowired
    private CrmInvoiceOaFlowRepo crmInvoiceOaFlowRepo;
    @Autowired
    private CrmContractOaFlowMappingRepo crmContractOaFlowMappingRepo;
    @Autowired
    private CrmSaleRepo saleRepo;
    @Autowired
    private CrmContractOaFlowRepo crmContractOaFlowRepo;
    @Autowired
    private CrmFlowContractDeductRelationRepo crmFlowContractDeductRelationRepo;
    @Autowired
    private CrmRebateCheckRecordRepo crmRebateCheckRecordRepo;
    @Autowired
    private CrmContractArchiveRecordRepo crmContractArchiveRecordRepo;
    @Autowired
    private ContractQueryServiceHelper contractQueryServiceHelper;
    @Autowired
    private ContractService contractService;
    @Autowired
    private IContractFinanceRebateRecordService contractFinanceRebateRecordService;

    @Resource
    private CrmContractExecutiveOrderRepo crmContractExecutiveOrderRepo;
    @Autowired
    private SaleContractSubstituteRepo saleContractSubstituteRepo;

    @Resource
    private ContractBillPeriodService contractBillPeriodService;

    @Resource
    private IContractQueryService contractQueryService;


    /**
     * 将抵扣关系 po 列表转为抵扣 dto
     *
     * @param deductRelationPos
     * @return
     */
    public List<RevenueExpenditureContractDeductDto> convertDeductRelatationPos2ContractDeductDtos(List<CrmFlowContractDeductRelationPo> deductRelationPos) {
        List<Integer> flowIds =
                deductRelationPos.stream().filter(t -> (ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(t.getType())
                                || ContractDeductType.REVERSED_DEDUCT.getCode().equals(t.getType())))
                        .map(t -> t.getFlowId()).distinct().collect(Collectors.toList());
        List<Integer> contractIds = deductRelationPos.stream().map(t -> t.getContractId()).distinct().collect(Collectors.toList());
        List<CrmRevenueExpenditureFlowPo> flowPos = crmRevenueExpenditureFlowRepo.queryListByIds(flowIds);
        Map<Integer, CrmRevenueExpenditureFlowPo> flowPoMap = flowPos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));
        Map<Integer, CrmFlowAdditionInfoPo> flowAdditionInfoPoMap = crmFlowAdditionInfoRepo.queryMapByFlowIds(flowIds);

        // 获取关账合同的账单
        Map<Integer, List<SimpleCrmInterlayerMoneyPo>> interlayMoneyMap = crmInterlayerMoneyRepo.queryInterlayerMoneySimpleMapByContractIds(contractIds);
        List<ContractDto> contractDtos = contractService.queryContractsInIds(contractIds);
        Map<Integer, ContractDto> contractDtoMap = contractDtos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));

        List<CrmContractSaleMappingPo> crmContractSaleMappingPos = crmContractSaleMappingRepo.queryListByContractIds(contractIds);
        Map<Integer, List<SaleDto>> substituteSaleMap = saleContractSubstituteRepo.queryByContract(contractIds);
        List<Integer> salesIds = crmContractSaleMappingPos.stream().map(t -> t.getCrmSaleId()).distinct().collect(Collectors.toList());
        Map<Integer, CrmSalePo> salePoMap = saleRepo.getSalesMapByIds(salesIds);
        Map<Integer, List<CrmContractSaleMappingPo>> contractSalesMappingMap = crmContractSaleMappingPos.stream().collect(Collectors.groupingBy(t -> t.getCrmContractId()));

        // 流水的客户
        List<Integer> bindCustomerIdsOfFlows = flowPos.stream().map(t -> t.getBindCustomerId()).distinct().collect(Collectors.toList());

        // 广告主账号与客户
        List<Integer> allAccountIds = new ArrayList<>();
        List<Integer> advertiseAccountIds = contractDtos.stream().map(t -> t.getAccountId()).distinct().collect(Collectors.toList());
        allAccountIds.addAll(advertiseAccountIds);

        // 代理商账号与客户
        List<Integer> agentIds = contractDtos.stream().filter(t -> Utils.isPositive(t.getAgentId())).map(t -> t.getAgentId()).distinct().collect(Collectors.toList());
        List<CrmAgentPo> crmAgentPos = crmAgentRepo.queryAgentListByAgentIds(agentIds);
        List<Integer> accountIdsOfAgent = crmAgentPos.stream().map(t -> t.getSysAgentId()).distinct().collect(Collectors.toList());
        Map<Integer, Integer> agentIdsMap = crmAgentPos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t.getSysAgentId()));
        allAccountIds.addAll(accountIdsOfAgent);

        List<AccAccountPo> accAccountPos = accAccountRepo.queryAccountListByIds(allAccountIds);
        Map<Integer, AccAccountPo> accountPoMap = accAccountPos.stream().collect(Collectors.toMap(t -> t.getAccountId(), t -> t));
        List<Integer> advertiseCustomerIds = accAccountPos.stream().map(t -> t.getCustomerId()).distinct().collect(Collectors.toList());
        advertiseCustomerIds.addAll(bindCustomerIdsOfFlows);
        Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(advertiseCustomerIds);

        // 获取合同的 oa 信息
        List<CrmContractOaFlowMappingPo> crmContractOaFlowMappingPos = crmContractOaFlowMappingRepo.queryCrmContractOaFlowListByContractIds(contractIds);
        Map<Integer, List<CrmContractOaFlowMappingPo>> contractOaFlowMappingPoMap = crmContractOaFlowMappingPos.stream().collect(Collectors.groupingBy(CrmContractOaFlowMappingPo::getContractId));
        List<Integer> oaFlowIds = crmContractOaFlowMappingPos.stream().map(t -> t.getOaFlowId()).distinct().collect(Collectors.toList());
        Map<Integer, CrmOaFlowPo> crmOaFlowPoMap = crmOaFlowRepo.queryOaFlowMapByOaFlowIds(oaFlowIds);

        // 返点核算相关
        // 根据返点核算 ids 获取返点核算列表
        List<Integer> rebateCheckIds =
                deductRelationPos.stream().filter(t -> ContractDeductType.REBATE_CHECK_DEDUCT.getCode().equals(t.getType())).map(t -> t.getFlowId()).distinct().collect(Collectors.toList());
        Map<Integer, CrmRebateCheckRecordPo> rebateCheckRecordPoMap = crmRebateCheckRecordRepo.queryMapByIds(rebateCheckIds);
        List<CrmRebateCheckRecordPo> rebateCheckRecordPos = rebateCheckRecordPoMap.values().stream().collect(Collectors.toList());
        List<Integer> invoiceIdsOfRebateCheck = rebateCheckRecordPos.stream().map(t -> t.getCrmInvoiceOaFlowId()).distinct().collect(Collectors.toList());
        List<Integer> oaFlowIdsOfRebateCheck = rebateCheckRecordPos.stream().map(t -> t.getCrmOaFlowId()).distinct().collect(Collectors.toList());
        Map<Integer, CrmOaFlowPo> crmOaFlowPoMapOfRebate = crmOaFlowRepo.queryOaFlowMapByOaFlowIds(oaFlowIdsOfRebateCheck);
        Map<Integer, CrmInvoiceOaFlowPo> invoiceOaFlowPoMap = crmInvoiceOaFlowRepo.queryCrmInvoiceOaFlowMapByIds(invoiceIdsOfRebateCheck);
        // 最新现金汇款流水时间 查询
        List<Integer> rebateCheckContractIds = rebateCheckRecordPos.stream().map(CrmRebateCheckRecordPo::getCrmContractId).distinct().collect(Collectors.toList());
        List<Integer> rebateContractIds =
                deductRelationPos.stream().filter(t -> ContractDeductType.REBATE_CHECK_DEDUCT.getCode().equals(t.getType())).map(CrmFlowContractDeductRelationPo::getContractId).distinct().collect(Collectors.toList());
        rebateContractIds.addAll(rebateCheckContractIds);
        Map<Integer, List<Integer>> rebateBankFlowMap = Maps.newHashMap();
        Map<Integer, CrmRevenueExpenditureFlowPo> rebateFlowPoMap = Maps.newHashMap();
        if (CollectionUtils.isNotEmpty(rebateContractIds)) {
            CrmFlowContractDeductRelationPoExample example = new CrmFlowContractDeductRelationPoExample();
            example.createCriteria()
                    .andContractIdIn(rebateContractIds)
                    .andTypeEqualTo(ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<CrmFlowContractDeductRelationPo> relationPos = crmFlowContractDeductRelationRepo.queryByExample(example);
            if (CollectionUtils.isNotEmpty(relationPos)) {
                rebateBankFlowMap = relationPos.stream().collect(Collectors.groupingBy(CrmFlowContractDeductRelationPo::getContractId, Collectors.mapping(CrmFlowContractDeductRelationPo::getFlowId, Collectors.toList())));
                List<CrmRevenueExpenditureFlowPo> rebateFlowPos = crmRevenueExpenditureFlowRepo.queryListByIds(rebateBankFlowMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList()));
                rebateFlowPoMap = rebateFlowPos.stream().collect(Collectors.toMap(CrmRevenueExpenditureFlowPo::getId, Function.identity()));
            }
        }

//        // 帐期相关，这里查询要改下，抵扣不再抵扣到帐期维度了，账期id都为0了，和产品确认后先注释，后续产品看这块有什么迭代
//        List<CrmContractBillPeriodDto> periodDtoList = contractBillPeriodService.queryContractBillList(contractIds);
//        Map<Long, CrmContractBillPeriodDto> periodDtoMap = periodDtoList.stream().collect(Collectors.toMap(CrmContractBillPeriodDto::getBillPeriodId, Function.identity()));

        List<RevenueExpenditureContractDeductDto> deductDtos = new ArrayList<>(deductRelationPos.size());
        for (CrmFlowContractDeductRelationPo deductRelationPo : deductRelationPos) {
            RevenueExpenditureContractDeductDto deductDto = RevenueExpenditureContractDeductDto.builder().build();
            // 抵扣维度信息
            deductDto.setFlowContractDeductRelationId(deductRelationPo.getId());
            deductDto.setOptBatchNo(deductRelationPo.getOptBatchNo());
            deductDto.setDeductTime(deductRelationPo.getCtime());
            deductDto.setOperator(deductRelationPo.getOperator());
            deductDto.setDeductAmount(new BigDecimal(deductRelationPo.getDeductAmount()));
            deductDto.setDeductType(deductRelationPo.getType());
            deductDto.setDeductTypeDesc(ContractDeductType.getByCode(deductRelationPo.getType()).getDesc());
            deductDto.setContractBillPeriodId(deductRelationPo.getContractBillPeriodId());
            deductDto.setDeductSourceId(deductRelationPo.getDeductSourceId());

            if (Objects.equals(deductRelationPo.getDeductSourceId(), 0)) { // 当有值是说明是回退操作的记录，没有值是原始的抵扣明细
                deductDto.setNotReversedDeductAmount(new BigDecimal(deductRelationPo.getDeductAmount() - deductRelationPo.getAlreadyReversedDeductAmount()));
                deductDto.setAlreadyReversedDeductAmount(new BigDecimal(deductRelationPo.getAlreadyReversedDeductAmount()));
            }

            // 银企直连抵扣
            if (ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(deductRelationPo.getType()) || ContractDeductType.REVERSED_DEDUCT.getCode().equals(deductRelationPo.getType())) {
                CrmRevenueExpenditureFlowPo flowPo = flowPoMap.getOrDefault(deductRelationPo.getFlowId(), CrmRevenueExpenditureFlowPo.builder().build());
                CrmFlowAdditionInfoPo additionInfoPo = flowAdditionInfoPoMap.getOrDefault(flowPo.getId(), CrmFlowAdditionInfoPo.builder().build());

                // 流水维度信息
                deductDto.setFlowId(flowPo.getId());
                deductDto.setFlowNo(flowPo.getFlowNo());
                deductDto.setType(flowPo.getType());
                deductDto.setTypeDesc(ReceiveMoneyFlowTypeEnum.getByCode(flowPo.getType()).getDesc());
                deductDto.setFlowReceiveTime(flowPo.getPayTime());
                deductDto.setPayAccountNo(flowPo.getPayAccountNo());
                deductDto.setPayAccountName(flowPo.getPayAccountName());
                deductDto.setReceiveAccountNo(flowPo.getReceiveAccountNo());
                deductDto.setReceiveAccountName(flowPo.getReceiveAccountName());
                deductDto.setReceiveSubAccountNo(flowPo.getReceiveSubAccountNo());
                deductDto.setReceiveSubAccountName(flowPo.getReceiveSubAccountName());
                deductDto.setBank(flowPo.getBank());
                deductDto.setFlowBizStatus(flowPo.getBizStatus());
                deductDto.setFlowBizStatusDesc(FlowBizStatusEnum.getByCode(flowPo.getBizStatus()).getDesc());
                deductDto.setIsProxyPay(flowPo.getIsProxyPay());
                deductDto.setIsProxyPayDesc(IsProxyPayEnum.getByCode(flowPo.getIsProxyPay()).getDesc());
                deductDto.setFlowAmount(new BigDecimal(Objects.isNull(additionInfoPo.getAmount()) ? 0 : additionInfoPo.getAmount()));
                deductDto.setFlowRefundAmount(new BigDecimal(Objects.isNull(additionInfoPo.getRefundAmount()) ? 0 : additionInfoPo.getRefundAmount()));
                deductDto.setFlowRemark(flowPo.getRemark());
                // 流水维度账号客户
                deductDto.setBindCustomerId(flowPo.getBindCustomerId());
                CustomerPo flowCustomerPo = customerPoMap.getOrDefault(flowPo.getBindCustomerId(), CustomerPo.builder().build());
                deductDto.setBindCustomerName(flowCustomerPo.getUsername());
            } else {
                // 核算 id
                deductDto.setRebateCheckId(deductRelationPo.getFlowId());
                CrmRebateCheckRecordPo crmRebateCheckRecordPo = rebateCheckRecordPoMap.get(deductRelationPo.getFlowId());
                Integer rebateContractId = null;
                if (crmRebateCheckRecordPo != null) {
                    rebateContractId = crmRebateCheckRecordPo.getCrmContractId();
                    deductDto.setBillId(crmRebateCheckRecordPo.getCrmInvoiceOaFlowId());
                    CrmInvoiceOaFlowPo crmInvoiceOaFlowPo = invoiceOaFlowPoMap.get(crmRebateCheckRecordPo.getCrmInvoiceOaFlowId());
                    CrmOaFlowPo crmOaFlowPo = crmOaFlowPoMapOfRebate.get(crmRebateCheckRecordPo.getCrmOaFlowId());

                    deductDto.setRebateCheckContractNumber(crmRebateCheckRecordPo.getContractNumber());
                    // 核算金额
                    deductDto.setRebateCheckAmount(new BigDecimal(crmRebateCheckRecordPo.getCheckAmount()));
                    deductDto.setRebateCheckType(crmRebateCheckRecordPo.getType());
                    deductDto.setRebateCheckTypeDesc(RebateCheckType.getByCode(crmRebateCheckRecordPo.getType()).getDesc());
                    deductDto.setRebateCheckStatus(crmRebateCheckRecordPo.getRebateCheckStatus());
                    deductDto.setRebateCheckStatusDesc(RebateCheckStatusEnum.getByCode(crmRebateCheckRecordPo.getRebateCheckStatus()).getDesc());

                    if (crmInvoiceOaFlowPo != null) {
                        deductDto.setBillingDate(crmInvoiceOaFlowPo.getCtime());
                        deductDto.setBillingCompleteDate(crmInvoiceOaFlowPo.getCompleteTime());
                        deductDto.setFlowReceiveTime(crmInvoiceOaFlowPo.getCompleteTime());
                        deductDto.setInvoiceSource(crmInvoiceOaFlowPo.getInvoiceSource());
                        deductDto.setInvoiceSourceDesc(SystemType.getByCode(crmInvoiceOaFlowPo.getInvoiceSource()).getDescCN());
                        deductDto.setTotalPriceTax(new BigDecimal(crmInvoiceOaFlowPo.getTotalPriceTax()));
                        deductDto.setDiscountAmount(new BigDecimal(crmInvoiceOaFlowPo.getDiscountAmount()));
                        deductDto.setRebateCheckBillAmount(new BigDecimal(crmInvoiceOaFlowPo.getAmount()));
                        // 账号与客户
                        deductDto.setAccountId(crmInvoiceOaFlowPo.getAccountId());
                        deductDto.setAccountName(crmInvoiceOaFlowPo.getAccountName());
                        deductDto.setCustomerId(crmInvoiceOaFlowPo.getCustomerId());
                        deductDto.setCustomerName(crmInvoiceOaFlowPo.getCustomerName());
                    }
                    if (crmOaFlowPo != null) {
                        deductDto.setOaBillFlowNo(crmOaFlowPo.getOaFlowNo());
                    }
                }
                List<Integer> rebateTimeFlowIds = new ArrayList<>();
                rebateTimeFlowIds = rebateBankFlowMap.getOrDefault(deductRelationPo.getContractId(), new ArrayList<>());
                // 最新现金汇款流水时间
                if (rebateContractId != null) {
                    rebateTimeFlowIds.addAll(rebateBankFlowMap.getOrDefault(rebateContractId, new ArrayList<>()));
                }
                Map<Integer, CrmRevenueExpenditureFlowPo> finalRebateFlowPoMap = rebateFlowPoMap;
                List<CrmRevenueExpenditureFlowPo> expenditureFlowPos = rebateTimeFlowIds.stream()
                        .map(finalRebateFlowPoMap::get)
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(CrmRevenueExpenditureFlowPo::getPayTime).reversed())
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(expenditureFlowPos)) {
                    deductDto.setLastCashRevenueFlowTime(expenditureFlowPos.get(0).getPayTime());
                }
            }

            // 合同维度信息
            ContractDto contractDto = contractDtoMap.getOrDefault(deductRelationPo.getContractId(), ContractDto.builder().build());
            deductDto.setContractId(contractDto.getId());
            deductDto.setContractNo(contractDto.getContractNumber());
            deductDto.setContractName(contractDto.getName());
            deductDto.setContractBeginTime(contractDto.getBeginTime());
            deductDto.setContractEndTime(contractDto.getEndTime());
            deductDto.setInvoiceStatus(contractDto.getInvoiceStatus());
            deductDto.setSignProjectId(contractDto.getSignSubjectId());
            // 合同上的广告主账号客户
            deductDto.setAdvertiseAccountId(contractDto.getAccountId());
            AccAccountPo accAccountPo = accountPoMap.getOrDefault(contractDto.getAccountId(), AccAccountPo.builder().build());
            deductDto.setAdvertiseAccountName(accAccountPo.getUsername());
            deductDto.setAdvertiseCustomerId(accAccountPo.getCustomerId());
            CustomerPo advertiseCustomerPo = customerPoMap.getOrDefault(accAccountPo.getCustomerId(), CustomerPo.builder().build());
            deductDto.setAdvertiseCustomerName(advertiseCustomerPo.getUsername());
            if (ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(deductRelationPo.getType())) {
                // 回款日期距合同结束日期天数
                if (contractDto.getEndTime() != null) {
                    deductDto.setPayDateAndContractEndDays(Utils.getDateSpace(contractDto.getEndTime(),
                            deductDto.getFlowReceiveTime()) - 1);
                }
            } else if (ContractDeductType.REBATE_CHECK_DEDUCT.getCode().equals(deductRelationPo.getType())) {
                if (contractDto.getEndTime() != null) {
                    deductDto.setPayDateAndContractEndDays(Utils.getDateSpace(contractDto.getEndTime(),
                            deductDto.getBillingDate()) - 1);
                }
            }
            // 合同上的代理商的账号与客户信息
            if (Utils.isPositive(contractDto.getAgentId())) {
                Integer accountIdOfAgent = agentIdsMap.getOrDefault(contractDto.getAgentId(), 0);
                deductDto.setAgentAccountId(accountIdOfAgent);
                AccAccountPo accAccountPoOfAgent = accountPoMap.getOrDefault(accountIdOfAgent,
                        AccAccountPo.builder().build());
                deductDto.setAgentAccountName(accAccountPoOfAgent.getUsername());
                deductDto.setAgentCustomerId(accAccountPoOfAgent.getCustomerId());
                CustomerPo customerPoOfAgent = customerPoMap.getOrDefault(accAccountPoOfAgent.getCustomerId(),
                        CustomerPo.builder().build());
                deductDto.setAgentCustomerName(customerPoOfAgent.getUsername());
            }

            // 合同销售
            List<CrmContractSaleMappingPo> saleMappingPos = contractSalesMappingMap.getOrDefault(contractDto.getId(), Collections.EMPTY_LIST);
            deductDto.setBelongSales(saleMappingPos.stream().map(t -> getSaleNameById(salePoMap, t)).collect(Collectors.toList()));
            List<SaleDto> substituteSaleDtoList = substituteSaleMap.getOrDefault(contractDto.getId(), new ArrayList<>());
            List<SaleDto> substituteChannelSaleList = substituteSaleDtoList.stream().filter(saleDto -> Lists.newArrayList(SaleTypeEnum.BRAND_CHANNEL.getCode(), SaleTypeEnum.EFFECT_CHANNEL.getCode()).contains(saleDto.getType())).collect(Collectors.toList());
            List<SaleDto> substituteDirectSaleList = substituteSaleDtoList.stream().filter(saleDto -> Lists.newArrayList(SaleTypeEnum.BRAND_DIRECT.getCode(), SaleTypeEnum.EFFECT_DIRECT.getCode()).contains(saleDto.getType())).collect(Collectors.toList());
            deductDto.setSubstituteDirectSale(Optional.ofNullable(substituteDirectSaleList).orElse(new ArrayList<>()).stream().map(SaleDto::getName).collect(Collectors.joining(",")));
            deductDto.setSubstituteChannelSale(Optional.ofNullable(substituteChannelSaleList).orElse(new ArrayList<>()).stream().map(SaleDto::getName).collect(Collectors.joining(",")));
            // 计算合同的关账金额
            List<SimpleCrmInterlayerMoneyPo> interlayerMoneyPosOfContract = interlayMoneyMap.getOrDefault(contractDto.getId(), Collections.EMPTY_LIST);
            Long sumCloseAmount =
                    interlayerMoneyPosOfContract.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);
            Long sumNotCloseAmount =
                    interlayerMoneyPosOfContract.stream().filter(t -> YesOrNoEnum.NO.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);
            deductDto.setContractPackageAmount(new BigDecimal(Optional.ofNullable(contractDto.getAmount()).orElse(0L)));
            deductDto.setContractBillAmount(new BigDecimal(Optional.ofNullable(contractDto.getBillAmount()).orElse(0L) + Optional.ofNullable(contractDto.getOfflineBillAmount()).orElse(0L)));
            deductDto.setContractClosedAmount(new BigDecimal(sumCloseAmount));
            deductDto.setContractNotClosedAmount(new BigDecimal(sumNotCloseAmount));

            // oa 流程信息
            if (ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(deductRelationPo.getType())) {
                List<CrmContractOaFlowMappingPo> contractOaFlowMappingPos = contractOaFlowMappingPoMap.getOrDefault(contractDto.getId(), Collections.EMPTY_LIST);
                for (CrmContractOaFlowMappingPo crmContractOaFlowMappingPo : contractOaFlowMappingPos) {
                    CrmOaFlowPo crmOaFlowPo = crmOaFlowPoMap.get(crmContractOaFlowMappingPo.getOaFlowId());
                    if (OaFlowTypeEnum.BILL.getCode().equals(crmOaFlowPo.getType())) {
                        deductDto.setOaBillFlowNo(crmOaFlowPo.getOaFlowNo());
                    } else if (OaFlowTypeEnum.CONTRACT.getCode().equals(crmOaFlowPo.getType())) {
                        deductDto.setOaContractFlowNo(crmOaFlowPo.getOaFlowNo());
                    }
                }
            }

            // 解密
            deductDto = SensitiveHandler.decrypt(deductDto);


            // 帐期维度信息
//            CrmContractBillPeriodDto periodDto = periodDtoMap.getOrDefault(deductDto.getContractBillPeriodId(), new CrmContractBillPeriodDto());
//            deductDto.setBillPeriodRuleType(periodDto.getRuleType());
//            deductDto.setBillPeriodRuleLevel(periodDto.getRuleLevel());
//            deductDto.setBillPeriodStartTime(periodDto.getBillStartTime());
//            deductDto.setBillPeriodEndTime(periodDto.getBillEndTime());
//            deductDto.setBillPeriodCollectionDeadlineTime(periodDto.getCollectionDeadlineTime());
//            deductDto.setBillPeriodAdjustCollectionDeadlineTime(periodDto.getAdjustCollectionDeadlineTime());
//            deductDto.setBillPeriodBillAmount(periodDto.getBillAmount());
//            deductDto.setBillPeriodClosedAmount(periodDto.getClosedAmount());
//            deductDto.setBillPeriodBizStatus(periodDto.getBizStatus());
//            deductDto.setBillPeriodBankDeductAmount(periodDto.getBankDeductAmount());
//            deductDto.setBillPeriodDebateDeductAmount(periodDto.getDebateDeductAmount());
//            deductDto.setBillPeriodOfflineDeductAmount(periodDto.getOfflineDeductAmount());
//            deductDto.setBillPeriodDeductAmount(periodDto.getDeductAmount());
//            deductDto.setBillPeriodToDeductAmount(periodDto.getToBeDeductAmount());
            deductDtos.add(deductDto);


        }
        return deductDtos;
    }

    public Map<Integer, BigDecimal> queryCanDeductBizToDeductAmount(List<CrmContractPo> contractPos) {
        Map<Integer, BigDecimal> bizToDeductAmountMap = new HashMap<>();


        List<Integer> contractIds = contractPos.stream().map(CrmContractPo::getId).distinct().collect(Collectors.toList());
        // 获取合同的账单 map
        Map<Integer, List<SimpleCrmInterlayerMoneyPo>> interlayMoneyMap = crmInterlayerMoneyRepo.queryInterlayerMoneySimpleMapByContractIds(contractIds);

        for (CrmContractPo crmContractPo : contractPos) {
            List<SimpleCrmInterlayerMoneyPo> interlayerMoneyPosOfContract = interlayMoneyMap.getOrDefault(crmContractPo.getId(), Lists.newArrayList());

            // 实结后金额 = 合同金额+pd+实结金额（只包含人工部分）
            Long actualTotalAmount = interlayerMoneyPosOfContract.stream()
                    .filter(r -> (BillSourceType.CONTRACT_CLOSE.getCode().equals(r.getBillSource()) && BillCloseTypeEnum.PERSON_CLOSE.getCode().equals(r.getBillCloseType())))
                    .mapToLong(SimpleCrmInterlayerMoneyPo::getDailyPackageAmount).sum();

            BigDecimal actualAfterTotalAmount = BigDecimal.valueOf(crmContractPo.getAmount() + crmContractPo.getPdAmount() + Utils.fromMilliToFen(actualTotalAmount));
            BigDecimal bizToDeductAmount = actualAfterTotalAmount.subtract(BigDecimal.valueOf(crmContractPo.getTotalClaimAmount() + crmContractPo.getInitDeductedAmount()));
            bizToDeductAmountMap.put(crmContractPo.getId(), bizToDeductAmount);
        }
        return bizToDeductAmountMap;
    }


    public Map<Integer, BigDecimal> queryCanDeductToDeductAmount(List<CrmContractPo> contractPos) {
        Map<Integer, BigDecimal> toDeductAmountMap = new HashMap<>();

        List<Integer> contractIds = contractPos.stream().map(CrmContractPo::getId).distinct().collect(Collectors.toList());
        // 获取合同的账单 map
        Map<Integer, List<SimpleCrmInterlayerMoneyPo>> interlayMoneyMap = crmInterlayerMoneyRepo.queryInterlayerMoneySimpleMapByContractIds(contractIds);

        for (CrmContractPo crmContractPo : contractPos) {
            List<SimpleCrmInterlayerMoneyPo> interlayerMoneyPosOfContract = interlayMoneyMap.getOrDefault(crmContractPo.getId(), Lists.newArrayList());
            Long sumCloseAmount = interlayerMoneyPosOfContract.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);
            BigDecimal sumCloseAmountFen = BigDecimal.valueOf(sumCloseAmount).divide(BigDecimal.valueOf(1000d), 2, RoundingMode.HALF_UP);
            BigDecimal toDeductAmount = sumCloseAmountFen.subtract(BigDecimal.valueOf(crmContractPo.getTotalClaimAmount() + crmContractPo.getInitDeductedAmount()));
            toDeductAmountMap.put(crmContractPo.getId(), toDeductAmount);
        }
        return toDeductAmountMap;
    }

    /**
     * 根据合同 po 列表获取 合同可以抵扣 dto 列表
     *
     * @param contractPos         是否是可抵扣列表，如果是抵扣的列表需要过滤掉待抵扣金额为0和负数的情况
     * @param needCloseBillDetail 是否需要关账明细
     */
    public List<CanDeductContractDto> convertCanDeductContractPos2ContractDtos(List<CrmContractPo> contractPos,
                                                                               Boolean needCloseBillDetail, Timestamp closeBeginTime, Timestamp closeEndTime) {
        List<Integer> contractIds = contractPos.stream().map(CrmContractPo::getId).distinct().collect(Collectors.toList());
        // 获取合同的账单 map
        Map<Integer, List<SimpleCrmInterlayerMoneyPo>> interlayMoneyMap = crmInterlayerMoneyRepo.queryInterlayerMoneySimpleMapByContractIds(contractIds);
        Map<Integer, List<SaleDto>> contractSaleDtoMap = contractQueryService.getContractSaleDtosMap(contractIds);
        Map<Integer, List<SaleDto>> substituteSaleMap = saleContractSubstituteRepo.queryByContract(contractIds);
        // ================================== 代理商账号与客户 ==================================
        List<Integer> allAccountIds = new ArrayList<>();
        List<Integer> advertiseAccountIds = contractPos.stream().map(CrmContractPo::getAccountId).distinct().collect(Collectors.toList());
        allAccountIds.addAll(advertiseAccountIds);
        List<Integer> agentIds = contractPos.stream()
                .map(CrmContractPo::getAgentId)
                .filter(Utils::isPositive)
                .distinct().collect(Collectors.toList());
        List<CrmAgentPo> crmAgentPos = crmAgentRepo.queryAgentListByAgentIds(agentIds);
        List<Integer> accountIdsOfAgent = crmAgentPos.stream().map(CrmAgentPo::getSysAgentId).distinct().collect(Collectors.toList());
        Map<Integer, Integer> agentIdsMap = crmAgentPos.stream().collect(Collectors.toMap(CrmAgentPo::getId, CrmAgentPo::getSysAgentId));
        allAccountIds.addAll(accountIdsOfAgent);
        List<AccAccountPo> accAccountPos = accAccountRepo.queryAccountListByIds(allAccountIds);
        Map<Integer, AccAccountPo> accountPoMap = accAccountPos.stream().collect(Collectors.toMap(AccAccountPo::getAccountId, t -> t));
        List<Integer> allCustomerIds = accAccountPos.stream().map(AccAccountPo::getCustomerId).distinct().collect(Collectors.toList());
        Map<Integer, CustomerPo> customerPoMap = customerRepo.queryMapByCustomerIds(allCustomerIds);
        Map<Integer, List<CrmContractArchiveRecordPo>> contractArchiveRecordMap =
                crmContractArchiveRecordRepo.queryMapByContractIds(contractIds);
        // ================================== 获取合同的 oa 信息 ==================================
        List<CrmContractOaFlowMappingPo> crmContractOaFlowMappingPos = crmContractOaFlowMappingRepo.queryCrmContractOaFlowListByContractIds(contractIds);
        Map<Integer, List<CrmContractOaFlowMappingPo>> contractOaFlowMappingPoMap = crmContractOaFlowMappingPos.stream().collect(Collectors.groupingBy(CrmContractOaFlowMappingPo::getContractId));
        List<Integer> oaFlowIds = crmContractOaFlowMappingPos.stream().map(CrmContractOaFlowMappingPo::getOaFlowId)
                .distinct().collect(Collectors.toList());
        List<CrmOaFlowPo> crmOaFlowPos = crmOaFlowRepo.queryOaFlowByOaFlowIds(oaFlowIds);
        Map<Integer, CrmOaFlowPo> crmOaFlowPoMap = crmOaFlowPos.stream().collect(Collectors.toMap(CrmOaFlowPo::getId, t -> t));
        // oa 合同
        List<CrmOaFlowPo> oaContractFlowPos = crmOaFlowPos.stream().filter(t -> OaFlowTypeEnum.CONTRACT.getCode().equals(t.getType())).collect(Collectors.toList());
        List<CrmContractOaFlowPo> crmContractOaFlowPos = crmContractOaFlowRepo.queryCrmContractOaFlowByFlowIds(oaContractFlowPos.stream().map(t -> t.getId()).collect(Collectors.toList()));
        Map<Integer, CrmContractOaFlowPo> contractOaFlowPoMap = crmContractOaFlowPos.stream().collect(Collectors.toMap(CrmContractOaFlowPo::getCrmOaFlowId,
                Function.identity(), (t1, t2) -> t1));
        // 该合同所有被抵扣的关系(含有银企直连和返点核算抵扣)
        List<CrmFlowContractDeductRelationPo> crmFlowContractDeductRelationPos = crmFlowContractDeductRelationRepo.queryByContractIds(contractIds);
        Map<Integer, List<CrmFlowContractDeductRelationPo>> deductRelationPoMap = crmFlowContractDeductRelationPos.stream().collect(Collectors.groupingBy(t -> t.getContractId()));
        List<CrmRevenueExpenditureFlowPo> allExpenditureFlowPos =
                crmRevenueExpenditureFlowRepo.queryListByIds(crmFlowContractDeductRelationPos.stream().map(CrmFlowContractDeductRelationPo::getFlowId).distinct().collect(Collectors.toList()));
        Map<Integer, CrmRevenueExpenditureFlowPo> flowPosMap = allExpenditureFlowPos.stream().collect(Collectors.toMap(CrmRevenueExpenditureFlowPo::getId, t -> t));
        // 返点核算 id
        List<Integer> rebateIds = crmFlowContractDeductRelationPos.stream()
                .filter(t -> ContractDeductType.REBATE_CHECK_DEDUCT.getCode().equals(t.getType()))
                .map(CrmFlowContractDeductRelationPo::getFlowId).collect(Collectors.toList());
        Map<Integer, CrmRebateCheckRecordPo> rebateCheckMap = crmRebateCheckRecordRepo.queryMapByIds(rebateIds);
        List<Integer> contractIdsOfRebate = rebateCheckMap.values().stream().map(CrmRebateCheckRecordPo::getCrmContractId).distinct().collect(Collectors.toList());
        Map<Integer, CrmContractPo> contractPoMapOfRebate = crmContractRepo.queryContractMapByIds(contractIdsOfRebate);
        // 合同的返点核算开票信息
        Map<Integer, List<CrmRebateCheckRecordPo>> rebateCheckRecordPoMap = crmRebateCheckRecordRepo.queryMapByContractIds(contractIds);
        Map<Integer, List<ContractFinanceRebateRecordDto>> rebateRecordMap = contractFinanceRebateRecordService.queryRecordInContractIds(contractIds);
        // 合同id -> 银企直联回款流水list，包含合同 & 合同的返点抵扣合同
        Map<Integer, List<Integer>> deductBankFlowMap = Maps.newHashMap();
        // 银企直联回款流水id -> 银企直联回款流水po
        Map<Integer, CrmRevenueExpenditureFlowPo> deductFlowPoMap = Maps.newHashMap();
        contractIdsOfRebate.addAll(contractIds);
        if (CollectionUtils.isNotEmpty(contractIdsOfRebate)) {
            CrmFlowContractDeductRelationPoExample example = new CrmFlowContractDeductRelationPoExample();
            example.createCriteria()
                    .andContractIdIn(contractIdsOfRebate)
                    .andTypeEqualTo(ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<CrmFlowContractDeductRelationPo> relationPos = crmFlowContractDeductRelationRepo.queryByExample(example);
            if (CollectionUtils.isNotEmpty(relationPos)) {
                deductBankFlowMap = relationPos.stream().collect(Collectors.groupingBy(CrmFlowContractDeductRelationPo::getContractId, Collectors.mapping(CrmFlowContractDeductRelationPo::getFlowId, Collectors.toList())));
                List<CrmRevenueExpenditureFlowPo> rebateFlowPos = crmRevenueExpenditureFlowRepo.queryListByIds(deductBankFlowMap.values().stream().flatMap(Collection::stream).distinct().collect(Collectors.toList()));
                deductFlowPoMap = rebateFlowPos.stream().collect(Collectors.toMap(CrmRevenueExpenditureFlowPo::getId, Function.identity()));
            }
        }
        List<CrmContractStatusRecordPo> statusRecordPosByCrmContractIds = contractQueryServiceHelper.getStatusRecordPosByCrmContractIds(contractIds);
        Map<Integer, List<CrmContractStatusRecordPo>> statusRecordPosByCrmContractIdsMap = statusRecordPosByCrmContractIds.stream().collect(Collectors.groupingBy(CrmContractStatusRecordPo::getCrmContractId));
        // 执行单数据
        Map<Integer, ExecutiveOrderBasicDto> contractId2ExecutiveOrderBasicDtoMap = crmContractExecutiveOrderRepo.queryBasicMapByContractIds(contractIds);
        Map<Integer, CrmContractBillRuleDto> ruleDtoMap  = contractBillPeriodService.queryRuleByContract(contractIds);
        List<CanDeductContractDto> contractOfFlowDtos = new ArrayList<>();
        for (CrmContractPo contractPo : contractPos) {
            CanDeductContractDto contractOfFlowDto = CanDeductContractDto.builder().build();
            // 合同字段
            contractOfFlowDto.setContractId(contractPo.getId());
            contractOfFlowDto.setContractType(contractPo.getType());
            contractOfFlowDto.setOptAgentId(contractPo.getOptAgentId());
            contractOfFlowDto.setOptAgentName(contractPo.getOptAgentName());
            contractOfFlowDto.setOnlineTime(contractPo.getLaunchTime());
            contractOfFlowDto.setContractNo(String.valueOf(contractPo.getContractNumber()));
            contractOfFlowDto.setContractName(contractPo.getName());
            contractOfFlowDto.setContractBizStatus(contractPo.getBusStatus());
            contractOfFlowDto.setContractBizStatusDesc(ContractBusStatus.getByCode(contractPo.getBusStatus()).getDesc());
            contractOfFlowDto.setContractArchiveStatus(contractPo.getArchiveStatus());
            contractOfFlowDto.setContractArchiveStatusDesc(ContractArchiveStatus.getByCode(contractPo.getArchiveStatus()).getDesc());
            contractOfFlowDto.setContractAuditStatus(contractPo.getAuditStatus());
            contractOfFlowDto.setContractAuditStatusDesc(ContractAuditStatus.getByCode(contractPo.getAuditStatus()).getDesc());
            contractOfFlowDto.setContractPackageAmount(new BigDecimal(contractPo.getAmount() + contractPo.getPdAmount())); // 打包价
            contractOfFlowDto.setContractAmount(new BigDecimal(contractPo.getAmount())); // 实际金额
            contractOfFlowDto.setOnlineBillAmount(new BigDecimal(contractPo.getBillAmount())); // 合同线上开票金额
            contractOfFlowDto.setOfflineBillAmount(new BigDecimal(contractPo.getOfflineBillAmount())); // 合同线下开票金额
            contractOfFlowDto.setIsOaOpenBill(contractPo.getIsOaOpenBill());
            contractOfFlowDto.setExecutiveOrderState(contractId2ExecutiveOrderBasicDtoMap.getOrDefault(contractPo.getId(), ExecutiveOrderBasicDto.builder().build()).getState());
            contractOfFlowDto.setHasOptAgent(contractPo.getHasOptAgent());
            contractOfFlowDto.setOptAgentId(contractPo.getOptAgentId());
            contractOfFlowDto.setOptAgentName(contractPo.getOptAgentName());
            contractOfFlowDto.setSignProjectId(contractPo.getSignSubjectId());
            //合同开票金额 = 线上已开票金额 + 线下已开票金额
            BigDecimal invoiceAmount = BigDecimal.valueOf(contractPo.getBillAmount() + contractPo.getOfflineBillAmount());
            invoiceAmount = Utils.fromFenToYuan(invoiceAmount);
            //返点记录，兼容历史
            List<ContractFinanceRebateRecordDto> rebateRecordDtos = Optional.ofNullable(rebateRecordMap.get(contractPo.getId()))
                    .orElse(Lists.newArrayList(ContractFinanceRebateRecordDto.builder().build()))
                    .stream().filter(dto -> new Integer(1).equals(dto.getType())).collect(Collectors.toList());
            //返点金额，该合同返点记录中的所有返点金额汇总
            BigDecimal rebateAmount = CollectionUtils.isEmpty(rebateRecordDtos) ? BigDecimal.ZERO
                    : Utils.fromFenToYuan(rebateRecordDtos.stream().mapToLong(dto -> contractQueryServiceHelper.calculateAmount(dto.getRebateBaseAmount(), dto.getRebateRate()))
                    .summaryStatistics().getSum());
            //开票状态，已开票金额+返点金额<合同金额，则开票状态为“未完成”，否则为“已完成”
            BigDecimal contractAmount = Utils.fromFenToYuan(contractPo.getAmount() + contractPo.getPdAmount()); // FIXME 没有算实结金额
            boolean invoiceStatus = (invoiceAmount.add(rebateAmount)).compareTo(contractAmount) < 0;
            String contractInvoiceStatusDesc = invoiceStatus ? ContractInvioceStatus.INCOMPLETED.getDesc() : ContractInvioceStatus.COMPLETED.getDesc();
            //未开票
            if (invoiceAmount.compareTo(BigDecimal.ZERO) == 0) {
                contractInvoiceStatusDesc = ContractInvioceStatus.UN_INVOICE.getDesc();
            }
            contractOfFlowDto.setInvoiceStatus(contractInvoiceStatusDesc);
            // 合同总开票金额 = 线上开票金额 + 线下开票金额
            contractOfFlowDto.setContractBillAmount(contractOfFlowDto.getOnlineBillAmount().add(contractOfFlowDto.getOfflineBillAmount()));
            contractOfFlowDto.setBegin_time(contractPo.getBeginTime());
            contractOfFlowDto.setEnd_time(contractPo.getEndTime());
            contractOfFlowDto.setBillPeriod(contractPo.getBillPeriod());
            // 超时天数: 当前日期-合同结束日期
            Integer billPeriod = contractPo.getBillPeriod();
            if (contractPo.getEndTime() != null) {
                int timeoutDays = Utils.getDateSpace(contractPo.getEndTime(), Utils.getToday()) - 1 - billPeriod;
                contractOfFlowDto.setTimeoutDays(Math.max(timeoutDays, 0));
            }
            // 合同上的广告主账号客户
            contractOfFlowDto.setAdvertiseAccountId(contractPo.getAccountId());
            AccAccountPo accAccountPo = accountPoMap.getOrDefault(contractPo.getAccountId(), AccAccountPo.builder().build());
            contractOfFlowDto.setAdvertiseAccountName(accAccountPo.getUsername());
            contractOfFlowDto.setAdvertiseCustomerId(accAccountPo.getCustomerId());
            contractOfFlowDto.setAdvertiseDepartmentId(accAccountPo.getDepartmentId());

            // 广告主账户所属集团
            contractOfFlowDto.setAdvertiseGroupId(accAccountPo.getGroupId());
            // 广告主客户名称
            CustomerPo customerPo = customerPoMap.getOrDefault(accAccountPo.getCustomerId(), CustomerPo.builder().build());
            contractOfFlowDto.setAdvertiseCustomerName(customerPo.getUsername());
            // 广告主客户新行业信息
            contractOfFlowDto.setCustomerUnitedFirstIndustryId(customerPo.getUnitedFirstIndustryId());
            contractOfFlowDto.setCustomerUnitedSecondIndustryId(customerPo.getUnitedSecondIndustryId());
            contractOfFlowDto.setCustomerUnitedThirdIndustryId(customerPo.getUnitedThirdIndustryId());
            // 账期规则
            CrmContractBillRuleDto billRuleDto = ruleDtoMap.getOrDefault(contractOfFlowDto.getContractId(), new CrmContractBillRuleDto());
            contractOfFlowDto.setBillRulePeriod(billRuleDto.getBillPeriod());
            contractOfFlowDto.setRuleLevel(billRuleDto.getRuleLevel());
            contractOfFlowDto.setBillIsUnpack(billRuleDto.getBillPeriodUnpack());
            if (contractOfFlowDto.getRuleLevel() != null && ContractBillRuleLevel.queryMultiBillRules().contains(contractOfFlowDto.getRuleLevel())) {
                DecimalFormat df = new DecimalFormat("#.0");
                contractOfFlowDto.setBillPeriodString(df.format(contractOfFlowDto.getBillRulePeriod()) + "月");
            } else {
                contractOfFlowDto.setBillPeriodString(contractOfFlowDto.getBillPeriod() + "天");
            }
            // 合同上的代理商的账号与客户信息
            if (Utils.isPositive(contractPo.getAgentId())) {
                Integer accountIdOfAgent = agentIdsMap.getOrDefault(contractPo.getAgentId(), 0);
                contractOfFlowDto.setAgentAccountId(accountIdOfAgent);
                AccAccountPo accAccountPoOfAgent = accountPoMap.getOrDefault(accountIdOfAgent,
                        AccAccountPo.builder().build());
                contractOfFlowDto.setAgentAccountName(accAccountPoOfAgent.getUsername());
                contractOfFlowDto.setAgentGroupId(accAccountPoOfAgent.getGroupId());

                contractOfFlowDto.setAgentCustomerId(accAccountPoOfAgent.getCustomerId());
                CustomerPo customerPoOfAgent = customerPoMap.getOrDefault(accAccountPoOfAgent.getCustomerId(),
                        CustomerPo.builder().build());
                contractOfFlowDto.setAgentCustomerName(customerPoOfAgent.getUsername());
            }
            // 合同销售
            List<SaleDto> saleDtos = contractSaleDtoMap.getOrDefault(contractPo.getId(), new ArrayList<>());
            contractOfFlowDto.setSaleDtos(saleDtos);
            contractOfFlowDto.setBelongSales(saleDtos.stream().map(SaleDto::getName).distinct().collect(Collectors.toList()));
            contractOfFlowDto.setBelongSaleDtos(saleDtos.stream().map(t -> {
                    SaleBaseDto saleBaseDto = new SaleBaseDto();
                    BeanUtils.copyProperties(t, saleBaseDto);
                    return saleBaseDto;
            }).filter(Objects::nonNull).collect(Collectors.toList()));
            List<SaleDto> substituteSaleDtoList = substituteSaleMap.getOrDefault(contractPo.getId(), new ArrayList<>());
            contractOfFlowDto.setSubstituteSaleDtoList(substituteSaleDtoList);
            List<SaleDto> substituteChannelSaleList = substituteSaleDtoList.stream().filter(saleDto -> Lists.newArrayList(SaleTypeEnum.BRAND_CHANNEL.getCode(), SaleTypeEnum.EFFECT_CHANNEL.getCode()).contains(saleDto.getType())).collect(Collectors.toList());
            List<SaleDto> substituteDirectSaleList = substituteSaleDtoList.stream().filter(saleDto -> Lists.newArrayList(SaleTypeEnum.BRAND_DIRECT.getCode(), SaleTypeEnum.EFFECT_DIRECT.getCode()).contains(saleDto.getType())).collect(Collectors.toList());
            contractOfFlowDto.setSubstituteDirectSale(Optional.ofNullable(substituteDirectSaleList).orElse(new ArrayList<>()).stream().map(SaleDto::getName).collect(Collectors.joining(",")));
            contractOfFlowDto.setSubstituteChannelSale(Optional.ofNullable(substituteChannelSaleList).orElse(new ArrayList<>()).stream().map(SaleDto::getName).collect(Collectors.joining(",")));
            // 计算合同的关账金额
            List<SimpleCrmInterlayerMoneyPo> interlayerMoneyPosOfContract = interlayMoneyMap.getOrDefault(contractPo.getId(), Collections.EMPTY_LIST);
            Long sumCloseAmount = interlayerMoneyPosOfContract.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);
            Long sumNotCloseAmount = interlayerMoneyPosOfContract.stream().filter(t -> YesOrNoEnum.NO.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);

            // 实结后金额 = 合同金额+pd+实结金额（只包含人工部分）
            Long actualTotalAmount = interlayerMoneyPosOfContract.stream()
                    .filter(r -> (BillSourceType.CONTRACT_CLOSE.getCode().equals(r.getBillSource()) && BillCloseTypeEnum.PERSON_CLOSE.getCode().equals(r.getBillCloseType())))
                    .mapToLong(SimpleCrmInterlayerMoneyPo::getDailyPackageAmount).sum();

            BigDecimal actualAfterTotalAmount = BigDecimal.valueOf(contractPo.getAmount() + contractPo.getPdAmount() + Utils.fromMilliToFen(actualTotalAmount));
            contractOfFlowDto.setAdjustedTotalAmount(actualAfterTotalAmount);
            contractOfFlowDto.setContractClosedAmount(new BigDecimal(sumCloseAmount)); // 合同关账金额
            contractOfFlowDto.setContractNotClosedAmount(new BigDecimal(sumNotCloseAmount)); // 合同未关账金额
            // 流水合同的抵扣关系(包含银企直连连 + 返点核算抵扣)
            List<CrmFlowContractDeductRelationPo> flowContractDeductPosOfContract = deductRelationPoMap.get(contractPo.getId());
            if (CollectionUtils.isNotEmpty(flowContractDeductPosOfContract)) {
                // 最新一笔银企直连抵扣的打款时间
                Optional<CrmFlowContractDeductRelationPo> latestDeductRel =
                        flowContractDeductPosOfContract.stream().sorted(Comparator.comparing(CrmFlowContractDeductRelationPo::getCtime).reversed()).findFirst();
                if (latestDeductRel.isPresent()) {
                    CrmFlowContractDeductRelationPo latestFlowContractDeductRelationPo = latestDeductRel.get();
                    if (ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(latestFlowContractDeductRelationPo.getType())) {
                        CrmRevenueExpenditureFlowPo flowPoOfContract = flowPosMap.getOrDefault(latestFlowContractDeductRelationPo.getFlowId(),CrmRevenueExpenditureFlowPo.builder().build());
                        contractOfFlowDto.setLatestBankConnectDeductPayTime(flowPoOfContract.getPayTime());
                    }
                }
                // 银企直连总抵扣 和 返点核算总抵扣
                List<CrmFlowContractDeductRelationPo> bankConnectDeductRels = flowContractDeductPosOfContract.stream().filter(t -> ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(t.getType())).collect(Collectors.toList());
                contractOfFlowDto.setBankDirectConnectTotalDeductAmount(new BigDecimal(bankConnectDeductRels.stream().mapToLong(t -> t.getDeductAmount()).sum())); // 合同总的银企直连抵扣金额
                if (CollectionUtils.isNotEmpty(bankConnectDeductRels)) {
                    List<Integer> haveFlowIdList = bankConnectDeductRels.stream().map(CrmFlowContractDeductRelationPo::getFlowId).collect(Collectors.toList());
                    allExpenditureFlowPos.stream()
                            .filter(e -> haveFlowIdList.contains(e.getId())).max(Comparator.comparing(CrmRevenueExpenditureFlowPo::getPayTime))
                            .ifPresent(expenditureFlowPo -> contractOfFlowDto.setLatestBankConnectDeductPayTime2(expenditureFlowPo.getPayTime()));
                }

                List<CrmFlowContractDeductRelationPo> reversedDeductRelations = flowContractDeductPosOfContract.stream().filter(t -> ContractDeductType.REVERSED_DEDUCT.getCode().equals(t.getType())).collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(reversedDeductRelations)) {
                    reversedDeductRelations.stream().max(Comparator.comparing(CrmFlowContractDeductRelationPo::getCtime)).ifPresent(crmFlowContractDeductRelationPo -> contractOfFlowDto.setReversedDeductOperateTime(crmFlowContractDeductRelationPo.getCtime()));
                }

                // 返点核算抵扣（只会有一个）
                List<CrmFlowContractDeductRelationPo> rebateDeductRels = flowContractDeductPosOfContract.stream().filter(t -> ContractDeductType.REBATE_CHECK_DEDUCT.getCode().equals(t.getType())).collect(Collectors.toList());
                contractOfFlowDto.setRebateTotalDeductAmount(new BigDecimal(rebateDeductRels.stream().mapToLong(t -> t.getDeductAmount()).sum())); // 合同总的返点核算抵扣金额
                if (CollectionUtils.isNotEmpty(rebateDeductRels)) {
                    CrmRebateCheckRecordPo rebateCheckRecordPo = rebateCheckMap.get(rebateDeductRels.get(0).getFlowId());
                    CrmContractPo crmContractPoOfRebate = contractPoMapOfRebate.get(rebateCheckRecordPo.getCrmContractId());
                    if (crmContractPoOfRebate != null) {
                        contractOfFlowDto.setRebateCheckDeductContractId(crmContractPoOfRebate.getId());
                        contractOfFlowDto.setRebateCheckDeductContractStatus(crmContractPoOfRebate.getBusStatus());
                        contractOfFlowDto.setRebateCheckDeductContractStatusDesc(ContractBusStatus.getByCode(crmContractPoOfRebate.getBusStatus()).getDesc());
                    }
                    contractOfFlowDto.setLastDebateDeductTime(rebateDeductRels.stream().map(CrmFlowContractDeductRelationPo::getCtime).max(Timestamp::compareTo).orElse(null));
                }
                contractOfFlowDto.setLastDeductTime(CrmUtils.getMaxTime(contractOfFlowDto.getLastDebateDeductTime(),contractOfFlowDto.getLatestBankConnectDeductPayTime2()));
                // 最新现金汇款流水时间
                List<Integer> rebateTimeFlowIds = deductBankFlowMap.getOrDefault(contractPo.getId(), new ArrayList<>());// 当前合同的银企直联抵扣流水id
                if (contractOfFlowDto.getRebateCheckDeductContractId() != null) {
                    rebateTimeFlowIds.addAll(deductBankFlowMap.getOrDefault(contractOfFlowDto.getRebateCheckDeductContractId(), new ArrayList<>()));//当前合同的返点抵扣合同的 银企直联抵扣流水id
                }
                Map<Integer, CrmRevenueExpenditureFlowPo> finalRebateFlowPoMap = deductFlowPoMap;
                List<CrmRevenueExpenditureFlowPo> expenditureFlowPos = rebateTimeFlowIds.stream()
                        .map(finalRebateFlowPoMap::get)
                        .filter(Objects::nonNull)
                        .sorted(Comparator.comparing(CrmRevenueExpenditureFlowPo::getPayTime).reversed())
                        .collect(Collectors.toList());
                if (CollectionUtils.isNotEmpty(expenditureFlowPos)) {
                    contractOfFlowDto.setLastCashRevenueFlowTime(expenditureFlowPos.get(0).getPayTime());
                }
                // 合同最新回款时间
                Timestamp latestClaimTimeOfContract = flowContractDeductPosOfContract.stream().map(t -> t.getCtime()).max(Timestamp::compareTo).get();
                contractOfFlowDto.setFlowLatestClaimTime(latestClaimTimeOfContract);
                // 最新收款时间
                Timestamp latestFlowPayTime = null;
                for (CrmFlowContractDeductRelationPo deductRelationPo : flowContractDeductPosOfContract) {
                    CrmRevenueExpenditureFlowPo flowPoOfContract = flowPosMap.get(deductRelationPo.getFlowId());
                    if (flowPoOfContract != null) {
                        if (latestFlowPayTime == null) {
                            latestFlowPayTime = flowPoOfContract.getPayTime();
                        }
                        if (flowPoOfContract.getPayTime().compareTo(latestFlowPayTime) > 0) {
                            latestFlowPayTime = flowPoOfContract.getPayTime();
                        }
                    }
                }
                contractOfFlowDto.setFlowLatestReceiveTime(latestFlowPayTime);
            }
            // 合同返点信息
            List<CrmRebateCheckRecordPo> rebateCheckRecordPos = rebateCheckRecordPoMap.get(contractPo.getId());
            if (CollectionUtils.isNotEmpty(rebateCheckRecordPos)) {
                long sumRebateCheckAmount = rebateCheckRecordPos.stream().filter(t -> OAFlowStatusEnum.COMPLETED.getCode().equals(t.getOaFlowStatus())).mapToLong(t -> t.getCheckAmount()).sum();
                contractOfFlowDto.setRebateCheckAmount(new BigDecimal(sumRebateCheckAmount));
                // 已关账金额-已开票金额(总开票金额 = 线上开票金额 + 线下开票金额)-返点核算金额(该合同的所有的负数开票金额之和[是个负数])   100-74-（-2）
                contractOfFlowDto.setHasSettledRebateCheckAmount(new BigDecimal(sumCloseAmount / 1000).subtract(contractOfFlowDto.getContractBillAmount()).subtract(contractOfFlowDto.getRebateCheckAmount()));
            } else {
                long sumRebateCheckAmount = 0L;
                contractOfFlowDto.setRebateCheckAmount(new BigDecimal(sumRebateCheckAmount));
                // 已关账金额-已开票金额(整数开票金额)-返点核算金额(该合同的所有的负数开票金额之和[是个负数])   100-74-（-2）
                contractOfFlowDto.setHasSettledRebateCheckAmount(new BigDecimal(sumCloseAmount / 1000).subtract(contractOfFlowDto.getContractBillAmount()).subtract(contractOfFlowDto.getRebateCheckAmount()));
            }
            // 计算合同维度的抵扣金额
            contractOfFlowDto.setTotalClaimAmount(new BigDecimal(contractPo.getTotalClaimAmount())); // 线上总抵扣库
            contractOfFlowDto.setInitDeductedAmount(new BigDecimal(contractPo.getInitDeductedAmount())); // 初始化抵扣
            // 合同总抵扣 = 线下抵扣 + 线上抵扣
            contractOfFlowDto.setTotalDeductedAmount(contractOfFlowDto.getTotalClaimAmount().add(contractOfFlowDto.getInitDeductedAmount()));
            String isDeductCompleted = "";
            if (contractPo.getIsOaOpenBill() == 1) {
                // 开票的话，待抵扣 = 开票金额(线上+线下) - 已抵扣(线下已抵扣 + 线上已抵扣)
                isDeductCompleted = YesOrNoEnum.YES.getCode().equals(contractPo.getIsDeductCompleted()) ? "是" : "否";
            } else {
                // 未开票的话，待抵扣 = 打包价 - 已抵扣(线下已抵扣 + 线上已抵扣)
                isDeductCompleted = "否";
            }
            List<CrmContractOaFlowMappingPo> contractOaFlowMappingPos = contractOaFlowMappingPoMap.getOrDefault(contractPo.getId(), new ArrayList<>());
            // 关帐金额
//            BigDecimal toDeductAmount = BigDecimal.valueOf(Utils.fromMilliToFen(sumCloseAmount)).subtract(BigDecimal.valueOf(contractPo.getTotalClaimAmount() + contractPo.getInitDeductedAmount()));

            BigDecimal sumCloseAmountFen = BigDecimal.valueOf(sumCloseAmount).divide(BigDecimal.valueOf(1000d), 0, RoundingMode.HALF_UP);
            BigDecimal toDeductAmount = sumCloseAmountFen.subtract(BigDecimal.valueOf(contractPo.getTotalClaimAmount() + contractPo.getInitDeductedAmount()));

            // 业务口径合同待抵扣金额 = 实结后总金额 - 线上 - 线下
            BigDecimal bizToDeductAmount = actualAfterTotalAmount.subtract(BigDecimal.valueOf(contractPo.getTotalClaimAmount() + contractPo.getInitDeductedAmount()));

            contractOfFlowDto.setBizToDeductAmount(bizToDeductAmount);
            contractOfFlowDto.setToDeductAmount(toDeductAmount);
            contractOfFlowDto.setContractIsDeductCompleted(isDeductCompleted);
            setOaFlowInfoForContract(crmOaFlowPoMap, contractOaFlowPoMap, contractOfFlowDto, contractOaFlowMappingPos);
            // 合同归档时间为空，历史合同法务合同上传的记录时间
            if (contractOfFlowDto.getContractArchiveTime() == null) {
                List<CrmContractArchiveRecordPo> crmContractArchiveRecordPos = contractArchiveRecordMap.get(contractPo.getId());
                if (CollectionUtils.isNotEmpty(crmContractArchiveRecordPos)) {
                    Optional<CrmContractArchiveRecordPo> contractArchiveRecordPo = crmContractArchiveRecordPos.stream().sorted(Comparator.comparing(CrmContractArchiveRecordPo::getCtime).reversed()).findFirst();
                    if (contractArchiveRecordPo.isPresent()) {
                        contractOfFlowDto.setContractArchiveTime(contractArchiveRecordPo.get().getProcessingTime());
                    }
                }
            }
            List<CrmContractStatusRecordPo> contractStatusRecordPos = statusRecordPosByCrmContractIdsMap.getOrDefault(contractPo.getId(), Collections.emptyList());
            CrmContractStatusRecordPo crmContractStatusRecordPo = contractStatusRecordPos.stream().filter(r -> ContractBusStatus.COMPLETED.getCode().equals(r.getBusStatus())).findFirst().orElse(CrmContractStatusRecordPo.builder().build());
            contractOfFlowDto.setContractCompleteTime(crmContractStatusRecordPo.getCtime());
            // 有关账明细的关账月份与金额
            if (needCloseBillDetail != null && needCloseBillDetail) {
                if (!CollectionUtils.isEmpty(interlayerMoneyPosOfContract)) {

                    // 过滤关账账单
                    List<SimpleCrmInterlayerMoneyPo> finalMoneyPos = interlayerMoneyPosOfContract.stream()
                            .filter(t -> {
                                if (! Objects.equals(t.getIsClosed(), IsValid.TRUE.getCode())) {
                                    return false;
                                }
                                if (t.getClosingDate() == null) {
                                    return false;
                                }
                                // 增加对关账时间的过滤
                                if (null != closeBeginTime && null != closeEndTime) {
                                    return CrmUtils.timestampBetween(t.getClosingDate(), closeBeginTime, closeEndTime);
                                }

                                return true;
                            }).collect(Collectors.toList());

                    Map<Integer, List<SimpleCrmInterlayerMoneyPo>> interlayMoneyMapByMonth = finalMoneyPos.stream().collect(Collectors.groupingBy(t -> Utils.getMonth(t.getClosingDate())));
                    Set<Map.Entry<Integer, List<SimpleCrmInterlayerMoneyPo>>> entriesByMonth = interlayMoneyMapByMonth.entrySet();
                    for (Map.Entry<Integer, List<SimpleCrmInterlayerMoneyPo>> entryByMonth : entriesByMonth) {
                        List<SimpleCrmInterlayerMoneyPo> interlayerMoneyPos = entryByMonth.getValue();
                        long sumCloseAmountOfMonth = interlayerMoneyPos.stream().filter(t -> t.getIsClosed() != 0 && t.getClosingDate() != null).mapToLong(t -> t.getDailyPackageAmount()).sum();
                        // bean copy
                        CanDeductContractDto contractOfFlowDtoCopy = CanDeductContractDto.builder().build();
                        BeanUtils.copyProperties(contractOfFlowDto, contractOfFlowDtoCopy);
                        contractOfFlowDtoCopy.setCloseBillMonth(entryByMonth.getKey());
                        contractOfFlowDtoCopy.setCloseBillAmount(new BigDecimal(sumCloseAmountOfMonth));
                        contractOfFlowDtoCopy.setClosedBillList(interlayerMoneyPos);
                        contractOfFlowDtos.add(contractOfFlowDtoCopy);
                    }
                }
            } else {
                contractOfFlowDtos.add(contractOfFlowDto);
            }
        }
        return contractOfFlowDtos;
    }

    /**
     * 为合同设置 oa 流程信息
     *
     * @param crmOaFlowPoMap           oa 流程 map
     * @param contractOaFlowPoMap      oa 合同 map
     * @param contractOfFlowDto
     * @param contractOaFlowMappingPos crm 合同与 oa 流程关系 map
     */
    private void setOaFlowInfoForContract(Map<Integer, CrmOaFlowPo> crmOaFlowPoMap, Map<Integer, CrmContractOaFlowPo> contractOaFlowPoMap, CanDeductContractDto contractOfFlowDto, List<CrmContractOaFlowMappingPo> contractOaFlowMappingPos) {
        Integer recentlyCompletedInvoiceId = 0;
        for (CrmContractOaFlowMappingPo crmContractOaFlowMappingPo : contractOaFlowMappingPos) {
            CrmOaFlowPo crmOaFlowPo = crmOaFlowPoMap.get(crmContractOaFlowMappingPo.getOaFlowId());
            if (crmOaFlowPo != null) {
                if (OaFlowTypeEnum.BILL.getCode().equals(crmOaFlowPo.getType())) {
                    if (OAFlowStatusEnum.COMPLETED.getCode().equals(crmOaFlowPo.getStatus())) {
                        if (crmOaFlowPo.getId() > recentlyCompletedInvoiceId) {
                            recentlyCompletedInvoiceId = crmOaFlowPo.getId();
                            // 获取最新的审核通过的完成的 oa 开票
                            contractOfFlowDto.setCrmOaFlowId(crmOaFlowPo.getId());
                            contractOfFlowDto.setOaBillFlowNo(crmOaFlowPo.getOaFlowNo());
                        }
                    }
                } else if (OaFlowTypeEnum.CONTRACT.getCode().equals(crmOaFlowPo.getType())) {
                    // oa 合同
                    CrmContractOaFlowPo contractOaFlowPo = contractOaFlowPoMap.getOrDefault(crmOaFlowPo.getId(), CrmContractOaFlowPo.builder().build());
                    contractOfFlowDto.setCrmOaFlowId(crmOaFlowPo.getId());
                    contractOfFlowDto.setOaContractFlowNo(crmOaFlowPo.getOaFlowNo());
                    contractOfFlowDto.setOaContractNo(contractOaFlowPo.getOaContractNo());
                    contractOfFlowDto.setContractArchiveTime(contractOaFlowPo.getEndTime());
                }
            }
        }
    }

    private String getSaleNameById(Map<Integer, CrmSalePo> salePoMap, CrmContractSaleMappingPo t) {
        CrmSalePo crmSalePo = salePoMap.get(t.getCrmSaleId());
        if (crmSalePo != null) {
            return crmSalePo.getName();
        }
        return "";
    }
}
