package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CustomerDepartmentMappingPo;
import com.bilibili.crm.platform.biz.po.CustomerDepartmentMappingPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CustomerDepartmentMappingDao {
    long countByExample(CustomerDepartmentMappingPoExample example);

    int deleteByExample(CustomerDepartmentMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerDepartmentMappingPo record);

    int insertBatch(List<CustomerDepartmentMappingPo> records);

    int insertUpdateBatch(List<CustomerDepartmentMappingPo> records);

    int insert(CustomerDepartmentMappingPo record);

    int insertUpdateSelective(CustomerDepartmentMappingPo record);

    int insertSelective(CustomerDepartmentMappingPo record);

    List<CustomerDepartmentMappingPo> selectByExample(CustomerDepartmentMappingPoExample example);

    CustomerDepartmentMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerDepartmentMappingPo record, @Param("example") CustomerDepartmentMappingPoExample example);

    int updateByExample(@Param("record") CustomerDepartmentMappingPo record, @Param("example") CustomerDepartmentMappingPoExample example);

    int updateByPrimaryKeySelective(CustomerDepartmentMappingPo record);

    int updateByPrimaryKey(CustomerDepartmentMappingPo record);
}