package com.bilibili.crm.platform.biz.taishan;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.LancerMessage;
import com.bilibili.crm.common.LogEventService;
import com.bilibili.crm.platform.adx.biz.po.EsGetIdInterface;
import com.bilibili.crm.platform.adx.biz.service.EsToHiveInterface;
import lombok.Data;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;

@Component
public class EsToTaiShanToHive implements EsToHiveInterface {

    @Autowired
    private PaladinConfig paladinConfig;

    @Autowired
    private TaiShanService taiShanService;

    @Autowired
    private LogEventService logEventService;

    @Override
    public <T> void putLancer(String index, String action, T message) {
        try {
            if (paladinConfig.switchOn("write.taishan")) {
                putTaiShan(index, action, message);
            } else {
                CrmEsLancer msg = new CrmEsLancer();
                msg.setScene(index);
                msg.setAction(action);
                msg.setJson(JSONObject.toJSONString(message));

                logEventService.putLancer(msg);
            }
        } catch (Throwable throwable) {
            AlarmHelper.log("TakeTaiShan", "Err", index, action, message, throwable);
        }
    }

    @Data
    public static class CrmEsLancer implements LancerMessage {
        private String scene;

        private String json;

        private String action;

        @Override
        public String getLancerMsg() {
            return toHiveString(scene, json, action, System.currentTimeMillis());
        }

        @Override
        public String getLancerId() {
            return "027221";
        }
    }

    private <T> void putTaiShan(String index, String action, T message) {
        if (!paladinConfig.contains("es.index.to.lancer", index)) {
            AlarmHelper.log("EsNotToLancer", index);
            return;
        }
        TaiShanSceneEnum scene = TaiShanSceneEnum.esToTaiShan(index);
        if (scene == null) {
            AlarmHelper.log("TakeTaiShan", "TaiShanNotFound", index, action, message);
            return;
        }
        if (!(message instanceof EsGetIdInterface)) {
            AlarmHelper.log("TakeTaiShan", "PoErr", index, action, message);
            return;
        }
        EsGetIdInterface val = (EsGetIdInterface) message;
        if (Objects.equals(action, "insert") || Objects.equals(action, "JOB")) {
            taiShanService.put(scene, val.getId(), val);
        } else if (Objects.equals("delete", action)) {
            taiShanService.del(scene, val.getId(), val);
        } else {
            AlarmHelper.log("TakeTaiShan", "ActionErr", index, action, message);
        }
    }

    @Override
    public <T> int putLancer(String index, String action, List<T> message) {
        if (CollectionUtils.isEmpty(message)) {
            return 0;
        }
        for (T msg : message) {
            putLancer(index, action, msg);
        }
        return message.size();
    }
}
