package com.bilibili.crm.platform.biz.service.expenditure;

import com.bilibili.crm.platform.api.account.dto.BiliUserBaseInfoDto;
import com.bilibili.crm.platform.api.expenditure.dto.CrmBtpVirtualGoldAmountLogDto;
import com.bilibili.crm.platform.api.expenditure.service.ICrmBtpVirtualGoldAmountService;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmBtpVirtualGoldAmountDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmBtpVirtualGoldAmountLogDao;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPoExample;
import com.bilibili.crm.platform.biz.po.expenditure.*;
import com.bilibili.crm.platform.biz.service.account.bilibiliuser.BilibiliUserQuerier;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

@Component
@Slf4j
public class CrmBtpVirtualGoldAmountService implements ICrmBtpVirtualGoldAmountService {
    @Autowired
    private CrmBtpVirtualGoldAmountDao crmBtpVirtualGoldAmountDao;
    @Autowired
    private CrmBtpVirtualGoldAmountLogDao crmBtpVirtualGoldAmountLogDao;
    @Autowired
    private BilibiliUserQuerier bilibiliUserQuerier;

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void accumulate(CrmBtpVirtualGoldAmountLogDto crmBtpVirtualGoldAmountLogDto) {
        String btpProjectCode = crmBtpVirtualGoldAmountLogDto.getBtpProjectCode();
        int currencyType = crmBtpVirtualGoldAmountLogDto.getCurrencyType();
        Long mid = crmBtpVirtualGoldAmountLogDto.getMid();
        if (isDuplicated(crmBtpVirtualGoldAmountLogDto)) {
            log.info("duplicated btp virtual gold amount of: {}", crmBtpVirtualGoldAmountLogDto);
            return;
        }

        CrmBtpVirtualGoldAmountLogPo crmBtpVirtualGoldAmountLogPo = from(crmBtpVirtualGoldAmountLogDto);
        Optional<CrmBtpVirtualGoldAmountPo> crmBtpVirtualGoldAmountPoByBtpProjectCodeOptional = findCrmBtpVirtualGoldAmountPo(btpProjectCode, currencyType);
        Optional<CrmBtpVirtualGoldAmountPo> crmBtpVirtualGoldAmountPoByMidOptional = findCrmBtpVirtualGoldAmountPo(mid);

        if (crmBtpVirtualGoldAmountPoByBtpProjectCodeOptional.isPresent()) {
            // group count by btp project code + currency type
            updateCrmBtpVirtualGoldAmountByBtpProjectCode(crmBtpVirtualGoldAmountLogDto, crmBtpVirtualGoldAmountPoByBtpProjectCodeOptional.get());
        } else {
            // group count by btp project code + currency type
            CrmBtpVirtualGoldAmountPo crmBtpVirtualGoldAmountPo = initFrom(crmBtpVirtualGoldAmountLogDto);
            crmBtpVirtualGoldAmountPo.setBtpProjectCode(btpProjectCode);
            crmBtpVirtualGoldAmountPo.setType(currencyType);
            crmBtpVirtualGoldAmountDao.insertSelective(crmBtpVirtualGoldAmountPo);
        }

        if (crmBtpVirtualGoldAmountPoByMidOptional.isPresent()) {
            // group count by mid
            updateCrmBtpVirtualGoldAmountByMid(crmBtpVirtualGoldAmountLogDto, crmBtpVirtualGoldAmountPoByMidOptional.get());
        } else {
            // group count by mid
            CrmBtpVirtualGoldAmountPo crmBtpVirtualGoldAmountPo = initFrom(crmBtpVirtualGoldAmountLogDto);
            crmBtpVirtualGoldAmountPo.setMid(mid);
            crmBtpVirtualGoldAmountPo.setType(currencyType);
            crmBtpVirtualGoldAmountDao.insertSelective(crmBtpVirtualGoldAmountPo);
        }

        crmBtpVirtualGoldAmountLogDao.insertSelective(crmBtpVirtualGoldAmountLogPo);
    }

    private CrmBtpVirtualGoldAmountLogPo from(CrmBtpVirtualGoldAmountLogDto crmBtpVirtualGoldAmountLogDto) {
        Long mid = crmBtpVirtualGoldAmountLogDto.getMid();
        CrmBtpVirtualGoldAmountLogPo crmBtpVirtualGoldAmountLogPo = CrmBtpVirtualGoldAmountLogPo.builder()
                .btpProjectCode(crmBtpVirtualGoldAmountLogDto.getBtpProjectCode())
                .mid(mid)
                .nickName(getNickName(mid))
                .type(crmBtpVirtualGoldAmountLogDto.getCurrencyType())
                .build();

        CrmBtpVirtualGoldAmountLogDto.AmountType amountType = CrmBtpVirtualGoldAmountLogDto.AmountType.getByCode(crmBtpVirtualGoldAmountLogDto.getAmountType());
        Timestamp btpTime = new Timestamp(crmBtpVirtualGoldAmountLogDto.getTime().getTime());
        String orderCode = crmBtpVirtualGoldAmountLogDto.getOrderCode();
        Long amount = Long.valueOf(crmBtpVirtualGoldAmountLogDto.getAmount());

        switch (Objects.requireNonNull(amountType)) {
            case ACCRUED:
                crmBtpVirtualGoldAmountLogPo.setAccruedAmount(amount);
                crmBtpVirtualGoldAmountLogPo.setAccruedOrderCode(orderCode);
                crmBtpVirtualGoldAmountLogPo.setAccruedTime(btpTime);
                break;
            case PREPAID:
            case WRITTEN_OFF:
                crmBtpVirtualGoldAmountLogPo.setPrepaidAmount(0L);
                crmBtpVirtualGoldAmountLogPo.setPrepaidOrderCode(orderCode);
                crmBtpVirtualGoldAmountLogPo.setPrepaidTime(null);
                crmBtpVirtualGoldAmountLogPo.setWrittenOffAmount(amount);
                crmBtpVirtualGoldAmountLogPo.setWrittenOffOrderCode(orderCode);
                crmBtpVirtualGoldAmountLogPo.setWrittenOffTime(btpTime);
                break;
        }

        return crmBtpVirtualGoldAmountLogPo;
    }

    private CrmBtpVirtualGoldAmountPo initFrom(CrmBtpVirtualGoldAmountLogDto crmBtpVirtualGoldAmountLogDto) {
        CrmBtpVirtualGoldAmountPo crmBtpVirtualGoldAmountPo = new CrmBtpVirtualGoldAmountPo();
        CrmBtpVirtualGoldAmountLogDto.AmountType amountType = CrmBtpVirtualGoldAmountLogDto.AmountType.getByCode(crmBtpVirtualGoldAmountLogDto.getAmountType());
        Long amount = Long.valueOf(crmBtpVirtualGoldAmountLogDto.getAmount());
        Timestamp btpTime = new Timestamp(crmBtpVirtualGoldAmountLogDto.getTime().getTime());

        switch (Objects.requireNonNull(amountType)) {
            case ACCRUED:
                crmBtpVirtualGoldAmountPo.setAccruedAmount(amount);
                crmBtpVirtualGoldAmountPo.setAccruedTime(btpTime);
                break;
            case PREPAID:
            case WRITTEN_OFF:
                crmBtpVirtualGoldAmountPo.setPrepaidAmount(0L);
                crmBtpVirtualGoldAmountPo.setPrepaidTime(null);
                crmBtpVirtualGoldAmountPo.setWrittenOffAmount(amount);
                crmBtpVirtualGoldAmountPo.setWrittenOffTime(btpTime);
                break;
        }

        return crmBtpVirtualGoldAmountPo;
    }

    private Boolean isDuplicated(CrmBtpVirtualGoldAmountLogDto crmBtpVirtualGoldAmountLogDto) {
        String orderCode = crmBtpVirtualGoldAmountLogDto.getOrderCode();

        CrmBtpVirtualGoldAmountLogPoExample crmBtpVirtualGoldAmountLogPoExample = new CrmBtpVirtualGoldAmountLogPoExample();
        CrmBtpVirtualGoldAmountLogPoExample.Criteria criteria = crmBtpVirtualGoldAmountLogPoExample.createCriteria();
        CrmBtpVirtualGoldAmountLogDto.AmountType amountType = CrmBtpVirtualGoldAmountLogDto.AmountType.getByCode(crmBtpVirtualGoldAmountLogDto.getAmountType());

        switch (Objects.requireNonNull(amountType)) {
            case PREPAID:
                criteria.andPrepaidOrderCodeEqualTo(orderCode);
                break;
            case ACCRUED:
                criteria.andAccruedOrderCodeEqualTo(orderCode);
                break;
            case WRITTEN_OFF:
                criteria.andWrittenOffOrderCodeEqualTo(orderCode);
                break;
        }

        List<CrmBtpVirtualGoldAmountLogPo> crmBtpVirtualGoldAmountLogPoList = crmBtpVirtualGoldAmountLogDao.selectByExample(crmBtpVirtualGoldAmountLogPoExample);
        return !CollectionUtils.isEmpty(crmBtpVirtualGoldAmountLogPoList);
    }

    /**
     * 按照 btpProjectCode + currencyType 维度累加
     */
    private void updateCrmBtpVirtualGoldAmountByBtpProjectCode(CrmBtpVirtualGoldAmountLogDto crmBtpVirtualGoldAmountLogDto,
                                                               CrmBtpVirtualGoldAmountPo crmBtpVirtualGoldAmountPo) {

        CrmBtpVirtualGoldAmountLogDto.AmountType amountType = CrmBtpVirtualGoldAmountLogDto.AmountType.getByCode(crmBtpVirtualGoldAmountLogDto.getAmountType());
        Timestamp btpTime = new Timestamp(crmBtpVirtualGoldAmountLogDto.getTime().getTime());
        Long amount = Long.valueOf(crmBtpVirtualGoldAmountLogDto.getAmount());

        switch (Objects.requireNonNull(amountType)) {
            case PREPAID:
            case WRITTEN_OFF:
                crmBtpVirtualGoldAmountPo.setWrittenOffAmount(amount + crmBtpVirtualGoldAmountPo.getWrittenOffAmount());
                crmBtpVirtualGoldAmountPo.setWrittenOffTime(btpTime);
                break;
            case ACCRUED:
                crmBtpVirtualGoldAmountPo.setAccruedAmount(amount + crmBtpVirtualGoldAmountPo.getAccruedAmount());
                crmBtpVirtualGoldAmountPo.setAccruedTime(btpTime);
                break;
        }

        // set 0
        crmBtpVirtualGoldAmountPo.setMid(0L);
        crmBtpVirtualGoldAmountPo.setType(crmBtpVirtualGoldAmountLogDto.getCurrencyType());
        crmBtpVirtualGoldAmountDao.updateByPrimaryKeySelective(crmBtpVirtualGoldAmountPo);
    }

    /**
     * 按照mid维度累加
     */
    private void updateCrmBtpVirtualGoldAmountByMid(CrmBtpVirtualGoldAmountLogDto crmBtpVirtualGoldAmountLogDto,
                                                    CrmBtpVirtualGoldAmountPo crmBtpVirtualGoldAmountPo) {

        CrmBtpVirtualGoldAmountLogDto.AmountType amountType = CrmBtpVirtualGoldAmountLogDto.AmountType.getByCode(crmBtpVirtualGoldAmountLogDto.getAmountType());
        Timestamp btpTime = new Timestamp(crmBtpVirtualGoldAmountLogDto.getTime().getTime());
        Long amount = Long.valueOf(crmBtpVirtualGoldAmountLogDto.getAmount());

        switch (Objects.requireNonNull(amountType)) {
            case PREPAID:
            case WRITTEN_OFF:
                crmBtpVirtualGoldAmountPo.setWrittenOffAmount(amount + crmBtpVirtualGoldAmountPo.getWrittenOffAmount());
                crmBtpVirtualGoldAmountPo.setWrittenOffTime(btpTime);
                break;
            case ACCRUED:
                crmBtpVirtualGoldAmountPo.setAccruedAmount(amount + crmBtpVirtualGoldAmountPo.getAccruedAmount());
                crmBtpVirtualGoldAmountPo.setAccruedTime(btpTime);
                break;
        }

        // set ""
        crmBtpVirtualGoldAmountPo.setBtpProjectCode(StringUtils.EMPTY);
        crmBtpVirtualGoldAmountPo.setType(crmBtpVirtualGoldAmountLogDto.getCurrencyType());
        crmBtpVirtualGoldAmountDao.updateByPrimaryKeySelective(crmBtpVirtualGoldAmountPo);
    }

    private Optional<CrmBtpVirtualGoldAmountPo> findCrmBtpVirtualGoldAmountPo(String btpProjectCode, Integer currencyType) {
        CrmBtpVirtualGoldAmountPoExample crmBtpVirtualGoldAmountPoExample = new CrmBtpVirtualGoldAmountPoExample();
        CrmBtpVirtualGoldAmountPoExample.Criteria criteria = crmBtpVirtualGoldAmountPoExample.createCriteria();
        criteria.andBtpProjectCodeEqualTo(btpProjectCode).andTypeEqualTo(currencyType);
        List<CrmBtpVirtualGoldAmountPo> crmBtpVirtualGoldAmountPoList = crmBtpVirtualGoldAmountDao.selectByExample(crmBtpVirtualGoldAmountPoExample);
        if (CollectionUtils.isEmpty(crmBtpVirtualGoldAmountPoList)) {
            return Optional.empty();
        } else {
            return Optional.of(crmBtpVirtualGoldAmountPoList.get(0));
        }
    }

    private Optional<CrmBtpVirtualGoldAmountPo> findCrmBtpVirtualGoldAmountPo(Long mid) {
        CrmBtpVirtualGoldAmountPoExample crmBtpVirtualGoldAmountPoExample = new CrmBtpVirtualGoldAmountPoExample();
        CrmBtpVirtualGoldAmountPoExample.Criteria criteria = crmBtpVirtualGoldAmountPoExample.createCriteria();
        criteria.andMidEqualTo(mid);
        List<CrmBtpVirtualGoldAmountPo> crmBtpVirtualGoldAmountPoList = crmBtpVirtualGoldAmountDao.selectByExample(crmBtpVirtualGoldAmountPoExample);
        if (CollectionUtils.isEmpty(crmBtpVirtualGoldAmountPoList)) {
            return Optional.empty();
        } else {
            return Optional.of(crmBtpVirtualGoldAmountPoList.get(0));
        }
    }

    public String getNickName(Long mid) {
        if (mid == null) {
            return null;
        }

        BiliUserBaseInfoDto biliUserBaseInfoDto = bilibiliUserQuerier.getBiliUserBaseInfoByMid(mid);
        if (biliUserBaseInfoDto == null) {
            return null;
        }

        return biliUserBaseInfoDto.getNickName();
    }
}