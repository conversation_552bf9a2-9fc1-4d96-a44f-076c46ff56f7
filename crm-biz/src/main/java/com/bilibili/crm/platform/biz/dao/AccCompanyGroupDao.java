package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.AccCompanyGroupPo;
import com.bilibili.crm.platform.biz.po.AccCompanyGroupPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AccCompanyGroupDao {
    long countByExample(AccCompanyGroupPoExample example);

    int deleteByExample(AccCompanyGroupPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AccCompanyGroupPo record);

    int insertBatch(List<AccCompanyGroupPo> records);

    int insertUpdateBatch(List<AccCompanyGroupPo> records);

    int insert(AccCompanyGroupPo record);

    int insertUpdateSelective(AccCompanyGroupPo record);

    int insertSelective(AccCompanyGroupPo record);

    List<AccCompanyGroupPo> selectByExample(AccCompanyGroupPoExample example);

    AccCompanyGroupPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccCompanyGroupPo record, @Param("example") AccCompanyGroupPoExample example);

    int updateByExample(@Param("record") AccCompanyGroupPo record, @Param("example") AccCompanyGroupPoExample example);

    int updateByPrimaryKeySelective(AccCompanyGroupPo record);

    int updateByPrimaryKey(AccCompanyGroupPo record);
}