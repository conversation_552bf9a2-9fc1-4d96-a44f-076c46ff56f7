package com.bilibili.crm.platform.biz.statusmachine.return_money.validator;

import com.bilibili.crm.platform.api.statusmachine.return_money.validator.IReturnMoneyStateValidator;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

/**
 * 执行单状态校验器
 *
 * <AUTHOR>
 * @date 2022/8/26 16:53
 */
@Slf4j
@Component
public class ReturnMoneyStateValidator implements IReturnMoneyStateValidator {

}
