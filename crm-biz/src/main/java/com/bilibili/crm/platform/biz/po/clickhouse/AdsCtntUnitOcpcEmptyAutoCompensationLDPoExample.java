package com.bilibili.crm.platform.biz.po.clickhouse;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class AdsCtntUnitOcpcEmptyAutoCompensationLDPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public AdsCtntUnitOcpcEmptyAutoCompensationLDPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Long value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Long value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Long value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Long value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Long value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Long value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Long> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Long> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Long value1, Long value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Long value1, Long value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNull() {
            addCriterion("unit_id is null");
            return (Criteria) this;
        }

        public Criteria andUnitIdIsNotNull() {
            addCriterion("unit_id is not null");
            return (Criteria) this;
        }

        public Criteria andUnitIdEqualTo(String value) {
            addCriterion("unit_id =", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotEqualTo(String value) {
            addCriterion("unit_id <>", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThan(String value) {
            addCriterion("unit_id >", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdGreaterThanOrEqualTo(String value) {
            addCriterion("unit_id >=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThan(String value) {
            addCriterion("unit_id <", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLessThanOrEqualTo(String value) {
            addCriterion("unit_id <=", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdLike(String value) {
            addCriterion("unit_id like", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotLike(String value) {
            addCriterion("unit_id not like", value, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdIn(List<String> values) {
            addCriterion("unit_id in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotIn(List<String> values) {
            addCriterion("unit_id not in", values, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdBetween(String value1, String value2) {
            addCriterion("unit_id between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andUnitIdNotBetween(String value1, String value2) {
            addCriterion("unit_id not between", value1, value2, "unitId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(String value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(String value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(String value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(String value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(String value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(String value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLike(String value) {
            addCriterion("account_id like", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotLike(String value) {
            addCriterion("account_id not like", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<String> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<String> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(String value1, String value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(String value1, String value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountNameIsNull() {
            addCriterion("account_name is null");
            return (Criteria) this;
        }

        public Criteria andAccountNameIsNotNull() {
            addCriterion("account_name is not null");
            return (Criteria) this;
        }

        public Criteria andAccountNameEqualTo(String value) {
            addCriterion("account_name =", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameNotEqualTo(String value) {
            addCriterion("account_name <>", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameGreaterThan(String value) {
            addCriterion("account_name >", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameGreaterThanOrEqualTo(String value) {
            addCriterion("account_name >=", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameLessThan(String value) {
            addCriterion("account_name <", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameLessThanOrEqualTo(String value) {
            addCriterion("account_name <=", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameLike(String value) {
            addCriterion("account_name like", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameNotLike(String value) {
            addCriterion("account_name not like", value, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameIn(List<String> values) {
            addCriterion("account_name in", values, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameNotIn(List<String> values) {
            addCriterion("account_name not in", values, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameBetween(String value1, String value2) {
            addCriterion("account_name between", value1, value2, "accountName");
            return (Criteria) this;
        }

        public Criteria andAccountNameNotBetween(String value1, String value2) {
            addCriterion("account_name not between", value1, value2, "accountName");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNull() {
            addCriterion("product_name is null");
            return (Criteria) this;
        }

        public Criteria andProductNameIsNotNull() {
            addCriterion("product_name is not null");
            return (Criteria) this;
        }

        public Criteria andProductNameEqualTo(String value) {
            addCriterion("product_name =", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotEqualTo(String value) {
            addCriterion("product_name <>", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThan(String value) {
            addCriterion("product_name >", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameGreaterThanOrEqualTo(String value) {
            addCriterion("product_name >=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThan(String value) {
            addCriterion("product_name <", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLessThanOrEqualTo(String value) {
            addCriterion("product_name <=", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameLike(String value) {
            addCriterion("product_name like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotLike(String value) {
            addCriterion("product_name not like", value, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameIn(List<String> values) {
            addCriterion("product_name in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotIn(List<String> values) {
            addCriterion("product_name not in", values, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameBetween(String value1, String value2) {
            addCriterion("product_name between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andProductNameNotBetween(String value1, String value2) {
            addCriterion("product_name not between", value1, value2, "productName");
            return (Criteria) this;
        }

        public Criteria andAgentNameIsNull() {
            addCriterion("agent_name is null");
            return (Criteria) this;
        }

        public Criteria andAgentNameIsNotNull() {
            addCriterion("agent_name is not null");
            return (Criteria) this;
        }

        public Criteria andAgentNameEqualTo(String value) {
            addCriterion("agent_name =", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotEqualTo(String value) {
            addCriterion("agent_name <>", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThan(String value) {
            addCriterion("agent_name >", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameGreaterThanOrEqualTo(String value) {
            addCriterion("agent_name >=", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThan(String value) {
            addCriterion("agent_name <", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLessThanOrEqualTo(String value) {
            addCriterion("agent_name <=", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameLike(String value) {
            addCriterion("agent_name like", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotLike(String value) {
            addCriterion("agent_name not like", value, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameIn(List<String> values) {
            addCriterion("agent_name in", values, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotIn(List<String> values) {
            addCriterion("agent_name not in", values, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameBetween(String value1, String value2) {
            addCriterion("agent_name between", value1, value2, "agentName");
            return (Criteria) this;
        }

        public Criteria andAgentNameNotBetween(String value1, String value2) {
            addCriterion("agent_name not between", value1, value2, "agentName");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(String value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(String value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(String value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(String value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(String value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(String value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLike(String value) {
            addCriterion("create_date like", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotLike(String value) {
            addCriterion("create_date not like", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<String> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<String> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(String value1, String value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(String value1, String value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateIsNull() {
            addCriterion("first_show_date is null");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateIsNotNull() {
            addCriterion("first_show_date is not null");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateEqualTo(String value) {
            addCriterion("first_show_date =", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateNotEqualTo(String value) {
            addCriterion("first_show_date <>", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateGreaterThan(String value) {
            addCriterion("first_show_date >", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateGreaterThanOrEqualTo(String value) {
            addCriterion("first_show_date >=", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateLessThan(String value) {
            addCriterion("first_show_date <", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateLessThanOrEqualTo(String value) {
            addCriterion("first_show_date <=", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateLike(String value) {
            addCriterion("first_show_date like", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateNotLike(String value) {
            addCriterion("first_show_date not like", value, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateIn(List<String> values) {
            addCriterion("first_show_date in", values, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateNotIn(List<String> values) {
            addCriterion("first_show_date not in", values, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateBetween(String value1, String value2) {
            addCriterion("first_show_date between", value1, value2, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andFirstShowDateNotBetween(String value1, String value2) {
            addCriterion("first_show_date not between", value1, value2, "firstShowDate");
            return (Criteria) this;
        }

        public Criteria andUnitCostIsNull() {
            addCriterion("unit_cost is null");
            return (Criteria) this;
        }

        public Criteria andUnitCostIsNotNull() {
            addCriterion("unit_cost is not null");
            return (Criteria) this;
        }

        public Criteria andUnitCostEqualTo(Double value) {
            addCriterion("unit_cost =", value, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostNotEqualTo(Double value) {
            addCriterion("unit_cost <>", value, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostGreaterThan(Double value) {
            addCriterion("unit_cost >", value, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostGreaterThanOrEqualTo(Double value) {
            addCriterion("unit_cost >=", value, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostLessThan(Double value) {
            addCriterion("unit_cost <", value, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostLessThanOrEqualTo(Double value) {
            addCriterion("unit_cost <=", value, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostIn(List<Double> values) {
            addCriterion("unit_cost in", values, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostNotIn(List<Double> values) {
            addCriterion("unit_cost not in", values, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostBetween(Double value1, Double value2) {
            addCriterion("unit_cost between", value1, value2, "unitCost");
            return (Criteria) this;
        }

        public Criteria andUnitCostNotBetween(Double value1, Double value2) {
            addCriterion("unit_cost not between", value1, value2, "unitCost");
            return (Criteria) this;
        }

        public Criteria andCpaSetIsNull() {
            addCriterion("cpa_set is null");
            return (Criteria) this;
        }

        public Criteria andCpaSetIsNotNull() {
            addCriterion("cpa_set is not null");
            return (Criteria) this;
        }

        public Criteria andCpaSetEqualTo(Double value) {
            addCriterion("cpa_set =", value, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetNotEqualTo(Double value) {
            addCriterion("cpa_set <>", value, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetGreaterThan(Double value) {
            addCriterion("cpa_set >", value, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetGreaterThanOrEqualTo(Double value) {
            addCriterion("cpa_set >=", value, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetLessThan(Double value) {
            addCriterion("cpa_set <", value, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetLessThanOrEqualTo(Double value) {
            addCriterion("cpa_set <=", value, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetIn(List<Double> values) {
            addCriterion("cpa_set in", values, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetNotIn(List<Double> values) {
            addCriterion("cpa_set not in", values, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetBetween(Double value1, Double value2) {
            addCriterion("cpa_set between", value1, value2, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andCpaSetNotBetween(Double value1, Double value2) {
            addCriterion("cpa_set not between", value1, value2, "cpaSet");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumIsNull() {
            addCriterion("unit_conv_num is null");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumIsNotNull() {
            addCriterion("unit_conv_num is not null");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumEqualTo(Long value) {
            addCriterion("unit_conv_num =", value, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumNotEqualTo(Long value) {
            addCriterion("unit_conv_num <>", value, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumGreaterThan(Long value) {
            addCriterion("unit_conv_num >", value, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumGreaterThanOrEqualTo(Long value) {
            addCriterion("unit_conv_num >=", value, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumLessThan(Long value) {
            addCriterion("unit_conv_num <", value, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumLessThanOrEqualTo(Long value) {
            addCriterion("unit_conv_num <=", value, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumIn(List<Long> values) {
            addCriterion("unit_conv_num in", values, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumNotIn(List<Long> values) {
            addCriterion("unit_conv_num not in", values, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumBetween(Long value1, Long value2) {
            addCriterion("unit_conv_num between", value1, value2, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andUnitConvNumNotBetween(Long value1, Long value2) {
            addCriterion("unit_conv_num not between", value1, value2, "unitConvNum");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntIsNull() {
            addCriterion("change_bid_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntIsNotNull() {
            addCriterion("change_bid_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntEqualTo(Long value) {
            addCriterion("change_bid_cnt =", value, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntNotEqualTo(Long value) {
            addCriterion("change_bid_cnt <>", value, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntGreaterThan(Long value) {
            addCriterion("change_bid_cnt >", value, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntGreaterThanOrEqualTo(Long value) {
            addCriterion("change_bid_cnt >=", value, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntLessThan(Long value) {
            addCriterion("change_bid_cnt <", value, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntLessThanOrEqualTo(Long value) {
            addCriterion("change_bid_cnt <=", value, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntIn(List<Long> values) {
            addCriterion("change_bid_cnt in", values, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntNotIn(List<Long> values) {
            addCriterion("change_bid_cnt not in", values, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntBetween(Long value1, Long value2) {
            addCriterion("change_bid_cnt between", value1, value2, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChangeBidCntNotBetween(Long value1, Long value2) {
            addCriterion("change_bid_cnt not between", value1, value2, "changeBidCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntIsNull() {
            addCriterion("chage_target_cnt is null");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntIsNotNull() {
            addCriterion("chage_target_cnt is not null");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntEqualTo(Long value) {
            addCriterion("chage_target_cnt =", value, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntNotEqualTo(Long value) {
            addCriterion("chage_target_cnt <>", value, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntGreaterThan(Long value) {
            addCriterion("chage_target_cnt >", value, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntGreaterThanOrEqualTo(Long value) {
            addCriterion("chage_target_cnt >=", value, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntLessThan(Long value) {
            addCriterion("chage_target_cnt <", value, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntLessThanOrEqualTo(Long value) {
            addCriterion("chage_target_cnt <=", value, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntIn(List<Long> values) {
            addCriterion("chage_target_cnt in", values, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntNotIn(List<Long> values) {
            addCriterion("chage_target_cnt not in", values, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntBetween(Long value1, Long value2) {
            addCriterion("chage_target_cnt between", value1, value2, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andChageTargetCntNotBetween(Long value1, Long value2) {
            addCriterion("chage_target_cnt not between", value1, value2, "chageTargetCnt");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceIsNull() {
            addCriterion("is_confidence is null");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceIsNotNull() {
            addCriterion("is_confidence is not null");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceEqualTo(Integer value) {
            addCriterion("is_confidence =", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceNotEqualTo(Integer value) {
            addCriterion("is_confidence <>", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceGreaterThan(Integer value) {
            addCriterion("is_confidence >", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_confidence >=", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceLessThan(Integer value) {
            addCriterion("is_confidence <", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceLessThanOrEqualTo(Integer value) {
            addCriterion("is_confidence <=", value, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceIn(List<Integer> values) {
            addCriterion("is_confidence in", values, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceNotIn(List<Integer> values) {
            addCriterion("is_confidence not in", values, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceBetween(Integer value1, Integer value2) {
            addCriterion("is_confidence between", value1, value2, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andIsConfidenceNotBetween(Integer value1, Integer value2) {
            addCriterion("is_confidence not between", value1, value2, "isConfidence");
            return (Criteria) this;
        }

        public Criteria andCompensationIsNull() {
            addCriterion("compensation is null");
            return (Criteria) this;
        }

        public Criteria andCompensationIsNotNull() {
            addCriterion("compensation is not null");
            return (Criteria) this;
        }

        public Criteria andCompensationEqualTo(Double value) {
            addCriterion("compensation =", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationNotEqualTo(Double value) {
            addCriterion("compensation <>", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationGreaterThan(Double value) {
            addCriterion("compensation >", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationGreaterThanOrEqualTo(Double value) {
            addCriterion("compensation >=", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationLessThan(Double value) {
            addCriterion("compensation <", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationLessThanOrEqualTo(Double value) {
            addCriterion("compensation <=", value, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationIn(List<Double> values) {
            addCriterion("compensation in", values, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationNotIn(List<Double> values) {
            addCriterion("compensation not in", values, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationBetween(Double value1, Double value2) {
            addCriterion("compensation between", value1, value2, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensationNotBetween(Double value1, Double value2) {
            addCriterion("compensation not between", value1, value2, "compensation");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateIsNull() {
            addCriterion("compensate_execute_date is null");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateIsNotNull() {
            addCriterion("compensate_execute_date is not null");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateEqualTo(String value) {
            addCriterion("compensate_execute_date =", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateNotEqualTo(String value) {
            addCriterion("compensate_execute_date <>", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateGreaterThan(String value) {
            addCriterion("compensate_execute_date >", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateGreaterThanOrEqualTo(String value) {
            addCriterion("compensate_execute_date >=", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateLessThan(String value) {
            addCriterion("compensate_execute_date <", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateLessThanOrEqualTo(String value) {
            addCriterion("compensate_execute_date <=", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateLike(String value) {
            addCriterion("compensate_execute_date like", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateNotLike(String value) {
            addCriterion("compensate_execute_date not like", value, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateIn(List<String> values) {
            addCriterion("compensate_execute_date in", values, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateNotIn(List<String> values) {
            addCriterion("compensate_execute_date not in", values, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateBetween(String value1, String value2) {
            addCriterion("compensate_execute_date between", value1, value2, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andCompensateExecuteDateNotBetween(String value1, String value2) {
            addCriterion("compensate_execute_date not between", value1, value2, "compensateExecuteDate");
            return (Criteria) this;
        }

        public Criteria andLogDateIsNull() {
            addCriterion("log_date is null");
            return (Criteria) this;
        }

        public Criteria andLogDateIsNotNull() {
            addCriterion("log_date is not null");
            return (Criteria) this;
        }

        public Criteria andLogDateEqualTo(Timestamp value) {
            addCriterion("log_date =", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateNotEqualTo(Timestamp value) {
            addCriterion("log_date <>", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateGreaterThan(Timestamp value) {
            addCriterion("log_date >", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("log_date >=", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateLessThan(Timestamp value) {
            addCriterion("log_date <", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("log_date <=", value, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateIn(List<Timestamp> values) {
            addCriterion("log_date in", values, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateNotIn(List<Timestamp> values) {
            addCriterion("log_date not in", values, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("log_date between", value1, value2, "logDate");
            return (Criteria) this;
        }

        public Criteria andLogDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("log_date not between", value1, value2, "logDate");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}