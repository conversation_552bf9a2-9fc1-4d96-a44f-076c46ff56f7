package com.bilibili.crm.platform.biz.mq.handler;

import com.bilibili.crm.platform.biz.mq.message.MqMessage;
import lombok.extern.slf4j.Slf4j;

/**
 * <AUTHOR>
 * description：mq消息发送抽象类
 * date       ：2020/11/12 4:01 下午
 */
@Slf4j
@Deprecated
public abstract class AbstractMqMessageHandler<T extends MqMessage> {

    public abstract Boolean messageValidate(T msg);

    public abstract void convertAndSend(T msg);

    protected void sendMsgWithRetry(T msg, String exchangeName, String routingKey) {
        this.sendMsgWithRetry(msg, exchangeName, routingKey, 3);
    }

    protected void sendMsgWithRetry(T msg, String exchangeName, String routingKey, Integer retryCount) {
        boolean needRetry = false;
        Integer count = retryCount;
        Exception exp;
        do {
            try {
                log.info("try to send message with msg={}, exchangeName={}, routingKey={}, count={}", msg, exchangeName, routingKey, count);
                exp = null;
            } catch (Exception e) {
                needRetry = true;
                exp = e;
                log.warn("failed to send message with exception ", e);
            }
            count--;
        } while (needRetry && count > 0);
        if (exp != null) {
            throw new RuntimeException("send message failed with " + exp.getMessage());
        }
    }
}
