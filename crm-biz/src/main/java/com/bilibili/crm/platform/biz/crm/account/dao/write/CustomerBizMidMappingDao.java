package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CustomerBizMidMappingPo;
import com.bilibili.crm.platform.biz.po.CustomerBizMidMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CustomerBizMidMappingDao {
    long countByExample(CustomerBizMidMappingPoExample example);

    int deleteByExample(CustomerBizMidMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerBizMidMappingPo record);

    int insertBatch(List<CustomerBizMidMappingPo> records);

    int insertUpdateBatch(List<CustomerBizMidMappingPo> records);

    int insert(CustomerBizMidMappingPo record);

    int insertUpdateSelective(CustomerBizMidMappingPo record);

    int insertSelective(CustomerBizMidMappingPo record);

    List<CustomerBizMidMappingPo> selectByExample(CustomerBizMidMappingPoExample example);

    CustomerBizMidMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerBizMidMappingPo record, @Param("example") CustomerBizMidMappingPoExample example);

    int updateByExample(@Param("record") CustomerBizMidMappingPo record, @Param("example") CustomerBizMidMappingPoExample example);

    int updateByPrimaryKeySelective(CustomerBizMidMappingPo record);

    int updateByPrimaryKey(CustomerBizMidMappingPo record);
}