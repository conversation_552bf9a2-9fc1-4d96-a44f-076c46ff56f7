package com.bilibili.crm.platform.biz.po;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CrmPickupSettleNewPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CrmPickupSettleNewPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andObjIdIsNull() {
            addCriterion("obj_id is null");
            return (Criteria) this;
        }

        public Criteria andObjIdIsNotNull() {
            addCriterion("obj_id is not null");
            return (Criteria) this;
        }

        public Criteria andObjIdEqualTo(Long value) {
            addCriterion("obj_id =", value, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdNotEqualTo(Long value) {
            addCriterion("obj_id <>", value, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdGreaterThan(Long value) {
            addCriterion("obj_id >", value, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdGreaterThanOrEqualTo(Long value) {
            addCriterion("obj_id >=", value, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdLessThan(Long value) {
            addCriterion("obj_id <", value, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdLessThanOrEqualTo(Long value) {
            addCriterion("obj_id <=", value, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdIn(List<Long> values) {
            addCriterion("obj_id in", values, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdNotIn(List<Long> values) {
            addCriterion("obj_id not in", values, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdBetween(Long value1, Long value2) {
            addCriterion("obj_id between", value1, value2, "objId");
            return (Criteria) this;
        }

        public Criteria andObjIdNotBetween(Long value1, Long value2) {
            addCriterion("obj_id not between", value1, value2, "objId");
            return (Criteria) this;
        }

        public Criteria andObjTypeIsNull() {
            addCriterion("obj_type is null");
            return (Criteria) this;
        }

        public Criteria andObjTypeIsNotNull() {
            addCriterion("obj_type is not null");
            return (Criteria) this;
        }

        public Criteria andObjTypeEqualTo(Integer value) {
            addCriterion("obj_type =", value, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeNotEqualTo(Integer value) {
            addCriterion("obj_type <>", value, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeGreaterThan(Integer value) {
            addCriterion("obj_type >", value, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("obj_type >=", value, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeLessThan(Integer value) {
            addCriterion("obj_type <", value, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeLessThanOrEqualTo(Integer value) {
            addCriterion("obj_type <=", value, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeIn(List<Integer> values) {
            addCriterion("obj_type in", values, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeNotIn(List<Integer> values) {
            addCriterion("obj_type not in", values, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeBetween(Integer value1, Integer value2) {
            addCriterion("obj_type between", value1, value2, "objType");
            return (Criteria) this;
        }

        public Criteria andObjTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("obj_type not between", value1, value2, "objType");
            return (Criteria) this;
        }

        public Criteria andObjStatusIsNull() {
            addCriterion("obj_status is null");
            return (Criteria) this;
        }

        public Criteria andObjStatusIsNotNull() {
            addCriterion("obj_status is not null");
            return (Criteria) this;
        }

        public Criteria andObjStatusEqualTo(Integer value) {
            addCriterion("obj_status =", value, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusNotEqualTo(Integer value) {
            addCriterion("obj_status <>", value, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusGreaterThan(Integer value) {
            addCriterion("obj_status >", value, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("obj_status >=", value, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusLessThan(Integer value) {
            addCriterion("obj_status <", value, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusLessThanOrEqualTo(Integer value) {
            addCriterion("obj_status <=", value, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusIn(List<Integer> values) {
            addCriterion("obj_status in", values, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusNotIn(List<Integer> values) {
            addCriterion("obj_status not in", values, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusBetween(Integer value1, Integer value2) {
            addCriterion("obj_status between", value1, value2, "objStatus");
            return (Criteria) this;
        }

        public Criteria andObjStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("obj_status not between", value1, value2, "objStatus");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleIsNull() {
            addCriterion("creative_title is null");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleIsNotNull() {
            addCriterion("creative_title is not null");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleEqualTo(String value) {
            addCriterion("creative_title =", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleNotEqualTo(String value) {
            addCriterion("creative_title <>", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleGreaterThan(String value) {
            addCriterion("creative_title >", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleGreaterThanOrEqualTo(String value) {
            addCriterion("creative_title >=", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleLessThan(String value) {
            addCriterion("creative_title <", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleLessThanOrEqualTo(String value) {
            addCriterion("creative_title <=", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleLike(String value) {
            addCriterion("creative_title like", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleNotLike(String value) {
            addCriterion("creative_title not like", value, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleIn(List<String> values) {
            addCriterion("creative_title in", values, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleNotIn(List<String> values) {
            addCriterion("creative_title not in", values, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleBetween(String value1, String value2) {
            addCriterion("creative_title between", value1, value2, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andCreativeTitleNotBetween(String value1, String value2) {
            addCriterion("creative_title not between", value1, value2, "creativeTitle");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeIsNull() {
            addCriterion("pickup_order_type is null");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeIsNotNull() {
            addCriterion("pickup_order_type is not null");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeEqualTo(Integer value) {
            addCriterion("pickup_order_type =", value, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeNotEqualTo(Integer value) {
            addCriterion("pickup_order_type <>", value, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeGreaterThan(Integer value) {
            addCriterion("pickup_order_type >", value, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("pickup_order_type >=", value, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeLessThan(Integer value) {
            addCriterion("pickup_order_type <", value, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeLessThanOrEqualTo(Integer value) {
            addCriterion("pickup_order_type <=", value, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeIn(List<Integer> values) {
            addCriterion("pickup_order_type in", values, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeNotIn(List<Integer> values) {
            addCriterion("pickup_order_type not in", values, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeBetween(Integer value1, Integer value2) {
            addCriterion("pickup_order_type between", value1, value2, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andPickupOrderTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("pickup_order_type not between", value1, value2, "pickupOrderType");
            return (Criteria) this;
        }

        public Criteria andMidIsNull() {
            addCriterion("mid is null");
            return (Criteria) this;
        }

        public Criteria andMidIsNotNull() {
            addCriterion("mid is not null");
            return (Criteria) this;
        }

        public Criteria andMidEqualTo(Long value) {
            addCriterion("mid =", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotEqualTo(Long value) {
            addCriterion("mid <>", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidGreaterThan(Long value) {
            addCriterion("mid >", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidGreaterThanOrEqualTo(Long value) {
            addCriterion("mid >=", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLessThan(Long value) {
            addCriterion("mid <", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidLessThanOrEqualTo(Long value) {
            addCriterion("mid <=", value, "mid");
            return (Criteria) this;
        }

        public Criteria andMidIn(List<Long> values) {
            addCriterion("mid in", values, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotIn(List<Long> values) {
            addCriterion("mid not in", values, "mid");
            return (Criteria) this;
        }

        public Criteria andMidBetween(Long value1, Long value2) {
            addCriterion("mid between", value1, value2, "mid");
            return (Criteria) this;
        }

        public Criteria andMidNotBetween(Long value1, Long value2) {
            addCriterion("mid not between", value1, value2, "mid");
            return (Criteria) this;
        }

        public Criteria andAgentIdIsNull() {
            addCriterion("agent_id is null");
            return (Criteria) this;
        }

        public Criteria andAgentIdIsNotNull() {
            addCriterion("agent_id is not null");
            return (Criteria) this;
        }

        public Criteria andAgentIdEqualTo(Integer value) {
            addCriterion("agent_id =", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotEqualTo(Integer value) {
            addCriterion("agent_id <>", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdGreaterThan(Integer value) {
            addCriterion("agent_id >", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("agent_id >=", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdLessThan(Integer value) {
            addCriterion("agent_id <", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdLessThanOrEqualTo(Integer value) {
            addCriterion("agent_id <=", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdIn(List<Integer> values) {
            addCriterion("agent_id in", values, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotIn(List<Integer> values) {
            addCriterion("agent_id not in", values, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdBetween(Integer value1, Integer value2) {
            addCriterion("agent_id between", value1, value2, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("agent_id not between", value1, value2, "agentId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andMcnIdIsNull() {
            addCriterion("mcn_id is null");
            return (Criteria) this;
        }

        public Criteria andMcnIdIsNotNull() {
            addCriterion("mcn_id is not null");
            return (Criteria) this;
        }

        public Criteria andMcnIdEqualTo(Long value) {
            addCriterion("mcn_id =", value, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdNotEqualTo(Long value) {
            addCriterion("mcn_id <>", value, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdGreaterThan(Long value) {
            addCriterion("mcn_id >", value, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdGreaterThanOrEqualTo(Long value) {
            addCriterion("mcn_id >=", value, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdLessThan(Long value) {
            addCriterion("mcn_id <", value, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdLessThanOrEqualTo(Long value) {
            addCriterion("mcn_id <=", value, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdIn(List<Long> values) {
            addCriterion("mcn_id in", values, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdNotIn(List<Long> values) {
            addCriterion("mcn_id not in", values, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdBetween(Long value1, Long value2) {
            addCriterion("mcn_id between", value1, value2, "mcnId");
            return (Criteria) this;
        }

        public Criteria andMcnIdNotBetween(Long value1, Long value2) {
            addCriterion("mcn_id not between", value1, value2, "mcnId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdIsNull() {
            addCriterion("service_provider_id is null");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdIsNotNull() {
            addCriterion("service_provider_id is not null");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdEqualTo(Integer value) {
            addCriterion("service_provider_id =", value, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdNotEqualTo(Integer value) {
            addCriterion("service_provider_id <>", value, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdGreaterThan(Integer value) {
            addCriterion("service_provider_id >", value, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("service_provider_id >=", value, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdLessThan(Integer value) {
            addCriterion("service_provider_id <", value, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdLessThanOrEqualTo(Integer value) {
            addCriterion("service_provider_id <=", value, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdIn(List<Integer> values) {
            addCriterion("service_provider_id in", values, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdNotIn(List<Integer> values) {
            addCriterion("service_provider_id not in", values, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdBetween(Integer value1, Integer value2) {
            addCriterion("service_provider_id between", value1, value2, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andServiceProviderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("service_provider_id not between", value1, value2, "serviceProviderId");
            return (Criteria) this;
        }

        public Criteria andSettleTypeIsNull() {
            addCriterion("settle_type is null");
            return (Criteria) this;
        }

        public Criteria andSettleTypeIsNotNull() {
            addCriterion("settle_type is not null");
            return (Criteria) this;
        }

        public Criteria andSettleTypeEqualTo(Integer value) {
            addCriterion("settle_type =", value, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeNotEqualTo(Integer value) {
            addCriterion("settle_type <>", value, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeGreaterThan(Integer value) {
            addCriterion("settle_type >", value, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("settle_type >=", value, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeLessThan(Integer value) {
            addCriterion("settle_type <", value, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("settle_type <=", value, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeIn(List<Integer> values) {
            addCriterion("settle_type in", values, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeNotIn(List<Integer> values) {
            addCriterion("settle_type not in", values, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeBetween(Integer value1, Integer value2) {
            addCriterion("settle_type between", value1, value2, "settleType");
            return (Criteria) this;
        }

        public Criteria andSettleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("settle_type not between", value1, value2, "settleType");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmIsNull() {
            addCriterion("payment_confirm is null");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmIsNotNull() {
            addCriterion("payment_confirm is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmEqualTo(Integer value) {
            addCriterion("payment_confirm =", value, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmNotEqualTo(Integer value) {
            addCriterion("payment_confirm <>", value, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmGreaterThan(Integer value) {
            addCriterion("payment_confirm >", value, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_confirm >=", value, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmLessThan(Integer value) {
            addCriterion("payment_confirm <", value, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmLessThanOrEqualTo(Integer value) {
            addCriterion("payment_confirm <=", value, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmIn(List<Integer> values) {
            addCriterion("payment_confirm in", values, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmNotIn(List<Integer> values) {
            addCriterion("payment_confirm not in", values, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmBetween(Integer value1, Integer value2) {
            addCriterion("payment_confirm between", value1, value2, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andPaymentConfirmNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_confirm not between", value1, value2, "paymentConfirm");
            return (Criteria) this;
        }

        public Criteria andCompleteDateIsNull() {
            addCriterion("complete_date is null");
            return (Criteria) this;
        }

        public Criteria andCompleteDateIsNotNull() {
            addCriterion("complete_date is not null");
            return (Criteria) this;
        }

        public Criteria andCompleteDateEqualTo(Timestamp value) {
            addCriterion("complete_date =", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateNotEqualTo(Timestamp value) {
            addCriterion("complete_date <>", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateGreaterThan(Timestamp value) {
            addCriterion("complete_date >", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("complete_date >=", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateLessThan(Timestamp value) {
            addCriterion("complete_date <", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("complete_date <=", value, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateIn(List<Timestamp> values) {
            addCriterion("complete_date in", values, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateNotIn(List<Timestamp> values) {
            addCriterion("complete_date not in", values, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("complete_date between", value1, value2, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCompleteDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("complete_date not between", value1, value2, "completeDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateIsNull() {
            addCriterion("checking_date is null");
            return (Criteria) this;
        }

        public Criteria andCheckingDateIsNotNull() {
            addCriterion("checking_date is not null");
            return (Criteria) this;
        }

        public Criteria andCheckingDateEqualTo(Timestamp value) {
            addCriterion("checking_date =", value, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateNotEqualTo(Timestamp value) {
            addCriterion("checking_date <>", value, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateGreaterThan(Timestamp value) {
            addCriterion("checking_date >", value, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("checking_date >=", value, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateLessThan(Timestamp value) {
            addCriterion("checking_date <", value, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("checking_date <=", value, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateIn(List<Timestamp> values) {
            addCriterion("checking_date in", values, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateNotIn(List<Timestamp> values) {
            addCriterion("checking_date not in", values, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("checking_date between", value1, value2, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andCheckingDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("checking_date not between", value1, value2, "checkingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateIsNull() {
            addCriterion("closing_date is null");
            return (Criteria) this;
        }

        public Criteria andClosingDateIsNotNull() {
            addCriterion("closing_date is not null");
            return (Criteria) this;
        }

        public Criteria andClosingDateEqualTo(Timestamp value) {
            addCriterion("closing_date =", value, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateNotEqualTo(Timestamp value) {
            addCriterion("closing_date <>", value, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateGreaterThan(Timestamp value) {
            addCriterion("closing_date >", value, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("closing_date >=", value, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateLessThan(Timestamp value) {
            addCriterion("closing_date <", value, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("closing_date <=", value, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateIn(List<Timestamp> values) {
            addCriterion("closing_date in", values, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateNotIn(List<Timestamp> values) {
            addCriterion("closing_date not in", values, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("closing_date between", value1, value2, "closingDate");
            return (Criteria) this;
        }

        public Criteria andClosingDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("closing_date not between", value1, value2, "closingDate");
            return (Criteria) this;
        }

        public Criteria andIsCheckingIsNull() {
            addCriterion("is_checking is null");
            return (Criteria) this;
        }

        public Criteria andIsCheckingIsNotNull() {
            addCriterion("is_checking is not null");
            return (Criteria) this;
        }

        public Criteria andIsCheckingEqualTo(Integer value) {
            addCriterion("is_checking =", value, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingNotEqualTo(Integer value) {
            addCriterion("is_checking <>", value, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingGreaterThan(Integer value) {
            addCriterion("is_checking >", value, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_checking >=", value, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingLessThan(Integer value) {
            addCriterion("is_checking <", value, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingLessThanOrEqualTo(Integer value) {
            addCriterion("is_checking <=", value, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingIn(List<Integer> values) {
            addCriterion("is_checking in", values, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingNotIn(List<Integer> values) {
            addCriterion("is_checking not in", values, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingBetween(Integer value1, Integer value2) {
            addCriterion("is_checking between", value1, value2, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsCheckingNotBetween(Integer value1, Integer value2) {
            addCriterion("is_checking not between", value1, value2, "isChecking");
            return (Criteria) this;
        }

        public Criteria andIsClosingIsNull() {
            addCriterion("is_closing is null");
            return (Criteria) this;
        }

        public Criteria andIsClosingIsNotNull() {
            addCriterion("is_closing is not null");
            return (Criteria) this;
        }

        public Criteria andIsClosingEqualTo(Integer value) {
            addCriterion("is_closing =", value, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingNotEqualTo(Integer value) {
            addCriterion("is_closing <>", value, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingGreaterThan(Integer value) {
            addCriterion("is_closing >", value, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_closing >=", value, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingLessThan(Integer value) {
            addCriterion("is_closing <", value, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingLessThanOrEqualTo(Integer value) {
            addCriterion("is_closing <=", value, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingIn(List<Integer> values) {
            addCriterion("is_closing in", values, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingNotIn(List<Integer> values) {
            addCriterion("is_closing not in", values, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingBetween(Integer value1, Integer value2) {
            addCriterion("is_closing between", value1, value2, "isClosing");
            return (Criteria) this;
        }

        public Criteria andIsClosingNotBetween(Integer value1, Integer value2) {
            addCriterion("is_closing not between", value1, value2, "isClosing");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(Integer value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(Integer value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(Integer value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(Integer value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(Integer value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(Integer value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<Integer> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<Integer> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(Integer value1, Integer value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(Integer value1, Integer value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoIsNull() {
            addCriterion("pickup_order_no is null");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoIsNotNull() {
            addCriterion("pickup_order_no is not null");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoEqualTo(Long value) {
            addCriterion("pickup_order_no =", value, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoNotEqualTo(Long value) {
            addCriterion("pickup_order_no <>", value, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoGreaterThan(Long value) {
            addCriterion("pickup_order_no >", value, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoGreaterThanOrEqualTo(Long value) {
            addCriterion("pickup_order_no >=", value, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoLessThan(Long value) {
            addCriterion("pickup_order_no <", value, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoLessThanOrEqualTo(Long value) {
            addCriterion("pickup_order_no <=", value, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoIn(List<Long> values) {
            addCriterion("pickup_order_no in", values, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoNotIn(List<Long> values) {
            addCriterion("pickup_order_no not in", values, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoBetween(Long value1, Long value2) {
            addCriterion("pickup_order_no between", value1, value2, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andPickupOrderNoNotBetween(Long value1, Long value2) {
            addCriterion("pickup_order_no not between", value1, value2, "pickupOrderNo");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Integer value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Integer value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Integer value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Integer value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Integer value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Integer> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Integer> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Integer value1, Integer value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdIsNull() {
            addCriterion("crm_order_id is null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdIsNotNull() {
            addCriterion("crm_order_id is not null");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdEqualTo(Integer value) {
            addCriterion("crm_order_id =", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdNotEqualTo(Integer value) {
            addCriterion("crm_order_id <>", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdGreaterThan(Integer value) {
            addCriterion("crm_order_id >", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_order_id >=", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdLessThan(Integer value) {
            addCriterion("crm_order_id <", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdLessThanOrEqualTo(Integer value) {
            addCriterion("crm_order_id <=", value, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdIn(List<Integer> values) {
            addCriterion("crm_order_id in", values, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdNotIn(List<Integer> values) {
            addCriterion("crm_order_id not in", values, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_id between", value1, value2, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCrmOrderIdNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_order_id not between", value1, value2, "crmOrderId");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeIsNull() {
            addCriterion("cooperation_type is null");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeIsNotNull() {
            addCriterion("cooperation_type is not null");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeEqualTo(Integer value) {
            addCriterion("cooperation_type =", value, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeNotEqualTo(Integer value) {
            addCriterion("cooperation_type <>", value, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeGreaterThan(Integer value) {
            addCriterion("cooperation_type >", value, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("cooperation_type >=", value, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeLessThan(Integer value) {
            addCriterion("cooperation_type <", value, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeLessThanOrEqualTo(Integer value) {
            addCriterion("cooperation_type <=", value, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeIn(List<Integer> values) {
            addCriterion("cooperation_type in", values, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeNotIn(List<Integer> values) {
            addCriterion("cooperation_type not in", values, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeBetween(Integer value1, Integer value2) {
            addCriterion("cooperation_type between", value1, value2, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCooperationTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("cooperation_type not between", value1, value2, "cooperationType");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNull() {
            addCriterion("create_date is null");
            return (Criteria) this;
        }

        public Criteria andCreateDateIsNotNull() {
            addCriterion("create_date is not null");
            return (Criteria) this;
        }

        public Criteria andCreateDateEqualTo(Timestamp value) {
            addCriterion("create_date =", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotEqualTo(Timestamp value) {
            addCriterion("create_date <>", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThan(Timestamp value) {
            addCriterion("create_date >", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("create_date >=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThan(Timestamp value) {
            addCriterion("create_date <", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("create_date <=", value, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateIn(List<Timestamp> values) {
            addCriterion("create_date in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotIn(List<Timestamp> values) {
            addCriterion("create_date not in", values, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andCreateDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("create_date not between", value1, value2, "createDate");
            return (Criteria) this;
        }

        public Criteria andSaleIdIsNull() {
            addCriterion("sale_id is null");
            return (Criteria) this;
        }

        public Criteria andSaleIdIsNotNull() {
            addCriterion("sale_id is not null");
            return (Criteria) this;
        }

        public Criteria andSaleIdEqualTo(Integer value) {
            addCriterion("sale_id =", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotEqualTo(Integer value) {
            addCriterion("sale_id <>", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdGreaterThan(Integer value) {
            addCriterion("sale_id >", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("sale_id >=", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLessThan(Integer value) {
            addCriterion("sale_id <", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdLessThanOrEqualTo(Integer value) {
            addCriterion("sale_id <=", value, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdIn(List<Integer> values) {
            addCriterion("sale_id in", values, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotIn(List<Integer> values) {
            addCriterion("sale_id not in", values, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdBetween(Integer value1, Integer value2) {
            addCriterion("sale_id between", value1, value2, "saleId");
            return (Criteria) this;
        }

        public Criteria andSaleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("sale_id not between", value1, value2, "saleId");
            return (Criteria) this;
        }

        public Criteria andUpperTypeIsNull() {
            addCriterion("upper_type is null");
            return (Criteria) this;
        }

        public Criteria andUpperTypeIsNotNull() {
            addCriterion("upper_type is not null");
            return (Criteria) this;
        }

        public Criteria andUpperTypeEqualTo(Integer value) {
            addCriterion("upper_type =", value, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeNotEqualTo(Integer value) {
            addCriterion("upper_type <>", value, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeGreaterThan(Integer value) {
            addCriterion("upper_type >", value, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("upper_type >=", value, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeLessThan(Integer value) {
            addCriterion("upper_type <", value, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeLessThanOrEqualTo(Integer value) {
            addCriterion("upper_type <=", value, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeIn(List<Integer> values) {
            addCriterion("upper_type in", values, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeNotIn(List<Integer> values) {
            addCriterion("upper_type not in", values, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeBetween(Integer value1, Integer value2) {
            addCriterion("upper_type between", value1, value2, "upperType");
            return (Criteria) this;
        }

        public Criteria andUpperTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("upper_type not between", value1, value2, "upperType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeIsNull() {
            addCriterion("huahuo_settle_type is null");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeIsNotNull() {
            addCriterion("huahuo_settle_type is not null");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeEqualTo(Integer value) {
            addCriterion("huahuo_settle_type =", value, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeNotEqualTo(Integer value) {
            addCriterion("huahuo_settle_type <>", value, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeGreaterThan(Integer value) {
            addCriterion("huahuo_settle_type >", value, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("huahuo_settle_type >=", value, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeLessThan(Integer value) {
            addCriterion("huahuo_settle_type <", value, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeLessThanOrEqualTo(Integer value) {
            addCriterion("huahuo_settle_type <=", value, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeIn(List<Integer> values) {
            addCriterion("huahuo_settle_type in", values, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeNotIn(List<Integer> values) {
            addCriterion("huahuo_settle_type not in", values, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeBetween(Integer value1, Integer value2) {
            addCriterion("huahuo_settle_type between", value1, value2, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andHuahuoSettleTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("huahuo_settle_type not between", value1, value2, "huahuoSettleType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeIsNull() {
            addCriterion("settle_body_type is null");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeIsNotNull() {
            addCriterion("settle_body_type is not null");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeEqualTo(Integer value) {
            addCriterion("settle_body_type =", value, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeNotEqualTo(Integer value) {
            addCriterion("settle_body_type <>", value, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeGreaterThan(Integer value) {
            addCriterion("settle_body_type >", value, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("settle_body_type >=", value, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeLessThan(Integer value) {
            addCriterion("settle_body_type <", value, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeLessThanOrEqualTo(Integer value) {
            addCriterion("settle_body_type <=", value, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeIn(List<Integer> values) {
            addCriterion("settle_body_type in", values, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeNotIn(List<Integer> values) {
            addCriterion("settle_body_type not in", values, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeBetween(Integer value1, Integer value2) {
            addCriterion("settle_body_type between", value1, value2, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andSettleBodyTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("settle_body_type not between", value1, value2, "settleBodyType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeIsNull() {
            addCriterion("order_account_type is null");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeIsNotNull() {
            addCriterion("order_account_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeEqualTo(Integer value) {
            addCriterion("order_account_type =", value, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeNotEqualTo(Integer value) {
            addCriterion("order_account_type <>", value, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeGreaterThan(Integer value) {
            addCriterion("order_account_type >", value, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("order_account_type >=", value, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeLessThan(Integer value) {
            addCriterion("order_account_type <", value, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeLessThanOrEqualTo(Integer value) {
            addCriterion("order_account_type <=", value, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeIn(List<Integer> values) {
            addCriterion("order_account_type in", values, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeNotIn(List<Integer> values) {
            addCriterion("order_account_type not in", values, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeBetween(Integer value1, Integer value2) {
            addCriterion("order_account_type between", value1, value2, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andOrderAccountTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("order_account_type not between", value1, value2, "orderAccountType");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkIsNull() {
            addCriterion("sale_remark is null");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkIsNotNull() {
            addCriterion("sale_remark is not null");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkEqualTo(String value) {
            addCriterion("sale_remark =", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkNotEqualTo(String value) {
            addCriterion("sale_remark <>", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkGreaterThan(String value) {
            addCriterion("sale_remark >", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("sale_remark >=", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkLessThan(String value) {
            addCriterion("sale_remark <", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkLessThanOrEqualTo(String value) {
            addCriterion("sale_remark <=", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkLike(String value) {
            addCriterion("sale_remark like", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkNotLike(String value) {
            addCriterion("sale_remark not like", value, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkIn(List<String> values) {
            addCriterion("sale_remark in", values, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkNotIn(List<String> values) {
            addCriterion("sale_remark not in", values, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkBetween(String value1, String value2) {
            addCriterion("sale_remark between", value1, value2, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andSaleRemarkNotBetween(String value1, String value2) {
            addCriterion("sale_remark not between", value1, value2, "saleRemark");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdIsNull() {
            addCriterion("channel_sale_id is null");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdIsNotNull() {
            addCriterion("channel_sale_id is not null");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdEqualTo(Integer value) {
            addCriterion("channel_sale_id =", value, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdNotEqualTo(Integer value) {
            addCriterion("channel_sale_id <>", value, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdGreaterThan(Integer value) {
            addCriterion("channel_sale_id >", value, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("channel_sale_id >=", value, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdLessThan(Integer value) {
            addCriterion("channel_sale_id <", value, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdLessThanOrEqualTo(Integer value) {
            addCriterion("channel_sale_id <=", value, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdIn(List<Integer> values) {
            addCriterion("channel_sale_id in", values, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdNotIn(List<Integer> values) {
            addCriterion("channel_sale_id not in", values, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdBetween(Integer value1, Integer value2) {
            addCriterion("channel_sale_id between", value1, value2, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("channel_sale_id not between", value1, value2, "channelSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdIsNull() {
            addCriterion("direct_sale_id is null");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdIsNotNull() {
            addCriterion("direct_sale_id is not null");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdEqualTo(Integer value) {
            addCriterion("direct_sale_id =", value, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdNotEqualTo(Integer value) {
            addCriterion("direct_sale_id <>", value, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdGreaterThan(Integer value) {
            addCriterion("direct_sale_id >", value, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("direct_sale_id >=", value, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdLessThan(Integer value) {
            addCriterion("direct_sale_id <", value, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdLessThanOrEqualTo(Integer value) {
            addCriterion("direct_sale_id <=", value, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdIn(List<Integer> values) {
            addCriterion("direct_sale_id in", values, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdNotIn(List<Integer> values) {
            addCriterion("direct_sale_id not in", values, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdBetween(Integer value1, Integer value2) {
            addCriterion("direct_sale_id between", value1, value2, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdNotBetween(Integer value1, Integer value2) {
            addCriterion("direct_sale_id not between", value1, value2, "directSaleId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdIsNull() {
            addCriterion("check_subject_id is null");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdIsNotNull() {
            addCriterion("check_subject_id is not null");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdEqualTo(Integer value) {
            addCriterion("check_subject_id =", value, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdNotEqualTo(Integer value) {
            addCriterion("check_subject_id <>", value, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdGreaterThan(Integer value) {
            addCriterion("check_subject_id >", value, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("check_subject_id >=", value, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdLessThan(Integer value) {
            addCriterion("check_subject_id <", value, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdLessThanOrEqualTo(Integer value) {
            addCriterion("check_subject_id <=", value, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdIn(List<Integer> values) {
            addCriterion("check_subject_id in", values, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdNotIn(List<Integer> values) {
            addCriterion("check_subject_id not in", values, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdBetween(Integer value1, Integer value2) {
            addCriterion("check_subject_id between", value1, value2, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andCheckSubjectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("check_subject_id not between", value1, value2, "checkSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdIsNull() {
            addCriterion("payment_subject_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdIsNotNull() {
            addCriterion("payment_subject_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdEqualTo(Integer value) {
            addCriterion("payment_subject_id =", value, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdNotEqualTo(Integer value) {
            addCriterion("payment_subject_id <>", value, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdGreaterThan(Integer value) {
            addCriterion("payment_subject_id >", value, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_subject_id >=", value, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdLessThan(Integer value) {
            addCriterion("payment_subject_id <", value, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdLessThanOrEqualTo(Integer value) {
            addCriterion("payment_subject_id <=", value, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdIn(List<Integer> values) {
            addCriterion("payment_subject_id in", values, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdNotIn(List<Integer> values) {
            addCriterion("payment_subject_id not in", values, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdBetween(Integer value1, Integer value2) {
            addCriterion("payment_subject_id between", value1, value2, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentSubjectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_subject_id not between", value1, value2, "paymentSubjectId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdIsNull() {
            addCriterion("payment_type_id is null");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdIsNotNull() {
            addCriterion("payment_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdEqualTo(Integer value) {
            addCriterion("payment_type_id =", value, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdNotEqualTo(Integer value) {
            addCriterion("payment_type_id <>", value, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdGreaterThan(Integer value) {
            addCriterion("payment_type_id >", value, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("payment_type_id >=", value, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdLessThan(Integer value) {
            addCriterion("payment_type_id <", value, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdLessThanOrEqualTo(Integer value) {
            addCriterion("payment_type_id <=", value, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdIn(List<Integer> values) {
            addCriterion("payment_type_id in", values, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdNotIn(List<Integer> values) {
            addCriterion("payment_type_id not in", values, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdBetween(Integer value1, Integer value2) {
            addCriterion("payment_type_id between", value1, value2, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andPaymentTypeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("payment_type_id not between", value1, value2, "paymentTypeId");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeIsNull() {
            addCriterion("online_time is null");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeIsNotNull() {
            addCriterion("online_time is not null");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeEqualTo(Timestamp value) {
            addCriterion("online_time =", value, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeNotEqualTo(Timestamp value) {
            addCriterion("online_time <>", value, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeGreaterThan(Timestamp value) {
            addCriterion("online_time >", value, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("online_time >=", value, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeLessThan(Timestamp value) {
            addCriterion("online_time <", value, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("online_time <=", value, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeIn(List<Timestamp> values) {
            addCriterion("online_time in", values, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeNotIn(List<Timestamp> values) {
            addCriterion("online_time not in", values, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("online_time between", value1, value2, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andOnlineTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("online_time not between", value1, value2, "onlineTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNull() {
            addCriterion("pay_time is null");
            return (Criteria) this;
        }

        public Criteria andPayTimeIsNotNull() {
            addCriterion("pay_time is not null");
            return (Criteria) this;
        }

        public Criteria andPayTimeEqualTo(Timestamp value) {
            addCriterion("pay_time =", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotEqualTo(Timestamp value) {
            addCriterion("pay_time <>", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThan(Timestamp value) {
            addCriterion("pay_time >", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("pay_time >=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThan(Timestamp value) {
            addCriterion("pay_time <", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("pay_time <=", value, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeIn(List<Timestamp> values) {
            addCriterion("pay_time in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotIn(List<Timestamp> values) {
            addCriterion("pay_time not in", values, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("pay_time between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andPayTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("pay_time not between", value1, value2, "payTime");
            return (Criteria) this;
        }

        public Criteria andPickupSourceIsNull() {
            addCriterion("pickup_source is null");
            return (Criteria) this;
        }

        public Criteria andPickupSourceIsNotNull() {
            addCriterion("pickup_source is not null");
            return (Criteria) this;
        }

        public Criteria andPickupSourceEqualTo(Integer value) {
            addCriterion("pickup_source =", value, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceNotEqualTo(Integer value) {
            addCriterion("pickup_source <>", value, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceGreaterThan(Integer value) {
            addCriterion("pickup_source >", value, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceGreaterThanOrEqualTo(Integer value) {
            addCriterion("pickup_source >=", value, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceLessThan(Integer value) {
            addCriterion("pickup_source <", value, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceLessThanOrEqualTo(Integer value) {
            addCriterion("pickup_source <=", value, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceIn(List<Integer> values) {
            addCriterion("pickup_source in", values, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceNotIn(List<Integer> values) {
            addCriterion("pickup_source not in", values, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceBetween(Integer value1, Integer value2) {
            addCriterion("pickup_source between", value1, value2, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupSourceNotBetween(Integer value1, Integer value2) {
            addCriterion("pickup_source not between", value1, value2, "pickupSource");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonIsNull() {
            addCriterion("pickup_adjust_json is null");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonIsNotNull() {
            addCriterion("pickup_adjust_json is not null");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonEqualTo(String value) {
            addCriterion("pickup_adjust_json =", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonNotEqualTo(String value) {
            addCriterion("pickup_adjust_json <>", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonGreaterThan(String value) {
            addCriterion("pickup_adjust_json >", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonGreaterThanOrEqualTo(String value) {
            addCriterion("pickup_adjust_json >=", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonLessThan(String value) {
            addCriterion("pickup_adjust_json <", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonLessThanOrEqualTo(String value) {
            addCriterion("pickup_adjust_json <=", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonLike(String value) {
            addCriterion("pickup_adjust_json like", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonNotLike(String value) {
            addCriterion("pickup_adjust_json not like", value, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonIn(List<String> values) {
            addCriterion("pickup_adjust_json in", values, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonNotIn(List<String> values) {
            addCriterion("pickup_adjust_json not in", values, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonBetween(String value1, String value2) {
            addCriterion("pickup_adjust_json between", value1, value2, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andPickupAdjustJsonNotBetween(String value1, String value2) {
            addCriterion("pickup_adjust_json not between", value1, value2, "pickupAdjustJson");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeIsNull() {
            addCriterion("finance_type is null");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeIsNotNull() {
            addCriterion("finance_type is not null");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeEqualTo(Integer value) {
            addCriterion("finance_type =", value, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeNotEqualTo(Integer value) {
            addCriterion("finance_type <>", value, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeGreaterThan(Integer value) {
            addCriterion("finance_type >", value, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("finance_type >=", value, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeLessThan(Integer value) {
            addCriterion("finance_type <", value, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeLessThanOrEqualTo(Integer value) {
            addCriterion("finance_type <=", value, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeIn(List<Integer> values) {
            addCriterion("finance_type in", values, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeNotIn(List<Integer> values) {
            addCriterion("finance_type not in", values, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeBetween(Integer value1, Integer value2) {
            addCriterion("finance_type between", value1, value2, "financeType");
            return (Criteria) this;
        }

        public Criteria andFinanceTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("finance_type not between", value1, value2, "financeType");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceIsNull() {
            addCriterion("upper_service_provider_price is null");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceIsNotNull() {
            addCriterion("upper_service_provider_price is not null");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceEqualTo(BigDecimal value) {
            addCriterion("upper_service_provider_price =", value, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceNotEqualTo(BigDecimal value) {
            addCriterion("upper_service_provider_price <>", value, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceGreaterThan(BigDecimal value) {
            addCriterion("upper_service_provider_price >", value, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("upper_service_provider_price >=", value, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceLessThan(BigDecimal value) {
            addCriterion("upper_service_provider_price <", value, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceLessThanOrEqualTo(BigDecimal value) {
            addCriterion("upper_service_provider_price <=", value, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceIn(List<BigDecimal> values) {
            addCriterion("upper_service_provider_price in", values, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceNotIn(List<BigDecimal> values) {
            addCriterion("upper_service_provider_price not in", values, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("upper_service_provider_price between", value1, value2, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andUpperServiceProviderPriceNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("upper_service_provider_price not between", value1, value2, "upperServiceProviderPrice");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignIsNull() {
            addCriterion("performance_sign is null");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignIsNotNull() {
            addCriterion("performance_sign is not null");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignEqualTo(Integer value) {
            addCriterion("performance_sign =", value, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignNotEqualTo(Integer value) {
            addCriterion("performance_sign <>", value, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignGreaterThan(Integer value) {
            addCriterion("performance_sign >", value, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignGreaterThanOrEqualTo(Integer value) {
            addCriterion("performance_sign >=", value, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignLessThan(Integer value) {
            addCriterion("performance_sign <", value, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignLessThanOrEqualTo(Integer value) {
            addCriterion("performance_sign <=", value, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignIn(List<Integer> values) {
            addCriterion("performance_sign in", values, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignNotIn(List<Integer> values) {
            addCriterion("performance_sign not in", values, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignBetween(Integer value1, Integer value2) {
            addCriterion("performance_sign between", value1, value2, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andPerformanceSignNotBetween(Integer value1, Integer value2) {
            addCriterion("performance_sign not between", value1, value2, "performanceSign");
            return (Criteria) this;
        }

        public Criteria andTopicIdIsNull() {
            addCriterion("topic_id is null");
            return (Criteria) this;
        }

        public Criteria andTopicIdIsNotNull() {
            addCriterion("topic_id is not null");
            return (Criteria) this;
        }

        public Criteria andTopicIdEqualTo(String value) {
            addCriterion("topic_id =", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotEqualTo(String value) {
            addCriterion("topic_id <>", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdGreaterThan(String value) {
            addCriterion("topic_id >", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdGreaterThanOrEqualTo(String value) {
            addCriterion("topic_id >=", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdLessThan(String value) {
            addCriterion("topic_id <", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdLessThanOrEqualTo(String value) {
            addCriterion("topic_id <=", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdLike(String value) {
            addCriterion("topic_id like", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotLike(String value) {
            addCriterion("topic_id not like", value, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdIn(List<String> values) {
            addCriterion("topic_id in", values, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotIn(List<String> values) {
            addCriterion("topic_id not in", values, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdBetween(String value1, String value2) {
            addCriterion("topic_id between", value1, value2, "topicId");
            return (Criteria) this;
        }

        public Criteria andTopicIdNotBetween(String value1, String value2) {
            addCriterion("topic_id not between", value1, value2, "topicId");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillIsNull() {
            addCriterion("is_push_pickup_bill is null");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillIsNotNull() {
            addCriterion("is_push_pickup_bill is not null");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillEqualTo(Integer value) {
            addCriterion("is_push_pickup_bill =", value, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillNotEqualTo(Integer value) {
            addCriterion("is_push_pickup_bill <>", value, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillGreaterThan(Integer value) {
            addCriterion("is_push_pickup_bill >", value, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_push_pickup_bill >=", value, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillLessThan(Integer value) {
            addCriterion("is_push_pickup_bill <", value, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillLessThanOrEqualTo(Integer value) {
            addCriterion("is_push_pickup_bill <=", value, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillIn(List<Integer> values) {
            addCriterion("is_push_pickup_bill in", values, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillNotIn(List<Integer> values) {
            addCriterion("is_push_pickup_bill not in", values, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillBetween(Integer value1, Integer value2) {
            addCriterion("is_push_pickup_bill between", value1, value2, "isPushPickupBill");
            return (Criteria) this;
        }

        public Criteria andIsPushPickupBillNotBetween(Integer value1, Integer value2) {
            addCriterion("is_push_pickup_bill not between", value1, value2, "isPushPickupBill");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}