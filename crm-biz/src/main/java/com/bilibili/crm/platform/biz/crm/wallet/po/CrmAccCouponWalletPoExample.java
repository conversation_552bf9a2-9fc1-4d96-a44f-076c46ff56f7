//package com.bilibili.crm.platform.biz.crm.wallet.po;
//
//import java.sql.Timestamp;
//import java.util.ArrayList;
//import java.util.List;
//import lombok.AllArgsConstructor;
//import lombok.Builder;
//import lombok.Data;
//import lombok.NoArgsConstructor;
//
//public class CrmAccCouponWalletPoExample {
//    protected String orderByClause;
//
//    protected boolean distinct;
//
//    protected List<Criteria> oredCriteria;
//
//    private Integer limit;
//
//    private Integer offset;
//
//    public CrmAccCouponWalletPoExample() {
//        oredCriteria = new ArrayList<Criteria>();
//    }
//
//    public void setOrderByClause(String orderByClause) {
//        this.orderByClause = orderByClause;
//    }
//
//    public String getOrderByClause() {
//        return orderByClause;
//    }
//
//    public void setDistinct(boolean distinct) {
//        this.distinct = distinct;
//    }
//
//    public boolean isDistinct() {
//        return distinct;
//    }
//
//    public List<Criteria> getOredCriteria() {
//        return oredCriteria;
//    }
//
//    public void or(Criteria criteria) {
//        oredCriteria.add(criteria);
//    }
//
//    public Criteria or() {
//        Criteria criteria = createCriteriaInternal();
//        oredCriteria.add(criteria);
//        return criteria;
//    }
//
//    public Criteria createCriteria() {
//        Criteria criteria = createCriteriaInternal();
//        if (oredCriteria.size() == 0) {
//            oredCriteria.add(criteria);
//        }
//        return criteria;
//    }
//
//    protected Criteria createCriteriaInternal() {
//        Criteria criteria = new Criteria();
//        return criteria;
//    }
//
//    public void clear() {
//        oredCriteria.clear();
//        orderByClause = null;
//        distinct = false;
//    }
//
//    public void setLimit(Integer limit) {
//        this.limit = limit;
//    }
//
//    public Integer getLimit() {
//        return limit;
//    }
//
//    public void setOffset(Integer offset) {
//        this.offset = offset;
//    }
//
//    public Integer getOffset() {
//        return offset;
//    }
//
//    protected abstract static class GeneratedCriteria {
//        protected List<Criterion> criteria;
//
//        protected GeneratedCriteria() {
//            super();
//            criteria = new ArrayList<Criterion>();
//        }
//
//        public boolean isValid() {
//            return criteria.size() > 0;
//        }
//
//        public List<Criterion> getAllCriteria() {
//            return criteria;
//        }
//
//        public List<Criterion> getCriteria() {
//            return criteria;
//        }
//
//        protected void addCriterion(String condition) {
//            if (condition == null) {
//                throw new RuntimeException("Value for condition cannot be null");
//            }
//            criteria.add(new Criterion(condition));
//        }
//
//        protected void addCriterion(String condition, Object value, String property) {
//            if (value == null) {
//                throw new RuntimeException("Value for " + property + " cannot be null");
//            }
//            criteria.add(new Criterion(condition, value));
//        }
//
//        protected void addCriterion(String condition, Object value1, Object value2, String property) {
//            if (value1 == null || value2 == null) {
//                throw new RuntimeException("Between values for " + property + " cannot be null");
//            }
//            criteria.add(new Criterion(condition, value1, value2));
//        }
//
//        public Criteria andIdIsNull() {
//            addCriterion("id is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdIsNotNull() {
//            addCriterion("id is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdEqualTo(Long value) {
//            addCriterion("id =", value, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdNotEqualTo(Long value) {
//            addCriterion("id <>", value, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdGreaterThan(Long value) {
//            addCriterion("id >", value, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdGreaterThanOrEqualTo(Long value) {
//            addCriterion("id >=", value, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdLessThan(Long value) {
//            addCriterion("id <", value, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdLessThanOrEqualTo(Long value) {
//            addCriterion("id <=", value, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdIn(List<Long> values) {
//            addCriterion("id in", values, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdNotIn(List<Long> values) {
//            addCriterion("id not in", values, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdBetween(Long value1, Long value2) {
//            addCriterion("id between", value1, value2, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andIdNotBetween(Long value1, Long value2) {
//            addCriterion("id not between", value1, value2, "id");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdIsNull() {
//            addCriterion("account_id is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdIsNotNull() {
//            addCriterion("account_id is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdEqualTo(Integer value) {
//            addCriterion("account_id =", value, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdNotEqualTo(Integer value) {
//            addCriterion("account_id <>", value, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdGreaterThan(Integer value) {
//            addCriterion("account_id >", value, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
//            addCriterion("account_id >=", value, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdLessThan(Integer value) {
//            addCriterion("account_id <", value, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
//            addCriterion("account_id <=", value, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdIn(List<Integer> values) {
//            addCriterion("account_id in", values, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdNotIn(List<Integer> values) {
//            addCriterion("account_id not in", values, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
//            addCriterion("account_id between", value1, value2, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
//            addCriterion("account_id not between", value1, value2, "accountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameIsNull() {
//            addCriterion("coupon_name is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameIsNotNull() {
//            addCriterion("coupon_name is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameEqualTo(String value) {
//            addCriterion("coupon_name =", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameNotEqualTo(String value) {
//            addCriterion("coupon_name <>", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameGreaterThan(String value) {
//            addCriterion("coupon_name >", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameGreaterThanOrEqualTo(String value) {
//            addCriterion("coupon_name >=", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameLessThan(String value) {
//            addCriterion("coupon_name <", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameLessThanOrEqualTo(String value) {
//            addCriterion("coupon_name <=", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameLike(String value) {
//            addCriterion("coupon_name like", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameNotLike(String value) {
//            addCriterion("coupon_name not like", value, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameIn(List<String> values) {
//            addCriterion("coupon_name in", values, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameNotIn(List<String> values) {
//            addCriterion("coupon_name not in", values, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameBetween(String value1, String value2) {
//            addCriterion("coupon_name between", value1, value2, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponNameNotBetween(String value1, String value2) {
//            addCriterion("coupon_name not between", value1, value2, "couponName");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescIsNull() {
//            addCriterion("coupon_desc is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescIsNotNull() {
//            addCriterion("coupon_desc is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescEqualTo(String value) {
//            addCriterion("coupon_desc =", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescNotEqualTo(String value) {
//            addCriterion("coupon_desc <>", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescGreaterThan(String value) {
//            addCriterion("coupon_desc >", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescGreaterThanOrEqualTo(String value) {
//            addCriterion("coupon_desc >=", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescLessThan(String value) {
//            addCriterion("coupon_desc <", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescLessThanOrEqualTo(String value) {
//            addCriterion("coupon_desc <=", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescLike(String value) {
//            addCriterion("coupon_desc like", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescNotLike(String value) {
//            addCriterion("coupon_desc not like", value, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescIn(List<String> values) {
//            addCriterion("coupon_desc in", values, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescNotIn(List<String> values) {
//            addCriterion("coupon_desc not in", values, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescBetween(String value1, String value2) {
//            addCriterion("coupon_desc between", value1, value2, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponDescNotBetween(String value1, String value2) {
//            addCriterion("coupon_desc not between", value1, value2, "couponDesc");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdIsNull() {
//            addCriterion("project_item_id is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdIsNotNull() {
//            addCriterion("project_item_id is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdEqualTo(Integer value) {
//            addCriterion("project_item_id =", value, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdNotEqualTo(Integer value) {
//            addCriterion("project_item_id <>", value, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdGreaterThan(Integer value) {
//            addCriterion("project_item_id >", value, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdGreaterThanOrEqualTo(Integer value) {
//            addCriterion("project_item_id >=", value, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdLessThan(Integer value) {
//            addCriterion("project_item_id <", value, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdLessThanOrEqualTo(Integer value) {
//            addCriterion("project_item_id <=", value, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdIn(List<Integer> values) {
//            addCriterion("project_item_id in", values, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdNotIn(List<Integer> values) {
//            addCriterion("project_item_id not in", values, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdBetween(Integer value1, Integer value2) {
//            addCriterion("project_item_id between", value1, value2, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andProjectItemIdNotBetween(Integer value1, Integer value2) {
//            addCriterion("project_item_id not between", value1, value2, "projectItemId");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueIsNull() {
//            addCriterion("coupon_value is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueIsNotNull() {
//            addCriterion("coupon_value is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueEqualTo(Long value) {
//            addCriterion("coupon_value =", value, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueNotEqualTo(Long value) {
//            addCriterion("coupon_value <>", value, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueGreaterThan(Long value) {
//            addCriterion("coupon_value >", value, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueGreaterThanOrEqualTo(Long value) {
//            addCriterion("coupon_value >=", value, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueLessThan(Long value) {
//            addCriterion("coupon_value <", value, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueLessThanOrEqualTo(Long value) {
//            addCriterion("coupon_value <=", value, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueIn(List<Long> values) {
//            addCriterion("coupon_value in", values, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueNotIn(List<Long> values) {
//            addCriterion("coupon_value not in", values, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBetween(Long value1, Long value2) {
//            addCriterion("coupon_value between", value1, value2, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueNotBetween(Long value1, Long value2) {
//            addCriterion("coupon_value not between", value1, value2, "couponValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueIsNull() {
//            addCriterion("coupon_lock_value is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueIsNotNull() {
//            addCriterion("coupon_lock_value is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueEqualTo(Long value) {
//            addCriterion("coupon_lock_value =", value, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueNotEqualTo(Long value) {
//            addCriterion("coupon_lock_value <>", value, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueGreaterThan(Long value) {
//            addCriterion("coupon_lock_value >", value, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueGreaterThanOrEqualTo(Long value) {
//            addCriterion("coupon_lock_value >=", value, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueLessThan(Long value) {
//            addCriterion("coupon_lock_value <", value, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueLessThanOrEqualTo(Long value) {
//            addCriterion("coupon_lock_value <=", value, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueIn(List<Long> values) {
//            addCriterion("coupon_lock_value in", values, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueNotIn(List<Long> values) {
//            addCriterion("coupon_lock_value not in", values, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueBetween(Long value1, Long value2) {
//            addCriterion("coupon_lock_value between", value1, value2, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponLockValueNotBetween(Long value1, Long value2) {
//            addCriterion("coupon_lock_value not between", value1, value2, "couponLockValue");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashIsNull() {
//            addCriterion("lock_cash is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashIsNotNull() {
//            addCriterion("lock_cash is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashEqualTo(Long value) {
//            addCriterion("lock_cash =", value, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashNotEqualTo(Long value) {
//            addCriterion("lock_cash <>", value, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashGreaterThan(Long value) {
//            addCriterion("lock_cash >", value, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_cash >=", value, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashLessThan(Long value) {
//            addCriterion("lock_cash <", value, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashLessThanOrEqualTo(Long value) {
//            addCriterion("lock_cash <=", value, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashIn(List<Long> values) {
//            addCriterion("lock_cash in", values, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashNotIn(List<Long> values) {
//            addCriterion("lock_cash not in", values, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBetween(Long value1, Long value2) {
//            addCriterion("lock_cash between", value1, value2, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashNotBetween(Long value1, Long value2) {
//            addCriterion("lock_cash not between", value1, value2, "lockCash");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketIsNull() {
//            addCriterion("lock_red_packet is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketIsNotNull() {
//            addCriterion("lock_red_packet is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketEqualTo(Long value) {
//            addCriterion("lock_red_packet =", value, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketNotEqualTo(Long value) {
//            addCriterion("lock_red_packet <>", value, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketGreaterThan(Long value) {
//            addCriterion("lock_red_packet >", value, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_red_packet >=", value, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketLessThan(Long value) {
//            addCriterion("lock_red_packet <", value, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketLessThanOrEqualTo(Long value) {
//            addCriterion("lock_red_packet <=", value, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketIn(List<Long> values) {
//            addCriterion("lock_red_packet in", values, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketNotIn(List<Long> values) {
//            addCriterion("lock_red_packet not in", values, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBetween(Long value1, Long value2) {
//            addCriterion("lock_red_packet between", value1, value2, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketNotBetween(Long value1, Long value2) {
//            addCriterion("lock_red_packet not between", value1, value2, "lockRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketIsNull() {
//            addCriterion("lock_special_red_packet is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketIsNotNull() {
//            addCriterion("lock_special_red_packet is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketEqualTo(Long value) {
//            addCriterion("lock_special_red_packet =", value, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketNotEqualTo(Long value) {
//            addCriterion("lock_special_red_packet <>", value, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketGreaterThan(Long value) {
//            addCriterion("lock_special_red_packet >", value, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_special_red_packet >=", value, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketLessThan(Long value) {
//            addCriterion("lock_special_red_packet <", value, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketLessThanOrEqualTo(Long value) {
//            addCriterion("lock_special_red_packet <=", value, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketIn(List<Long> values) {
//            addCriterion("lock_special_red_packet in", values, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketNotIn(List<Long> values) {
//            addCriterion("lock_special_red_packet not in", values, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBetween(Long value1, Long value2) {
//            addCriterion("lock_special_red_packet between", value1, value2, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketNotBetween(Long value1, Long value2) {
//            addCriterion("lock_special_red_packet not between", value1, value2, "lockSpecialRedPacket");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceIsNull() {
//            addCriterion("coupon_value_balance is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceIsNotNull() {
//            addCriterion("coupon_value_balance is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceEqualTo(Long value) {
//            addCriterion("coupon_value_balance =", value, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceNotEqualTo(Long value) {
//            addCriterion("coupon_value_balance <>", value, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceGreaterThan(Long value) {
//            addCriterion("coupon_value_balance >", value, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceGreaterThanOrEqualTo(Long value) {
//            addCriterion("coupon_value_balance >=", value, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceLessThan(Long value) {
//            addCriterion("coupon_value_balance <", value, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceLessThanOrEqualTo(Long value) {
//            addCriterion("coupon_value_balance <=", value, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceIn(List<Long> values) {
//            addCriterion("coupon_value_balance in", values, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceNotIn(List<Long> values) {
//            addCriterion("coupon_value_balance not in", values, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceBetween(Long value1, Long value2) {
//            addCriterion("coupon_value_balance between", value1, value2, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceNotBetween(Long value1, Long value2) {
//            addCriterion("coupon_value_balance not between", value1, value2, "couponValueBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceIsNull() {
//            addCriterion("lock_cash_balance is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceIsNotNull() {
//            addCriterion("lock_cash_balance is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceEqualTo(Long value) {
//            addCriterion("lock_cash_balance =", value, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceNotEqualTo(Long value) {
//            addCriterion("lock_cash_balance <>", value, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceGreaterThan(Long value) {
//            addCriterion("lock_cash_balance >", value, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_cash_balance >=", value, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceLessThan(Long value) {
//            addCriterion("lock_cash_balance <", value, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceLessThanOrEqualTo(Long value) {
//            addCriterion("lock_cash_balance <=", value, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceIn(List<Long> values) {
//            addCriterion("lock_cash_balance in", values, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceNotIn(List<Long> values) {
//            addCriterion("lock_cash_balance not in", values, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceBetween(Long value1, Long value2) {
//            addCriterion("lock_cash_balance between", value1, value2, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceNotBetween(Long value1, Long value2) {
//            addCriterion("lock_cash_balance not between", value1, value2, "lockCashBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceIsNull() {
//            addCriterion("lock_red_packet_balance is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceIsNotNull() {
//            addCriterion("lock_red_packet_balance is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance =", value, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceNotEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance <>", value, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceGreaterThan(Long value) {
//            addCriterion("lock_red_packet_balance >", value, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance >=", value, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceLessThan(Long value) {
//            addCriterion("lock_red_packet_balance <", value, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceLessThanOrEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance <=", value, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceIn(List<Long> values) {
//            addCriterion("lock_red_packet_balance in", values, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceNotIn(List<Long> values) {
//            addCriterion("lock_red_packet_balance not in", values, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceBetween(Long value1, Long value2) {
//            addCriterion("lock_red_packet_balance between", value1, value2, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceNotBetween(Long value1, Long value2) {
//            addCriterion("lock_red_packet_balance not between", value1, value2, "lockRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceIsNull() {
//            addCriterion("lock_special_red_packet_balance is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceIsNotNull() {
//            addCriterion("lock_special_red_packet_balance is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance =", value, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceNotEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance <>", value, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceGreaterThan(Long value) {
//            addCriterion("lock_special_red_packet_balance >", value, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance >=", value, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceLessThan(Long value) {
//            addCriterion("lock_special_red_packet_balance <", value, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceLessThanOrEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance <=", value, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceIn(List<Long> values) {
//            addCriterion("lock_special_red_packet_balance in", values, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceNotIn(List<Long> values) {
//            addCriterion("lock_special_red_packet_balance not in", values, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceBetween(Long value1, Long value2) {
//            addCriterion("lock_special_red_packet_balance between", value1, value2, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceNotBetween(Long value1, Long value2) {
//            addCriterion("lock_special_red_packet_balance not between", value1, value2, "lockSpecialRedPacketBalance");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapIsNull() {
//            addCriterion("coupon_value_balance_snap is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapIsNotNull() {
//            addCriterion("coupon_value_balance_snap is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapEqualTo(Long value) {
//            addCriterion("coupon_value_balance_snap =", value, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapNotEqualTo(Long value) {
//            addCriterion("coupon_value_balance_snap <>", value, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapGreaterThan(Long value) {
//            addCriterion("coupon_value_balance_snap >", value, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapGreaterThanOrEqualTo(Long value) {
//            addCriterion("coupon_value_balance_snap >=", value, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapLessThan(Long value) {
//            addCriterion("coupon_value_balance_snap <", value, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapLessThanOrEqualTo(Long value) {
//            addCriterion("coupon_value_balance_snap <=", value, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapIn(List<Long> values) {
//            addCriterion("coupon_value_balance_snap in", values, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapNotIn(List<Long> values) {
//            addCriterion("coupon_value_balance_snap not in", values, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapBetween(Long value1, Long value2) {
//            addCriterion("coupon_value_balance_snap between", value1, value2, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponValueBalanceSnapNotBetween(Long value1, Long value2) {
//            addCriterion("coupon_value_balance_snap not between", value1, value2, "couponValueBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapIsNull() {
//            addCriterion("lock_cash_balance_snap is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapIsNotNull() {
//            addCriterion("lock_cash_balance_snap is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapEqualTo(Long value) {
//            addCriterion("lock_cash_balance_snap =", value, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapNotEqualTo(Long value) {
//            addCriterion("lock_cash_balance_snap <>", value, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapGreaterThan(Long value) {
//            addCriterion("lock_cash_balance_snap >", value, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_cash_balance_snap >=", value, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapLessThan(Long value) {
//            addCriterion("lock_cash_balance_snap <", value, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapLessThanOrEqualTo(Long value) {
//            addCriterion("lock_cash_balance_snap <=", value, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapIn(List<Long> values) {
//            addCriterion("lock_cash_balance_snap in", values, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapNotIn(List<Long> values) {
//            addCriterion("lock_cash_balance_snap not in", values, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapBetween(Long value1, Long value2) {
//            addCriterion("lock_cash_balance_snap between", value1, value2, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockCashBalanceSnapNotBetween(Long value1, Long value2) {
//            addCriterion("lock_cash_balance_snap not between", value1, value2, "lockCashBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapIsNull() {
//            addCriterion("lock_red_packet_balance_snap is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapIsNotNull() {
//            addCriterion("lock_red_packet_balance_snap is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance_snap =", value, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapNotEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance_snap <>", value, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapGreaterThan(Long value) {
//            addCriterion("lock_red_packet_balance_snap >", value, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance_snap >=", value, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapLessThan(Long value) {
//            addCriterion("lock_red_packet_balance_snap <", value, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapLessThanOrEqualTo(Long value) {
//            addCriterion("lock_red_packet_balance_snap <=", value, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapIn(List<Long> values) {
//            addCriterion("lock_red_packet_balance_snap in", values, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapNotIn(List<Long> values) {
//            addCriterion("lock_red_packet_balance_snap not in", values, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapBetween(Long value1, Long value2) {
//            addCriterion("lock_red_packet_balance_snap between", value1, value2, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockRedPacketBalanceSnapNotBetween(Long value1, Long value2) {
//            addCriterion("lock_red_packet_balance_snap not between", value1, value2, "lockRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapIsNull() {
//            addCriterion("lock_special_red_packet_balance_snap is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapIsNotNull() {
//            addCriterion("lock_special_red_packet_balance_snap is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance_snap =", value, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapNotEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance_snap <>", value, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapGreaterThan(Long value) {
//            addCriterion("lock_special_red_packet_balance_snap >", value, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapGreaterThanOrEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance_snap >=", value, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapLessThan(Long value) {
//            addCriterion("lock_special_red_packet_balance_snap <", value, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapLessThanOrEqualTo(Long value) {
//            addCriterion("lock_special_red_packet_balance_snap <=", value, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapIn(List<Long> values) {
//            addCriterion("lock_special_red_packet_balance_snap in", values, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapNotIn(List<Long> values) {
//            addCriterion("lock_special_red_packet_balance_snap not in", values, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapBetween(Long value1, Long value2) {
//            addCriterion("lock_special_red_packet_balance_snap between", value1, value2, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andLockSpecialRedPacketBalanceSnapNotBetween(Long value1, Long value2) {
//            addCriterion("lock_special_red_packet_balance_snap not between", value1, value2, "lockSpecialRedPacketBalanceSnap");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeIsNull() {
//            addCriterion("begin_time is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeIsNotNull() {
//            addCriterion("begin_time is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeEqualTo(Timestamp value) {
//            addCriterion("begin_time =", value, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeNotEqualTo(Timestamp value) {
//            addCriterion("begin_time <>", value, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeGreaterThan(Timestamp value) {
//            addCriterion("begin_time >", value, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeGreaterThanOrEqualTo(Timestamp value) {
//            addCriterion("begin_time >=", value, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeLessThan(Timestamp value) {
//            addCriterion("begin_time <", value, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeLessThanOrEqualTo(Timestamp value) {
//            addCriterion("begin_time <=", value, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeIn(List<Timestamp> values) {
//            addCriterion("begin_time in", values, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeNotIn(List<Timestamp> values) {
//            addCriterion("begin_time not in", values, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("begin_time between", value1, value2, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBeginTimeNotBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("begin_time not between", value1, value2, "beginTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeIsNull() {
//            addCriterion("end_time is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeIsNotNull() {
//            addCriterion("end_time is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeEqualTo(Timestamp value) {
//            addCriterion("end_time =", value, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeNotEqualTo(Timestamp value) {
//            addCriterion("end_time <>", value, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeGreaterThan(Timestamp value) {
//            addCriterion("end_time >", value, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeGreaterThanOrEqualTo(Timestamp value) {
//            addCriterion("end_time >=", value, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeLessThan(Timestamp value) {
//            addCriterion("end_time <", value, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeLessThanOrEqualTo(Timestamp value) {
//            addCriterion("end_time <=", value, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeIn(List<Timestamp> values) {
//            addCriterion("end_time in", values, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeNotIn(List<Timestamp> values) {
//            addCriterion("end_time not in", values, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("end_time between", value1, value2, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andEndTimeNotBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("end_time not between", value1, value2, "endTime");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusIsNull() {
//            addCriterion("biz_status is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusIsNotNull() {
//            addCriterion("biz_status is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusEqualTo(Integer value) {
//            addCriterion("biz_status =", value, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusNotEqualTo(Integer value) {
//            addCriterion("biz_status <>", value, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusGreaterThan(Integer value) {
//            addCriterion("biz_status >", value, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusGreaterThanOrEqualTo(Integer value) {
//            addCriterion("biz_status >=", value, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusLessThan(Integer value) {
//            addCriterion("biz_status <", value, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusLessThanOrEqualTo(Integer value) {
//            addCriterion("biz_status <=", value, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusIn(List<Integer> values) {
//            addCriterion("biz_status in", values, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusNotIn(List<Integer> values) {
//            addCriterion("biz_status not in", values, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusBetween(Integer value1, Integer value2) {
//            addCriterion("biz_status between", value1, value2, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizStatusNotBetween(Integer value1, Integer value2) {
//            addCriterion("biz_status not between", value1, value2, "bizStatus");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeIsNull() {
//            addCriterion("coupon_type is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeIsNotNull() {
//            addCriterion("coupon_type is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeEqualTo(Integer value) {
//            addCriterion("coupon_type =", value, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeNotEqualTo(Integer value) {
//            addCriterion("coupon_type <>", value, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeGreaterThan(Integer value) {
//            addCriterion("coupon_type >", value, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeGreaterThanOrEqualTo(Integer value) {
//            addCriterion("coupon_type >=", value, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeLessThan(Integer value) {
//            addCriterion("coupon_type <", value, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeLessThanOrEqualTo(Integer value) {
//            addCriterion("coupon_type <=", value, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeIn(List<Integer> values) {
//            addCriterion("coupon_type in", values, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeNotIn(List<Integer> values) {
//            addCriterion("coupon_type not in", values, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeBetween(Integer value1, Integer value2) {
//            addCriterion("coupon_type between", value1, value2, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andCouponTypeNotBetween(Integer value1, Integer value2) {
//            addCriterion("coupon_type not between", value1, value2, "couponType");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedIsNull() {
//            addCriterion("is_deleted is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedIsNotNull() {
//            addCriterion("is_deleted is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedEqualTo(Integer value) {
//            addCriterion("is_deleted =", value, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedNotEqualTo(Integer value) {
//            addCriterion("is_deleted <>", value, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedGreaterThan(Integer value) {
//            addCriterion("is_deleted >", value, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
//            addCriterion("is_deleted >=", value, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedLessThan(Integer value) {
//            addCriterion("is_deleted <", value, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
//            addCriterion("is_deleted <=", value, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedIn(List<Integer> values) {
//            addCriterion("is_deleted in", values, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedNotIn(List<Integer> values) {
//            addCriterion("is_deleted not in", values, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
//            addCriterion("is_deleted between", value1, value2, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
//            addCriterion("is_deleted not between", value1, value2, "isDeleted");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeIsNull() {
//            addCriterion("ctime is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeIsNotNull() {
//            addCriterion("ctime is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeEqualTo(Timestamp value) {
//            addCriterion("ctime =", value, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeNotEqualTo(Timestamp value) {
//            addCriterion("ctime <>", value, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeGreaterThan(Timestamp value) {
//            addCriterion("ctime >", value, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
//            addCriterion("ctime >=", value, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeLessThan(Timestamp value) {
//            addCriterion("ctime <", value, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
//            addCriterion("ctime <=", value, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeIn(List<Timestamp> values) {
//            addCriterion("ctime in", values, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeNotIn(List<Timestamp> values) {
//            addCriterion("ctime not in", values, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("ctime between", value1, value2, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("ctime not between", value1, value2, "ctime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeIsNull() {
//            addCriterion("mtime is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeIsNotNull() {
//            addCriterion("mtime is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeEqualTo(Timestamp value) {
//            addCriterion("mtime =", value, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeNotEqualTo(Timestamp value) {
//            addCriterion("mtime <>", value, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeGreaterThan(Timestamp value) {
//            addCriterion("mtime >", value, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
//            addCriterion("mtime >=", value, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeLessThan(Timestamp value) {
//            addCriterion("mtime <", value, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
//            addCriterion("mtime <=", value, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeIn(List<Timestamp> values) {
//            addCriterion("mtime in", values, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeNotIn(List<Timestamp> values) {
//            addCriterion("mtime not in", values, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("mtime between", value1, value2, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
//            addCriterion("mtime not between", value1, value2, "mtime");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerIsNull() {
//            addCriterion("is_inner is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerIsNotNull() {
//            addCriterion("is_inner is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerEqualTo(Integer value) {
//            addCriterion("is_inner =", value, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerNotEqualTo(Integer value) {
//            addCriterion("is_inner <>", value, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerGreaterThan(Integer value) {
//            addCriterion("is_inner >", value, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerGreaterThanOrEqualTo(Integer value) {
//            addCriterion("is_inner >=", value, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerLessThan(Integer value) {
//            addCriterion("is_inner <", value, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerLessThanOrEqualTo(Integer value) {
//            addCriterion("is_inner <=", value, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerIn(List<Integer> values) {
//            addCriterion("is_inner in", values, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerNotIn(List<Integer> values) {
//            addCriterion("is_inner not in", values, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerBetween(Integer value1, Integer value2) {
//            addCriterion("is_inner between", value1, value2, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsInnerNotBetween(Integer value1, Integer value2) {
//            addCriterion("is_inner not between", value1, value2, "isInner");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionIsNull() {
//            addCriterion("biz_version is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionIsNotNull() {
//            addCriterion("biz_version is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionEqualTo(Long value) {
//            addCriterion("biz_version =", value, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionNotEqualTo(Long value) {
//            addCriterion("biz_version <>", value, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionGreaterThan(Long value) {
//            addCriterion("biz_version >", value, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionGreaterThanOrEqualTo(Long value) {
//            addCriterion("biz_version >=", value, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionLessThan(Long value) {
//            addCriterion("biz_version <", value, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionLessThanOrEqualTo(Long value) {
//            addCriterion("biz_version <=", value, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionIn(List<Long> values) {
//            addCriterion("biz_version in", values, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionNotIn(List<Long> values) {
//            addCriterion("biz_version not in", values, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionBetween(Long value1, Long value2) {
//            addCriterion("biz_version between", value1, value2, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andBizVersionNotBetween(Long value1, Long value2) {
//            addCriterion("biz_version not between", value1, value2, "bizVersion");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdIsNull() {
//            addCriterion("agent_account_id is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdIsNotNull() {
//            addCriterion("agent_account_id is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdEqualTo(Integer value) {
//            addCriterion("agent_account_id =", value, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdNotEqualTo(Integer value) {
//            addCriterion("agent_account_id <>", value, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdGreaterThan(Integer value) {
//            addCriterion("agent_account_id >", value, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdGreaterThanOrEqualTo(Integer value) {
//            addCriterion("agent_account_id >=", value, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdLessThan(Integer value) {
//            addCriterion("agent_account_id <", value, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdLessThanOrEqualTo(Integer value) {
//            addCriterion("agent_account_id <=", value, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdIn(List<Integer> values) {
//            addCriterion("agent_account_id in", values, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdNotIn(List<Integer> values) {
//            addCriterion("agent_account_id not in", values, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdBetween(Integer value1, Integer value2) {
//            addCriterion("agent_account_id between", value1, value2, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andAgentAccountIdNotBetween(Integer value1, Integer value2) {
//            addCriterion("agent_account_id not between", value1, value2, "agentAccountId");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentIsNull() {
//            addCriterion("is_agent is null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentIsNotNull() {
//            addCriterion("is_agent is not null");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentEqualTo(Integer value) {
//            addCriterion("is_agent =", value, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentNotEqualTo(Integer value) {
//            addCriterion("is_agent <>", value, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentGreaterThan(Integer value) {
//            addCriterion("is_agent >", value, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentGreaterThanOrEqualTo(Integer value) {
//            addCriterion("is_agent >=", value, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentLessThan(Integer value) {
//            addCriterion("is_agent <", value, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentLessThanOrEqualTo(Integer value) {
//            addCriterion("is_agent <=", value, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentIn(List<Integer> values) {
//            addCriterion("is_agent in", values, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentNotIn(List<Integer> values) {
//            addCriterion("is_agent not in", values, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentBetween(Integer value1, Integer value2) {
//            addCriterion("is_agent between", value1, value2, "isAgent");
//            return (Criteria) this;
//        }
//
//        public Criteria andIsAgentNotBetween(Integer value1, Integer value2) {
//            addCriterion("is_agent not between", value1, value2, "isAgent");
//            return (Criteria) this;
//        }
//    }
//
//    /**
//     */
//    public static class Criteria extends GeneratedCriteria {
//
//        protected Criteria() {
//            super();
//        }
//    }
//
//    public static class Criterion {
//        private String condition;
//
//        private Object value;
//
//        private Object secondValue;
//
//        private boolean noValue;
//
//        private boolean singleValue;
//
//        private boolean betweenValue;
//
//        private boolean listValue;
//
//        private String typeHandler;
//
//        public String getCondition() {
//            return condition;
//        }
//
//        public Object getValue() {
//            return value;
//        }
//
//        public Object getSecondValue() {
//            return secondValue;
//        }
//
//        public boolean isNoValue() {
//            return noValue;
//        }
//
//        public boolean isSingleValue() {
//            return singleValue;
//        }
//
//        public boolean isBetweenValue() {
//            return betweenValue;
//        }
//
//        public boolean isListValue() {
//            return listValue;
//        }
//
//        public String getTypeHandler() {
//            return typeHandler;
//        }
//
//        protected Criterion(String condition) {
//            super();
//            this.condition = condition;
//            this.typeHandler = null;
//            this.noValue = true;
//        }
//
//        protected Criterion(String condition, Object value, String typeHandler) {
//            super();
//            this.condition = condition;
//            this.value = value;
//            this.typeHandler = typeHandler;
//            if (value instanceof List<?>) {
//                this.listValue = true;
//            } else {
//                this.singleValue = true;
//            }
//        }
//
//        protected Criterion(String condition, Object value) {
//            this(condition, value, null);
//        }
//
//        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
//            super();
//            this.condition = condition;
//            this.value = value;
//            this.secondValue = secondValue;
//            this.typeHandler = typeHandler;
//            this.betweenValue = true;
//        }
//
//        protected Criterion(String condition, Object value, Object secondValue) {
//            this(condition, value, secondValue, null);
//        }
//    }
//}