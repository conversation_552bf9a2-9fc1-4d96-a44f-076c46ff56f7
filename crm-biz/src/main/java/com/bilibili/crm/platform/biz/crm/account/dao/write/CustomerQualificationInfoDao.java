package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CustomerQualificationInfoPo;
import com.bilibili.crm.platform.biz.po.CustomerQualificationInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CustomerQualificationInfoDao {
    long countByExample(CustomerQualificationInfoPoExample example);

    int deleteByExample(CustomerQualificationInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerQualificationInfoPo record);

    int insertBatch(List<CustomerQualificationInfoPo> records);

    int insertUpdateBatch(List<CustomerQualificationInfoPo> records);

    int insert(CustomerQualificationInfoPo record);

    int insertUpdateSelective(CustomerQualificationInfoPo record);

    int insertSelective(CustomerQualificationInfoPo record);

    List<CustomerQualificationInfoPo> selectByExample(CustomerQualificationInfoPoExample example);

    CustomerQualificationInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerQualificationInfoPo record, @Param("example") CustomerQualificationInfoPoExample example);

    int updateByExample(@Param("record") CustomerQualificationInfoPo record, @Param("example") CustomerQualificationInfoPoExample example);

    int updateByPrimaryKeySelective(CustomerQualificationInfoPo record);

    int updateByPrimaryKey(CustomerQualificationInfoPo record);
}