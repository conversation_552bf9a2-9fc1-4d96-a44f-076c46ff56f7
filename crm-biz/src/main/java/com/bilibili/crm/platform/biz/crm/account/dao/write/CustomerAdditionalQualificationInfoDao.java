package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CustomerAdditionalQualificationInfoPo;
import com.bilibili.crm.platform.biz.po.CustomerAdditionalQualificationInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CustomerAdditionalQualificationInfoDao {
    long countByExample(CustomerAdditionalQualificationInfoPoExample example);

    int deleteByExample(CustomerAdditionalQualificationInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerAdditionalQualificationInfoPo record);

    int insertBatch(List<CustomerAdditionalQualificationInfoPo> records);

    int insertUpdateBatch(List<CustomerAdditionalQualificationInfoPo> records);

    int insert(CustomerAdditionalQualificationInfoPo record);

    int insertUpdateSelective(CustomerAdditionalQualificationInfoPo record);

    int insertSelective(CustomerAdditionalQualificationInfoPo record);

    List<CustomerAdditionalQualificationInfoPo> selectByExample(CustomerAdditionalQualificationInfoPoExample example);

    CustomerAdditionalQualificationInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerAdditionalQualificationInfoPo record, @Param("example") CustomerAdditionalQualificationInfoPoExample example);

    int updateByExample(@Param("record") CustomerAdditionalQualificationInfoPo record, @Param("example") CustomerAdditionalQualificationInfoPoExample example);

    int updateByPrimaryKeySelective(CustomerAdditionalQualificationInfoPo record);

    int updateByPrimaryKey(CustomerAdditionalQualificationInfoPo record);
}