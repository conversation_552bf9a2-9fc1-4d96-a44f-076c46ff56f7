package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCompanyAccountMappingPo;
import com.bilibili.crm.platform.biz.po.CrmCompanyAccountMappingPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CrmCompanyAccountMappingDao {
    long countByExample(CrmCompanyAccountMappingPoExample example);

    int deleteByExample(CrmCompanyAccountMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CrmCompanyAccountMappingPo record);

    int insertSelective(CrmCompanyAccountMappingPo record);

    List<CrmCompanyAccountMappingPo> selectByExample(CrmCompanyAccountMappingPoExample example);

    CrmCompanyAccountMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmCompanyAccountMappingPo record, @Param("example") CrmCompanyAccountMappingPoExample example);

    int updateByExample(@Param("record") CrmCompanyAccountMappingPo record, @Param("example") CrmCompanyAccountMappingPoExample example);

    int updateByPrimaryKeySelective(CrmCompanyAccountMappingPo record);

    int updateByPrimaryKey(CrmCompanyAccountMappingPo record);
}