package com.bilibili.crm.platform.biz.util.wx.dto.response;

import com.google.gson.annotations.SerializedName;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
 * @date 2020/3/30
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class WxAppAccessToken extends WxAppResponse {

    @SerializedName("access_token")
    private String accessToken;

    @SerializedName("expires_in")
    private Integer expiresIn;
}
