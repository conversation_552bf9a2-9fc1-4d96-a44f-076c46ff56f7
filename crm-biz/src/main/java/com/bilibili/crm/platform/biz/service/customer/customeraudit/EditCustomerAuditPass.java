package com.bilibili.crm.platform.biz.service.customer.customeraudit;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.customer.dto.AuditCustomerDto;
import com.bilibili.crm.platform.api.customer.dto.EditCustomerDto;
import com.bilibili.crm.platform.api.customer.dto.SaveCustomerResultDto;
import com.bilibili.crm.platform.api.wo.dto.WorkOrderDto;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * 修改客户工单审核通过
 *
 * <AUTHOR>
 * @date 2021/3/17 1:34 下午
 */
@Slf4j
@Component(value = "EditCustomerAuditPass")
public class EditCustomerAuditPass extends BaseCustomerAuditPass {

    @Override
    protected void checkCustomerRepeat(String username, Integer id) {
        customerRepeatValidator.checkCustomerIfRepeatForUpdateCustomer(username, id);
    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public SaveCustomerResultDto doCreateOrUpdateCustomer(Operator operator, AuditCustomerDto auditCustomerDto, WorkOrderDto workOrderDto, EditCustomerDto editCustomerDto) {
        log.info("=====> auditSuccess[编辑客户工单], workOrderId:{}, operator:{}", auditCustomerDto.getWorkOrderId(),
                operator.getOperatorName());
        CustomerPo originCustomerPo = customerRepo.queryNotDeletedCustomerById(editCustomerDto.getId());
        // 修改客户相关信息（比如：客户自身信息，资质）
        // todo: 既然资质是在这里更新的，这里就必须特别注意
        updateCustomer(editCustomerDto, operator);
        // 将该客户id下的所有的账户重新绑定集团id
        updateAccountsInfo(auditCustomerDto, workOrderDto, editCustomerDto, originCustomerPo);
        return SaveCustomerResultDto.builder()
                .customerId(editCustomerDto.getId())
                .isCustomerReuse(false)
                .build();
    }
}
