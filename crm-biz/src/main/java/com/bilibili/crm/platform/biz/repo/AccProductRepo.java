package com.bilibili.crm.platform.biz.repo;

import com.bilibili.adp.common.enums.Status;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.brand.api.common.enums.IsDeleted;
import com.bilibili.commercialorder.api.upper.enums.EnableStatus;
import com.bilibili.crm.platform.api.company.dto.ProductDto;
import com.bilibili.crm.platform.biz.common.PageInfo;
import com.bilibili.crm.platform.biz.dao.AccProductDao;
import com.bilibili.crm.platform.biz.po.AccProductPo;
import com.bilibili.crm.platform.biz.po.AccProductPoExample;
import com.bilibili.crm.platform.biz.repo.param.QueryProductParam;
import com.bilibili.crm.platform.biz.service.corporationgroup.component.ProductAndLineRelUpdateInfo;
import com.bilibili.crm.platform.common.CrmConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.validation.constraints.NotNull;
import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/9/27 8:03 下午
 */
@Slf4j
@Repository
public class AccProductRepo {

    @Autowired
    private AccProductDao accProductDao;

    /**
     * 根据 productName 获取有效地产品的 count
     *
     * @param productName
     * @return
     */
    public Integer countByName(String productName) {
        if (StringUtils.isEmpty(productName)) {
            throw new ServiceRuntimeException("product name can't be null!");
        }
        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andNameEqualTo(productName).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Long count = accProductDao.countByExample(example);
        return count.intValue();
    }

    /**
     * 获取所有的产品列表（修复数据用，一般用不到）
     *
     * @return
     */
    public List<AccProductPo> queryAllProductList(Timestamp fromDate, Timestamp toDate) {
        AccProductPoExample example = new AccProductPoExample();

        AccProductPoExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (fromDate != null) {
            criteria.andCtimeGreaterThan(fromDate);
        }
        if (toDate != null) {
            criteria.andCtimeLessThanOrEqualTo(toDate);
        }
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        return accProductPos;
    }

    /**
     * 根据产品线id和产品名称模糊查询产品列表
     *
     * @param productNameLike
     * @return
     */
    public List<ProductDto> queryProductsByLikeName(String productNameLike) {
        return this.queryProductsByLikeName(productNameLike, null);
    }

    /**
     * 根据产品名称模糊查询产品
     *
     * @param productNameLike
     * @return
     */
    public List<ProductDto> queryEnableProductsByLikeName(String productNameLike) {
        return this.queryProductsByLikeName(productNameLike, EnableStatus.ACTIVATED.getCode());
    }

    public List<ProductDto> queryProductsByLikeName(String productNameLike, Integer status) {
        AccProductPoExample example = new AccProductPoExample();
        AccProductPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(0);
        if (status != null) {
            criteria.andStatusEqualTo(status);
        }
        if (StringUtils.isNotBlank(productNameLike)) {
            criteria.andNameLike("%" + productNameLike + "%");
        }
        List<AccProductPo> poList = accProductDao.selectByExample(example);
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        return poList.stream().map(po -> {
            ProductDto dto = ProductDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据id 获取产品
     *
     * @param productId
     * @return
     */
    public AccProductPo getProductById(@NotNull Integer productId) {
        if (productId == null || productId.equals(0)) {
            return null;
        }

        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accProductPos)) {
            return null;
        }
        return accProductPos.get(0);
    }

    public List<AccProductPo> getProductListByIds(List<Integer> productIds) {
        if (org.springframework.util.CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }
        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdIn(productIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        return accProductPos;
    }

    public Map<Integer, AccProductPo> queryProductMapByIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_MAP;
        }

        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdIn(productIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        Map<Integer, AccProductPo> accProductPoMap = accProductPos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));
        return accProductPoMap;
    }

    public Map<Integer, String> queryProductNameMapByIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_MAP;
        }

        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdIn(productIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        Map<Integer, String> nameMap = accProductPos.stream().collect(Collectors.toMap(AccProductPo::getId, AccProductPo::getName));
        return nameMap;
    }

    public List<AccProductPo> queryProductListByIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.EMPTY_LIST;
        }

        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdIn(productIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        return accProductPos;
    }

    public List<AccProductPo> queryProductListByNameList(List<String> productNames) {
        if (CollectionUtils.isEmpty(productNames)) {
            return Collections.EMPTY_LIST;
        }
        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andNameIn(productNames).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        return accProductPos;
    }

    public Map<Integer, AccProductPo> getProductMapByIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return new HashMap<>();
        }
        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdIn(productIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        Map<Integer, AccProductPo> accProductPoMap = accProductPos.stream().collect(Collectors.toMap(AccProductPo::getId, accProductPo -> accProductPo));
        return accProductPoMap;
    }

    /**
     * 启用产品
     *
     * @param productId
     * @param operatorName
     * @return
     */
    public Integer enableProduct(Integer productId, String operatorName) {
        return updateProductStatus(productId, operatorName, EnableStatus.ACTIVATED.getCode());
    }

    /**
     * 禁用产品
     *
     * @param productId
     * @param operatorName
     * @return
     */
    public Integer disableProduct(Integer productId, String operatorName) {
        return updateProductStatus(productId, operatorName, EnableStatus.TERMINATED.getCode());
    }

    /**
     * 修改产品线状态
     *
     * @param productLineId
     * @param operatorName
     * @param status        目标状态
     * @return
     */
    public Integer updateProductStatus(@NotNull Integer productLineId, String operatorName, Integer status) {
        AccProductPo updatePo = new AccProductPo();
        updatePo.setId(productLineId);
        updatePo.setStatus(status);
        updatePo.setUpdateUser(operatorName);
        int count = accProductDao.updateByPrimaryKeySelective(updatePo);
        return count;
    }

    /**
     * 批量删除
     *
     * @param productIds
     */
    public Integer batchDisable(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            log.info("=====> batchDelete, no data need to delete");
            return 0;
        }
        AccProductPoExample example = new AccProductPoExample();
        example.or().andIdIn(productIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        AccProductPo productPo = new AccProductPo();
        productPo.setStatus(EnableStatus.TERMINATED.getCode());
        int deleteCount = accProductDao.updateByExampleSelective(productPo, example);
        log.info("=====> batchDelete, need delete size:{}, real delete size:{}", productIds.size(), deleteCount);
        return deleteCount;
    }

    public Integer disableByProductId(Integer productId) {
        if (!Utils.isPositive(productId)) {
            return 0;
        }
        AccProductPoExample example = new AccProductPoExample();
        example.or().andIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        AccProductPo productPo = new AccProductPo();
        productPo.setStatus(EnableStatus.TERMINATED.getCode());
        int deleteCount = accProductDao.updateByExampleSelective(new AccProductPo(), example);
        return deleteCount;
    }

    /**
     * 批量新增
     *
     * @param needAddProductPos
     */
    public Integer batchInsert(List<ProductAndLineRelUpdateInfo> needAddProductPos) {
        if (CollectionUtils.isEmpty(needAddProductPos)) {
            log.info("=====> batchInsert, no data need to process");
            return 0;
        }
        List<AccProductPo> productPos = new ArrayList<>(needAddProductPos.size());
        for (ProductAndLineRelUpdateInfo productAndLineRelUpdateInfo : needAddProductPos) {
            AccProductPo productPo = AccProductPo.builder().build();
            productPo.setProductLineId(0);
            productPo.setTag("");
            productPo.setName(productAndLineRelUpdateInfo.getNewProductName());
            productPo.setIsDeleted(IsDeleted.VALID.getCode());
            productPo.setCtime(Utils.getNow());
            productPo.setMtime(Utils.getNow());
            productPo.setStatus(1);
            productPo.setCreateUser(CrmConstant.SYSTEM_OPERATOR);
            productPo.setUpdateUser(CrmConstant.SYSTEM_OPERATOR);
            productPos.add(productPo);
        }
        int count = accProductDao.insertBatch(productPos);
        log.info("=====> batchInsert, need added count:{}, real added count:{}", needAddProductPos.size(), count);
        return count;
    }

    public Integer insert(ProductAndLineRelUpdateInfo productAndLineRelUpdateInfo) {
        AccProductPo productPo = AccProductPo.builder().build();
        productPo.setProductLineId(0);
        productPo.setTag("");
        productPo.setName(productAndLineRelUpdateInfo.getNewProductName());
        productPo.setIsDeleted(IsDeleted.VALID.getCode());
        productPo.setCtime(Utils.getNow());
        productPo.setMtime(Utils.getNow());
        productPo.setStatus(1);
        productPo.setCreateUser(CrmConstant.SYSTEM_OPERATOR);
        productPo.setUpdateUser(CrmConstant.SYSTEM_OPERATOR);
        accProductDao.insert(productPo);
        return productPo.getId();
    }

    /**
     * 批量修改产品的名称
     * 根据产品 id 修改，幂等的
     *
     * @param needUpdateNameProductPos
     * @return
     */
    public Integer batchUpdateName(List<ProductAndLineRelUpdateInfo> needUpdateNameProductPos) {
        if (CollectionUtils.isEmpty(needUpdateNameProductPos)) {
            log.info("=====> batchUpdateName, no data need to process");
            return 0;
        }
        log.info("=====> batchUpdate start ... size:{}", needUpdateNameProductPos.size());

        Integer sumCount = 0;
        for (ProductAndLineRelUpdateInfo productAndLineRelUpdateInfo : needUpdateNameProductPos) {
            AccProductPo newProductPo = AccProductPo.builder().build();
            newProductPo.setId(productAndLineRelUpdateInfo.getOldProductId());
            newProductPo.setName(productAndLineRelUpdateInfo.getNewProductName());
            sumCount += accProductDao.updateByPrimaryKeySelective(newProductPo);
        }
        log.info("=====> batchUpdateName, need update size:{}, real update size:{}", needUpdateNameProductPos.size(), sumCount);
        return sumCount;
    }

    public AccProductPo queryProductsByName(String productName) {
        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andNameEqualTo(productName).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> productPos = accProductDao.selectByExample(example);
        if (CollectionUtils.isEmpty(productPos)) {
            return null;
        }
        return productPos.get(0);
    }

    public List<AccProductPo> queryListByExample(AccProductPoExample example) {
        List<AccProductPo> productPos = accProductDao.selectByExample(example);
        if (CollectionUtils.isEmpty(productPos)) {
            return null;
        }
        return productPos;
    }

    public List<AccProductPo> queryListByParam(QueryProductParam queryProductParam) {
        AccProductPoExample productPoExample = new AccProductPoExample();
        AccProductPoExample.Criteria criteria = productPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andStatusEqualTo(Status.VALID.getCode());
        ObjectUtils.setInteger(queryProductParam::getProductId, criteria::andIdEqualTo);
        ObjectUtils.setList(queryProductParam::getProductIds, criteria::andIdIn);
        ObjectUtils.setString(queryProductParam::getProductName, criteria::andNameEqualTo);
        ObjectUtils.setLikeString(queryProductParam::getProductNameLike, criteria::andNameLike);
        ObjectUtils.setList(queryProductParam::getCommerceCategoryFirstIds, criteria::andCommerceCategoryFirstIdIn);
        ObjectUtils.setList(queryProductParam::getCommerceCategorySecondIds, criteria::andCommerceCategorySecondIdIn);

        List<AccProductPo> productPos = accProductDao.selectByExample(productPoExample);

        if (org.springframework.util.CollectionUtils.isEmpty(productPos)) {
            return Collections.emptyList();
        }

        return productPos;
    }

    /**
     * 模糊查询品牌总数
     *
     * @param productName
     * @return
     */
    public Integer countByFuzzyName(String productName, List<Integer> ids) {
        AccProductPoExample example = new AccProductPoExample();
        AccProductPoExample.Criteria criteria = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (StringUtils.isNotBlank(productName)) {
            criteria.andNameLike(productName);
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(ids)){
            criteria.andIdIn(ids);
        }
        long count = accProductDao.countByExample(example);
        return (int) count;
    }

    /**
     * 分页查询，品牌名称可能为空
     *
     * @param pageInfo
     * @param productName
     * @return
     */
    public List<ProductDto> pageQueryByFuzzyName(PageInfo pageInfo, String productName, List<Integer> ids) {
        AccProductPoExample example = new AccProductPoExample();
        AccProductPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(0);
        if (StringUtils.isNotBlank(productName)) {
            criteria.andNameLike("%" + productName + "%");
        }
        if(org.apache.commons.collections4.CollectionUtils.isNotEmpty(ids)){
            criteria.andIdIn(ids);
        }
        example.setLimit(pageInfo.getPageSize());
        example.setOffset(pageInfo.calculateOffset());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accProductPos)) {
            return Collections.emptyList();
        }
        return accProductPos.stream().map(po -> {
            ProductDto dto = ProductDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList());
    }

    /**
     * 根据id 精准查询
     *
     * @param productId
     * @return
     */
    public ProductDto queryProductById(Integer productId) {
        if (null == productId) {
            return null;
        }
        AccProductPoExample example = new AccProductPoExample();
        example.createCriteria().andIdEqualTo(productId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<AccProductPo> accProductPos = accProductDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accProductPos)) {
            return null;
        }
        AccProductPo accProductPo = accProductPos.get(0);
        ProductDto dto = ProductDto.builder().build();
        BeanUtils.copyProperties(accProductPo, dto);
        return dto;
    }

    /**
     * @param productIds
     * @return
     */
    public List<ProductDto> batchQueryProductsByIds(List<Integer> productIds) {
        if (CollectionUtils.isEmpty(productIds)) {
            return Collections.emptyList();
        }
        List<AccProductPo> accProductPos = queryProductListByIds(productIds);
        return accProductPos.stream().map(po -> {
            ProductDto dto = ProductDto.builder().build();
            BeanUtils.copyProperties(po, dto);
            return dto;
        }).collect(Collectors.toList());
    }

}
