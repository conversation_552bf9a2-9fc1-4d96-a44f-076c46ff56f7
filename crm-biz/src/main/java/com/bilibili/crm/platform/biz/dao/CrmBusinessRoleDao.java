package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmBusinessRolePo;
import com.bilibili.crm.platform.biz.po.CrmBusinessRolePoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CrmBusinessRoleDao {
    long countByExample(CrmBusinessRolePoExample example);

    int deleteByExample(CrmBusinessRolePoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CrmBusinessRolePo record);

    int insertSelective(CrmBusinessRolePo record);

    List<CrmBusinessRolePo> selectByExample(CrmBusinessRolePoExample example);

    CrmBusinessRolePo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmBusinessRolePo record, @Param("example") CrmBusinessRolePoExample example);

    int updateByExample(@Param("record") CrmBusinessRolePo record, @Param("example") CrmBusinessRolePoExample example);

    int updateByPrimaryKeySelective(CrmBusinessRolePo record);

    int updateByPrimaryKey(CrmBusinessRolePo record);
}