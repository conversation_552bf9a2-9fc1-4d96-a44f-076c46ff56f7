package com.bilibili.crm.platform.biz.util;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.exception.code.CrmAppExceptionCode;
import com.bilibili.crm.platform.support.metrics.Metrics;
import com.bilibili.crm.platform.support.metrics.MetricsNames;
import com.google.common.collect.Maps;
import com.google.common.util.concurrent.ListeningExecutorService;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.Equator;
import org.apache.commons.lang3.builder.EqualsBuilder;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.slf4j.MDC;

import java.util.List;
import java.util.Map;
import java.util.function.Supplier;

import static com.bilibili.crm.platform.biz.util.ThreadPoolUtils.*;

@Slf4j
public class SimulateUtil {

    public enum Scene {
        ACCOUNT_LIST_QUERY,
        BSI_ACCOUNTS_QUERY,
        CONTRACT_BINDING_BSI,
        ACCOUNT_EXPORT
        ;
    }

    private static Map<Scene, ListeningExecutorService> executorServiceMap = Maps.newConcurrentMap();

    static {
        executorServiceMap.putIfAbsent(Scene.ACCOUNT_LIST_QUERY, executorSupplier(Scene.ACCOUNT_LIST_QUERY));
        executorServiceMap.putIfAbsent(Scene.BSI_ACCOUNTS_QUERY, executorSupplier(Scene.BSI_ACCOUNTS_QUERY));
        executorServiceMap.putIfAbsent(Scene.CONTRACT_BINDING_BSI, executorSupplier(Scene.CONTRACT_BINDING_BSI));
        executorServiceMap.putIfAbsent(Scene.ACCOUNT_EXPORT, executorSupplier(Scene.ACCOUNT_EXPORT));
    }

    private static PaladinConfig paladinConfig = SpringContextHolder.getBean(PaladinConfig.class);
    private static final OpenTelemetry openTelemetry = SpringContextHolder.getBean(OpenTelemetry.class);

    private static ListeningExecutorService executorSupplier(Scene scene) {
        return ThreadPoolUtils.newThreadPool("simulate_",
                DEFAULT_CORE_POOL_SIZE, DEFAULT_MAX_POOL_SIZE, DEFAULT_QUEUE_SIZE, DEFAULT_KEEP_ALIVE_TIME, (r, executor) -> {
                    log.warn("线程池已满，模拟查询异步差异对比任务丢弃");
                    Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.ASYNC_SIMULATE_ABORT);
                });
    }

    public static <T> List<T> asyncSimulate(Scene scene,
                                            Supplier<List<T>> supplier1,
                                            Supplier<List<T>> supplier2,
                                            String... ignoreFields) {

        List<T> actualResult;
        Supplier<List<T>> actualSupplier, simulateSupplier;

        // 决策模拟执行的supplier
        String isSwitch = paladinConfig.getString(scene.name() + "_SIMULATE_SWITCH");
        if (HotKey.SWITCH_ON.equals(isSwitch)) {
            log.info("模拟查询切换至模拟逻辑, scene:{}", scene);
            actualSupplier = supplier2;
            simulateSupplier = supplier1;
        } else {
            actualSupplier = supplier1;
            simulateSupplier = supplier2;
        }

        actualResult = actualSupplier.get();

        String parentTraceId = Span.current().getSpanContext().getTraceId();

        try {
            executorServiceMap.get(scene).execute(() -> {
                Tracer tracer = openTelemetry.getTracer("asyncSimulate");
                Span span = tracer.spanBuilder("asyncSimulate")
                        // 若异步任务与主线程不需要关联，需要设置noParent，使该Span成为RootSpan
                        .setNoParent()
                        // 可以在attribute中把主线程的traceId放入，以便排障、关联
                        .setAttribute("pub_trace_id", parentTraceId)
                        // 可添加自定义键值对
                        .setAttribute("scene", scene.name())
                        .startSpan();
                // 必须要调用 makeCurrent，否则Span并没有被真正激活
                try (Scope ignoredScope = span.makeCurrent();
                     MDC.MDCCloseable ignoredMdc = MDC.putCloseable("pub_trace_id", parentTraceId)) {
                    try {
                        List<T> simulateResult = simulateSupplier.get();
                        boolean isDiff = false;
                        if (actualResult.size() != simulateResult.size()) {
                            AlarmHelper.log("模拟查询数据总数存在差异", actualResult.size(), simulateResult.size());
                            Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.TOTAL_COUNT_DIFF);
                            isDiff = true;
                        }
                        if (!CollectionUtils.isEqualCollection(actualResult, simulateResult, generateEquator(ignoreFields))) {
                            AlarmHelper.log("模拟查询数据列表存在差异", actualResult, simulateResult);
                            Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.LIST_ITEM_DIFF);
                            isDiff = true;
                        }
                        if (isDiff) {
                            return;
                        }
                        Metrics.one(MetricsNames.SIMULATE, scene.name());
                        log.info("恭喜，模拟查询不存在数据差异🎉");
                    } catch (Throwable t) {
                        // 异常处理，标记错误状态后会被强制采样
                        span.setStatus(StatusCode.ERROR)
                                .recordException(t);
                        log.error("模拟查询异步差异对比任务执行失败", t);
                        Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.SYSTEM_ERROR);
                    }
                } finally {
                    // 需要包裹的代码块结束后必须调用 end
                    span.end();
                }
            });
        } catch (Throwable t) {
            log.error("模拟查询异步差异对比任务执行失败", t);
            Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.SYSTEM_ERROR);
        }

        return actualResult;
    }

    public static <T> PageResult<T> asyncPageSimulate(Scene scene,
                                                      Supplier<PageResult<T>> supplier1,
                                                      Supplier<PageResult<T>> supplier2,
                                                      String... ignoreFields) {

        PageResult<T> actualPageResult;
        Supplier<PageResult<T>> actualSupplier, simulateSupplier;

        // 决策模拟执行的supplier
        String isSwitch = paladinConfig.getString(scene.name() + "_SIMULATE_SWITCH");
        if (HotKey.SWITCH_ON.equals(isSwitch)) {
            log.info("模拟查询切换至模拟逻辑, scene:{}", scene);
            actualSupplier = supplier2;
            simulateSupplier = supplier1;
        } else {
            actualSupplier = supplier1;
            simulateSupplier = supplier2;
        }

        actualPageResult = actualSupplier.get();

        String parentTraceId = Span.current().getSpanContext().getTraceId();

        try {
            executorServiceMap.get(scene).execute(() -> {
                Tracer tracer = openTelemetry.getTracer("asyncSimulate");
                Span span = tracer.spanBuilder("asyncSimulate")
                        // 若异步任务与主线程不需要关联，需要设置noParent，使该Span成为RootSpan
                        .setNoParent()
                        // 可以在attribute中把主线程的traceId放入，以便排障、关联
                        .setAttribute("pub_trace_id", parentTraceId)
                        // 可添加自定义键值对
                        .setAttribute("scene", scene.name())
                        .startSpan();
                // 必须要调用 makeCurrent，否则Span并没有被真正激活
                try (Scope ignoredScope = span.makeCurrent();
                     MDC.MDCCloseable ignoredMdc = MDC.putCloseable("pub_trace_id", parentTraceId)) {
                    try {
                        PageResult<T> simulatePageResult = simulateSupplier.get();
                        boolean isDiff = false;
                        if (actualPageResult.getTotal() != simulatePageResult.getTotal()) {
                            AlarmHelper.log("模拟查询数据总数存在差异", actualPageResult.getTotal(), simulatePageResult.getTotal());
                            Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.TOTAL_COUNT_DIFF);
                            isDiff = true;
                        }
                        if (!CollectionUtils.isEqualCollection(actualPageResult.getRecords(), simulatePageResult.getRecords(), generateEquator(ignoreFields))) {
                            AlarmHelper.log("模拟查询数据列表存在差异", actualPageResult, simulatePageResult);
                            Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.LIST_ITEM_DIFF);
                            isDiff = true;
                        }
                        if (isDiff) {
                            return;
                        }
                        Metrics.one(MetricsNames.SIMULATE, scene.name());
                        log.info("恭喜，模拟查询不存在数据差异🎉");
                    } catch (Throwable t) {
                        // 异常处理，标记错误状态后会被强制采样
                        span.setStatus(StatusCode.ERROR)
                                .recordException(t);
                        log.error("模拟查询异步差异对比任务执行失败", t);
                        Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.SYSTEM_ERROR);
                    }
                } finally {
                    // 需要包裹的代码块结束后必须调用 end
                    span.end();
                }
            });
        } catch (Throwable t) {
            log.error("模拟查询异步差异对比任务执行失败", t);
            Metrics.oneErr(MetricsNames.SIMULATE, scene.name(), CrmAppExceptionCode.SYSTEM_ERROR);
        }

        return actualPageResult;
    }

    private static <T> Equator<T> generateEquator(final String... excludedFields) {
        return new Equator<T>() {
            @Override
            public boolean equate(Object o1, Object o2) {
                if (o1 == null && o2 == null) {
                    return true;
                }
                if (o1 == null || o2 == null) {
                    return false;
                }
                if (o1.getClass() != o2.getClass()) {
                    return false;
                }
                return EqualsBuilder.reflectionEquals(o1, o2, excludedFields);
            }

            @Override
            public int hash(Object o) {
                return HashCodeBuilder.reflectionHashCode(o, excludedFields);
            }
        };
    }
}
