package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.BsiOpportunityBizMappingPo;
import com.bilibili.crm.platform.biz.po.BsiOpportunityBizMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BsiOpportunityBizMappingDao {
    long countByExample(BsiOpportunityBizMappingPoExample example);

    int deleteByExample(BsiOpportunityBizMappingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(BsiOpportunityBizMappingPo record);

    int insertBatch(List<BsiOpportunityBizMappingPo> records);

    int insertUpdateBatch(List<BsiOpportunityBizMappingPo> records);

    int insert(BsiOpportunityBizMappingPo record);

    int insertUpdateSelective(BsiOpportunityBizMappingPo record);

    int insertSelective(BsiOpportunityBizMappingPo record);

    List<BsiOpportunityBizMappingPo> selectByExample(BsiOpportunityBizMappingPoExample example);

    BsiOpportunityBizMappingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") BsiOpportunityBizMappingPo record, @Param("example") BsiOpportunityBizMappingPoExample example);

    int updateByExample(@Param("record") BsiOpportunityBizMappingPo record, @Param("example") BsiOpportunityBizMappingPoExample example);

    int updateByPrimaryKeySelective(BsiOpportunityBizMappingPo record);

    int updateByPrimaryKey(BsiOpportunityBizMappingPo record);
}