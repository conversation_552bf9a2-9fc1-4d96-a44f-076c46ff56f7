package com.bilibili.crm.platform.biz.service.return_online.rule;

import com.bilibili.crm.platform.api.return_online.dto.RelationDetailItem;
import com.bilibili.crm.platform.api.return_online.enums.OperatorUseTypeEnum;
import com.bilibili.crm.platform.api.return_online.enums.RelationOperatorEnum;
import com.bilibili.crm.platform.api.return_online.enums.ReturnPolicyTypeEnum;
import com.bilibili.crm.platform.api.return_online.enums.ValueTypeEnum;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * 范围描述器
 *
 * <AUTHOR>
 * @date 2022/1/12 上午11:09
 */
@Slf4j
@Component
public class RangeDescriptor {

    /**
     * 获取定级档位描述(代理商定级和指标里的档位描述)
     *
     * @return
     */
    public String getGareDesc(Integer returnPolicyType, Integer operatorUseType, Integer valueType,
                              RelationDetailItem relationInfo, RelationOperatorEnum relationOperatorEnum, List<Object> params) {
        String desc = null;
        String indexDesc = null;
        String levelDesc = null;

        // 符号的中文填充参数描述
        String relationDesc = relationOperatorEnum.fillFormatChDesc(valueType, params);

        // 定级的都是季度消耗；
        // 指标的需要看值类型：代理商月度-完成；代理商季度-季度完成/季度环比增加；代理商评分-评分等级；广告主非游戏：季度日均；广告主游戏：空
        if (OperatorUseTypeEnum.CLASSIFY.getCode().equals(operatorUseType)) {
            indexDesc = "季度消耗";
            levelDesc = "定为" + relationInfo.getLevel();
        } else if (OperatorUseTypeEnum.PROTECTION.getCode().equals(operatorUseType)) {
            // 行业保护
            indexDesc = "环比增长";
            levelDesc = "时";
            return String.format("%s%s%s", indexDesc, relationDesc, levelDesc);
        } else {
            levelDesc = "返货" + relationInfo.getReturnAmountRate().toString() + "%";

            if (ReturnPolicyTypeEnum.AGENT_MONTH.getCode().equals(returnPolicyType)) {
                if (ValueTypeEnum.ABS.getCode().equals(valueType)) {
                    indexDesc = "月度完成";
                } else {
                    indexDesc = "完成";
                }
            } else if (ReturnPolicyTypeEnum.AGENT_QUARTER.getCode().equals(returnPolicyType)) {
                if (ValueTypeEnum.ABS.getCode().equals(valueType)) {
                    indexDesc = "季度完成";
                } else {
                    indexDesc = "季度环比增加";
                }
            } else if (ReturnPolicyTypeEnum.AGENT_CHANNEL_SCORE_QUARTER.getCode().equals(returnPolicyType)) {
                indexDesc = "评分等级为";
            } else if (ReturnPolicyTypeEnum.DIRECT_NOT_GAME.getCode().equals(returnPolicyType)) {
                indexDesc = "季度日均";
            } else if (ReturnPolicyTypeEnum.DIRECT_ANDROID_GAME.getCode().equals(returnPolicyType)) {
                indexDesc = "";
            }
        }

        // 定级：季度消耗 大于 10万，定位 A
        if (ReturnPolicyTypeEnum.DIRECT_ANDROID_GAME.getCode().equals(returnPolicyType)) {
            desc = String.format("%s%s%s", indexDesc, relationDesc, levelDesc);
        } else {
            desc = String.format("%s%s，%s", indexDesc, relationDesc, levelDesc);
        }
        return desc;
    }
}
