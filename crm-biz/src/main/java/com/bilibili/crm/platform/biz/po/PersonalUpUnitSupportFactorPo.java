package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class PersonalUpUnitSupportFactorPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    private Integer accountId;

    private Integer unitId;

    private Integer ocpxTarget;

    private Float supportFactor;

    private Integer deductionSign;

    private String logDate;

    private Timestamp ctime;

    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}