package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmReturnCorrectDataPo;
import com.bilibili.crm.platform.biz.po.CrmReturnCorrectDataPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmReturnCorrectDataDao {
    long countByExample(CrmReturnCorrectDataPoExample example);

    int deleteByExample(CrmReturnCorrectDataPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmReturnCorrectDataPo record);

    int insertBatch(List<CrmReturnCorrectDataPo> records);

    int insertUpdateBatch(List<CrmReturnCorrectDataPo> records);

    int insert(CrmReturnCorrectDataPo record);

    int insertUpdateSelective(CrmReturnCorrectDataPo record);

    int insertSelective(CrmReturnCorrectDataPo record);

    List<CrmReturnCorrectDataPo> selectByExample(CrmReturnCorrectDataPoExample example);

    CrmReturnCorrectDataPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmReturnCorrectDataPo record, @Param("example") CrmReturnCorrectDataPoExample example);

    int updateByExample(@Param("record") CrmReturnCorrectDataPo record, @Param("example") CrmReturnCorrectDataPoExample example);

    int updateByPrimaryKeySelective(CrmReturnCorrectDataPo record);

    int updateByPrimaryKey(CrmReturnCorrectDataPo record);
}