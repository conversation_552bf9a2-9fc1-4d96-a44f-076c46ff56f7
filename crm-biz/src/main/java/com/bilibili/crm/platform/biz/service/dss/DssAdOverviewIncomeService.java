package com.bilibili.crm.platform.biz.service.dss;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.wallet.bean.DssAdOverviewEsBean;
import com.bilibili.crm.biz.wallet.bean.DssAdOverviewQuery;
import com.bilibili.crm.biz.wallet.olap.DssAdOverviewOlapService;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.biz_common.olap.migrate.enums.MigrateBizScene;
import com.bilibili.crm.biz_common.olap.migrate.util.GenericComparator;
import com.bilibili.crm.biz_common.olap.migrate.util.MigrateUtil;
import com.bilibili.crm.platform.api.account.service.IBizIndustryService;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.company.service.ICompanyGroupService;
import com.bilibili.crm.platform.api.company.service.ICorporationGroupService;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.dss.dto.DssAdOverviewDto;
import com.bilibili.crm.platform.api.dss.dto.DssAdOverviewQueryDto;
import com.bilibili.crm.platform.api.dss.dto.RtbReturnDto;
import com.bilibili.crm.platform.api.dss.dto.RtbReturnDtoTotalWithListDto;
import com.bilibili.crm.platform.api.dss.service.IDssAdOverviewIncomeService;
import com.bilibili.crm.platform.api.es.agg.helper.bean.AggregationCondition;
import com.bilibili.crm.platform.api.stat.dto.AdvertiserLaunchDataDto;
import com.bilibili.crm.platform.api.stat.dto.PerformanceAdQueryDto;
import com.bilibili.crm.platform.biz.clickhouse.dao.AdsAdCrmShikouFentan1dDDao;
import com.bilibili.crm.platform.biz.clickhouse.dao.AdsCrmCouponShikouAggSerialNumberFentan1dDao;
import com.bilibili.crm.platform.biz.extractor.*;
import com.bilibili.crm.platform.biz.helper.PerformanceAdHelper;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsAdCrmShikouFentan1dDPo;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsAdCrmShikouFentan1dDPoExample;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmCouponShikouAggSerialNumberFentan1dPo;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.service.dss.helper.DssAdOverviewHelper;
import com.bilibili.crm.platform.biz.service.stat.PerformanceAdService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.DssResourceType;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.LocationPlatform;
import com.bilibili.crm.platform.common.SaleCategoryEnum;
import com.bilibili.crm.platform.utils.AdUtils;
import com.bilibili.crm.platform.utils.CrmPageUtils;
import com.bilibili.report.platform.api.dto.StatDto;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.aggregations.AbstractAggregationBuilder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.ResultsExtractor;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.elasticsearch.index.query.QueryBuilders.*;

@Slf4j
@Service
public class DssAdOverviewIncomeService implements IDssAdOverviewIncomeService {
    @Autowired
    private DssAdOverviewHelper dssAdOverviewHelper;
    @Resource(name = "elasticsearchTemplate_bi")
    private ElasticsearchTemplate elasticsearchTemplate;

    @Value("#{'${achieve.market.dept.id:1,95,101}'.split(',')}")
    private List<Integer> dssRtbDeptIds;

    @Resource
    private AdsAdCrmShikouFentan1dDDao adsAdCrmShikouFentanDao;
    @Autowired
    private PerformanceAdHelper performanceAdHelper;
    @Autowired
    private AdsCrmCouponShikouAggSerialNumberFentan1dDao adsCrmCouponShikouAggSerialNumberFentan1dDao;

    /**
     * BI超播导出
     */
    @Override
    public List<DssAdOverviewDto> queryDssAdOverviewExport(DssAdOverviewQueryDto queryDto) {
        return Lists.newArrayList();
    }

    /**
     * BI超播导出
     */
    @Override
    public List<DssAdOverviewDto> queryDssAdOverviewExportFromCK(DssAdOverviewQueryDto queryDto) {
        if (!queryDto.isNonDep()) {
            queryDto.setDepartmentId(dssRtbDeptIds);
        }
        AdsAdCrmShikouFentan1dDPoExample example = new AdsAdCrmShikouFentan1dDPoExample();
        AdsAdCrmShikouFentan1dDPoExample.Criteria criteria = example.createCriteria()
                .andTradeDateBetween(CrmUtils.formatDate(queryDto.getFromTime(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT), CrmUtils.formatDate(queryDto.getToTime(), CrmUtils.YYYYMMDD_WITHOUT_SPLIT))
                .andLogDateBetween(CrmUtils.formatDate(Utils.getSomeDayAgo(queryDto.getFromTime(), 3), CrmUtils.YYYYMMDD_WITHOUT_SPLIT), CrmUtils.formatDate(Utils.getSomeDayAfter(queryDto.getToTime(), 3), CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
        List<Long> accIds = new ArrayList<>();
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getAccountId())) {
            queryDto.getAccountId().forEach(r -> {
                        try {
                            long longAccId = Long.parseLong(String.valueOf(r));
                            accIds.add(longAccId);
                        } catch (Exception e) {
                            log.error("error ", e);
                        }
                    }
            );
        }
        ObjectUtils.setList(() -> accIds, criteria::andAccountIdIn);
        ObjectUtils.setList(() -> queryDto.getDepartmentId(), criteria::andDepartmentIdIn);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getPlatformId())) {
            ObjectUtils.setList(() -> queryDto.getPlatformId().stream().map(r -> LocationPlatform.getByCode(r).getDesc()).collect(Collectors.toList()), criteria::andPlatformNameIn);
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getSrcType())) {
            ObjectUtils.setList(() -> queryDto.getSrcType().stream().map(String::valueOf).collect(Collectors.toList()), criteria::andSrcTypeIn);
        }
        List<AdsAdCrmShikouFentan1dDPo> adsAdCrmShikouFentanPos = adsAdCrmShikouFentanDao.selectByExample(example);
        List<DssAdOverviewDto> result = adsAdCrmShikouFentanPos.stream().map(
                r -> DssAdOverviewDto.builder()
                        .accountId(r.getAccountId().intValue())
                        .agentId(r.getAgentId().intValue())
                        .srcType(Integer.parseInt(r.getSrcType()))
                        .platformId(LocationPlatform.getByDesc(r.getPlatformName()).getCode())
                        .date(Utils.getTimeStampWithFormat(r.getTradeDate(), "yyyyMMdd"))
                        .showCount(r.getPvTotal())
                        .clickCount(r.getClickTotal())
                        .totalConsume(Utils.fromFenToYuan(new BigDecimal(r.getFantuiPtfhShikou().toString())
                                .add(new BigDecimal(r.getFantuiXianjinShikou().toString()))
                                .add(new BigDecimal(r.getFantuiShouXinShikou().toString()))
                                .add(new BigDecimal(r.getFantuiZxfhShikou().toString()))))
                        .cashAmount(Utils.fromFenToYuan(new BigDecimal(r.getFantuiXianjinShikou().toString())))
                        .creditAmount(Utils.fromFenToYuan(new BigDecimal(r.getFantuiShouXinShikou().toString())))
                        .redPacketAmount(Utils.fromFenToYuan(new BigDecimal(r.getFantuiPtfhShikou().toString())))
                        .specialRedPacketAmount(Utils.fromFenToYuan(new BigDecimal(r.getFantuiZxfhShikou().toString())))
                        .isTakeGoods(StringUtils.isBlank(r.getIsTakeGoods()) ? r.getIsTakeGoods() : IsValid.TRUE.getCode().toString().equals(r.getIsTakeGoods()) ? IsValid.TRUE.getDesc() : IsValid.FALSE.getDesc())
                        .build()
        ).collect(Collectors.toList());
        return result;
    }


    @Override
    public List<RtbReturnDto> exportRtbReturnFlowList(Operator operator, PerformanceAdQueryDto query) {

        Assert.notNull(query, "查询条件不可为空");
        query.setPageInfo(Page.valueOf(1, 1000000));
        List<AdsAdCrmShikouFentan1dDPo> adsAdCrmShikouFentanPos = adsAdCrmShikouFentanDao.groupByAccountIdAndDate(query);
        List<RtbReturnDto> data = adsAdCrmShikouFentanPos.stream().map(
                item -> {
                    RtbReturnDto dataDto = (RtbReturnDto) RtbReturnDto.builder()
                            .accountId(item.getAccountId().intValue())
                            .agentId(item.getAgentId().intValue())
                            .cashConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiXianjinShikou().toString())))
                            .creditConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiShouXinShikou().toString())))
                            .redPacketConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiPtfhShikou().toString())))
                            .specialRedPacketConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiZxfhShikou().toString())))
                            .build();

                    dataDto.setDateStr(performanceAdHelper.getRangeStr(query, Utils.getTimeStampWithFormat(item.getTradeDate(), "yyyyMMdd")));
                    dataDto.setDate(Utils.getTimeStampWithFormat(item.getTradeDate(), "yyyyMMdd"));
                    dataDto.setShowCount(item.getPvTotal());
                    dataDto.setClickCount(item.getClickTotal());
                    dataDto.setIsTakeGoods(StringUtils.isBlank(item.getIsTakeGoods()) ? item.getIsTakeGoods() : IsValid.TRUE.getCode().toString().equals(item.getIsTakeGoods()) ? IsValid.TRUE.getDesc() : IsValid.FALSE.getDesc());
                    dataDto.setSalesCategory(StringUtils.isBlank(item.getSalesCategory()) ? item.getSalesCategory() : SaleCategoryEnum.getByCode(Integer.parseInt(item.getSalesCategory())).getDesc());
                    dataDto.setPlatformName(item.getPlatformName());
                    dataDto.setSrcType(StringUtils.isBlank(item.getSrcType()) ? item.getSalesCategory() : DssResourceType.getByCode(Integer.parseInt(item.getSrcType())).getDesc());
                    dataDto.setTotalConsume(dataDto.getCashConsume().add(dataDto.getRedPacketConsume()).add(dataDto.getSpecialRedPacketConsume()));
                    dataDto.setCtr(AdUtils.getCtr(BigDecimal.valueOf(dataDto.getClickCount()), BigDecimal.valueOf(dataDto.getShowCount())));
                    dataDto.setCpc(AdUtils.getCpc(dataDto.getTotalConsume(), BigDecimal.valueOf(dataDto.getClickCount())));
                    dataDto.setEcpm(AdUtils.getEcpm(dataDto.getTotalConsume(), BigDecimal.valueOf(dataDto.getShowCount())));
                    return dataDto;
                }).collect(Collectors.toList());
        return data;
    }

    @Override
    public List<RtbReturnDto> exportRtbReturnFlowListByCk(Operator operator, PerformanceAdQueryDto query)  {
        Assert.notNull(query, "查询条件不可为空");
        query.setPageInfo(Page.valueOf(1, 1000000));
        log.info("exportRtbReturnFlowListByCk query = {}", query);
        List<AdsCrmCouponShikouAggSerialNumberFentan1dPo> adsAdCrmShikouFentanPos = adsCrmCouponShikouAggSerialNumberFentan1dDao.groupByAccountIdAndDate(query);
        log.info("adsAdCrmShikouFentanPos result size = {}", adsAdCrmShikouFentanPos.size());
        List<RtbReturnDto> data = adsAdCrmShikouFentanPos.stream().map(
                item -> {
                    RtbReturnDto dataDto = (RtbReturnDto) RtbReturnDto.builder()
                            .accountId(item.getAccountId().intValue())
                            .agentId(item.getAgentId().intValue())
                            .cashConsume(Utils.fromFenToYuan(item.getFantuiXianjinShikou()))
                            .creditConsume(Utils.fromFenToYuan(item.getFantuiShouxinShikou()))
                            .redPacketConsume(Utils.fromFenToYuan(item.getFantuiPtfhShikou()))
                            .specialRedPacketConsume(Utils.fromFenToYuan(item.getFantuiZxfhShikou()))
                            .build();

                    dataDto.setDateStr(performanceAdHelper.getRangeStr(query, Utils.getTimeStampWithFormat(item.getTradeDate(), "yyyyMMdd")));
                    dataDto.setDate(Utils.getTimeStampWithFormat(item.getTradeDate(), "yyyyMMdd"));
                    dataDto.setShowCount(item.getPvTotal());
                    dataDto.setClickCount(item.getClickTotal());
                    dataDto.setIsTakeGoods(StringUtils.isBlank(item.getIsTakeGoods()) ? item.getIsTakeGoods() : IsValid.TRUE.getCode().toString().equals(item.getIsTakeGoods()) ? IsValid.TRUE.getDesc() : IsValid.FALSE.getDesc());
                    dataDto.setSalesCategory(StringUtils.isBlank(item.getSalesCategory()) ? item.getSalesCategory() : SaleCategoryEnum.getByCode(Integer.parseInt(item.getSalesCategory())).getDesc());
                    dataDto.setPlatformName(item.getPlatformName());
                    dataDto.setSrcType(StringUtils.isBlank(item.getSrcType()) ? item.getSalesCategory() : DssResourceType.getByCode(Integer.parseInt(item.getSrcType())).getDesc());
                    dataDto.setTotalConsume(dataDto.getCashConsume().add(dataDto.getRedPacketConsume()).add(dataDto.getSpecialRedPacketConsume()));
                    dataDto.setCtr(AdUtils.getCtr(BigDecimal.valueOf(dataDto.getClickCount()), BigDecimal.valueOf(dataDto.getShowCount())));
                    dataDto.setCpc(AdUtils.getCpc(dataDto.getTotalConsume(), BigDecimal.valueOf(dataDto.getClickCount())));
                    dataDto.setEcpm(AdUtils.getEcpm(dataDto.getTotalConsume(), BigDecimal.valueOf(dataDto.getShowCount())));

                    dataDto.setAccountName(item.getAccountName());
                    dataDto.setIsInner(item.getIsInner().intValue());
                    dataDto.setAccountCustomerId(stringToInteger(item.getCustomerId()));
                    dataDto.setAccountCustomerName(item.getCustomerName());
                    dataDto.setUserType(stringToInteger(item.getUserType()));
                    dataDto.setAgentName(item.getAgentName());
                    dataDto.setAgentCustomerId(stringToInteger(item.getAgentCustomerId()));
                    dataDto.setAgentCustomerName(item.getAgentCustomerName());
                    dataDto.setProductId(stringToInteger(item.getProductId()));
                    dataDto.setProductName(item.getProductName());
                    dataDto.setGroupId(stringToInteger(item.getGroupId()));
                    dataDto.setGroupName(item.getGroupName());
                    dataDto.setDepartmentName(item.getDepartmentName());
                    dataDto.setCustomerUnitedFirstIndustryName(item.getCustomerUnitedFirstIndustryName());
                    dataDto.setCustomerUnitedSecondIndustryName(item.getCustomerUnitedSecondIndustryName());
                    dataDto.setCustomerUnitedThirdIndustryName(item.getCustomerUnitedThirdIndustryName());
                    dataDto.setCpATargetTypeName(item.getCpaTargetTypeName());
                    dataDto.setSecondCpATargetTypeName(item.getSecondCpaTargetTypeName());
                    dataDto.setPromotionPurposeTypeName(item.getPromotionPurposeTypeName());
                    dataDto.setUnitPromotionPurposeTypeName(item.getUnitPromotionPurposeTypeName());
                    dataDto.setBusinessDomainName(item.getBusinessDomainName());
                    dataDto.setBusinessDomainNameNew(item.getBusinessDomainNameNew());
                    return dataDto;
                }).collect(Collectors.toList());
        return data;
    }

    private Integer stringToInteger(String num) {
        try {
            if (StringUtils.isBlank(num)) {
                return null;
            }
            return Integer.parseInt(num);
        } catch (Exception e) {
            log.warn("stringToInteger error", e);
            return 0;
        }
    }


    @Override
    public RtbReturnDtoTotalWithListDto rtbReturnFlowList(Operator operator, PerformanceAdQueryDto query) {

        Assert.notNull(query, "查询条件不可为空");
        if (null == query.getPageInfo()) {
            query.setPageInfo(Page.valueOf(1, 20));
        }

        //// TODO: 10/17/23 测试环境mock 可以删除
        PerformanceAdQueryDto queryCopy = new PerformanceAdQueryDto();
        BeanUtils.copyProperties(query, queryCopy);
        if ("uat".equals(System.getProperty("deploy_env"))) {
            queryCopy.setFromTime(null);
            queryCopy.setToTime(null);
        }

        List<AdsAdCrmShikouFentan1dDPo> adsAdCrmShikouFentanPos = adsAdCrmShikouFentanDao.groupByAccountIdAndDate(queryCopy);
        List<RtbReturnDto> data = adsAdCrmShikouFentanPos.stream().map(
                item -> {
                    RtbReturnDto dataDto = (RtbReturnDto) RtbReturnDto.builder()
                            .accountId(item.getAccountId().intValue())
                            .agentId(item.getAgentId().intValue())
                            .cashConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiXianjinShikou().toString())))
                            .creditConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiShouXinShikou().toString())))
                            .redPacketConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiPtfhShikou().toString())))
                            .specialRedPacketConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiZxfhShikou().toString())))
                            .totalCashConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiXianjinShikou().toString())))
                            .totalCreditConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiShouXinShikou().toString())))
                            .totalRedPacketConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiPtfhShikou().toString())))
                            .totalSpecialRedPacketConsume(Utils.fromFenToYuan(new BigDecimal(item.getFantuiZxfhShikou().toString())))
                            .build();
                    dataDto.setDateStr(performanceAdHelper.getRangeStr(query, Utils.getTimeStampWithFormat(item.getTradeDate(), "yyyyMMdd")));
                    dataDto.setDate(performanceAdHelper.getfirstDay(query, Utils.getTimeStampWithFormat(item.getTradeDate(), "yyyyMMdd")));
                    dataDto.setShowCount(item.getPvTotal());
                    dataDto.setClickCount(item.getClickTotal());
                    dataDto.setIsTakeGoods(StringUtils.isBlank(item.getIsTakeGoods()) ? item.getIsTakeGoods() : IsValid.TRUE.getCode().toString().equals(item.getIsTakeGoods()) ? IsValid.TRUE.getDesc() : IsValid.FALSE.getDesc());
                    dataDto.setSalesCategory(StringUtils.isBlank(item.getSalesCategory()) ? item.getSalesCategory() : SaleCategoryEnum.getByCode(Integer.parseInt(item.getSalesCategory())).getDesc());
                    dataDto.setPlatformName(item.getPlatformName());
                    dataDto.setSrcType(StringUtils.isBlank(item.getSrcType()) ? item.getSrcType() : DssResourceType.getByCode(Integer.parseInt(item.getSrcType())).getDesc());
                    dataDto.setTotalConsume(dataDto.getCashConsume().add(dataDto.getRedPacketConsume()).add(dataDto.getSpecialRedPacketConsume()));
                    dataDto.setCtr(AdUtils.getCtr(BigDecimal.valueOf(dataDto.getClickCount()), BigDecimal.valueOf(dataDto.getShowCount())));
                    dataDto.setCpc(AdUtils.getCpc(dataDto.getTotalConsume(), BigDecimal.valueOf(dataDto.getClickCount())));
                    dataDto.setEcpm(AdUtils.getEcpm(dataDto.getTotalConsume(), BigDecimal.valueOf(dataDto.getShowCount())));
                    return dataDto;
                }).collect(Collectors.toList());

//        List<Integer> accIds = data.stream().filter(r -> SalesType.CPM.getDesc().equals(r.getSalesCategory())).map(RtbReturnDto::getAccountId).distinct().collect(Collectors.toList());
//        cluePassAndFansBuild(query, data, accIds);

        //排序

        // 对总数据进行内存分页
        PageResult<RtbReturnDto> adPageResult = CrmPageUtils.extractorWithPage(data, query.getPageInfo().getPage(), query.getPageInfo().getPageSize());

        RtbReturnDto total = calTotal(adPageResult);
        //总计
        return RtbReturnDtoTotalWithListDto.builder().pageData(adPageResult).total(total).build();
    }


    private RtbReturnDto calTotal(PageResult<RtbReturnDto> adPageResult) {
        RtbReturnDto total = (RtbReturnDto) RtbReturnDto.builder()
                .cashConsume(BigDecimal.ZERO)
                .totalConsume(BigDecimal.ZERO)
                .creditConsume(BigDecimal.ZERO)
                .totalCreditConsume(BigDecimal.ZERO)
                .redPacketConsume(BigDecimal.ZERO)
                .specialRedPacketConsume(BigDecimal.ZERO)
                .totalConsume(BigDecimal.ZERO)
                .build();
        for (RtbReturnDto rtbReturnDto : adPageResult.getRecords()) {
            total.setCashConsume(total.getCashConsume().add(rtbReturnDto.getCashConsume()));
            total.setCreditConsume(total.getCreditConsume().add(rtbReturnDto.getCreditConsume()));
            total.setRedPacketConsume(total.getRedPacketConsume().add(rtbReturnDto.getRedPacketConsume()));
            total.setSpecialRedPacketConsume(total.getSpecialRedPacketConsume().add(rtbReturnDto.getSpecialRedPacketConsume()));
            total.setTotalConsume(total.getTotalConsume().add(rtbReturnDto.getTotalConsume()));
        }

        return total;
    }


    /**
     * BI查询广告主
     */
    @Override
    public List<Integer> queryDssAdOverviewAccount(DssAdOverviewQueryDto queryDto) {

        List<Integer> result = this.queryAccountFromESByQueryDto(queryDto, new DssAdOverviewAccountExtractor());

        return result;
    }

    /**
     * BI查询广告主消耗,数据源最低维度，按天
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DssAdOverviewDto> queryDssAdOverviewDayData(DssAdOverviewQueryDto queryDto) {
        return Lists.newArrayList();
    }


    /**
     * BI查询广告主消耗, 查询小时表
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DssAdOverviewDto> queryDssAdOverviewHourData(DssAdOverviewQueryDto queryDto) {

        List<DssAdOverviewDto> result = this.queryHourFromESByQueryDto(queryDto, new DssAdOverviewDataExtractor());

        return filterEmptyObject(result);
    }

    private List<DssAdOverviewDto> filterEmptyObject(List<DssAdOverviewDto> result) {
        if (CollectionUtils.isEmpty(result)) {
            return Collections.emptyList();
        }
        return result.stream().filter(dto -> !dto.isEmptyObject()).collect(Collectors.toList());
    }

    /**
     * 行业分布
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DssAdOverviewDto> queryCategoryFromESByQueryDto(DssAdOverviewQueryDto queryDto) {
        return Lists.newArrayList();
    }

    /**
     * 资源位分布
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<DssAdOverviewDto> queryDssAdOverviewResourceData(DssAdOverviewQueryDto queryDto) {
        return Lists.newArrayList();
    }


    /**
     * dss_ad明细查询，适用于小范围数据量查询 （count<10000）
     *
     * @param queryDto
     * @param cls
     * @param <T>
     * @return
     */
    @Override
    public <T> List<T> queryDssDetailESByQueryDto(DssAdOverviewQueryDto queryDto, Class<T> cls) {
        BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryDto);

        AbstractAggregationBuilder aggregationBuilder = dssAdOverviewHelper.buildAggregation(queryDto);

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                //.withSort(new FieldSortBuilder("date").order(SortOrder.ASC))
                .withIndices("dss_ad_overview")
                .withTypes("dss_ad_data")
                .withQuery(boolQueryBuilder)
                .addAggregation(aggregationBuilder)
                .withPageable(PageRequest.of(0, 10000))
                .build();

        List<T> esDssAdOverviewPos = elasticsearchTemplate.queryForList(searchQuery, cls);
        return esDssAdOverviewPos;
    }

    @Override
    public Boolean checkCkDataIsReady(Timestamp dateBegin, Timestamp dateEnd) {
        PerformanceAdQueryDto queryDto = new PerformanceAdQueryDto();
        queryDto.setFromTime(dateBegin);
        queryDto.setToTime(dateEnd);
        Integer countDataLimit100 = adsCrmCouponShikouAggSerialNumberFentan1dDao.countAdAdsGroupByDate(queryDto);
        if (Utils.isPositive(countDataLimit100)) {
            return true;
        }
        return false;
    }

    /**
     * 广告主点展消从es中查询并聚合 es索引dss2_hour
     *
     * @param queryDto  查询条件
     * @param extractor es查询结果提取器
     * @param <T>
     * @return
     */
    private <T> List<T> queryHourFromESByQueryDto(DssAdOverviewQueryDto queryDto, ResultsExtractor<List<T>> extractor) {
        BoolQueryBuilder boolQueryBuilder = buildBoolQueryBuilder(queryDto);

        AbstractAggregationBuilder aggregationBuilder = dssAdOverviewHelper.buildAggregation(queryDto);

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices("dss2_hour")
                .withTypes("dss_ad_data")
                .withQuery(boolQueryBuilder)
                .addAggregation(aggregationBuilder)
                .build();

        return elasticsearchTemplate.query(searchQuery, extractor);
    }


    // 仿照 DssAdOverviewResourceExtractor 写的convert
    private DssAdOverviewDto convertToResourceDto(DssAdOverviewEsBean esBean) {
        DssAdOverviewDto dto = DssAdOverviewDto.builder()
                .totalConsume(Utils.fromFenToYuan(esBean.getExternalCost()))
                .build();
        return dto;
    }

    /**
     * BI-ES所有account
     */
    public <T> List<T> queryAccountFromESByQueryDto(DssAdOverviewQueryDto queryDto, ResultsExtractor<List<T>> extractor) {

        BoolQueryBuilder boolQueryBuilder = boolQuery();

        boolQueryBuilder.must(rangeQuery("date")
                .from(queryDto.getFromTime().getTime())
                .to(queryDto.getToTime().getTime())
        );
        //是否广告位
        boolQueryBuilder.must(termQuery("isAdLoc", 1));

        AbstractAggregationBuilder aggregationBuilder = dssAdOverviewHelper.aggregationWithAccount();

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(queryDto.getIndex())
                .withTypes("dss_ad_data")
                .withQuery(boolQueryBuilder)
                .addAggregation(aggregationBuilder)
                .build();

        return elasticsearchTemplate.query(searchQuery, extractor);
    }

    private BoolQueryBuilder buildBoolQueryBuilder(DssAdOverviewQueryDto queryDto) {

        if (!queryDto.isNonDep()) {
            queryDto.setDepartmentId(dssRtbDeptIds);
        }

        BoolQueryBuilder boolQueryBuilder = boolQuery();

        boolQueryBuilder.must(rangeQuery("date")
                .from(queryDto.getFromTime().getTime())
                .to(queryDto.getToTime().getTime())
        );
        //是否广告位
        boolQueryBuilder.must(termQuery("isAdLoc", 1));
        //效果营销部
        //boolQueryBuilder.must(termQuery("departmentId", dssRtbDeptIds));

        if (!CollectionUtils.isEmpty(queryDto.getAccountId())) {
            boolQueryBuilder.must(termsQuery("accountId", queryDto.getAccountId()));
        }

        if (!CollectionUtils.isEmpty(queryDto.getDepartmentId())) {
            boolQueryBuilder.must(termsQuery("departmentId", queryDto.getDepartmentId()));
        }

        if (!CollectionUtils.isEmpty(queryDto.getAgentId())) {
            boolQueryBuilder.must(termsQuery("crmAgentId", queryDto.getAgentId()));
        }

        if (!CollectionUtils.isEmpty(queryDto.getSalesType())) {
            boolQueryBuilder.must(termsQuery("salesType", queryDto.getSalesType()));
        }

        if (!CollectionUtils.isEmpty(queryDto.getSrcType())) {
            boolQueryBuilder.must(termsQuery("srcType", queryDto.getSrcType()));
        }

        if (!CollectionUtils.isEmpty(queryDto.getPlatformId())) {
            boolQueryBuilder.must(termsQuery("platformId", queryDto.getPlatformId()));
        }

        if (null != queryDto.getScheduleId()) {
            boolQueryBuilder.must(termQuery("scheduleId", queryDto.getScheduleId()));
        }

        if (null != queryDto.getCrmOrderId()) {
            boolQueryBuilder.must(termQuery("crmOrderId", queryDto.getCrmOrderId()));
        }

        return boolQueryBuilder;
    }


}
