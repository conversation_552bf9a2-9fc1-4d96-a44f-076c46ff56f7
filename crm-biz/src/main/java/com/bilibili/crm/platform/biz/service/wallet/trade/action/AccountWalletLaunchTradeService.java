package com.bilibili.crm.platform.biz.service.wallet.trade.action;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.biz.bean.AccountWalletTradingBean;
import com.bilibili.crm.platform.biz.dao.CrmLaunchTradeRecordDao;
import com.bilibili.crm.platform.biz.po.CrmLaunchTradeRecordPo;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.wallet.LaunchBizType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: brady
 * @time: 2022/4/27 12:51 下午
 */
@Service
@Slf4j
public class AccountWalletLaunchTradeService {
    @Autowired
    private CrmLaunchTradeRecordDao launchTradeRecordDao;
    @Autowired
    private IFastQueryAccountService fastQueryAccountService;

    public Integer saveTradeRecode(AccountWalletTradingBean dto, LaunchBizType bizType) {
        //account
        AccountBaseDto account = fastQueryAccountService.fetchOne(dto.getAccountId(), AccountFieldMapping.accountId, AccountFieldMapping.dependencyAgentId);

        CrmLaunchTradeRecordPo po = CrmLaunchTradeRecordPo.builder()
                .accountId(dto.getAccountId())
                .masAccountId(dto.getAdAccountId())
                .agentId(null != account ? account.getDependencyAgentId() : 0)
                .amount(dto.getAmountFen())
                .objId(dto.getObjId())
                .salesType(dto.getSalesType())
                .serialNumber(dto.getBusinessId())
                .tradeDate(Utils.getBeginOfDay(dto.getTradeDate()))
                .bizType(bizType.getType())
                .optType(dto.getWalletTradingOperateType())
                .build();

        return launchTradeRecordDao.insertSelective(po);
    }
}
