package com.bilibili.crm.platform.biz.read.only.dao;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerBizMidMappingDao;
import com.bilibili.crm.platform.biz.po.CustomerBizMidMappingPo;
import com.bilibili.crm.platform.biz.po.CustomerBizMidMappingPoExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/12/21 6:41 下午
 */
@Slf4j
@Repository
public class CustomerBizMidMappingRepo {

    @Autowired
    private CustomerBizMidMappingDao customerBizMidMappingDao;

    /**
     * 保存客户层级 mid
     *
     * @param customerId
     * @param mid
     * @param bizType
     */
    public void insertOrUpdateBizMidMapping(Integer customerId, Long mid, Integer bizType) {
        log.info("=====> insertOrUpdateBizMidMapping, customerId:{}, mid:{}, bizType:{}", customerId, mid, bizType);
        CustomerBizMidMappingPo customerBizMidMappingPo = CustomerBizMidMappingPo.builder()
                .customerId(customerId)
                .mid(mid)
                .bizType(bizType)
                .isDeleted(IsDeleted.VALID.getCode())
                .build();
        customerBizMidMappingDao.insertUpdateSelective(customerBizMidMappingPo);
    }

    /**
     * 根据 mid 查询列表
     *
     * @param midInt
     * @return
     */
    public List<CustomerBizMidMappingPo> queryListByMid(Long midInt) {
        CustomerBizMidMappingPoExample example = new CustomerBizMidMappingPoExample();
        example.createCriteria().andMidEqualTo(midInt).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return customerBizMidMappingDao.selectByExample(example);
    }

    public CustomerBizMidMappingPo getByCustomerId(Integer customerId) {
        if (customerId == null) {
            return null;
        }
        CustomerBizMidMappingPoExample example = new CustomerBizMidMappingPoExample();
        example.createCriteria().andCustomerIdEqualTo(customerId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CustomerBizMidMappingPo> customerBizMidMappingPos = customerBizMidMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(customerBizMidMappingPos)) {
            return null;
        }
        return customerBizMidMappingPos.get(0);
    }

    public void deleteCustomerMidByCustomerId(Integer id, Integer bizType) {
        CustomerBizMidMappingPoExample example = new CustomerBizMidMappingPoExample();
        example.createCriteria().andCustomerIdEqualTo(id).andBizTypeEqualTo(bizType);
        customerBizMidMappingDao.deleteByExample(example);
    }
}
