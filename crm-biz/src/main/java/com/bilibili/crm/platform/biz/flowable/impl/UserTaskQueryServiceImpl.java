package com.bilibili.crm.platform.biz.flowable.impl;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.biz.flowable.IUserTaskQueryService;
import com.bilibili.crm.platform.biz.repo.flowable.FlowCcTaskRepo;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.flowable.engine.HistoryService;
import org.flowable.engine.TaskService;
import org.flowable.task.api.Task;
import org.flowable.task.api.history.HistoricTaskInstance;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/28 14:45
 */
@Slf4j
@Service
public class UserTaskQueryServiceImpl implements IUserTaskQueryService {

    @Resource
    private TaskService taskService;

    @Resource
    private HistoryService historyService;

    @Resource
    private FlowCcTaskRepo flowCcTaskRepo;

    @Override
    public boolean hasPermission(Operator operator, String procInstId) {
        List<Task> tasks = taskService.createTaskQuery()
                .processInstanceId(procInstId)
                .taskCandidateOrAssigned(operator.getOperatorName())
                .list();

        return CollectionUtils.isNotEmpty(tasks);
    }

    @Override
    public List<String> todoTasks(Operator operator, String flowKey) {
        List<Task> tasks = taskService.createTaskQuery()
                .processDefinitionKey(flowKey)
                .taskCandidateOrAssigned(operator.getOperatorName())
                .list();

        return CrmUtils.convert(tasks, Task::getProcessInstanceId);
    }

    @Override
    public List<String> doneTasks(Operator operator, String flowKey) {
        List<HistoricTaskInstance> tasks = historyService.createHistoricTaskInstanceQuery()
                .processDefinitionKey(flowKey)
                .taskAssignee(operator.getOperatorName())
                // 已完成的用户任务
                .finished()
                .list();

        return CrmUtils.convert(tasks, HistoricTaskInstance::getProcessInstanceId);
    }

    @Override
    public List<String> ccTasks(Operator operator, String flowKey) {
        return flowCcTaskRepo.queryUserCcTasks(operator.getOperatorName(), flowKey);
    }
}
