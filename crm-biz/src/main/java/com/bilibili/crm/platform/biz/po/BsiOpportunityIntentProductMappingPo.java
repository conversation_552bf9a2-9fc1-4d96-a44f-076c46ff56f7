package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BsiOpportunityIntentProductMappingPo implements Serializable {
    /**
     * ID
     */
    private Long id;

    /**
     * 商机id
     */
    private Integer bsiOpportunityId;

    /**
     * 意向产品类型
     */
    private Integer intentProductType;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 意向产品ID
     */
    private String intentProductId;

    /**
     * 0 不拆分 1 拆分
     */
    private Integer unpackQuarter;

    /**
     * 本季度金额
     */
    private Long quarterAmount;

    /**
     * 其他季度金额
     */
    private Long otherQuarterAmount;

    /**
     * 总金额
     */
    private Long totalAmount;

    private static final long serialVersionUID = 1L;
}