package com.bilibili.crm.platform.biz.service.caster;

import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.service.caster.bean.CasterHookMessage;
import com.bilibili.crm.platform.biz.service.caster.config.CasterHookConfig;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.redis.core.HashOperations;
import org.springframework.data.redis.core.RedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/26
 * caster回调服务
 */
@Slf4j
@Service
public class CasterHookService {

    @Autowired
    private CasterHookConfig config;
    @Resource(name = "commonRedisTemplate")
    private RedisTemplate<String, Object> commonRedisTemplate;
    @Autowired
    private WxRobotService wxRobotService;

    public void saveMessage (String sys) {

        Cat.logEvent("CasterHook.saveMessage", sys);

        // 查询系统对应负责人
        HashMap<String, String> noticeMap = config.getNoticeMap();
        String username = noticeMap.get(sys);

        // redis key
        Long id = Utils.getNow().getTime();
        // redis content
        String notice = String.format(config.getNoticeContent(), SystemType.valueOf(sys).getDescCN(), Utils.getNowTimestampString(), username);
        // redis message
        CasterHookMessage message = CasterHookMessage.builder()
                .id(id)
                .username(username)
                .notice(notice)
                .build();

        // 写入
        HashOperations<String, String, CasterHookMessage> operations = commonRedisTemplate.opsForHash();
        operations.put(config.getCasterHookRedisKey(), message.getId().toString(), message);
    }

    public List<CasterHookMessage> getMessageYesterday () {

        HashOperations<String, String, CasterHookMessage> operations = commonRedisTemplate.opsForHash();
        Map<String, CasterHookMessage> messageMap = operations.entries(config.getCasterHookRedisKey());

        if (CollectionUtils.isEmpty(messageMap)) {
            return Collections.emptyList();
        }

        // 获取昨天之前的所有消息
        return messageMap.values().stream()
                .filter(msg -> msg.getId() < Utils.getToday().getTime())
                .collect(Collectors.toList());
    }

    public void sendMessage () {

        List<CasterHookMessage> messageList = getMessageYesterday();
        log.info("CasterHook.sendMessage message:{}", messageList);

        messageList.forEach(message -> {

            // 机器人发消息
            WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                    .msgtype(MsgType.TEXT.getType())
                    .text(new Text(message.getNotice()))
                    .build();
            wxRobotService.sendWeChatWork(wxRobotMsg, config.getCasterRobotUrl());

            // delete redis message
            commonRedisTemplate.opsForHash().delete(config.getCasterHookRedisKey(), message.getId().toString());
        });
    }
}
