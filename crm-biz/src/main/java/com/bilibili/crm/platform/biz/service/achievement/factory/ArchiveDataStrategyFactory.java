package com.bilibili.crm.platform.biz.service.achievement.factory;

import com.bilibili.crm.platform.api.achievement.IAchieveDataStrategyService;
import com.bilibili.crm.platform.common.DomainOrgType;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;

/**
 * @description:
 * @author: brady
 * @time: 2020/10/23 2:55 下午
 */
@Component
public class ArchiveDataStrategyFactory {
    @Resource
    private Collection<IAchieveDataStrategyService> dataHandlers;

    public IAchieveDataStrategyService getObject (DomainOrgType type) {

        return dataHandlers.stream()
                .filter(handler -> handler.type().equals(type))
                .findFirst().orElse(null);
    }
}
