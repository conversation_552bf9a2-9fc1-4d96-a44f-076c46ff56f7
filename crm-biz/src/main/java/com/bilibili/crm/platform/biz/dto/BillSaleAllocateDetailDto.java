package com.bilibili.crm.platform.biz.dto;

import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.google.common.collect.Maps;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.Map;
import java.util.concurrent.atomic.AtomicLong;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class BillSaleAllocateDetailDto {

    private Map<Integer, Long> directWeightMap = Maps.newHashMap();

    /**
     * 业绩直客销售权重，可能为负值、0、正值
     *
     * 理论上其值与{@link #totalAmount}值相等
     */
    private AtomicLong directTotalWeight = new AtomicLong(0);

    private Map<Integer, Long> channelWeightMap = Maps.newHashMap();

    /**
     * 业绩渠道销售权重，可能为负值、0、正值
     *
     * 理论上其值与{@link #totalAmount}值相等
     */
    private AtomicLong channelTotalWeight = new AtomicLong(0);

    /**
     * 已执行已计收账单总金额
     */
    private Long totalAmount = 0L;

    private Map<Integer, SaleDto> saleDtoMap = Maps.newHashMap();
}
