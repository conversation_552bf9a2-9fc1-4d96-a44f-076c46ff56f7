package com.bilibili.crm.platform.biz.repo.follow_record;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppPlannerStatus;
import com.bilibili.crm.platform.api.follow_manage.dto.FollowManageContactsDto;
import com.bilibili.crm.platform.api.follow_manage.dto.FollowManageQueryDto;
import com.bilibili.crm.platform.api.follow_manage.dto.FollowManageRecordDto;
import com.bilibili.crm.platform.api.follow_manage.enums.FollowStatusType;
import com.bilibili.crm.platform.biz.dao.CrmFollowManageRecordDao;
import com.bilibili.crm.platform.biz.po.CrmFollowManageRecordPo;
import com.bilibili.crm.platform.biz.po.CrmFollowManageRecordPoExample;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.google.common.collect.Lists;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.Optional;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-10-18 20:30:53
 * @description:
 **/

@Repository
public class FollowManageRecordRepo {

    @Resource
    private CrmFollowManageRecordDao crmFollowManageRecordDao;


    public List<FollowManageRecordDto> queryList(FollowManageQueryDto queryDto) {
        CrmFollowManageRecordPoExample example = buildExample(queryDto);
        if (CollectionUtils.isEmpty(example.getOredCriteria())) {
            return new ArrayList<>();
        }
        List<CrmFollowManageRecordPo> recordPos = crmFollowManageRecordDao.selectByExample(example);
        return poToDto(recordPos);
    }

    public Long queryTotal(FollowManageQueryDto queryDto){
        CrmFollowManageRecordPoExample example = buildExample(queryDto);
        if (CollectionUtils.isEmpty(example.getOredCriteria())) {
            return 0L;
        }
        return crmFollowManageRecordDao.countByExample(example);
    }

    public Long save(FollowManageRecordDto recordDto) {
        CrmFollowManageRecordPo po = new CrmFollowManageRecordPo();
        if (Objects.nonNull(recordDto.getRecordId())) {
            po = crmFollowManageRecordDao.selectByPrimaryKey(recordDto.getRecordId());
            po.setMtime(new Timestamp(System.currentTimeMillis()));
            ObjectUtils.setObject(recordDto::getCustomerRecordId, po::setCustomerRecordId);
            ObjectUtils.setObject(recordDto::getFollowTime, po::setFollowTime);
            ObjectUtils.setObject(recordDto::getFollowType, po::setFollowType);
            ObjectUtils.setObject(recordDto::getFollowSecondType, po::setFollowSecondType);
            ObjectUtils.setObject(recordDto::getContactId, po::setContactId);
            ObjectUtils.setObject(recordDto::getPlannerStatus, po::setPlannerStatus);
            ObjectUtils.setObject(recordDto::getPlannerRejectReason, po::setPlannerRejectReason);
            ObjectUtils.setObject(recordDto::getFollowStatus, po::setFollowStatus);
            ObjectUtils.setObject(recordDto::getAbandonReason, po::setAbandonReason);
            ObjectUtils.setObject(recordDto::getCreatorEmail, po::setCreatorEmail);
            ObjectUtils.setObject(recordDto::getCreatorName, po::setCreatorName);
            ObjectUtils.setObject(recordDto::getFollowContent, po::setFollowContent);
            ObjectUtils.setObject(recordDto::getIsPlanner, po::setIsPlanner);
            if (recordDto.getFollowStatus().equals(FollowStatusType.EDIT.getCode())) {
                buildContactInfo(po, Optional.ofNullable(recordDto.getContactDto()).orElse(new FollowManageContactsDto()));
            }
            crmFollowManageRecordDao.updateByPrimaryKeySelective(po);
        } else {
            po.setCtime(new Timestamp(System.currentTimeMillis()));
            po.setMtime(new Timestamp(System.currentTimeMillis()));
            po.setIsDeleted(IsDeleted.VALID.getCode());
            po.setCreatorName(recordDto.getCreatorName());
            po.setCreatorEmail(recordDto.getCreatorEmail());
            po.setCustomerRecordId(recordDto.getCustomerRecordId());
            po.setFollowTime(recordDto.getFollowTime());
            po.setFollowType(recordDto.getFollowType());
            po.setFollowSecondType(recordDto.getFollowSecondType());
            po.setContactId(recordDto.getContactId());
            po.setPlannerStatus(recordDto.getPlannerStatus());
            po.setPlannerRejectReason(recordDto.getPlannerRejectReason());
            po.setFollowStatus(recordDto.getFollowStatus());
            po.setFollowContent(recordDto.getFollowContent());
            po.setIsPlanner(recordDto.getIsPlanner());
            if (recordDto.getFollowStatus().equals(FollowStatusType.EDIT.getCode())) {
                buildContactInfo(po, Optional.ofNullable(recordDto.getContactDto()).orElse(new FollowManageContactsDto()));
            }
            crmFollowManageRecordDao.insertSelective(po);
        }
        return po.getId();
    }

    private void buildContactInfo(CrmFollowManageRecordPo po, FollowManageContactsDto contactDto){
        po.setContactName(contactDto.getContactName());
        po.setMobilePhone(contactDto.getMobilePhone());
        po.setPhone(contactDto.getPhone());
        po.setPosition(contactDto.getPosition());
        po.setAreaId(contactDto.getAreaId());
        po.setProvince(contactDto.getProvince());
        po.setCity(contactDto.getCity());
        po.setCounty(contactDto.getCounty());
        po.setDetailAddress(contactDto.getDetailAddress());
        po.setWechat(contactDto.getWechat());
        po.setEmail(contactDto.getEmail());
    }

    public void discard(Long recordId, String discardReason) {
        CrmFollowManageRecordPo recordPo = crmFollowManageRecordDao.selectByPrimaryKey(recordId);
        Assert.notNull(recordPo, "记录不存在");
        recordPo.setAbandonReason(discardReason);
        recordPo.setFollowStatus(FollowStatusType.DISCARD.getCode());
        recordPo.setMtime(new Timestamp(System.currentTimeMillis()));
        crmFollowManageRecordDao.updateByPrimaryKeySelective(recordPo);
    }

    public void confirmPlanner(Long recordId) {
        CrmFollowManageRecordPo recordPo = crmFollowManageRecordDao.selectByPrimaryKey(recordId);
        Assert.notNull(recordPo, "记录不存在");
        recordPo.setPlannerStatus(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode());
        recordPo.setMtime(new Timestamp(System.currentTimeMillis()));
        crmFollowManageRecordDao.updateByPrimaryKeySelective(recordPo);
    }

    public void assignPlanner(Long recordId) {
        CrmFollowManageRecordPo recordPo = crmFollowManageRecordDao.selectByPrimaryKey(recordId);
        Assert.notNull(recordPo, "记录不存在");
        recordPo.setPlannerStatus(BsiOppPlannerStatus.RECEIPT_ORDER.getCode());
        recordPo.setMtime(new Timestamp(System.currentTimeMillis()));
        crmFollowManageRecordDao.updateByPrimaryKeySelective(recordPo);
    }

    public void rejectPlanner(Long recordId, String rejectReason) {
        CrmFollowManageRecordPo recordPo = crmFollowManageRecordDao.selectByPrimaryKey(recordId);
        Assert.notNull(recordPo, "记录不存在");
        Assert.isTrue(!recordPo.getPlannerStatus().equals(BsiOppPlannerStatus.RECEIPT_ORDER.getCode()), "跟进记录已经分配策划，不可驳回");
        recordPo.setPlannerStatus(BsiOppPlannerStatus.HAS_REJECTED.getCode());
        recordPo.setPlannerRejectReason(rejectReason);
        recordPo.setMtime(new Timestamp(System.currentTimeMillis()));
        crmFollowManageRecordDao.updateByPrimaryKeySelective(recordPo);
    }

    public CrmFollowManageRecordPoExample buildExample(FollowManageQueryDto queryDto){
        CrmFollowManageRecordPoExample example = new CrmFollowManageRecordPoExample();
        if (!CollectionUtils.isEmpty(queryDto.getRecordIdsOr())) {
            FollowManageQueryDto queryDto1 = new FollowManageQueryDto();
            BeanUtils.copyProperties(queryDto, queryDto1);
            queryDto1.setCreatorUsers(Lists.newArrayList());
            buildCriteriaOr(queryDto1, example);
        }
        queryDto.setRecordIdsOr(null);
        buildCriteriaOr(queryDto, example);
        example.setOrderByClause("ctime desc");
        return example;
    }


    private CrmFollowManageRecordPoExample.Criteria buildCriteriaOr(FollowManageQueryDto queryDto, CrmFollowManageRecordPoExample example) {
        CrmFollowManageRecordPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.setObject(queryDto::getRecordId, criteria::andIdEqualTo);
        ObjectUtils.setObject(queryDto::getFollowStatus, criteria::andFollowStatusEqualTo);
        ObjectUtils.setObject(queryDto::getFollowType, criteria::andFollowTypeEqualTo);
        ObjectUtils.setObject(queryDto::getFollowSecondType, criteria::andFollowSecondTypeEqualTo);
        ObjectUtils.setList(queryDto::getCustomerRecordIds, criteria::andCustomerRecordIdIn);
        ObjectUtils.setList(queryDto::getRecordIds, criteria::andIdIn);
        ObjectUtils.setList(queryDto::getRecordIdsOr, criteria::andIdIn);
        ObjectUtils.setList(queryDto::getFollowStatusList, criteria::andFollowStatusIn);
        ObjectUtils.setList(queryDto::getCreatorUserSearchList, criteria::andCreatorEmailIn);
        ObjectUtils.setList(queryDto::getPlannerStatusList, criteria::andPlannerStatusIn);
        if (!CollectionUtils.isEmpty(queryDto.getCreatorUsers())) {
            criteria.andCreatorEmailIn(queryDto.getCreatorUsers());
        }
        if (!StringUtils.isBlank(queryDto.getCreatorUserSearch())) {
            criteria.andCreatorEmailEqualTo(queryDto.getCreatorUserSearch());
        }
        if (queryDto.getCreatorTimeLeft() != null && queryDto.getCreatorTimeRight() != null) {
            ObjectUtils.setObject(queryDto::getCreatorTimeLeft, criteria::andCtimeGreaterThanOrEqualTo);
            ObjectUtils.setObject(queryDto::getCreatorTimeRight, criteria::andCtimeLessThanOrEqualTo);
        }
        if (queryDto.getFollowTimeLeft() != null && queryDto.getFollowTimeRight() != null) {
            ObjectUtils.setObject(queryDto::getFollowTimeLeft, criteria::andFollowTimeGreaterThanOrEqualTo);
            ObjectUtils.setObject(queryDto::getFollowTimeRight, criteria::andFollowTimeLessThanOrEqualTo);
        }
        return criteria;
    }


    private List<FollowManageRecordDto> poToDto(List<CrmFollowManageRecordPo> recordPos) {
        return CrmUtils.convert(recordPos, po -> {
            FollowManageRecordDto dto = new FollowManageRecordDto();
            BeanUtils.copyProperties(po, dto);
            dto.setRecordId(po.getId());
            dto.setIsPlanner(po.getIsPlanner());
            dto.setPlannerRejectReason(po.getPlannerRejectReason());
            FollowManageContactsDto contactDto = FollowManageContactsDto.builder()
                    .contactName(po.getContactName())
                    .mobilePhone(po.getMobilePhone())
                    .phone(po.getPhone())
                    .position(po.getPosition())
                    .areaId(po.getAreaId())
                    .province(po.getProvince())
                    .city(po.getCity())
                    .county(po.getCounty())
                    .detailAddress(po.getDetailAddress())
                    .wechat(po.getWechat())
                    .email(po.getEmail())
                    .build();
            dto.setContactDto(contactDto);
            return dto;
        });
    }


    public List<CrmFollowManageRecordPo> listFollowManageRecords(List<Long> customerRecordIds) {
        if (CollectionUtils.isEmpty(customerRecordIds)) {
            return Lists.newArrayList();
        }
        CrmFollowManageRecordPoExample example = new CrmFollowManageRecordPoExample();
        CrmFollowManageRecordPoExample.Criteria criteria = example.createCriteria();
        criteria.andCustomerRecordIdIn(customerRecordIds);
        example.setOrderByClause("ctime desc");
        return crmFollowManageRecordDao.selectByExample(example);
    }
}
