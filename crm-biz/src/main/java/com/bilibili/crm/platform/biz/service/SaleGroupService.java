/**
 * <AUTHOR>
 * @date 2017年5月17日
 */

package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.sales.bo.CrmSaleBO;
import com.bilibili.crm.biz.sales.bo.QuerySaleByGroupRequest;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.biz.sales.repo.SaleGroupMappingRepo;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.constants.SaleGroupRoleType;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.UnitedIndustryDto;
import com.bilibili.crm.platform.api.account.service.IBizIndustryService;
import com.bilibili.crm.platform.api.enums.CategoryLevelEnum;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.sale.dto.*;
import com.bilibili.crm.platform.api.sale.group.dto.*;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.api.sale.type.dto.SaleTypeDto;
import com.bilibili.crm.platform.api.sale.type.service.ISaleTypeService;
import com.bilibili.crm.platform.biz.dao.CrmSaleDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleGroupDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleGroupIndustryMappingDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleKpiDao;
import com.bilibili.crm.platform.biz.hive.api.InvestmentHiveService;
import com.bilibili.crm.platform.biz.industry.service.UnitedIndustryService;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.CrmSaleRepo;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.tuple.Pair;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Consumer;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class SaleGroupService implements ISaleGroupService {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CrmSaleGroupDao saleGroupDao;
    @Autowired
    private ISaleTypeService saleTypeService;
    @Autowired
    private SaleService saleService;
    @Autowired
    private CrmSaleDao saleDao;
    @Autowired
    private ILogOperatorService logOperatorService;
    @Autowired
    private CrmSaleRepo crmSaleRepo;
    @Autowired
    private CrmSaleKpiDao crmSaleKpiDao;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    @Lazy
    private IBizIndustryService iBizIndustryService;
    @Resource
    private InvestmentHiveService investmentHiveService;
    @Autowired
    private CrmSaleGroupIndustryMappingDao crmSaleGroupIndustryMappingDao;

    @Resource
    private UnitedIndustryService unitedIndustryService;
    @Autowired
    private SaleGroupMappingRepo saleGroupMappingRepo;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;


    @Override
    public boolean haveValidSaleGroupBySaleTypeId(Integer saleTypeId) {
        Assert.notNull(saleTypeId, "销售类型ID不可为空");
        LOGGER.info("SaleGroupService.getValidSaleGroupBySaleTypeId saleTypeId {}", saleTypeId);

        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andSaleTypeEqualTo(saleTypeId).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode());

        List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(example);
        return CollectionUtils.isNotEmpty(saleGroupPos);
    }

    @Override
    public List<SaleGroupDto> getSaleGroupsInIds(List<Integer> saleGroupIds) {
        if (CollectionUtils.isEmpty(saleGroupIds)) {
            return new ArrayList<>();
        }
        List<CrmSaleGroupPo> saleGroupPos = getCrmSaleGroupPos(saleGroupIds);
        return buildSaleGroupDtos(saleGroupPos);
    }

    public List<CrmSaleGroupPo> getCrmSaleGroupPos(List<Integer> saleGroupIds) {
        if (CollectionUtils.isEmpty(saleGroupIds)) {
            return Lists.newArrayList();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(saleGroupIds).andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        return saleGroupDao.selectByExample(example);
    }

    public List<SaleGroupDto> buildSaleGroupDtos(List<CrmSaleGroupPo> saleGroupPos) {
        if (CollectionUtils.isEmpty(saleGroupPos)) {
            return Collections.emptyList();
        }
        OrgTree orgTree = getSalesGroupOrg();
        List<Integer> saleGroupIds = saleGroupPos.stream().map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        Map<Integer, Long> groupIdToSaleCountMap = saleGroupMappingServiceImpl.getSaleGroupIdToSaleCountMapInGroupIds(saleGroupIds);

        Map<Integer, SaleTypeDto> saleTypeMap = saleTypeService.getSaleTypeMap();
        return saleGroupPos.stream().map(po -> saleGroupPo2Dto(po, saleTypeMap, groupIdToSaleCountMap, orgTree))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, SaleGroupDto> getSaleGroupMapInIds(List<Integer> saleGroupIds) {
        List<SaleGroupDto> saleGroupDtos = this.getSaleGroupsInIds(saleGroupIds);
        return saleGroupDtos.stream().collect(Collectors.toMap(SaleGroupDto::getId, Function.identity()));
    }

    public Map<Integer, List<CrmSaleGroupPo>> getParentSaleGroup(List<Integer> saleGroupIds) {
        Map<Integer, List<CrmSaleGroupPo>> ret = new HashMap<>();
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setLimit(2000); // TODO 查询所有销售组不应该限制总量吧？
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        Map<Integer, CrmSaleGroupPo> saleGroupMap = crmSaleGroupPos.stream().collect(Collectors.toMap(CrmSaleGroupPo::getId, Function.identity()));

        for (Integer groupId : saleGroupIds) {
            ret.put(groupId, buildParentTree(groupId, saleGroupMap));
        }
        return ret;
    }

    private List<CrmSaleGroupPo> buildParentTree(Integer groupId, Map<Integer, CrmSaleGroupPo> saleGroupMap) {
        List<CrmSaleGroupPo> result = new ArrayList<>();
        CrmSaleGroupPo parent = saleGroupMap.get(groupId);
        if (null == parent) {
            return result;
        } else {
            result.add(parent);
        }
        result.addAll(buildParentTree(parent.getParentId(), saleGroupMap));
        return result;
    }

    private SaleGroupDto saleGroupPo2Dto(CrmSaleGroupPo saleGroupPo,
                                         Map<Integer, SaleTypeDto> saleTypeMap,
                                         Map<Integer, Long> groupIdToSaleCountMap,
                                         OrgTree orgTree) {
        SaleTypeDto saleTypeDto = saleTypeMap.getOrDefault(saleGroupPo.getSaleType(),
                SaleTypeDto.builder().id(0).name("未知").build());

        int saleCount = groupIdToSaleCountMap.getOrDefault(saleGroupPo.getId(), 0L).intValue();
        OrgTree salesGroupOrgById = getSalesGroupOrgById(orgTree, saleGroupPo.getId(), new OrgTree());
        List<OrgTree> result = new ArrayList<>();
        getNodePath(salesGroupOrgById, saleGroupPo.getId(), result);
        List<String> parentGroups = result.stream().map(OrgTree::getLabel).collect(Collectors.toList());
        List<Integer> pIds = result.stream().map(OrgTree::getValue).collect(Collectors.toList());

        return SaleGroupDto.builder()
                .id(saleGroupPo.getId())
                .name(saleGroupPo.getName())
                .saleType(saleGroupPo.getSaleType()).saleTypeName(saleTypeDto.getName())
                .status(saleGroupPo.getStatus()).number(saleCount)
                .saleDirectorId(0)
                .saleDirectorName("未知")
                .parentId(saleGroupPo.getParentId())
                .parentGroups(parentGroups)
                .pIds(pIds)
                .totalMember(0).build();
    }

    @Override
    public SaleGroupDto load(Integer groupId) {
        Assert.notNull(groupId, "销售小组ID不可为空");
        CrmSaleGroupPo saleGroupPo = saleGroupDao.selectByPrimaryKey(groupId);
        Assert.notNull(saleGroupPo, "该销售小组不存在" + groupId);

        SaleGroupDto saleGroupDto = SaleGroupDto.builder().build();
        BeanUtils.copyProperties(saleGroupPo, saleGroupDto);
        return saleGroupDto;
    }

    @Override
    public List<SaleGroupDto> getAll() {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(example);
        return buildSaleGroupDtos(saleGroupPos);
    }

    @Override
    public Map<Integer, SaleGroupSimpleItem> getGroupIdParentGroupsMap() {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(example);

        OrgTree orgTree = getSalesGroupOrg();
        List<Integer> saleGroupIds = saleGroupPos.stream().map(CrmSaleGroupPo::getId).collect(Collectors.toList());

        Map<Integer, SaleGroupSimpleItem> orgTreeMap = Maps.newHashMapWithExpectedSize(saleGroupIds.size());
        saleGroupPos.forEach(saleGroupPo -> {
            OrgTree salesGroupOrgById = getSalesGroupOrgById(orgTree, saleGroupPo.getId(), new OrgTree());
            List<OrgTree> result = new ArrayList<>();
            getNodePath(salesGroupOrgById, saleGroupPo.getId(), result);

            SaleGroupSimpleItem saleGroupSimpleItem = new SaleGroupSimpleItem();
            saleGroupSimpleItem.setParents(result.stream().map(orgTree1 -> {
                SaleGroupSimpleItem parent = new SaleGroupSimpleItem();
                parent.setId(orgTree1.getValue());
                parent.setName(orgTree1.getLabel());
                return parent;
            }).collect(Collectors.toList()));
            saleGroupSimpleItem.setId(saleGroupPo.getId());
            saleGroupSimpleItem.setName(saleGroupPo.getName());
            orgTreeMap.put(saleGroupPo.getId(), saleGroupSimpleItem);
        });

        return orgTreeMap;
    }

    @Override
    public List<SaleGroupDto> getAll(SaleGroupQueryDto param) {

        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        CrmSaleGroupPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (0 != param.getStatus()) {
            criteria.andStatusEqualTo(param.getStatus());
        }
        if (!StringUtils.isEmpty(param.getName())) {
            criteria.andNameLike("%" + param.getName() + "%");
        }

        List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(example);
        return buildSaleGroupDtos(saleGroupPos);
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public Integer create(Operator operator, NewSaleGroupDto newSaleGroupDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(newSaleGroupDto, "销售组信息不可为空");
        LOGGER.info("SaleGroupService.create operator {}, newSaleGroupDto {}", operator, newSaleGroupDto);

        CrmSaleGroupPo saleGroupPo = new CrmSaleGroupPo();
        BeanUtils.copyProperties(newSaleGroupDto, saleGroupPo);
        saleGroupPo.setStatus(IsAble.NORMAL.getCode());
        saleGroupDao.insertSelective(saleGroupPo);

        if (CollectionUtils.isEmpty(newSaleGroupDto.getLeaders())) {
            return saleGroupPo.getId();
        }

        saleGroupMappingRepo.initGroupLeader(saleGroupPo.getId(), newSaleGroupDto.getLeaders());

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.CREATE_SALE_GROUP)
                .module(Module.SALE_GROUP)
                .obj(newSaleGroupDto)
                .objId(saleGroupPo.getId())
                .systemType(SystemType.CRM)
                .build();

        logOperatorService.insertLog(operator, logDto);

        return saleGroupPo.getId();
    }


    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void update(Operator operator, UpdateSaleGroupDto updateSaleGroupDto) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(updateSaleGroupDto, "销售组信息不可为空");
        if (updateSaleGroupDto.getParentId().equals(updateSaleGroupDto.getId())) {
            return;
        }
        LOGGER.info("SaleGroupService.update operator {}, updateSaleGroupDto {}", operator, updateSaleGroupDto);

        CrmSaleGroupPo crmSaleGroupPo = saleGroupDao.selectByPrimaryKey(updateSaleGroupDto.getId());
        Assert.notNull(crmSaleGroupPo, "该销售小组不存在");

        List<Integer> childGroupIds = getChildSaleGroupOrg(updateSaleGroupDto.getId());
        Assert.isTrue(!childGroupIds.contains(updateSaleGroupDto.getParentId()), "上级和本级形成环路");

        saleGroupMappingRepo.updateGroupLeader(updateSaleGroupDto.getId(), updateSaleGroupDto.getLeaders());

        CrmSaleGroupPo saleGroupPo = new CrmSaleGroupPo();
        BeanUtils.copyProperties(updateSaleGroupDto, saleGroupPo);
        saleGroupDao.updateByPrimaryKeySelective(saleGroupPo);

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.UPDATE_SALE_GROUP)
                .module(Module.SALE_GROUP)
                .obj(updateSaleGroupDto)
                .objId(updateSaleGroupDto.getId())
                .systemType(SystemType.CRM)
                .build();

        logOperatorService.insertLog(operator, logDto);
    }

    private void buildTeamKpi(SaleGroupDto saleGroupDto, String quarterStr) {
        List<CrmSaleKpiDto> teamKpiDtos = saleService.querySaleKpi(Lists.newArrayList(saleGroupDto.getId()), quarterStr, SaleKpiTypeEnum.SALE_TEAM.getBizId());
        List<Integer> leaders = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(Collections.singletonList(saleGroupDto.getId()));
        List<CrmSaleKpiDto> leaderKpiDtos = saleService.querySaleKpi(leaders, quarterStr, SaleKpiTypeEnum.SALE.getBizId());
        saleGroupDto.setLeaderKpi(CollectionUtils.isEmpty(leaderKpiDtos) ? BigDecimal.ZERO : new BigDecimal(leaderKpiDtos.get(0).getQuarterKpi()));
        saleGroupDto.setTeamKpi(CollectionUtils.isEmpty(teamKpiDtos) ? BigDecimal.ZERO : new BigDecimal(teamKpiDtos.get(0).getQuarterKpi()));
    }

    private void buildSaleConfig(List<SaleDto> saleDtos, String quarterDesc) {
        List<Integer> saleIds = saleDtos.stream().map(SaleDto::getId).collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(saleIds)) {
            CrmSaleKpiPoExample example = new CrmSaleKpiPoExample();
            example.or()
                    .andBizIdIn(saleIds)
                    .andBizTypeEqualTo(SaleKpiTypeEnum.SALE.getBizId())
                    .andQuarterStrEqualTo(quarterDesc)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<CrmSaleKpiPo> crmSaleKpiPos = crmSaleKpiDao.selectByExample(example);
            Map<Integer, CrmSaleKpiPo> crmSaleKpiPoMap = crmSaleKpiPos.stream().collect(Collectors.toMap(CrmSaleKpiPo::getBizId, Function.identity()));
            saleDtos.forEach(
                    item -> {
                        item.setKpi(crmSaleKpiPoMap.getOrDefault(item.getId(), CrmSaleKpiPo.builder().quarterKpi(0L).build()).getQuarterKpi());
                        item.setCurrentQuarter(quarterDesc);
                    }
            );
        }
    }

    @Override
    public SaleGroupDto getSaleGroupWithSaleList(Integer groupId, String quarterStr) {
        SaleGroupDto saleGroupDto = this.load(groupId);
        Timestamp time = incomeTimeUtil.getQuarterFirstDate(incomeTimeUtil.buildTimeByQuarterStr(quarterStr));
        buildTeamKpi(saleGroupDto, quarterStr);
        List<Integer> bindSaleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupId(groupId);
        List<Integer> saleIds = saleService.getSalesIdBySaleIdsWithTime(bindSaleIds, time);
        List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupIds(Collections.singletonList(groupId));
        if (CollectionUtils.isNotEmpty(leaderIds)) {
            saleIds.removeAll(leaderIds);
        }
        List<SaleDto> saleDtos = saleService.getSalesInIds(saleIds);
        buildSaleConfig(saleDtos, quarterStr);
        saleGroupDto.setSaleDtos(saleDtos);
        saleGroupDto.setNumber(CollectionUtils.isEmpty(saleDtos) ? 0 : saleDtos.size());
        return saleGroupDto;
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void disableSaleGroup(Operator operator, Integer saleGroupId) {
        Assert.isTrue(!Operator.validateParamIsNull(operator));
        Assert.notNull(saleGroupId, "销售组ID不可为空");
        CrmSaleGroupPo saleGroupPo = saleGroupDao.selectByPrimaryKey(saleGroupId);
        Assert.notNull(saleGroupPo, "该销售组不存在");
        LOGGER.info("SaleGroupService.disableSaleGroup operator {}, saleGroupId {}", operator, saleGroupId);

        List<Integer> saleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupId(saleGroupId);
        Assert.isTrue(CollectionUtils.isEmpty(saleIds), "销售组下还有销售人员，不允许删除");

        CrmSaleGroupPo upateSaleGroupPo = new CrmSaleGroupPo();
        upateSaleGroupPo.setId(saleGroupId);
        upateSaleGroupPo.setStatus(IsAble.BANNED.getCode());
        saleGroupDao.updateByPrimaryKeySelective(upateSaleGroupPo);

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.UPDATE_SALE_GROUP_STATUS)
                .module(Module.SALE_GROUP)
                .obj(SaleGroupDto.builder().id(saleGroupId).status(IsAble.BANNED.getCode()).build())
                .objId(saleGroupId)
                .systemType(SystemType.CRM)
                .build();

        logOperatorService.insertLog(operator, logDto);

    }

    @Override
    public void deleteSaleFromGroup(Operator operator, Integer saleGroupId, Integer saleId) {
        LOGGER.info("SaleGroupService.deleteSaleFromGroup operator {}, saleGroupId {}, saleId {}", operator, saleGroupId, saleId);
        Assert.notNull(Operator.validateParamIsNull(operator));
        Assert.notNull(saleGroupId, "销售组ID不可为空");
        Assert.notNull(saleId, "销售ID不可为空");
        CrmSaleGroupPo saleGroupPo = saleGroupDao.selectByPrimaryKey(saleGroupId);
        Assert.notNull(saleGroupPo, "该销售组不存在");
        CrmSalePo crmSalePo = saleDao.selectByPrimaryKey(saleId);
        Assert.notNull(crmSalePo, "该销售不存在");
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePo);
        CrmSaleBO saleAuth = saleGroupMappingServiceImpl.querySale(saleId);
        Assert.isTrue(saleAuth.inGroup(saleGroupId), "该销售人员不在销售组下");
        Assert.isTrue(!saleAuth.isLeader(), "该销售为组长,不可被删除");

        CrmSalePo salePo = new CrmSalePo();
        salePo.setId(saleId);
        salePo.setType(0);
        saleDao.updateByPrimaryKeySelective(salePo);

        // todo 可能有修改
        saleGroupMappingRepo.deleteMapping(saleGroupId, saleId);

        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.DELETE_SALE_GROUP)
                .module(Module.SALE_GROUP)
                .obj(SaleGroupDto.builder().build())
                .objId(saleGroupId)
                .systemType(SystemType.CRM)
                .build();

        logOperatorService.insertLog(operator, logDto);
    }

    @Override
    public List<Integer> getAllChildGroupIdListByParentIdListNotWithSelf(List<Integer> groupIds) {
        List<Integer> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return result;
        }
        List<Integer> nextGroupIds = getNextChildGroupIdListByParentIdList(groupIds);
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            result.addAll(nextGroupIds);
            nextGroupIds = getNextChildGroupIdListByParentIdList(nextGroupIds);
        }
        return result;
    }

    public static String buildSaleGroupName(List<Integer> groupIds, Map<Integer, SaleGroupDto> saleGroupDtoMap) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return "";
        }
        groupIds = groupIds.stream().filter(Objects::nonNull).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(groupIds)) {
            return "";
        }
        StringBuilder stringBuilder = new StringBuilder();
        for (Integer groupId : groupIds) {
            String name = saleGroupDtoMap.getOrDefault(groupId, SaleGroupDto.builder().name("").build()).getName();
            stringBuilder.append(name).append(",");
        }
        String result = stringBuilder.toString();
        return !result.isEmpty() ? result.substring(0, result.length() - 1) : result;
    }

    @Override
    public List<SaleDto> getGroupMembersNew(String email) {
        if (Strings.isNullOrEmpty(email)) {
            return Collections.emptyList();
        }
        //根据email获取id
        SaleDto saleDto = saleService.getSaleByEmail(email);
        if (null == saleDto) {
            return Collections.emptyList();
        }
        return getGroupMembersNew(saleDto, saleDto.getLevel());
    }

    @Override
    public List<SaleDto> getGroupMembersNew(SaleDto saleDto, Integer level) {
        return getGroupMembersWithTime(saleDto, level, null);
    }

    /**
     * 如果该销售为销售组长，则获取其负责的所有销售小组及其下级小组的所有销售；
     * 否则，获取其所在小组的所有下级小组的所有销售（不包括当前所在组）。
     */
    @Override
    public List<SaleDto> getGroupMembersWithTime(SaleDto saleDto, Integer level, Timestamp base) {
        if (Objects.isNull(saleDto)) {
            return new ArrayList<>();
        }
        // 如果该销售为销售组长，则获取所有组长是该销售的小组
        List<Integer> leaderGroupIds = new ArrayList<>();

        if (saleDto.getLevel().equals(SaleLevelEnum.LEADER.getCode())) {
            leaderGroupIds = saleGroupMappingServiceImpl.queryGroupIdBySaleId(saleDto.getId(), SaleGroupRoleType.LEADER);
        }
        leaderGroupIds.add(saleDto.getGroupId());
        leaderGroupIds = leaderGroupIds.stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(leaderGroupIds)) {
            return new ArrayList<>();
        }
        List<Integer> groupIds = new ArrayList<>();
        for (Integer leaderGroupId : leaderGroupIds) {
            groupIds.addAll(getChildSaleGroupOrg(leaderGroupId));
        }
        if (level.equals(SaleLevelEnum.LEADER.getCode())) {
            groupIds.addAll(leaderGroupIds);
        }
        List<CrmSalePo> sales;
        List<Integer> bindSaleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupIds(groupIds);
        if (base == null) {
            sales = getSalesBySaleIds(bindSaleIds);
        } else {
            List<Integer> saleIds = saleService.getSalesIdBySaleIdsWithTime(bindSaleIds, base);
            sales = getSales(saleIds);
        }
        if (CollectionUtils.isEmpty(sales)) {
            return new ArrayList<>();
        }
        Map<Integer, CrmSaleBO> saleAuthMap = saleGroupMappingServiceImpl
                .querySales(sales.stream().map(CrmSalePo::getId).distinct().collect(Collectors.toList()));
        return sales.stream()
                .map(po -> SaleDto.builder()
                        .id(po.getId()).email(po.getEmail()).type(po.getType())
                        .saleIsQuit(po.getSaleIsQuit()).status(po.getStatus())
                        .saleQuitTime(po.getSaleQuitTime())
                        .groupId(saleAuthMap.getOrDefault(po.getId(), CrmSaleBO.EMPTY).getDefaultGroupId()).build())
                .collect(Collectors.toList());
    }


    private List<CrmSalePo> getSalesBySaleIds(List<Integer> bindSaleIds) {
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample saleExample = new CrmSalePoExample();
        saleExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> sales = saleDao.selectByExample(saleExample);
        if (CollectionUtils.isEmpty(sales)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        return sales;
    }

    private List<CrmSalePo> getSales(List<Integer> saleIds) {
        if (CollectionUtils.isEmpty(saleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample saleExample = new CrmSalePoExample();
        saleExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(saleIds);
        List<CrmSalePo> sales = saleDao.selectByExample(saleExample);
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        if (CollectionUtils.isEmpty(sales)) {
            return Collections.emptyList();
        }
        return sales;
    }

    @Override
    public List<Integer> getSaleIdByGroupId(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        List<Integer> bindSaleIds = saleGroupMappingRepo.querySaleIdsByGroupIdsAndLevels(groupIds, null);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample saleExample = new CrmSalePoExample();
        saleExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> sales = saleDao.selectByExample(saleExample);
        if (CollectionUtils.isEmpty(sales)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        return sales.stream().map(CrmSalePo::getId).collect(Collectors.toList());
    }


    @Override
    public List<Integer> getAllSaleIdByGroupId(Integer groupId) {
        List<Integer> result = new ArrayList<>();
        if (null == groupId) {
            return result;
        }
        CrmSaleGroupPoExample groupPoExample = new CrmSaleGroupPoExample();
        groupPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(groupPoExample);
        return getSaleIdsToGroupId(crmSaleGroupPos, groupId);
    }

    @Override
    public Map<Integer, List<Integer>> getAllSaleIdMapByGroupIdList(List<Integer> groupIds) {
        Map<Integer, List<Integer>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return result;
        }
        CrmSaleGroupPoExample groupPoExample = new CrmSaleGroupPoExample();
        groupPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(groupPoExample);
        groupIds.forEach(groupId -> {
            List<Integer> saleIds = getSaleIdsToGroupId(crmSaleGroupPos, groupId);
            if (CollectionUtils.isEmpty(saleIds)) {
                result.put(groupId, new ArrayList<>());
            }
            result.put(groupId, saleIds);
        });
        return result;
    }

    private List<Integer> getSaleIdsToGroupId(List<CrmSaleGroupPo> crmSaleGroupPos, Integer groupId) {
        List<Integer> allTeamIds = new ArrayList<>();
        allTeamIds.add(groupId);
        List<Integer> nextGroupIds = crmSaleGroupPos.stream().filter(e -> groupId.equals(e.getParentId())).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            allTeamIds.addAll(nextGroupIds);
            List<Integer> finalNextGroupIds = nextGroupIds;
            nextGroupIds = crmSaleGroupPos.stream().filter(e -> finalNextGroupIds.contains(e.getParentId())).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        }

        List<Integer> bindSaleIds = saleGroupMappingRepo.querySaleIdsByGroupIdsAndLevels(allTeamIds, null);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample saleExample = new CrmSalePoExample();
        saleExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(bindSaleIds);

        return saleDao.selectByExample(saleExample).stream().map(CrmSalePo::getId).distinct().collect(Collectors.toList());
    }

    @Override
    public List<Integer> getValidSaleIdByGroupIdWithTime(Integer groupId, Timestamp base) {
        List<Integer> groupIds = getAllChildGroupIdListByParentId(groupId);
        groupIds.add(groupId);
        List<Integer> bindSaleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupIds(groupIds);
        return saleService.getSalesIdBySaleIdsWithTime(bindSaleIds, base);
    }

    @Override
    public List<Integer> getAllValidSaleIdByGroupId(Integer groupId) {
        List<Integer> result = new ArrayList<>();
        if (null == groupId) {
            return result;
        }
        List<Integer> allTeamIds = new ArrayList<>();
        allTeamIds.add(groupId);
        CrmSaleGroupPoExample groupPoExample = new CrmSaleGroupPoExample();
        groupPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(groupPoExample);
        List<Integer> nextGroupIds = crmSaleGroupPos.stream().filter(e -> groupId.equals(e.getParentId())).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            allTeamIds.addAll(nextGroupIds);
            List<Integer> finalNextGroupIds = nextGroupIds;
            nextGroupIds = crmSaleGroupPos.stream().filter(e -> finalNextGroupIds.contains(e.getParentId())).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        }

        List<Integer> bindSaleIds = saleGroupMappingRepo.querySaleIdsByGroupIdsAndLevels(allTeamIds, null);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample saleExample = new CrmSalePoExample();
        saleExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> sales = saleDao.selectByExample(saleExample);
        if (CollectionUtils.isEmpty(sales)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        return sales.stream()
                .map(CrmSalePo::getId).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getAllValidSaleIdByGroupIdList(List<Integer> groupIds) {
        List<Integer> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return result;
        }
        List<Integer> allTeamIds = new ArrayList<>(groupIds);
        CrmSaleGroupPoExample groupPoExample = new CrmSaleGroupPoExample();
        groupPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(groupPoExample);
        List<Integer> nextGroupIds = crmSaleGroupPos.stream().filter(e -> groupIds.contains(e.getParentId())).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            allTeamIds.addAll(nextGroupIds);
            List<Integer> finalNextGroupIds = nextGroupIds;
            nextGroupIds = crmSaleGroupPos.stream().filter(e -> finalNextGroupIds.contains(e.getParentId())).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
        }

        List<Integer> bindSaleIds = saleGroupMappingRepo.querySaleIdsByGroupIdsAndLevels(allTeamIds, null);
        if (CollectionUtils.isEmpty(bindSaleIds)) {
            return Collections.emptyList();
        }
        CrmSalePoExample saleExample = new CrmSalePoExample();
        saleExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andIdIn(bindSaleIds);
        List<CrmSalePo> sales = saleDao.selectByExample(saleExample);
        if (CollectionUtils.isEmpty(sales)) {
            return Collections.emptyList();
        }
        saleGroupMappingServiceImpl.fillSalePoLevel(sales);
        return sales.stream()
                .map(CrmSalePo::getId).collect(Collectors.toList());
    }


    @Override
    public List<Integer> getAllChildGroupIdListByParentId(Integer groupId) {
        List<Integer> result = new ArrayList<>();
        if (null == groupId) {
            return result;
        }
        result.add(groupId);
        List<Integer> nextGroupIds = getNextChildGroupIdListByParentIdList(Lists.newArrayList(groupId));
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            result.addAll(nextGroupIds);
            nextGroupIds = getNextChildGroupIdListByParentIdList(nextGroupIds);
        }
        return result;
    }

    @Override
    public List<Integer> getAllChildGroupIdListByParentIdList(List<Integer> groupIds) {
        List<Integer> result = new ArrayList<>();
        if (CollectionUtils.isEmpty(groupIds)) {
            return result;
        }
        result.addAll(groupIds);
        List<Integer> nextGroupIds = getNextChildGroupIdListByParentIdList(groupIds);
        while (!CollectionUtils.isEmpty(nextGroupIds)) {
            result.addAll(nextGroupIds);
            nextGroupIds = getNextChildGroupIdListByParentIdList(nextGroupIds);
        }
        return result;
    }

    public CrmSaleGroupPo getSaleGroupInfoById(Integer groupId) {
        if (groupId == null) {
            return null;
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIdEqualTo(groupId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(example);
        if (CollectionUtils.isEmpty(saleGroupPos)) {
            return null;
        }
        return saleGroupPos.get(0);
    }

    @Override
    public Map<Integer, List<Integer>> getParentGroupLeaderIds(List<Integer> groupIds) {
        Map<Integer, List<Integer>> groupIdLeaderIds = Maps.newHashMapWithExpectedSize(groupIds.size());
        for (Integer groupId : groupIds) {
            if (groupId == null) {
                continue;
            }
            CrmSaleGroupPo po = getSaleGroupInfoById(groupId);

            while (po != null) {
                po = getSaleGroupInfoById(po.getParentId());
                if (po != null) {
                    List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupId(po.getId());
                    if (CollectionUtils.isNotEmpty(leaderIds)) {
                        groupIdLeaderIds.put(po.getId(), leaderIds);
                        break;
                    }
                }
            }
        }
        AlarmHelper.log("getParentGroupLeaderIds", groupIds, groupIdLeaderIds);
        return groupIdLeaderIds;
    }

    @Override
    public List<Integer> getAllParentGroupIdListByGroupId(Integer groupId) {
        if (null == groupId) {
            return Collections.emptyList();
        }
        List<CrmSaleGroupPo> resultPos = new ArrayList<>();
        CrmSaleGroupPo groupPo = getSaleGroupInfoById(groupId);
        while (groupPo != null) {
            resultPos.add(0, groupPo);
            groupPo = getSaleGroupInfoById(groupPo.getParentId());
        }
        return resultPos.stream().filter(Objects::nonNull).map(CrmSaleGroupPo::getId).collect(Collectors.toList());
    }

    @Override
    public List<SaleGroupDto> getAllParentGroupInfoListByGroupId(Integer groupId) {
        if (null == groupId) {
            return Collections.emptyList();
        }
        List<CrmSaleGroupPo> resultPos = new ArrayList<>();
        CrmSaleGroupPo groupPo = getSaleGroupInfoById(groupId);
        while (groupPo != null) {
            resultPos.add(0, groupPo);
            groupPo = getSaleGroupInfoById(groupPo.getParentId());
        }
        return resultPos.stream().filter(Objects::nonNull).map(this::saleGroupPo2Dto).collect(Collectors.toList());
    }

    public List<Integer> getNextChildGroupIdListByParentIdList(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyList();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andParentIdIn(groupIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        return crmSaleGroupPos.stream().map(CrmSaleGroupPo::getId).collect(Collectors.toList());
    }

    @Override
    public List<SaleBaseDto> getAllSaleByGroupIdWithLeaderValidQuarterQuit(Integer groupId, Timestamp saleQuitTimeBegin, Timestamp saleQuitTimeEnd) {
        CrmSaleGroupPo groupPo = saleGroupDao.selectByPrimaryKey(groupId);
        List<SaleBaseDto> result = new ArrayList<>();
        if (Objects.isNull(groupPo)) {
            return result;
        }
        List<Integer> saleIds = saleGroupMappingServiceImpl.querySaleIdsByGroupId(groupId);
        AlarmHelper.log("SaleIds", groupId, saleIds);
        //未封禁未离职
        List<SaleBaseDto> saleBaseDtoList1 = CollectionUtils.isEmpty(saleIds) ? Collections.emptyList() :
                saleService.getSaleBaseDto(QuerySaleDto.builder().saleIds(saleIds)
                        .saleIsQuit(IsValid.FALSE.getCode()).status(IsAble.NORMAL.getCode()).build());
        AlarmHelper.log("NoBanNoQuit", saleBaseDtoList1.stream().map(SaleBaseDto::getId).collect(Collectors.toList()));
        //未封禁本月离职
        List<SaleBaseDto> saleBaseDtoList2 = CollectionUtils.isEmpty(saleIds) ? Collections.emptyList() :
                saleService.getSaleBaseDto(QuerySaleDto.builder()
                        .saleIds(saleIds)
                        .saleIsQuit(IsValid.TRUE.getCode()).status(IsAble.NORMAL.getCode())
                        .saleQuitTimeBegin(saleQuitTimeBegin).saleQuitTimeEnd(saleQuitTimeEnd).build());
        AlarmHelper.log("NoBanQuitCurMonth", saleBaseDtoList2.stream().map(SaleBaseDto::getId).collect(Collectors.toList()));
        List<Integer> leaderIds = saleGroupMappingServiceImpl.queryLeaderIdsByGroupId(groupId);
        AlarmHelper.log("LeaderIds", groupId, leaderIds);
        if (CollectionUtils.isNotEmpty(leaderIds)) {
            List<SaleBaseDto> saleBaseLeaderDto = saleService.getSaleBaseDto(QuerySaleDto.builder().saleIds(leaderIds)
                    .saleIsQuit(IsValid.FALSE.getCode()).status(IsAble.NORMAL.getCode()).build());
            result.addAll(saleBaseLeaderDto);
        }
        result.addAll(saleBaseDtoList1);
        result.addAll(saleBaseDtoList2);
        return result;
    }

    @Override
    public List<SaleBaseDto> getAllSaleByGroupIdWithLeaderValidQuarterQuitByLogDate(Integer groupId, Timestamp saleQuitTimeBegin, Timestamp saleQuitTimeEnd, String logDateStr) {
        List<SaleBaseDto> result = new ArrayList<>();
        if (Objects.isNull(groupId)) {
            return Collections.emptyList();
        }
        List<SaleInfoWithHiveDTO> resultHive = new ArrayList<>();
        List<SaleGroupInfoWithHiveDTO> dtoList = investmentHiveService.queryAllSaleGroupInfoWithHiveDTO(logDateStr, String.valueOf(groupId), null);
        dtoList = dtoList.stream().filter(e -> Objects.equals(e.getSale_group_status(), SaleGroupStatus.VALID.getCode().toString())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dtoList)) {
            return result;
        }
        SaleGroupInfoWithHiveDTO groupInfo = dtoList.get(0);
        List<SaleInfoWithHiveDTO> saleInfoWithHiveDTOList = investmentHiveService.queryAllSaleInfoWithHiveDTO(logDateStr);
        //未封禁未离职
        List<SaleInfoWithHiveDTO> saleBaseDtoList1 = saleInfoWithHiveDTOList.stream()
                .filter(r -> r.getSaleGroupIds().contains(String.valueOf(groupId)))
                .filter(r -> Objects.equals(r.getSale_status(), SaleTypeStatus.VALID.getCode().toString()) && Objects.equals(r.getSale_is_quit(), IsValid.FALSE.getCode().toString()))
                .collect(Collectors.toList());
        //未封禁本月离职
        String beginTimeStr = CrmUtils.formatDateCanNull(saleQuitTimeBegin, CrmUtils.YYYYMMDDHHMMSS);
        String endTimeStr = CrmUtils.formatDateCanNull(saleQuitTimeEnd, CrmUtils.YYYYMMDDHHMMSS);
        List<SaleInfoWithHiveDTO> saleBaseDtoList2 = saleInfoWithHiveDTOList.stream()
                .filter(r -> r.getSaleGroupIds().contains(String.valueOf(groupId)))
                .filter(r -> Objects.equals(r.getSale_status(), SaleTypeStatus.VALID.getCode().toString()) && Objects.equals(r.getSale_is_quit(), IsValid.TRUE.getCode().toString()))
                .filter(r -> beginTimeStr.compareTo(r.getSale_quit_time()) <= 0 && endTimeStr.compareTo(r.getSale_quit_time()) >= 0)
                .collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(groupInfo.getLeaderIds())) {
            saleInfoWithHiveDTOList.stream()
                    .filter(r -> groupInfo.getLeaderIds().contains(r.getSale_id()))
                    .forEach(resultHive::add);
        }
        resultHive.addAll(saleBaseDtoList1);
        resultHive.addAll(saleBaseDtoList2);
        return resultHive.stream().map(t -> {
            SaleBaseDto saleBaseDto = new SaleBaseDto();
            saleBaseDto.setId(Integer.valueOf(t.getSale_id()));
            saleBaseDto.setName(t.getSale_name());
            saleBaseDto.setEmail(t.getSale_email());
            saleBaseDto.setType(Integer.valueOf(t.getSale_type()));
            saleBaseDto.setGroupId(Integer.valueOf(t.getSale_group_id_v1()));
            saleBaseDto.setStatus(Integer.valueOf(t.getSale_status()));
            saleBaseDto.setLevel(Integer.valueOf(t.getSale_level()));
            saleBaseDto.setNickName(t.getSale_name());
            saleBaseDto.setSaleIsQuit(Integer.valueOf(t.getSale_is_quit()));
            if (Objects.equals(t.getSale_is_quit(), "1")) {
                saleBaseDto.setSaleQuitTime(CrmUtils.parseTimestamp(t.getSale_quit_time()));
            }
            return saleBaseDto;
        }).collect(Collectors.toList());
    }

    @Override
    public List<Integer> getChildSaleIdByGroupId(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        List<Integer> allChildrenGroupIds = new ArrayList<>();
        for (Integer groupId : groupIds) {
            allChildrenGroupIds.addAll(getChildSaleGroupIds(groupId));
        }
        allChildrenGroupIds.addAll(groupIds);
        allChildrenGroupIds = allChildrenGroupIds.stream().distinct().collect(Collectors.toList());
        return saleService.getSalesIdByGroupIdsNew(allChildrenGroupIds);
    }

    @Override
    public List<Integer> getChildSaleIdByGroupIdWithProhibit(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        List<Integer> allChildrenGroupIds = new ArrayList<>();
        for (Integer groupId : groupIds) {
            allChildrenGroupIds.addAll(getChildSaleGroupIds(groupId));
        }
        allChildrenGroupIds.addAll(groupIds);
        allChildrenGroupIds = allChildrenGroupIds.stream().distinct().collect(Collectors.toList());
        return saleService.getSalesIdByGroupIdsWithAbandon(allChildrenGroupIds);
    }

    @Override
    public List<Integer> getChildSaleIdByGroupIdAndOperateName(List<Integer> groupIds, String operateName) {
        // TODO 又是这种扯淡的逻辑，传列表参数却只取其一
        Integer groupId = 0;
        if (!CollectionUtils.isEmpty(groupIds)) {
            groupId = groupIds.iterator().next();
        }
        // 获取指定 group id 下的所有的子 group ids
        List<Integer> allChildGroupIdsOfPointGroup = getChildSaleGroupIds(groupId);
        allChildGroupIdsOfPointGroup.add(groupId);

        // 获取该用户的所有的下级 groupIds(包括自己)
        CrmSalePo operateNameSalePo = crmSaleRepo.queryEnableByName(operateName);
        if (operateNameSalePo != null) {
            CrmSaleBO saleAuth = saleGroupMappingServiceImpl.querySale(operateNameSalePo.getId());
            Set<Integer> allChildGroupIdsOfOperateName = new HashSet<>();
            saleAuth.getRoleMap()
                    .keySet()
                    .forEach(gid-> {
                        allChildGroupIdsOfOperateName.addAll(getChildSaleGroupIds(gid));
                    });
            allChildGroupIdsOfOperateName.add(groupId); // TODO why not operateNameSalePo's groupId？
            // 指定的 group 下的所有组 ids 与操作人的所有组的 ids 求交集
            allChildGroupIdsOfPointGroup.retainAll(allChildGroupIdsOfOperateName);
        }

        // 获取这些销售组的所有的销售 ids
        List<Integer> salesIdByGroupIds = saleService.getSalesIdByGroupIdsNew(allChildGroupIdsOfPointGroup);
        if (CollectionUtils.isEmpty(salesIdByGroupIds) && operateNameSalePo != null) {
            salesIdByGroupIds = new ArrayList<>();
            salesIdByGroupIds.add(-1);
        }
        return salesIdByGroupIds;
    }


    @Override
    public List<SaleGroupDto> getSaleGroupById(List<Integer> ids) {
        if (CollectionUtils.isNotEmpty(ids)) {
            return new ArrayList<>();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode()).andIdIn(ids);

        List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(example);
        return saleGroupPos.stream().map(this::saleGroupPo2Dto).collect(Collectors.toList());
    }

    private SaleGroupDto saleGroupPo2Dto(CrmSaleGroupPo po) {
        SaleGroupDto dto = SaleGroupDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    @Override
    public List<Integer> getChildSaleGroupIds(Integer groupId) {
        OrgTree orgTree = getSaleGroupOrgById(getSalesGroupOrg(), groupId);
        return getAllChildGroupIds(orgTree);
    }

    @Override
    public List<Integer> getChildSaleGroupIds(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        List<Integer> resultGroupIds = new ArrayList<>();
        for (Integer groupId : groupIds) {
            OrgTree orgTree = getSaleGroupOrgById(getSalesGroupOrg(), groupId);
            resultGroupIds.addAll(getAllChildGroupIds(orgTree));
        }
        resultGroupIds.addAll(groupIds);
        return resultGroupIds.stream().distinct().collect(Collectors.toList());
    }

    @Override
    public List<Integer> getChildSaleGroupOrg(Integer groupId) {
        OrgTree orgTree = getSaleGroupOrgById(getSalesGroupOrg(), groupId);
        List<Integer> groupIds = getAllChildGroupIds(orgTree);
        return getSaleGroupInfo(groupIds).stream().map(SaleGroupDto::getId).collect(Collectors.toList());
    }

    /**
     * 获取树形组织架构结构里的所有的下级的销售组 ids
     */
    private List<Integer> getAllChildGroupIds(OrgTree orgTree) {
        List<Integer> result = new ArrayList<>();
        if (null == orgTree) {
            return result;
        }
        for (OrgTree tree : orgTree.getChildren()) {
            result.add(tree.getValue());
            result.addAll(getAllChildGroupIds(tree));
        }
        return result;
    }


    private List<SaleGroupDto> getSaleGroupInfo(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }

        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode()).andIdIn(groupIds);
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        return crmSaleGroupPos.stream().map(item -> {
            SaleGroupDto tmp = new SaleGroupDto();
            BeanUtils.copyProperties(item, tmp);
            return tmp;
        }).collect(Collectors.toList());
    }

    private List<CrmSaleGroupPo> getSaleGroupPo(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new ArrayList<>();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode()).andIdIn(groupIds);
        return saleGroupDao.selectByExample(example);
    }

    @Override
    public OrgTree getSalesGroupOrg() {
        // 顶级根节点
        OrgTree orgTree = new OrgTree("bilibili", 0, new ArrayList<>());

        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        for (CrmSaleGroupPo crmSaleGroupPo : crmSaleGroupPos) {
            Integer parentId = crmSaleGroupPo.getParentId();
            if (null == parentId) {
                AlarmHelper.log("ErrorData", crmSaleGroupPo);
                continue;
            }
            // bilibili 下一级的放入到 bilibili 的 children
            if (0 == parentId) {
                orgTree.getChildren().add(new OrgTree(crmSaleGroupPo.getName(), crmSaleGroupPo.getId(), new ArrayList<>()));
            }
        }

        // bilibili 下一级排序
        sortOrgTree(orgTree.getChildren());

        // 构建这个销售组织架构
        buildOrg(orgTree, crmSaleGroupPos);
        return orgTree;
    }

    public void loopOrgTree(OrgTree root, Consumer<OrgTree> consumer) {
        if (root == null) {
            return;
        }
        consumer.accept(root);
        for (OrgTree child : root.getChildren()) {
            loopOrgTree(child, consumer);
        }
    }

    /**
     * 特定排序
     *
     * @param children 子树排序
     */
    private void sortOrgTree(List<OrgTree> children) {
        children.sort(Comparator.comparing(o -> CrmConstant.ORG_TREE_PRIORITY.getOrDefault(o.getLabel(), 50)));
    }

    /**
     * 递归构建销售组的组织架构树形关系
     *
     * @param orgTree         销售组的根节点的
     * @param crmSaleGroupPos 所有的销售组
     */
    private void buildOrg(OrgTree orgTree, List<CrmSaleGroupPo> crmSaleGroupPos) {
        if (null == orgTree || CollectionUtils.isEmpty(orgTree.getChildren())) {
            return;
        }

        // 构建 orgTree 孩子的下一级 group
        for (OrgTree child : orgTree.getChildren()) {
            Integer id = child.getValue();
            for (CrmSaleGroupPo crmSaleGroupPo : crmSaleGroupPos) {
                Integer parentId = crmSaleGroupPo.getParentId();
                if (Objects.equals(id, parentId)) {
                    child.getChildren().add(new OrgTree(crmSaleGroupPo.getName(), crmSaleGroupPo.getId(), new ArrayList<>()));
                }
            }
            // 递归构建销售组的组织架构树形关系
            buildOrg(child, crmSaleGroupPos);
        }
    }

    @Override
    public OrgTree getSalesGroupOrgById(Integer groupId) {
        OrgTree org = getSalesGroupOrg();
        return getSalesGroupOrgById(org, groupId, new OrgTree());
    }

    @Override
    public OrgTree getSalesGroupParentOrgById(Integer groupId) {
        OrgTree org = getSalesGroupOrg();
        return getSalesGroupParentOrgById(org, groupId, new OrgTree());
    }

    @Override
    public List<Integer> getSalesGroupParentIdsById(Integer groupId) {
        List<Integer> result = new ArrayList<>();
        OrgTree orgTree = getSalesGroupParentOrgById(groupId);
        getTreeIds(orgTree, result);
        return result;
    }

    @Override
    public Pair<String, String> getGroupNameAndParentGroupNames(List<Integer> groupIds) {
        List<SaleGroupAndParentGroupDto> orgs = getParentGroupOrgByIds(groupIds, 1);
        if (CollectionUtils.isEmpty(orgs)) {
            return Pair.of("", "");
        }
        List<String> groupNames = new ArrayList<>();
        List<String> parentGroupNames = new ArrayList<>();
        for (SaleGroupAndParentGroupDto org : orgs) {
            groupNames.add(org.getGroupName());
            if (org.getParentGroup() != null) {
                parentGroupNames.add(org.getParentGroup().getGroupName());
            }
        }
        return Pair.of(String.join(",", groupNames), String.join(",", parentGroupNames));
    }

    @Override
    public String getGroupName(List<Integer> groupIds) {
        List<SaleGroupAndParentGroupDto> orgs = getParentGroupOrgByIds(groupIds, 0);
        if (CollectionUtils.isEmpty(orgs)) {
            return "";
        }

        return orgs.stream().map(SaleGroupAndParentGroupDto::getGroupName).collect(Collectors.joining(","));
    }

    @Override
    public List<SaleGroupAndParentGroupDto> getParentGroupOrgByIds(List<Integer> groupIds, int height) {
        if (CollectionUtils.isEmpty(groupIds)) {
            AlarmHelper.log("GroupNotFound", groupIds);
            return Collections.emptyList();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIdIn(groupIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> groupPo = saleGroupDao.selectByExample(example);
        if (CollectionUtils.isEmpty(groupPo)) {
            AlarmHelper.log("GroupNotFound", groupIds);
            return Collections.emptyList();
        }
        List<SaleGroupAndParentGroupDto> res = groupPo.stream()
                .map(p -> {
                    SaleGroupAndParentGroupDto g = new SaleGroupAndParentGroupDto();
                    g.setGroupId(p.getId());
                    g.setGroupName(p.getName());
                    g.setParentGroupId(p.getParentId());
                    return g;
                })
                .collect(Collectors.toList());

        List<SaleGroupAndParentGroupDto> cur = res;
        for (int i = 0; i < height; i++) {
            List<Integer> pids = groupPo.stream().map(CrmSaleGroupPo::getParentId).distinct()
                    .collect(Collectors.toList());
            example = new CrmSaleGroupPoExample();
            example.createCriteria().andIdIn(pids)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            groupPo = saleGroupDao.selectByExample(example);
            if (CollectionUtils.isEmpty(groupPo)) {
                break;
            }
            Map<Integer, CrmSaleGroupPo> groupMap = groupPo.stream()
                    .collect(Collectors.toMap(CrmSaleGroupPo::getId, Function.identity()));
            List<SaleGroupAndParentGroupDto> tmp = new ArrayList<>();
            cur.forEach(item-> {
                CrmSaleGroupPo po = groupMap.get(item.getParentGroupId());
                if (po != null) {
                    item.setParentGroup(new SaleGroupAndParentGroupDto());
                    item.getParentGroup().setGroupId(po.getId());
                    item.getParentGroup().setGroupName(po.getName());
                    tmp.add(item.getParentGroup());
                }
            });
            cur = tmp;
        }
        AlarmHelper.log("getParentGroupOrgByIds", groupIds, height, res);

        return res;
    }

    private void getTreeIds(OrgTree orgTree, List<Integer> ids) {
        if (null == orgTree) {
            return;
        }

        if (CollectionUtils.isEmpty(orgTree.getChildren())) {
            if (null != orgTree.getValue()) {
                ids.add(orgTree.getValue());
            }
            return;
        }

        ids.add(orgTree.getValue());
        for (OrgTree child : orgTree.getChildren()) {
            getTreeIds(child, ids);
        }
    }


    private List<OrgTree> getNodePath(OrgTree org, Integer groupId, List<OrgTree> result) {
        if (null == org) {
            return null;
        }

        result.add(new OrgTree(org.getLabel(), org.getValue(), new ArrayList<>()));
        if (groupId.equals(org.getValue())) {
            return result;
        }

        for (OrgTree orgTree : org.getChildren()) {
            List<OrgTree> orgTrees = getNodePath(orgTree, groupId, result);
            if (null != orgTrees) {
                return result;
            }
        }
        return result;
    }


    private OrgTree getSalesGroupOrgById(OrgTree org, Integer groupId, OrgTree result) {

        if (null == org) {
            return null;
        }

        result.setLabel(org.getLabel());
        result.setValue(org.getValue());
        result.setChildren(new ArrayList<>());
        result.getChildren().add(new OrgTree());
        if (Objects.equals(org.getValue(), groupId)) {
            result.setChildren(org.getChildren());
            return result;
        }

        for (OrgTree orgTree : org.getChildren()) {
            OrgTree tree = getSalesGroupOrgById(orgTree, groupId, result.getChildren().get(0));
            if (null == tree) {
                result.getChildren().remove(0);
                result.getChildren().add(new OrgTree());
            } else {
                return result;
            }
        }
        return null;
    }


    private OrgTree getSalesGroupParentOrgById(OrgTree org, Integer groupId, OrgTree result) {

        if (null == org) {
            return null;
        }

        result.setLabel(org.getLabel());
        result.setValue(org.getValue());
        result.setChildren(new ArrayList<>());
        result.getChildren().add(new OrgTree());
        if (Objects.equals(org.getValue(), groupId)) {
//            result.setChildren(org.getChildren());
            return result;
        }

        for (OrgTree orgTree : org.getChildren()) {
            OrgTree tree = getSalesGroupParentOrgById(orgTree, groupId, result.getChildren().get(0));
            if (null == tree) {
                result.getChildren().remove(0);
                result.getChildren().add(new OrgTree());
            } else {
                return result;
            }
        }
        return null;
    }


    /**
     * 从销售组织架构获取指定的 groupId 的组织结构(树形结构，含有子节点体系) 通过宽度优先的方式获取
     *
     * @param groupId
     * @return
     */
    @Override
    public OrgTree getChildGroupOrgById(Integer groupId) {
        return getSaleGroupOrgById(getSalesGroupOrg(), groupId);
    }

    /**
     * 从销售组织架构获取指定的 groupId 的组的组织结构(树形结构，含有子节点体系) 通过宽度优先的方式获取
     *
     * @param org     整个销售组织架构树
     * @param groupId
     * @return
     */
    public OrgTree getSaleGroupOrgById(OrgTree org, Integer groupId) {
        if (Objects.equals(org.getValue(), groupId)) {
            return org;
        }
        for (OrgTree orgTree : org.getChildren()) {
            OrgTree tree = getSaleGroupOrgById(orgTree, groupId);
            if (null != tree) {
                return tree;
            }
        }
        return null;
    }

    @Override
    public OrgTree getOperatorOrgNew(String contextUserName) {
        CrmSalePoExample crmSalePoExample = new CrmSalePoExample();
        crmSalePoExample.or().andEmailEqualTo(contextUserName).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> crmSalePos = saleDao.selectByExample(crmSalePoExample);
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePos);
        if (CollectionUtils.isEmpty(crmSalePos)) {
            return getSalesGroupOrg();
        }
        List<Integer> groupIds = new ArrayList<>();
        groupIds.add(crmSalePos.get(0).getGroupId());
        if (crmSalePos.get(0).getLevel().equals(SaleLevelEnum.LEADER.getCode())) {
            groupIds = saleGroupMappingServiceImpl.queryGroupIdBySaleId(crmSalePos.get(0).getId(), SaleGroupRoleType.LEADER);
        }
        groupIds = groupIds.stream().distinct().collect(Collectors.toList());
        OrgTree orgTree;
        List<SaleGroupDto> groupDtos = getSaleGroupsInIds(groupIds);
        // 父节点
        List<Integer> parentGroupIds = groupDtos.stream().map(SaleGroupDto::getPIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        if (groupIds.size() == 1) {
            orgTree = getSalesGroupOrgById(crmSalePos.iterator().next().getGroupId());
        } else {
            List<Integer> childGroupIds = new ArrayList<>();
            for (Integer groupId : groupIds) {
                childGroupIds.addAll(getChildSaleGroupIds(groupId));
            }
            List<Integer> allGroupIds = new ArrayList<>();
            allGroupIds.addAll(parentGroupIds);
            // 本节点
            allGroupIds.addAll(groupIds);
            // 子节点
            allGroupIds.addAll(childGroupIds);
            List<CrmSaleGroupPo> crmSaleGroupPos = getSaleGroupPo(allGroupIds);
            orgTree = new OrgTree("bilibili", 0, new ArrayList<>());
            for (CrmSaleGroupPo crmSaleGroupPo : crmSaleGroupPos) {
                Integer parentId = crmSaleGroupPo.getParentId();
                if (null == parentId) {
                    LOGGER.info("error data {}", crmSaleGroupPo);
                    continue;
                }
                // bilibili 下一级的放入到 bilibili 的 children
                if (0 == parentId) {
                    orgTree.getChildren().add(new OrgTree(crmSaleGroupPo.getName(), crmSaleGroupPo.getId(), new ArrayList<>()));
                }
            }
            // 构建这个销售组织架构
            buildOrg(orgTree, crmSaleGroupPos);
        }
        // 是否可选择
        buildCanChoose(parentGroupIds, groupIds, orgTree, crmSalePos.get(0).getLevel());
        return orgTree;
    }

    private void buildCanChoose(List<Integer> parentGroupIds, List<Integer> groupIds, OrgTree orgTree, Integer level) {
        if (orgTree == null) {
            return;
        }
        if (Objects.equals(level, SaleLevelEnum.LEADER.getCode()) && parentGroupIds.contains(orgTree.getValue()) && !groupIds.contains(orgTree.getValue())) {
            orgTree.setAbandon(IsValid.TRUE.getCode());
            for (OrgTree children : orgTree.getChildren()) {
                buildCanChoose(parentGroupIds, groupIds, children, level);
            }
        } else if (Objects.equals(level, SaleLevelEnum.SALE.getCode()) && (parentGroupIds.contains(orgTree.getValue()) || groupIds.contains(orgTree.getValue()))) {
            orgTree.setAbandon(IsValid.TRUE.getCode());
            for (OrgTree children : orgTree.getChildren()) {
                buildCanChoose(parentGroupIds, groupIds, children, level);
            }
        }
    }


    @Override
    public List<OrgTree> getOperatorOrgInGroupIds(String contextUserName, List<Integer> groupIds) {
        CrmSalePoExample crmSalePoExample = new CrmSalePoExample();
        crmSalePoExample.or().andEmailEqualTo(contextUserName).andStatusEqualTo(IsAble.NORMAL.getCode())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSalePo> crmSalePos = saleDao.selectByExample(crmSalePoExample);
        saleGroupMappingServiceImpl.fillSalePoLevel(crmSalePos);
        boolean isSale = !CollectionUtils.isEmpty(crmSalePos) && Objects.nonNull(crmSalePos.iterator().next().getGroupId());

        if (isSale) {
            // 销售查找当前销售组及销售组所有的父级销售组，和配置销售组取交集
            return findSalesGroupOrgForSales(groupIds, crmSalePos.iterator().next().getGroupId());
        }

        // 获取组织树
        return getSalesGroupOrgInGroupIds(groupIds);
    }

    /**
     * 从销售所属的销售小组反向构造销售小组树
     *
     * @param groupIds 最外层销售小组
     * @param groupId  销售所属销售小组
     * @return 小组树
     */
    private List<OrgTree> findSalesGroupOrgForSales(List<Integer> groupIds, Integer groupId) {
        List<OrgTree> orgTrees = Lists.newArrayList();

        // 查询所有的销售小组
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);

        if (CollectionUtils.isEmpty(crmSaleGroupPos)) {
            return orgTrees;
        }

        Map<Integer, CrmSaleGroupPo> id2PoMap = crmSaleGroupPos.stream().collect(Collectors.toMap(CrmSaleGroupPo::getId, Function.identity(), (k1, k2) -> k1));

        // 销售所属销售组
        CrmSaleGroupPo salesGroup = id2PoMap.get(groupId);

        if (Objects.isNull(salesGroup)) {
            return orgTrees;
        }
        List<Integer> orgTreeIds = Lists.newArrayList(groupId);

        // 先找子级
        List<OrgTree> saleOrgTree = getSalesGroupOrgInGroupIds(Lists.newArrayList(groupId));

        if (groupIds.contains(groupId)) {
            // 已经是最外层了，直接返回
            return saleOrgTree;
        }

        // 再找父级
        OrgTree child = saleOrgTree.get(0);
        OrgTree root = child;
        Integer pid = salesGroup.getParentId();
        while (Objects.nonNull(pid) && !pid.equals(0)) {
            orgTreeIds.add(pid);
            CrmSaleGroupPo parent = id2PoMap.get(pid);

            if (Objects.isNull(parent)) {
                break;
            }

            root = new OrgTree(parent.getName(), parent.getId(), Lists.newArrayList(child));
            child = root;

            if (groupIds.contains(pid)) {
                // 已经最外层
                break;
            }
            pid = parent.getParentId();
        }
        orgTrees.add(root);

        if (Collections.disjoint(groupIds, orgTreeIds)) {
            // 判断是否有交集
            return Lists.newArrayList();
        }

        return orgTrees;
    }

    /**
     * 根据销售组ids 获取组织树
     *
     * @param groupIds 销售组ids
     * @return 小组树
     */
    private List<OrgTree> getSalesGroupOrgInGroupIds(List<Integer> groupIds) {
        List<OrgTree> orgTrees = Lists.newArrayList();

        if (CollectionUtils.isEmpty(groupIds)) {
            return orgTrees;
        }

        // 查询销售小组
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);


        for (CrmSaleGroupPo po : crmSaleGroupPos) {
            // 先找到根节点对应的销售小组
            Integer groupId = po.getId();
            if (groupIds.contains(groupId)) {
                // 这是一个根节点
                OrgTree root = new OrgTree(po.getName(), po.getId(), Lists.newArrayList());
                orgTrees.add(root);

                for (CrmSaleGroupPo crmSaleGroupPo : crmSaleGroupPos) {
                    Integer parentId = crmSaleGroupPo.getParentId();
                    if (Objects.equals(groupId, parentId)) {
                        root.getChildren().add(new OrgTree(crmSaleGroupPo.getName(), crmSaleGroupPo.getId(), new ArrayList<>()));
                    }
                }

                // 构建这个销售组织架构
                buildOrg(root, crmSaleGroupPos);
            }
        }

        return orgTrees;
    }

    @Override
    public Integer getOrgLevel(Integer saleGroupId) {
        List<Integer> salesGroupParentIdsById = getSalesGroupParentIdsById(saleGroupId);
        return CollectionUtils.isEmpty(salesGroupParentIdsById) ? 0 : salesGroupParentIdsById.size() - 1;
    }

    @Override
    public OrgTree getChildSalesGroupOrg(String contextUserName) {

        SaleDto saleByEmail = saleService.getSaleByEmail(contextUserName);
        if (null != saleByEmail) {
            Integer groupId = saleByEmail.getGroupId();
            CrmSaleGroupPo crmSaleGroupPo = saleGroupDao.selectByPrimaryKey(groupId);
            Assert.isTrue(null != crmSaleGroupPo, "小组不存在");
            return getChildGroupOrgById(groupId);
        }

        return getSalesGroupOrg();
    }

    @Override
    public List<SaleGroupDto> getNextSalesGroup(Integer groupId) {

        if (null == groupId) {
            return Collections.EMPTY_LIST;
        }

        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.or().andParentIdEqualTo(groupId);
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        return crmSaleGroupPos.stream().map(
                item -> {
                    SaleGroupDto dto = SaleGroupDto.builder().build();
                    BeanUtils.copyProperties(item, dto);
                    return dto;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<SaleGroupDto> getValidNextSalesGroup(Integer groupId) {
        if (null == groupId) {
            return Collections.emptyList();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andParentIdEqualTo(groupId).andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        return crmSaleGroupPos.stream().map(
                item -> {
                    SaleGroupDto dto = SaleGroupDto.builder().build();
                    BeanUtils.copyProperties(item, dto);
                    return dto;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public List<SaleGroupDto> getAllValidGroupList() {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(SaleGroupStatus.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        return crmSaleGroupPos.stream().map(
                item -> {
                    SaleGroupDto dto = SaleGroupDto.builder().build();
                    BeanUtils.copyProperties(item, dto);
                    return dto;
                }
        ).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, SaleGroupDto> getAllGroupMap() {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        List<SaleGroupDto> groupDtoList = crmSaleGroupPos.stream().map(
                item -> {
                    SaleGroupDto dto = SaleGroupDto.builder().build();
                    BeanUtils.copyProperties(item, dto);
                    return dto;
                }
        ).collect(Collectors.toList());
        return groupDtoList.stream().collect(Collectors.toMap(SaleGroupDto::getId, Function.identity()));
    }

    @Override
    public Map<Integer, SaleGroupInfoWithHiveDTO> getAllGroupMapForHive(String logDate) {
        List<SaleGroupInfoWithHiveDTO> saleGroupInfoWithHiveDTOList = investmentHiveService.queryAllSaleGroupInfoWithHiveDTO(logDate, null, null);
        return saleGroupInfoWithHiveDTOList.stream().collect(Collectors.toMap(e -> Integer.valueOf(e.getSale_group_id()), Function.identity()));
    }

    @Override
    public List<SaleGroupInfoWithHiveDTO> getNextChildGroupListByParentIdListByLogDate(List<Integer> groupIds, String logDate) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return null;
        }
        List<String> groupIdStrList = groupIds.stream().map(String::valueOf).collect(Collectors.toList());
        List<SaleGroupInfoWithHiveDTO> saleGroupInfoWithHiveDTOList = investmentHiveService
                .queryAllSaleGroupInfoWithHiveDTO(logDate, null, groupIdStrList);
        return saleGroupInfoWithHiveDTOList.stream()
                .filter(e -> Objects.equals(e.getSale_group_status(), SaleGroupStatus.VALID.getCode().toString()))
                .collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<SaleGroupInfoWithHiveDTO>> getAllChildGroupListByParentIdListByLogDate(List<Integer> groupIds, String logDate) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return Collections.emptyMap();
        }
        List<SaleGroupInfoWithHiveDTO> allSaleGroup = investmentHiveService.queryAllSaleGroupInfoWithHiveDTO(logDate, null, null);
        Map<Integer, List<SaleGroupInfoWithHiveDTO>> res = Maps.newHashMapWithExpectedSize(groupIds.size());
        for (Integer groupId : groupIds) {
            List<SaleGroupInfoWithHiveDTO> childGroupOfCur = new ArrayList<>();
            List<SaleGroupInfoWithHiveDTO> childGroup = allSaleGroup.stream()
                    .filter(g-> Objects.equals(g.getParentId(), groupId))
                    .collect(Collectors.toList());
            while (CollectionUtils.isNotEmpty(childGroup)) {
                childGroupOfCur.addAll(childGroup);
                Set<Integer> childGroupIds = childGroup.stream()
                        .map(SaleGroupInfoWithHiveDTO::getId)
                        .collect(Collectors.toSet());
                childGroup = allSaleGroup.stream()
                        .filter(g-> childGroupIds.contains(g.getParentId()))
                        .collect(Collectors.toList());
            }
            res.put(groupId, childGroupOfCur);
        }
        return res;
    }

    @Override
    public SaleGroupInfoWithHiveDTO querySaleGroupInfoWithHiveById(Integer groupId, String logDate) {
        if (Objects.isNull(groupId)) {
            return null;
        }
        List<SaleGroupInfoWithHiveDTO> saleGroupInfoWithHiveDTOList = investmentHiveService.queryAllSaleGroupInfoWithHiveDTO(logDate, String.valueOf(groupId), null);
        if (CollectionUtils.isEmpty(saleGroupInfoWithHiveDTOList)) {
            return null;
        }
        return saleGroupInfoWithHiveDTOList.stream()
                .filter(e -> Objects.equals(e.getSale_group_status(), SaleGroupStatus.VALID.getCode().toString()))
                .findFirst().orElse(null);
    }

    @Override
    public Map<Integer, SaleGroupDto> getGroupBaseInfoMapByIds(List<Integer> groupIds) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return new HashMap<>();
        }
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(groupIds);
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        List<SaleGroupDto> groupDtoList = crmSaleGroupPos.stream().map(
                item -> {
                    SaleGroupDto dto = SaleGroupDto.builder().build();
                    BeanUtils.copyProperties(item, dto);
                    return dto;
                }
        ).collect(Collectors.toList());
        return groupDtoList.stream().collect(Collectors.toMap(SaleGroupDto::getId, Function.identity()));
    }

    @Override
    public void createOrUpdateSaleGroupCategory(Operator operator, SaleGroupCategoryConfigDto dto) {
        // create or update  redis 锁  一级行业查询
        Assert.isTrue(null != dto.getSaleGroupId(), "销售小组id不能为空");


        CrmSaleGroupIndustryMappingPoExample example = new CrmSaleGroupIndustryMappingPoExample();
        CrmSaleGroupIndustryMappingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andSaleGroupIdEqualTo(dto.getSaleGroupId());
        //添加的有二级行业 对一级行业校验 并写入
        if (null != dto.getSecondCategory()) {
            CategoryDto secondaryCategoryDto = iBizIndustryService.getCategoryDtoById(null == dto.getSecondCategory() ? 0 : dto.getSecondCategory());
            if (null != secondaryCategoryDto) {
                Assert.isTrue(secondaryCategoryDto.getLevel() == 1, "二级行业不对 ID:" + dto.getSecondCategory());
            }
            criteria.andCommerceCategoryIdEqualTo(secondaryCategoryDto.getPId());
            List<CrmSaleGroupIndustryMappingPo> secondaryPidCrmSaleIndustryMappingPos = crmSaleGroupIndustryMappingDao.selectByExample(example);
            Assert.isTrue(CollectionUtils.isEmpty(secondaryPidCrmSaleIndustryMappingPos), "存在相同的一级行业 " + secondaryCategoryDto.getName());

            crmSaleGroupIndustryMappingDao.insertUpdateSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .saleGroupId(dto.getSaleGroupId())
                    .commerceCategoryId(dto.getSecondCategory())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
            return;
        }
        if (null != dto.getFirstCategory()) {
            CategoryDto firstCategoryDto = iBizIndustryService.getCategoryDtoById(dto.getFirstCategory());

            if (null != firstCategoryDto) {
                Assert.isTrue(firstCategoryDto.getLevel() == 0, "一级行业不对 ID:" + dto.getFirstCategory());
            }
            //一级校验
            criteria.andCommerceCategoryIdEqualTo(dto.getFirstCategory());
            List<CrmSaleGroupIndustryMappingPo> firstCrmSaleIndustryMappingPos = crmSaleGroupIndustryMappingDao.selectByExample(example);
            Assert.isTrue(CollectionUtils.isEmpty(firstCrmSaleIndustryMappingPos), "存在相同的一级行业 " + firstCategoryDto.getName());

            List<CategoryDto> dtosByPid = iBizIndustryService.getValidCategoryDtosByPid(dto.getFirstCategory());
            List<Integer> subCategoryIds = dtosByPid.stream().map(CategoryDto::getId).collect(Collectors.toList());

            CrmSaleGroupIndustryMappingPoExample updateExample = new CrmSaleGroupIndustryMappingPoExample();
            CrmSaleGroupIndustryMappingPoExample.Criteria updateCriteria = updateExample.or();
            updateCriteria.andSaleGroupIdEqualTo(dto.getSaleGroupId());
            updateCriteria.andCommerceCategoryIdIn(subCategoryIds);

            //写入一级行业映射前 删除该一级行业下的二级行业
            crmSaleGroupIndustryMappingDao.updateByExampleSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build(), updateExample);

            //写入一级行业映射前 删除该二级行业下的一级行业
            crmSaleGroupIndustryMappingDao.insertUpdateSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .saleGroupId(dto.getSaleGroupId())
                    .commerceCategoryId(dto.getFirstCategory())
                    .unitedIndustryId(dto.getUnitedFirstIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
        }
        //处理新-三级行业
        if (dto.getUnitedFirstIndustryId() != null || dto.getUnitedSecondIndustryId() != null || dto.getUnitedThirdIndustryId() != null) {
            this.createOrUpdateSaleGroupUnitedIndustry(operator, dto);
        }

    }

    //处理新-三级行业
    /*
    配置的时候，我们可以选一二三级，如果配的是二级，代表有二级下所有三级的权限；配的一级代表有一级下所有三级的权限，最细到三级
     */
    public void createOrUpdateSaleGroupUnitedIndustry(Operator operator, SaleGroupCategoryConfigDto dto) {
        //create or update  redis 锁  一级行业查询
        Assert.isTrue(null != dto.getSaleGroupId(), "销售小组id不能为空");

        //传到了三级行业
        if (null != dto.getUnitedThirdIndustryId()) {
            crmSaleGroupIndustryMappingDao.insertUpdateSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .saleGroupId(dto.getSaleGroupId())
                    .unitedIndustryId(dto.getUnitedThirdIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
            return;
        }
        //传到了二级行业
        if (null != dto.getUnitedSecondIndustryId()) {
            //把二级行业下的三级行业全部清空
            //写入二级行业映射前 删除该二级行业下的三级行业
            List<UnitedIndustryDto> unitedSecondIndustryDtoByLevel = unitedIndustryService.queryAllIndustryDtoByLevel(CategoryLevelEnum.THIRD.getLevel());
            List<Integer> subCategoryIds = unitedSecondIndustryDtoByLevel.stream().filter(v -> v.getPId().equals(dto.getUnitedSecondIndustryId())).map(UnitedIndustryDto::getId).collect(Collectors.toList());


            CrmSaleGroupIndustryMappingPoExample updateExample = new CrmSaleGroupIndustryMappingPoExample();
            CrmSaleGroupIndustryMappingPoExample.Criteria updateCriteria = updateExample.or();
            updateCriteria.andSaleGroupIdEqualTo(dto.getSaleGroupId());
            updateCriteria.andUnitedIndustryIdIn(subCategoryIds);
            crmSaleGroupIndustryMappingDao.updateByExampleSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), updateExample);

            crmSaleGroupIndustryMappingDao.insertUpdateSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .saleGroupId(dto.getSaleGroupId())
                    .unitedIndustryId(dto.getUnitedSecondIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
            return;
        }
        //传到了一级行业
        //写入一级行业映射前 删除该一级行业下的二级和三级行业

        if (null != dto.getUnitedFirstIndustryId()) {
            List<UnitedIndustryDto> unitedSecondIndustryDtoByLevel = unitedIndustryService.queryAllIndustryDtoByLevel(CategoryLevelEnum.SECOND.getLevel());

            //删除该一级行业下的二级
            List<Integer> subSecondCategoryIds = unitedSecondIndustryDtoByLevel.stream().filter(v -> v.getPId().equals(dto.getUnitedFirstIndustryId())).map(UnitedIndustryDto::getId).collect(Collectors.toList());

            CrmSaleGroupIndustryMappingPoExample updateExample = new CrmSaleGroupIndustryMappingPoExample();
            CrmSaleGroupIndustryMappingPoExample.Criteria updateCriteria = updateExample.or();
            updateCriteria.andSaleGroupIdEqualTo(dto.getSaleGroupId());
            updateCriteria.andUnitedIndustryIdIn(subSecondCategoryIds);
            crmSaleGroupIndustryMappingDao.updateByExampleSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), updateExample);

            //删除该一级行业下的三级
            List<UnitedIndustryDto> unitedThirdIndustryDtoByLevel = unitedIndustryService.queryAllIndustryDtoByLevel(CategoryLevelEnum.THIRD.getLevel());
            List<Integer> subThirdCategoryIds = unitedThirdIndustryDtoByLevel.stream().filter(v -> subSecondCategoryIds.contains(v.getPId())).map(UnitedIndustryDto::getId).collect(Collectors.toList());

            CrmSaleGroupIndustryMappingPoExample updateExampleThird = new CrmSaleGroupIndustryMappingPoExample();
            CrmSaleGroupIndustryMappingPoExample.Criteria updateCriteriaThird = updateExampleThird.or();
            updateCriteriaThird.andSaleGroupIdEqualTo(dto.getSaleGroupId());
            updateCriteriaThird.andUnitedIndustryIdIn(subThirdCategoryIds);
            crmSaleGroupIndustryMappingDao.updateByExampleSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build(), updateExampleThird);

            crmSaleGroupIndustryMappingDao.insertUpdateSelective(CrmSaleGroupIndustryMappingPo.builder()
                    .saleGroupId(dto.getSaleGroupId())
                    .unitedIndustryId(dto.getUnitedFirstIndustryId())
                    .isDeleted(IsDeleted.VALID.getCode())
                    .mtime(new Timestamp(System.currentTimeMillis())).build());
        }
    }

    @Override
    public void deleteSaleGroupCategory(Operator operator, SaleGroupCategoryConfigDto saleGroupCategoryConfigDto) {
        CrmSaleGroupIndustryMappingPoExample example = new CrmSaleGroupIndustryMappingPoExample();
        CrmSaleGroupIndustryMappingPoExample.Criteria criteria = example.or();
        criteria.andSaleGroupIdEqualTo(saleGroupCategoryConfigDto.getSaleGroupId());

        Integer categoryId = saleGroupCategoryConfigDto.getFirstCategory();
        if (null != saleGroupCategoryConfigDto.getSecondCategory()) {
            categoryId = saleGroupCategoryConfigDto.getSecondCategory();
        }
        if (null != categoryId) {
            criteria.andCommerceCategoryIdEqualTo(categoryId);
        }
        //新行业
        Integer industryId = null;
        if (null != saleGroupCategoryConfigDto.getUnitedFirstIndustryId()) {
            industryId = saleGroupCategoryConfigDto.getUnitedFirstIndustryId();
        }
        if (null != saleGroupCategoryConfigDto.getUnitedSecondIndustryId()) {
            industryId = saleGroupCategoryConfigDto.getUnitedSecondIndustryId();
        }
        if (null != saleGroupCategoryConfigDto.getUnitedThirdIndustryId()) {
            industryId = saleGroupCategoryConfigDto.getUnitedThirdIndustryId();
        }
        if (null != industryId) {
            criteria.andUnitedIndustryIdEqualTo(industryId);
        }
        crmSaleGroupIndustryMappingDao.updateByExampleSelective(CrmSaleGroupIndustryMappingPo.builder()
                .mtime(new Timestamp(System.currentTimeMillis()))
                .isDeleted(IsDeleted.DELETED.getCode())
                .build(), example);
    }

    @Override
    public List<SaleGroupCategoryMappingDto> querySaleGroupCategoryMapping(SaleGroupCategoryMappingQueryDto queryDto) {
        CrmSaleGroupIndustryMappingPoExample example = new CrmSaleGroupIndustryMappingPoExample();
        CrmSaleGroupIndustryMappingPoExample.Criteria criteria = example.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.setList(queryDto::getCategoryIds, criteria::andCommerceCategoryIdIn);
        ObjectUtils.setList(queryDto::getUnitedIndustryId, criteria::andUnitedIndustryIdIn);
        ObjectUtils.setList(queryDto::getSaleGroupIds, criteria::andSaleGroupIdIn);

        List<CrmSaleGroupIndustryMappingPo> mappingPos = crmSaleGroupIndustryMappingDao.selectByExample(example);
        return mappingPos.stream().map(item -> {
            SaleGroupCategoryMappingDto saleCategoryMappingDto = new SaleGroupCategoryMappingDto();
            BeanUtils.copyProperties(item, saleCategoryMappingDto);
            return saleCategoryMappingDto;
        }).collect(Collectors.toList());
    }

    @Override
    public Map<Integer, List<Integer>> getAllSaleGroupIndustryMap() {
        CrmSaleGroupIndustryMappingPoExample example = new CrmSaleGroupIndustryMappingPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupIndustryMappingPo> pos = crmSaleGroupIndustryMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos.stream().collect(Collectors.groupingBy(CrmSaleGroupIndustryMappingPo::getCommerceCategoryId,
                Collectors.mapping(CrmSaleGroupIndustryMappingPo::getSaleGroupId, Collectors.toList())));
    }

    @Override
    public List<Integer> queryAllGroupId() {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmSaleGroupPos)) {
            return Collections.emptyList();
        }
        return crmSaleGroupPos.stream().map(CrmSaleGroupPo::getId).collect(Collectors.toList());
    }


    //true = 有配置过  false = 没有配置过
    @Override
    public boolean queryIndustryBySaleGroupIdsAndId(List<Integer> groupIds, Integer unitedIndustryId) {
        if (CollectionUtils.isEmpty(groupIds)) {
            return false;
        }
        CrmSaleGroupIndustryMappingPoExample example = new CrmSaleGroupIndustryMappingPoExample();
        example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSaleGroupIdIn(groupIds)
                .andUnitedIndustryIdEqualTo(unitedIndustryId);
        List<CrmSaleGroupIndustryMappingPo> mappingPos = crmSaleGroupIndustryMappingDao.selectByExample(example);
        //true = 有配置过  false = 没有配置过
        return !CollectionUtils.isEmpty(mappingPos);
    }


    @Override
    public List<SaleGroupDto> queryValidSaleGroupByIndustryId(Integer unitedIndustryId) {
        if (!Utils.isPositive(unitedIndustryId)) {
            return Lists.newArrayList();
        }
        CrmSaleGroupIndustryMappingPoExample example = new CrmSaleGroupIndustryMappingPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andUnitedIndustryIdEqualTo(unitedIndustryId);
        List<CrmSaleGroupIndustryMappingPo> mappingPos = crmSaleGroupIndustryMappingDao.selectByExample(example);
        List<Integer> saleGroupIds = CrmUtils.convert(mappingPos, CrmSaleGroupIndustryMappingPo::getSaleGroupId);

        if (CollectionUtils.isEmpty(saleGroupIds)) {
            return Lists.newArrayList();
        }
        return this.getSaleGroupsInIds(saleGroupIds);
    }


    @Override
    public Map<Integer, SaleGroupDto> queryBaseSaleGroupByGroupId(List<Integer> groupIds) {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(1)
                .andIdIn(groupIds);
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        return crmSaleGroupPos.stream().collect(Collectors.toMap(CrmSaleGroupPo::getId, item -> {
            SaleGroupDto dto = SaleGroupDto.builder().build();
            BeanUtils.copyProperties(item, dto);
            return dto;
        }));
    }

    @Override
    public SaleGroupDto queryBaseSaleGroupByGroupId(Integer groupId) {
        CrmSaleGroupPoExample example = new CrmSaleGroupPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(groupId);
        List<CrmSaleGroupPo> crmSaleGroupPos = saleGroupDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmSaleGroupPos)) {
            return null;
        }
        SaleGroupDto dto = SaleGroupDto.builder().build();
        BeanUtils.copyProperties(crmSaleGroupPos.get(0), dto);
        return dto;
    }

    @Override
    public SaleGroupDto queryDirectBaseSaleGroupByGroupId(Integer groupId) {
        SaleGroupDto saleGroupDto = queryBaseSaleGroupByGroupId(groupId);
        if (null == saleGroupDto) {
            return null;
        }
        return queryBaseSaleGroupByGroupId(saleGroupDto.getParentId());
    }
}
