package com.bilibili.crm.platform.biz.elasticsearch.new_es.po.migrate;

import com.bilibili.crm.platform.api.effect.enums.BreakTapeSubType;
import com.bilibili.crm.platform.api.effect.enums.BreakTapeType;
import com.fasterxml.jackson.annotation.JsonFormat;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.data.annotation.Id;
import org.springframework.data.elasticsearch.annotations.Document;
import org.springframework.data.elasticsearch.annotations.Field;
import org.springframework.data.elasticsearch.annotations.FieldType;

import java.sql.Timestamp;

import static com.bilibili.crm.platform.biz.constant.EsIndexConstant.CRM_BREAK_TAPE_ACCOUNT_HOUR;

/**
 * <AUTHOR>
 * @date 2024-05-20 15:25
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Document(indexName = CRM_BREAK_TAPE_ACCOUNT_HOUR,  shards = 3)
public class EsBreakTapeAccountHourNewPo {

    /**
     * ID
     * 唯一键: account, budgetArrivedSubType, groupTime
     */
    @Id
    private String id;

    /**
     * 撞线账户id
     */
    @Field(type = FieldType.Keyword)
    private Integer accountId;

    /**
     * 撞线时间日预算
     */
    @Field(type = FieldType.Keyword)
    private Long accountBudget;

    /**
     * 撞线时间日消耗
     */
    @Field(type = FieldType.Keyword)
    private Long accountCost;

    /**
     * 账户余额(分)
     */
    @Field(type = FieldType.Keyword)
    private Long accountBalance;


    /**
     * 撞线时间
     */
    @JsonFormat(timezone = "Asia/Shanghai", pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp groupTime;

    /**
     * 生效日期(天级别)
     */
    @JsonFormat(timezone = "Asia/Shanghai", pattern="yyyy-MM-dd HH:mm:ss")
    private Timestamp validTime;

    /**
     * 撞线类型
     * @see BreakTapeType
     */
    @Field(type = FieldType.Keyword)
    private Integer budgetArrivedType;

    /**
     * 撞线子类型
     * @see BreakTapeSubType
     */
    @Field(type = FieldType.Keyword)
    private Integer budgetArrivedSubType;
}
