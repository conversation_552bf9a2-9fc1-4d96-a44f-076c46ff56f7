package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BsiOpportunityMediumPo implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 商机id
     */
    private Integer bsiOpportunityId;

    /**
     * 商机媒介处理人姓名
     */
    private String mediumOperatorName;

    /**
     * 商机媒介处理人email前缀
     */
    private String mediumOperatorEmail;

    /**
     * 媒介处理的状态 详情见枚举 BsiOppMediumStatus
     */
    private Integer status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 是否删除 0 未删除 1 已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 商机创建者email前缀
     */
    private String bsiOpportunityCreator;

    private static final long serialVersionUID = 5133787065830781976L;
}