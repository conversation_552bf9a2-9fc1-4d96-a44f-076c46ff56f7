package com.bilibili.crm.platform.biz.oneservice;

import com.alibaba.fastjson.JSONObject;
import com.bapis.datacenter.service.oneservice.*;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.util.ThreadPoolManager;
import com.google.common.collect.Lists;
import lombok.Setter;
import okhttp3.OkHttpClient;
import okhttp3.Request;
import okhttp3.Response;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import pleiades.venus.starter.rpc.client.RPCClient;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * one service 的限流策略
 * 举例QPS10， 则每100ms放行一个请求，意味着，哪怕1S内只有2个请求，但是这2个请求是在同一时间发生的，也会被限流住。
 * 需要一个分布式锁来控制
 */
@Service
public class OneServiceGrpcService {
    @Autowired
    private OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub openApiManagerBlockingStub;

    @RPCClient("datacenter.oneservice.associative-service")
    private OneServiceOpenApiCustomerGrpc.OneServiceOpenApiCustomerBlockingStub oneServiceOpenApiCustomerBlockingStub;

    private static final OkHttpClient client = new OkHttpClient();
    @Autowired
    private PaladinConfig paladinConfig;

    private String asyncSubmit(OneServiceQueryObject query) {
        AsyncSubmitReq.Builder builder = AsyncSubmitReq.newBuilder()
                .setOsHeader(getOsHeader(query.getScene()));

        builder.addAllReqs(buildReqs(query));

        if (CollectionUtils.isNotEmpty(query.getRespFieldSeq())) {
            builder.addAllResps(query.getRespFieldSeq());
        }

        AsyncSubmitReq asyncReq = builder.build();
        AlarmHelper.log("AsyncSubmit", asyncReq);
        AsyncSubmitResp resp = openApiManagerBlockingStub.asyncSubmit(asyncReq);
        AlarmHelper.log("AsyncSubmitResponse", resp);
        return resp.getTaskId();
    }

    private static List<OperatorVo> buildReqs(OneServiceQueryObject query) {
        List<OperatorVo> reqs = new ArrayList<>();
        if (query.getInCon() != null) {
            query.getInCon().forEach((field, con) -> {
                if (CollectionUtils.isNotEmpty(con)) {
                    reqs.add(OperatorVo
                            .newBuilder()
                            .setOperator("in")
                            .setField(field)
                            .addAllValues(con)
                            .build());
                }
            });
        }
        if (query.getEqCon() != null) {
            query.getEqCon().forEach((field, con) -> {
                if (StringUtils.isNotEmpty(con)) {
                    reqs.add(OperatorVo
                            .newBuilder()
                            .setOperator("=")
                            .setField(field)
                            .addValues(con)
                            .build());
                }
            });
        }
        return reqs;
    }

    private OsHeader getOsHeader(String scene) {
        return OsHeader
                .newBuilder()
                .setSecret(StringUtils.trim(paladinConfig.getString("ONE.SERVICE.SECRET." + scene)))
                .setAppKey(StringUtils.trim(paladinConfig.getString("ONE.SERVICE.APP.KEY." + scene)))
                .setApiId(StringUtils.trim(paladinConfig.getString("ONE.SERVICE.API.ID." + scene)))
                .build();
    }

    /**
     * 一次性最多查500个key，注意限制
     */
    public <T> List<T> queryTaiShan(OneServiceTaiShanQueryObject query, Class<T> cls) {
        List<T> res = Lists.newArrayList();
        for (OneServiceQueryObject subQuery : query.splitQuery()) {
            QueryReq req = QueryReq.newBuilder()
                    .addAllReqs(buildReqs(subQuery))
                    .setOsHeader(getOsHeader(subQuery.getScene()))
                    .build();
            QueryResp resp = oneServiceOpenApiCustomerBlockingStub.query(req);
            if (onlyPrintReq(query.getScene())) {
                AlarmHelper.log("QueryTaiShan", req);
            } else {
                AlarmHelper.log("QueryTaiShan", req, resp);
            }
            resp.getRowsList().stream()
                    .map(row-> JSONObject.parseObject(JSONObject.toJSONString(row.getValueMap()), cls))
                    .forEach(res::add);
        }
        return res;
    }

    private boolean onlyPrintReq(String scene) {
        return paladinConfig.contains("one.service.only.print.req", scene);
    }

    private <T> boolean asyncJobStatus(String scene, String taskId, Consumer<List<String>> consumer) throws Exception {
        AsyncJobStatusReq statusReq = AsyncJobStatusReq.newBuilder()
                .setOsHeader(getOsHeader(scene))
                .setTaskId(taskId).build();
        AsyncJobStatusResp resp = openApiManagerBlockingStub.asyncJobStatus(statusReq);
        AlarmHelper.log("AsyncJobStatus", taskId, resp);
        if (resp.getStatus() == 1) {
            for (String bossUrl : resp.getDataPathList()) {
                AlarmHelper.log("DownloadFile", bossUrl);
                Request req = new Request.Builder().url(bossUrl).get().build();
                try (Response res = client.newCall(req).execute()) {
                    assert res.body() != null;
                    String fileBody = res.body().string();
                    String[] lines = fileBody.split("\n");
                    AlarmHelper.log("ReadLines", lines.length);
                    if (lines.length > 0) {
                        AlarmHelper.log("FieldSeq", lines[0]);
                    }
                    for (int i = 1; i < lines.length; i++) {
                        String line = lines[i];
                        String[] fields = line.split("\u0001");
                        List<String> fieldList = Arrays.stream(fields).map(f -> {
                            if (Objects.equals("\\N", f)) {
                                return null;
                            }
                            return f;
                        }).collect(Collectors.toList());
                        consumer.accept(fieldList);
                    }
                }
                AlarmHelper.log("FileAnaDone", bossUrl);
            }
            return true;
        }
        return false;
    }

    /**
     * 全异步处理
     *
     * @param query 导出的条件
     * @param <T>   EXCEL实体，或者表实体
     */
    public <T> void asyncDownload(OneServiceQueryObject query, DownloadCallback<T> callback) {
        AlarmHelper.log("Query", query);
        String taskId = asyncSubmit(query);
        AsyncDownload<T> asyncDownload = new AsyncDownload<>(taskId, query, callback);

        ThreadPoolManager.SCHEDULE.schedule(asyncDownload, 5, TimeUnit.SECONDS);
        AlarmHelper.log("ScheduleDone");
    }

    /**
     * 简单查询 one service
     */
    public <T> List<T> query(OneServiceQueryObject query, Class<T> cls) {
        AlarmHelper.log("Query", query);
        OsHeader osHeader = getOsHeader(query.getScene());
        QueryReq openApiReq = QueryReq.newBuilder()
                .setOsHeader(osHeader)
                .addAllReqs(buildReqs(query))
                .build();

        QueryResp resp;
        try {
            resp = openApiManagerBlockingStub.query(openApiReq);
        } catch (Exception e) {
            AlarmHelper.alarmEx("OneServiceErr", e, cls.getName(), query, openApiReq);
            throw e;
        }
        if (onlyPrintReq(query.getScene())) {
            AlarmHelper.log("QueryResponse", openApiReq);
        } else {
            AlarmHelper.log("QueryResponse", openApiReq, resp);
        }
        return resp.getRowsList().stream()
                .map(row-> JSONObject.toJSONString(row.getValueMap()))
                .map(j-> JSONObject.parseObject(j, cls))
                .collect(Collectors.toList());
    }

    @Setter
    private class AsyncDownload<T> implements Runnable {

        String taskId;
        OneServiceQueryObject query;
        DownloadCallback<T> callback;
        int loopCount = paladinConfig.getIntegerOrDefault("one.service.download.loop.count", 1000);

        public AsyncDownload(String taskId, OneServiceQueryObject query, DownloadCallback<T> callback) {
            this.callback = callback;
            this.query = query;
            this.taskId = taskId;
        }

        @Override
        public void run() {
            try {
                boolean res = asyncJobStatus(query.getScene(), taskId, callback::trans);
                if (res) {
                    AlarmHelper.log("TaskFinish", loopCount, taskId);
                    callback.finish();
                } else {
                    if (loopCount <= 0) {
                        AlarmHelper.alarm("TaskDownloadOverTime", taskId);
                        callback.error();
                    } else {
                        AlarmHelper.log("TaskNotFinish,SubmitNewOne", loopCount, taskId);
                        ThreadPoolManager.SCHEDULE.schedule(this, 5, TimeUnit.SECONDS);
                        loopCount--;
                    }
                }
            } catch (Throwable e) {
                AlarmHelper.log("AsyncJobStatusErr", e, taskId, query);
                callback.error();
            }
        }
    }

}
