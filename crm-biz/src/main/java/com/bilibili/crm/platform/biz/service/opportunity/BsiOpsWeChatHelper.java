package com.bilibili.crm.platform.biz.service.opportunity;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpsMediumService;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityMediumDTO;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityMediumQueryDTO;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppMediumStatus;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppOGVStatus;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppPUGVStatus;
import com.bilibili.crm.platform.api.enums.bsi.BsiOppPlannerStatus;
import com.bilibili.crm.platform.api.sale.dto.SaleBaseDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.biz.dao.BsiOpportunityContractMappingDao;
import com.bilibili.crm.platform.biz.dao.CrmContractDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.service.SaleService;
import com.bilibili.crm.platform.biz.service.contract.component.ContractBiiPeriodAlertComponent;
import com.bilibili.rbac.api.dto.QueryUserDto;
import com.bilibili.rbac.api.dto.UserDto;
import com.bilibili.rbac.api.service.IUserService;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 商机发送企微消息相关
 *
 * <AUTHOR>
 * date 2023/11/14 11:25.
 * Contact: <EMAIL>.
 */
@Slf4j
@Component
public class BsiOpsWeChatHelper {
    @Resource
    private IBsiOpportunityService bsiOpportunityService;
    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private CrmContractDao crmContractDao;
    @Resource
    private ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent;

    @Autowired
    private BsiOpportunityContractMappingDao bsiOpportunityContractMappingDao;
    @Resource
    private SaleService saleService;
    @Autowired
    private IUserService userService;
    @Autowired
    private Integer tenantId;
    @Autowired
    private IBsiOpsMediumService bsiOpsMediumService;
    private static final String ROLE_MANAGER = "guowei05";

    /**
     * 移动端商机列表链接url
     */
    @Value("${bsi.ops.bsi.list.wechat.url:http://cm.bilibili.com/ldad/opportunity/main.html#/}")
    private String wechatBsiListUrl;

    @Value("${bsi.ops.planner.wechat.url:http://cm.bilibili.com/ldad/opportunity/main.html/#/view/}")
    private String wechatUrl;

    public void sendWeChat(BsiOpportunityPlannerPo oldPo, BsiOpportunityPlannerPo newPo, BsiOpportunityDto bsiBaseDto) {
        oldPo = Objects.isNull(oldPo) ? BsiOpportunityPlannerPo.builder().status(BsiOppPlannerStatus.INIT.getCode()).build() : oldPo;
        if (StringUtils.isEmpty(bsiBaseDto.getCustomerName()) && bsiBaseDto.getCustomerId() > 0) {
            CustomerPo customerPo = customerRepo.queryNotDeletedCustomerById(bsiBaseDto.getCustomerId());
            bsiBaseDto.setCustomerName(customerPo == null ? "" : customerPo.getUsername());
        } else {
            bsiBaseDto.setCustomerName(bsiBaseDto.getCompanyFullName());
        }
        PageResult<UserDto> pageResult = userService.queryUserByPage(QueryUserDto.builder()
                .tenantId(tenantId)
                .operatorName(ROLE_MANAGER)
                .usernameLike(bsiBaseDto.getCreateUser())
                .page(1)
                .size(15)
                .build());
        List<Integer> saleIds = Lists.newArrayList();
        saleIds.addAll(Optional.ofNullable(bsiBaseDto.getDirectSales()).orElse(new ArrayList<>()));
        saleIds.addAll(Optional.ofNullable(bsiBaseDto.getChannelSales()).orElse(new ArrayList<>()));
        String belongToSaleName = "";
        String createUser = CollectionUtils.isEmpty(pageResult.getRecords()) ? bsiBaseDto.getCreateUser() :
                pageResult.getRecords().get(0).getUserBaseDto().getNickname();
        if (!CollectionUtils.isEmpty(saleIds)) {
            List<SaleDto> saleDtoList = saleService.getSalesInIds(saleIds);
            belongToSaleName = saleDtoList.stream().map(SaleDto::getName).filter(a -> !a.equals(createUser)).collect(Collectors.joining(","));
        }
        List<String> newEmailList = Arrays.stream(Optional.ofNullable(newPo.getPlannerSpecialistEmail()).orElse("").split(",")).collect(Collectors.toList());
        List<String> newLeaderList = Arrays.stream(Optional.ofNullable(newPo.getPlannerLeaderEmail()).orElse("").split(",")).collect(Collectors.toList());
        List<String> oldEmailList = Arrays.stream(Optional.ofNullable(oldPo.getPlannerSpecialistEmail()).orElse("").split(",")).collect(Collectors.toList());
        if (newPo.getStatus().equals(BsiOppPlannerStatus.RECEIPT_ORDER.getCode()) && Objects.equals(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //策划确认接单
            String specialistWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条新的商机确认接单<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "分配人：%s<br/>" +
                            "策划专员：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    oldPo.getPlannerLeaderName(),
                    newPo.getPlannerSpecialistName(),
                    createUser,
                    belongToSaleName,
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            String leaderWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，以下商机已成功分配至:%s <br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    newPo.getPlannerSpecialistName(),
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    createUser,
                    belongToSaleName,
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            List<String> users = new ArrayList<>(newEmailList);
            users.add(bsiBaseDto.getCreateUser());
            contractBiiPeriodAlertComponent.sendQiwe(users, specialistWxContent);
            contractBiiPeriodAlertComponent.sendQiwe(newLeaderList, leaderWxContent);
        } else if (newPo.getStatus().equals(BsiOppPlannerStatus.RECEIPT_ORDER.getCode()) && Objects.equals(oldPo.getStatus(), BsiOppPlannerStatus.RECEIPT_ORDER.getCode())) {
            //修改策划专员
            if (newPo.getPlannerSpecialistEmail().equals(oldPo.getPlannerSpecialistEmail())) {
                return;
            }
            String specialistWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条新的商机确认接单<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "分配人：%s<br/>" +
                            "策划专员：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    oldPo.getPlannerLeaderName(),
                    newPo.getPlannerSpecialistName(),
                    createUser,
                    belongToSaleName,
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            String changeWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条的商机变更策划，请知晓<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "分配人：%s<br/>" +
                            "策划专员：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    oldPo.getPlannerLeaderName(),
                    newPo.getPlannerSpecialistName(),
                    createUser,
                    belongToSaleName,
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            String deleteWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条商机取消跟进，该条商机将从您的商机列表中删除，请知晓<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "分配人：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    oldPo.getPlannerLeaderName(),
                    createUser,
                    belongToSaleName);
            String leaderWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，以下商机的接单人，已从%s修改为至%s<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    oldPo.getPlannerSpecialistName(),
                    newPo.getPlannerSpecialistName(),
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    createUser,
                    belongToSaleName,
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(newLeaderList), leaderWxContent);
            if (!CollectionUtils.isEmpty(newEmailList)) {
                contractBiiPeriodAlertComponent.sendQiwe(newEmailList, specialistWxContent);
            }
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(bsiBaseDto.getCreateUser()), changeWxContent);
            if (!CollectionUtils.isEmpty(oldEmailList)) {
                contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(oldEmailList), deleteWxContent);
            }
        } else if (Objects.equals(newPo.getStatus(), BsiOppPlannerStatus.HAS_REJECTED.getCode()) &&
                Objects.equals(oldPo.getStatus(), BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode())) {
            //策划驳回
            String rejectWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条待分配策划的商机被驳回，请及时处理<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "分配人：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "驳回原因：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    oldPo.getPlannerLeaderName(),
                    createUser,
                    belongToSaleName,
                    newPo.getRejectReason(),
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            contractBiiPeriodAlertComponent.sendQiwe(Lists.newArrayList(bsiBaseDto.getCreateUser()), rejectWxContent);
            String rejectLeaderWxContent = String.format("【商机状态更新】<br/>" +
                            "以下商机已驳回，驳回原因:%s<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    newPo.getRejectReason(),
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    createUser,
                    belongToSaleName,
                    String.format("%s%s", wechatUrl, bsiBaseDto.getId()));
            contractBiiPeriodAlertComponent.sendQiwe(newLeaderList, rejectLeaderWxContent);
        } else if (newPo.getStatus().equals(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode()) &&
                (Objects.equals(BsiOppPlannerStatus.HAS_REJECTED.getCode(), oldPo.getStatus()) || Objects.equals(BsiOppPlannerStatus.INIT.getCode(), oldPo.getStatus()))) {
            //转到待策划接单
            String leaderWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条新的商机待分配策划<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    createUser,
                    belongToSaleName,
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(newLeaderList, leaderWxContent);
        } else if (Objects.equals(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode(), newPo.getStatus()) && Objects.equals(BsiOppPlannerStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //修改策划leader
            if (Objects.equals(newPo.getPlannerLeaderEmail(), oldPo.getPlannerLeaderEmail())) {
                return;
            }
            String leaderWxContent = String.format("【商机状态更新】<br/>" +
                            "您好，您有1条新的商机待分配策划<br/>" +
                            "<br/>" +
                            "商机项目名称：%s<br/>" +
                            "商机ID：%s<br/>" +
                            "客户名称：%s<br/>" +
                            "提交销售：%s<br/>" +
                            "合作销售：%s<br/>" +
                            "<br/>" +
                            "<a href=\"%s\">【查看详情】</a>",
                    bsiBaseDto.getName(),
                    bsiBaseDto.getId(),
                    bsiBaseDto.getCustomerName(),
                    createUser,
                    belongToSaleName,
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(newLeaderList, leaderWxContent);
        } else if (newPo.getStatus().equals(BsiOppPlannerStatus.HAVE_UPLOAD_SCHEME.getCode())) {
            String creatorWxContent = String.format("您商机 %s 的策划案文件上传完毕，可以登陆CRM系统-商机详情页进行细节查看。", bsiBaseDto.getName());
            contractBiiPeriodAlertComponent.sendQiwe(Collections.singletonList(newPo.getBsiOpportunityCreator()), creatorWxContent);
        }
    }

    public void sendOgvWeChat(BsiOpportunityOgvPo oldPo, BsiOpportunityOgvPo newPo, BsiOpportunityDto bsiBaseDto) {
        oldPo = Objects.isNull(oldPo) ? BsiOpportunityOgvPo.builder().status(BsiOppOGVStatus.INIT.getCode()).build() : oldPo;
        Assert.notNull(bsiBaseDto, "sendOgvWeChat_bsi_is_null_id=" + newPo.getBsiOpportunityId());
        List<String> weChatOgvEmailList = new ArrayList<>();
        List<String> weChatSaleEmailList = new ArrayList<>();
        if (Objects.equals(BsiOppOGVStatus.TO_BE_ALLOCATE.getCode(), newPo.getStatus()) && (Objects.isNull(oldPo) || Objects.equals(BsiOppOGVStatus.INIT.getCode(), oldPo.getStatus()) || Objects.equals(BsiOppOGVStatus.HAS_REJECTED.getCode(), oldPo.getStatus()))) {
            //销售发起OGV协作协作申请
            weChatOgvEmailList.add(newPo.getOgvLeaderEmail());
            String ogvWxContent = String.format("销售 %s 给您发起了商机 %s 的OGV内容营销协作流程，请进行确认处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getBsiOpportunityCreator(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatOgvEmailList, ogvWxContent);
        } else if (Objects.equals(BsiOppOGVStatus.RECEIPT_ORDER.getCode(), newPo.getStatus()) && Objects.equals(BsiOppOGVStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //OGV确认接单
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            UserDto newOgvActor = userService.getUserByUserName(tenantId, newPo.getOgvActorEmail());
            weChatOgvEmailList.add(newPo.getOgvActorEmail());
            String saleWxContent = String.format("策略专员 %s 确认了您商机 %s 的OGV内容营销协作流程。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newOgvActor == null ? "-" : newOgvActor.getUserBaseDto().getNickname(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);

            String ogvWxContent = String.format("OGV协作Leader %s 给你您分配了新的商机 %s 需要协作，请进行协作处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getOgvLeaderName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatOgvEmailList, ogvWxContent);

        } else if (Objects.equals(BsiOppOGVStatus.RECEIPT_ORDER.getCode(), newPo.getStatus()) && Objects.equals(BsiOppOGVStatus.RECEIPT_ORDER.getCode(), oldPo.getStatus())
                && !Objects.equals(newPo.getOgvActorEmail(), oldPo.getOgvActorEmail())) {
            //OGV更换协作者
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            weChatOgvEmailList.add(newPo.getOgvActorEmail());
            String saleWxContent = String.format("您的商机 %s 的OGV内容协作者从 %s 更换为 %s。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    bsiBaseDto.getName(),
                    oldPo.getOgvActorName(),
                    newPo.getOgvActorName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);

            String ogvWxContent = String.format("OGV协作Leader %s 给你您分配了新的商机 %s 需要协作，请进行协作处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getOgvLeaderName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatOgvEmailList, ogvWxContent);

        } else if (Objects.equals(BsiOppOGVStatus.HAS_REJECTED.getCode(), newPo.getStatus()) && Objects.equals(BsiOppOGVStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //OGV驳回接单
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            String saleWxContent = String.format("OGV协作Leader %s 驳回了您商机 %s 的OGV内容营销协作流程，请与他进行相关信息的确认和修改。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getOgvLeaderName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        } else if (Objects.equals(BsiOppOGVStatus.HAVE_UPLOAD_SCHEME.getCode(), newPo.getStatus())) {
            //OGV策划案已上传
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            String saleWxContent = String.format("您商机 %s 的OGV内容营销策划案文件上传完毕，可以登陆CRM系统-商机详情页进行细节查看。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        }
    }


    public void sendPugvWeChat(BsiOpportunityPugvPo oldPo, BsiOpportunityPugvPo newPo, BsiOpportunityDto bsiBaseDto) {
        oldPo = Objects.isNull(oldPo) ? BsiOpportunityPugvPo.builder().status(BsiOppPUGVStatus.INIT.getCode()).build() : oldPo;
        Assert.notNull(bsiBaseDto, "sendPugvWeChat_bsi_is_null_id=" + newPo.getBsiOpportunityId());
        List<String> weChatPugvEmailList = new ArrayList<>();
        List<String> weChatSaleEmailList = new ArrayList<>();
        if (Objects.equals(BsiOppPUGVStatus.TO_BE_ALLOCATE.getCode(), newPo.getStatus()) && (Objects.isNull(oldPo) || Objects.equals(BsiOppPUGVStatus.INIT.getCode(), oldPo.getStatus()) || Objects.equals(BsiOppPUGVStatus.HAS_REJECTED.getCode(), oldPo.getStatus()))) {
            //销售发起Pugv协作协作申请
            weChatPugvEmailList.add(newPo.getPugvLeaderEmail());
            String pugvWxContent = String.format("销售 %s 给您发起了商机 %s 的PUGV内容营销协作流程，请进行确认处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getBsiOpportunityCreator(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatPugvEmailList, pugvWxContent);
        } else if (Objects.equals(BsiOppPUGVStatus.RECEIPT_ORDER.getCode(), newPo.getStatus()) && Objects.equals(BsiOppPUGVStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //Pugv确认接单
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            UserDto newPugvActor = userService.getUserByUserName(tenantId, newPo.getPugvActorEmail());
            weChatPugvEmailList.add(newPo.getPugvActorEmail());
            String saleWxContent = String.format("策略专员 %s 确认了您商机 %s 的PUGV内容营销协作流程。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPugvActor == null ? "-" : newPugvActor.getUserBaseDto().getNickname(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);

            String pugvWxContent = String.format("Pugv协作Leader %s 给你您分配了新的商机 %s 需要协作，请进行协作处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getPugvLeaderName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatPugvEmailList, pugvWxContent);

        } else if (Objects.equals(BsiOppPUGVStatus.RECEIPT_ORDER.getCode(), newPo.getStatus()) && Objects.equals(BsiOppPUGVStatus.RECEIPT_ORDER.getCode(), oldPo.getStatus())
                && !Objects.equals(newPo.getPugvActorEmail(), oldPo.getPugvActorEmail())) {
            //PUGV更换协作者
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            weChatPugvEmailList.add(newPo.getPugvActorEmail());
            String saleWxContent = String.format("您的商机 %s 的PUGV内容协作者从 %s 更换为 %s。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    bsiBaseDto.getName(),
                    oldPo.getPugvActorName(),
                    newPo.getPugvActorName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);

            String pugvWxContent = String.format("PUGV协作Leader %s 给你您分配了新的商机 %s 需要协作，请进行协作处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getPugvLeaderName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatPugvEmailList, pugvWxContent);

        } else if (Objects.equals(BsiOppPUGVStatus.HAS_REJECTED.getCode(), newPo.getStatus()) && Objects.equals(BsiOppPUGVStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //Pugv驳回接单
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            String saleWxContent = String.format("PUGV协作Leader %s 驳回了您商机 %s 的PUGV内容营销协作流程，请与他进行相关信息的确认和修改。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newPo.getPugvLeaderName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        } else if (Objects.equals(BsiOppPUGVStatus.HAVE_UPLOAD_SCHEME.getCode(), newPo.getStatus())) {
            //Pugv策划案已上传
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            String saleWxContent = String.format("您商机 %s 的PUGV内容营销策划案文件上传完毕，可以登陆CRM系统-商机详情页进行细节查看。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        }
    }

    public void sendMediumWeChat(BsiOpportunityMediumPo oldPo, BsiOpportunityMediumPo newPo, BsiOpportunityDto bsiBaseDto) {
        Assert.notNull(bsiBaseDto, "bsi_is_null_id=" + newPo.getBsiOpportunityId());
        SaleBaseDto creatorSale = saleService.getSaleByEmailExcludeBanned(newPo.getBsiOpportunityCreator());
        Assert.notNull(creatorSale, "sale_is_null_email=" + newPo.getBsiOpportunityCreator());
        List<String> weChatMediumEmailList = new ArrayList<>();
        List<String> weChatSaleEmailList = new ArrayList<>();
        if (Objects.equals(BsiOppMediumStatus.TO_BE_ALLOCATE.getCode(), newPo.getStatus()) && (Objects.isNull(oldPo) || Objects.equals(BsiOppMediumStatus.INIT.getCode(), oldPo.getStatus()) || Objects.equals(BsiOppMediumStatus.HAS_REJECTED.getCode(), oldPo.getStatus()))) {
            //销售发起媒介协作申请
            weChatMediumEmailList.add(newPo.getMediumOperatorEmail());
            String mediumWxContent = String.format("销售 %s 给您发起了商机 %s 的预占合同申请流程，请进行确认处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    creatorSale.getName(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatMediumEmailList, mediumWxContent);
        } else if (Objects.equals(BsiOppMediumStatus.TO_BE_ALLOCATE.getCode(), newPo.getStatus()) && Objects.equals(BsiOppMediumStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //媒介修改对接媒介名字
            if (Objects.equals(oldPo.getMediumOperatorEmail(), newPo.getMediumOperatorEmail())) {
                return;
            }
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            weChatMediumEmailList.add(newPo.getMediumOperatorEmail());
            UserDto newMedium = userService.getUserByUserName(tenantId, newPo.getMediumOperatorEmail());
            UserDto oldMedium = userService.getUserByUserName(tenantId, oldPo.getMediumOperatorEmail());
            String mediumWxContent = String.format("媒介 %s 给您流转了商机  %s 的预占合同申请流程，请进行确认处理。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    oldMedium == null ? "-" : oldMedium.getUserBaseDto().getNickname(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatMediumEmailList, mediumWxContent);
            String saleWxContent = String.format("媒介 %s 把您商机 %s 的预占合同申请流转给了媒介 %s" +
                            "<a href=\"%s\">【商机列表】</a>",
                    oldMedium == null ? "-" : oldMedium.getUserBaseDto().getNickname(),
                    bsiBaseDto.getName(),
                    newMedium == null ? "-" : newMedium.getUserBaseDto().getNickname(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);

        } else if (Objects.equals(BsiOppMediumStatus.RECEIPT_ORDER.getCode(), newPo.getStatus()) && Objects.equals(BsiOppMediumStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //媒介确认接单
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            UserDto newMedium = userService.getUserByUserName(tenantId, newPo.getMediumOperatorEmail());
            String saleWxContent = String.format("媒介 %s 确认了您商机 %s 的预占合同申请流程，合同创建成功后，将会推送对应的合同信息。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newMedium == null ? "-" : newMedium.getUserBaseDto().getNickname(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        } else if (Objects.equals(BsiOppMediumStatus.HAS_REJECTED.getCode(), newPo.getStatus()) && Objects.equals(BsiOppMediumStatus.TO_BE_ALLOCATE.getCode(), oldPo.getStatus())) {
            //媒介驳回
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            UserDto newMedium = userService.getUserByUserName(tenantId, newPo.getMediumOperatorEmail());
            String saleWxContent = String.format("媒介 %s 驳回了您商机 %s 的预占合同申请流程，请与媒介进行相关信息的确认和修改。" +
                            "<a href=\"%s\">【商机列表】</a>",
                    newMedium == null ? "-" : newMedium.getUserBaseDto().getNickname(),
                    bsiBaseDto.getName(),
                    wechatBsiListUrl);
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        } else if (Objects.equals(BsiOppMediumStatus.CONTRACT_STARTED.getCode(), newPo.getStatus())) {
            //媒介发起媒介预占合同
            weChatSaleEmailList.add(newPo.getBsiOpportunityCreator());
            BsiOpportunityContractMappingPoExample example = new BsiOpportunityContractMappingPoExample();
            example.createCriteria().andBsiOpportunityIdEqualTo(newPo.getBsiOpportunityId()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<BsiOpportunityContractMappingPo> contractMappingPos = bsiOpportunityContractMappingDao.selectByExample(example);
            String saleWxContent = String.format("您商机 %s 的预占合同创建完毕，合同号 %s ，可以登陆CRM系统-合同列表进行细节查看。",
                    bsiBaseDto.getName(),
                    getContractNumberById(contractMappingPos));
            contractBiiPeriodAlertComponent.sendQiwe(weChatSaleEmailList, saleWxContent);
        }
    }

    /**
     * 商机一些操作发送企微 比如商机弃单
     * type=1 弃单
     */
    public void sendBsiWeChatByBsiType(Integer bsiId, Integer type) {
        if (Objects.equals(type, 1)) {
            List<BsiOpportunityMediumDTO> dtoList = bsiOpsMediumService.queryBsiDTOListByCondition(BsiOpportunityMediumQueryDTO.builder()
                    .status(Collections.singletonList(BsiOppMediumStatus.CONTRACT_STARTED.getCode()))
                    .bsiIdList(Collections.singletonList(bsiId)).build());
            if (CollectionUtils.isEmpty(dtoList)) {
                return;
            }
            BsiOpportunityDto bsiBaseDto = bsiOpportunityService.getBsiOpportunityBaseInfoById(bsiId);
            BsiOpportunityContractMappingPoExample example = new BsiOpportunityContractMappingPoExample();
            example.createCriteria().andBsiOpportunityIdEqualTo(bsiBaseDto.getId()).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<BsiOpportunityContractMappingPo> contractMappingPos = bsiOpportunityContractMappingDao.selectByExample(example);
            String saleWxContent = String.format("您发起过预占合同的商机 %s 已弃单，请检查预占合同 %s 中的预占点位是否已释放，避免资源浪费。",
                    bsiBaseDto.getName(),
                    getContractNumberById(contractMappingPos));
            contractBiiPeriodAlertComponent.sendQiwe(dtoList.stream().map(BsiOpportunityMediumDTO::getMediumOperatorEmail).collect(Collectors.toList()), saleWxContent);
        }
    }

    public String getContractNumberById(List<BsiOpportunityContractMappingPo> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return "-";
        }
        CrmContractPo crmContractPo = crmContractDao.selectByPrimaryKey(poList.stream().findFirst().orElse(BsiOpportunityContractMappingPo.builder().build()).getContractId());
        if (crmContractPo == null) {
            return "-";
        }
        return crmContractPo.getContractNumber().toString();
    }
}

