package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmBsiOptClueMappingPo;
import com.bilibili.crm.platform.biz.po.CrmBsiOptClueMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmBsiOptClueMappingDao {
    long countByExample(CrmBsiOptClueMappingPoExample example);

    int deleteByExample(CrmBsiOptClueMappingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmBsiOptClueMappingPo record);

    int insertBatch(List<CrmBsiOptClueMappingPo> records);

    int insertUpdateBatch(List<CrmBsiOptClueMappingPo> records);

    int insert(CrmBsiOptClueMappingPo record);

    int insertUpdateSelective(CrmBsiOptClueMappingPo record);

    int insertSelective(CrmBsiOptClueMappingPo record);

    List<CrmBsiOptClueMappingPo> selectByExample(CrmBsiOptClueMappingPoExample example);

    CrmBsiOptClueMappingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmBsiOptClueMappingPo record, @Param("example") CrmBsiOptClueMappingPoExample example);

    int updateByExample(@Param("record") CrmBsiOptClueMappingPo record, @Param("example") CrmBsiOptClueMappingPoExample example);

    int updateByPrimaryKeySelective(CrmBsiOptClueMappingPo record);

    int updateByPrimaryKey(CrmBsiOptClueMappingPo record);
}