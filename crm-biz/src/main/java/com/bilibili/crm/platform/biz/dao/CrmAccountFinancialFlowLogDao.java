package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmAccountFinancialFlowLogPo;
import com.bilibili.crm.platform.biz.po.CrmAccountFinancialFlowLogPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CrmAccountFinancialFlowLogDao {
    long countByExample(CrmAccountFinancialFlowLogPoExample example);

    int deleteByExample(CrmAccountFinancialFlowLogPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CrmAccountFinancialFlowLogPo record);

    int insertSelective(CrmAccountFinancialFlowLogPo record);

    List<CrmAccountFinancialFlowLogPo> selectByExample(CrmAccountFinancialFlowLogPoExample example);

    CrmAccountFinancialFlowLogPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmAccountFinancialFlowLogPo record, @Param("example") CrmAccountFinancialFlowLogPoExample example);

    int updateByExample(@Param("record") CrmAccountFinancialFlowLogPo record, @Param("example") CrmAccountFinancialFlowLogPoExample example);

    int updateByPrimaryKeySelective(CrmAccountFinancialFlowLogPo record);

    int updateByPrimaryKey(CrmAccountFinancialFlowLogPo record);
}