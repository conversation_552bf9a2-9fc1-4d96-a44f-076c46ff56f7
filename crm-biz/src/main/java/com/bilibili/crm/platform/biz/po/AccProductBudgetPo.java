package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccProductBudgetPo implements Serializable {
    private Long id;

    /**
     * 品牌id
     */
    private Integer productId;

    /**
     * 预算（分）
     */
    private Long budget;

    /**
     * 年份
     */
    private String yearStr;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除0未删除1 删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}