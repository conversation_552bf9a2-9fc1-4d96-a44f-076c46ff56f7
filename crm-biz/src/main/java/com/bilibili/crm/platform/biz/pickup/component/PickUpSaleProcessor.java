package com.bilibili.crm.platform.biz.pickup.component;

import com.bilibili.adp.common.bean.DropboxDto;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.pickup.INewPickupSettleService;
import com.bilibili.crm.platform.api.pickup.dto.NewPickupSettleDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale_sea.dto.PickupSaleResultDto;
import com.bilibili.crm.platform.api.sale_sea.dto.PickupSaleSeaDto;
import com.bilibili.crm.platform.biz.common.PickupCheckOrPaymentSubjectEnum;
import com.bilibili.crm.platform.biz.common.PickupSettleTypeEnum;
import com.bilibili.crm.platform.biz.dao.CrmPickupSettleNewDao;
import com.bilibili.crm.platform.biz.po.CrmPickupSettleNewPo;
import com.bilibili.crm.platform.biz.po.CrmPickupSettleNewPoExample;
import com.bilibili.crm.platform.biz.po.CrmSalePo;
import com.bilibili.crm.platform.biz.repo.CrmSaleRepo;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/7/8 下午4:46
 */
@Slf4j
@Component
public class PickUpSaleProcessor {

    @Autowired
    private CrmPickupSettleNewDao crmPickupSettleNewDao;
    @Autowired
    private CrmSaleRepo crmSaleRepo;
    @Autowired
    private PickupOrderSaveSalesMsgSendHandler pickupOrderSaveSalesMsgSendHandler;
    @Autowired
    private ISaleService saleService;
    @Autowired
    private INewPickupSettleService iNewPickupSettleService;

    /**
     * 保存销售
     *
     * @param operator
     * @param pickupSaleSeaDto 入参：销售 id 如果为空或0，都是将订单的销售置为0
     */
    public Integer saveSales(Operator operator, PickupSaleSeaDto pickupSaleSeaDto) {
        // 校验，不允许同时为空
        checkParams(pickupSaleSeaDto);

        Map<BiliUserType, List<Integer>> biliUserSaleMap = pickupSaleSeaDto.getBiliUserSaleMap();

        List<Integer> channelSaleIds = biliUserSaleMap.get(BiliUserType.CHANNEL_MANAGER);
        List<Integer> directSaleIds = biliUserSaleMap.get(BiliUserType.STRAIGHT_MANAGER);
        if (CollectionUtils.isEmpty(channelSaleIds) && CollectionUtils.isEmpty(directSaleIds)) {
            throw new ServiceRuntimeException("直客销售与渠道销售不能同时为空！");
        }
        Integer channelSaleId = null;
        Integer directSaleId = null;
        if (!CollectionUtils.isEmpty(channelSaleIds)) {
            channelSaleId = channelSaleIds.get(0);
        } else {
            channelSaleId = 0;
        }
        if (!CollectionUtils.isEmpty(directSaleIds)) {
            directSaleId = directSaleIds.get(0);
        } else {
            directSaleId = 0;
        }

        CrmPickupSettleNewPoExample example = new CrmPickupSettleNewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPickupOrderNoEqualTo(pickupSaleSeaDto.getOrderNo());

        CrmPickupSettleNewPo record = new CrmPickupSettleNewPo();
        record.setChannelSaleId(channelSaleId);
        record.setDirectSaleId(directSaleId);
        log.info("=====> saveSales, order no:{}, direct sale id:{}, channel sale id:{}",
                pickupSaleSeaDto.getOrderNo(), directSaleId, channelSaleId);
        int count = crmPickupSettleNewDao.updateByExampleSelective(record, example);


        // todo simer 添加操作记录
        // 发送消息给花火
//        PickupOrderSaveSaleMsgDto pickupOrderSaveSaleMsgDto = PickupOrderSaveSaleMsgDto.builder().orderId(pickupSaleSeaDto.getObj_id()).orderNo(pickupSaleSeaDto.getOrderNo()).build();
//        pickupOrderSaveSalesMsgSendHandler.convertAndSend(pickupOrderSaveSaleMsgDto);
        return count;
    }


    public Integer updateSubject(Operator operator, NewPickupSettleDto newPickupSettleDto) {
        //校验，不允许同时为空
        checkParamsWithPickup(newPickupSettleDto);
        CrmPickupSettleNewPoExample example = new CrmPickupSettleNewPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andPickupOrderNoEqualTo(newPickupSettleDto.getPickupOrderNo());

        CrmPickupSettleNewPo po = CrmPickupSettleNewPo.builder()
                .checkSubjectId(newPickupSettleDto.getCheckSubjectId())
                .paymentSubjectId(newPickupSettleDto.getPaymentSubjectId())
                .paymentTypeId(newPickupSettleDto.getPaymentTypeId())
                .build();
        log.info("=====> updateSubject, order no:{}, checkSubjectId:{}, paymentSubjectId:{}",
                newPickupSettleDto.getPickupOrderNo(), newPickupSettleDto.getCheckSubjectId(), newPickupSettleDto.getPaymentSubjectId());
        int count = crmPickupSettleNewDao.updateByExampleSelective(po, example);
        return count;
    }

    private void checkParamsWithPickup(NewPickupSettleDto newPickupSettleDto) {
        Assert.isTrue(newPickupSettleDto.getPickupOrderNo() != null, "花火订单编号不能为空！");
        Assert.isTrue(newPickupSettleDto.getCheckSubjectId() != null, "花火计收主体不能为空！");
        Assert.isTrue(newPickupSettleDto.getPaymentSubjectId() != null, "花火付款主体不能为空！");
    }

    //此处需要加一个事务，如果发生异常的话就直接回滚
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer batchUpdateSubject(Operator operator, List<String> rowsList) {
        if (CollectionUtils.isEmpty(rowsList)) {
            throw new ServiceRuntimeException("没有数据！");
        }
        List<NewPickupSettleDto> newPickupSettleDtos = convertExcelRows2PickupSettleDto(rowsList);
        Integer count = 0;
        if (!CollectionUtils.isEmpty(newPickupSettleDtos)){
            for (NewPickupSettleDto newPickupSettleDto : newPickupSettleDtos){
                updateSubject(operator,newPickupSettleDto);
                count++;
            }
        }
        log.info("=====> updateSubject success, size:{},now is{},operatorName is {}", newPickupSettleDtos.size(),System.currentTimeMillis(),operator.getOperatorName());
        return count;
    }

    private List<NewPickupSettleDto> convertExcelRows2PickupSettleDto(List<String> rowsList) {
        List<NewPickupSettleDto> newPickupSettleDtos =new ArrayList<>();
        for (int i =0; i<rowsList.size();i++) {
            if (i != 0) {//第一行是标题
                NewPickupSettleDto newPickupSettleDto = new NewPickupSettleDto();
                String[] fields = rowsList.get(i).split(",");
                if (fields.length != 3) {
                    Assert.isTrue(fields.length == 3, "导入的数据格式不正确，请重新导入");
                    throw new ServiceRuntimeException("导入的数据格式不正确，请重新导入" + rowsList.get(i));
                }
                String orderNoStr = fields[0].trim();
                String checkSubjectStr = fields[1].trim();
                String paymentSubjectStr = fields[2].trim();

                if (!StringUtils.isNotEmpty(orderNoStr)) {
                    Assert.isTrue(orderNoStr != null, "导入的数据格式不正确,花火订单编号不能为空，请重新导入");
                    throw new ServiceRuntimeException("导入的数据格式不正确,花火订单编号不能为空，请重新导入");
                }
                //计收主体和付款主体是不可以同时为空的
                if (!StringUtils.isNotEmpty(checkSubjectStr) && !StringUtils.isNotEmpty(paymentSubjectStr)) {
                    Assert.isTrue(1!=1,"导入的数据格式不正确,计收主体与付款主体不能同时为空！"+orderNoStr);
                    throw new ServiceRuntimeException("导入的数据格式不正确,计收主体与付款主体不能同时为空！"+orderNoStr);
                }
                //计收主体和付款主体两者必须同时有值
                if(!StringUtils.isNotEmpty(checkSubjectStr) || !StringUtils.isNotEmpty(paymentSubjectStr)){
                    Assert.isTrue(1!=1,"导入的数据格式不正确,计收主体与付款主体两者必须同时有值"+orderNoStr);
                    throw new ServiceRuntimeException("导入的数据格式不正确,计收主体与付款主体两者必须同时有值"+orderNoStr);
                }

                if (StringUtils.isNotEmpty(checkSubjectStr)) {
                    Boolean flag = false;
                    for (PickupCheckOrPaymentSubjectEnum type : PickupCheckOrPaymentSubjectEnum.values()) {
                        if (type.getName().equals(checkSubjectStr)) {
                            if (!checkSubjectStr.equals("未知")){
                                newPickupSettleDto.setCheckSubjectId(type.getCode());
                                flag = true;
                            }
                        }
                    }
                    if (!flag){
                        Assert.isTrue(flag,"导入的数据格式不正确,该计收主体名称错误"+checkSubjectStr);
                        throw new ServiceRuntimeException("导入的数据格式不正确,该计收主体名称错误"+checkSubjectStr);
                    }
                }

                if (StringUtils.isNotEmpty(paymentSubjectStr)) {
                    Boolean flag = false;
                    for (PickupCheckOrPaymentSubjectEnum type : PickupCheckOrPaymentSubjectEnum.values()) {
                        if (type.getName().equals(paymentSubjectStr)) {
                            if (!paymentSubjectStr.equals("未知")){
                                newPickupSettleDto.setPaymentSubjectId(type.getCode());
                                flag = true;
                            }
                        }
                    }
                    if (!flag){
                        Assert.isTrue(flag,"导入的数据格式不正确,该付款主体名称错误"+paymentSubjectStr);
                        throw new ServiceRuntimeException("导入的数据格式不正确,该付款主体名称错误"+paymentSubjectStr);
                    }
                }
                newPickupSettleDto.setPickupOrderNo(Long.valueOf(orderNoStr));
                iNewPickupSettleService.buildPaymentType(newPickupSettleDto);
                newPickupSettleDtos.add(newPickupSettleDto);
            }
        }
        return newPickupSettleDtos;
    }

    /**
     * 批量保存销售
     * @param operator
     * @param rowsList
     * @return
     */
    //此处需要加一个事务，如果发生异常的话就直接回滚
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer batchSaveSales(Operator operator, List<String> rowsList) {
        if (CollectionUtils.isEmpty(rowsList)) {
            throw new ServiceRuntimeException("没有数据！");
        }
        List<PickupSaleSeaDto> pickupSaleSeaDtos = convertExcelRows2SaleSeaData(rowsList);
        int count =0;
        if (!CollectionUtils.isEmpty(pickupSaleSeaDtos)){
            for (PickupSaleSeaDto pickupSaleSeaDto : pickupSaleSeaDtos){
                saveSales(operator,pickupSaleSeaDto);
                count++;
            }
        }
        log.info("=====> batchSaveSales success, size:{},now is{},operatorName is {}", rowsList.size(),System.currentTimeMillis(),operator.getOperatorName());
        return count;
    }

    private List<PickupSaleSeaDto> convertExcelRows2SaleSeaData(List<String> rowsList) {
        List<PickupSaleSeaDto> pickupSaleSeaDtos =new ArrayList<>();
        for (int i =0; i<rowsList.size();i++) {
            if (i != 0) {//第一行是标题
                PickupSaleSeaDto pickupSaleSeaDto = PickupSaleSeaDto.builder().build();
                String[] fields = rowsList.get(i).split(",");
                if (fields.length < 2 || fields.length>3) {
                    Assert.isTrue(fields.length == 3, "导入的数据格式不正确，请重新导入");
                    throw new ServiceRuntimeException("导入的数据格式不正确，请重新导入" + rowsList.get(i));
                }
                String orderNoStr = fields[0].trim();
                String directSaleIdsStr = fields[1].trim();
                String channelSaleIdsStr=null;
                if (fields.length!=2){
                    channelSaleIdsStr=fields[2].trim();
                }

                Map<BiliUserType, List<Integer>> biliUserSaleMap = new HashMap<>();
                List<Integer> directSaleIds = null;
                List<Integer> channelSaleIds = null;

                if (!StringUtils.isNotEmpty(orderNoStr)) {
                    Assert.isTrue(orderNoStr != null, "导入的数据格式不正确,订单ID不能为空，请重新导入");
                    throw new ServiceRuntimeException("导入的数据格式不正确,订单ID不能为空，请重新导入");
                }
                if (!StringUtils.isNotEmpty(directSaleIdsStr) && !StringUtils.isNotEmpty(channelSaleIdsStr)) {
                    Assert.isTrue(1!=1,"导入的数据格式不正确,直客销售与渠道销售不能同时为空！");
                    throw new ServiceRuntimeException("导入的数据格式不正确,直客销售与渠道销售不能同时为空！");
                }
                if (StringUtils.isNotEmpty(directSaleIdsStr)) {
                    //通过名称去找ID
                    List<SaleDto> directSaleDto = saleService.getByName(directSaleIdsStr);
                    if (!CollectionUtils.isEmpty(directSaleDto)) {
                        if (directSaleDto.size() != 1) {
                            Assert.isTrue(directSaleDto.size() == 1, "直客销售名称重复！"+directSaleIdsStr);
                            throw new ServiceRuntimeException("导入的数据格式不正确,直客销售名称重复！");
                        }
                        //直客销售的销售类型只能是直客
                        if(!Arrays.asList(1,3).contains(saleService.getSalesInIds(Arrays.asList(directSaleDto.get(0).getId())).get(0).getType())) {
                            Assert.isTrue(Arrays.asList(1,3).contains(saleService.getSalesInIds(Arrays.asList(directSaleDto.get(0).getId())).get(0).getType()),"导入的销售类型不匹配！"+directSaleIdsStr);
                            throw new ServiceRuntimeException("导入的销售类型不匹配！"+directSaleIdsStr);
                        }
                        directSaleIds = Arrays.asList(directSaleDto.get(0).getId());
                    } else {
                        Assert.isTrue(!CollectionUtils.isEmpty(directSaleDto), "暂无该直客销售，请核对后再导入"+directSaleIdsStr);
                        throw new ServiceRuntimeException("暂无该直客销售，请核对后再导入");
                    }
                }
                if (StringUtils.isNotEmpty(channelSaleIdsStr)) {
                    //通过名称去找ID
                    List<SaleDto> channelSaleDto = saleService.getByName(channelSaleIdsStr);
                    if (!CollectionUtils.isEmpty(channelSaleDto)) {
                        if (channelSaleDto.size() != 1) {
                            Assert.isTrue(channelSaleDto.size() == 1, "渠道销售名称重复！"+channelSaleIdsStr);
                            throw new ServiceRuntimeException("导入的数据格式不正确,渠道销售名称重复！");
                        }
                        //渠道销售的销售类型只能是渠道
                        if (!Arrays.asList(2,4).contains(saleService.getSalesInIds(Arrays.asList(channelSaleDto.get(0).getId())).get(0).getType())){
                            Assert.isTrue(Arrays.asList(2,4).contains(saleService.getSalesInIds(Arrays.asList(channelSaleDto.get(0).getId())).get(0).getType()),"导入的销售类型不匹配!"+channelSaleIdsStr);
                            throw new ServiceRuntimeException("导入的销售类型不匹配！"+channelSaleIdsStr);
                        }
                        channelSaleIds = Arrays.asList(channelSaleDto.get(0).getId());
                    } else {
                        Assert.isTrue(!CollectionUtils.isEmpty(channelSaleDto), "暂无该渠道销售，请核对后再导入");
                        throw new ServiceRuntimeException("暂无该渠道销售，请核对后再导入");
                    }
                }
                biliUserSaleMap.put(BiliUserType.STRAIGHT_MANAGER, directSaleIds);
                biliUserSaleMap.put(BiliUserType.CHANNEL_MANAGER, channelSaleIds);
                pickupSaleSeaDto.setBiliUserSaleMap(biliUserSaleMap);
                pickupSaleSeaDto.setOrderNo(Long.valueOf(orderNoStr));
                pickupSaleSeaDtos.add(pickupSaleSeaDto);
            }
        }
        return pickupSaleSeaDtos;
    }

    private void checkParams(PickupSaleSeaDto pickupSaleSeaDto) {
        Assert.isTrue(pickupSaleSeaDto.getOrderNo() != null, "订单号不能为空！");
        Assert.isTrue(pickupSaleSeaDto.getBiliUserSaleMap() != null, "订单的销售不能为空！");

    }

    public PickupSaleResultDto querySalesByOrderNo(Long orderNo) {
        Assert.notNull(orderNo, "订单编号不能为空！");

        // 获取订单备注
        CrmPickupSettleNewPoExample example = new CrmPickupSettleNewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPickupOrderNoEqualTo(orderNo).andObjTypeEqualTo(PickupSettleTypeEnum.PICKUP_ORDER.getCode());
        List<CrmPickupSettleNewPo> crmPickupSettleNewPos = crmPickupSettleNewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmPickupSettleNewPos)) {
            return null;
        }
        CrmPickupSettleNewPo crmPickupSettleNewPo = crmPickupSettleNewPos.get(0);
        List<Integer> saleIds = Arrays.asList(crmPickupSettleNewPo.getChannelSaleId(), crmPickupSettleNewPo.getDirectSaleId());
        Map<Integer, CrmSalePo> saleMap = crmSaleRepo.queryMapByIds(saleIds);

        PickupSaleResultDto pickupSaleResultDto = buildPickupSaleResultDto(saleMap, crmPickupSettleNewPo);
        return pickupSaleResultDto;
    }

    public List<PickupSaleResultDto> querySaleListByOrderNos(List<Long> orderNos) {
        Assert.isTrue(!CollectionUtils.isEmpty(orderNos), "订单编号列表不能为空！");

        // 花火订单
        CrmPickupSettleNewPoExample example = new CrmPickupSettleNewPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPickupOrderNoIn(orderNos).andObjTypeEqualTo(PickupSettleTypeEnum.PICKUP_ORDER.getCode());
        List<CrmPickupSettleNewPo> crmPickupSettleNewPos = crmPickupSettleNewDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmPickupSettleNewPos)) {
            return Collections.EMPTY_LIST;
        }

        // 获取直客销售与渠道销售 ids
        List<Integer> directSaleIds = crmPickupSettleNewPos.stream().map(crmPickupSettleNewPo -> crmPickupSettleNewPo.getDirectSaleId()).distinct().collect(Collectors.toList());
        List<Integer> channelSaleIds = crmPickupSettleNewPos.stream().map(crmPickupSettleNewPo -> crmPickupSettleNewPo.getChannelSaleId()).distinct().collect(Collectors.toList());
        List<Integer> allSaleIds = new ArrayList<>();
        if (!CollectionUtils.isEmpty(directSaleIds)) {
            allSaleIds.addAll(directSaleIds);
        }
        if (!CollectionUtils.isEmpty(channelSaleIds)) {
            allSaleIds.addAll(channelSaleIds);
        }
        Map<Integer, CrmSalePo> saleMap = crmSaleRepo.queryMapByIds(allSaleIds);
        List<PickupSaleResultDto> pickupSaleResultDtos = new ArrayList<>();
        for (CrmPickupSettleNewPo crmPickupSettleNewPo : crmPickupSettleNewPos) {
            PickupSaleResultDto pickupSaleResultDto = buildPickupSaleResultDto(saleMap, crmPickupSettleNewPo);
            pickupSaleResultDtos.add(pickupSaleResultDto);
        }
        return pickupSaleResultDtos;
    }

    private PickupSaleResultDto buildPickupSaleResultDto(Map<Integer, CrmSalePo> saleMap, CrmPickupSettleNewPo crmPickupSettleNewPo) {
        PickupSaleResultDto pickupSaleResultDto = PickupSaleResultDto.builder().build();
        pickupSaleResultDto.setOrderNo(crmPickupSettleNewPo.getPickupOrderNo());
        pickupSaleResultDto.setSale_remark(crmPickupSettleNewPo.getSaleRemark());

        Map<Integer, List<DropboxDto>> userTypeSaleMap = new HashMap<>();
        List<DropboxDto> directDropboxList = new ArrayList<>();
        List<DropboxDto> channelDropboxList = new ArrayList<>();
        if (crmPickupSettleNewPo.getChannelSaleId() > 0) {
            channelDropboxList.add(DropboxDto.builder().id(crmPickupSettleNewPo.getChannelSaleId()).name(saleMap.getOrDefault(crmPickupSettleNewPo.getChannelSaleId(), CrmSalePo.builder().build()).getName()).build());
        }
        if (crmPickupSettleNewPo.getDirectSaleId() > 0) {
            directDropboxList.add(DropboxDto.builder().id(crmPickupSettleNewPo.getDirectSaleId()).name(saleMap.getOrDefault(crmPickupSettleNewPo.getDirectSaleId(), CrmSalePo.builder().build()).getName()).build());
        }
        userTypeSaleMap.put(BiliUserType.CHANNEL_MANAGER.getCode(), channelDropboxList);
        userTypeSaleMap.put(BiliUserType.STRAIGHT_MANAGER.getCode(), directDropboxList);

        pickupSaleResultDto.setUser_type_sales_map(userTypeSaleMap);
        return pickupSaleResultDto;
    }

    public Map<Long, PickupSaleResultDto> querySaleMapByOrderNos(List<Long> orderNos) {
        List<PickupSaleResultDto> pickupSaleResultDtos = this.querySaleListByOrderNos(orderNos);
        return pickupSaleResultDtos.stream().collect(Collectors.toMap(pickupSaleResultDto -> pickupSaleResultDto.getOrderNo(), pickupSaleResultDto -> pickupSaleResultDto, (v1, v2) -> v1));
    }
}
