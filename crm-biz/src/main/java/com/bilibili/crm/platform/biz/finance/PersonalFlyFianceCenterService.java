package com.bilibili.crm.platform.biz.finance;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.account.bean.AccAccountEsBean;
import com.bilibili.crm.biz.account.bean.AccountComplexQuery;
import com.bilibili.crm.biz.account.query.IQueryAccountEsService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.NumberUtils;
import com.bilibili.crm.platform.api.fly.dto.FlyDataDto;
import com.bilibili.crm.platform.api.kingdee.dto.ArOtherBill;
import com.bilibili.crm.platform.api.kingdee.dto.PersonalFlyBillDto;
import com.bilibili.crm.platform.biz.boss.service.BossFileService;
import com.bilibili.crm.platform.biz.finance.common.FinanceBizType;
import com.bilibili.crm.platform.biz.finance.common.FinanceCenterConstant;
import com.bilibili.crm.platform.biz.finance.dto.CrmOrderBill;
import com.bilibili.crm.platform.biz.finance.dto.PersonalFlyOrderBill;
import com.bilibili.crm.platform.biz.finance.facade.FinanceCenterFacade;
import com.bilibili.crm.platform.biz.kingdee.service.conf.KingDeeConfig;
import com.bilibili.crm.platform.biz.service.fly.FlyConfig;
import com.bilibili.crm.platform.biz.service.fly.PersonFlyPayStyle;
import com.bilibili.crm.platform.biz.service.stat.PersonFlyService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.google.common.collect.Lists;
import io.swagger.models.auth.In;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.function.Function;
import java.util.stream.Collectors;

@Slf4j
@Service
public class PersonalFlyFianceCenterService {

    @Resource
    private BossFileService bossFileService;

    @Resource
    private PersonFlyService personFlyService;

    @Resource
    private IQueryAccountEsService iQueryAccountEsService;

    @Resource
    private FinanceCenterFacade financeCenterFacade;

    @Resource
    private FianceCenterHelper fianceCenterHelper;

    private static final String adminOrgUnitName = "商业产品部";

    public int monthOrderBill(Timestamp begin, Timestamp end) {
        List<PersonalFlyBillDto> arOtherBills = monthArOtherBill(begin, end);
        List<PersonalFlyOrderBill> crmOrderBills = buildOrderBill(arOtherBills);
        log.info("PersonalFlyFianceCenterService size:{}", crmOrderBills.size());
        if (CollectionUtils.isEmpty(crmOrderBills)) {
            return 0;
        }
        bossFileService.uploadBigList(crmOrderBills, CrmUtils.formatDate(begin, CrmUtils.YYYY_MM) + "_个人起飞");
        List<List<PersonalFlyOrderBill>> partitionList = Lists.partition(crmOrderBills, KingDeeConfig.BATCH_IMPORT_SIZE);
        String token = financeCenterFacade.buildToken();
        partitionList.forEach(partition -> {
            String billString = JSON.toJSONString(fianceCenterHelper.buildRequest(partition, FinanceBizType.BI_HUO_BILL));
            log.info("PersonalFlyFianceCenterService billString = {}", billString);
            String result = financeCenterFacade.doPost(FinanceCenterConstant.CRM_ORDER_URL, FinanceCenterConstant.CRM_ORDER_PARAM, billString, token);
            fianceCenterHelper.logErrorData(FinanceBizType.AR_OTHER_BILL, result);
            log.info("PersonalFlyFianceCenterService size:{}", partition.size());
            log.info("PersonalFlyFianceCenterService result:{}", result);
        });
        return crmOrderBills.size();
    }


    public List<PersonalFlyBillDto> monthArOtherBill(Timestamp begin, Timestamp end) {
        return getPersonalFlyBill(begin, end);
    }


    public List<PersonalFlyBillDto> getPersonalFlyBill(Timestamp begin, Timestamp end) {
        List<FlyDataDto> dtos = personFlyService.queryFly(begin.getTime(), end.getTime(), null, null);
        if (CollectionUtils.isEmpty(dtos)) {
            AlarmHelper.log("getPersonalFlyBill QueryFlyDataEmpty");
            return new ArrayList<>();
        }
        Timestamp monthEndDay = CrmUtils.getEndMonthDayOfTime(begin);
        String monthEndDayString = CrmUtils.getTimestamp2String(monthEndDay, "yyyy-MM-dd");
        List<Integer> accountIds = dtos.stream().map(FlyDataDto::getAccountId).distinct().collect(Collectors.toList());
        List<List<Integer>> partition = Lists.partition(accountIds, 50000);
        List<AccAccountEsBean> allAccountList = Lists.newArrayList();
        for (List<Integer> integers : partition) {
            AccountComplexQuery queryAccountParam = AccountComplexQuery.builder()
                    .accountIds(integers)// 查询参数
                    .bizScene("personFlyData") // 业务场景，必传，找crm开发申请
                    .appId("sycpb.cpm.crm-portal") // 调用方的应用名，必传，找crm开发申请
                    .build();
            List<AccAccountEsBean> tmp = iQueryAccountEsService.queryAccount(queryAccountParam);
            allAccountList.addAll(tmp);
        }
        Map<Integer, String> accountNameMap = allAccountList.stream().collect(Collectors.toMap(AccAccountEsBean::getAccountId, AccAccountEsBean::getUsername));

        // 过率花火现金部分呢
        List<PersonalFlyBillDto> personalFlyBillDtos = dtos.stream().filter(dto -> {
            if (PersonFlyPayStyle.HUAHUO_CASH.getCode().equals(dto.getPayStyle())) {
                return false;
            }
            return true;
        }).map(dto -> {
            PersonalFlyBillDto personalFlyBillDto = PersonalFlyBillDto.builder()
                    .sourceNumber(getSourceNumber(monthEndDayString, dto.getAccountId(), dto.getPayStyle(), dto.getIsWithHold()))
                    .transactionDate(monthEndDayString)
                    .AdvertiserId(dto.getAccountId())
                    .AdvertiserName(accountNameMap.get(dto.getAccountId()))
                    .adminOrgUnitName(adminOrgUnitName)
                    .paymentMethodName(PersonFlyPayStyle.getByCode(dto.getPayStyle()).getDesc())
                    .isHosting(FlyConfig.WITHHOLD_SOURCE.equals(dto.getSource()) ? "Y" : "N" )
                    // 整体消耗
                    .totalConsumption(Utils.fromFenToYuan(dto.getTotalConsume()))
                    .cashConsumption(Utils.fromFenToYuan(dto.getTotalCashCost()))
                    .goodsConsumption(CrmUtils.fromMilliToYuanWithFourDecimal(dto.getCostAfterSupport()))
                    .couponConsumption(CrmUtils.fromMilliToYuanWithFourDecimal(dto.getCouponCost()))
                    .totalRecharge(Utils.fromFenToYuan(dto.getRechargeAmount()))
                    .cashRecharge(Utils.fromFenToYuan(dto.getCashRechargeAmount()))
                    .couponRecharge(Utils.fromFenToYuan(dto.getCouponRechargeAmount()))
                    .totalRefund(Utils.fromFenToYuan(dto.getRefundAmount()))
                    .cashRefund(Utils.fromFenToYuan(dto.getCashRefundAmount()))
                    .couponRefund(CrmUtils.fromMilliToYuanWithFourDecimal(dto.getCouponRefundAmount()))
                    .build();
            return personalFlyBillDto;
                }).collect(Collectors.toList());

        // 聚合处理
        Map<String, List<PersonalFlyBillDto>> maps = personalFlyBillDtos.stream().collect(Collectors.groupingBy(PersonalFlyBillDto::getSourceNumber));
        return maps.entrySet().stream()
                .map(e -> aggItem(e.getKey(), e.getValue()))
                .collect(Collectors.toList());
    }

    private PersonalFlyBillDto aggItem(String key, List<PersonalFlyBillDto> list) {
        PersonalFlyBillDto res = new PersonalFlyBillDto();
        res.setSourceNumber(key);
        for (PersonalFlyBillDto personFlyData : list) {
            res.setTransactionDate(personFlyData.getTransactionDate());
            res.setAdvertiserId(personFlyData.getAdvertiserId());
            res.setAdvertiserName(personFlyData.getAdvertiserName());
            res.setAdminOrgUnitName(personFlyData.getAdminOrgUnitName());
            res.setPaymentMethodName(personFlyData.getPaymentMethodName());
            res.setIsHosting(personFlyData.getIsHosting());
            res.setTotalConsumption(NumberUtils.add(res.getTotalConsumption(), personFlyData.getTotalConsumption()));
            res.setCashConsumption(NumberUtils.add(res.getCashConsumption(), personFlyData.getCashConsumption()));
            res.setGoodsConsumption(NumberUtils.add(res.getGoodsConsumption(), personFlyData.getGoodsConsumption()));
            res.setCouponConsumption(NumberUtils.add(res.getCouponConsumption(), personFlyData.getCouponConsumption()));
            res.setTotalRecharge(NumberUtils.add(res.getTotalRecharge(), personFlyData.getTotalRecharge()));
            res.setCashRecharge(NumberUtils.add(res.getCashRecharge(), personFlyData.getCashRecharge()));
            res.setCouponRecharge(NumberUtils.add(res.getCouponRecharge(), personFlyData.getCouponRecharge()));
            res.setTotalRefund(NumberUtils.add(res.getTotalRefund(), personFlyData.getTotalRefund()));
            res.setCashRefund(NumberUtils.add(res.getCashRefund(), personFlyData.getCashRefund()));
            res.setCouponRefund(NumberUtils.add(res.getCouponRefund(), personFlyData.getCouponRefund()));
        }
        return res;
    }


    private String getSourceNumber(String monthEndDayString, Integer accountId, Integer payType, Integer isHosting) {
        String isHostingString = Objects.equals(isHosting, 1) ? "Y" : "N";
        return monthEndDayString + "_" + accountId + "_" + adminOrgUnitName + "_" + PersonFlyPayStyle.getByCode(payType).getDesc() +"_" + isHostingString;
    }

    public  List<PersonalFlyOrderBill> buildOrderBill(List<PersonalFlyBillDto> arOtherBills) {
        List<PersonalFlyOrderBill> crmOrderBills = Lists.newArrayList();
        for (PersonalFlyBillDto arOtherBill : arOtherBills) {
            PersonalFlyOrderBill crmOrderBill = PersonalFlyOrderBill.builder()
                    .sourceNumber(arOtherBill.getSourceNumber())
                    .transactionDate(arOtherBill.getTransactionDate())
                    .AdvertiserId(arOtherBill.getAdvertiserId())
                    .AdvertiserName(arOtherBill.getAdvertiserName())
                    .adminOrgUnitCode(arOtherBill.getAdminOrgUnitCode())
                    .adminOrgUnitName(arOtherBill.getAdminOrgUnitName())
                    .paymentMethodCode(arOtherBill.getPaymentMethodCode())
                    .paymentMethodName(arOtherBill.getPaymentMethodName())
                    .isHosting(arOtherBill.getIsHosting())
                    .totalConsumption(arOtherBill.getTotalConsumption())
                    .cashConsumption(arOtherBill.getCashConsumption())
                    .goodsConsumption(arOtherBill.getGoodsConsumption())
                    .couponConsumption(arOtherBill.getCouponConsumption())
                    .totalRecharge(arOtherBill.getTotalRecharge())
                    .cashRecharge(arOtherBill.getCashRecharge())
                    .couponRecharge(arOtherBill.getCouponRecharge())
                    .totalRefund(arOtherBill.getTotalRefund())
                    .cashRefund(arOtherBill.getCashRefund())
                    .couponRefund(arOtherBill.getCouponRefund())
                    .build();
            crmOrderBills.add(crmOrderBill);
        }
        return crmOrderBills;
    }



}
