package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCustomerBillInfoPo;
import com.bilibili.crm.platform.biz.po.CrmCustomerBillInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmCustomerBillInfoDao {
    long countByExample(CrmCustomerBillInfoPoExample example);

    int deleteByExample(CrmCustomerBillInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmCustomerBillInfoPo record);

    int insertBatch(List<CrmCustomerBillInfoPo> records);

    int insertUpdateBatch(List<CrmCustomerBillInfoPo> records);

    int insert(CrmCustomerBillInfoPo record);

    int insertUpdateSelective(CrmCustomerBillInfoPo record);

    int insertSelective(CrmCustomerBillInfoPo record);

    List<CrmCustomerBillInfoPo> selectByExample(CrmCustomerBillInfoPoExample example);

    CrmCustomerBillInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmCustomerBillInfoPo record, @Param("example") CrmCustomerBillInfoPoExample example);

    int updateByExample(@Param("record") CrmCustomerBillInfoPo record, @Param("example") CrmCustomerBillInfoPoExample example);

    int updateByPrimaryKeySelective(CrmCustomerBillInfoPo record);

    int updateByPrimaryKey(CrmCustomerBillInfoPo record);
}