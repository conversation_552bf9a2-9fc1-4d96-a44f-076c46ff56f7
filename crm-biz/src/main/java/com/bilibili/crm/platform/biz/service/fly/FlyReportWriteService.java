package com.bilibili.crm.platform.biz.service.fly;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.DeductionSign;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.service.IAccountWalletLogService;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.fly.IFlyReportWriteService;
import com.bilibili.crm.platform.api.oa.dto.OaNewCommonResponse;
import com.bilibili.crm.platform.api.stat.dto.FlyStatDto;
import com.bilibili.crm.platform.api.wallet.dto.WalletLogDetailBean;
import com.bilibili.crm.platform.biz.addata.dao.read.PersonalUpUnitSupportFactorDao;
import com.bilibili.crm.platform.biz.bean.NewFlyUpOrderDataDto;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.charging.AdStatAccountDayDao;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.FlyExtendEsPo;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.migrate.FlyExtendEsNewPo;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.repository.FlyExtendRepository;
import com.bilibili.crm.platform.biz.po.charging.AdStatAccountDayPo;
import com.bilibili.crm.platform.biz.po.PersonalUpUnitSupportFactorPo;
import com.bilibili.crm.platform.biz.po.PersonalUpUnitSupportFactorPoExample;
import com.bilibili.crm.platform.biz.po.charging.AdStatAccountDayPoExample;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.WalletOperationType;
import com.bilibili.report.platform.api.dto.StatDto;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import lombok.extern.slf4j.Slf4j;
import okhttp3.Request;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.Assert;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: brady
 * @time: 2021/7/28 3:30 下午
 */
@Service
@Slf4j
public class FlyReportWriteService implements IFlyReportWriteService {
    @Autowired
    private FlyExtendRepository flyExtendRepository;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private IAccountWalletLogService accountWalletLogService;
    @Autowired
    private PersonalUpUnitSupportFactorDao personalUpUnitSupportFactorDao;
    private static List<Integer> FLY_TRUST_DEDUCTION_SIGN = Lists.newArrayList(
            DeductionSign.PERSON_FLY_TRUST_FUND.getCode(),
            DeductionSign.PERSON_FLY_TRUST_CASH.getCode(),
            DeductionSign.PERSON_FLY_TRUST_INCENTIVE.getCode(),
            DeductionSign.PERSON_FLY_TRUST_FLY_COIN.getCode(),
            DeductionSign.PERSON_FLY_TRUST_FLYV2_CASH.getCode(),
            DeductionSign.PERSON_FLY_TRUST_FLYV2_FLY_COIN.getCode());
    @Value("${crm.new.fly.order.url:http://uat-cm.bilibili.co/x/internal/fly/web/fly2crm/order/list?}")
    private String newFlyCallUrl;
    @Value("${crm.portal.env:prd}")
    private String env;
    @Autowired
    private WxRobotService wxRobotService;
    @Autowired
    private CrmCacheManager crmCacheManager;
    @Autowired
    private AdStatAccountDayDao adStatAccountDayDao;
    @Autowired
    private EsMigrateMessageSender<FlyExtendEsNewPo> messageSender;

    private final static BigDecimal HUNDRED = new BigDecimal(100);
    private final static BigDecimal THOUSAND = new BigDecimal(1000);
    private final static BigDecimal MILLIS = new BigDecimal(100000);
    @Autowired
    private PaladinConfig paladinConfig;

    @Override
    public void batchFlushWithholdMoney(Timestamp begin, Timestamp end) {
        List<Timestamp> eachDays = Utils.getEachDays(begin, end);
        eachDays.forEach(this::flushWithholdMoney);
    }

    /**
     * 刷入托管资金
     */
    public void flushWithholdMoney(Timestamp date) {
        //账号 ，类型 ，资金
        List<WalletLogDetailBean> walletLogs = accountWalletLogService.getFlyWithholdMoneyByDate(date);
        Map<String, FlyStatDto> accountWalletMap = new HashMap<>();
        //写入资金  wallet->stat
        List<FlyStatDto> stats = Lists.newArrayList();
        for (WalletLogDetailBean dto : walletLogs) {
            if (Utils.isPositive(dto.getTrustFund())) {
                FlyStatDto flyStatDto = this.getFlyStatDto(accountWalletMap, PersonFlyPayStyle.TRUST_FUND, dto.getAccountId(), date);
                this.buildFlyStatDto(flyStatDto, dto.getOperationType(), dto.getTrustFund(),PersonFlyPayStyle.TRUST_FUND);
            }
            if (Utils.isPositive(dto.getTrustCash())) {
                FlyStatDto flyStatDto = this.getFlyStatDto(accountWalletMap, PersonFlyPayStyle.CASH_WITHHOLD, dto.getAccountId(), date);
                this.buildFlyStatDto(flyStatDto, dto.getOperationType(), dto.getTrustCash(),PersonFlyPayStyle.CASH_WITHHOLD);
            }
            if (Utils.isPositive(dto.getTrustIncentive())) {
                FlyStatDto flyStatDto = this.getFlyStatDto(accountWalletMap, PersonFlyPayStyle.INCENTIVE_WITHHOLD, dto.getAccountId(), date);
                this.buildFlyStatDto(flyStatDto, dto.getOperationType(), dto.getTrustIncentive(),PersonFlyPayStyle.INCENTIVE_WITHHOLD);
            }
            if (Utils.isPositive(dto.getTrustFlyCoin())) {
                FlyStatDto flyStatDto = this.getFlyStatDto(accountWalletMap, PersonFlyPayStyle.FLY_COIN_WITHHOLD, dto.getAccountId(), date);
                this.buildFlyStatDto(flyStatDto, dto.getOperationType(), dto.getTrustFlyCoin(),PersonFlyPayStyle.FLY_COIN_WITHHOLD);
            }
            if (Utils.isPositive(dto.getWithholdFund())) {
                FlyStatDto flyStatDto = this.getFlyStatDto(accountWalletMap, PersonFlyPayStyle.WITH_HOLD, dto.getAccountId(), date);
                this.buildFlyStatDto(flyStatDto, dto.getOperationType(), dto.getWithholdFund(),PersonFlyPayStyle.WITH_HOLD);
            }
            if (Utils.isPositive(dto.getRedPacket())) {
                FlyStatDto flyStatDto = this.getFlyStatDto(accountWalletMap, PersonFlyPayStyle.RED_PACKAGE_FUND, dto.getAccountId(), date);
                this.buildFlyStatDto(flyStatDto, dto.getOperationType(), dto.getRedPacket(),PersonFlyPayStyle.RED_PACKAGE_FUND);
            }
        }
        stats.addAll(accountWalletMap.values());

        if (CollectionUtils.isEmpty(stats)) {
            log.info("FlyReportService flushWithholdMoney empty date is {}", date);
            return;
        }
        //stat 扩展
        List<Integer> accountIdList = stats.stream().map(FlyStatDto::getAccountId).distinct().collect(Collectors.toList());
        Map<String, AdStatAccountDayPo> flySupportBoostMap = this.getFlySupportBoostMap(accountIdList, date);

        this.enhanceWithhold(stats, flySupportBoostMap, FlyConfig.WITHHOLD_SOURCE);

        List<FlyExtendEsPo> flyExtendEsPos = statDto2Po(stats);
        flyExtendEsPos = flyExtendEsPos.stream().filter(this::isAllZero).collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(flyExtendEsPos)) {
            flyExtendRepository.saveAll(flyExtendEsPos);
        }
        crmCacheManager.setValueWithTime("FlyExtendJob_batchFlushWithholdMoney_Flag" + CrmUtils.formatDate(date, CrmUtils.YYYYMMDD), "1", TimeUnit.DAYS, 1);
        log.info("FlyReportService flushWithholdMoney finished date is {}, size is {}", date, flyExtendEsPos.size());
        List<FlyExtendEsNewPo> newContent = convertToNewPo(flyExtendEsPos);
        if (!CollectionUtils.isEmpty(newContent)) {
            messageSender.sendBatch(newContent, FlyConfig.FLY_INDEX, EsMigrateCommon.INCR);
        }
    }

    private List<FlyExtendEsNewPo> convertToNewPo(List<FlyExtendEsPo> content) {
        return content.stream()
                .map(this::convertSinglePo)
                .collect(Collectors.toList());
    }

    private FlyExtendEsNewPo convertSinglePo(FlyExtendEsPo po) {
        FlyExtendEsNewPo newPo = new FlyExtendEsNewPo();
        BeanUtils.copyProperties(po, newPo);
        return newPo;
    }

    public boolean isAllZero(FlyExtendEsPo po) {
        if (Utils.isPositive(po.getRechargeAmount())) {
            return true;
        }
        if (Utils.isPositive(po.getRefundAmount())) {
            return true;
        }
        if (Utils.isPositive(po.getTotalConsume())) {
            return true;
        }
        return false;
    }

    /**
     * 定时任务同步数据到es
     */

    @Override
    public void writeFlyOrder2Es(Timestamp begin, Timestamp end) {
        List<Timestamp> eachDays = Utils.getEachDays(begin, end);
        eachDays.forEach(this::writeFlyOrderByDateToEsV2);
    }

    public void writeFlyOrderByDateToEsV2(Timestamp date) {
        Assert.isTrue(date.after(new Timestamp(1717171199000L)),"20240601之前不准重刷有问题找产品 拾玖 谨言 研发 阿钢 朝朝 辰星");
        Timestamp beginOfDay = Utils.getBeginOfDay(date);
        Timestamp endOfDay = Utils.getEndOfDay(date);
        List<FlyStatDto> flyStats = queryNewPersonaFlyOrders(beginOfDay, endOfDay);
        this.enhancePersonaFly(flyStats, FlyConfig.PERSONA_SOURCE);
        if (CollectionUtils.isEmpty(flyStats)) {
            log.info("writeFlyOrderByDateToEsV2 empty begin {} ,end {}", beginOfDay, endOfDay);
            if (Objects.equals(env, "prd") && isAfterSixHalf()) {
                // 机器人发告警消息
                WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                        .msgtype(MsgType.TEXT.getType())
                        .text(new Text("个人起飞查询订单时间在" + CrmUtils.formatDate(date, CrmUtils.YYYYMMDD) + "没有数据告警，任务执行时间:" + CrmUtils.formatDate(Utils.getNow())))
                        .build();
                wxRobotService.sendWeChatWork(wxRobotMsg, paladinConfig.getString("achievement.chat.robot"));
            }
            return;
        }
        List<FlyExtendEsPo> flyExtendPos = statDto2Po(flyStats);
        flyExtendRepository.saveAll(flyExtendPos);
        crmCacheManager.setValueWithTime("FlyExtendJob_writeFlyOrder2Es_Flag" + CrmUtils.formatDate(date, CrmUtils.YYYYMMDD), "1", TimeUnit.DAYS, 1);
        log.info("writeFlyOrderByDateToEsV2 finished begin {} ,end {} ,newPersonaFlyStats size {}", date, date, flyExtendPos.size());
        List<FlyExtendEsNewPo> newContent = convertToNewPo(flyExtendPos);
        messageSender.sendBatch(newContent, FlyConfig.FLY_INDEX, EsMigrateCommon.INCR);
    }

    public boolean isAfterSixHalf(){
        LocalDateTime now = LocalDateTime.now();
        LocalDateTime today = now.toLocalDate().atStartOfDay();
        // 创建今天的6点半的时间
        LocalDateTime sixThirtyToday = today.plusHours(5).plusMinutes(0);
        // 判断当前时间是否大于今天的6点半
        if (now.isAfter(sixThirtyToday)) {
            return true;
        }
        return false;
    }

    private List<FlyExtendEsPo> statDto2Po(List<FlyStatDto> flyStats) {
        return flyStats.stream().map(stat -> {
            FlyExtendEsPo po = FlyExtendEsPo.builder().build();
            BeanUtils.copyProperties(stat, po);
            if (FlyConfig.WITHHOLD_SOURCE.equals(stat.getSource())) {
                po.setId(stat.getSource() + "_" + stat.getPayStyle() + "_" + stat.getAccountId() + "_" + Utils.getTimestamp2String(po.getDate()));
            } else {
                po.setId(stat.getSource() + "_" + stat.getOrderId() + "_" + stat.getPayStyle() + "_" + stat.getAccountId() + "_" + Utils.getTimestamp2String(po.getDate()));
            }
            return po;
        }).collect(Collectors.toList());
    }

    public List<FlyStatDto> queryNewPersonaFlyOrders(Timestamp begin, Timestamp end) {

        int page = 1;
        List<FlyStatDto> flyOrders = new ArrayList<>();
        while (true) {
            StringBuilder urlBuilder = new StringBuilder(newFlyCallUrl)
                    .append("startTime=")
                    .append(begin)
                    .append("&endTime=")
                    .append(end)
                    .append("&size=200")
                    .append("&page=")
                    .append(page);

            Request req = new Request.Builder()
                    .url(urlBuilder.toString())
                    .build();
            TypeToken<OaNewCommonResponse<NewFlyUpOrderDataDto>> type = new TypeToken<OaNewCommonResponse<NewFlyUpOrderDataDto>>() {
            };

            OaNewCommonResponse<NewFlyUpOrderDataDto> data = OkHttpUtils.callForObject(req, type);

            if (data == null || data.getData() == null || CollectionUtils.isEmpty(data.getData().getList())) {
                break;
            }

            List<FlyStatDto> tmpFlyStatDtos = data.getData().getList().stream().map(dto -> {
                if (Objects.isNull(dto.getGroup_time()) || Objects.isNull(dto.getLaunch_start_time()) || Objects.isNull(dto.getLaunch_end_time()) || Objects.isNull(dto.getPayStyle())) {
                    log.info("queryNewPersonaFlyOrders_error_data:dto={}", JSON.toJSONString(data));
                }
                FlyStatDto flyStatDto = new FlyStatDto();
                flyStatDto.setDate(Utils.getBeginOfDay(new Timestamp(dto.getGroup_time())));
                flyStatDto.setOrderId(dto.getId());
                flyStatDto.setAccountId(dto.getAccountId());
                flyStatDto.setCampaignId(dto.getCampaignId());
                flyStatDto.setLaunchStartTime(new Timestamp(dto.getLaunch_start_time()));
                flyStatDto.setLaunchEndTime(new Timestamp(dto.getLaunch_end_time()));
                flyStatDto.setClickCount(dto.getClick_count());
                flyStatDto.setShowCount(dto.getShow_count());
                flyStatDto.setTotalConsume(dto.getTotalCost());
                flyStatDto.setFlySupportFactor(dto.getFlySupportFactor());
                flyStatDto.setFlySupportConsume(dto.getFlySupportConsume());
                flyStatDto.setRechargeAmount(dto.getTotalPrice());
                flyStatDto.setCashRechargeAmount(Utils.fromYuanToFen(dto.getPrice()));
                flyStatDto.setCouponRechargeAmount(dto.getFanhuoCouponAmount());
                flyStatDto.setRefundAmount(dto.getTotalRefundAmount());
                flyStatDto.setCashRefundAmount(Utils.fromYuanToFen(dto.getRefund_amount()));
                flyStatDto.setCouponRefundAmount(dto.getFanhuo_coupon_refund_amount());
                flyStatDto.setFanFollowCount(dto.getFans_growth());
                flyStatDto.setExpectImprove(dto.getExpectImprove());
                flyStatDto.setPayStyle(PersonFlyPayStyle.getByCode(dto.getPayStyle()).getCode());
                flyStatDto.setTotalCashCost(Utils.fromYuanToFen(dto.getOrder_cost()));
                flyStatDto.setCostAfterSupport(dto.getCostAfterSupport());
                flyStatDto.setCouponCost(dto.getCouponCost());
                flyStatDto.setAdPickUpAccountId(dto.getAdAccountId());
                flyStatDto.setAgentPickUpAccountId(dto.getProxyAccountId());
                return flyStatDto;
            }).collect(Collectors.toList());

            flyOrders.addAll(tmpFlyStatDtos);
            page++;
        }

        log.info("queryNewPersonaFlyOrders size {}", flyOrders.size());
        return flyOrders;
    }

    private void enhancePersonaFly(List<FlyStatDto> flyDtos, String source) {
        if (CollectionUtils.isEmpty(flyDtos)) {
            return;
        }
        //账号
        List<Integer> accountIds = flyDtos.stream().map(FlyStatDto::getAccountId).distinct().collect(Collectors.toList());
        List<AccountBaseDto> accountBaseDtoList = queryAccountService.getAccountBaseDtosInIdsWithOptimize(accountIds);

        //部门
        Map<Integer, Integer> accountDepMap = accountBaseDtoList.stream()
                .collect(Collectors.toMap(AccountBaseDto::getAccountId, AccountBaseDto::getDepartmentId));

        flyDtos.forEach(dto -> {
            dto.setDepartmentId(accountDepMap.getOrDefault(dto.getAccountId(), 0));
            dto.setTotalConsume(dto.getTotalConsume());
            dto.setClickCount(dto.getClickCount());
            dto.setShowCount(dto.getShowCount());
            dto.setClickRate(getClickRate(dto.getClickCount(), dto.getShowCount())); // 点击量 / 曝光量*100
            dto.setCostPerClick(getCostPerClick(dto.getTotalConsume() * 1000, dto.getClickCount())); //消耗（元）/ 点击量（次），单位：元
            dto.setAverageCostPerThousand(getAverageCostPerThousand(dto.getTotalConsume() * 1000, dto.getShowCount())); //消耗（元）/  曝光量*1000，单位：元
            dto.setFanFollowCount(dto.getFanFollowCount());
            dto.setSource(source);
        });
    }

    public void enhanceWithhold(List<FlyStatDto> flyDtos, Map<String, AdStatAccountDayPo> flySupportBoostMap, String source) {
        //账号
        List<Integer> accountIds = flyDtos.stream().map(FlyStatDto::getAccountId).distinct().collect(Collectors.toList());
        List<AccountBaseDto> accountBaseDtoList = queryAccountService.getAccountBaseDtosInIdsWithOptimize(accountIds);
        //部门
        Map<Integer, Integer> accountDepMap = accountBaseDtoList.stream()
                .collect(Collectors.toMap(AccountBaseDto::getAccountId, AccountBaseDto::getDepartmentId));

        // 计算同时存在充值额度新托管 和 赠款新托管的的账号
        Map<Integer, Boolean> accountsWithBothWithholdAndRedPackageFund = getAccountsWithBothWithholdAndRedPackageFund(flyDtos);

        for (FlyStatDto dto : flyDtos) {
            dto.setDepartmentId(accountDepMap.getOrDefault(dto.getAccountId(), 0));
            dto.setSource(source);

            // 计算boost（起飞扶持消耗）金额
            Long flySupportConsume = this.calcFlySupportConsume(dto, flySupportBoostMap);

            dto.setTotalCashCost(dto.getTotalConsume());// 扶持前

            if (flySupportConsume > 0
                    && accountsWithBothWithholdAndRedPackageFund.getOrDefault(dto.getAccountId(), false)
                    && PersonFlyPayStyle.RED_PACKAGE_FUND == PersonFlyPayStyle.getByCode(dto.getPayStyle())) {
                // 有扶持金额，同时存在赠款新托管、充值额度新托管，且自身是赠款新托管的，不计算扶持金额
                continue;
            }

            dto.setFlySupportConsume(flySupportConsume);// 扶持
            dto.setCostAfterSupport(flySupportConsume);// 扶持

            // 总消耗
            if (Utils.isPositive(dto.getTotalConsume())) {
                Long flySupportConsume2 = new BigDecimal(flySupportConsume).divide(BigDecimal.valueOf(1000), 0, RoundingMode.HALF_UP).longValue();
                dto.setTotalConsume(dto.getTotalConsume() + flySupportConsume2);
            } else {
                //还是保持原来的逻辑吧
                dto.setTotalConsume(dto.getTotalConsume());
            }
        }

    }

    public Map<Integer, Boolean> getAccountsWithBothWithholdAndRedPackageFund(List<FlyStatDto> flyDtos) {
        // 账号是否存在充值额度新托管
        Map<Integer, Boolean> accountHasWithHold = new HashMap<>();
        // 账号是否存在赠款新托管
        Map<Integer, Boolean> accountHasRedPackageFund = new HashMap<>();

        for (FlyStatDto dto : flyDtos) {
            PersonFlyPayStyle style = PersonFlyPayStyle.getByCode(dto.getPayStyle());
            Integer accountId = dto.getAccountId();

            if (style != null) {
                if (style == PersonFlyPayStyle.WITH_HOLD) {
                    accountHasWithHold.put(accountId, true);
                } else if (style == PersonFlyPayStyle.RED_PACKAGE_FUND) {
                    accountHasRedPackageFund.put(accountId, true);
                }
            }
        }

        // 计算同时存在充值额度新托管 和 赠款新托管的的账号
        Map<Integer, Boolean> accountsWithBothWithHoldAndRedPackageFund = new HashMap<>();

        for (Integer accountId : accountHasWithHold.keySet()) {
            if (accountHasRedPackageFund.containsKey(accountId)) {
                accountsWithBothWithHoldAndRedPackageFund.put(accountId, true);
            }
        }

        return accountsWithBothWithHoldAndRedPackageFund;
    }

    public Long calcFlySupportConsume(FlyStatDto dto, Map<String, AdStatAccountDayPo> flySupportBoostMap) {
        // 将支付类型转换成扣款标志
        final Integer FLY_COIN = DeductionSign.PERSON_FLY_TRUST_FLYV2_FLY_COIN.getCode();
        final Integer CHARGED = DeductionSign.PERSON_FLY_TRUST_FLYV2_CASH.getCode();
        PersonFlyPayStyle payStyle = PersonFlyPayStyle.getByCode(dto.getPayStyle());

        // 根据 <账号-扣款标志> 获取boost
        // key
        String supportBoostKey = dto.getAccountId() + "-" + (payStyle == PersonFlyPayStyle.TRUST_FUND ? FLY_COIN : CHARGED);
        Optional<AdStatAccountDayPo> boostPoOpt = Optional.ofNullable(flySupportBoostMap.getOrDefault(supportBoostKey, null));
        return boostPoOpt
                .map(AdStatAccountDayPo::getChargedBoostMilli) // 如果 boostPoOpt 为空，则不会执行 map 方法体内的操作
                .orElse(0L);
    }


    private FlyStatDto sumCreativeStat(List<StatDto> statDtoList, FlyStatDto stat) {
        Integer fans = statDtoList.stream().mapToInt(StatDto::getFanFollowCount).sum();
        Integer show = statDtoList.stream().mapToInt(StatDto::getShowAccount).sum();
        Integer click = statDtoList.stream().mapToInt(StatDto::getClickCount).sum();
        Long cost = stat.getTotalConsume();

        BigDecimal clickRate = getClickRate(click, show);// 点击量 / 曝光量*100
        BigDecimal costPerClick = getCostPerClick(cost * 1000, click);//消耗（元）/ 点击量（次），单位：元
        BigDecimal averageCostPerThousand = getAverageCostPerThousand(cost * 1000, show); //消耗（元）/  曝光量*1000，单位：元

        return FlyStatDto.builder()
                .totalConsume(cost)
                .fanFollowCount(fans)
                .showCount(show)
                .clickCount(click)
                .clickRate(clickRate)
                .costPerClick(costPerClick)
                .averageCostPerThousand(averageCostPerThousand)
                .build();
    }


    public BigDecimal getClickRate(Integer clickCount, Integer showCount) {
        if (0 == showCount) {
            return BigDecimal.ZERO;
        }
        return new BigDecimal(clickCount).divide(new BigDecimal(showCount), 4, BigDecimal.ROUND_HALF_UP).multiply(HUNDRED);
    }

    public BigDecimal getCostPerClick(Long cost, Integer clickCount) {
        if (0 == clickCount) {
            return BigDecimal.ZERO;
        }
        return this.getCost(cost).divide(new BigDecimal(clickCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    public BigDecimal getAverageCostPerThousand(Long cost, Integer showCount) {
        if (0 == showCount) {
            return BigDecimal.ZERO;
        }
        return this.getCost(cost).multiply(THOUSAND).divide(new BigDecimal(showCount), 2, BigDecimal.ROUND_HALF_UP);
    }

    /**
     * 沿用之前，表示confused
     *
     * @param cost
     * @return
     */
    public BigDecimal getCost(Long cost) {
        return new BigDecimal(cost).divide(MILLIS, 2, BigDecimal.ROUND_HALF_UP);
    }



    private FlyStatDto getFlyStatDto(Map<String, FlyStatDto> map, PersonFlyPayStyle style, Integer accountId, Timestamp date) {
        //KEY
        String key = Utils.date2String(date) + "_" + accountId + "_" + style.name();

        FlyStatDto statDto;
        if (!map.containsKey(key)) {
            statDto = FlyStatDto.builder()
                    .date(Utils.getBeginOfDay(date))
                    .accountId(accountId)
                    .payStyle(style.getCode())
                    .source(FlyConfig.WITHHOLD_SOURCE)
                    .build();
            map.put(key, statDto);
        } else {
            statDto = map.get(key);
        }
        return statDto;
    }

    private void buildFlyStatDto(FlyStatDto flyStatDto, Integer operateType, Long amount, PersonFlyPayStyle flyPayStyle) {
        if (WalletOperationType.CONSUME.getType().equals(operateType)) {
            flyStatDto.setTotalConsume(amount);
        }
        if (Lists.newArrayList(WalletOperationType.CHARGE.getType(),
                WalletOperationType.TRUST_FUND_RECHARGE.getType(),
                WalletOperationType.WITH_HOLD_FUND_RECHARGE.getType(),
                WalletOperationType.BACK_RED_PACKET.getType()).contains(operateType)) {
            if(Lists.newArrayList(PersonFlyPayStyle.WITH_HOLD,PersonFlyPayStyle.TRUST_FUND).contains(flyPayStyle)){
                flyStatDto.setRechargeAmount(0L);
            } else {
                flyStatDto.setRechargeAmount(amount);
            }
        }
        if (WalletOperationType.REFUND.getType().equals(operateType)) {
            flyStatDto.setRefundAmount(amount);
        }
    }

    @Deprecated
    public Map<Integer, PersonalUpUnitSupportFactorPo> getFlySupportConsumeFactorList(List<Integer> accountIdList, Timestamp date) {
        if (CollectionUtils.isEmpty(accountIdList) || date == null) {
            return Collections.emptyMap();
        }
        PersonalUpUnitSupportFactorPoExample example = new PersonalUpUnitSupportFactorPoExample();
        example.createCriteria().andAccountIdIn(accountIdList)
                .andLogDateEqualTo(CrmUtils.formatDate(date, CrmUtils.YYYYMMDD_WITHOUT_SPLIT))
                .andDeductionSignIn(FLY_TRUST_DEDUCTION_SIGN);
        List<PersonalUpUnitSupportFactorPo> poList = personalUpUnitSupportFactorDao.selectByExample(example);
        return poList.stream().collect(Collectors.toMap(PersonalUpUnitSupportFactorPo::getAccountId, Function.identity(), (oldValue, newValue) -> oldValue));
    }

    public Map<String, AdStatAccountDayPo> getFlySupportBoostMap(List<Integer> accountIdList, Timestamp date) {
        if (CollectionUtils.isEmpty(accountIdList) || Objects.isNull(date)) {
            return Collections.emptyMap();
        }

        // 根据账号ID、日期、扣款标识获取boost信息
        AdStatAccountDayPoExample example = new AdStatAccountDayPoExample();
        example.createCriteria()
                .andAccountIdIn(accountIdList)
                .andGroupTimeEqualTo(date)
                .andDeductionSignIn(Lists.newArrayList(
                        DeductionSign.PERSON_FLY_TRUST_FLYV2_CASH.getCode(),
                        DeductionSign.PERSON_FLY_TRUST_FLYV2_FLY_COIN.getCode()));
        List<AdStatAccountDayPo> poList = adStatAccountDayDao.selectByExample(example);

        return poList.stream()
                .collect(Collectors.toMap(
                        po -> po.getAccountId() + "-" + po.getDeductionSign(), // Key: accountId + deductionSign
                        Function.identity(), // Value: AdStatAccountDayPo
                        (existing, replacement) -> existing // 处理键冲突的情况，这里我们保留第一个元素
                ));
    }

}
