package com.bilibili.crm.platform.biz.config;

import lombok.Data;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/8/31 15:57
 * crm通用文案配置
 */
@Component
@Data
public class CopywritingConfig {

    @Value("${crm.copywriting.export.email.file.name:CRM_EXPORT_FILE}")
    private String exportFileName;

    @Value("${crm.copywriting.export.email.title:[CRM邮件导出服务]}")
    private String exportEmailTitle;

    @Value("${crm.copywriting.export.email.content:附件为您的导出结果，请查收}")
    private String exportEmailContent;

    @Value("${crm.copywriting.export.email.response:导出结果会尽快发送至您的域账号邮箱，请稍后前往查看}")
    private String exportEmailResponse;

    @Value("${crm.customer.role.all:775}")
    private Integer customerRoleAll;

}
