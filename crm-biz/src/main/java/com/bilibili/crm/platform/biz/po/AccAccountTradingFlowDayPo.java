package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccAccountTradingFlowDayPo implements Serializable {
    /**
     * 自增ID
     */
    private Integer id;

    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 代理商id
     */
    private Integer agentId;

    /**
     * 是否是代理商 0否 1是
     */
    private Integer isAgent;

    /**
     * 现金充值（分）
     */
    private Long cashRecharge;

    /**
     * 返货充值（分）
     */
    private Long redPacketRecharge;

    /**
     * 专项返货充值（分）
     */
    private Long specialRedPacketRecharge;

    /**
     * 现金退款（分）
     */
    private Long cashRefund;

    /**
     * 返货退款（分）
     */
    private Long redPacketRefund;

    /**
     * 专项返货退款（分）
     */
    private Long specialRedPacketRefund;

    /**
     * 现金转入（分）
     */
    private Long cashTurnIn;

    /**
     * 返货转入（分）
     */
    private Long redPacketTurnIn;

    /**
     * 现金转出（分）
     */
    private Long cashTurnOut;

    /**
     * 返货转出（分）
     */
    private Long redPacketTurnOut;

    /**
     * 现金反作弊（分）
     */
    private Long cashCheat;

    /**
     * 返货反作弊（分）
     */
    private Long redPacketCheat;

    /**
     * 现金消耗（分）
     */
    private Long cashConsume;

    /**
     * 返货消耗（分）
     */
    private Long redPacketConsume;

    /**
     * 专项返货消耗（分）
     */
    private Long specialRedPacketConsume;

    /**
     * 现金余额（分）
     */
    private Long cashBalance;

    /**
     * 返货余额（分）
     */
    private Long redPacketBalance;

    /**
     * 专项返货余额（分）
     */
    private Long specialRedPacketBalance;

    /**
     * 交易日期
     */
    private Timestamp date;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 转户现金转入(分)
     */
    private Long cashTransferIn;

    /**
     * 转户现金转出(分)
     */
    private Long cashTransferOut;

    /**
     * 转户返货转入(分)
     */
    private Long redPacketTransferIn;

    /**
     * 转户返货转出(分)
     */
    private Long redPacketTransferOut;

    /**
     * 代理商与代理商现金转入(分)
     */
    private Long agentToAgentCashIn;

    /**
     * 代理商与代理商现金转出(分)
     */
    private Long agentToAgentCashOut;

    /**
     * 代理商与代理商返货转入(分)
     */
    private Long agentToAgentRedPacketIn;

    /**
     * 代理商与代理商返货转出(分)
     */
    private Long agentToAgentRedPacketOut;

    /**
     * 线索通预扣
     */
    private Long cluePassPreDeduct;

    /**
     * 线索通扣费
     */
    private Long cluePassDeduct;

    /**
     * 线索通退款
     */
    private Long cluePassRefund;

    /**
     * 优惠券锁定现金
     */
    private Long couponLockCash;

    /**
     * 优惠券锁定返货
     */
    private Long couponLockRedPacket;

    /**
     * 优惠券锁定专返
     */
    private Long couponLockSpecialRedPacket;

    /**
     * 优惠券解锁现金
     */
    private Long couponUnLockCash;

    /**
     * 优惠券解锁返货
     */
    private Long couponUnLockRedPacket;

    /**
     * 优惠券解锁专返
     */
    private Long couponUnLockSpecialRedPacket;

    /**
     * 长周期锁定现金
     */
    private Long longPeriodLockCash;

    /**
     * 长周期锁定现金
     */
    private Long longPeriodLockRedPacket;

    /**
     * 长周期锁定现金
     */
    private Long longPeriodLockSpecialRedPacket;

    /**
     * 长周期解锁现金
     */
    private Long longPeriodUnLockCash;

    /**
     * 长周期解锁现金
     */
    private Long longPeriodUnLockRedPacket;

    /**
     * 长周期解锁现金
     */
    private Long longPeriodUnLockSpecialRedPacket;

    /**
     * 授信消耗（分）
     */
    private Long creditConsume;

    /**
     * 授信退款（分）
     */
    private Long creditRefund;

    /**
     * 转户授信转入(分)
     */
    private Long creditTransferIn;

    /**
     * 转户授信转出(分)
     */
    private Long creditTransferOut;

    /**
     * 代理商与代理商授信转入(分)
     */
    private Long agentToAgentCreditIn;

    /**
     * 代理商与代理商授信转出(分)
     */
    private Long agentToAgentCreditOut;

    /**
     * 授信余额（分）
     */
    private Long creditBalance;

    /**
     * 授信充值（分）
     */
    private Long creditRecharge;

    private static final long serialVersionUID = 1L;
}