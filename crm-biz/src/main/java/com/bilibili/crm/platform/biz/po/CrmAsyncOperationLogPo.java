package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmAsyncOperationLogPo implements Serializable {
    /**
     * 自增id
     */
    private Long id;

    /**
     * 业务类型
     */
    private String bizType;

    /**
     * 操作人
     */
    private String operatorId;

    /**
     * 执行状态
     */
    private Integer status;

    /**
     * 执行结束时间
     */
    private Timestamp finishTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    /**
     * 业务标识
     */
    private String bizNo;

    /**
     * 处理数量
     */
    private Long processCount;

    /**
     * 错误码
     */
    private Integer errorCode;

    /**
     * 错误描述
     */
    private String errorMsg;

    private static final long serialVersionUID = 1L;
}