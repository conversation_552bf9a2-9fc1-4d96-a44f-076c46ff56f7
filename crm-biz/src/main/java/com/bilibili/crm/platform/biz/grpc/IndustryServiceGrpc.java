package com.bilibili.crm.platform.biz.grpc;

import com.bapis.ad.crm.industry.*;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.NewOldCategoryMappingDto;
import com.bilibili.crm.platform.api.account.dto.UnitedIndustryDto;
import com.bilibili.crm.platform.api.account.service.IBizIndustryService;
import com.bilibili.crm.platform.biz.industry.enums.IndustryMappingEnum;
import com.bilibili.crm.platform.biz.industry.service.UnitedIndustryService;
import com.bilibili.crm.platform.biz.service.NewOldCategoryMappingService;
import com.google.protobuf.Empty;
import io.grpc.stub.StreamObserver;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import pleiades.venus.starter.rpc.server.RPCService;

import javax.annotation.Resource;
import java.util.*;
import java.util.function.Predicate;
import java.util.function.Supplier;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/1/30
 */
@RPCService
public class IndustryServiceGrpc extends com.bapis.ad.crm.industry.IndustryServiceGrpc.IndustryServiceImplBase {

    @Resource
    private IBizIndustryService iBizIndustryService;

    @Resource
    private RpcTemplate rpcTemplate;

    @Resource
    private UnitedIndustryService unitedIndustryService;

    @Resource
    private NewOldCategoryMappingService newOldCategoryMappingService;

    public void queryIndustry(IndustryReq request, StreamObserver<IndustryResp> responseObserver) {

        rpcTemplate.execute(() -> request, Objects::nonNull, () -> handle(request.getLevel(), request.getName()),
                this::buildFailResp, responseObserver, "queryIndustry");
    }

    public void queryAllParentIndustry(Empty request, StreamObserver<QueryAllParentIndustryResp> responseObserver) {
        rpcTemplate.execute(() -> request, null,
                () -> build(0, "success", buildAllParentIndustry()),
                () -> build(-1, "failed", Collections.emptyList()),
                responseObserver, "queryAllParentIndustry");
    }

    public void queryCommerceIndustryByUnitedId(com.bapis.ad.crm.industry.QueryCommerceIndustryByUnitedIdReq request, StreamObserver<QueryCommerceIndustryByUnitedIdResp> responseObserver) {
        Predicate<QueryCommerceIndustryByUnitedIdReq> predicate = (req) -> req.getId() > 0;
        Supplier<CommerceByUnitedMapping> supplier = () -> {
            NewOldCategoryMappingDto mapping = newOldCategoryMappingService.getOldCategoryByNew(request.getId(), IndustryMappingEnum.UNITED_COMMERCE_MAPPING.getDesc());
            return convert(mapping);
        };
        rpcTemplate.execute(() -> request, predicate,
                () -> buildCommerceIndustryByUnitedIdResp(0, "success", supplier.get()),
                () -> buildCommerceIndustryByUnitedIdResp(-1, "failed", CommerceByUnitedMapping.newBuilder().build()),
                responseObserver, "queryCommerceIndustryByUnitedId");
    }

    public void queryUnitedIndustry(IndustryReq request, StreamObserver<IndustryResp> responseObserver) {
        rpcTemplate.execute(() -> request, Objects::nonNull, () -> unitedIndustryHandle(request.getLevel(), request.getName()),
                this::buildFailResp, responseObserver, "queryUnitedIndustry");
    }

    public void queryParentUnitedIndustryById(QueryParentUnitedIndustryByIdReq request, StreamObserver<QueryParentUnitedIndustryByIdResp> responseObserver) {
        rpcTemplate.execute(() -> request, (req) -> req.getId() > 0, () -> queryParentUnitedIndustryById(request.getId()),
                this::buildFailParentUnitedResp, responseObserver, "queryParentUnitedIndustryById");
    }

    public void queryUnitedIndustryDetails(IndustryReq request, StreamObserver<CommonQueryIndustryResp> responseObserver) {
        rpcTemplate.execute(() -> request, Objects::nonNull, () -> unitedIndustryDetailHandle(request.getLevel(), request.getName()),
                this::buildFailCommonQueryIndustryResp, responseObserver, "queryUnitedIndustryDetails");
    }

    public void batchQueryUnitedIndustryByIds(BatchQueryUnitedIndustryByIdsReq request, StreamObserver<IndustryResp> responseObserver) {
        rpcTemplate.execute(() -> request, Objects::nonNull, () -> batchQueryByIds(request.getIdsList()),
                this::buildFailResp, responseObserver, "batchQueryUnitedIndustryByIds");
    }

    private IndustryResp batchQueryByIds(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            return buildResp(0, "success", IndustryInfoAgg.newBuilder().build());
        }
        Map<Integer, UnitedIndustryDto> map = unitedIndustryService.batchQueryIndustryByIds(ids);
        List<IndustryInfo> industryInfos = new ArrayList<>();
        map.values().forEach(categoryDto -> industryInfos.add(convertUnitedIndustry(categoryDto)));
        IndustryInfoAgg.Builder builder = IndustryInfoAgg.newBuilder();
        builder.addAllIndustryInfos(industryInfos);
        return buildResp(0, "success", builder.build());
    }

    private CommonQueryIndustryResp unitedIndustryDetailHandle(Integer level, String name) {
        List<UnitedIndustryDto> categoryDTOs = unitedIndustryService.queryValidIndustryDtoByLevelName(level, name);
        if (CollectionUtils.isEmpty(categoryDTOs)) {
            return buildResp(0, "success", Collections.emptyList());
        }
        List<IndustryDetailInfo> industryDetailInfos = convertUnitedIndustryDetails(categoryDTOs);
        return buildResp(0, "success", industryDetailInfos);
    }

    private CommonQueryIndustryResp buildFailCommonQueryIndustryResp() {
        return buildResp(-1, "failed", Collections.emptyList());
    }

    private CommonQueryIndustryResp buildResp(int code, String message, List<IndustryDetailInfo> industryInfos) {
        return CommonQueryIndustryResp.newBuilder()
                .setCode(code)
                .setMessage(message)
                .addAllData(industryInfos)
                .build();
    }

    private List<IndustryDetailInfo> convertUnitedIndustryDetails(List<UnitedIndustryDto> unitedIndustryDto) {
        if (null == unitedIndustryDto) {
            return Collections.emptyList();
        }
        return unitedIndustryDto.stream().map(dto -> IndustryDetailInfo.newBuilder()
                .setId(dto.getId())
                .setName(dto.getName())
                .setLevel(dto.getLevel())
                .setPId(dto.getPId())
                .setStatus(dto.getStatus())
                .build()).collect(Collectors.toList());
    }


    private QueryParentUnitedIndustryByIdResp buildFailParentUnitedResp() {
        return QueryParentUnitedIndustryByIdResp.newBuilder()
                .setCode(-1)
                .setMessage("failed")
                .build();
    }

    private QueryParentUnitedIndustryByIdResp queryParentUnitedIndustryById(int id) {
        List<UnitedIndustryDto> unitedIndustryDtos = unitedIndustryService.queryParentIndustryById(id);
        if (CollectionUtils.isEmpty(unitedIndustryDtos)) {
            return QueryParentUnitedIndustryByIdResp.newBuilder()
                    .setCode(0)
                    .setMessage("success")
                    .build();
        }
        List<IndustryDetailInfo> infos = new ArrayList<>();
        unitedIndustryDtos.forEach(dto -> {
            IndustryDetailInfo.Builder builder = IndustryDetailInfo.newBuilder();
            builder.setId(dto.getId());
            builder.setLevel(dto.getLevel());
            builder.setName(dto.getName());
            infos.add(builder.build());
        });
        QueryParentUnitedIndustryByIdResp.Builder builder = QueryParentUnitedIndustryByIdResp.newBuilder();
        ParentUnitedIndustry.Builder parentBuilder = ParentUnitedIndustry.newBuilder();
        parentBuilder.addAllParentIndustryInfos(infos);
        builder.setData(parentBuilder);
        builder.setCode(0);
        builder.setMessage("success");
        return builder.build();
    }


    private List<QueryAllParentIndustryInfo> buildAllParentIndustry() {
        return unitedIndustryService.queryAllParentIndustry().stream().map(dto -> {
            QueryAllParentIndustryInfo.Builder builder = QueryAllParentIndustryInfo.newBuilder();
            builder.setId(dto.getId());
            builder.setName(dto.getName());
            builder.setParentId(dto.getPId());
            builder.setLevel(dto.getLevel());
            builder.addAllChildren(dto.getChildren().stream().map(this::convert).collect(Collectors.toList()));
            return builder.build();
        }).collect(Collectors.toList());
    }

    private QueryCommerceIndustryByUnitedIdResp buildCommerceIndustryByUnitedIdResp(int code, String message, CommerceByUnitedMapping mapping) {
        QueryCommerceIndustryByUnitedIdResp.Builder builder = QueryCommerceIndustryByUnitedIdResp.newBuilder();
        builder.setCode(code);
        builder.setMessage(message);
        builder.setData(mapping);
        return builder.build();
    }

    private QueryAllParentIndustryResp build(int code, String message, List<QueryAllParentIndustryInfo> industryInfos) {
        QueryAllParentIndustryResp.Builder builder = QueryAllParentIndustryResp.newBuilder();
        builder.setCode(code);
        builder.setMessage(message);
        builder.addAllData(industryInfos);
        return builder.build();
    }


    private IndustryDetailInfo convert(UnitedIndustryDto dto) {
        if (null == dto) {
            return IndustryDetailInfo.newBuilder().build();
        }
        IndustryDetailInfo.Builder builder = IndustryDetailInfo.newBuilder();
        builder.setId(dto.getId());
        builder.setName(dto.getName());
        builder.setPId(dto.getPId());
        builder.setLevel(dto.getLevel());
        builder.setPrompt(dto.getPrompt());
        builder.setStatus(dto.getStatus());
        builder.setRemark(dto.getRemark());
        return builder.build();
    }

    private CommerceByUnitedMapping convert(NewOldCategoryMappingDto dto) {
        if (null == dto) {
            return CommerceByUnitedMapping.newBuilder().build();
        }
        CommerceByUnitedMapping.Builder builder = CommerceByUnitedMapping.newBuilder();
        builder.setFirstId(dto.getOldCategoryFirstId());
        builder.setSecondId(dto.getOldCategorySecondId());
        return builder.build();
    }


    private IndustryResp buildFailResp() {
        return buildResp(-1, "failed", IndustryInfoAgg.newBuilder().build());
    }

    private IndustryResp handle(Integer level, String name) {
        List<CategoryDto> categoryDTOs = iBizIndustryService.getValidCategoryDtosByLevelName(level, name);
        if (CollectionUtils.isEmpty(categoryDTOs)) {
            return buildResp(0, "success", IndustryInfoAgg.newBuilder().build());
        }
        List<IndustryInfo> industryInfos = new ArrayList<>();
        categoryDTOs.forEach(categoryDto -> industryInfos.add(convert(categoryDto)));
        IndustryInfoAgg.Builder builder = IndustryInfoAgg.newBuilder();
        builder.addAllIndustryInfos(industryInfos);
        return buildResp(0, "success", builder.build());
    }

    private IndustryResp unitedIndustryHandle(Integer level, String name) {
        List<UnitedIndustryDto> categoryDTOs = unitedIndustryService.queryValidIndustryDtoByLevelName(level, name);
        if (CollectionUtils.isEmpty(categoryDTOs)) {
            return buildResp(0, "success", IndustryInfoAgg.newBuilder().build());
        }
        List<IndustryInfo> industryInfos = new ArrayList<>();
        categoryDTOs.forEach(categoryDto -> industryInfos.add(convertUnitedIndustry(categoryDto)));
        IndustryInfoAgg.Builder builder = IndustryInfoAgg.newBuilder();
        builder.addAllIndustryInfos(industryInfos);
        return buildResp(0, "success", builder.build());
    }

    private IndustryResp buildResp(int code, String message, IndustryInfoAgg industryInfoAgg) {
        return IndustryResp.newBuilder()
                .setCode(code)
                .setMessage(message)
                .setData(industryInfoAgg)
                .build();
    }

    private IndustryInfo convert(CategoryDto categoryDto) {
        if (null == categoryDto) {
            return IndustryInfo.newBuilder().build();
        }
        return IndustryInfo.newBuilder()
                .setId(categoryDto.getId())
                .setName(categoryDto.getName())
                .build();
    }

    private IndustryInfo convertUnitedIndustry(UnitedIndustryDto unitedIndustryDto) {
        if (null == unitedIndustryDto) {
            return IndustryInfo.newBuilder().build();
        }
        return IndustryInfo.newBuilder()
                .setId(unitedIndustryDto.getId())
                .setName(unitedIndustryDto.getName())
                .build();
    }
}
