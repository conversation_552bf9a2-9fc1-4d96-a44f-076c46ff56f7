package com.bilibili.crm.platform.biz.service.account.label;

import com.alibaba.excel.EasyExcelFactory;
import com.alibaba.excel.context.AnalysisContext;
import com.alibaba.excel.event.AnalysisEventListener;
import com.alibaba.excel.metadata.Sheet;
import com.bilibili.crm.platform.api.account.dto.accountlabel.AccountLabelExcelItemDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentUploadDto;
import com.bilibili.crm.platform.biz.service.return_online.utils.EasyExcelUtils;
import com.bilibili.crm.platform.biz.util.OssSignService;
import com.bilibili.crm.platform.common.account.label.AccountLabelBatchOptType;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.mockito.Mock;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.io.FileNotFoundException;
import java.io.IOException;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.MalformedURLException;
import java.net.ProtocolException;
import java.net.URL;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/8/1 下午4:13
 */
@Component
@Slf4j
public class AccountLabelExcelParser {

    @Autowired
    private OssSignService ossSignService;

    public List<AccountLabelExcelItemDto> parseExcel(CommonAttachmentUploadDto commonAttachmentUploadDto) {
        Assert.notNull(commonAttachmentUploadDto, "UploadDto 必传！");

        List<AccountLabelExcelItemDto> excelItemDtos = new ArrayList<>();
        // 解析 sheet1
        List<AccountLabelExcelItemDto> sheet1Items = parseExcel(commonAttachmentUploadDto, 1);
        // 解析 sheet2
        List<AccountLabelExcelItemDto> sheet2Items = parseExcel(commonAttachmentUploadDto, 2);
        excelItemDtos.addAll(CollectionUtils.isEmpty(sheet1Items) ? Collections.EMPTY_LIST: sheet1Items);
        excelItemDtos.addAll(CollectionUtils.isEmpty(sheet2Items) ? Collections.EMPTY_LIST: sheet2Items);
        Cat.logEvent("parseExcel", "parseExcel", Event.SUCCESS, String.format("fileName=%s&url=%s&excelItemDtos" +
                        ".size=%s", commonAttachmentUploadDto.getUrl(), commonAttachmentUploadDto.getFileName(),
                excelItemDtos.size()));
        return excelItemDtos;
    }

    /**
     * 解析 excel
     *
     * @param commonAttachmentUploadDto
     * @return
     */
    public List<AccountLabelExcelItemDto> parseExcel(CommonAttachmentUploadDto commonAttachmentUploadDto,
                                                     Integer sheetNo) {
        Assert.isTrue(StringUtils.isNotEmpty(commonAttachmentUploadDto.getOssKey()), "oss key 不能为空");
        Assert.isTrue(StringUtils.isNotEmpty(commonAttachmentUploadDto.getFileName()), "file name 不能为空");
        String playUrl = ossSignService.getPlayUrl(commonAttachmentUploadDto.getOssKey());
        commonAttachmentUploadDto.setUrl(playUrl);

        List<AccountLabelExcelItemDto> returnCorrectDataDtos = new ArrayList();
        Integer optType = AccountLabelBatchOptType.DELETE.getCode();
        if (sheetNo.equals(1)) {
            optType = AccountLabelBatchOptType.ADD.getCode();
        }
        InputStream inputStream = null;
        try {
            log.info("=====> parseExcel start...sheet:{}, fileName:{}, ossKey:{}, url:{}", sheetNo,
                    commonAttachmentUploadDto.getFileName(), commonAttachmentUploadDto.getOssKey(), commonAttachmentUploadDto.getUrl());
            HttpURLConnection httpConn = null;
            URL urlObj = new URL(commonAttachmentUploadDto.getUrl());
            // 创建HttpURLConnection对象，通过这个对象打开跟远程服务器之间的连接
            httpConn = (HttpURLConnection) urlObj.openConnection();
            httpConn.setDoInput(true);
            httpConn.setRequestMethod("GET");
            httpConn.setConnectTimeout(5000);
            httpConn.connect();
            inputStream = httpConn.getInputStream();
            // 新增
            Integer finalOptType = optType;
            EasyExcelFactory.readBySax(inputStream, new Sheet(sheetNo, 1), new AnalysisEventListener() {

                @Override
                public void invoke(Object object, AnalysisContext analysisContext) {
                    log.info("=====> parseExcel sheet:{}, 当前行：{} 对应的对象信息为：{}", sheetNo,
                            analysisContext.getCurrentRowNum(), object);
                    ArrayList columns = (ArrayList) object;

                    if (CollectionUtils.isEmpty(columns) || columns.stream().allMatch(t -> t == null)) {
                        return;
                    }

                    AccountLabelExcelItemDto excelItemDto = new AccountLabelExcelItemDto();
                    if (columns.size() > 0) {
                        excelItemDto.setAccountIdsStr(EasyExcelUtils.getStringValue(columns, 0));
                    }
                    if (columns.size() > 1) {
                        excelItemDto.setLabelIdsStr(EasyExcelUtils.getStringValue(columns, 1));
                    }
                    excelItemDto.setOperateType(finalOptType);
                    returnCorrectDataDtos.add(excelItemDto);
                }

                @Override
                public void doAfterAllAnalysed(AnalysisContext analysisContext) {
                    log.info("=====> parseExcel sheet:{}, 最后一批存储dataList的大小为：{}, sheetName:{}", sheetNo,
                            returnCorrectDataDtos.size(), analysisContext.getCurrentSheet().getSheetName());
                }
            });
            log.info("=====> parseExcel end parse excel...");
        } catch (FileNotFoundException e) {
            e.printStackTrace();
        } catch (ProtocolException e) {
            e.printStackTrace();
        } catch (MalformedURLException e) {
            e.printStackTrace();
        } catch (IOException e) {
            e.printStackTrace();
        } finally {
            try {
                inputStream.close();
            } catch (IOException e) {
                e.printStackTrace();
            }
        }
        return returnCorrectDataDtos;
    }
}
