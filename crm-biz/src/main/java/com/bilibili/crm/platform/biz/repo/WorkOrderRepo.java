package com.bilibili.crm.platform.biz.repo;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.customer.dto.CustomerInfoDto;
import com.bilibili.crm.platform.api.wo.dto.WorkOrderDto;
import com.bilibili.crm.platform.biz.common.WorkOrderMappingType;
import com.bilibili.crm.platform.biz.common.WorkOrderType;
import com.bilibili.crm.platform.biz.dao.CrmWorkOrderDao;
import com.bilibili.crm.platform.biz.dao.CrmWorkOrderDetailDao;
import com.bilibili.crm.platform.biz.po.CrmWorkOrderDetailPo;
import com.bilibili.crm.platform.biz.po.CrmWorkOrderDetailPoExample;
import com.bilibili.crm.platform.biz.po.CrmWorkOrderPo;
import com.bilibili.crm.platform.biz.po.CrmWorkOrderPoExample;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/11/2 3:59 下午
 */
@Slf4j
@Repository
public class WorkOrderRepo {

    @Autowired
    private CrmWorkOrderDao crmWorkOrderDao;
    @Autowired
    private CrmWorkOrderDetailDao crmWorkOrderDetailDao;

    /**
     * 获取所有没有部门id的客户的工单列表
     *
     * @return
     */
    public List<CrmWorkOrderPo> getCustomerWorkOrderListOfNoDepartmentId(Integer fromId, Integer toId) {
        if (!Utils.isPositive(fromId)) {
            throw new ServiceRuntimeException("from id is wrong!");
        }
        if (!Utils.isPositive(toId)) {
            throw new ServiceRuntimeException("to id is wrong!");
        }
        CrmWorkOrderPoExample example = new CrmWorkOrderPoExample();
        example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdGreaterThanOrEqualTo(fromId)
                .andIdLessThanOrEqualTo(toId)
                .andDepartmentIdEqualTo(0)
                .andTypeEqualTo(WorkOrderType.CUSTOMER.getCode());
        List<CrmWorkOrderPo> crmWorkOrderPos = crmWorkOrderDao.selectByExample(example);
        return crmWorkOrderPos;
    }

    public List<CrmWorkOrderDetailPo> getDetailsListByWorkOrderIds(List<Integer> workOrderIds) {
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return Collections.EMPTY_LIST;
        }
        CrmWorkOrderDetailPoExample example = new CrmWorkOrderDetailPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCrmWorkOrderIdIn(workOrderIds);
        List<CrmWorkOrderDetailPo> crmWorkOrderDetailPos = crmWorkOrderDetailDao.selectByExampleWithBLOBs(example);
        return crmWorkOrderDetailPos;
    }

    public Map<Integer, CrmWorkOrderDetailPo> getDetailsMapByWorkOrderIds(List<Integer> workOrderIds) {
        List<CrmWorkOrderDetailPo> detailsListByWorkOrderIds = this.getDetailsListByWorkOrderIds(workOrderIds);
        Map<Integer, CrmWorkOrderDetailPo> crmWorkOrderDetailPoMap = detailsListByWorkOrderIds.stream().collect(Collectors.toMap(t -> t.getCrmWorkOrderId(), t -> t, (a, b) -> b));
        return crmWorkOrderDetailPoMap;
    }

    public Integer updateDepartmentId(Integer workOrderId, Integer departmentId) {
        if (workOrderId == null) {
            throw new ServiceRuntimeException("work order id can't be null!");
        }
        CrmWorkOrderPo record = new CrmWorkOrderPo();
        record.setId(workOrderId);
        record.setDepartmentId(departmentId);
        record.setMtime(Utils.getNow());
        int count = crmWorkOrderDao.updateByPrimaryKeySelective(record);
        return count;
    }

    /**
     * 根据工单号id获取工单详情里的部门id
     * 根据customerId找到工单号，根据工单id找到详情
     *
     * @param customerId
     * @return
     */
    public Integer getWorkOrderDepartmentIdByCustomerId(Integer customerId) {
        if (customerId == null) {
            return 0;
        }
        // 根据 customerId 获取 workOrderId
        CrmWorkOrderPoExample workOrderPoExample = new CrmWorkOrderPoExample();
        workOrderPoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andMappingIdEqualTo(customerId);
        List<CrmWorkOrderPo> crmWorkOrderPos = crmWorkOrderDao.selectByExample(workOrderPoExample);
        if (CollectionUtils.isEmpty(crmWorkOrderPos)) {
            return 0;
        }
        Integer workOrderId = crmWorkOrderPos.get(0).getId();

        // 根据 workOrderId 获取 workOrderDetails
        CrmWorkOrderDetailPoExample workOrderDetailPoExample = new CrmWorkOrderDetailPoExample();
        workOrderDetailPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCrmWorkOrderIdEqualTo(workOrderId);
        List<CrmWorkOrderDetailPo> crmWorkOrderDetailPos = crmWorkOrderDetailDao.selectByExampleWithBLOBs(workOrderDetailPoExample);
        if (CollectionUtils.isEmpty(crmWorkOrderDetailPos)) {
            log.info("=====> getWorkOrderDepartmentIdByWorkOrderId[没有工单详情], customerId:{}, workOrderId:{}",
                    customerId, workOrderId);
            return 0;
        }

        // 从 workOrderDetails 中解析出 departmentId
        CrmWorkOrderDetailPo crmWorkOrderDetailPo = crmWorkOrderDetailPos.get(0);
        JSONObject detailJson = JSON.parseObject(crmWorkOrderDetailPo.getDetail());
        if (detailJson != null) {
            Integer departmentId = detailJson.getInteger("departmentId");
            if (departmentId == null) {
                log.info("=====> getWorkOrderDepartmentIdByWorkOrderId[detail json中没有部门id], customerId:{}, workOrderId:{}",
                        customerId, workOrderId);
                return 0;
            }
            return departmentId;
        }
        log.info("=====> getWorkOrderDepartmentIdByWorkOrderId[detail json有问题], customerId:{}, workOrderId:{}",
                customerId, workOrderId);
        return 0;
    }

    /**
     * 获取客户工单列表
     *
     * @param fromId
     * @param toId
     * @return
     */
    public List<CrmWorkOrderPo> getCustomerWorkOrderListByIdRange(Integer fromId, Integer toId) {
        CrmWorkOrderPoExample workOrderPoExample = new CrmWorkOrderPoExample();
        workOrderPoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andTypeEqualTo(WorkOrderType.CUSTOMER.getCode()).andIdGreaterThanOrEqualTo(fromId).andIdLessThan(toId);
        List<CrmWorkOrderPo> crmWorkOrderPos = crmWorkOrderDao.selectByExample(workOrderPoExample);
        return crmWorkOrderPos;
    }

    public WorkOrderDto getCustomerSnapshot(Integer workOrderId) {
        CrmWorkOrderPoExample example = new CrmWorkOrderPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andMappingIdEqualTo(workOrderId)
                .andMappingTypeEqualTo(WorkOrderMappingType.CUSTOMER_SNAPSHOT.getCode())
                .andTypeEqualTo(WorkOrderType.CUSTOMER_SNAPSHOT.getCode());

        List<CrmWorkOrderPo> crmWorkOrderPos = crmWorkOrderDao.selectByExample(example);
        List<Integer> workOrderIds = crmWorkOrderPos.stream().map(CrmWorkOrderPo::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(workOrderIds)) {
            return null;
        } else {
            WorkOrderDto workOrderDto = new WorkOrderDto();
            BeanUtils.copyProperties(crmWorkOrderPos.get(0), workOrderDto);
            List<CrmWorkOrderDetailPo> crmWorkOrderDetailPos = getDetailsListByWorkOrderIds(Lists.newArrayList(crmWorkOrderPos.get(0).getId()));
            if (!CollectionUtils.isEmpty(crmWorkOrderDetailPos)) {
                workOrderDto.setDetail(JSON.parseObject(crmWorkOrderDetailPos.get(0).getDetail(), CustomerInfoDto.class));
            }
            return workOrderDto;
        }
    }
}
