package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.api.customer.dto.CustomerAdditionQualNullExpireDateQueryDto;
import com.bilibili.crm.platform.biz.po.CustomerAdditionalQualificationInfoPo;
import com.bilibili.crm.platform.biz.po.CustomerAdditionalQualificationInfoPoExample;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/11/6 2:46 下午
 */
public interface ExtCustomerAdditionalQualificationInfoDao {

    /**
     * 获取范围内expireDate为null的 列表
     *
     * @param queryDto
     * @return
     */
    List<CustomerAdditionalQualificationInfoPo> selectNullExpireDateListByIdRange(CustomerAdditionQualNullExpireDateQueryDto  queryDto);
}
