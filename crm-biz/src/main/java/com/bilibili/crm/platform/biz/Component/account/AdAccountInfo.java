package com.bilibili.crm.platform.biz.Component.account;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * 广告账号信息
 *
 * <AUTHOR>
 * @date 2021/4/13 11:42 上午
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdAccountInfo implements Serializable {

    private static final long serialVersionUID = -2551693256295897007L;
    /**
     * 账号ID
     */
    private Integer accountId;

    /**
     * 用户名
     */
    private String username;

    /**
     * 手机号码
     */
    private String mobile;

    /**
     * 密码强度-已废弃
     */
    private Integer passwordStrength;

    /**
     * 盐-已废弃
     */
    private String salt;

    /**
     * 加盐密码-已废弃
     */
    private String saltPassword;

    /**
     * 状态:   -1-未开通 0-启用 1-冻结 2-余额不足
     */
    private Integer status;

    /**
     * crm customer主键-已废弃
     */
    private Integer crmCustomerId;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 账户订单类型(0预付款 1后付款 -1未开通)-已废弃
     */
    private Integer orderType;

    /**
     * 主站账号id
     */
    private Long mid;

    /**
     * 0客户（空绑定账号） 1主站账号
     */
    private Integer accountType;

    /**
     * 激活时间（默认为空）
     */
    private Timestamp activeTime;

    /**
     * 真实姓名（企业用户为公司名）
     */
    private String name;

    /**
     * icp备案号
     */
    private String icpRecordNumber;

    /**
     * icp截图url(废弃)
     */
    private String icpInfoImage;

    /**
     * 推广域名
     */
    private String brandDomain;

    /**
     * 用户属性 0个人用户 1机构用户 2个人起飞用户
     */
    private Integer userType;

    /**
     * 是否允许投放效果广告，广告系统状态（-1未激活 0允许 1禁止）
     */
    private Integer adStatus;

    /**
     * 版本号(mvcc)
     */
    private Integer version;

    /**
     * 行业一级分类id
     */
    private Integer categoryFirstId;

    /**
     * 行业二级分类id
     */
    private Integer categorySecondId;

    /**
     * 备注
     */
    private String remark;

    /**
     * 是否是代理商 0否 1是
     */
    private Integer isAgent;

    /**
     * 代理商类型：0-无 1-品牌代理商  2-MCN代理商  3-效果代理商
     */
    private Integer agentType;

    /**
     * 业务角色ID
     */
    private Integer businessRoleId;

    /**
     * 公司名称
     */
    private String companyName;

    /**
     * 区域id
     */
    private Integer areaId;

    /**
     * 所属代理商id
     */
    private Integer dependencyAgentId;

    /**
     * 网站名称
     */
    private String websiteName;

    /**
     * 微博账号
     */
    private String weibo;

    /**
     * 内部联系人（内网账号）
     */
    private String internalLinkman;

    /**
     * 联系人地址
     */
    private String linkmanAddress;

    /**
     * 开户行
     */
    private String bank;

    /**
     * 主体资质分类id
     */
    private Integer qualificationId;

    /**
     * 营业执照编码
     */
    private String businessLicenceCode;

    /**
     * 营业执照到期时间
     */
    private Timestamp businessLicenceExpireDate;

    /**
     * 营业执照是否长期有效 1是 0 否
     */
    private Integer isBusinessLicenceIndefinite;

    /**
     * 法人姓名
     */
    private String legalPersonName;

    /**
     * 企业法人身份证过期时间
     */
    private Timestamp legalPersonIdcardExpireDate;

    /**
     * 法人身份证是否长期有效 1是 0 否
     */
    private Integer isLegalPersonIdcardIndefinite;

    /**
     * 审核备注
     */
    private String auditRemark;

    /**
     * 0-启用 1-冻结 3编辑中 4待审核 5驳回
     */
    private Integer accountStatus;

    /**
     * 联系人邮箱
     */
    private String linkmanEmail;

    /**
     * GD广告系统状态（0允许 1禁止）
     */
    private Integer gdStatus;

    /**
     * 代理商授权有效期
     */
    private Timestamp agentAuthExpireDate;

    /**
     * 是否是内部账号 0否 1是
     */
    private Integer isInner;

    /**
     * 部门 id
     */
    private Integer departmentId;

    /**
     * 是否支持商家中心投放 0-否 1-是
     */
    private Integer isSupportSeller;

    /**
     * 是否支持游戏中心投放 0-否 1-是
     */
    private Integer isSupportGame;

    /**
     * 是否支持DPA投放 0-否 1-是
     */
    private Integer isSupportDpa;

    /**
     * 是否支持内容推广 0-否 1-是
     */
    private Integer isSupportContent;

    /**
     * 产品线名
     */
    private String productLine;

    /**
     * 联系人手机号
     */
    private String phoneNumber;

    /**
     * 证件类型 0-未知 1-身份证（大陆地区） 2-护照（港澳台及海外）
     */
    private Integer idcardType;

    /**
     * 证件号码
     */
    private String idcardNumber;

    /**
     * 证件到期时间
     */
    private Timestamp idcardExpireDate;

    /**
     * 联系人地址（个人账户）
     */
    private String personalAddress;

    /**
     * 证件有效期是否长期有效 1是 0 否
     */
    private Integer isIdcardIndefinite;

    /**
     * 个人姓名
     */
    private String personalName;

    /**
     * 集团id（对应acc_company_group的ID）
     */
    private Integer groupId;

    /**
     * 产品线id
     */
    private Integer productLineId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 是否支持UP主商单投放 0-否 1-是
     */
    private Integer isSupportPickup;

    /**
     * 是否支持互选广告投放 0-否 1-是
     */
    private Integer isSupportMas;

    /**
     * 是否自动更新帐号标签 0-否 1-是
     */
    private Integer autoUpdateLabel;

    /**
     * 是否支持商业起飞投放 0-否 1-是
     */
    private Integer isSupportFly;

    /**
     * 是否允许现金支付（个人起飞专用） 0-不允许 1-允许
     */
    private Integer allowCashPay;

    /**
     * 是否允许激励金支付（个人起飞专用） 0-不允许 1-允许
     */
    private Integer allowIncentiveBonusPay;

    /**
     * 所属客户id
     */
    private Integer customerId;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 账期
     */
    private Integer paymentPeriod;

    /**
     * 是否支持本地广告产品 0-否 1-是
     */
    private Integer isSupportLocalAd;

    /**
     * 一级行业标签
     */
    private Integer firstIndustryTagId;

    /**
     * 二级行业标签
     */
    private Integer secondIndustryTagId;

    /**
     * 三级行业标签
     */
    private Integer thirdIndustryTagId;

    /**
     * 商业行业一级分类id(新版)
     */
    private Integer commerceCategoryFirstId;

    /**
     * 商业行业二级分类id（新版）
     */
    private Integer commerceCategorySecondId;

    /**
     * 是否允许签约金支付（个人起飞专用） 0-不允许 1-允许
     */
    private Integer allowSigningBonusPay;
}
