package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.Status;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.crm.platform.api.account.dto.*;
import com.bilibili.crm.platform.api.account.service.*;
import com.bilibili.crm.platform.api.enums.CategoryLevelEnum;
import com.bilibili.crm.platform.api.enums.IndustryCategoryEnum;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.risk.dto.RiskControlCategoryDto;
import com.bilibili.crm.platform.api.risk.service.IRiskControlCommerceCategoryService;
import com.bilibili.crm.platform.biz.crm.account.dao.read.AccAccountReadOnlyDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerDao;
import com.bilibili.crm.platform.biz.dao.*;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.util.ExcelReadUtils;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.soa.dto.CommerceCategoryParentDto;
import com.dianping.cat.Cat;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * description：商业中心行业分类（行业重构后的统一分类）
 * date       ：2020/11/2 4:31 下午
 */
@Slf4j
@Service
public class CommerceCenterCategoryServiceImpl implements ICommerceCenterCategoryService {
    private final CommerceCenterCategoryDao commerceCenterCategoryDao;
    private final CommerceCenterCategoryRepo commerceCenterCategoryRepo;
    private final ILogOperatorService logOperatorService;
    private final IRiskControlCommerceCategoryService riskControlCommerceCategoryService;
    private final OldToNewCategoryMappingDao oldToNewCategoryMappingDao;
    private final NewToOldCategoryMappingDao newToOldCategoryMappingDao;
    private final INewOldCategoryMappingService newOldCategoryMappingService;
    private final AccAccountDao accAccountDao;
    private final CustomerDao customerDao;
    private final BsiOpportunityDao bsiOpportunityDao;
    private final ICategoryService categoryService;
    private final AccIndustryTagMappingDao accIndustryTagMappingDao;
    private final IIndustryTagService industryTagService;
    private final LauCreativeAuditDistributionIndustryMappingDao lauCreativeAuditDistributionIndustryMappingDao;
    private final IFastQueryAccountService fastQueryAccountService;
    private final AccAccountReadOnlyDao accAccountReadOnlyDao;

    public CommerceCenterCategoryServiceImpl(CommerceCenterCategoryDao commerceCenterCategoryDao,
                                             CommerceCenterCategoryRepo commerceCenterCategoryRepo,
                                             ILogOperatorService logOperatorService,
                                             IRiskControlCommerceCategoryService riskControlCommerceCategoryService,
                                             OldToNewCategoryMappingDao oldToNewCategoryMappingDao,
                                             NewToOldCategoryMappingDao newToOldCategoryMappingDao,
                                             INewOldCategoryMappingService newOldCategoryMappingService,
                                             AccAccountDao accAccountDao,
                                             CustomerDao customerDao,
                                             BsiOpportunityDao bsiOpportunityDao,
                                             ICategoryService categoryService,
                                             AccIndustryTagMappingDao accIndustryTagMappingDao,
                                             IIndustryTagService industryTagService,
                                             LauCreativeAuditDistributionIndustryMappingDao lauCreativeAuditDistributionIndustryMappingDao,
                                             IFastQueryAccountService fastQueryAccountService,
                                             AccAccountReadOnlyDao accAccountReadOnlyDao) {
        this.commerceCenterCategoryDao = commerceCenterCategoryDao;
        this.commerceCenterCategoryRepo = commerceCenterCategoryRepo;
        this.logOperatorService = logOperatorService;
        this.riskControlCommerceCategoryService = riskControlCommerceCategoryService;
        this.oldToNewCategoryMappingDao = oldToNewCategoryMappingDao;
        this.newToOldCategoryMappingDao = newToOldCategoryMappingDao;
        this.newOldCategoryMappingService = newOldCategoryMappingService;
        this.accAccountDao = accAccountDao;
        this.customerDao = customerDao;
        this.bsiOpportunityDao = bsiOpportunityDao;
        this.categoryService = categoryService;
        this.accIndustryTagMappingDao = accIndustryTagMappingDao;
        this.industryTagService = industryTagService;
        this.lauCreativeAuditDistributionIndustryMappingDao = lauCreativeAuditDistributionIndustryMappingDao;
        this.fastQueryAccountService = fastQueryAccountService;
        this.accAccountReadOnlyDao = accAccountReadOnlyDao;
    }

    @Override
    public Map<Integer, CategoryDto> getCategoryDtoInIds(List<Integer> ids) {
        return commerceCenterCategoryRepo.getCategoryDtoInIds(ids);
    }

    @Override
    public List<CategoryDto> getValidCategoryDtosByLevel(Integer level) {
        Assert.notNull(level, "行业分类层级不能为空");
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andLevelEqualTo(level);
        example.setOrderByClause("id desc");
        return commerceCenterCategoryRepo.queryList(example);
    }

    @Override
    public List<CategoryDto> getValidCategoryDtosByLevelName(Integer level, String name) {
        Assert.notNull(level, "行业分类层级不能为空");
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        CommerceCenterCategoryPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andLevelEqualTo(level);
        if (!StringUtils.isEmpty(name)) {
            criteria.andNameLike("%" + name + "%");
        }
        example.setOrderByClause("id desc");
        return commerceCenterCategoryRepo.queryList(example);
    }

    @Override
    public List<CategoryDto> getValidCategoryDtosByPid(Integer pId) {
        Assert.notNull(pId, "父级id不能为空");

        CategoryDto parentCategoryDto = commerceCenterCategoryRepo.getCategoryDtoById(pId);
        Assert.notNull(parentCategoryDto, "父级行业不存在");
        Assert.isTrue(parentCategoryDto.getStatus() != Status.INVALID.getCode(), "一级行业分类已被禁用，请重新选择后提交");

        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andPIdEqualTo(pId);
        example.setOrderByClause("id desc");
        return commerceCenterCategoryRepo.queryList(example);
    }


    @Override
    public List<CategoryDto> getAllCategoryDtosByLevel(Integer level) {
        Assert.notNull(level, "行业分类层级不能为空");
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andLevelEqualTo(level);

        return commerceCenterCategoryRepo.queryList(example);
    }

    private List<CommerceCategoryDto> setRiskControlLevel(
            List<CommerceCategoryDto> dtos) {

        if (CollectionUtils.isEmpty(dtos)) {
            return Collections.emptyList();
        }

        List<Integer> thirdIds = dtos.stream().map(CommerceCategoryDto::getThirdId).collect(Collectors.toList());
        Map<Integer, RiskControlCategoryDto> thirdMap = riskControlCommerceCategoryService.queryCategoryRiskLevelByCategoryIds(thirdIds, IndustryCategoryEnum.COMMERCE_INDUSTRY.getId());
        List<Integer> secondIds = dtos.stream().map(CommerceCategoryDto::getSecondId).collect(Collectors.toList());
        Map<Integer, RiskControlCategoryDto> secondMap = riskControlCommerceCategoryService.queryCategoryRiskLevelByCategoryIds(secondIds, IndustryCategoryEnum.COMMERCE_INDUSTRY.getId());


        return dtos.stream().map(dto -> {
            if (dto.getLevel().equals(CategoryLevelEnum.THIRD.getLevel())) {
                Integer thirdRiskLevel = thirdMap.getOrDefault(dto.getThirdId(), new RiskControlCategoryDto()).getRiskLevel();
                Integer secondRiskLevel = secondMap.getOrDefault(dto.getSecondId(), new RiskControlCategoryDto()).getRiskLevel();
                dto.setThirdRiskLevel(RiskLevel.getByLevel(thirdRiskLevel).getLevel());
                dto.setSecondRiskLevel(RiskLevel.getByLevel(secondRiskLevel).getLevel());
            } else if (dto.getLevel().equals(CategoryLevelEnum.SECOND.getLevel())) {
                Integer secondRiskLevel = secondMap.getOrDefault(dto.getSecondId(), new RiskControlCategoryDto()).getRiskLevel();
                dto.setSecondRiskLevel(RiskLevel.getByLevel(secondRiskLevel).getLevel());
            }
            return dto;
        }).collect(Collectors.toList());
    }

    private CategoryDto setRiskControlLevel(CategoryDto dto) {
        if (dto == null) {
            return null;
        }
        RiskControlCategoryDto categoryRisk = riskControlCommerceCategoryService.getCategoryRiskLevelByCategoryId(dto.getId(), IndustryCategoryEnum.COMMERCE_INDUSTRY.getId());
        if (categoryRisk != null) {
            dto.setRiskLevel(categoryRisk.getRiskLevel());
        }
        return dto;
    }

    @Override
    public CategoryDto getCategoryDtoById(Integer id) {
        CategoryDto categoryDto = commerceCenterCategoryRepo.getCategoryDtoById(id);
        return setRiskControlLevel(categoryDto);
    }


    @Override
    public Integer addCategoryDto(Operator operator, CategoryDto categoryDto) {

        CommerceCenterCategoryPo commerceCenterCategoryPo = new CommerceCenterCategoryPo();
        BeanUtils.copyProperties(categoryDto, commerceCenterCategoryPo);

        commerceCenterCategoryDao.insertSelective(commerceCenterCategoryPo);

        riskControlCommerceCategoryService.insertOrUpdate(commerceCenterCategoryPo.getId(), categoryDto.getRiskLevel(), IndustryCategoryEnum.COMMERCE_INDUSTRY.getId());

        logOperatorService.insertLog(operator, NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.CREATE_CATEGORY)
                .module(Module.ACCOUNT)
                .obj(commerceCenterCategoryPo)
                .objId(commerceCenterCategoryPo.getId())
                .systemType(SystemType.CRM)
                .build());

        return commerceCenterCategoryPo.getId();
    }

    @Override
    public void updateStatus(Operator operator, Integer categoryId) {

        Assert.notNull(categoryId, "行业分类ID不可为空");

        CommerceCenterCategoryPo commerceCenterCategoryPo = commerceCenterCategoryDao.selectByPrimaryKey(categoryId);
        Assert.notNull(commerceCenterCategoryPo, "该行业分类不存在");

        Integer newStatus = IsAble.NORMAL.getCode();
        Integer oldStatus = commerceCenterCategoryPo.getStatus();
        if (IsAble.NORMAL.getCode().equals(oldStatus)) {
            newStatus = IsAble.BANNED.getCode();
        }

        setCategoryStatus(categoryId, newStatus);

        logOperatorService.insertLog(operator, NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.DELETE_CATEGORY)
                .module(Module.ACCOUNT)
                .obj(commerceCenterCategoryPo)
                .objId(commerceCenterCategoryPo.getId())
                .systemType(SystemType.CRM)
                .build());
    }

    private void setCategoryStatus(Integer categoryId, Integer status) {
        CommerceCenterCategoryPo updatePo = new CommerceCenterCategoryPo();
        updatePo.setId(categoryId);
        updatePo.setStatus(status);
        commerceCenterCategoryDao.updateByPrimaryKeySelective(updatePo);
    }

    private void deleteCategory(CommerceCenterCategoryPo commerceCenterCategoryPo) {
        CommerceCenterCategoryPo record = new CommerceCenterCategoryPo();
        record.setIsDeleted(IsDeleted.DELETED.getCode());

        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(commerceCenterCategoryPo.getId())
                .andIsDeletedNotEqualTo(IsDeleted.DELETED.getCode());
        commerceCenterCategoryDao.updateByExampleSelective(record, example);
    }

    @Override
    public void update(Operator operator, CategoryDto categoryDto) {
        CommerceCenterCategoryPo commerceCenterCategoryPo = new CommerceCenterCategoryPo();
        BeanUtils.copyProperties(categoryDto, commerceCenterCategoryPo);
        commerceCenterCategoryDao.updateByPrimaryKeySelective(commerceCenterCategoryPo);

        //行业分类风险控制
        riskControlCommerceCategoryService.insertOrUpdate(commerceCenterCategoryPo.getId(), categoryDto.getRiskLevel(), IndustryCategoryEnum.COMMERCE_INDUSTRY.getId());

        logOperatorService.insertLog(operator, NewLogOperatorDto
                .builder()
                //需要增加枚举值
                .modifyType(ModifyType.UPDATE_CATEGORY)
                .module(Module.ACCOUNT)
                .obj(commerceCenterCategoryPo)
                .objId(commerceCenterCategoryPo.getId())
                .systemType(SystemType.CRM)
                .build());
    }

    @Override
    public List<CategoryDto> queryCategoryList(QueryCategoryParam param) {

        CommerceCenterCategoryPoExample example = buildAccCategoryPoExample(param);
        List<CommerceCenterCategoryPo> pos = commerceCenterCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convert).collect(Collectors.toList());
    }

    /**
     * 查询 行业 [id ,name] map
     *
     * @param param
     * @return
     */
    @Override
    public Map<Integer, String> queryCategoryNameMap(QueryCategoryParam param) {

        CommerceCenterCategoryPoExample example = buildAccCategoryPoExample(param);
        List<CommerceCenterCategoryPo> pos = commerceCenterCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos.stream().collect(Collectors.toMap(CommerceCenterCategoryPo::getId, CommerceCenterCategoryPo::getName));
    }

    @Override
    public List<CategoryDto> getCommerceCategoryByLevelPids(Integer level, List<Integer> pids, String nameLike) {
        Assert.notNull(level, "选择行业层级不可为空");

        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        CommerceCenterCategoryPoExample.Criteria criteria = example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andLevelEqualTo(level);
        if (!CollectionUtils.isEmpty(pids)) {
            criteria.andPIdIn(pids);
        }
        if (!StringUtils.isEmpty(nameLike)) {
            criteria.andNameLike("%" + nameLike + "%");
        }
        List<CommerceCenterCategoryPo> pos = commerceCenterCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::convert).collect(Collectors.toList());
    }

    @Override
    public PageResult<CommerceCategoryDto> getCommerceCategoryList(QueryCommerceCategoryDto query) {

        Assert.notNull(query, "查询条件不可为空");

        CommerceCenterCategoryPoExample example = this.getExample(query);
        long total = commerceCenterCategoryDao.countByExample(example);
        if (total == 0) {
            return PageResult.emptyPageResult();
        }
        List<CommerceCategoryDto> dtos = queryCommerceCategoryList(query);
        return PageResult.<CommerceCategoryDto>builder()
                .total((int) total)
                .records(dtos)
                .build();
    }

    @Override
    public List<CommerceCategoryParentDto> getAllParentCategories() {
        List<CommerceCenterCategoryPo> pos = getAllValidCategories();
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        } else {
            Map<Integer, List<CategoryDto>> childrenMap = pos.stream().filter(po -> po.getLevel() != 0)
                    .map(this::convert).collect(Collectors.groupingBy(CategoryDto::getPId));
            return pos.stream().filter(po -> po.getLevel() == 0)
                    .map(po -> this.po2ParentDto(po, childrenMap)).collect(Collectors.toList());
        }
    }

    @Override
    public CategoryDto getCommerceCategory(String name, Integer level, Integer pId) {
        CommerceCenterCategoryPo po = getCommerceCategoryByNameLevel(name, level, pId);
        return convert(po);
    }

    private List<CommerceCenterCategoryPo> getAllValidCategories() {
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode());
        return commerceCenterCategoryDao.selectByExample(example);
    }

    private CommerceCategoryParentDto po2ParentDto(CommerceCenterCategoryPo po, Map<Integer, List<CategoryDto>> childrenMap) {
        CommerceCategoryParentDto dto = CommerceCategoryParentDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        dto.setChildren(childrenMap.getOrDefault(dto.getId(), Collections.emptyList()));
        return dto;
    }

    @Override
    public void importNewCategory(String fileName) {
        List<String> newCategories = new ArrayList<>();
        try {
            newCategories = ExcelReadUtils.readExcel(fileName);
        } catch (Exception e) {
            Cat.logError(e);
            log.info("importNewCategory.error, fileName: {} , e : {}", fileName, e);
        }
        for (int i = 2; i < newCategories.size(); i++) {
            String[] categories = newCategories.get(i).split(",");
            //一级行业不存在则插入数据
            CommerceCenterCategoryPo firstCategory = getCommerceCategoryByNameLevel(categories[0], CategoryLevelEnum.ONE.getLevel(), 0);
            if (firstCategory == null) {
                firstCategory = CommerceCenterCategoryPo.builder().name(categories[0]).level(CategoryLevelEnum.ONE.getLevel()).pId(0).status(IsAble.NORMAL.getCode()).build();
                commerceCenterCategoryDao.insertSelective(firstCategory);
            }
            //二级行业不存在则插入数据
            CommerceCenterCategoryPo secondCategory = getCommerceCategoryByNameLevel(categories[1], CategoryLevelEnum.SECOND.getLevel(), firstCategory.getId());
            if (secondCategory == null) {
                secondCategory = CommerceCenterCategoryPo.builder().name(categories[1]).level(CategoryLevelEnum.SECOND.getLevel()).pId(firstCategory.getId()).status(IsAble.NORMAL.getCode()).build();
                commerceCenterCategoryDao.insertSelective(secondCategory);
            }
            //三级行业不存在则插入数据
            CommerceCenterCategoryPo thirdCategory = getCommerceCategoryByNameLevel(categories[2], CategoryLevelEnum.THIRD.getLevel(), secondCategory.getId());
            if (thirdCategory == null) {
                commerceCenterCategoryDao.insertSelective(CommerceCenterCategoryPo.builder().name(categories[2]).level(CategoryLevelEnum.THIRD.getLevel()).pId(secondCategory.getId()).status(IsAble.NORMAL.getCode()).build());
            }
        }
    }

    @Override
    public void fixNewCategory(Integer id, Integer pId) {
        CommerceCenterCategoryPo po = commerceCenterCategoryDao.selectByPrimaryKey(id);
        if (po == null) {
            return;
        }
        po.setPId(pId);
        commerceCenterCategoryDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public void importOldToNewCategory(String fileName) {
        List<String> oldToNewCategories = new ArrayList<>();
        try {
            oldToNewCategories = ExcelReadUtils.readExcel(fileName);
        } catch (Exception e) {
            Cat.logError(e);
            log.info("importOldToNewCategory.error, fileName: {} , e : {}", fileName, e);
        }
        for (int i = 2; i < oldToNewCategories.size(); i++) {
            String[] categories = oldToNewCategories.get(i).split(",");
            OldToNewCategoryMappingPoExample example = new OldToNewCategoryMappingPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andOldCategorySecondIdEqualTo(Integer.valueOf(categories[2]));
            List<OldToNewCategoryMappingPo> mappingPos = oldToNewCategoryMappingDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(mappingPos)) {
                continue;
            }
            CommerceCenterCategoryPo firstCategory = this.getCommerceCategoryByNameLevel(categories[4], CategoryLevelEnum.ONE.getLevel(), 0);
            if (firstCategory == null) {
                continue;
            }
            CommerceCenterCategoryPo commerceCategoryByNameLevel = this.getCommerceCategoryByNameLevel(categories[5], CategoryLevelEnum.SECOND.getLevel(), firstCategory.getId());
            if (commerceCategoryByNameLevel == null) {
                continue;
            }
            oldToNewCategoryMappingDao.insertSelective(OldToNewCategoryMappingPo.builder().oldCategorySecondId(Integer.valueOf(categories[2])).newCategorySecondId(commerceCategoryByNameLevel.getId()).build());
        }
    }

    @Override
    public void importOldToNewCategoryFix(String fileName) {
        List<String> oldToNewCategories = new ArrayList<>();
        try {
            oldToNewCategories = ExcelReadUtils.readExcel(fileName);
        } catch (Exception e) {
            Cat.logError(e);
            log.info("importOldToNewCategoryFix.error, fileName: {} , e : {}", fileName, e);
        }
        for (int i = 2; i < oldToNewCategories.size(); i++) {
            String[] categories = oldToNewCategories.get(i).split(",");
            CommerceCenterCategoryPo newFirstCategory = this.getCommerceCategoryByNameLevel(categories[4], CategoryLevelEnum.ONE.getLevel(), 0);
            if (newFirstCategory == null) {
                newFirstCategory = CommerceCenterCategoryPo.builder().name(categories[4]).level(CategoryLevelEnum.ONE.getLevel()).pId(0).status(IsAble.NORMAL.getCode()).build();
                commerceCenterCategoryDao.insertSelective(newFirstCategory);
            }
            CommerceCenterCategoryPo newSecondCategory = this.getCommerceCategoryByNameLevel(categories[5], CategoryLevelEnum.SECOND.getLevel(), newFirstCategory.getId());
            if (newSecondCategory == null) {
                newSecondCategory = CommerceCenterCategoryPo.builder().name(categories[5]).level(CategoryLevelEnum.SECOND.getLevel()).pId(newFirstCategory.getId()).status(IsAble.NORMAL.getCode()).build();
                commerceCenterCategoryDao.insertSelective(newSecondCategory);
            }
            OldToNewCategoryMappingPoExample example = new OldToNewCategoryMappingPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andOldCategorySecondIdEqualTo(Integer.valueOf(categories[2]));
            List<OldToNewCategoryMappingPo> mappingPos = oldToNewCategoryMappingDao.selectByExample(example);
            if (CollectionUtils.isEmpty(mappingPos)) {
                continue;
            }
            oldToNewCategoryMappingDao.updateByPrimaryKeySelective(OldToNewCategoryMappingPo.builder().id(mappingPos.get(0).getId()).oldCategorySecondId(Integer.valueOf(categories[2])).newCategorySecondId(newSecondCategory.getId()).build());
        }
    }

    @Override
    public void importNewToOldCategory(String fileName) {
        List<String> newToOldCategories = new ArrayList<>();
        try {
            newToOldCategories = ExcelReadUtils.readExcel(fileName);
        } catch (Exception e) {
            Cat.logError(e);
            log.info("importNewToOldCategory.error, fileName: {} , e : {}", fileName, e);
        }
        for (int i = 2; i < newToOldCategories.size(); i++) {
            String[] categories = newToOldCategories.get(i).split(",");
            CommerceCenterCategoryPo newFirstCategory = this.getCommerceCategoryByNameLevel(categories[0], CategoryLevelEnum.ONE.getLevel(), 0);
            if (newFirstCategory == null) {
                continue;
            }
            CommerceCenterCategoryPo newSecondCategory = this.getCommerceCategoryByNameLevel(categories[1], CategoryLevelEnum.SECOND.getLevel(), newFirstCategory.getId());
            if (newSecondCategory == null) {
                continue;
            }
            NewToOldCategoryMappingPoExample example = new NewToOldCategoryMappingPoExample();
            example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andNewCategorySecondIdEqualTo(newSecondCategory.getId());
            List<NewToOldCategoryMappingPo> mappingPos = newToOldCategoryMappingDao.selectByExample(example);
            if (!CollectionUtils.isEmpty(mappingPos)) {
                continue;
            }
            CategoryDto oldFirstCategory = categoryService.getCategoryDtoByNameLevel(categories[2], CategoryLevelEnum.ONE.getLevel(), 0);
            if (oldFirstCategory == null) {
                continue;
            }
            CategoryDto oldSecondCategory = categoryService.getCategoryDtoByNameLevel(categories[3], CategoryLevelEnum.SECOND.getLevel(), oldFirstCategory.getId());
            if (oldSecondCategory == null) {
                continue;
            }
            newToOldCategoryMappingDao.insertSelective(NewToOldCategoryMappingPo.builder().newCategorySecondId(newSecondCategory.getId()).oldCategorySecondId(oldSecondCategory.getId()).build());
        }
    }

    @Override
    public void fixNewToOldCategory(Integer commerceCategorySecondId, Integer categorySecondId) {
        newToOldCategoryMappingDao.insertSelective(NewToOldCategoryMappingPo.builder().newCategorySecondId(commerceCategorySecondId).oldCategorySecondId(categorySecondId).build());
    }

    @Override
    public void initAccountCategoryNew(List<Integer> accountIds) {
        List<AccAccountPo> accountPos = getAllValidAccount(accountIds);
        List<Integer> secondCategoryIds = accountPos.stream().map(AccAccountPo::getCategorySecondId).collect(Collectors.toList());
        Map<Integer, NewOldCategoryMappingDto> newCategoriesMap = newOldCategoryMappingService.getNewCategoriesMap(secondCategoryIds);
        Assert.notNull(newCategoriesMap, "旧-新行业映射为空");
        accountPos.forEach(po -> {
            NewOldCategoryMappingDto newOldCategoryMappingDto = newCategoriesMap.getOrDefault(po.getCategorySecondId(), NewOldCategoryMappingDto.builder().build());
            po.setCommerceCategoryFirstId(newOldCategoryMappingDto.getNewCategoryFirstId());
            po.setCommerceCategorySecondId(newOldCategoryMappingDto.getNewCategorySecondId());
            accAccountDao.updateByPrimaryKeySelective(po);
        });

    }

    @Override
    public void initAccountByIndustryTag(List<Integer> accountIds, String fileName) {
        List<AccIndustryTagMappingPo> mappingPos = getAllAccountIndustryTagValid(accountIds);
        List<Integer> mainTagIds = mappingPos.stream().map(AccIndustryTagMappingPo::getMainTagId).collect(Collectors.toList());
        Map<Integer, Integer> tagMappingMap = mappingPos.stream().distinct().collect(Collectors.toMap(AccIndustryTagMappingPo::getAccountId, AccIndustryTagMappingPo::getMainTagId));
        List<IndustryTagDto> industryTagDtos = industryTagService.getIndustryTagByIds(mainTagIds);
        Map<Integer, IndustryTagDto> industryTagMap = industryTagDtos.stream().collect(Collectors.toMap(IndustryTagDto::getId, Function.identity(), (o1, o2) -> o1));

        List<Integer> tagAccountIds = mappingPos.stream().map(AccIndustryTagMappingPo::getAccountId).collect(Collectors.toList());
        List<String> tagToNewCategories = new ArrayList<>();
        try {
            tagToNewCategories = ExcelReadUtils.readExcel(fileName);
        } catch (Exception e) {
            Cat.logError(e);
            log.info("importOldToNewCategory.error, fileName: {} , e : {}", fileName, e);
        }
        Map<Integer, CommerceCenterCategoryPo> map = new HashMap<>();
        for (int i = 1; i < tagToNewCategories.size(); i++) {
            String[] categories = tagToNewCategories.get(i).split(",");
            CommerceCenterCategoryPo newFirstCategory = this.getCommerceCategoryByNameLevel(categories[4], CategoryLevelEnum.ONE.getLevel(), 0);
            if (newFirstCategory == null) {
                continue;
            }
            CommerceCenterCategoryPo newSecondCategory = this.getCommerceCategoryByNameLevel(categories[5], CategoryLevelEnum.SECOND.getLevel(), newFirstCategory.getId());
            if (newSecondCategory == null) {
                continue;
            }
            if (!CollectionUtils.isEmpty(industryTagMap)) {
                List<IndustryTagDto> secondIndustryTags = industryTagService.getIndustryTagByTagName(categories[1]);
                List<IndustryTagDto> thirdIndustryTags = industryTagService.getIndustryTagByTagName(categories[2]);
                if (CollectionUtils.isEmpty(secondIndustryTags)) {
                    continue;
                }
                List<IndustryTagDto> industryTag = thirdIndustryTags.stream().filter(item -> (item.getParentId().equals(secondIndustryTags.get(0).getId()))).collect(Collectors.toList());
                if (!CollectionUtils.isEmpty(industryTag) && industryTagMap.get(industryTag.get(0).getId()) != null) {
                    map.put(industryTagMap.get(industryTag.get(0).getId()).getId(), newSecondCategory);
                }
            }
        }
        Assert.notNull(map, "映射为空");
        List<AccAccountPo> accountPos = getAllValidAccount(tagAccountIds);
        accountPos.forEach(po -> {
            Integer mainTagId = tagMappingMap.get(po.getAccountId());
            if (mainTagId != null) {
                CommerceCenterCategoryPo secondCategory = map.get(mainTagId);
                if (secondCategory != null) {
                    po.setCommerceCategoryFirstId(secondCategory.getPId());
                    po.setCommerceCategorySecondId(secondCategory.getId());
                }
            }
            accAccountDao.updateByPrimaryKeySelective(po);
        });
    }

    @Override
    public void initCustomerCategoryNew(List<Integer> customerIds) {
        List<CustomerPo> customerPos = getAllValidOrgCustomer(customerIds);
        List<Integer> secondCategoryIds = customerPos.stream().map(CustomerPo::getCategorySecondId).collect(Collectors.toList());
        Map<Integer, NewOldCategoryMappingDto> newCategoriesMap = newOldCategoryMappingService.getNewCategoriesMap(secondCategoryIds);
        Assert.notNull(newCategoriesMap, "旧-新行业映射为空");
        customerPos.forEach(po -> {
            NewOldCategoryMappingDto newOldCategoryMappingDto = newCategoriesMap.getOrDefault(po.getCategorySecondId(), NewOldCategoryMappingDto.builder().build());
            po.setCommerceCategoryFirstId(newOldCategoryMappingDto.getNewCategoryFirstId());
            po.setCommerceCategorySecondId(newOldCategoryMappingDto.getNewCategorySecondId());
            customerDao.updateByPrimaryKeySelective(po);
        });

    }

    @Override
    public void initBsiOpportunityCategoryNew(List<Integer> bsiIds) {
        List<BsiOpportunityPo> bsiOpportunityPos = getAllValidBsiOpportuni(bsiIds);
        List<Integer> secondCategoryIds = bsiOpportunityPos.stream().map(BsiOpportunityPo::getCategorySecondId).collect(Collectors.toList());
        Map<Integer, NewOldCategoryMappingDto> newCategoriesMap = newOldCategoryMappingService.getNewCategoriesMap(secondCategoryIds);
        Assert.notNull(newCategoriesMap, "旧-新行业映射为空");
        bsiOpportunityPos.forEach(po -> {
            NewOldCategoryMappingDto newOldCategoryMappingDto = newCategoriesMap.getOrDefault(po.getCategorySecondId(), NewOldCategoryMappingDto.builder().build());
            po.setCommerceCategoryFirstId(newOldCategoryMappingDto.getNewCategoryFirstId());
            po.setCommerceCategorySecondId(newOldCategoryMappingDto.getNewCategorySecondId());
            bsiOpportunityDao.updateByPrimaryKeySelective(po);
        });

    }

    @Override
    public void initLauCreativeAuditCategoryNew(List<Integer> mappingId) {
        List<LauCreativeAuditDistributionIndustryMappingPo> creativeAuditMappingPos = getAllValidLauCreativeAudit(mappingId);
        List<Integer> secondCategoryIds = creativeAuditMappingPos.stream().map(LauCreativeAuditDistributionIndustryMappingPo::getAccCategoryId).collect(Collectors.toList());
        Map<Integer, NewOldCategoryMappingDto> newCategoriesMap = newOldCategoryMappingService.getNewCategoriesMap(secondCategoryIds);
        Assert.notNull(newCategoriesMap, "旧-新行业映射为空");
        creativeAuditMappingPos.forEach(po -> {
            NewOldCategoryMappingDto newOldCategoryMappingDto = newCategoriesMap.getOrDefault(po.getAccCategoryId(), NewOldCategoryMappingDto.builder().build());
            po.setCommerceCategorySecondId(newOldCategoryMappingDto.getNewCategorySecondId());
            lauCreativeAuditDistributionIndustryMappingDao.updateByPrimaryKeySelective(po);
        });
    }

    @Override
    public void fixSecondCategory(String fileName) {
        List<String> fixSecond = new ArrayList<>();
        Integer sheetCount = ExcelReadUtils.getSheetCount(fileName);
        for (int i = 0; i < sheetCount; i++) {
            try {
                fixSecond = ExcelReadUtils.readExcel(fileName, i, 0, ",");
            } catch (Exception e) {
                Cat.logError(e);
                log.info("fixSecondCategory.error, fileName: {} , e : {}", fileName, e);
            }
            for (int j = 1; j < fixSecond.size(); j++) {
                String[] account = fixSecond.get(j).split(",");
                CommerceCenterCategoryPo firstCategoryDto = this.getCommerceCategoryByNameLevel(account[3], CategoryLevelEnum.ONE.getLevel(), 0);
                if (firstCategoryDto == null) {
                    continue;
                }
                CommerceCenterCategoryPo secondCategoryDto = this.getCommerceCategoryByNameLevel(account[4], CategoryLevelEnum.SECOND.getLevel(), firstCategoryDto.getId());
                if (secondCategoryDto == null) {
                    continue;
                }
                AccountBaseDto accountBaseDto = fastQueryAccountService.fetchOne(Integer.valueOf(account[0]), AccountFieldMapping.accountId);
                if (accountBaseDto == null) {
                    continue;
                }
                AccAccountPo po = AccAccountPo.builder().accountId(Integer.valueOf(account[0])).commerceCategoryFirstId(secondCategoryDto.getPId()).commerceCategorySecondId(secondCategoryDto.getId()).build();
                accAccountDao.updateByPrimaryKeySelective(po);
            }
        }
    }

    @Override
    public void fixCustomerSecondCategory(String fileName) {
        List<String> fixSecond = new ArrayList<>();
        Integer sheetCount = ExcelReadUtils.getSheetCount(fileName);
        for (int i = 0; i < sheetCount; i++) {
            try {
                fixSecond = ExcelReadUtils.readExcel(fileName, i, 0, ",");
            } catch (Exception e) {
                Cat.logError(e);
                log.info("fixSecondCategory.error, fileName: {} , e : {}", fileName, e);
            }
            for (int j = 1; j < fixSecond.size(); j++) {
                String[] customer = fixSecond.get(j).split(",");
                CommerceCenterCategoryPo firstCategoryDto = this.getCommerceCategoryByNameLevel(customer[3], CategoryLevelEnum.ONE.getLevel(), 0);
                if (firstCategoryDto == null) {
                    continue;
                }
                CommerceCenterCategoryPo secondCategoryDto = this.getCommerceCategoryByNameLevel(customer[4], CategoryLevelEnum.SECOND.getLevel(), firstCategoryDto.getId());
                if (secondCategoryDto == null) {
                    continue;
                }
                CustomerPo customerPo = customerDao.selectByPrimaryKey(Integer.valueOf(customer[0]));
                if (customerPo == null) {
                    continue;
                }
                CustomerPo po = CustomerPo.builder().id(Integer.valueOf(customer[0])).commerceCategoryFirstId(secondCategoryDto.getPId()).commerceCategorySecondId(secondCategoryDto.getId()).build();
                customerDao.updateByPrimaryKeySelective(po);
            }
        }
    }

    private CommerceCenterCategoryPoExample getExample(QueryCommerceCategoryDto query) {
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();

        CommerceCenterCategoryPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!CollectionUtils.isEmpty(query.getThirdIds())) {
            criteria.andIdIn(query.getThirdIds());
        } else if (!CollectionUtils.isEmpty(query.getSecondIds())) {
//            criteria.andPIdIn(query.getSecondIds());
            criteria.andIdIn(query.getSecondIds());
        } else if (!CollectionUtils.isEmpty(query.getFirstIds())) {
//            List<CommerceCenterCategoryPo> secondCategoryPos = this.queryCommerceCategoryByPids(query.getFirstIds());
//            //判断所选一级行业下是否有二级行业
//            if (CollectionUtils.isEmpty(secondCategoryPos)) {
//                criteria.andIdIn(query.getFirstIds());
//            } else {
//                List<Integer> secondIds = secondCategoryPos.stream().map(CommerceCenterCategoryPo::getId).collect(Collectors.toList());
//                criteria.andPIdIn(secondIds);
//            }
            criteria.andIdIn(query.getFirstIds());
        }

        example.setLimit(query.getPageInfo().getLimit());
        example.setOffset(query.getPageInfo().getOffset());
        example.setOrderByClause("id desc");
        return example;
    }

    //根据父级pids查询行业
    private List<CommerceCenterCategoryPo> queryCommerceCategoryByPids(List<Integer> pids) {
        if (CollectionUtils.isEmpty(pids)) {
            return Collections.emptyList();
        }
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();

        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andPIdIn(pids);
        return commerceCenterCategoryDao.selectByExample(example);
    }

    private List<CommerceCategoryDto> queryCommerceCategoryList(QueryCommerceCategoryDto query) {
        CommerceCenterCategoryPoExample example = getExample(query);
        List<CommerceCenterCategoryPo> pos = commerceCenterCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        List<CategoryDto> firstList = getAllCategoryDtosByLevel(CategoryLevelEnum.ONE.getLevel());
        Map<Integer, CategoryDto> firstMap = firstList.stream().collect(Collectors.toMap(CategoryDto::getId, Function.identity()));

        List<CategoryDto> secondList = getAllCategoryDtosByLevel(CategoryLevelEnum.SECOND.getLevel());
        Map<Integer, CategoryDto> secondMap = secondList.stream().collect(Collectors.toMap(CategoryDto::getId, Function.identity()));

        ArrayList<CommerceCategoryDto> list = new ArrayList<>();

        for (CommerceCenterCategoryPo po : pos) {
            Integer firstId = null, secondId = null, thirdId = null;
            String firstName = null, secondName = null, thirdName = null, firstRemark = null, secondRemark = null, thirdRemark = null;
            if (po.getLevel().equals(CategoryLevelEnum.THIRD.getLevel())) {
                thirdId = po.getId();
                thirdName = po.getName();
                thirdRemark = po.getRemark();
                CategoryDto secondCategory = secondMap.getOrDefault(po.getPId(), new CategoryDto());
                secondId = secondCategory.getId();
                secondName = secondCategory.getName();
                secondRemark = secondCategory.getRemark();
                CategoryDto firstCategory = firstMap.getOrDefault(secondCategory.getPId(), new CategoryDto());
                firstId = firstCategory.getId();
                firstName = firstCategory.getName();
                firstRemark = firstCategory.getRemark();
            } else if (po.getLevel().equals(CategoryLevelEnum.SECOND.getLevel())) {
                secondId = po.getId();
                secondName = po.getName();
                secondRemark = po.getRemark();
                CategoryDto firstCategory = firstMap.getOrDefault(po.getPId(), new CategoryDto());
                firstId = firstCategory.getId();
                firstName = firstCategory.getName();
                firstRemark = firstCategory.getRemark();
            } else if (po.getLevel().equals(CategoryLevelEnum.ONE.getLevel())) {
                firstId = po.getId();
                firstName = po.getName();
                firstRemark = po.getRemark();
            }
            CommerceCategoryDto dto = CommerceCategoryDto.builder()
                    .id(po.getId())
                    .level(po.getLevel())
                    .firstId(firstId)
                    .firstName(firstName)
                    .secondId(secondId)
                    .secondName(secondName)
                    .thirdId(thirdId)
                    .thirdName(thirdName)
                    .firstRemark(firstRemark)
                    .secondRemark(secondRemark)
                    .thirdRemark(thirdRemark)
                    .status(po.getStatus())
                    .prompt(po.getPrompt())
                    .build();
            list.add(dto);
        }
        return setRiskControlLevel(list);
    }


    private CommerceCenterCategoryPoExample buildAccCategoryPoExample(QueryCategoryParam param) {
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();

        CommerceCenterCategoryPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (!StringUtils.isEmpty(param.getCategorySecondName())) {
            criteria.andNameLike("%" + param.getCategorySecondName() + "%");
        }
        if (param.isSecond()) {
            criteria.andPIdNotEqualTo(0);
        }
        ObjectUtils.setList(param::getParentIds, criteria::andPIdIn);
        ObjectUtils.setObject(param::getBizLevel, criteria::andLevelEqualTo);

        return example;
    }

    private CategoryDto convert(CommerceCenterCategoryPo po) {
        CategoryDto dto = new CategoryDto();
        BeanUtils.copyProperties(po, dto);
        return dto;
    }

    private CommerceCenterCategoryPo getCommerceCategoryByNameLevel(String name, Integer level, Integer pId) {
        CommerceCenterCategoryPoExample example = new CommerceCenterCategoryPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andStatusEqualTo(IsAble.NORMAL.getCode()).andNameEqualTo(name).andLevelEqualTo(level).andPIdEqualTo(pId);
        List<CommerceCenterCategoryPo> pos = commerceCenterCategoryDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return null;
        }
        return pos.get(0);
    }

    private List<AccAccountPo> getAllValidAccount(List<Integer> accountIds) {
        AccAccountPoExample example = new AccAccountPoExample();
        AccAccountPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCategorySecondIdNotEqualTo(IsValid.FALSE.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        return accAccountReadOnlyDao.selectByExample(example);
    }

    private List<AccIndustryTagMappingPo> getAllAccountIndustryTagValid(List<Integer> accountIds) {
        AccIndustryTagMappingPoExample example = new AccIndustryTagMappingPoExample();
        AccIndustryTagMappingPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andMainTagIdNotEqualTo(IsValid.FALSE.getCode());
        if (!CollectionUtils.isEmpty(accountIds)) {
            criteria.andAccountIdIn(accountIds);
        }
        return accIndustryTagMappingDao.selectByExample(example);
    }

    private List<CustomerPo> getAllValidOrgCustomer(List<Integer> customerIds) {
        CustomerPoExample example = new CustomerPoExample();
        CustomerPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCategorySecondIdNotEqualTo(IsValid.FALSE.getCode());
        if (!CollectionUtils.isEmpty(customerIds)) {
            criteria.andIdIn(customerIds);
        }
        return customerDao.selectByExample(example);
    }

    private List<BsiOpportunityPo> getAllValidBsiOpportuni(List<Integer> bsiIds) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        BsiOpportunityPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCategorySecondIdNotEqualTo(IsValid.FALSE.getCode());
        if (!CollectionUtils.isEmpty(bsiIds)) {
            criteria.andIdIn(bsiIds);
        }
        return bsiOpportunityDao.selectByExample(example);
    }

    private List<LauCreativeAuditDistributionIndustryMappingPo> getAllValidLauCreativeAudit(List<Integer> mappingId) {
        LauCreativeAuditDistributionIndustryMappingPoExample example = new LauCreativeAuditDistributionIndustryMappingPoExample();
        LauCreativeAuditDistributionIndustryMappingPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccCategoryIdNotEqualTo(IsValid.FALSE.getCode());
        if (!CollectionUtils.isEmpty(mappingId)) {
            criteria.andIdIn(mappingId);
        }
        return lauCreativeAuditDistributionIndustryMappingDao.selectByExample(example);
    }
}
