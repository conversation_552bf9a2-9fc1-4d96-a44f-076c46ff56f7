package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.passport.api.dto.UserInfoDto;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.AreaDto;
import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.CrmDepartmentDto;
import com.bilibili.crm.platform.api.account.service.*;
import com.bilibili.crm.platform.api.company.dto.CompanyGroupDto;
import com.bilibili.crm.platform.api.contract.dto.*;
import com.bilibili.crm.platform.api.contract.service.*;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerQueryDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.expenditure.dto.QueryAuditInfoParam;
import com.bilibili.crm.platform.api.interlayer.dto.InterlayerCalculateDto;
import com.bilibili.crm.platform.api.interlayer.dto.InterlayerQueryDto;
import com.bilibili.crm.platform.api.interlayer.service.IInterlayerMoneyService;
import com.bilibili.crm.platform.api.order.dto.OrderBaseDto;
import com.bilibili.crm.platform.api.order.dto.OrderBaseWithProductCategoryDto;
import com.bilibili.crm.platform.api.order.dto.OrderDto;
import com.bilibili.crm.platform.api.order.service.IOrderQueryService;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.biz.Component.AccountWalletLogQuerier;
import com.bilibili.crm.platform.biz.Component.EffectMarketContractQuerier;
import com.bilibili.crm.platform.biz.Component.account.AdvertiseAndAgentInfoQuerier;
import com.bilibili.crm.platform.biz.Component.account.ContractAdvertiseAndAgentInfo;
import com.bilibili.crm.platform.biz.constant.CommonConstants;
import com.bilibili.crm.platform.biz.helper.ExpenditureAuditHelper;
import com.bilibili.crm.platform.biz.industry.enums.IndustryTypeEnum;
import com.bilibili.crm.platform.biz.industry.service.AggIndustryService;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.service.account.bilibiliuser.BilibiliUserQuerier;
import com.bilibili.crm.platform.biz.service.settle_account.common.CheckingType;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.*;
import com.bilibili.rbac.api.dto.UserBaseDto;
import com.bilibili.rbac.api.service.IUserService;
import com.google.common.collect.Lists;
import com.site.lookup.util.StringUtils;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.Map.Entry;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * Created by fanwenbin on 2017/11/27.
 */
@Service
public class ContractStatService implements IContractStatService {
    @Autowired
    private IContractService contractService;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private IAreaService areaService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private Integer tenantId;
    @Autowired
    private IUserService userService;
    @Autowired
    private IOrderQueryService orderQueryService;
    @Autowired
    private IContractQueryService contractQueryService;
    @Resource
    private BilibiliUserQuerier bilibiliUserQuerier;
    @Autowired
    private ICommerceCenterCategoryService commerceCenterCategoryService;
    /**
     * UP主视频制作支出
     */
    @Autowired
    private IContractVideoOutcomeService contractVideoOutcomeService;
    @Autowired
    private IContractOptimizeOutcomeService contractOptimizeOutcomeService;
    @Autowired
    private IContractRebatesOutcomeService contractRebatesOutcomeService;
    @Autowired
    private IContractOtherOutcomeService contractOtherOutcomeService;
    @Autowired
    private IInterlayerMoneyService interlayerMoneyService;
    @Autowired
    private IContractUserMappingService contractUserMappingService;
    @Autowired
    private ExpenditureAuditHelper expenditureAuditHelper;
    @Autowired
    private ICrmDepartmentService crmDepartmentService;
    @Autowired
    private EffectMarketContractQuerier effectMarketQuerier;
    @Autowired
    private AccountWalletLogQuerier accountWalletLogQuerier;
    @Autowired
    private AdvertiseAndAgentInfoQuerier advertiseAndAgentInfoQuerier;
    @Autowired
    private ICustomerQueryService iCustomerQueryService;
    @Autowired
    private IBizIndustryService iBizIndustryService;

    @Resource
    private AggIndustryService aggIndustryService;

    @Value("${contract.report.max.days:93}")
    private Integer maxDays;

    @Value("#{'${crm.direct.sale.types:1,3,14,17}'.split(',')}")
    @Deprecated
    public List<Integer> directSaleType;

    @Value("#{'${crm.channel.sale.types:2,4,9,11}'.split(',')}")
    public List<Integer> channelSaleType;

    @Override
    public ContractStatAmountDto statContractAmount(List<OrderDto> orderDtos) {
        List<OrderBaseDto> orderBaseDtos = orderDtos.stream().map(orderDto -> {
            OrderBaseDto orderBaseDto = new OrderBaseDto();
            BeanUtils.copyProperties(orderDto, orderBaseDto);
            return orderBaseDto;
        }).collect(Collectors.toList());
        return this.statContractAmountByOrderBaseDto(orderBaseDtos);
    }

    private ContractStatAmountDto statContractAmountByOrderBaseDto(List<OrderBaseDto> orderBaseDtos) {
        BigDecimal salesAmount = BigDecimal.ZERO;

        BigDecimal distributionAmount = BigDecimal.ZERO;

        BigDecimal replenishmentAmount = BigDecimal.ZERO;

        BigDecimal present = BigDecimal.ZERO;

        BigDecimal compensate = BigDecimal.ZERO;

        BigDecimal internalAmount = BigDecimal.ZERO;

        BigDecimal otherAmount = BigDecimal.ZERO;

        BigDecimal receivableAmount = BigDecimal.ZERO;

        BigDecimal payableAmount = BigDecimal.ZERO;

        for (OrderBaseDto orderDto : orderBaseDtos) {
            if (CrmOrderStatus.DELETED.getCode().equals(orderDto.getStatus())) {
                continue;
            }
            BigDecimal discountAmount = this.getDiscountAmount(orderDto);
            if (orderDto.getType().equals(CrmOrderType.UP_REQUIREMENT.getCode())) {
                payableAmount = payableAmount.add(discountAmount);
            }
            if (orderDto.getType().equals(CrmOrderType.UP_MAKE.getCode())) {
                payableAmount = payableAmount.add(discountAmount);
            } else {
                if (!(orderDto.getResourceType().equals(CrmOrderResourceType.DISTRIBUTION.getCode())
                        || orderDto.getResourceType().equals(CrmOrderResourceType.REPLENISHMENT.getCode())
                        || orderDto.getResourceType().equals(CrmOrderResourceType.PRESENT.getCode())
                        || orderDto.getResourceType().equals(CrmOrderResourceType.COMPENSATE.getCode())
                )) {
                    //补量和配送不算应收
                    receivableAmount = receivableAmount.add(discountAmount);
                }
            }
            switch (CrmOrderResourceType.getByCode(orderDto.getResourceType())) {
                case OTHER:
                    otherAmount = otherAmount.add(discountAmount);
                    break;
                case INTERNAL:
                    internalAmount = internalAmount.add(discountAmount);
                    break;
                case SALES:
                    salesAmount = salesAmount.add(discountAmount);
                    break;
                case DISTRIBUTION:
                    distributionAmount = distributionAmount.add(discountAmount);
                    break;
                case REPLENISHMENT:
                    replenishmentAmount = replenishmentAmount.add(discountAmount);
                    break;
                case PRESENT:
                    present = present.add(discountAmount);
                    break;
                case COMPENSATE:
                    compensate = present.add(discountAmount);
                    break;
            }
        }
        return ContractStatAmountDto.builder()
                .otherAmount(otherAmount)
                .internalAmount(internalAmount)
                .salesAmount(salesAmount)
                .distributionAmount(distributionAmount)
                .replenishmentAmount(replenishmentAmount)
                .present(present)
                .compensate(compensate)
                .receivableAmount(receivableAmount)
                .payableAmount(payableAmount)
                .totalAmount(receivableAmount.subtract(payableAmount))
                .build();
    }

    @Override
    public PageResult<ContractDataDto> queryContractData(Timestamp begin, Timestamp end, Integer page, Integer size, List<Integer> departmentIds, String username, List<Timestamp> closeMonth) {
        Assert.notNull(page, "page is not null");
        Assert.notNull(size, "size is not null");
        Assert.isTrue(!(null == begin || null == end) || !CollectionUtils.isEmpty(closeMonth), "关账月份和计收时间不可同时为空");

        List<Integer> filterAccountIds = queryAccountService.getAccountIdByDepartmentIdExcludePersonalFly(departmentIds);
        if (CollectionUtils.isEmpty(filterAccountIds)) {
            return PageResult.emptyPageResult();
        }

        InterlayerQueryDto queryDto = new InterlayerQueryDto();
        queryDto.setStartLaunchDate(begin);
        queryDto.setEndLaunchDate(end);
        queryDto.setClosingDates(closeMonth);
        List<InterlayerCalculateDto> interlayerCalculateDtos = interlayerMoneyService.queryInterlayerMoney(queryDto);

        if (CollectionUtils.isEmpty(interlayerCalculateDtos)) {
            return PageResult.emptyPageResult();
        }

        List<Integer> contractIds = interlayerCalculateDtos.stream().map(InterlayerCalculateDto::getContractId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(contractIds)) {
            return PageResult.emptyPageResult();
        }

        PageResult<ContractDto> contractDtoPageResult = contractQueryService.queryContractPageResult(QueryContractParam.builder()
                .contractIds(contractIds)
                .accountIds(filterAccountIds)
                .userName(username)
                .build(), page, size);
        if (contractDtoPageResult.getTotal() == 0) {
            return PageResult.emptyPageResult();
        }

        List<Integer> accountIds = contractDtoPageResult.getRecords().stream().map(ContractDto::getAccountId).collect(Collectors.toList());

        // 获取合同的广告主/代理商 账号/客户信息
        Map<Integer, ContractAdvertiseAndAgentInfo> advertiseAndAgentInfoMap =
                advertiseAndAgentInfoQuerier.queryAdvertiseAndAgentMap(contractDtoPageResult.getRecords());
        List<ContractDataDto> contractDataDtos = this.getContractDataDtos(contractDtoPageResult.getRecords(),
                accountIds, closeMonth, advertiseAndAgentInfoMap);

        return PageResult.<ContractDataDto>builder().total(contractDtoPageResult.getTotal()).records(contractDataDtos).build();
    }

    @Override
    public List<ContractDataDto> queryContractDataList(Timestamp begin, Timestamp end, List<Integer> departmentIds, String username, List<Timestamp> closeMonth) {
        Assert.isTrue(!(null == begin || null == end) || !CollectionUtils.isEmpty(closeMonth), "关账月份和计收时间不可同时为空");

        List<Integer> filterAccountIds = queryAccountService.getAccountIdByDepartmentIdExcludePersonalFly(departmentIds);
        if (CollectionUtils.isEmpty(filterAccountIds)) {
            return Collections.emptyList();
        }

        InterlayerQueryDto queryDto = new InterlayerQueryDto();
        queryDto.setStartLaunchDate(begin);
        queryDto.setEndLaunchDate(end);
        queryDto.setClosingDates(closeMonth);
        List<InterlayerCalculateDto> interlayerCalculateDtos = interlayerMoneyService.queryInterlayerMoney(queryDto);

        if (CollectionUtils.isEmpty(interlayerCalculateDtos)) {
            return Collections.emptyList();
        }

        List<Integer> contractIds = interlayerCalculateDtos.stream().map(InterlayerCalculateDto::getContractId).distinct().collect(Collectors.toList());

        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }

        List<ContractDto> contractDtoPageResult = contractQueryService.queryContractList(QueryContractParam.builder()
                .contractIds(contractIds)
                .accountIds(filterAccountIds)
                .userName(username)
                .build());
        if (CollectionUtils.isEmpty(contractDtoPageResult)) {
            return Collections.emptyList();
        }

        List<Integer> accountIds = contractDtoPageResult.stream().map(ContractDto::getAccountId).collect(Collectors.toList());

        // 获取合同的广告主/代理商 账号/客户信息
        Map<Integer, ContractAdvertiseAndAgentInfo> advertiseAndAgentInfoMap = advertiseAndAgentInfoQuerier.queryAdvertiseAndAgentMap(contractDtoPageResult);
        List<ContractDataDto> contractDataDtos = this.getContractDataDtos(contractDtoPageResult, accountIds, closeMonth, advertiseAndAgentInfoMap);

        return contractDataDtos;
    }

    private Map<Integer, String> getAccId2DepartNameMapByAccountIds(List<Integer> accountIds) {

        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.emptyMap();
        }
        Map<Integer, AccountBaseDto> accountBaseDtoMap = queryAccountService.getAccountBaseDtoMapInIdsFromReadOnly(accountIds);
        List<Integer> departmentIds = accountBaseDtoMap.values().stream().map(AccountBaseDto::getDepartmentId).collect(Collectors.toList());

        Map<Integer, CrmDepartmentDto> departmentMap = crmDepartmentService.queryDepartmentMapByIds(departmentIds);

        return accountBaseDtoMap.values().stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, item -> {
            CrmDepartmentDto curDepart = departmentMap.getOrDefault(item.getDepartmentId(), CrmDepartmentDto.builder().build());
            return StringUtils.isNotEmpty(curDepart.getName()) ? curDepart.getName() : "";
        }));
    }

    private List<ContractDataDto> getContractDataDtos(List<ContractDto> contractDtoPageResult, List<Integer> accountIds,
                                                      List<Timestamp> closeMonth, Map<Integer, ContractAdvertiseAndAgentInfo> contractAdvertiseAndAgentInfoMap) {
        //账号ID 和 合同中的代理商ID 放到一个数组中，然后查询所有账号账号ID 和代理商ID 对应的信息
        Set<Integer> agentIds = contractDtoPageResult.stream().map(dto -> dto.getAgentId()).collect(Collectors.toSet());
        accountIds.addAll(agentIds);
        Map<Integer, AccountBaseDto> accountBaseDtoMap = queryAccountService.getAccountBaseDtoMapInIdsFromReadOnly(accountIds);
        List<Integer> areaIds = accountBaseDtoMap.values().stream().map(AccountBaseDto::getAreaId).distinct().collect(Collectors.toList());

        Map<Integer, AreaDto> areaDtoMap = areaService.getAreaInIds(areaIds);

        Map<String, String> industryMap = aggIndustryService.queryIndustryInfo(accountBaseDtoMap, Lists.newArrayList(IndustryTypeEnum.COMMERCE_INDUSTRY, IndustryTypeEnum.UNITED_INDUSTRY));

        // 获取合同客户的所属部门
        List<Integer> contractAccountIds = contractDtoPageResult.stream().map(ContractDto::getAccountId).collect(Collectors.toList());
        Map<Integer, String> accId2DepartNameMap = getAccId2DepartNameMapByAccountIds(contractAccountIds);

        final List<ContractDataDto> contractDataDtos = new Vector<>();

        contractDtoPageResult.parallelStream().forEach(contractDto -> {
            //<contractId, orderList>
            Map<Integer, List<OrderBaseDto>> orderBaseDtoMap = orderQueryService.getOrderMapInCrmContractIds(Lists.newArrayList(contractDto.getId()));
            List<OrderBaseDto> orderBaseDtoList = orderBaseDtoMap.getOrDefault(contractDto.getId(), Collections.emptyList());
            ContractDataDto contractDataDto = buildContractDataDto(accountBaseDtoMap, areaDtoMap, industryMap, contractDto, orderBaseDtoList, accId2DepartNameMap);
            List<ContractDataDto> result = this.aggregateWithMonthAmount2(contractDataDto, contractDto.getId(),
                    closeMonth, contractAdvertiseAndAgentInfoMap);
            contractDataDtos.addAll(result);
        });
        return contractDataDtos.stream()
                .sorted(Comparator.comparing(ContractDataDto::getContractNumber)
                        .reversed()
                        .thenComparing(ContractDataDto::getLaunchDate))
                .collect(Collectors.toList());
    }

    /**
     * 拆月金额计算 (基于中间表)
     *
     * @param contractDataDto
     * @param crmContractId
     * @return
     */
    private List<ContractDataDto> aggregateWithMonthAmount2(ContractDataDto contractDataDto, Integer crmContractId,
                                                            List<Timestamp> closeMonth, Map<Integer, ContractAdvertiseAndAgentInfo> contractAdvertiseAndAgentInfoMap) {
        SimpleDateFormat YYYYMM = new SimpleDateFormat("yyyyMM");
        SimpleDateFormat SDF = new SimpleDateFormat("yyyy-MM-dd HH:mm");
        //增加关账月份查询
        List<InterlayerCalculateDto> dtoList = interlayerMoneyService.queryInterlayerMoney(InterlayerQueryDto.builder()
                .contractId(crmContractId)
                .closingDates(closeMonth)
                .build());

        if (CollectionUtils.isEmpty(dtoList)) {
            return Collections.emptyList();
        }

        //业务行业构建
        Map<Integer, CategoryDto> bizIndustryCategoryDtoMap = new HashMap<>();
        Map<Integer, CustomerBaseDto> customerBaseDtoMap = new HashMap<>();
        List<Integer> adCustomerIds = contractAdvertiseAndAgentInfoMap.values().stream().map(ContractAdvertiseAndAgentInfo::getAdvertiseCustomerId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(adCustomerIds)) {
            List<CustomerBaseDto> customerBaseDtos = iCustomerQueryService.queryCustomerByQueryDto(CustomerQueryDto.builder().customerIds(adCustomerIds).build());
            customerBaseDtoMap = customerBaseDtos.stream().collect(Collectors.toMap(CustomerBaseDto::getId, Function.identity()));
            List<Integer> bizIndustryFirstIds = customerBaseDtos.stream().map(CustomerBaseDto::getBizIndustryCategoryFirstId).distinct().collect(Collectors.toList());
            List<Integer> bizIndustrySecondIds = customerBaseDtos.stream().map(CustomerBaseDto::getBizIndustryCategorySecondId).distinct().collect(Collectors.toList());
            List<Integer> bizIndustryTagIds = Stream.of(bizIndustryFirstIds, bizIndustrySecondIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
            bizIndustryCategoryDtoMap = iBizIndustryService.getCategoryDtoInIds(bizIndustryTagIds);
        }

        // 预警值
        BigDecimal settleAlarmAmount = buildSettleAlarmAmount(dtoList, contractDataDto.getContractAmount());

        // 账单分组聚合: launchDate + isChecking + closingDate
        // 账单分组聚合: launchDate + + billSource+ isChecking + closingDate
        Map<String, Map<Integer, Map<Integer, Map<String, List<InterlayerCalculateDto>>>>> launchDateGroup = dtoList.stream().collect(
                Collectors.groupingBy(item -> YYYYMM.format(item.getLaunchDate()),
                        Collectors.groupingBy(InterlayerCalculateDto::getBillSource,
                                Collectors.groupingBy(InterlayerCalculateDto::getIsChecking,
                                        Collectors.groupingBy(item -> Objects.isNull(item.getClosingDate()) ? "" : YYYYMM.format(item.getClosingDate()))))));

        Map<Integer, CustomerBaseDto> finalCustomerBaseDtoMap = customerBaseDtoMap;
        Map<Integer, CategoryDto> finalBizIndustryCategoryDtoMap = bizIndustryCategoryDtoMap;
        List<ContractDataDto> result = launchDateGroup.entrySet().stream().map(entry -> {
            // 投放月份
            String launchDate = entry.getKey();
            Map<Integer, Map<Integer, Map<String, List<InterlayerCalculateDto>>>> sourceGroup = entry.getValue();

            return sourceGroup.entrySet().stream().map(sourceEntry -> {
                Integer billSource = sourceEntry.getKey();
                Map<Integer, Map<String, List<InterlayerCalculateDto>>> checkingGroup = sourceEntry.getValue();
                return checkingGroup.entrySet().stream().map(checkingEntry -> {
                    // 是否记收
                    CheckingType checkingType = CheckingType.getByCode(checkingEntry.getKey());
                    Map<String, List<InterlayerCalculateDto>> closingGroup = checkingEntry.getValue();
                    return closingGroup.entrySet().stream().map(item -> {
                        // 关账月份
                        String closingDate = item.getKey();
                        // 账单
                        List<InterlayerCalculateDto> bills = item.getValue();

                        // 点位打包价
                        BigDecimal amount = bills.stream().map(InterlayerCalculateDto::getDailyPackageAmount).reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
                        // 待计收
                        BigDecimal waitSettlePackageAmount = buildWaitSettlePackageAmount(bills);
                        // 已关账
                        BigDecimal closePackageAmount = buildClosePackageAmount(bills);
                        // 实结
                        //BigDecimal closeContractNowAmount = getCloseContractNowAmount(launchDate, closeContractNowDto);

                        ContractDataDto data = ContractDataDto.builder().build();
                        BeanUtils.copyProperties(contractDataDto, data);
                        // 合同的广告主/代理商信息
                        ContractAdvertiseAndAgentInfo contractAdvertiseAndAgentInfo = contractAdvertiseAndAgentInfoMap.get(contractDataDto.getContractId());
                        if (contractAdvertiseAndAgentInfo != null) {
                            data.setAdvertiseAccountId(contractAdvertiseAndAgentInfo.getAdvertiseAccountId());
                            data.setAdvertiseAccountName(contractAdvertiseAndAgentInfo.getAdvertiseAccountName());
                            data.setAdvertiseCustomerId(contractAdvertiseAndAgentInfo.getAdvertiseCustomerId());
                            data.setAdvertiseCustomerName(contractAdvertiseAndAgentInfo.getAdvertiseCustomerName());

                            CustomerBaseDto customerBaseDto = finalCustomerBaseDtoMap.getOrDefault(contractAdvertiseAndAgentInfo.getAdvertiseCustomerId(), CustomerBaseDto.builder().build());
                            data.setBizIndustryCategoryFirstName(finalBizIndustryCategoryDtoMap.getOrDefault(customerBaseDto.getBizIndustryCategoryFirstId(), CategoryDto.builder().build()).getName());
                            data.setBizIndustryCategorySecondName(finalBizIndustryCategoryDtoMap.getOrDefault(customerBaseDto.getBizIndustryCategorySecondId(), CategoryDto.builder().build()).getName());

                            data.setAgentAccountId(contractAdvertiseAndAgentInfo.getAgentAccountId());
                            data.setAgentAccountName(contractAdvertiseAndAgentInfo.getAgentAccountName());
                            data.setAgentCustomerId(contractAdvertiseAndAgentInfo.getAgentCustomerId());
                            data.setAgentCustomerName(contractAdvertiseAndAgentInfo.getAgentCustomerName());
                        }
                        ContractDto contractDto = contractService.getContractById(crmContractId);
                        if (null != contractDto) {
                            if (contractDto.getType().equals(ContractType.OUTER_MARKET.getCode())) {
                                data.setSignSubjectId(contractDto.getSignSubjectId());
                            } else {
                                data.setSignSubjectId(-1);
                            }
                        }
                        data.setLaunchDate(launchDate);
                        data.setMonthAmount(amount);
                        //data.setCloseContractNowAmount(closeContractNowAmount);
                        data.setDataCalculateDate(SDF.format(bills.get(0).getMtime()));
                        data.setWaitSettlePackageAmount(Utils.fromFenToYuan(waitSettlePackageAmount));
                        data.setClosePackageAmount(Utils.fromFenToYuan(closePackageAmount));
                        data.setSettleAlarmAmount(settleAlarmAmount);
                        data.setCheckType(checkingType.getCode());
                        data.setClosingDate(closingDate);
                        data.setBillSourceName(BillSourceType.getByCode(billSource).getName());
                        data.setCompanySubject("幻电科技（上海）有限公司");
                        if (data.getClosePackageAmount().compareTo(BigDecimal.ZERO) == 0) {
                            data.setAmountNoTax(data.getWaitSettlePackageAmount().divide(new BigDecimal(1.06), 2, RoundingMode.HALF_UP));
                        } else {
                            data.setAmountNoTax(data.getClosePackageAmount().divide(new BigDecimal(1.06), 2, RoundingMode.HALF_UP));
                        }
                        data.setAmountTax(data.getAmountNoTax().multiply(new BigDecimal(0.06)).setScale(2, RoundingMode.HALF_UP));
                        return data;
                    }).collect(Collectors.toList());
                }).flatMap(Collection::stream).collect(Collectors.toList());
            }).flatMap(Collection::stream).collect(Collectors.toList());
        }).flatMap(Collection::stream).collect(Collectors.toList());
        return result;
    }

    private BigDecimal buildWaitSettlePackageAmount(List<InterlayerCalculateDto> dtoList) {
        BigDecimal waitSettlePackageAmount = dtoList.stream()
                .filter(x -> IsValid.FALSE.getCode().equals(x.getIsClosed()))
                .map(InterlayerCalculateDto::getDailyPackageAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        return waitSettlePackageAmount;
    }

    private BigDecimal buildClosePackageAmount(List<InterlayerCalculateDto> dtoList) {
        BigDecimal closePackageAmount = dtoList.stream()
                .filter(x -> IsValid.TRUE.getCode().equals(x.getIsClosed()))
                .map(InterlayerCalculateDto::getDailyPackageAmount)
                .reduce(BigDecimal::add).orElse(BigDecimal.ZERO);
        return closePackageAmount;
    }

    private BigDecimal buildSettleAlarmAmount(List<InterlayerCalculateDto> dtoList, BigDecimal contractAmount) {

        BigDecimal waitSettlePackageAmount = Utils.fromFenToYuan(buildWaitSettlePackageAmount(dtoList));

        BigDecimal closePackageAmount = Utils.fromFenToYuan(buildClosePackageAmount(dtoList));

        return waitSettlePackageAmount.add(closePackageAmount).subtract(contractAmount);
    }

    /**
     * 组装拆月金额，需要考虑实结审核通过的调整金额，单位元
     * <p>
     * 实结的时间取实结记录的创建时间
     *
     * @param month
     * @param monthAmount
     * @param closeContractNowDto
     * @return
     */
    private BigDecimal buildMonthAmount(String month, BigDecimal monthAmount, CloseContractNowDto closeContractNowDto) {
        SimpleDateFormat YYYYMM = new SimpleDateFormat("yyyyMM");
        if (closeContractNowDto != null
                && month.equals(YYYYMM.format(closeContractNowDto.getCtime()))
                && ContractStatus.AUDITED.getCode().equals(closeContractNowDto.getStatus())) {

            if (OrderAmountAdjustType.ADD.getCode().equals(closeContractNowDto.getAdjustType())) {
                return Utils.fromFenToYuan(monthAmount.longValue() + closeContractNowDto.getAmount());
            } else if (OrderAmountAdjustType.REDUCE.getCode().equals(closeContractNowDto.getAdjustType())) {
                return Utils.fromFenToYuan(monthAmount.longValue() - closeContractNowDto.getAmount());
            }
        }
        return Utils.fromFenToYuan(monthAmount.longValue());
    }

    private BigDecimal getCloseContractNowAmount(String month, CloseContractNowDto closeContractNowDto) {

        SimpleDateFormat YYYYMM = new SimpleDateFormat("yyyyMM");
        if (closeContractNowDto != null
                && month.equals(YYYYMM.format(closeContractNowDto.getCtime()))
                && ContractStatus.AUDITED.getCode().equals(closeContractNowDto.getStatus())) {

            if (OrderAmountAdjustType.ADD.getCode().equals(closeContractNowDto.getAdjustType())) {
                return Utils.fromFenToYuan(closeContractNowDto.getAmount());
            } else if (OrderAmountAdjustType.REDUCE.getCode().equals(closeContractNowDto.getAdjustType())) {
                return Utils.fromFenToYuan(closeContractNowDto.getAmount()).negate();
            }
        }
        return Utils.fromFenToYuan(0);
    }

    private ContractDataDto buildContractDataDto(Map<Integer, AccountBaseDto> accountBaseDtoMap, Map<Integer, AreaDto> areaDtoMap,
                                                 Map<String, String> industryMap, ContractDto contractDto,
                                                 List<OrderBaseDto> orderBaseDtoList,
                                                 Map<Integer, String> accId2DepartNameMap) {

        String[] receiptsRecord = buildReceiptsRecord(contractDto.getReceiptsRecordDtos());

        StringBuilder channelSalesName = new StringBuilder();
        StringBuilder channelSalesType = new StringBuilder();
        StringBuilder directSalesName = new StringBuilder();
        StringBuilder directSalesType = new StringBuilder();

        buildSalesInfo(contractDto.getSaleDtos(), channelSalesName, channelSalesType, directSalesName, directSalesType);
        //法务合同编号，该合同归档记录中的法务合同编号，有多条时逗号隔开分别展示 +原合同基本信息中的法务合同id
        String legalContractIds = buildLeaglContractIds(contractDto);

        //不参加返点金额
        List<ContractFinanceRebateRecordDto> rebateRecordDtos = contractDto.getRebateRecordDtos();
        BigDecimal rebateBaseAmount = CollectionUtils.isEmpty(rebateRecordDtos) ? BigDecimal.ZERO
                : Utils.fromFenToYuan(rebateRecordDtos.stream().mapToLong(ContractFinanceRebateRecordDto::getRebateBaseAmount)
                .summaryStatistics().getSum());
        //打包 + pd 金额
        BigDecimal contract_amount = Utils.fromFenToYuan(contractDto.getAllAmount());
        BigDecimal notAttendRebateAmount = contract_amount.subtract(rebateBaseAmount);

        //合同开票金额
        BigDecimal invoiceAmount = CollectionUtils.isEmpty(contractDto.getInvoiceRecordDtos())
                ? BigDecimal.ZERO
                : Utils.fromFenToYuan(contractDto.getInvoiceRecordDtos().stream().mapToLong(InvoiceRecordDto::getAmount)
                .summaryStatistics().getSum());
        //返点金额
        BigDecimal rebateAmount = buildRebateAmount(contractDto.getRebateRecordDtos());

        //最新开票日期
        Timestamp lastInvoiceRecordDate = buildLastInvoiceRecordDate(contractDto.getInvoiceRecordDtos());
        AccountBaseDto accountBaseDto = accountBaseDtoMap.get(contractDto.getAccountId());
        int unitedFirstIndustryId = accountBaseDto == null ? 0 : accountBaseDto.getUnitedFirstIndustryId();
        int unitedSecondIndustryId = accountBaseDto == null ? 0 : accountBaseDto.getUnitedSecondIndustryId();
        int unitedThirdIndustryId = accountBaseDto == null ? 0 : accountBaseDto.getUnitedThirdIndustryId();
        return ContractDataDto.builder()
                .contractId(contractDto.getId())
                .contractNumber(contractDto.getContractNumber().toString())
                .accountArea(this.buildAccountArea(contractDto.getAccountId(), accountBaseDtoMap, areaDtoMap))
                .category(this.buildCategoryName(contractDto, accountBaseDtoMap, industryMap))
                .groupName(contractDto.getGroupName())
                .productLineName(contractDto.getProductLineName())
                .productName(contractDto.getProductName())
                //projectName使用合同表里面的项目名称字段2018-5-17
                .projectName(contractDto.getProjectName()) //this.buildProjectName(contractDto.getName()
                //服务对象变更为公司名称 2018-5-17
                .agentName(contractDto.getCompanyName())
                .legalContractId(legalContractIds)     //归档法务合同编号集合
                .contractAmount(Utils.fromFenToYuan(contractDto.getAmount() + contractDto.getPdAmount()))
                .archiveStatusDesc(ContractArchiveStatus.getByCode(contractDto.getArchiveStatus()).getDesc())
                .contractStatusDesc(ContractStatus.getByCode(contractDto.getStatus()).getDesc())
                .contractReceiptsAmount(this.buildReceiptsAmount((contractDto.getReceiptsRecordDtos())))
                .contractReceiptsRecord(receiptsRecord[0])
                .lastestReceiveDate(receiptsRecord[1])
                .contractInvoicesAmount(invoiceAmount)
                .contractInvoicesRecord(this.buildInvoiceRecord(contractDto.getInvoiceRecordDtos())[0])
                .classify(this.buildClassify(orderBaseDtoList))
                .receivableAmount(this.buildReceivableAmount(orderBaseDtoList))
                .scheduleStartTime(this.buildStartTime(orderBaseDtoList))
                .scheduleStopTime(this.buildStopTime(orderBaseDtoList))  //排期开始时间 2018-5-17
                .scheduleMonth(this.buildScheduleMonth(orderBaseDtoList)) //排期结束时间 2018-5-17
                .straightSign((contractDto.getAgentId() != null && contractDto.getAgentId() > 0) ? "代理" : "直签")
                .directSales(directSalesName.toString())
                .directSaleType(directSalesType.toString())
                .channelSales(channelSalesName.toString())
                .channelSaleType(channelSalesType.toString())
                .departmentName(accId2DepartNameMap.get(contractDto.getAccountId()))
                .remark(contractDto.getRemark()) //备注 2018-5-17
                .medium(contractDto.getCreator())
                .orderType(this.buildOrderType(orderBaseDtoList))
                .notAttendRebateAmount(notAttendRebateAmount)
                .rebatesRatio(buildRebatesRatioString(contractDto.getRebateRecordDtos()))
                .contractRebateAmount(rebateAmount)
                .lastestInvoiceDate(Utils.getTimestamp2String(lastInvoiceRecordDate))
                .unitedFirstIndustry(industryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + unitedFirstIndustryId, ""))
                .unitedSecondIndustry(industryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + unitedSecondIndustryId, ""))
                .unitedThirdIndustry(industryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + unitedThirdIndustryId, ""))
                .build();
    }

    @Override
    public void buildSalesInfo(List<SaleDto> saleDtos, StringBuilder channelSalesName, StringBuilder channelSalesType, StringBuilder directSalesName, StringBuilder directSalesType) {
        if (CollectionUtils.isNotEmpty(saleDtos)) {
            for (SaleDto saleDto : saleDtos) {
                String name = saleDto.getName();
                String typeName = saleDto.getTypeName();
                if (channelSaleType.contains(saleDto.getType())) {
                    channelSalesName.append(name).append(",");
                    channelSalesType.append(typeName).append(",");
                } else {
                    directSalesName.append(name).append(",");
                    directSalesType.append(typeName).append(",");
                }
            }
        }
        filterDelimiter(channelSalesName);
        filterDelimiter(channelSalesType);
        filterDelimiter(directSalesName);
        filterDelimiter(directSalesType);
    }

    private void filterDelimiter(StringBuilder stringBuilder) {
        if (null == stringBuilder) {
            return;
        }
        if (stringBuilder.lastIndexOf(",") > 0) {
            stringBuilder.deleteCharAt(stringBuilder.length() - 1);
        }
    }

    private Timestamp buildLastInvoiceRecordDate(List<InvoiceRecordDto> invoiceRecordDtos) {
        if (CollectionUtils.isEmpty(invoiceRecordDtos)) {
            return null;
        }
        List<InvoiceRecordDto> dtos = invoiceRecordDtos.stream().filter(item -> {
            return null != item.getBillingDate();
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(dtos)) {
            return null;
        }
        return dtos.stream().sorted(Comparator.comparing(InvoiceRecordDto::getBillingDate).reversed()).collect(Collectors.toList()).get(0).getBillingDate();
    }

    private BigDecimal buildRebatesRatio(List<ContractFinanceRebateRecordDto> rebateRecordDtos) {
        rebateRecordDtos = CollectionUtils.isEmpty(rebateRecordDtos) ? null
                : rebateRecordDtos.stream().filter(dto -> dto.getType().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rebateRecordDtos)) {
            return BigDecimal.ZERO;
        }

        Double result = 0d;
        for (ContractFinanceRebateRecordDto dto : rebateRecordDtos) {
            if (dto.getRebateRate() != null) {
                result += dto.getRebateRate().doubleValue();
            }
        }
        return new BigDecimal(result).setScale(2, RoundingMode.HALF_UP);
    }

    private String buildRebatesRatioString(List<ContractFinanceRebateRecordDto> rebateRecordDtos) {
        List<ContractFinanceRebateRecordDto> recordDtos = CollectionUtils.isEmpty(rebateRecordDtos) ? null
                : rebateRecordDtos.stream().filter(dto -> dto.getType().equals(1)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(recordDtos)) {
            return "";
        }
        return recordDtos.stream().map(dto -> dto.getRebateRate().toString() + "%").collect(Collectors.joining(","));
    }

    private Long buildNotAttendRebateAmount(List<ContractFinanceRebateRecordDto> rebateRecordDtos) {
        rebateRecordDtos = CollectionUtils.isEmpty(rebateRecordDtos) ? null
                : rebateRecordDtos.stream().filter(dto -> dto.getType().equals(0)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(rebateRecordDtos)) {
            return 0L;
        }

        Long result = 0L;
        for (ContractFinanceRebateRecordDto dto : rebateRecordDtos) {
            if (dto.getAmount() != null) {
                result += dto.getAmount();
            }
        }
        return result.longValue();
    }

    private String buildLeaglContractIds(ContractDto contractDto) {
        List<ArchiveRecordDto> archivesRecordDtos = Optional.ofNullable(contractDto.getArchivesRecordDtos()).orElse(Lists.newArrayList(ArchiveRecordDto.builder().build()));
        archivesRecordDtos.add(ArchiveRecordDto.builder().legalContractId(contractDto.getLegalContractId()).build());
        String legalContractIds = archivesRecordDtos.stream().filter(dto -> !StringUtils.isEmpty(dto.getLegalContractId()))
                .filter(dto -> ContractArchiveAuditStatus.PASS.getCode().equals(dto.getStatus()))
                .map(ArchiveRecordDto::getLegalContractId).collect(Collectors.joining(","));
        return legalContractIds;
    }

    private String buildAccountArea(Integer accountId, Map<Integer, AccountBaseDto> accountBaseDtoMap, Map<Integer, AreaDto> areaDtoMap) {
        if (!accountBaseDtoMap.containsKey(accountId)) {
            return "";
        }
        if (!areaDtoMap.containsKey(accountBaseDtoMap.get(accountId).getAreaId())) {
            return "";
        }
        return areaDtoMap.get(accountBaseDtoMap.get(accountId).getAreaId()).getName();
    }

    @Override
    public ContractStatDto queryContractStatDto(Timestamp begin, Timestamp end) {
        Assert.notNull(begin, "begin is not null");
        Assert.notNull(end, "end is not null");
        List<Integer> contractIds = contractService.queryContractIdsBetweenStatuCTime(begin, end);
        if (CollectionUtils.isEmpty(contractIds)) {
            return ContractStatDto.DEFAULT;
        }
        List<ContractDto> contractDtos = contractService.queryContractsInIds(contractIds);
        Long newContractCount = contractService.countNewContract(begin, end);
        Map<Integer, List<OrderBaseDto>> orderBaseDtoMap = orderQueryService.getOrderMapInCrmContractIds(contractIds);
        BigDecimal newContractAmount = Utils.fromFenToYuan(
                contractIds.stream().mapToLong(contractId -> orderBaseDtoMap.getOrDefault(contractId, Collections.emptyList())
                        .stream().filter(orderBaseDto -> orderBaseDto.getStatus().equals(CrmOrderStatus.WAIT.getCode())
                                || orderBaseDto.getStatus().equals(CrmOrderStatus.IN_PROGRESS.getCode())
                                || orderBaseDto.getStatus().equals(CrmOrderStatus.COMPLETED.getCode()))
                        .mapToLong(OrderBaseDto::getAmount).summaryStatistics().getSum()).summaryStatistics().getSum()
        );
        Long executeContractCount = contractDtos.stream()
                .filter(contractDto -> contractDto.getStatus().equals(ContractStatus.EXECUTE.getCode())).count();

        BigDecimal executeContractAmount = Utils.fromFenToYuan(contractDtos.stream()
                .filter(contractDto -> contractDto.getStatus().equals(ContractStatus.EXECUTE.getCode()))
                .filter(contractDto -> contractDto.getAmount() != null)
                .mapToLong(ContractDto::getAmount).summaryStatistics().getSum());

        Long partInvoiceCount = contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getInvoiceRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() > contractDto.getInvoiceRecordDtos().stream()
                        .filter(invoiceRecordDto -> invoiceRecordDto.getStatus().equals(ContractInvoiceAuditStatus.PASS.getCode()))
                        .mapToLong(InvoiceRecordDto::getAmount).summaryStatistics().getSum())
                .count();
        BigDecimal partInvoiceAmount = Utils.fromFenToYuan(contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getInvoiceRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() > contractDto.getInvoiceRecordDtos().stream()
                        .filter(invoiceRecordDto -> invoiceRecordDto.getStatus().equals(ContractInvoiceAuditStatus.PASS.getCode()))
                        .mapToLong(InvoiceRecordDto::getAmount).sum())
                .mapToLong(ContractDto::getAmount).sum());

        Long allInvoiceCount = contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getInvoiceRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() <= contractDto.getInvoiceRecordDtos().stream()
                        .filter(invoiceRecordDto -> invoiceRecordDto.getStatus().equals(ContractInvoiceAuditStatus.PASS.getCode()))
                        .mapToLong(InvoiceRecordDto::getAmount).sum())
                .count();
        BigDecimal allInvoiceAmount = Utils.fromFenToYuan(contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getInvoiceRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() <= contractDto.getInvoiceRecordDtos().stream()
                        .filter(invoiceRecordDto -> invoiceRecordDto.getStatus().equals(ContractInvoiceAuditStatus.PASS.getCode()))
                        .mapToLong(InvoiceRecordDto::getAmount).sum())
                .mapToLong(ContractDto::getAmount).sum());

        Long partReceiptsCount = contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getReceiptsRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() > contractDto.getReceiptsRecordDtos().stream()
                        .filter(receiptsRecordDto -> receiptsRecordDto.getStatus().equals(FinanceAuditStatus.PASS.getCode()))
                        .mapToLong(ReceiptsRecordDto::getAmount).sum())
                .count();

        BigDecimal partReceiptsAmount = Utils.fromFenToYuan(contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getReceiptsRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() > contractDto.getReceiptsRecordDtos().stream()
                        .filter(receiptsRecordDto -> receiptsRecordDto.getStatus().equals(FinanceAuditStatus.PASS.getCode()))
                        .mapToLong(ReceiptsRecordDto::getAmount).sum())
                .mapToLong(ContractDto::getAmount).sum());

        Long allReceiptsCount = contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getReceiptsRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() <= contractDto.getReceiptsRecordDtos().stream()
                        .filter(receiptsRecordDto -> receiptsRecordDto.getStatus().equals(FinanceAuditStatus.PASS.getCode()))
                        .mapToLong(ReceiptsRecordDto::getAmount).sum())
                .count();
        BigDecimal allReceiptsAmount = Utils.fromFenToYuan(contractDtos.stream()
                .filter(contractDto -> contractDto.getAmount() != null)
                .filter(contractDto -> !CollectionUtils.isEmpty(contractDto.getReceiptsRecordDtos()))
                .filter(contractDto -> contractDto.getAmount() <= contractDto.getReceiptsRecordDtos().stream()
                        .filter(receiptsRecordDto -> receiptsRecordDto.getStatus().equals(FinanceAuditStatus.PASS.getCode()))
                        .mapToLong(ReceiptsRecordDto::getAmount).sum())
                .mapToLong(ContractDto::getAmount).sum());


        return ContractStatDto.builder()
                .newContractCount(newContractCount)
                .executeContractCount(executeContractCount)
                .newContractAmount(newContractAmount)
                .executeContractAmount(executeContractAmount)
                .partInvoiceCount(partInvoiceCount)
                .partInvoiceAmount(partInvoiceAmount)
                .allInvoiceCount(allInvoiceCount)
                .allInvoiceAmount(allInvoiceAmount)
                .partReceiptsCount(partReceiptsCount)
                .partReceiptsAmount(partReceiptsAmount)
                .allReceiptsCount(allReceiptsCount)
                .allReceiptsAmount(allReceiptsAmount)
                .build();
    }

    private String buildScheduleMonth(List<OrderBaseDto> orderBaseDtos) {
        List<Timestamp> eachMonthFirstDay = orderBaseDtos.stream()
                .filter(orderBaseDto -> CommonConstants.validCrmOrderStatus(orderBaseDto.getStatus()))
                .filter(orderBaseDto -> orderBaseDto.getBeginTime() != null)
                .map(orderBaseDto -> CrmUtils.getEachMonthFirstDay(orderBaseDto.getBeginTime(),
                        Optional.ofNullable(orderBaseDto.getEndTime()).orElse(Utils.getNow())))
                .flatMap(timestamps -> timestamps.stream()).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(eachMonthFirstDay)) {
            return "";
        }
        return eachMonthFirstDay.stream().distinct().sorted()
                .map(timestamp -> (timestamp.getMonth() + 1) + "月").collect(Collectors.joining("、"));
    }

    private String buildStartTime(List<OrderBaseDto> orderBaseDtos) {
        List<Timestamp> startTimes = orderBaseDtos.stream()
                .filter(orderBaseDto -> CommonConstants.validCrmOrderStatus(orderBaseDto.getStatus()))
                .filter(orderBaseDto -> orderBaseDto.getBeginTime() != null)
                .map(OrderBaseDto::getBeginTime).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(startTimes)) {
            return "";
        }
//        Timestamp minStart = startTimes.stream().min((o1, o2) -> o1.compareTo(o2)).get();
        Timestamp minStart = Collections.max(startTimes);
        return Utils.getTimestamp2String(minStart);
    }

    private String buildStopTime(List<OrderBaseDto> orderBaseDtos) {
        List<Timestamp> endTimes = orderBaseDtos.stream()
                .filter(orderBaseDto -> CommonConstants.validCrmOrderStatus(orderBaseDto.getStatus()))
                .filter(orderBaseDto -> orderBaseDto.getBeginTime() != null)//开始时间没有 证明没开始 结束时间没有可能是进行中
                .map(orderBaseDto -> Optional.ofNullable(orderBaseDto.getEndTime()).orElse(Utils.getNow())).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(endTimes)) {
            return "";
        }
//        Timestamp maxEnd = endTimes.stream().max((o1, o2) -> o1.compareTo(o2)).get();
        Timestamp maxEnd = Collections.max(endTimes);
        return Utils.getTimestamp2String(maxEnd);
    }

    private BigDecimal buildReceivableAmount(List<OrderBaseDto> orderBaseDtos) {
        return this.statContractAmountByOrderBaseDto(orderBaseDtos).getReceivableAmount();
    }

    private String buildIsRebate(List<OrderBaseDto> orderBaseDtos) {
        if (CollectionUtils.isEmpty(orderBaseDtos)) {
            return "否";
        }
        if (orderBaseDtos.stream().allMatch(orderBaseDto ->
                orderBaseDto.getType().equals(CrmOrderType.UP_REQUIREMENT.getCode())
                        && IsValid.TRUE.getCode().equals(orderBaseDto.getIsRebate()))) {
            return "是";
        } else {
            return "否";
        }
    }

    private String buildClassify(List<OrderBaseDto> orderBaseDtos) {
        if (CollectionUtils.isEmpty(orderBaseDtos)) {
            return "";
        }
        List<String> classify = new ArrayList<>();
        if (orderBaseDtos.stream().anyMatch(orderBaseDto -> orderBaseDto.getType().equals(CrmOrderType.CPT.getCode()) || orderBaseDto.getType().equals(CrmOrderType.GD.getCode()))) {
            classify.add("硬广");
        }
        if (orderBaseDtos.stream().anyMatch(orderBaseDto -> orderBaseDto.getType().equals(CrmOrderType.UP_REQUIREMENT.getCode()))) {
            classify.add("UP主商单");
        } else {
            classify.add("其他");
        }
        return classify.stream().collect(Collectors.joining("、"));
    }

    private String buildOrderType(List<OrderBaseDto> orderBaseDtos) {
        if (CollectionUtils.isEmpty(orderBaseDtos)) {
            return "";
        }
        List<Integer> orderTypes = orderBaseDtos.stream().map(dto -> dto.getType()).distinct().collect(Collectors.toList());

        return orderTypes.stream().map(type -> CrmOrderType.getByCode(type).getDesc()).collect(Collectors.joining(","));
    }

    private String[] buildReceiptsRecord(List<ReceiptsRecordDto> receiptsRecordDtos) {
        if (CollectionUtils.isEmpty(receiptsRecordDtos)) {
            return new String[]{"", ""};
        }
        List<String> records = new ArrayList<>(receiptsRecordDtos.size());
        Timestamp lastestCollectDate = null;
        for (ReceiptsRecordDto receiptsRecordDto : receiptsRecordDtos) {
            Timestamp collectDate = receiptsRecordDto.getCollectionDate();
            records.add(Utils.fromFenToYuan(receiptsRecordDto.getAmount()) + "," + Utils.getTimestamp2String(collectDate));

            if (lastestCollectDate == null
                    || (collectDate != null && collectDate.getTime() > lastestCollectDate.getTime())) {
                lastestCollectDate = collectDate;
            }
        }
        String record = records.stream().collect(Collectors.joining(";"));

        return new String[]{record, lastestCollectDate == null ? "" : Utils.getTimestamp2String(lastestCollectDate)};
    }

    private BigDecimal buildReceiptsAmount(List<ReceiptsRecordDto> receiptsRecordDtos) {
        if (CollectionUtils.isEmpty(receiptsRecordDtos)) {
            return BigDecimal.ZERO;
        }

        BigDecimal amount = BigDecimal.ZERO;
        for (ReceiptsRecordDto dto : receiptsRecordDtos) {
            //收款取消了审核流程,创建收款即为审核通过
            if (!FinanceAuditStatus.PASS.getCode().equals(dto.getStatus())) {
                continue;
            }
            amount = amount.add(Utils.fromFenToYuan(dto.getAmount()));
        }

        return amount;
    }

    private BigDecimal buildRebateAmount(List<ContractFinanceRebateRecordDto> rebateRecordDtos) {
        if (CollectionUtils.isEmpty(rebateRecordDtos)) {
            return BigDecimal.ZERO;
        }
        BigDecimal rebateAmount = Utils.fromFenToYuan(rebateRecordDtos.stream().mapToLong(dto -> calculateAmount(dto.getRebateBaseAmount(), dto.getRebateRate()))
                .summaryStatistics().getSum());
        return rebateAmount;
    }

    private Long calculateAmount(Long amount, BigDecimal rate) {
        if (Objects.isNull(amount)) {
            return BigDecimal.ZERO.longValue();
        }
        return BigDecimal.valueOf(amount).multiply(rate).divide(new BigDecimal(100), 2, RoundingMode.HALF_UP).longValue();
    }

    private BigDecimal buildInvoiceAmount(List<InvoiceRecordDto> invoiceRecordDtos) {
        if (CollectionUtils.isEmpty(invoiceRecordDtos)) {
            return BigDecimal.ZERO;
        }

        BigDecimal amount = BigDecimal.ZERO;
        for (InvoiceRecordDto dto : invoiceRecordDtos) {
            if (!FinanceAuditStatus.PASS.getCode().equals(dto.getStatus())) {
                continue;
            }
            amount = amount.add(Utils.fromFenToYuan(dto.getAmount()));
        }

        return amount;
    }

    private String[] buildInvoiceRecord(List<InvoiceRecordDto> invoiceRecordDtos) {
        if (CollectionUtils.isEmpty(invoiceRecordDtos)) {
            return new String[]{"", ""};
        }
        List<String> records = new ArrayList<>(invoiceRecordDtos.size());
        Timestamp lastestBillingDate = null;
        for (InvoiceRecordDto invoiceRecordDto : invoiceRecordDtos) {
            Timestamp billingDate = invoiceRecordDto.getBillingDate();
            records.add(Utils.fromFenToYuan(invoiceRecordDto.getAmount()) + "," + Utils.getTimestamp2String(billingDate));

            if (lastestBillingDate == null || (billingDate != null && billingDate.getTime() > lastestBillingDate.getTime())) {
                lastestBillingDate = billingDate;
            }
        }
        String record = records.stream().collect(Collectors.joining(";"));
        String lastDate = lastestBillingDate == null ? "" : Utils.getTimestamp2String(lastestBillingDate);

        return new String[]{lastDate, record};
    }

    private String buildCategoryName(ContractDto contractDto, Map<Integer, AccountBaseDto> accountBaseDtoMap, Map<String, String> industryMap) {
        if (!accountBaseDtoMap.containsKey(contractDto.getAccountId())) {
            return "";
        }
        AccountBaseDto accountBaseDto = accountBaseDtoMap.get(contractDto.getAccountId());

        return industryMap.getOrDefault(IndustryTypeEnum.COMMERCE_INDUSTRY.name() + "-" + accountBaseDto.getCommerceCategoryFirstId(), "")
                + "/" + industryMap.getOrDefault(IndustryTypeEnum.COMMERCE_INDUSTRY.name() + "-" + accountBaseDto.getCommerceCategorySecondId(), "");
    }

    private String buildGroupName(ContractDto contractDto, Map<Integer, AccountBaseDto> accountBaseDtoMap, Map<Integer, CompanyGroupDto> companyGroupDtoMap) {
        if (!accountBaseDtoMap.containsKey(contractDto.getAccountId())) {
            return "";
        }
        //如果有代理商，且代理商存在关联公司组，取公司组的名称，
        if (contractDto.getAgentId() != null && contractDto.getAgentId() > 0) {
            if (companyGroupDtoMap.get(contractDto.getAgentId()) != null) {
                return companyGroupDtoMap.get(contractDto.getAgentId()).getName();
            } else {
                return accountBaseDtoMap.get(contractDto.getAgentId()) == null ? "" : accountBaseDtoMap.get(contractDto.getAgentId()).getCompanyName();
            }
        } else { //如果没有代理商 取合同关联的账号判断如果账号存在关联公司组  取公司组的名称
            if (companyGroupDtoMap.get(contractDto.getAccountId()) != null) {
                return companyGroupDtoMap.get(contractDto.getAccountId()).getName();
            } else {
                return contractDto.getCompanyName();
            }
        }
    }

    private BigDecimal getDiscountAmount(OrderBaseDto orderDto) {
        return Utils.fromFenToYuan(orderDto.getAmount()).multiply(orderDto.getDiscount()).divide(Utils.HUNDRED, 2, RoundingMode.HALF_UP);
    }

    /**
     * 获取占比  UP主视频实际金额/合同实际金额
     *
     * @param upAmount
     * @param amount
     * @return
     */
    private Float getPropotion(BigDecimal upAmount, BigDecimal amount) {
        if (amount.compareTo(BigDecimal.ZERO) == 0) {
            return 0f;
        }
        return upAmount.divide(amount, 2, BigDecimal.ROUND_HALF_DOWN).floatValue();
    }

    /**
     * 其他成本    合同支出合计-UP主支出成本
     *
     * @param contractId
     * @return
     */
    private BigDecimal getOtherCost(Integer contractId) {
//        BigDecimal optimizeTotal = contractOptimizeOutcomeService.getTotalOutcomeCost(contractId);
        BigDecimal otherTotal = contractOtherOutcomeService.getTotalOutcomeCost(contractId);
//        BigDecimal rebatesTotal = contractRebatesOutcomeService.getTotalOutcomeCost(contractId);
        return otherTotal;
    }

    /**
     * 统计订单里面的 UP主需求订单*折扣
     *
     * @param orderBaseDtoList
     * @return
     */
    private BigDecimal getUploaderEstimate(List<OrderBaseDto> orderBaseDtoList) {
        Long sum = orderBaseDtoList.stream().
                filter(orderBaseDto -> CrmOrderType.UP_REQUIREMENT.getCode().equals(orderBaseDto.getType()))
                .map(orderBaseDto -> (new BigDecimal(orderBaseDto.getAmount()).multiply(orderBaseDto.getDiscount()).divide(new BigDecimal(100))).longValue()).reduce(0L, Long::sum);
        return Utils.fromFenToYuan(sum);
    }

    private List<InterlayerCalculateDto> getInterLayerCalculateDto(Timestamp fromTime, Timestamp toTime, List<Integer> contractIds, List<Timestamp> colseMonth) {
        InterlayerQueryDto dto = new InterlayerQueryDto();
        dto.setStartLaunchDate(fromTime);
        dto.setEndLaunchDate(toTime);
        dto.setExecutedStatusList(Arrays.asList(0, 1));
        dto.setOrderStatusList(Arrays.asList(0, 1, 2));
        dto.setContractIds(contractIds);
        dto.setClosingDates(colseMonth);
        return interlayerMoneyService.queryInterlayerMoney(dto);
    }

    @Override
    public List<BrandMidlleDataDto> queryContractMiddleDataList(Timestamp fromTime, Timestamp toTime, List<Integer> departmentIds, String username, List<Timestamp> colseMonth) {
        Assert.isTrue(!(null == fromTime || null == toTime) || !CollectionUtils.isEmpty(colseMonth), "关账月份和计收时间不可同时为空");

        List<Integer> filterContractIds = getContractIdByDepartmentId(departmentIds, username);
        if (CollectionUtils.isEmpty(filterContractIds)) {
            return Collections.emptyList();
        }

        List<InterlayerCalculateDto> interlayerCalculateDtos = getInterLayerCalculateDto(fromTime, toTime, filterContractIds, colseMonth);

        if (CollectionUtils.isEmpty(interlayerCalculateDtos)) {
            return Collections.emptyList();
        }

        List<Integer> contractIds = interlayerCalculateDtos.stream().map(InterlayerCalculateDto::getContractId).collect(Collectors.toList());
        List<Integer> orderIds = interlayerCalculateDtos.stream().map(InterlayerCalculateDto::getOrderId).collect(Collectors.toList());

        Map<Integer, ContractDto> contractDtoMap = contractQueryService.queryContractMapInIds(contractIds);
        List<ContractDto> contractDtos = contractDtoMap.values().stream().collect(Collectors.toList());
        Map<Integer, ContractAdvertiseAndAgentInfo> contractAdvertiseAndAgentInfoMap = advertiseAndAgentInfoQuerier.queryAdvertiseAndAgentMap(contractDtos);

        Map<Integer, OrderBaseWithProductCategoryDto> idMapOrder = orderService.getOrderWithProductCategoryMapInOrderIds(orderIds);

        List<Integer> accountIds = contractDtoMap.values().stream().map(ContractDto::getAccountId).collect(Collectors.toList());

        Map<Integer, AccountBaseDto> idMapAccount = queryAccountService.getAccountBaseDtoMapInIdsFromReadOnly(accountIds);

        Map<String, String> industryMap = aggIndustryService.queryIndustryInfo(idMapAccount, Lists.newArrayList(IndustryTypeEnum.UNITED_INDUSTRY));

        Map<Integer, String> accId2DepartNameMap = getAccId2DepartNameMapByAccountIds(accountIds);

        //合同责任人账号信息
        List<Integer> executeIds = contractDtoMap.values().stream()
                .filter(dto -> Utils.isPositive(dto.getExecuteId())).map(ContractDto::getExecuteId).distinct()
                .filter(Objects::nonNull).collect(Collectors.toList());
        List<UserBaseDto> userByIds = userService.getUserByIds(tenantId, executeIds);
        Map<Integer, UserBaseDto> idMapUser = userByIds.stream().collect(Collectors.toMap(UserBaseDto::getId, Function.identity()));

        return convert2BrandMidlleDataDto(interlayerCalculateDtos, contractDtoMap, contractAdvertiseAndAgentInfoMap, idMapOrder, idMapAccount,
                industryMap, accId2DepartNameMap, idMapUser);
    }

    /**
     * 查询合同的4种支出项（编辑中，已通过）
     */
    @Override
    public List<ContractExpenditureDto> queryContractExpenditureList(Timestamp fromTime, Timestamp toTime, Integer auditStatus, List<Integer> departmentIds, String username) {
        Assert.notNull(fromTime, "fromTime is not allow null");
        Assert.notNull(toTime, "endTime is not allow null");
        Assert.isTrue(Utils.getEachDays(fromTime, toTime).size() <= maxDays, "品牌广告支出最多展现" + maxDays + "天数据");
        Assert.isTrue(!Utils.isPositive(auditStatus) || ExpenditureAuditStatus.canShowInReport.contains(auditStatus), "非法的审核状态");

        List<ContractBaseDto> dtos = contractService.queryContractIdsBetweenOrderTime(fromTime, toTime);
        List<Integer> validContractIds = contractService.queryValidContractIds();

        List<Integer> filterContractIds = getContractIdByDepartmentId(departmentIds, username);
        if (CollectionUtils.isEmpty(filterContractIds)) {
            return Collections.emptyList();
        }
        validContractIds.retainAll(filterContractIds);

        //过滤出合同状态机流水表中有过【执行中】，【待收款】，【已完成】，【可执行完成】状态的合同
        List<Integer> contractIds = dtos.stream().filter(dto -> validContractIds.contains(dto.getId()))
                .map(ContractBaseDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(contractIds)) {
            return Collections.emptyList();
        }

        //【品牌广告支出数据报表】中，只包含【编辑中】【审核通过】的支出项
        List<Integer> auditStatuses = (Utils.isPositive(auditStatus)) ? Arrays.asList(auditStatus)
                : Arrays.asList(ExpenditureAuditStatus.EDIT.getCode(), ExpenditureAuditStatus.RECHECK_PASS.getCode());

        QueryAuditInfoParam param = QueryAuditInfoParam.builder()
                .contractIds(contractIds)
                .auditStatuses(auditStatuses)
                .build();
        List<ContractVideoOutcomeDto> videos = contractVideoOutcomeService.queryByParam(param);
        List<ContractOptimizeOutcomeDto> optimizes = contractOptimizeOutcomeService.queryByParam(param);
        List<ContractRebatesOutcomeDto> rebates = contractRebatesOutcomeService.queryByParam(param);
        List<ContractOtherOutcomeDto> others = contractOtherOutcomeService.queryByParam(param);


        //将4种支出dto转成ContractExpenditureDto
        List<ContractExpenditureDto> videoList = buildFromVideoOutcomeDtos(videos);
        List<ContractExpenditureDto> optimizeList = buildFromOptimizeOutcomeDtos(optimizes);
        List<ContractExpenditureDto> rebateList = buildFromRebatesOutcomeDtos(rebates);
        List<ContractExpenditureDto> otherList = buildFromOtherOutcomeDtos(others);

        List<ContractExpenditureDto> allExpenditure = Lists.newArrayList();
        allExpenditure.addAll(videoList);
        allExpenditure.addAll(optimizeList);
        allExpenditure.addAll(rebateList);
        allExpenditure.addAll(otherList);

        Map<Integer, List<ContractUserDto>> userMap = contractUserMappingService.queryContractUserByContractIds(contractIds);

        allExpenditure.forEach(dto -> {
            Integer contractId = dto.getContractId();
            dto.setDirectSales(expenditureAuditHelper.buildDirectSales(userMap.get(contractId)));
            dto.setDirectLeader(expenditureAuditHelper.buildDirectLeader(userMap.get(contractId)));
            dto.setDirectChief(expenditureAuditHelper.buildDirectChief(userMap.get(contractId)));
            dto.setChannelSales(expenditureAuditHelper.buildChannelSales(userMap.get(contractId)));
            dto.setChannelLeader(expenditureAuditHelper.buildChannelLeader(userMap.get(contractId)));
            dto.setChannelChief(expenditureAuditHelper.buildChannelChief(userMap.get(contractId)));
        });

        return allExpenditure;
    }

    private List<ContractExpenditureDto> buildFromVideoOutcomeDtos(List<ContractVideoOutcomeDto> videoOutcomeDtos) {

        List<ContractExpenditureDto> expenditureDtos = Lists.newArrayList();
        videoOutcomeDtos.forEach(video -> expenditureDtos.add(ContractExpenditureDto.builder()
                .contractId(video.getContractId())
                .departmentId(video.getUploaderBelongMcn())
                .expenditureType(ExpenditureType.VIDEO.getCode())
                .expenditureTypeDesc(ExpenditureType.VIDEO.getDesc())
                .expenditureName(video.getUploaderName())
                .outcomeTypeDesc(Utils.isPositive(video.getUploaderCooperateType()) ? UploaderCooperateType.getByCode(video.getUploaderCooperateType()).getDesc() : "")
                .amount(video.getPriceOfMaking())
                .orderId(video.getCrmOrderId())
                .auditStatus(video.getAuditStatus())
                .build()));

        return expenditureDtos;
    }

    private List<ContractExpenditureDto> buildFromOptimizeOutcomeDtos(List<ContractOptimizeOutcomeDto> optimizeOutcomeDtos) {
        List<ContractExpenditureDto> expenditureDtos = Lists.newArrayList();
        optimizeOutcomeDtos.forEach(optimize -> expenditureDtos.add(ContractExpenditureDto.builder()
                .contractId(optimize.getContractId())
                .departmentId(optimize.getDepartmentId())
                .expenditureType(ExpenditureType.OPTIMIZE.getCode())
                .expenditureTypeDesc(ExpenditureType.OPTIMIZE.getDesc())
                .expenditureName(optimize.getName())
                .amount(optimize.getPrice())
                .orderId(optimize.getCrmOrderId())
                .auditStatus(optimize.getAuditStatus())
                .build()));

        return expenditureDtos;
    }

    private List<ContractExpenditureDto> buildFromRebatesOutcomeDtos(List<ContractRebatesOutcomeDto> rebatesOutcomeDtos) {

        List<ContractExpenditureDto> expenditureDtos = Lists.newArrayList();
        rebatesOutcomeDtos.forEach(rebate -> expenditureDtos.add(ContractExpenditureDto.builder()
                .contractId(rebate.getContractId())
                .expenditureType(ExpenditureType.REBATE.getCode())
                .expenditureTypeDesc(ExpenditureType.REBATE.getDesc())
                .expenditureName(ExpenditureType.REBATE.getDesc())
                .amount(rebate.getPriceOfRebates())
                .auditStatus(rebate.getAuditStatus())
                .build()));
        return expenditureDtos;
    }

    private List<ContractExpenditureDto> buildFromOtherOutcomeDtos(List<ContractOtherOutcomeDto> otherOutcomeDtos) {
        List<ContractExpenditureDto> expenditureDtos = Lists.newArrayList();
        otherOutcomeDtos.forEach(other -> expenditureDtos.add(ContractExpenditureDto.builder()
                .contractId(other.getContractId())
                .departmentId(other.getDepartmentId())
                .expenditureType(ExpenditureType.OTHER.getCode())
                .expenditureTypeDesc(ExpenditureType.OTHER.getDesc())
                .expenditureName(other.getName())
                .outcomeTypeDesc(Utils.isPositive(other.getOutcomeType()) ? OtherOutcomeType.getByCode(other.getOutcomeType()).getDesc() : "")
                .amount(other.getPrice())
                .orderId(other.getCrmOrderId())
                .auditStatus(other.getAuditStatus())
                .build()));

        return expenditureDtos;
    }

    private List<BrandMidlleDataDto> convert2BrandMidlleDataDto(List<InterlayerCalculateDto> interlayerCalculateDtos,
                                                                Map<Integer, ContractDto> idMapContractDtos,
                                                                Map<Integer, ContractAdvertiseAndAgentInfo> contractAdvertiseAndAgentInfoMap,
                                                                Map<Integer, OrderBaseWithProductCategoryDto> idMapOrder,
                                                                Map<Integer, AccountBaseDto> idMapAccount,
                                                                Map<String, String> industryMap,
                                                                Map<Integer, String> accId2DepartNameMap,
                                                                Map<Integer, UserBaseDto> idMapUser) {

        if (CollectionUtils.isEmpty(interlayerCalculateDtos)) {
            return Collections.emptyList();
        }

        List<BrandMidlleDataDto> list = new ArrayList<>(interlayerCalculateDtos.size());

        // 获取orderIds
        List<Integer> orderIds = interlayerCalculateDtos.stream().map(t -> t.getOrderId()).collect(Collectors.toList());
        // 获取mids
        Map<Integer, OrderBaseDto> orderMapInCrmOrderIds = orderService.getOrderMapInCrmOrderIds(orderIds);
        List<Long> mids = orderMapInCrmOrderIds.values().stream().map(t -> t.getMid()).collect(Collectors.toList());
        // 根据mids获取bilibili用户信息
        Map<Long, UserInfoDto> biliUserInfosByMids = bilibiliUserQuerier.getBiliUserInfosByMids(mids);

        // 订单中的资源提供方
        List<Integer> resDepartmentIds = orderMapInCrmOrderIds.values().stream().map(OrderBaseDto::getDepartmentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmDepartmentDto> resDepartmentMap = crmDepartmentService.queryDepartmentMapByIds(resDepartmentIds);
        // 订单中的资源提供方2
        List<Integer> resSecondDepartmentIds = orderMapInCrmOrderIds.values().stream().map(OrderBaseDto::getSecondDepartmentId).distinct().collect(Collectors.toList());
        Map<Integer, CrmDepartmentDto> resSecondDepartmentMap = crmDepartmentService.queryDepartmentMapByIds(resSecondDepartmentIds);

        for (InterlayerCalculateDto interlayerCalculateDto : interlayerCalculateDtos) {
            BrandMidlleDataDto dataDto = new BrandMidlleDataDto();
            OrderBaseDto orderBaseDto = orderMapInCrmOrderIds.get(interlayerCalculateDto.getOrderId());
            if (orderBaseDto != null) {
                //资源提供方1
                if (resDepartmentMap.isEmpty()) {
                    dataDto.setResDepartmentName("");
                } else {
                    CrmDepartmentDto resDepartment = resDepartmentMap.getOrDefault(orderBaseDto.getDepartmentId(), CrmDepartmentDto.builder().build());
                    dataDto.setResDepartmentName(resDepartment.getName());
                }
                //资源提供方2
                if (resSecondDepartmentMap.isEmpty()) {
                    dataDto.setResSecondDepartmentName("");
                } else {
                    CrmDepartmentDto resSecondDepartment = resSecondDepartmentMap.getOrDefault(orderBaseDto.getSecondDepartmentId(), CrmDepartmentDto.builder().build());
                    dataDto.setResSecondDepartmentName(resSecondDepartment.getName());
                }
            } else {
                dataDto.setResDepartmentName("");
                dataDto.setResSecondDepartmentName("");
            }
            dataDto.setContractId(interlayerCalculateDto.getContractId());
            dataDto.setDailyPackageAmount(interlayerCalculateDto.getDailyPackageAmount() == null
                    ? BigDecimal.ZERO : Utils.fromFenToYuan(interlayerCalculateDto.getDailyPackageAmount().doubleValue()));
            dataDto.setLaunchDate(Utils.getTimestamp2String(interlayerCalculateDto.getLaunchDate(), "yyyy-MM-dd"));
            dataDto.setOrderId(interlayerCalculateDto.getOrderId());
            dataDto.setAmountNoTax(dataDto.getDailyPackageAmount().divide(new BigDecimal(1.06), 2, RoundingMode.HALF_UP));
            dataDto.setAmountTax(dataDto.getAmountNoTax().multiply(new BigDecimal(0.06)).setScale(2, RoundingMode.HALF_UP));
//            ContractDto contract = contractService.getContractById(dataDto.getContractId());
            ContractDto contract = idMapContractDtos.get(dataDto.getContractId());
            if (null != contract) {
                if (contract.getType().equals(ContractType.OUTER_MARKET.getCode())) {
                    dataDto.setSignSubjectId(contract.getSignSubjectId());
                } else {
                    dataDto.setSignSubjectId(-1);
                }
            }


            dataDto.setIsChecking(interlayerCalculateDto.getIsChecking());
            dataDto.setClosingDate(interlayerCalculateDto.getClosingDate());

            // 主站up主mid和昵称
            if (CommonConstants.NEED_QUERY_NICK_NAME_ORDER_TYPES.contains(interlayerCalculateDto.getOrderType())) {
                setBilibiliUserInfo(orderMapInCrmOrderIds, biliUserInfosByMids, interlayerCalculateDto, dataDto);
            }

            //账单来源
            dataDto.setBillSource(interlayerCalculateDto.getBillSource());

            ContractDto contractDto = idMapContractDtos.get(interlayerCalculateDto.getContractId());
            if (contractDto != null) {
                dataDto.setAccountId(contractDto.getAccountId());
                dataDto.setContractName(contractDto.getName());
                dataDto.setContractNumber(contractDto.getContractNumber());
                //合同责任PM
                if (Utils.isPositive(contractDto.getExecuteId()) && null != idMapUser.get(contractDto.getExecuteId())) {
                    dataDto.setExecuteName(idMapUser.get(contractDto.getExecuteId()).getUsername());
                }

                AccountBaseDto account = idMapAccount.get(contractDto.getAccountId());
                if (account != null) {
                    dataDto.setCustomerName(account.getName());
                    String departName = accId2DepartNameMap.get(account.getAccountId());
                    dataDto.setDepartmentName(departName);
                    dataDto.setIsInner(account.getIsInner());
                    dataDto.setUnitedFirstIndustry(industryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + account.getUnitedFirstIndustryId(), ""));
                    dataDto.setUnitedSecondIndustry(industryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + account.getUnitedSecondIndustryId(), ""));
                    dataDto.setUnitedThirdIndustry(industryMap.getOrDefault(IndustryTypeEnum.UNITED_INDUSTRY + "-" + account.getUnitedThirdIndustryId(), ""));
                }
                ContractAdvertiseAndAgentInfo contractAdvertiseAndAgentInfo = contractAdvertiseAndAgentInfoMap.get(contractDto.getId());
                if (contractAdvertiseAndAgentInfo != null) {
                    dataDto.setAdvertiseAccountId(contractAdvertiseAndAgentInfo.getAdvertiseAccountId());
                    dataDto.setAdvertiseAccountName(contractAdvertiseAndAgentInfo.getAdvertiseAccountName());
                    dataDto.setAdvertiseCustomerId(contractAdvertiseAndAgentInfo.getAdvertiseCustomerId());
                    dataDto.setAdvertiseCustomerName(contractAdvertiseAndAgentInfo.getAdvertiseCustomerName());
                    dataDto.setAgentAccountId(contractAdvertiseAndAgentInfo.getAgentAccountId());
                    dataDto.setAgentAccountName(contractAdvertiseAndAgentInfo.getAgentAccountName());
                    dataDto.setAgentCustomerId(contractAdvertiseAndAgentInfo.getAgentCustomerId());
                    dataDto.setAgentCustomerName(contractAdvertiseAndAgentInfo.getAgentCustomerName());
                }
            }

            OrderBaseWithProductCategoryDto orderDto = idMapOrder.get(interlayerCalculateDto.getOrderId());
            if (orderDto != null) {
                dataDto.setFirstCategoryProduct(orderDto.getFirstCategoryName());
                dataDto.setSecondCategoryProduct(orderDto.getSecondCategoryName());
                dataDto.setOrderStatusDesc(CrmOrderStatus.getByCode(orderDto.getStatus()).getDesc());
                dataDto.setOrderName(orderDto.getExplanation());
                dataDto.setOrderType(orderDto.getType());
                dataDto.setOrderTypeDesc(CrmOrderType.getByCode(orderDto.getType()).getDesc());
                dataDto.setProjectItemName(orderDto.getProjectItemName());

            }
            dataDto.setCompanySubject("幻电科技（上海）有限公司");
            // 根据订单id获取营销中心合同效果营销部的扣款记录
            spliceEffectMarketContractOrderDeduct(idMapAccount, dataDto, contractDto);
            list.add(dataDto);
        }
        return list;
    }

    /**
     * 根据订单id获取营销中心合同效果营销部的扣款记录
     *
     * @param idMapAccount
     * @param dataDto
     * @param contractDto
     */
    private void spliceEffectMarketContractOrderDeduct(Map<Integer, AccountBaseDto> idMapAccount, BrandMidlleDataDto dataDto, ContractDto contractDto) {
        if (CrmConstant.EFFECT_CONTRACT_NEED_TURN_IN_ORDER_TYPES.contains(dataDto.getOrderType())) {
            AccountBaseDto accountBaseDto = idMapAccount.get(contractDto.getAccountId());
            AccAccountPo accAccountPo =
                    AccAccountPo.builder().accountId(accountBaseDto.getAccountId()).departmentId(accountBaseDto.getDepartmentId()).build();
            // 是否是营销中心合同，效果营销部的
            EffectMarketContractQuerier.EffectMarketQuerierResult result = effectMarketQuerier.queryIfEffectMarketContract(contractDto.getType(), accAccountPo,
                    dataDto.getContractId());
            if (result.getIfEffectMarket()) {
                dataDto.setIsEffectMarketOrder(true);
                // 根据accountId和salesType,operateType获取
                BigDecimal totalDeductMoney = accountWalletLogQuerier.queryTotalDeductMoney(dataDto.getOrderId(), contractDto.getAccountId());
                BigDecimal totalCashRefundMoney = accountWalletLogQuerier.queryTotalCashRefundMoney(dataDto.getOrderId(), contractDto.getAccountId());
                dataDto.setTotalDeductMoney(totalDeductMoney);
                dataDto.setTotalCashRefundMoney(totalCashRefundMoney);
            } else {
                dataDto.setIsEffectMarketOrder(false);
            }
        } else {
            dataDto.setIsEffectMarketOrder(false);
        }
    }

    private void setBilibiliUserInfo(Map<Integer, OrderBaseDto> orderMapInCrmOrderIds, Map<Long, UserInfoDto> biliUserInfosByMids, InterlayerCalculateDto dto, BrandMidlleDataDto dataDto) {
        if (orderMapInCrmOrderIds == null || CollectionUtils.isEmpty(orderMapInCrmOrderIds.keySet())) {
            return;
        }
        if (biliUserInfosByMids == null || CollectionUtils.isEmpty(biliUserInfosByMids.keySet())) {
            return;
        }
        OrderBaseDto orderBaseDto = orderMapInCrmOrderIds.get(dto.getOrderId());
        if (orderBaseDto != null) {
            dataDto.setMid(orderBaseDto.getMid());
            //TODO MID CHECK
            UserInfoDto userInfoDto = biliUserInfosByMids.get(orderBaseDto.getMid());
            dataDto.setBilibiliUserName(userInfoDto != null ? userInfoDto.getName() : "");
        }
    }

    private Map<Integer, CategoryDto> getCategoryInfoFrom(Map<Integer, AccountBaseDto> idMapAccount) {
        List<Integer> cateIds = new ArrayList<>();
        for (Entry<Integer, AccountBaseDto> entry : idMapAccount.entrySet()) {
            if (entry.getValue().getCategoryFirstId() != null) {
                cateIds.add(entry.getValue().getCommerceCategoryFirstId());
            }
            if (entry.getValue().getCategorySecondId() != null) {
                cateIds.add(entry.getValue().getCommerceCategorySecondId());
            }
        }

        return commerceCenterCategoryService.getCategoryDtoInIds(cateIds);
    }

    /**
     * 根据部门信息，查询出对应的账号id，再查询出对应的合同id
     **/
    private List<Integer> getContractIdByDepartmentId(List<Integer> departmentIds, String username) {
        if (CollectionUtils.isNotEmpty(departmentIds)) {
            List<Integer> accountIds = queryAccountService.getAccountIdByDepartmentId(departmentIds);
            if (CollectionUtils.isNotEmpty(accountIds)) {
                List<ContractBaseDto> contractDtos = contractQueryService.queryContractBaseListOptimization(QueryContractParam.builder().accountIds(accountIds).userName(username).build());
                return contractDtos.stream().map(ContractBaseDto::getId).distinct().collect(Collectors.toList());
            }
            return Collections.emptyList();
        }
        return Collections.emptyList();
    }

    @Override
    public List<InterlayerCalculateDto> queryClosedContract(List<Integer> departmentIds, String username) {
        List<Integer> filterContractIds = getContractIdByDepartmentId(departmentIds, username);
        if (CollectionUtils.isEmpty(filterContractIds)) {
            return Collections.emptyList();
        }

        List<InterlayerCalculateDto> interlayerCalculateDtos = getInterLayerCalculateDto(filterContractIds);
        //拆天账单明细中不包括实结部分的数据
        List<InterlayerCalculateDto> filterInterlayerCalculateDtos = interlayerCalculateDtos.stream().filter(s -> s.getBillSource() != 2).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterInterlayerCalculateDtos)) {
            return Collections.emptyList();
        }
        return filterInterlayerCalculateDtos;
    }

    private List<InterlayerCalculateDto> getInterLayerCalculateDto(List<Integer> contractIds) {
        InterlayerQueryDto dto = new InterlayerQueryDto();
        dto.setExecutedStatusList(Arrays.asList(0, 1));
        dto.setOrderStatusList(Arrays.asList(0, 1, 2));
        dto.setContractIds(contractIds);
        return interlayerMoneyService.queryInterlayerMoney(dto);
    }
}
