package com.bilibili.crm.platform.biz.wallet.dao;

import com.bilibili.crm.platform.biz.po.CrmAccCouponWalletPo;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/3/4
 */
public interface CrmAccCouponWalletDaoExt {

    @Select("<script>" + "select sum(coupon_value) as couponValue, project_item_id as projectItemId from crm_acc_coupon_wallet" +
            " where project_item_id in <foreach item = 'item' index = 'index' collection='projectIds' open='(' separator=',' close=')'> #{item} </foreach>" +
            "and biz_status in <foreach item = 'status' index = 'index' collection='bizStatusList' open='(' separator=',' close=')'> #{status} </foreach>" +
            " and coupon_value = coupon_value_balance_snap and biz_source = #{bizSource} group by project_item_id" +
            " </script>")
    List<CrmAccCouponWalletPo> queryUnusedSumMoney(@Param("projectIds") List<Integer> projectIds, @Param("bizSource") String bizSource,
                                                   @Param("bizStatusList") List<Integer> bizStatusList);
}
