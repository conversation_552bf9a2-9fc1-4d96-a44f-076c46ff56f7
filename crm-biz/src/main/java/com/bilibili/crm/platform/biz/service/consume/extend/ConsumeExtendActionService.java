package com.bilibili.crm.platform.biz.service.consume.extend;

import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.biz.service.consume.config.ConsumeExtendConfig;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.client.Client;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.reindex.BulkByScrollResponse;
import org.elasticsearch.index.reindex.UpdateByQueryAction;
import org.elasticsearch.index.reindex.UpdateByQueryRequestBuilder;
import org.elasticsearch.script.Script;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;

/**
 * @author: brady
 * @time: 2021/5/13 2:12 下午
 */
@Service
@Slf4j
public class ConsumeExtendActionService {
}
