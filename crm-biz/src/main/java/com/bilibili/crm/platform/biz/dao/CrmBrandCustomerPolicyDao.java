package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmBrandCustomerPolicyPo;
import com.bilibili.crm.platform.biz.po.CrmBrandCustomerPolicyPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmBrandCustomerPolicyDao {
    long countByExample(CrmBrandCustomerPolicyPoExample example);

    int deleteByExample(CrmBrandCustomerPolicyPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmBrandCustomerPolicyPo record);

    int insertBatch(List<CrmBrandCustomerPolicyPo> records);

    int insertUpdateBatch(List<CrmBrandCustomerPolicyPo> records);

    int insert(CrmBrandCustomerPolicyPo record);

    int insertUpdateSelective(CrmBrandCustomerPolicyPo record);

    int insertSelective(CrmBrandCustomerPolicyPo record);

    List<CrmBrandCustomerPolicyPo> selectByExample(CrmBrandCustomerPolicyPoExample example);

    CrmBrandCustomerPolicyPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmBrandCustomerPolicyPo record, @Param("example") CrmBrandCustomerPolicyPoExample example);

    int updateByExample(@Param("record") CrmBrandCustomerPolicyPo record, @Param("example") CrmBrandCustomerPolicyPoExample example);

    int updateByPrimaryKeySelective(CrmBrandCustomerPolicyPo record);

    int updateByPrimaryKey(CrmBrandCustomerPolicyPo record);
}