package com.bilibili.crm.platform.biz.service.finance.automation.component.recharge;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.NewRechargeDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CrmRevenueExpenditureFlowDto;
import com.bilibili.crm.platform.api.finance.service.IAccountFinanceService;
import com.bilibili.crm.platform.biz.mq.message.AutoRechargeCompleteMessage;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.po.CrmAccountRedPacketBackRecordPo;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.repo.finance.CrmAccountRedPacketBackRecordRepo;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.IsSupportPickUpEnum;
import com.bilibili.crm.platform.common.RechargeMode;
import com.bilibili.crm.platform.common.RechargeSource;
import com.bilibili.crm.platform.common.RechargeType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 自动充值处理器
 *
 * <AUTHOR>
 * @date 2021/1/25 3:35 下午
 */
@Slf4j
@Component
public class AutoRechargeProcessor {

    @Autowired
    private CrmAccountRedPacketBackRecordRepo crmAccountRedPacketBackRecordRepo;
    @Autowired
    private IAccountFinanceService accountFinanceService;
    @Autowired
    private AccAccountRepo accAccountRepo;

    /**
     * 自动充值
     * 自动生成充值记录
     *
     * @param receiveMoneyFlowDataDto
     */
    public void recharge(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto) {
        log.info("=====> recharge[auto], flowId:{}, amount:{}", receiveMoneyFlowDataDto.getId(),
                receiveMoneyFlowDataDto.getAmount());
        checkParams(receiveMoneyFlowDataDto);

        Operator operator = CrmUtils.newSystemOperator();
        // 自动生成充值记录并审核通过
        NewRechargeDto newRechargeDto = NewRechargeDto.builder()
                .amount(Utils.fromFenToYuan(receiveMoneyFlowDataDto.getAmount()))
                .accountId(receiveMoneyFlowDataDto.getBindAccountId())
                .source(RechargeSource.BANK_ENTERPRISE_DIRECT_CONNECT)
                .remark(receiveMoneyFlowDataDto.getRemark())
                .publicAccounts(receiveMoneyFlowDataDto.getPayAccountName())
                .rechargeDate(Utils.getNow())
                .mode(RechargeMode.AUTO.getCode())
                .flowId(receiveMoneyFlowDataDto.getId())
                .type(RechargeType.BANK.getCode())
                .build();
        // 生成自动充值记录
        Integer rechargeId = accountFinanceService.createRecharge(CrmUtils.newSystemOperator(), newRechargeDto);

        // 自动充值审核通过，该操作需要在创建自动返货后面，因为该操作内部会更新返货记录中的流水号来与充值记录关联起来
        if (rechargeId > 0) {
            accountFinanceService.rechargeAuditPass(operator, rechargeId);
        }

        // 返货自动审核通过
        CrmAccountRedPacketBackRecordPo redPacketBackRecordPo = crmAccountRedPacketBackRecordRepo.queryByRechargeId(rechargeId);
        if (redPacketBackRecordPo != null) {
            accountFinanceService.backRedPacketAuditPass(operator, redPacketBackRecordPo.getId());
        }

        // 对于花火的充值，发送充值消息给花火
        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(receiveMoneyFlowDataDto.getBindAccountId());
        if (accAccountPo == null) {
            return;
        }
        if (IsSupportPickUpEnum.YES.getCode().equals(accAccountPo.getIsSupportPickup())) {
            AutoRechargeCompleteMessage msg = new AutoRechargeCompleteMessage();
            msg.setRechargeId(rechargeId);
            msg.setAccountId(receiveMoneyFlowDataDto.getBindAccountId());
            msg.setAmount(receiveMoneyFlowDataDto.getAmount());
            // 无人使用
            //autoRechargeCompleteMsgSendHandler.convertAndSend(msg);
        }
    }

    private void checkParams(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto) {
        StringBuilder sbMsg = new StringBuilder();
        if (receiveMoneyFlowDataDto.getAmount() == null) {
            sbMsg.append("amount can't be null!");
        }
        if (!Utils.isPositive(receiveMoneyFlowDataDto.getId())) {
            sbMsg.append("flow id can't be null!");
        }
        if (!Utils.isPositive(receiveMoneyFlowDataDto.getBindAccountId())) {
            sbMsg.append("bind ref id[account id] can't be null!");
        }
        if (sbMsg.length() > 0) {
            throw new ServiceRuntimeException(sbMsg.toString());
        }
    }
}
