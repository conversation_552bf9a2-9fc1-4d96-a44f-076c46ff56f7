package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.NewToOldCategoryMappingPo;
import com.bilibili.crm.platform.biz.po.NewToOldCategoryMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface NewToOldCategoryMappingDao {
    long countByExample(NewToOldCategoryMappingPoExample example);

    int deleteByExample(NewToOldCategoryMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(NewToOldCategoryMappingPo record);

    int insertBatch(List<NewToOldCategoryMappingPo> records);

    int insertUpdateBatch(List<NewToOldCategoryMappingPo> records);

    int insert(NewToOldCategoryMappingPo record);

    int insertUpdateSelective(NewToOldCategoryMappingPo record);

    int insertSelective(NewToOldCategoryMappingPo record);

    List<NewToOldCategoryMappingPo> selectByExample(NewToOldCategoryMappingPoExample example);

    NewToOldCategoryMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") NewToOldCategoryMappingPo record, @Param("example") NewToOldCategoryMappingPoExample example);

    int updateByExample(@Param("record") NewToOldCategoryMappingPo record, @Param("example") NewToOldCategoryMappingPoExample example);

    int updateByPrimaryKeySelective(NewToOldCategoryMappingPo record);

    int updateByPrimaryKey(NewToOldCategoryMappingPo record);
}