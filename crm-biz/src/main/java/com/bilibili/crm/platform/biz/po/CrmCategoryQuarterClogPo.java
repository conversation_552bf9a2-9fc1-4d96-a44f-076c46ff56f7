package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmCategoryQuarterClogPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 季度
     */
    private String quarterName;

    /**
     * 集团ID
     */
    private Integer companyId;

    /**
     * 集团名称
     */
    private String companyName;

    /**
     * 一级行业ID
     */
    private Integer firstCategory;

    /**
     * 二级行业ID
     */
    private Integer secondCategory;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}