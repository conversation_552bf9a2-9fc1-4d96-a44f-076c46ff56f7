package com.bilibili.crm.platform.biz.service.return_online.finance_return;

import com.bilibili.adp.bfs.dto.BfsUploadResult;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.EmailScreenshotDto;
import com.bilibili.crm.platform.api.finance.dto.NewBackRedPacketDto;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentUploadDto;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowStatusEnum;
import com.bilibili.crm.platform.biz.Component.attachment.AttachmentUploadFacade;
import com.bilibili.crm.platform.biz.mq.databus.pub.DatabusPub;
import com.bilibili.crm.platform.biz.po.CrmReturnApplyAgentTaskRecordPo;
import com.bilibili.crm.platform.biz.po.CrmReturnApplyPo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyAgentTaskRecordRepo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyPolicyRelRepo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.backredpacket.AutoBackRedPacketProcessorForReturnOnline;
import com.bilibili.crm.platform.biz.service.return_online.EnterWechatMsgSender;
import com.bilibili.crm.platform.biz.service.return_online.data.FileUploader;
import com.bilibili.crm.platform.biz.service.return_online.data.ReturnExcelInputDataParser;
import com.bilibili.crm.platform.biz.service.return_online.dto.UploadFileDto;
import com.bilibili.crm.platform.common.AttachmentUploadBizModuleEnum;
import com.bilibili.crm.platform.common.CatOperateTypeConstants;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.io.InputStream;
import java.net.HttpURLConnection;
import java.net.URL;
import java.util.ArrayList;
import java.util.List;

/**
 * 财务返点任务处理
 *
 * <AUTHOR>
 * @date 2022/1/22 下午11:08
 */
@Slf4j
@Component
public class FinanceReturnTaskProcess {


    @Autowired
    private CrmReturnApplyRepo crmReturnApplyRepo;
    @Autowired
    private CrmReturnApplyPolicyRelRepo crmReturnApplyPolicyRelRepo;
    @Autowired
    private CrmReturnApplyAgentTaskRecordRepo crmReturnApplyAgentTaskRecordRepo;
    @Autowired
    private AutoBackRedPacketProcessorForReturnOnline autoBackRedPacketProcessorForReturnOnline;
    @Autowired
    private EnterWechatMsgSender enterWechatMsgSender;
    @Resource
    private DatabusPub databusPub;

    @Autowired
    private AttachmentUploadFacade attachmentUploadFacade;

    @Autowired
    private FileUploader fileUploader;
    @Autowired
    private ReturnExcelInputDataParser returnExcelInputDataParser;

    /**
     * 创建返点财务任务
     *
     * @param returnApplyId
     */
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void createFinanceReturnTask(Integer returnApplyId) {
        log.info("=====> createFinanceReturnTask[start] ..., applyId:{}", returnApplyId);

        CrmReturnApplyPo crmReturnApplyPo = crmReturnApplyRepo.queryById(returnApplyId);
        Assert.notNull(crmReturnApplyPo, "返货申请不存在！applyId:" + returnApplyId);

        // 审核完成后才允许自动创建返货
        if (!PolicyFlowStatusEnum.ADOPTED.getCode().equals(crmReturnApplyPo.getFlowStatus())) {
            throw new ServiceRuntimeException("流程审核完成后才允许创建返货任务！");
        }

        List<AttachmentResultDto> attachmentResultDtos = attachmentUploadFacade.queryAttachmentInfo(returnApplyId,
                AttachmentUploadBizModuleEnum.RETURN_FLOW_PIC);

        // 获取该返货申请需要创建的财务任务列表
        List<CrmReturnApplyAgentTaskRecordPo> applyAgentTaskRecordPos = crmReturnApplyAgentTaskRecordRepo.queryListByApplyId(returnApplyId);
        if (CollectionUtils.isEmpty(applyAgentTaskRecordPos)) {
            return;
        }

        Cat.logEvent(CatOperateTypeConstants.RETURN_APPLY, "createFinanceReturnTask", Event.SUCCESS,
                "size=" + applyAgentTaskRecordPos.size());
        List<NewBackRedPacketDto> backRedPacketDtos = new ArrayList<>();
        Integer autoBackRedPacketCount = 0;
        for (CrmReturnApplyAgentTaskRecordPo returnApplyAgentTaskRecordPo : applyAgentTaskRecordPos) {
            autoBackRedPacketCount++;
            List<EmailScreenshotDto> emailScreenshotDtos = new ArrayList<>();
            for (AttachmentResultDto attachmentResultDto : attachmentResultDtos) {

                // 创建特批返货任务
                EmailScreenshotDto emailScreenshotDto = new EmailScreenshotDto();
                emailScreenshotDto.setName(attachmentResultDto.getFileName());
                emailScreenshotDto.setUrl(attachmentResultDto.getUrl());
                emailScreenshotDtos.add(emailScreenshotDto);
            }

            // 发送消息
//                AgentAutoReturnAmountMessage msg = new AgentAutoReturnAmountMessage();
//                msg.setAccountId(totalExportDto.getAgentAccountId());
//                msg.setAmount(totalExportDto.getTotalReturnAmount());
//                msg.setBelongDate(crmReturnApplyPo.getBelongDate());
//                msg.setRemark(crmReturnApplyPo.getRemark());
//                agentAutoReturnAmountSendHandler.convertAndSend(msg);

            NewBackRedPacketDto backRedPacketDto = NewBackRedPacketDto.builder()
                    .accountId(returnApplyAgentTaskRecordPo.getAccountId())
                    // 要求是元
                    .amount(Utils.fromFenToYuan(returnApplyAgentTaskRecordPo.getCalReturnAmount()))
                    .belongDate(crmReturnApplyPo.getBelongDate())
                    .remark(crmReturnApplyPo.getRemark())
                    .emails(emailScreenshotDtos)
                    .build();

            Operator operator = new Operator(crmReturnApplyPo.getCreator(), OperatorType.SYSTEM);
            Integer financeTaskId = autoBackRedPacketProcessorForReturnOnline.createBackRedPacketForSystemCal(operator, backRedPacketDto);
            // 保存返货情况与 taskId 关系
            crmReturnApplyAgentTaskRecordRepo.updateFinanceTaskId(returnApplyAgentTaskRecordPo.getId(),
                    financeTaskId);
        }
        log.info("=====> createFinanceReturnTask[发送完成通知], applyId:{}", returnApplyId);
        enterWechatMsgSender.send("【返货财务任务执行完成】", String.format("返货财务任务执行完成, 返货申请id:%s, name:%s, 自动充值count:%s",
                returnApplyId, crmReturnApplyPo.getName(), autoBackRedPacketCount));
    }

    /**
     * 将 oss 文件上传到 bfs
     *
     * @param returnApplyId
     * @param attachmentResultDtos
     * @return
     */
    @Deprecated
    private List<EmailScreenshotDto> uploadFile2BfsFromOss(Integer returnApplyId, List<AttachmentResultDto> attachmentResultDtos) {
        // 获取附件上传到 bfs
        List<EmailScreenshotDto> emailScreenshotDtos = new ArrayList<>();
        for (AttachmentResultDto attachmentResultDto : attachmentResultDtos) {
            CommonAttachmentUploadDto commonAttachmentUploadDto = CommonAttachmentUploadDto.builder().build();
            commonAttachmentUploadDto.setUrl(attachmentResultDto.getUrl());
            commonAttachmentUploadDto.setOssKey(attachmentResultDto.getOssKey());
            // 过期则重生成 url
            returnExcelInputDataParser.updateUrlIfExpired(commonAttachmentUploadDto);

            EmailScreenshotDto emailScreenshotDto = new EmailScreenshotDto();
            BfsUploadResult bfsUploadResult = null;
            try {
                HttpURLConnection httpConn = null;
                URL urlObj = new URL(commonAttachmentUploadDto.getUrl());
                // 创建HttpURLConnection对象，通过这个对象打开跟远程服务器之间的连接
                httpConn = (HttpURLConnection) urlObj.openConnection();

                httpConn.setDoInput(true);
                httpConn.setRequestMethod("GET");
                httpConn.setConnectTimeout(5000);
                httpConn.connect();

                InputStream inputStream = httpConn.getInputStream();

                bfsUploadResult =
                        fileUploader.uploadWithRetry(UploadFileDto.builder().businessId(returnApplyId + "").resultFile(null).build());
                emailScreenshotDto.setName(attachmentResultDto.getFileName());
                emailScreenshotDto.setUrl(bfsUploadResult.getUrl());
                emailScreenshotDtos.add(emailScreenshotDto);
            } catch (Exception e) {
                log.error("=====> upload oss file to bfs fail, e:{}", e);
            }
        }
        return emailScreenshotDtos;
    }
}
