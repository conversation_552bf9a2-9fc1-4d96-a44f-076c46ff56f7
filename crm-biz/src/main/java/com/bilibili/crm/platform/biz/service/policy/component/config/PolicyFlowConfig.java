package com.bilibili.crm.platform.biz.service.policy.component.config;

import com.bilibili.crm.platform.api.policy.enums.PolicyFlowUserTypeEnum;
import lombok.Getter;
import lombok.Setter;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/8/17 下午1:52
 */
@Getter
@Component
public class PolicyFlowConfig {

    @Autowired
    private Integer tenantId;

    @Value("${crm.portal.env}")
    private String env;

    /**
     * 通知人召集令小组的 role (只有单笔合同有)
     */
    @Value("${flow.roleId.notice.recruit}")
    private Integer recruitNoticeRoleId;

    /**
     * 合同政策流程管理员 (只有单笔合同有)
     */
    @Value("${flow.roleId.administrator:567}")
    private Integer administratorRoleId;

    /**
     * 品牌销售媒介运营 (只有单笔合同有)
     */
    @Value("${flow.roleId.brandSaleMedium:570}")
    private Integer brandSaleMediumRoleId;

    // ================

    /**
     * 默认的通知人
     */
    @Value("${flow.roleId.notice.default:583}")
    private Integer defaultNoticeRoleId;

    /**
     * 默认的预审人
     */
    @Value("${flow.roleId.preExamine.default:582}")
    private Integer defaultPreExamineRoleId;

    /**
     * 合同政策预审审核人
     */
    @Value("${flow.roleId.preExamine:586}")
    private Integer preExamineRoleId;

    /**
     * 合同政策加签审核人
     */
    @Value("${flow.roleId.addSign:585}")
    private Integer addSignRoleId;

//    /**
//     * 合同政策知会人（废弃了，知会人是所有的crm用户）
//     */
//    @Value("${flow.roleId.notice:566}")
//    private Integer noticeRoleId;

    // =========== 共同的

    /**
     * 流程的开发模式：开发模式下只给 stableNoticeUser（固定的通知人）发消息
     */
    @Value("${flow.devModeSwitch:0}")
    @Setter
    public Integer devModeSwitch;

    /**
     * 固定的通知人（开发用）
     */
    @Value("#{'${users.policy.notice.stable:liuchunlong,wangjunfeng}'.split(',')}")
    public List<String> stableNoticeUser;

    /**
     * 消息接收白名单开关（主要用来控制测试环境是否发给开发者以外的人，开发自己用的）
     */
    @Value("${whiteList.policy.notice.switch:0}")
    @Setter
    public String policyNoticeWhiteListSwitch;

    /**
     * 消息接收白名单(已经废弃，配置到表里了)，主要用来预发的不用涉及到生产的真实用户
     */
    @Deprecated
    @Value("#{'${whiteList.policy.notice:sunyajun,moyizhi}'.split(',')}")
    public List<String> policyNoticeWhiteList;
    /**
     * 预发白名单配置
     */
    @Value("${policy.flow.whiteListSwitch:false}")
    @Setter
    private Boolean whiteListSwitch;

    /**
     * 根据用户类型获取 role id
     *
     * @param policyFlowUserTypeEnum
     * @return
     */
    public Integer getRoleIdByUserType(PolicyFlowUserTypeEnum policyFlowUserTypeEnum) {
        if (PolicyFlowUserTypeEnum.PRE_EXAMINE.equals(policyFlowUserTypeEnum)) {
            return preExamineRoleId;
        } else if (PolicyFlowUserTypeEnum.ADD_SIGN_EXAMINE.equals(policyFlowUserTypeEnum)) {
            return addSignRoleId;
        } else if (PolicyFlowUserTypeEnum.NOTICE_RECRUIT_GROUP.equals(policyFlowUserTypeEnum)) {
            return recruitNoticeRoleId;
        } else if (PolicyFlowUserTypeEnum.ADMINISTRATOR.equals(policyFlowUserTypeEnum)) {
            return administratorRoleId;
        } else if (PolicyFlowUserTypeEnum.BRAND_SALE_MEDIA_OPERATION.equals(policyFlowUserTypeEnum)) {
            return brandSaleMediumRoleId;
        }
        return 0;
    }
}
