package com.bilibili.crm.platform.biz.service.policy.component.suspend;

import org.flowable.engine.RuntimeService;
import org.flowable.engine.runtime.ProcessInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 流程挂起处理器
 *
 * <AUTHOR>
 * @date 2021/9/6 下午2:56
 */
@Component
public class ProcessInstanceSuspendProcessor {

    @Autowired
    protected RuntimeService runtimeService;

    /**
     * 判断是否挂起状态
     *
     * @param processInstanceId 流程实例id
     * @return
     */
    public boolean isSuspended(String processInstanceId) {
        boolean flag = true;
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();
        if (processInstance != null) {
            flag = !processInstance.isSuspended();
        }
        return flag;
    }
}
