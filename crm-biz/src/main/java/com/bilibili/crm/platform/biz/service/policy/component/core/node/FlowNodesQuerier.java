package com.bilibili.crm.platform.biz.service.policy.component.core.node;

import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.crm.platform.api.policy.dto.PolicyFlowNodeDto;
import com.bilibili.crm.platform.api.policy.enums.FlowTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowNodeTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowUserTypeEnum;
import com.bilibili.crm.platform.biz.dao.CrmBrandReturnDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.flowable.ActHiTaskInstRepo;
import com.bilibili.crm.platform.biz.repo.policy.CrmContractPolicyInfoRepo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyRepo;
import com.bilibili.crm.platform.biz.service.policy.component.core.FlowableBmpnModelProcessor;
import com.bilibili.crm.platform.biz.service.policy.component.core.querier.IRunTaskQuerier;
import com.bilibili.crm.platform.biz.service.policy.component.user.FlowUserQuerierFactory;
import com.bilibili.crm.platform.biz.service.policy.component.user.IFlowUserQuerier;
import com.bilibili.crm.platform.biz.service.policy.component.user.NowOperatorFlowUserQuerierImpl;
import com.bilibili.crm.platform.common.CatOperateTypeConstants;
import com.dianping.cat.Cat;
import com.site.lookup.util.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.engine.HistoryService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.variable.api.history.HistoricVariableInstance;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.*;
import java.util.stream.Collectors;

/**
 * 流程节点查询器
 *
 * <AUTHOR>
 * @date 2021/8/24 下午2:46
 */
@Component
public class FlowNodesQuerier {

    @Autowired
    private ProcessEngine processEngine;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    private TaskService taskService;
    @Autowired
    private HistoryService historyService;
    @Autowired
    private IRunTaskQuerier runTaskQuerier;
    @Autowired
    private FlowableBmpnModelProcessor flowableBmpnModelProcessor;
    @Autowired
    private FlowUserQuerierFactory flowUserQuerierFactory;
    @Autowired
    private CrmContractPolicyInfoRepo crmContractPolicyInfoRepo;
    @Autowired
    private CrmReturnApplyRepo crmReturnApplyRepo;
    @Autowired
    private ActHiTaskInstRepo actHiTaskInstRepo;
    @Autowired
    private CrmBrandReturnDao crmBrandReturnDao;
    @Autowired
    private NowOperatorFlowUserQuerierImpl nowOperatorFlowUserQuerier;

    /**
     * 查询某个流程实例的当前节点后面/前面的节点
     *
     * @param processInstanceId
     * @param findLaterTask
     * @return
     */
    public List<PolicyFlowNodeDto> queryNodesOfNow(Integer flowType, String processInstanceId, Boolean findLaterTask) {
        FlowTypeEnum flowTypeEnum = FlowTypeEnum.getByCode(flowType);
        //1.获取当前的流程实例
        Task runTask = runTaskQuerier.queryCurRunTask(processInstanceId);
        if (runTask == null) {
            throw new ServiceRuntimeException("当前不存在运行中的任务实例！");
        }
        Integer bizId = null;
        if (FlowTypeEnum.RETURN_POLICY_FLOW.equals(flowTypeEnum)) {
            CrmReturnApplyPo crmReturnApplyPo = crmReturnApplyRepo.queryByProcessInstanceId(processInstanceId);
            bizId = crmReturnApplyPo.getId();
        } else if(FlowTypeEnum.BRAND_RETURN.equals(flowTypeEnum)){
            CrmBrandReturnPoExample crmBrandReturnPoExample = new CrmBrandReturnPoExample();
            crmBrandReturnPoExample.createCriteria().andProcessInstanceIdEqualTo(processInstanceId);
            List<CrmBrandReturnPo> crmBrandReturnPos = crmBrandReturnDao.selectByExample(crmBrandReturnPoExample);
            Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(crmBrandReturnPos), "流程不存在");
            bizId = crmBrandReturnPos.get(0).getId().intValue();
        } else {
            CrmContractPolicyInfoPo contractPolicyInfoPo = crmContractPolicyInfoRepo.queryByProcessInstanceId(processInstanceId);
            bizId = contractPolicyInfoPo.getId();
        }
        ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(processInstanceId).singleResult();

        //4. 获取bpmnModel对象
        BpmnModel bpmnModel = flowableBmpnModelProcessor.getBpmnModelByProcessDefId(processInstance.getProcessDefinitionId());
        Process process = bpmnModel.getProcesses().get(0);
        Collection<FlowElement> flowElements = process.getFlowElements();

        // 获取 sequence flow 和 user task
        List<SequenceFlow> sequenceFlows = new ArrayList<>();
        List<UserTask> userTasks = new ArrayList<>();
        for (FlowElement tmpElement1 : flowElements) {
            if (tmpElement1 instanceof UserTask) {
                userTasks.add((UserTask) tmpElement1);
            }
            if (tmpElement1 instanceof SequenceFlow) {
                sequenceFlows.add((SequenceFlow) tmpElement1);
            }
        }

        // 对任务节点进行排序

        // sequenceFlows 找出第一个
        List<SequenceFlow> sortSequenceFlows = new ArrayList<>();
        List<UserTask> sortUserTasks = new ArrayList<>();

        // 遍历依次找下一个
        String needFindName = PolicyFlowNodeTypeEnum.START.getName();
        if(FlowTypeEnum.BRAND_RETURN.equals(flowTypeEnum)){
            needFindName = "start";
        }
        for (FlowElement tmpElement1 : flowElements) {
            String finalNeedFindName = needFindName;
            Optional<SequenceFlow> firstOptional = sequenceFlows.stream().filter(t -> t.getSourceRef().equals(finalNeedFindName)).findFirst();
            SequenceFlow firstSeqFlow = firstOptional.get();
            sortSequenceFlows.add(firstSeqFlow);

            if (PolicyFlowNodeTypeEnum.END.getName().equals(firstSeqFlow.getTargetRef())) {
                break;
            }

            if (FlowTypeEnum.BRAND_RETURN.equals(flowTypeEnum) && "end".equals(firstSeqFlow.getTargetRef())) {
                break;
            }

            UserTask targetUserTask = (UserTask) firstSeqFlow.getTargetFlowElement();
            sortUserTasks.add(targetUserTask);
            needFindName = targetUserTask.getId();
        }


        Boolean findCurTask = false;
        // 从有序的任务节点里，找出之前/ 之后的任务节点
        List<UserTask> finalUserTasks = new ArrayList<>();
        for (UserTask userTask : sortUserTasks) {
            if (runTask.getTaskDefinitionKey().equals(userTask.getId())) {
                findCurTask = true;
                continue;
            }
            // 寻找之后的任务节点
            if (findLaterTask) {
                if (findCurTask) {
                    finalUserTasks.add(userTask);
                }
            } else {
                // 寻找之前的任务节点
                if (!findCurTask) {
                    finalUserTasks.add(userTask);
                }
            }
        }

//        String sql = "select t.* from ACT_RU_ACTINST t where t.ACT_TYPE_ = 'userTask' " +
//                " and t.PROC_INST_ID_=#{processInstanceId} order by START_TIME_ desc";
//        List<ActivityInstance> activityInstances = processEngine.getRuntimeService().createNativeActivityInstanceQuery().sql(sql)
//                .parameter("processInstanceId", processInstanceId)
//                .list();

        List<ACTHITASKINSTPo> acthitaskinstPos = actHiTaskInstRepo.queryActHiTaskInstPos(processInstanceId);

        // 按 activity name 分组
//        Map<String, List<ActivityInstance>> actInstMap =
//                activityInstances.stream().collect(Collectors.groupingBy(t -> t.getActivityId()));
        Map<String, List<ACTHITASKINSTPo>> hiTaskMap =
                acthitaskinstPos.stream().collect(Collectors.groupingBy(t -> t.getTaskDefKey()));

        // 获取预选人员
        List<String> preExampleUsers =
                flowUserQuerierFactory.getByFlowUserType(PolicyFlowUserTypeEnum.PRE_EXAMINE).queryUsers(flowTypeEnum, processInstanceId, null);

        List<HistoricVariableInstance> historyVariableInstances = processEngine.getHistoryService().createHistoricVariableInstanceQuery().processInstanceId(processInstanceId).list();
//        Map<String, HistoricVariableInstance> variableMap = historyVariableInstances.stream().collect(Collectors.toMap(t -> t.getVariableName(), t -> t, (t1, t2) -> t2));
        Map<String, List<HistoricVariableInstance>> variableMap = historyVariableInstances.stream().collect(Collectors.groupingBy(t -> t.getVariableName()));

        // 拼接其他字段
        List<PolicyFlowNodeDto> flowNodeInfoDtos = new ArrayList<>();
        for (UserTask userTask : finalUserTasks) {
            if (PolicyFlowNodeTypeEnum.TO_ARCHIVED.getName().equals(userTask.getName())) {
                continue;
            }

            PolicyFlowNodeDto flowNodeInfoDto = PolicyFlowNodeDto.builder().build();
            flowNodeInfoDto.setTaskNodeName(userTask.getName());
            flowNodeInfoDto.setTaskNameChinese(PolicyFlowNodeTypeEnum.getByName(userTask.getName()).getDesc());
            flowNodeInfoDto.setUserName(userTask.getAssignee());
            // 获取该 activity name 的所有的 user tasks
            List<ACTHITASKINSTPo> tmpActhitaskinstPos = hiTaskMap.get(userTask.getId());
            if (!CollectionUtils.isEmpty(tmpActhitaskinstPos)) {
                ACTHITASKINSTPo acthitaskinstPo = tmpActhitaskinstPos.get(0);
//                flowNodeInfoDto.setActId(acthitaskinstPo.getActivityId());
//                flowNodeInfoDto.setActType(acthitaskinstPo.getActivityType());
                flowNodeInfoDto.setTaskNodeId(acthitaskinstPo.getId());
                flowNodeInfoDto.setProcessInstanceId(acthitaskinstPo.getProcInstId());
                flowNodeInfoDto.setBizId(bizId);
                // 设置每个任务的操作人
                if (PolicyFlowNodeTypeEnum.PRE_EXAMINE.getName().equals(userTask.getName())) {
                    flowNodeInfoDto.setUserName(preExampleUsers.stream().distinct().collect(Collectors.joining(",")));
                } else {
                    if (StringUtils.isNotEmpty(userTask.getAssignee()) && userTask.getAssignee().contains(
                            "${")) {
                        // ${}
                        String key = userTask.getAssignee().substring(2, userTask.getAssignee().length() - 1);
//                        HistoricVariableInstance historicVariableInstance = variableMap.get(key);
//                        if (historicVariableInstance != null) {
//                            flowNodeInfoDto.setUserName((String) historicVariableInstance.getValue());
//                        }
                        List<HistoricVariableInstance> historicVariableInstances = variableMap.get(key);
                        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(historicVariableInstances)) {
                            flowNodeInfoDto.setUserName(String.join(",", historicVariableInstances.stream().map(r -> String.valueOf(r.getValue())).distinct().collect(Collectors.toList())));
                        }
                    }
                }
                flowNodeInfoDto.setStartTime(acthitaskinstPo.getStartTime());
                flowNodeInfoDto.setEndTime(acthitaskinstPo.getEndTime());
            }

            if (StringUtils.isNotEmpty(userTask.getAssignee()) && userTask.getAssignee().contains(
                    "${")) {
                // ${}
                String key = userTask.getAssignee().substring(2, userTask.getAssignee().length() - 1);
//                HistoricVariableInstance historicVariableInstance = variableMap.get(key);
//                if (historicVariableInstance != null) {
//                    if (historicVariableInstance.getValue() instanceof String) {
//                        flowNodeInfoDto.setUserName((String) historicVariableInstance.getValue());
//                    }
//                    if (historicVariableInstance.getValue() instanceof List) {
//                        flowNodeInfoDto.setUserName(String.join(",", (List) historicVariableInstance.getValue()));
//                    }
//                }

                List<HistoricVariableInstance> historicVariableInstances = variableMap.get(key);
                if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(historicVariableInstances)) {
                    flowNodeInfoDto.setUserName(String.join(",", historicVariableInstances.stream().map(r -> String.valueOf(r.getValue())).distinct().collect(Collectors.toList())));
                }
            }

            flowNodeInfoDtos.add(flowNodeInfoDto);
        }

        if (findLaterTask) {
            return flowNodeInfoDtos;
        }
        return flowNodeInfoDtos.stream().filter(t -> StringUtils.isNotEmpty(t.getTaskNodeId())).collect(Collectors.toList());
    }

    public PolicyFlowNodeDto queryPolicyProcessNodeInfoById(Integer flowType, String processInstanceId) {
        Cat.logEvent(CatOperateTypeConstants.RETURN_APPLY, "queryPolicyProcessNodeInfoById");
        FlowTypeEnum flowTypeEnum = FlowTypeEnum.getByCode(flowType);
        Task runTask = runTaskQuerier.queryCurRunTask(processInstanceId);
        if (runTask == null) {
            return null;
        }
        Integer bizId = null;
        if (FlowTypeEnum.CONTRACT_FLOW.equals(flowTypeEnum)) {
            CrmContractPolicyInfoPo contractPolicyInfoPo = crmContractPolicyInfoRepo.queryByProcessInstanceId(runTask.getProcessInstanceId());
            bizId = contractPolicyInfoPo.getId();
        } else if (FlowTypeEnum.BRAND_RETURN.equals(flowTypeEnum)) {
            CrmBrandReturnPoExample crmBrandReturnPoExample = new CrmBrandReturnPoExample();
            crmBrandReturnPoExample.createCriteria().andProcessInstanceIdEqualTo(processInstanceId);
            List<CrmBrandReturnPo> crmBrandReturnPos = crmBrandReturnDao.selectByExample(crmBrandReturnPoExample);
            Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(crmBrandReturnPos), "流程不存在");
            bizId = crmBrandReturnPos.get(0).getId().intValue();
        } else {
            CrmReturnApplyPo crmReturnApplyPo = crmReturnApplyRepo.queryByProcessInstanceId(runTask.getProcessInstanceId());
            bizId = crmReturnApplyPo.getId();
        }

        String sql = "select t.* from ACT_RU_ACTINST t where t.ACT_TYPE_ = 'userTask' " +
                " and t.PROC_INST_ID_=#{processInstanceId} and t.TASK_ID_=#{taskId}";
        ActivityInstance runActInst = processEngine.getRuntimeService().createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("taskId", runTask.getId())
                .singleResult();

        PolicyFlowNodeDto policyFlowNodeDto = PolicyFlowNodeDto.builder().build();
        policyFlowNodeDto.setActId(runActInst.getActivityId());
        policyFlowNodeDto.setActType(runActInst.getActivityType());
        policyFlowNodeDto.setTaskNodeId(runTask.getId());
        policyFlowNodeDto.setTaskNodeName(runTask.getName());
        policyFlowNodeDto.setTaskNameChinese(PolicyFlowNodeTypeEnum.getByName(runTask.getName()).getDesc());
        policyFlowNodeDto.setProcessInstanceId(runTask.getProcessInstanceId());
        policyFlowNodeDto.setBizId(bizId);
//        policyFlowNodeDto.setUserName(runTask.getAssignee());
        policyFlowNodeDto.setUserName(String.join(",", nowOperatorFlowUserQuerier.queryUsers(flowTypeEnum, processInstanceId, null)));
        policyFlowNodeDto.setStartTime(runTask.getCreateTime());

        List<String> preExampleUsers =
                flowUserQuerierFactory.getByFlowUserType(PolicyFlowUserTypeEnum.PRE_EXAMINE).queryUsers(flowTypeEnum,
                        processInstanceId, null);
        policyFlowNodeDto.setPreExampleUsers(preExampleUsers);

        List<String> noticeUsers =
                flowUserQuerierFactory.getByFlowUserType(PolicyFlowUserTypeEnum.NOTICE).queryUsers(flowTypeEnum,
                        processInstanceId, null);
        policyFlowNodeDto.setNoticeUsers(noticeUsers);

        // 预审节点的用户获取
        if (PolicyFlowNodeTypeEnum.PRE_EXAMINE.getName().equals(runTask.getName())) {
            policyFlowNodeDto.setUserName(preExampleUsers.stream().collect(Collectors.joining(",")));
        }

        // 获取后续的节点
        policyFlowNodeDto.setLaterNodeDtos(this.queryNodesOfNow(flowType, runTask.getProcessInstanceId(), true));
        // 获取前面已经完成的节点
        policyFlowNodeDto.setFrontNodeDtos(this.queryNodesOfNow(flowType, runTask.getProcessInstanceId(),
                false));
        return policyFlowNodeDto;
    }
}
