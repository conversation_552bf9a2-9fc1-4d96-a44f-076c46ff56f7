package com.bilibili.crm.platform.biz.service.policy.component.listener;

import lombok.extern.slf4j.Slf4j;
import org.flowable.engine.delegate.DelegateExecution;
import org.flowable.engine.delegate.ExecutionListener;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/8/15 上午1:52
 */
@Component
@Slf4j
public class BrandReturnStartListener implements ExecutionListener {
    @Override
    public void notify(DelegateExecution execution) {
        log.info("=====> flow start, cur activity id:{}, processInstanceId:{}, eventName:{}",
                execution.getCurrentActivityId(), execution.getProcessInstanceId(), execution.getEventName());
    }
}
