package com.bilibili.crm.platform.biz.po;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CrmContractPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CrmContractPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andContractNumberIsNull() {
            addCriterion("contract_number is null");
            return (Criteria) this;
        }

        public Criteria andContractNumberIsNotNull() {
            addCriterion("contract_number is not null");
            return (Criteria) this;
        }

        public Criteria andContractNumberEqualTo(Long value) {
            addCriterion("contract_number =", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotEqualTo(Long value) {
            addCriterion("contract_number <>", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberGreaterThan(Long value) {
            addCriterion("contract_number >", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberGreaterThanOrEqualTo(Long value) {
            addCriterion("contract_number >=", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberLessThan(Long value) {
            addCriterion("contract_number <", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberLessThanOrEqualTo(Long value) {
            addCriterion("contract_number <=", value, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberIn(List<Long> values) {
            addCriterion("contract_number in", values, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotIn(List<Long> values) {
            addCriterion("contract_number not in", values, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberBetween(Long value1, Long value2) {
            addCriterion("contract_number between", value1, value2, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andContractNumberNotBetween(Long value1, Long value2) {
            addCriterion("contract_number not between", value1, value2, "contractNumber");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNull() {
            addCriterion("account_id is null");
            return (Criteria) this;
        }

        public Criteria andAccountIdIsNotNull() {
            addCriterion("account_id is not null");
            return (Criteria) this;
        }

        public Criteria andAccountIdEqualTo(Integer value) {
            addCriterion("account_id =", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotEqualTo(Integer value) {
            addCriterion("account_id <>", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThan(Integer value) {
            addCriterion("account_id >", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("account_id >=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThan(Integer value) {
            addCriterion("account_id <", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdLessThanOrEqualTo(Integer value) {
            addCriterion("account_id <=", value, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdIn(List<Integer> values) {
            addCriterion("account_id in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotIn(List<Integer> values) {
            addCriterion("account_id not in", values, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdBetween(Integer value1, Integer value2) {
            addCriterion("account_id between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andAccountIdNotBetween(Integer value1, Integer value2) {
            addCriterion("account_id not between", value1, value2, "accountId");
            return (Criteria) this;
        }

        public Criteria andNameIsNull() {
            addCriterion("name is null");
            return (Criteria) this;
        }

        public Criteria andNameIsNotNull() {
            addCriterion("name is not null");
            return (Criteria) this;
        }

        public Criteria andNameEqualTo(String value) {
            addCriterion("name =", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotEqualTo(String value) {
            addCriterion("name <>", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThan(String value) {
            addCriterion("name >", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameGreaterThanOrEqualTo(String value) {
            addCriterion("name >=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThan(String value) {
            addCriterion("name <", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLessThanOrEqualTo(String value) {
            addCriterion("name <=", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameLike(String value) {
            addCriterion("name like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotLike(String value) {
            addCriterion("name not like", value, "name");
            return (Criteria) this;
        }

        public Criteria andNameIn(List<String> values) {
            addCriterion("name in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotIn(List<String> values) {
            addCriterion("name not in", values, "name");
            return (Criteria) this;
        }

        public Criteria andNameBetween(String value1, String value2) {
            addCriterion("name between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andNameNotBetween(String value1, String value2) {
            addCriterion("name not between", value1, value2, "name");
            return (Criteria) this;
        }

        public Criteria andTypeIsNull() {
            addCriterion("type is null");
            return (Criteria) this;
        }

        public Criteria andTypeIsNotNull() {
            addCriterion("type is not null");
            return (Criteria) this;
        }

        public Criteria andTypeEqualTo(Integer value) {
            addCriterion("type =", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotEqualTo(Integer value) {
            addCriterion("type <>", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThan(Integer value) {
            addCriterion("type >", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("type >=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThan(Integer value) {
            addCriterion("type <", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeLessThanOrEqualTo(Integer value) {
            addCriterion("type <=", value, "type");
            return (Criteria) this;
        }

        public Criteria andTypeIn(List<Integer> values) {
            addCriterion("type in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotIn(List<Integer> values) {
            addCriterion("type not in", values, "type");
            return (Criteria) this;
        }

        public Criteria andTypeBetween(Integer value1, Integer value2) {
            addCriterion("type between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("type not between", value1, value2, "type");
            return (Criteria) this;
        }

        public Criteria andAgentIdIsNull() {
            addCriterion("agent_id is null");
            return (Criteria) this;
        }

        public Criteria andAgentIdIsNotNull() {
            addCriterion("agent_id is not null");
            return (Criteria) this;
        }

        public Criteria andAgentIdEqualTo(Integer value) {
            addCriterion("agent_id =", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotEqualTo(Integer value) {
            addCriterion("agent_id <>", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdGreaterThan(Integer value) {
            addCriterion("agent_id >", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("agent_id >=", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdLessThan(Integer value) {
            addCriterion("agent_id <", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdLessThanOrEqualTo(Integer value) {
            addCriterion("agent_id <=", value, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdIn(List<Integer> values) {
            addCriterion("agent_id in", values, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotIn(List<Integer> values) {
            addCriterion("agent_id not in", values, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdBetween(Integer value1, Integer value2) {
            addCriterion("agent_id between", value1, value2, "agentId");
            return (Criteria) this;
        }

        public Criteria andAgentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("agent_id not between", value1, value2, "agentId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdIsNull() {
            addCriterion("legal_contract_id is null");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdIsNotNull() {
            addCriterion("legal_contract_id is not null");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdEqualTo(String value) {
            addCriterion("legal_contract_id =", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdNotEqualTo(String value) {
            addCriterion("legal_contract_id <>", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdGreaterThan(String value) {
            addCriterion("legal_contract_id >", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdGreaterThanOrEqualTo(String value) {
            addCriterion("legal_contract_id >=", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdLessThan(String value) {
            addCriterion("legal_contract_id <", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdLessThanOrEqualTo(String value) {
            addCriterion("legal_contract_id <=", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdLike(String value) {
            addCriterion("legal_contract_id like", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdNotLike(String value) {
            addCriterion("legal_contract_id not like", value, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdIn(List<String> values) {
            addCriterion("legal_contract_id in", values, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdNotIn(List<String> values) {
            addCriterion("legal_contract_id not in", values, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdBetween(String value1, String value2) {
            addCriterion("legal_contract_id between", value1, value2, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andLegalContractIdNotBetween(String value1, String value2) {
            addCriterion("legal_contract_id not between", value1, value2, "legalContractId");
            return (Criteria) this;
        }

        public Criteria andBeginTimeIsNull() {
            addCriterion("begin_time is null");
            return (Criteria) this;
        }

        public Criteria andBeginTimeIsNotNull() {
            addCriterion("begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andBeginTimeEqualTo(Timestamp value) {
            addCriterion("begin_time =", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeNotEqualTo(Timestamp value) {
            addCriterion("begin_time <>", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeGreaterThan(Timestamp value) {
            addCriterion("begin_time >", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("begin_time >=", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeLessThan(Timestamp value) {
            addCriterion("begin_time <", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("begin_time <=", value, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeIn(List<Timestamp> values) {
            addCriterion("begin_time in", values, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeNotIn(List<Timestamp> values) {
            addCriterion("begin_time not in", values, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("begin_time between", value1, value2, "beginTime");
            return (Criteria) this;
        }

        public Criteria andBeginTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("begin_time not between", value1, value2, "beginTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNull() {
            addCriterion("end_time is null");
            return (Criteria) this;
        }

        public Criteria andEndTimeIsNotNull() {
            addCriterion("end_time is not null");
            return (Criteria) this;
        }

        public Criteria andEndTimeEqualTo(Timestamp value) {
            addCriterion("end_time =", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotEqualTo(Timestamp value) {
            addCriterion("end_time <>", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThan(Timestamp value) {
            addCriterion("end_time >", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("end_time >=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThan(Timestamp value) {
            addCriterion("end_time <", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("end_time <=", value, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeIn(List<Timestamp> values) {
            addCriterion("end_time in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotIn(List<Timestamp> values) {
            addCriterion("end_time not in", values, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_time between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("end_time not between", value1, value2, "endTime");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusIsNull() {
            addCriterion("archive_status is null");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusIsNotNull() {
            addCriterion("archive_status is not null");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusEqualTo(Integer value) {
            addCriterion("archive_status =", value, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusNotEqualTo(Integer value) {
            addCriterion("archive_status <>", value, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusGreaterThan(Integer value) {
            addCriterion("archive_status >", value, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("archive_status >=", value, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusLessThan(Integer value) {
            addCriterion("archive_status <", value, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusLessThanOrEqualTo(Integer value) {
            addCriterion("archive_status <=", value, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusIn(List<Integer> values) {
            addCriterion("archive_status in", values, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusNotIn(List<Integer> values) {
            addCriterion("archive_status not in", values, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusBetween(Integer value1, Integer value2) {
            addCriterion("archive_status between", value1, value2, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andArchiveStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("archive_status not between", value1, value2, "archiveStatus");
            return (Criteria) this;
        }

        public Criteria andStatusIsNull() {
            addCriterion("status is null");
            return (Criteria) this;
        }

        public Criteria andStatusIsNotNull() {
            addCriterion("status is not null");
            return (Criteria) this;
        }

        public Criteria andStatusEqualTo(Integer value) {
            addCriterion("status =", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotEqualTo(Integer value) {
            addCriterion("status <>", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThan(Integer value) {
            addCriterion("status >", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("status >=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThan(Integer value) {
            addCriterion("status <", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusLessThanOrEqualTo(Integer value) {
            addCriterion("status <=", value, "status");
            return (Criteria) this;
        }

        public Criteria andStatusIn(List<Integer> values) {
            addCriterion("status in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotIn(List<Integer> values) {
            addCriterion("status not in", values, "status");
            return (Criteria) this;
        }

        public Criteria andStatusBetween(Integer value1, Integer value2) {
            addCriterion("status between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("status not between", value1, value2, "status");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNull() {
            addCriterion("remark is null");
            return (Criteria) this;
        }

        public Criteria andRemarkIsNotNull() {
            addCriterion("remark is not null");
            return (Criteria) this;
        }

        public Criteria andRemarkEqualTo(String value) {
            addCriterion("remark =", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotEqualTo(String value) {
            addCriterion("remark <>", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThan(String value) {
            addCriterion("remark >", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("remark >=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThan(String value) {
            addCriterion("remark <", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLessThanOrEqualTo(String value) {
            addCriterion("remark <=", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkLike(String value) {
            addCriterion("remark like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotLike(String value) {
            addCriterion("remark not like", value, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkIn(List<String> values) {
            addCriterion("remark in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotIn(List<String> values) {
            addCriterion("remark not in", values, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkBetween(String value1, String value2) {
            addCriterion("remark between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andRemarkNotBetween(String value1, String value2) {
            addCriterion("remark not between", value1, value2, "remark");
            return (Criteria) this;
        }

        public Criteria andAmountIsNull() {
            addCriterion("amount is null");
            return (Criteria) this;
        }

        public Criteria andAmountIsNotNull() {
            addCriterion("amount is not null");
            return (Criteria) this;
        }

        public Criteria andAmountEqualTo(Long value) {
            addCriterion("amount =", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotEqualTo(Long value) {
            addCriterion("amount <>", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThan(Long value) {
            addCriterion("amount >", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("amount >=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThan(Long value) {
            addCriterion("amount <", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountLessThanOrEqualTo(Long value) {
            addCriterion("amount <=", value, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountIn(List<Long> values) {
            addCriterion("amount in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotIn(List<Long> values) {
            addCriterion("amount not in", values, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountBetween(Long value1, Long value2) {
            addCriterion("amount between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andAmountNotBetween(Long value1, Long value2) {
            addCriterion("amount not between", value1, value2, "amount");
            return (Criteria) this;
        }

        public Criteria andPdAmountIsNull() {
            addCriterion("pd_amount is null");
            return (Criteria) this;
        }

        public Criteria andPdAmountIsNotNull() {
            addCriterion("pd_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPdAmountEqualTo(Long value) {
            addCriterion("pd_amount =", value, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountNotEqualTo(Long value) {
            addCriterion("pd_amount <>", value, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountGreaterThan(Long value) {
            addCriterion("pd_amount >", value, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pd_amount >=", value, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountLessThan(Long value) {
            addCriterion("pd_amount <", value, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountLessThanOrEqualTo(Long value) {
            addCriterion("pd_amount <=", value, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountIn(List<Long> values) {
            addCriterion("pd_amount in", values, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountNotIn(List<Long> values) {
            addCriterion("pd_amount not in", values, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountBetween(Long value1, Long value2) {
            addCriterion("pd_amount between", value1, value2, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPdAmountNotBetween(Long value1, Long value2) {
            addCriterion("pd_amount not between", value1, value2, "pdAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountIsNull() {
            addCriterion("package_amount is null");
            return (Criteria) this;
        }

        public Criteria andPackageAmountIsNotNull() {
            addCriterion("package_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPackageAmountEqualTo(Long value) {
            addCriterion("package_amount =", value, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountNotEqualTo(Long value) {
            addCriterion("package_amount <>", value, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountGreaterThan(Long value) {
            addCriterion("package_amount >", value, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("package_amount >=", value, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountLessThan(Long value) {
            addCriterion("package_amount <", value, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountLessThanOrEqualTo(Long value) {
            addCriterion("package_amount <=", value, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountIn(List<Long> values) {
            addCriterion("package_amount in", values, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountNotIn(List<Long> values) {
            addCriterion("package_amount not in", values, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountBetween(Long value1, Long value2) {
            addCriterion("package_amount between", value1, value2, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andPackageAmountNotBetween(Long value1, Long value2) {
            addCriterion("package_amount not between", value1, value2, "packageAmount");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkIsNull() {
            addCriterion("amount_remark is null");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkIsNotNull() {
            addCriterion("amount_remark is not null");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkEqualTo(String value) {
            addCriterion("amount_remark =", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkNotEqualTo(String value) {
            addCriterion("amount_remark <>", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkGreaterThan(String value) {
            addCriterion("amount_remark >", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("amount_remark >=", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkLessThan(String value) {
            addCriterion("amount_remark <", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkLessThanOrEqualTo(String value) {
            addCriterion("amount_remark <=", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkLike(String value) {
            addCriterion("amount_remark like", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkNotLike(String value) {
            addCriterion("amount_remark not like", value, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkIn(List<String> values) {
            addCriterion("amount_remark in", values, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkNotIn(List<String> values) {
            addCriterion("amount_remark not in", values, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkBetween(String value1, String value2) {
            addCriterion("amount_remark between", value1, value2, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andAmountRemarkNotBetween(String value1, String value2) {
            addCriterion("amount_remark not between", value1, value2, "amountRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkIsNull() {
            addCriterion("review_remark is null");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkIsNotNull() {
            addCriterion("review_remark is not null");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkEqualTo(String value) {
            addCriterion("review_remark =", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotEqualTo(String value) {
            addCriterion("review_remark <>", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkGreaterThan(String value) {
            addCriterion("review_remark >", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkGreaterThanOrEqualTo(String value) {
            addCriterion("review_remark >=", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkLessThan(String value) {
            addCriterion("review_remark <", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkLessThanOrEqualTo(String value) {
            addCriterion("review_remark <=", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkLike(String value) {
            addCriterion("review_remark like", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotLike(String value) {
            addCriterion("review_remark not like", value, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkIn(List<String> values) {
            addCriterion("review_remark in", values, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotIn(List<String> values) {
            addCriterion("review_remark not in", values, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkBetween(String value1, String value2) {
            addCriterion("review_remark between", value1, value2, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andReviewRemarkNotBetween(String value1, String value2) {
            addCriterion("review_remark not between", value1, value2, "reviewRemark");
            return (Criteria) this;
        }

        public Criteria andVersionIsNull() {
            addCriterion("version is null");
            return (Criteria) this;
        }

        public Criteria andVersionIsNotNull() {
            addCriterion("version is not null");
            return (Criteria) this;
        }

        public Criteria andVersionEqualTo(Integer value) {
            addCriterion("version =", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotEqualTo(Integer value) {
            addCriterion("version <>", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThan(Integer value) {
            addCriterion("version >", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionGreaterThanOrEqualTo(Integer value) {
            addCriterion("version >=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThan(Integer value) {
            addCriterion("version <", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionLessThanOrEqualTo(Integer value) {
            addCriterion("version <=", value, "version");
            return (Criteria) this;
        }

        public Criteria andVersionIn(List<Integer> values) {
            addCriterion("version in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotIn(List<Integer> values) {
            addCriterion("version not in", values, "version");
            return (Criteria) this;
        }

        public Criteria andVersionBetween(Integer value1, Integer value2) {
            addCriterion("version between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andVersionNotBetween(Integer value1, Integer value2) {
            addCriterion("version not between", value1, value2, "version");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdIsNull() {
            addCriterion("finance_title_agent_id is null");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdIsNotNull() {
            addCriterion("finance_title_agent_id is not null");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdEqualTo(Integer value) {
            addCriterion("finance_title_agent_id =", value, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdNotEqualTo(Integer value) {
            addCriterion("finance_title_agent_id <>", value, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdGreaterThan(Integer value) {
            addCriterion("finance_title_agent_id >", value, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("finance_title_agent_id >=", value, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdLessThan(Integer value) {
            addCriterion("finance_title_agent_id <", value, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdLessThanOrEqualTo(Integer value) {
            addCriterion("finance_title_agent_id <=", value, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdIn(List<Integer> values) {
            addCriterion("finance_title_agent_id in", values, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdNotIn(List<Integer> values) {
            addCriterion("finance_title_agent_id not in", values, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdBetween(Integer value1, Integer value2) {
            addCriterion("finance_title_agent_id between", value1, value2, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andFinanceTitleAgentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("finance_title_agent_id not between", value1, value2, "financeTitleAgentId");
            return (Criteria) this;
        }

        public Criteria andDistributionIsNull() {
            addCriterion("distribution is null");
            return (Criteria) this;
        }

        public Criteria andDistributionIsNotNull() {
            addCriterion("distribution is not null");
            return (Criteria) this;
        }

        public Criteria andDistributionEqualTo(Integer value) {
            addCriterion("distribution =", value, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionNotEqualTo(Integer value) {
            addCriterion("distribution <>", value, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionGreaterThan(Integer value) {
            addCriterion("distribution >", value, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionGreaterThanOrEqualTo(Integer value) {
            addCriterion("distribution >=", value, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionLessThan(Integer value) {
            addCriterion("distribution <", value, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionLessThanOrEqualTo(Integer value) {
            addCriterion("distribution <=", value, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionIn(List<Integer> values) {
            addCriterion("distribution in", values, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionNotIn(List<Integer> values) {
            addCriterion("distribution not in", values, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionBetween(Integer value1, Integer value2) {
            addCriterion("distribution between", value1, value2, "distribution");
            return (Criteria) this;
        }

        public Criteria andDistributionNotBetween(Integer value1, Integer value2) {
            addCriterion("distribution not between", value1, value2, "distribution");
            return (Criteria) this;
        }

        public Criteria andDiscountIsNull() {
            addCriterion("discount is null");
            return (Criteria) this;
        }

        public Criteria andDiscountIsNotNull() {
            addCriterion("discount is not null");
            return (Criteria) this;
        }

        public Criteria andDiscountEqualTo(BigDecimal value) {
            addCriterion("discount =", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountNotEqualTo(BigDecimal value) {
            addCriterion("discount <>", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountGreaterThan(BigDecimal value) {
            addCriterion("discount >", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountGreaterThanOrEqualTo(BigDecimal value) {
            addCriterion("discount >=", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountLessThan(BigDecimal value) {
            addCriterion("discount <", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountLessThanOrEqualTo(BigDecimal value) {
            addCriterion("discount <=", value, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountIn(List<BigDecimal> values) {
            addCriterion("discount in", values, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountNotIn(List<BigDecimal> values) {
            addCriterion("discount not in", values, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount between", value1, value2, "discount");
            return (Criteria) this;
        }

        public Criteria andDiscountNotBetween(BigDecimal value1, BigDecimal value2) {
            addCriterion("discount not between", value1, value2, "discount");
            return (Criteria) this;
        }

        public Criteria andQuotaIdIsNull() {
            addCriterion("quota_id is null");
            return (Criteria) this;
        }

        public Criteria andQuotaIdIsNotNull() {
            addCriterion("quota_id is not null");
            return (Criteria) this;
        }

        public Criteria andQuotaIdEqualTo(Integer value) {
            addCriterion("quota_id =", value, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdNotEqualTo(Integer value) {
            addCriterion("quota_id <>", value, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdGreaterThan(Integer value) {
            addCriterion("quota_id >", value, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("quota_id >=", value, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdLessThan(Integer value) {
            addCriterion("quota_id <", value, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdLessThanOrEqualTo(Integer value) {
            addCriterion("quota_id <=", value, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdIn(List<Integer> values) {
            addCriterion("quota_id in", values, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdNotIn(List<Integer> values) {
            addCriterion("quota_id not in", values, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdBetween(Integer value1, Integer value2) {
            addCriterion("quota_id between", value1, value2, "quotaId");
            return (Criteria) this;
        }

        public Criteria andQuotaIdNotBetween(Integer value1, Integer value2) {
            addCriterion("quota_id not between", value1, value2, "quotaId");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaIsNull() {
            addCriterion("deduct_quota is null");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaIsNotNull() {
            addCriterion("deduct_quota is not null");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaEqualTo(Long value) {
            addCriterion("deduct_quota =", value, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaNotEqualTo(Long value) {
            addCriterion("deduct_quota <>", value, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaGreaterThan(Long value) {
            addCriterion("deduct_quota >", value, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaGreaterThanOrEqualTo(Long value) {
            addCriterion("deduct_quota >=", value, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaLessThan(Long value) {
            addCriterion("deduct_quota <", value, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaLessThanOrEqualTo(Long value) {
            addCriterion("deduct_quota <=", value, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaIn(List<Long> values) {
            addCriterion("deduct_quota in", values, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaNotIn(List<Long> values) {
            addCriterion("deduct_quota not in", values, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaBetween(Long value1, Long value2) {
            addCriterion("deduct_quota between", value1, value2, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andDeductQuotaNotBetween(Long value1, Long value2) {
            addCriterion("deduct_quota not between", value1, value2, "deductQuota");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNull() {
            addCriterion("project_name is null");
            return (Criteria) this;
        }

        public Criteria andProjectNameIsNotNull() {
            addCriterion("project_name is not null");
            return (Criteria) this;
        }

        public Criteria andProjectNameEqualTo(String value) {
            addCriterion("project_name =", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotEqualTo(String value) {
            addCriterion("project_name <>", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThan(String value) {
            addCriterion("project_name >", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameGreaterThanOrEqualTo(String value) {
            addCriterion("project_name >=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThan(String value) {
            addCriterion("project_name <", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLessThanOrEqualTo(String value) {
            addCriterion("project_name <=", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameLike(String value) {
            addCriterion("project_name like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotLike(String value) {
            addCriterion("project_name not like", value, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameIn(List<String> values) {
            addCriterion("project_name in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotIn(List<String> values) {
            addCriterion("project_name not in", values, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameBetween(String value1, String value2) {
            addCriterion("project_name between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andProjectNameNotBetween(String value1, String value2) {
            addCriterion("project_name not between", value1, value2, "projectName");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeIsNull() {
            addCriterion("order_begin_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeIsNotNull() {
            addCriterion("order_begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeEqualTo(Timestamp value) {
            addCriterion("order_begin_time =", value, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeNotEqualTo(Timestamp value) {
            addCriterion("order_begin_time <>", value, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeGreaterThan(Timestamp value) {
            addCriterion("order_begin_time >", value, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("order_begin_time >=", value, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeLessThan(Timestamp value) {
            addCriterion("order_begin_time <", value, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("order_begin_time <=", value, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeIn(List<Timestamp> values) {
            addCriterion("order_begin_time in", values, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeNotIn(List<Timestamp> values) {
            addCriterion("order_begin_time not in", values, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("order_begin_time between", value1, value2, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderBeginTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("order_begin_time not between", value1, value2, "orderBeginTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeIsNull() {
            addCriterion("order_end_time is null");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeIsNotNull() {
            addCriterion("order_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeEqualTo(Timestamp value) {
            addCriterion("order_end_time =", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeNotEqualTo(Timestamp value) {
            addCriterion("order_end_time <>", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeGreaterThan(Timestamp value) {
            addCriterion("order_end_time >", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("order_end_time >=", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeLessThan(Timestamp value) {
            addCriterion("order_end_time <", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("order_end_time <=", value, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeIn(List<Timestamp> values) {
            addCriterion("order_end_time in", values, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeNotIn(List<Timestamp> values) {
            addCriterion("order_end_time not in", values, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("order_end_time between", value1, value2, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andOrderEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("order_end_time not between", value1, value2, "orderEndTime");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyIsNull() {
            addCriterion("promotion_policy is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyIsNotNull() {
            addCriterion("promotion_policy is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyEqualTo(Integer value) {
            addCriterion("promotion_policy =", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotEqualTo(Integer value) {
            addCriterion("promotion_policy <>", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyGreaterThan(Integer value) {
            addCriterion("promotion_policy >", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyGreaterThanOrEqualTo(Integer value) {
            addCriterion("promotion_policy >=", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyLessThan(Integer value) {
            addCriterion("promotion_policy <", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyLessThanOrEqualTo(Integer value) {
            addCriterion("promotion_policy <=", value, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyIn(List<Integer> values) {
            addCriterion("promotion_policy in", values, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotIn(List<Integer> values) {
            addCriterion("promotion_policy not in", values, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyBetween(Integer value1, Integer value2) {
            addCriterion("promotion_policy between", value1, value2, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyNotBetween(Integer value1, Integer value2) {
            addCriterion("promotion_policy not between", value1, value2, "promotionPolicy");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNull() {
            addCriterion("group_id is null");
            return (Criteria) this;
        }

        public Criteria andGroupIdIsNotNull() {
            addCriterion("group_id is not null");
            return (Criteria) this;
        }

        public Criteria andGroupIdEqualTo(Integer value) {
            addCriterion("group_id =", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotEqualTo(Integer value) {
            addCriterion("group_id <>", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThan(Integer value) {
            addCriterion("group_id >", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("group_id >=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThan(Integer value) {
            addCriterion("group_id <", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdLessThanOrEqualTo(Integer value) {
            addCriterion("group_id <=", value, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdIn(List<Integer> values) {
            addCriterion("group_id in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotIn(List<Integer> values) {
            addCriterion("group_id not in", values, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdBetween(Integer value1, Integer value2) {
            addCriterion("group_id between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andGroupIdNotBetween(Integer value1, Integer value2) {
            addCriterion("group_id not between", value1, value2, "groupId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdIsNull() {
            addCriterion("product_line_id is null");
            return (Criteria) this;
        }

        public Criteria andProductLineIdIsNotNull() {
            addCriterion("product_line_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductLineIdEqualTo(Integer value) {
            addCriterion("product_line_id =", value, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdNotEqualTo(Integer value) {
            addCriterion("product_line_id <>", value, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdGreaterThan(Integer value) {
            addCriterion("product_line_id >", value, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_line_id >=", value, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdLessThan(Integer value) {
            addCriterion("product_line_id <", value, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdLessThanOrEqualTo(Integer value) {
            addCriterion("product_line_id <=", value, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdIn(List<Integer> values) {
            addCriterion("product_line_id in", values, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdNotIn(List<Integer> values) {
            addCriterion("product_line_id not in", values, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdBetween(Integer value1, Integer value2) {
            addCriterion("product_line_id between", value1, value2, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductLineIdNotBetween(Integer value1, Integer value2) {
            addCriterion("product_line_id not between", value1, value2, "productLineId");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNull() {
            addCriterion("product_id is null");
            return (Criteria) this;
        }

        public Criteria andProductIdIsNotNull() {
            addCriterion("product_id is not null");
            return (Criteria) this;
        }

        public Criteria andProductIdEqualTo(Integer value) {
            addCriterion("product_id =", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotEqualTo(Integer value) {
            addCriterion("product_id <>", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThan(Integer value) {
            addCriterion("product_id >", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("product_id >=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThan(Integer value) {
            addCriterion("product_id <", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdLessThanOrEqualTo(Integer value) {
            addCriterion("product_id <=", value, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdIn(List<Integer> values) {
            addCriterion("product_id in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotIn(List<Integer> values) {
            addCriterion("product_id not in", values, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdBetween(Integer value1, Integer value2) {
            addCriterion("product_id between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andProductIdNotBetween(Integer value1, Integer value2) {
            addCriterion("product_id not between", value1, value2, "productId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNull() {
            addCriterion("execute_id is null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIsNotNull() {
            addCriterion("execute_id is not null");
            return (Criteria) this;
        }

        public Criteria andExecuteIdEqualTo(Integer value) {
            addCriterion("execute_id =", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotEqualTo(Integer value) {
            addCriterion("execute_id <>", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThan(Integer value) {
            addCriterion("execute_id >", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("execute_id >=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThan(Integer value) {
            addCriterion("execute_id <", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdLessThanOrEqualTo(Integer value) {
            addCriterion("execute_id <=", value, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdIn(List<Integer> values) {
            addCriterion("execute_id in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotIn(List<Integer> values) {
            addCriterion("execute_id not in", values, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdBetween(Integer value1, Integer value2) {
            addCriterion("execute_id between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andExecuteIdNotBetween(Integer value1, Integer value2) {
            addCriterion("execute_id not between", value1, value2, "executeId");
            return (Criteria) this;
        }

        public Criteria andBusStatusIsNull() {
            addCriterion("bus_status is null");
            return (Criteria) this;
        }

        public Criteria andBusStatusIsNotNull() {
            addCriterion("bus_status is not null");
            return (Criteria) this;
        }

        public Criteria andBusStatusEqualTo(Integer value) {
            addCriterion("bus_status =", value, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusNotEqualTo(Integer value) {
            addCriterion("bus_status <>", value, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusGreaterThan(Integer value) {
            addCriterion("bus_status >", value, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("bus_status >=", value, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusLessThan(Integer value) {
            addCriterion("bus_status <", value, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusLessThanOrEqualTo(Integer value) {
            addCriterion("bus_status <=", value, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusIn(List<Integer> values) {
            addCriterion("bus_status in", values, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusNotIn(List<Integer> values) {
            addCriterion("bus_status not in", values, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusBetween(Integer value1, Integer value2) {
            addCriterion("bus_status between", value1, value2, "busStatus");
            return (Criteria) this;
        }

        public Criteria andBusStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("bus_status not between", value1, value2, "busStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNull() {
            addCriterion("audit_status is null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIsNotNull() {
            addCriterion("audit_status is not null");
            return (Criteria) this;
        }

        public Criteria andAuditStatusEqualTo(Integer value) {
            addCriterion("audit_status =", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotEqualTo(Integer value) {
            addCriterion("audit_status <>", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThan(Integer value) {
            addCriterion("audit_status >", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusGreaterThanOrEqualTo(Integer value) {
            addCriterion("audit_status >=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThan(Integer value) {
            addCriterion("audit_status <", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusLessThanOrEqualTo(Integer value) {
            addCriterion("audit_status <=", value, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusIn(List<Integer> values) {
            addCriterion("audit_status in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotIn(List<Integer> values) {
            addCriterion("audit_status not in", values, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusBetween(Integer value1, Integer value2) {
            addCriterion("audit_status between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andAuditStatusNotBetween(Integer value1, Integer value2) {
            addCriterion("audit_status not between", value1, value2, "auditStatus");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdIsNull() {
            addCriterion("old_agent_id is null");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdIsNotNull() {
            addCriterion("old_agent_id is not null");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdEqualTo(Integer value) {
            addCriterion("old_agent_id =", value, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdNotEqualTo(Integer value) {
            addCriterion("old_agent_id <>", value, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdGreaterThan(Integer value) {
            addCriterion("old_agent_id >", value, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("old_agent_id >=", value, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdLessThan(Integer value) {
            addCriterion("old_agent_id <", value, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdLessThanOrEqualTo(Integer value) {
            addCriterion("old_agent_id <=", value, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdIn(List<Integer> values) {
            addCriterion("old_agent_id in", values, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdNotIn(List<Integer> values) {
            addCriterion("old_agent_id not in", values, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdBetween(Integer value1, Integer value2) {
            addCriterion("old_agent_id between", value1, value2, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andOldAgentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("old_agent_id not between", value1, value2, "oldAgentId");
            return (Criteria) this;
        }

        public Criteria andBillAmountIsNull() {
            addCriterion("bill_amount is null");
            return (Criteria) this;
        }

        public Criteria andBillAmountIsNotNull() {
            addCriterion("bill_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBillAmountEqualTo(Long value) {
            addCriterion("bill_amount =", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountNotEqualTo(Long value) {
            addCriterion("bill_amount <>", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountGreaterThan(Long value) {
            addCriterion("bill_amount >", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("bill_amount >=", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountLessThan(Long value) {
            addCriterion("bill_amount <", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountLessThanOrEqualTo(Long value) {
            addCriterion("bill_amount <=", value, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountIn(List<Long> values) {
            addCriterion("bill_amount in", values, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountNotIn(List<Long> values) {
            addCriterion("bill_amount not in", values, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountBetween(Long value1, Long value2) {
            addCriterion("bill_amount between", value1, value2, "billAmount");
            return (Criteria) this;
        }

        public Criteria andBillAmountNotBetween(Long value1, Long value2) {
            addCriterion("bill_amount not between", value1, value2, "billAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountIsNull() {
            addCriterion("total_claim_amount is null");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountIsNotNull() {
            addCriterion("total_claim_amount is not null");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountEqualTo(Long value) {
            addCriterion("total_claim_amount =", value, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountNotEqualTo(Long value) {
            addCriterion("total_claim_amount <>", value, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountGreaterThan(Long value) {
            addCriterion("total_claim_amount >", value, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("total_claim_amount >=", value, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountLessThan(Long value) {
            addCriterion("total_claim_amount <", value, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountLessThanOrEqualTo(Long value) {
            addCriterion("total_claim_amount <=", value, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountIn(List<Long> values) {
            addCriterion("total_claim_amount in", values, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountNotIn(List<Long> values) {
            addCriterion("total_claim_amount not in", values, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountBetween(Long value1, Long value2) {
            addCriterion("total_claim_amount between", value1, value2, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andTotalClaimAmountNotBetween(Long value1, Long value2) {
            addCriterion("total_claim_amount not between", value1, value2, "totalClaimAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountIsNull() {
            addCriterion("init_deducted_amount is null");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountIsNotNull() {
            addCriterion("init_deducted_amount is not null");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountEqualTo(Long value) {
            addCriterion("init_deducted_amount =", value, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountNotEqualTo(Long value) {
            addCriterion("init_deducted_amount <>", value, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountGreaterThan(Long value) {
            addCriterion("init_deducted_amount >", value, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("init_deducted_amount >=", value, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountLessThan(Long value) {
            addCriterion("init_deducted_amount <", value, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountLessThanOrEqualTo(Long value) {
            addCriterion("init_deducted_amount <=", value, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountIn(List<Long> values) {
            addCriterion("init_deducted_amount in", values, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountNotIn(List<Long> values) {
            addCriterion("init_deducted_amount not in", values, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountBetween(Long value1, Long value2) {
            addCriterion("init_deducted_amount between", value1, value2, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andInitDeductedAmountNotBetween(Long value1, Long value2) {
            addCriterion("init_deducted_amount not between", value1, value2, "initDeductedAmount");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillIsNull() {
            addCriterion("is_oa_open_bill is null");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillIsNotNull() {
            addCriterion("is_oa_open_bill is not null");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillEqualTo(Integer value) {
            addCriterion("is_oa_open_bill =", value, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillNotEqualTo(Integer value) {
            addCriterion("is_oa_open_bill <>", value, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillGreaterThan(Integer value) {
            addCriterion("is_oa_open_bill >", value, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_oa_open_bill >=", value, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillLessThan(Integer value) {
            addCriterion("is_oa_open_bill <", value, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillLessThanOrEqualTo(Integer value) {
            addCriterion("is_oa_open_bill <=", value, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillIn(List<Integer> values) {
            addCriterion("is_oa_open_bill in", values, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillNotIn(List<Integer> values) {
            addCriterion("is_oa_open_bill not in", values, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillBetween(Integer value1, Integer value2) {
            addCriterion("is_oa_open_bill between", value1, value2, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsOaOpenBillNotBetween(Integer value1, Integer value2) {
            addCriterion("is_oa_open_bill not between", value1, value2, "isOaOpenBill");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedIsNull() {
            addCriterion("is_deduct_completed is null");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedIsNotNull() {
            addCriterion("is_deduct_completed is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedEqualTo(Integer value) {
            addCriterion("is_deduct_completed =", value, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedNotEqualTo(Integer value) {
            addCriterion("is_deduct_completed <>", value, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedGreaterThan(Integer value) {
            addCriterion("is_deduct_completed >", value, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deduct_completed >=", value, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedLessThan(Integer value) {
            addCriterion("is_deduct_completed <", value, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deduct_completed <=", value, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedIn(List<Integer> values) {
            addCriterion("is_deduct_completed in", values, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedNotIn(List<Integer> values) {
            addCriterion("is_deduct_completed not in", values, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deduct_completed between", value1, value2, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andIsDeductCompletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deduct_completed not between", value1, value2, "isDeductCompleted");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountIsNull() {
            addCriterion("offline_bill_amount is null");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountIsNotNull() {
            addCriterion("offline_bill_amount is not null");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountEqualTo(Long value) {
            addCriterion("offline_bill_amount =", value, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountNotEqualTo(Long value) {
            addCriterion("offline_bill_amount <>", value, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountGreaterThan(Long value) {
            addCriterion("offline_bill_amount >", value, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("offline_bill_amount >=", value, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountLessThan(Long value) {
            addCriterion("offline_bill_amount <", value, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountLessThanOrEqualTo(Long value) {
            addCriterion("offline_bill_amount <=", value, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountIn(List<Long> values) {
            addCriterion("offline_bill_amount in", values, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountNotIn(List<Long> values) {
            addCriterion("offline_bill_amount not in", values, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountBetween(Long value1, Long value2) {
            addCriterion("offline_bill_amount between", value1, value2, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andOfflineBillAmountNotBetween(Long value1, Long value2) {
            addCriterion("offline_bill_amount not between", value1, value2, "offlineBillAmount");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdIsNull() {
            addCriterion("sign_subject_id is null");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdIsNotNull() {
            addCriterion("sign_subject_id is not null");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdEqualTo(Integer value) {
            addCriterion("sign_subject_id =", value, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdNotEqualTo(Integer value) {
            addCriterion("sign_subject_id <>", value, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdGreaterThan(Integer value) {
            addCriterion("sign_subject_id >", value, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("sign_subject_id >=", value, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdLessThan(Integer value) {
            addCriterion("sign_subject_id <", value, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdLessThanOrEqualTo(Integer value) {
            addCriterion("sign_subject_id <=", value, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdIn(List<Integer> values) {
            addCriterion("sign_subject_id in", values, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdNotIn(List<Integer> values) {
            addCriterion("sign_subject_id not in", values, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdBetween(Integer value1, Integer value2) {
            addCriterion("sign_subject_id between", value1, value2, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andSignSubjectIdNotBetween(Integer value1, Integer value2) {
            addCriterion("sign_subject_id not between", value1, value2, "signSubjectId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdIsNull() {
            addCriterion("project_item_id is null");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdIsNotNull() {
            addCriterion("project_item_id is not null");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdEqualTo(Integer value) {
            addCriterion("project_item_id =", value, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdNotEqualTo(Integer value) {
            addCriterion("project_item_id <>", value, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdGreaterThan(Integer value) {
            addCriterion("project_item_id >", value, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("project_item_id >=", value, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdLessThan(Integer value) {
            addCriterion("project_item_id <", value, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdLessThanOrEqualTo(Integer value) {
            addCriterion("project_item_id <=", value, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdIn(List<Integer> values) {
            addCriterion("project_item_id in", values, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdNotIn(List<Integer> values) {
            addCriterion("project_item_id not in", values, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdBetween(Integer value1, Integer value2) {
            addCriterion("project_item_id between", value1, value2, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andProjectItemIdNotBetween(Integer value1, Integer value2) {
            addCriterion("project_item_id not between", value1, value2, "projectItemId");
            return (Criteria) this;
        }

        public Criteria andTagIdIsNull() {
            addCriterion("tag_id is null");
            return (Criteria) this;
        }

        public Criteria andTagIdIsNotNull() {
            addCriterion("tag_id is not null");
            return (Criteria) this;
        }

        public Criteria andTagIdEqualTo(Long value) {
            addCriterion("tag_id =", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdNotEqualTo(Long value) {
            addCriterion("tag_id <>", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdGreaterThan(Long value) {
            addCriterion("tag_id >", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdGreaterThanOrEqualTo(Long value) {
            addCriterion("tag_id >=", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdLessThan(Long value) {
            addCriterion("tag_id <", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdLessThanOrEqualTo(Long value) {
            addCriterion("tag_id <=", value, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdIn(List<Long> values) {
            addCriterion("tag_id in", values, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdNotIn(List<Long> values) {
            addCriterion("tag_id not in", values, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdBetween(Long value1, Long value2) {
            addCriterion("tag_id between", value1, value2, "tagId");
            return (Criteria) this;
        }

        public Criteria andTagIdNotBetween(Long value1, Long value2) {
            addCriterion("tag_id not between", value1, value2, "tagId");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeIsNull() {
            addCriterion("lite_pickup_begin_time is null");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeIsNotNull() {
            addCriterion("lite_pickup_begin_time is not null");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeEqualTo(Timestamp value) {
            addCriterion("lite_pickup_begin_time =", value, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeNotEqualTo(Timestamp value) {
            addCriterion("lite_pickup_begin_time <>", value, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeGreaterThan(Timestamp value) {
            addCriterion("lite_pickup_begin_time >", value, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("lite_pickup_begin_time >=", value, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeLessThan(Timestamp value) {
            addCriterion("lite_pickup_begin_time <", value, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("lite_pickup_begin_time <=", value, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeIn(List<Timestamp> values) {
            addCriterion("lite_pickup_begin_time in", values, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeNotIn(List<Timestamp> values) {
            addCriterion("lite_pickup_begin_time not in", values, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("lite_pickup_begin_time between", value1, value2, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupBeginTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("lite_pickup_begin_time not between", value1, value2, "litePickupBeginTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeIsNull() {
            addCriterion("lite_pickup_end_time is null");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeIsNotNull() {
            addCriterion("lite_pickup_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeEqualTo(Timestamp value) {
            addCriterion("lite_pickup_end_time =", value, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeNotEqualTo(Timestamp value) {
            addCriterion("lite_pickup_end_time <>", value, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeGreaterThan(Timestamp value) {
            addCriterion("lite_pickup_end_time >", value, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("lite_pickup_end_time >=", value, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeLessThan(Timestamp value) {
            addCriterion("lite_pickup_end_time <", value, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("lite_pickup_end_time <=", value, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeIn(List<Timestamp> values) {
            addCriterion("lite_pickup_end_time in", values, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeNotIn(List<Timestamp> values) {
            addCriterion("lite_pickup_end_time not in", values, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("lite_pickup_end_time between", value1, value2, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andLitePickupEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("lite_pickup_end_time not between", value1, value2, "litePickupEndTime");
            return (Criteria) this;
        }

        public Criteria andBillPeriodIsNull() {
            addCriterion("bill_period is null");
            return (Criteria) this;
        }

        public Criteria andBillPeriodIsNotNull() {
            addCriterion("bill_period is not null");
            return (Criteria) this;
        }

        public Criteria andBillPeriodEqualTo(Integer value) {
            addCriterion("bill_period =", value, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodNotEqualTo(Integer value) {
            addCriterion("bill_period <>", value, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodGreaterThan(Integer value) {
            addCriterion("bill_period >", value, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodGreaterThanOrEqualTo(Integer value) {
            addCriterion("bill_period >=", value, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodLessThan(Integer value) {
            addCriterion("bill_period <", value, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodLessThanOrEqualTo(Integer value) {
            addCriterion("bill_period <=", value, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodIn(List<Integer> values) {
            addCriterion("bill_period in", values, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodNotIn(List<Integer> values) {
            addCriterion("bill_period not in", values, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodBetween(Integer value1, Integer value2) {
            addCriterion("bill_period between", value1, value2, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andBillPeriodNotBetween(Integer value1, Integer value2) {
            addCriterion("bill_period not between", value1, value2, "billPeriod");
            return (Criteria) this;
        }

        public Criteria andOrderEmailIsNull() {
            addCriterion("order_email is null");
            return (Criteria) this;
        }

        public Criteria andOrderEmailIsNotNull() {
            addCriterion("order_email is not null");
            return (Criteria) this;
        }

        public Criteria andOrderEmailEqualTo(String value) {
            addCriterion("order_email =", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailNotEqualTo(String value) {
            addCriterion("order_email <>", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailGreaterThan(String value) {
            addCriterion("order_email >", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailGreaterThanOrEqualTo(String value) {
            addCriterion("order_email >=", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailLessThan(String value) {
            addCriterion("order_email <", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailLessThanOrEqualTo(String value) {
            addCriterion("order_email <=", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailLike(String value) {
            addCriterion("order_email like", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailNotLike(String value) {
            addCriterion("order_email not like", value, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailIn(List<String> values) {
            addCriterion("order_email in", values, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailNotIn(List<String> values) {
            addCriterion("order_email not in", values, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailBetween(String value1, String value2) {
            addCriterion("order_email between", value1, value2, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andOrderEmailNotBetween(String value1, String value2) {
            addCriterion("order_email not between", value1, value2, "orderEmail");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameIsNull() {
            addCriterion("is_schedule_the_same is null");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameIsNotNull() {
            addCriterion("is_schedule_the_same is not null");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameEqualTo(Integer value) {
            addCriterion("is_schedule_the_same =", value, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameNotEqualTo(Integer value) {
            addCriterion("is_schedule_the_same <>", value, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameGreaterThan(Integer value) {
            addCriterion("is_schedule_the_same >", value, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_schedule_the_same >=", value, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameLessThan(Integer value) {
            addCriterion("is_schedule_the_same <", value, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameLessThanOrEqualTo(Integer value) {
            addCriterion("is_schedule_the_same <=", value, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameIn(List<Integer> values) {
            addCriterion("is_schedule_the_same in", values, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameNotIn(List<Integer> values) {
            addCriterion("is_schedule_the_same not in", values, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameBetween(Integer value1, Integer value2) {
            addCriterion("is_schedule_the_same between", value1, value2, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andIsScheduleTheSameNotBetween(Integer value1, Integer value2) {
            addCriterion("is_schedule_the_same not between", value1, value2, "isScheduleTheSame");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeIsNull() {
            addCriterion("external_schedule_start_time is null");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeIsNotNull() {
            addCriterion("external_schedule_start_time is not null");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeEqualTo(Timestamp value) {
            addCriterion("external_schedule_start_time =", value, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeNotEqualTo(Timestamp value) {
            addCriterion("external_schedule_start_time <>", value, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeGreaterThan(Timestamp value) {
            addCriterion("external_schedule_start_time >", value, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("external_schedule_start_time >=", value, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeLessThan(Timestamp value) {
            addCriterion("external_schedule_start_time <", value, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("external_schedule_start_time <=", value, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeIn(List<Timestamp> values) {
            addCriterion("external_schedule_start_time in", values, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeNotIn(List<Timestamp> values) {
            addCriterion("external_schedule_start_time not in", values, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("external_schedule_start_time between", value1, value2, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleStartTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("external_schedule_start_time not between", value1, value2, "externalScheduleStartTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeIsNull() {
            addCriterion("external_schedule_end_time is null");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeIsNotNull() {
            addCriterion("external_schedule_end_time is not null");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeEqualTo(Timestamp value) {
            addCriterion("external_schedule_end_time =", value, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeNotEqualTo(Timestamp value) {
            addCriterion("external_schedule_end_time <>", value, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeGreaterThan(Timestamp value) {
            addCriterion("external_schedule_end_time >", value, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("external_schedule_end_time >=", value, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeLessThan(Timestamp value) {
            addCriterion("external_schedule_end_time <", value, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("external_schedule_end_time <=", value, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeIn(List<Timestamp> values) {
            addCriterion("external_schedule_end_time in", values, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeNotIn(List<Timestamp> values) {
            addCriterion("external_schedule_end_time not in", values, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("external_schedule_end_time between", value1, value2, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andExternalScheduleEndTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("external_schedule_end_time not between", value1, value2, "externalScheduleEndTime");
            return (Criteria) this;
        }

        public Criteria andPeriodStartIsNull() {
            addCriterion("period_start is null");
            return (Criteria) this;
        }

        public Criteria andPeriodStartIsNotNull() {
            addCriterion("period_start is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodStartEqualTo(Timestamp value) {
            addCriterion("period_start =", value, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartNotEqualTo(Timestamp value) {
            addCriterion("period_start <>", value, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartGreaterThan(Timestamp value) {
            addCriterion("period_start >", value, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("period_start >=", value, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartLessThan(Timestamp value) {
            addCriterion("period_start <", value, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartLessThanOrEqualTo(Timestamp value) {
            addCriterion("period_start <=", value, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartIn(List<Timestamp> values) {
            addCriterion("period_start in", values, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartNotIn(List<Timestamp> values) {
            addCriterion("period_start not in", values, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartBetween(Timestamp value1, Timestamp value2) {
            addCriterion("period_start between", value1, value2, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodStartNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("period_start not between", value1, value2, "periodStart");
            return (Criteria) this;
        }

        public Criteria andPeriodEndIsNull() {
            addCriterion("period_end is null");
            return (Criteria) this;
        }

        public Criteria andPeriodEndIsNotNull() {
            addCriterion("period_end is not null");
            return (Criteria) this;
        }

        public Criteria andPeriodEndEqualTo(Timestamp value) {
            addCriterion("period_end =", value, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndNotEqualTo(Timestamp value) {
            addCriterion("period_end <>", value, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndGreaterThan(Timestamp value) {
            addCriterion("period_end >", value, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("period_end >=", value, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndLessThan(Timestamp value) {
            addCriterion("period_end <", value, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndLessThanOrEqualTo(Timestamp value) {
            addCriterion("period_end <=", value, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndIn(List<Timestamp> values) {
            addCriterion("period_end in", values, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndNotIn(List<Timestamp> values) {
            addCriterion("period_end not in", values, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndBetween(Timestamp value1, Timestamp value2) {
            addCriterion("period_end between", value1, value2, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPeriodEndNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("period_end not between", value1, value2, "periodEnd");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListIsNull() {
            addCriterion("promotion_policy_list is null");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListIsNotNull() {
            addCriterion("promotion_policy_list is not null");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListEqualTo(String value) {
            addCriterion("promotion_policy_list =", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListNotEqualTo(String value) {
            addCriterion("promotion_policy_list <>", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListGreaterThan(String value) {
            addCriterion("promotion_policy_list >", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListGreaterThanOrEqualTo(String value) {
            addCriterion("promotion_policy_list >=", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListLessThan(String value) {
            addCriterion("promotion_policy_list <", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListLessThanOrEqualTo(String value) {
            addCriterion("promotion_policy_list <=", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListLike(String value) {
            addCriterion("promotion_policy_list like", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListNotLike(String value) {
            addCriterion("promotion_policy_list not like", value, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListIn(List<String> values) {
            addCriterion("promotion_policy_list in", values, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListNotIn(List<String> values) {
            addCriterion("promotion_policy_list not in", values, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListBetween(String value1, String value2) {
            addCriterion("promotion_policy_list between", value1, value2, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andPromotionPolicyListNotBetween(String value1, String value2) {
            addCriterion("promotion_policy_list not between", value1, value2, "promotionPolicyList");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentIsNull() {
            addCriterion("has_opt_agent is null");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentIsNotNull() {
            addCriterion("has_opt_agent is not null");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentEqualTo(Integer value) {
            addCriterion("has_opt_agent =", value, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentNotEqualTo(Integer value) {
            addCriterion("has_opt_agent <>", value, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentGreaterThan(Integer value) {
            addCriterion("has_opt_agent >", value, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentGreaterThanOrEqualTo(Integer value) {
            addCriterion("has_opt_agent >=", value, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentLessThan(Integer value) {
            addCriterion("has_opt_agent <", value, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentLessThanOrEqualTo(Integer value) {
            addCriterion("has_opt_agent <=", value, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentIn(List<Integer> values) {
            addCriterion("has_opt_agent in", values, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentNotIn(List<Integer> values) {
            addCriterion("has_opt_agent not in", values, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentBetween(Integer value1, Integer value2) {
            addCriterion("has_opt_agent between", value1, value2, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andHasOptAgentNotBetween(Integer value1, Integer value2) {
            addCriterion("has_opt_agent not between", value1, value2, "hasOptAgent");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdIsNull() {
            addCriterion("opt_agent_id is null");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdIsNotNull() {
            addCriterion("opt_agent_id is not null");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdEqualTo(Integer value) {
            addCriterion("opt_agent_id =", value, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdNotEqualTo(Integer value) {
            addCriterion("opt_agent_id <>", value, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdGreaterThan(Integer value) {
            addCriterion("opt_agent_id >", value, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("opt_agent_id >=", value, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdLessThan(Integer value) {
            addCriterion("opt_agent_id <", value, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdLessThanOrEqualTo(Integer value) {
            addCriterion("opt_agent_id <=", value, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdIn(List<Integer> values) {
            addCriterion("opt_agent_id in", values, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdNotIn(List<Integer> values) {
            addCriterion("opt_agent_id not in", values, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdBetween(Integer value1, Integer value2) {
            addCriterion("opt_agent_id between", value1, value2, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("opt_agent_id not between", value1, value2, "optAgentId");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameIsNull() {
            addCriterion("opt_agent_name is null");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameIsNotNull() {
            addCriterion("opt_agent_name is not null");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameEqualTo(String value) {
            addCriterion("opt_agent_name =", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameNotEqualTo(String value) {
            addCriterion("opt_agent_name <>", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameGreaterThan(String value) {
            addCriterion("opt_agent_name >", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameGreaterThanOrEqualTo(String value) {
            addCriterion("opt_agent_name >=", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameLessThan(String value) {
            addCriterion("opt_agent_name <", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameLessThanOrEqualTo(String value) {
            addCriterion("opt_agent_name <=", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameLike(String value) {
            addCriterion("opt_agent_name like", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameNotLike(String value) {
            addCriterion("opt_agent_name not like", value, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameIn(List<String> values) {
            addCriterion("opt_agent_name in", values, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameNotIn(List<String> values) {
            addCriterion("opt_agent_name not in", values, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameBetween(String value1, String value2) {
            addCriterion("opt_agent_name between", value1, value2, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andOptAgentNameNotBetween(String value1, String value2) {
            addCriterion("opt_agent_name not between", value1, value2, "optAgentName");
            return (Criteria) this;
        }

        public Criteria andPickupAmountIsNull() {
            addCriterion("pickup_amount is null");
            return (Criteria) this;
        }

        public Criteria andPickupAmountIsNotNull() {
            addCriterion("pickup_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPickupAmountEqualTo(Long value) {
            addCriterion("pickup_amount =", value, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountNotEqualTo(Long value) {
            addCriterion("pickup_amount <>", value, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountGreaterThan(Long value) {
            addCriterion("pickup_amount >", value, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pickup_amount >=", value, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountLessThan(Long value) {
            addCriterion("pickup_amount <", value, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountLessThanOrEqualTo(Long value) {
            addCriterion("pickup_amount <=", value, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountIn(List<Long> values) {
            addCriterion("pickup_amount in", values, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountNotIn(List<Long> values) {
            addCriterion("pickup_amount not in", values, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountBetween(Long value1, Long value2) {
            addCriterion("pickup_amount between", value1, value2, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andPickupAmountNotBetween(Long value1, Long value2) {
            addCriterion("pickup_amount not between", value1, value2, "pickupAmount");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeIsNull() {
            addCriterion("launch_time is null");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeIsNotNull() {
            addCriterion("launch_time is not null");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeEqualTo(Timestamp value) {
            addCriterion("launch_time =", value, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeNotEqualTo(Timestamp value) {
            addCriterion("launch_time <>", value, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeGreaterThan(Timestamp value) {
            addCriterion("launch_time >", value, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("launch_time >=", value, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeLessThan(Timestamp value) {
            addCriterion("launch_time <", value, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("launch_time <=", value, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeIn(List<Timestamp> values) {
            addCriterion("launch_time in", values, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeNotIn(List<Timestamp> values) {
            addCriterion("launch_time not in", values, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("launch_time between", value1, value2, "launchTime");
            return (Criteria) this;
        }

        public Criteria andLaunchTimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("launch_time not between", value1, value2, "launchTime");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountIsNull() {
            addCriterion("un_record_amount is null");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountIsNotNull() {
            addCriterion("un_record_amount is not null");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountEqualTo(Long value) {
            addCriterion("un_record_amount =", value, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountNotEqualTo(Long value) {
            addCriterion("un_record_amount <>", value, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountGreaterThan(Long value) {
            addCriterion("un_record_amount >", value, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("un_record_amount >=", value, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountLessThan(Long value) {
            addCriterion("un_record_amount <", value, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountLessThanOrEqualTo(Long value) {
            addCriterion("un_record_amount <=", value, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountIn(List<Long> values) {
            addCriterion("un_record_amount in", values, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountNotIn(List<Long> values) {
            addCriterion("un_record_amount not in", values, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountBetween(Long value1, Long value2) {
            addCriterion("un_record_amount between", value1, value2, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andUnRecordAmountNotBetween(Long value1, Long value2) {
            addCriterion("un_record_amount not between", value1, value2, "unRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountIsNull() {
            addCriterion("pickup_un_record_amount is null");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountIsNotNull() {
            addCriterion("pickup_un_record_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountEqualTo(Long value) {
            addCriterion("pickup_un_record_amount =", value, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountNotEqualTo(Long value) {
            addCriterion("pickup_un_record_amount <>", value, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountGreaterThan(Long value) {
            addCriterion("pickup_un_record_amount >", value, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pickup_un_record_amount >=", value, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountLessThan(Long value) {
            addCriterion("pickup_un_record_amount <", value, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountLessThanOrEqualTo(Long value) {
            addCriterion("pickup_un_record_amount <=", value, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountIn(List<Long> values) {
            addCriterion("pickup_un_record_amount in", values, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountNotIn(List<Long> values) {
            addCriterion("pickup_un_record_amount not in", values, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountBetween(Long value1, Long value2) {
            addCriterion("pickup_un_record_amount between", value1, value2, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupUnRecordAmountNotBetween(Long value1, Long value2) {
            addCriterion("pickup_un_record_amount not between", value1, value2, "pickupUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountIsNull() {
            addCriterion("brand_un_record_amount is null");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountIsNotNull() {
            addCriterion("brand_un_record_amount is not null");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountEqualTo(Long value) {
            addCriterion("brand_un_record_amount =", value, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountNotEqualTo(Long value) {
            addCriterion("brand_un_record_amount <>", value, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountGreaterThan(Long value) {
            addCriterion("brand_un_record_amount >", value, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("brand_un_record_amount >=", value, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountLessThan(Long value) {
            addCriterion("brand_un_record_amount <", value, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountLessThanOrEqualTo(Long value) {
            addCriterion("brand_un_record_amount <=", value, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountIn(List<Long> values) {
            addCriterion("brand_un_record_amount in", values, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountNotIn(List<Long> values) {
            addCriterion("brand_un_record_amount not in", values, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountBetween(Long value1, Long value2) {
            addCriterion("brand_un_record_amount between", value1, value2, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andBrandUnRecordAmountNotBetween(Long value1, Long value2) {
            addCriterion("brand_un_record_amount not between", value1, value2, "brandUnRecordAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountIsNull() {
            addCriterion("pickup_budget_occupied_amount is null");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountIsNotNull() {
            addCriterion("pickup_budget_occupied_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountEqualTo(Long value) {
            addCriterion("pickup_budget_occupied_amount =", value, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountNotEqualTo(Long value) {
            addCriterion("pickup_budget_occupied_amount <>", value, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountGreaterThan(Long value) {
            addCriterion("pickup_budget_occupied_amount >", value, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pickup_budget_occupied_amount >=", value, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountLessThan(Long value) {
            addCriterion("pickup_budget_occupied_amount <", value, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountLessThanOrEqualTo(Long value) {
            addCriterion("pickup_budget_occupied_amount <=", value, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountIn(List<Long> values) {
            addCriterion("pickup_budget_occupied_amount in", values, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountNotIn(List<Long> values) {
            addCriterion("pickup_budget_occupied_amount not in", values, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountBetween(Long value1, Long value2) {
            addCriterion("pickup_budget_occupied_amount between", value1, value2, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetOccupiedAmountNotBetween(Long value1, Long value2) {
            addCriterion("pickup_budget_occupied_amount not between", value1, value2, "pickupBudgetOccupiedAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountIsNull() {
            addCriterion("pickup_budget_remaining_amount is null");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountIsNotNull() {
            addCriterion("pickup_budget_remaining_amount is not null");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountEqualTo(Long value) {
            addCriterion("pickup_budget_remaining_amount =", value, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountNotEqualTo(Long value) {
            addCriterion("pickup_budget_remaining_amount <>", value, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountGreaterThan(Long value) {
            addCriterion("pickup_budget_remaining_amount >", value, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountGreaterThanOrEqualTo(Long value) {
            addCriterion("pickup_budget_remaining_amount >=", value, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountLessThan(Long value) {
            addCriterion("pickup_budget_remaining_amount <", value, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountLessThanOrEqualTo(Long value) {
            addCriterion("pickup_budget_remaining_amount <=", value, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountIn(List<Long> values) {
            addCriterion("pickup_budget_remaining_amount in", values, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountNotIn(List<Long> values) {
            addCriterion("pickup_budget_remaining_amount not in", values, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountBetween(Long value1, Long value2) {
            addCriterion("pickup_budget_remaining_amount between", value1, value2, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andPickupBudgetRemainingAmountNotBetween(Long value1, Long value2) {
            addCriterion("pickup_budget_remaining_amount not between", value1, value2, "pickupBudgetRemainingAmount");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNull() {
            addCriterion("department_id is null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIsNotNull() {
            addCriterion("department_id is not null");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdEqualTo(Integer value) {
            addCriterion("department_id =", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotEqualTo(Integer value) {
            addCriterion("department_id <>", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThan(Integer value) {
            addCriterion("department_id >", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("department_id >=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThan(Integer value) {
            addCriterion("department_id <", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdLessThanOrEqualTo(Integer value) {
            addCriterion("department_id <=", value, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdIn(List<Integer> values) {
            addCriterion("department_id in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotIn(List<Integer> values) {
            addCriterion("department_id not in", values, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdBetween(Integer value1, Integer value2) {
            addCriterion("department_id between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andDepartmentIdNotBetween(Integer value1, Integer value2) {
            addCriterion("department_id not between", value1, value2, "departmentId");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedIsNull() {
            addCriterion("is_direct_sales_manual_modified is null");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedIsNotNull() {
            addCriterion("is_direct_sales_manual_modified is not null");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedEqualTo(Integer value) {
            addCriterion("is_direct_sales_manual_modified =", value, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedNotEqualTo(Integer value) {
            addCriterion("is_direct_sales_manual_modified <>", value, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedGreaterThan(Integer value) {
            addCriterion("is_direct_sales_manual_modified >", value, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_direct_sales_manual_modified >=", value, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedLessThan(Integer value) {
            addCriterion("is_direct_sales_manual_modified <", value, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedLessThanOrEqualTo(Integer value) {
            addCriterion("is_direct_sales_manual_modified <=", value, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedIn(List<Integer> values) {
            addCriterion("is_direct_sales_manual_modified in", values, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedNotIn(List<Integer> values) {
            addCriterion("is_direct_sales_manual_modified not in", values, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedBetween(Integer value1, Integer value2) {
            addCriterion("is_direct_sales_manual_modified between", value1, value2, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsDirectSalesManualModifiedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_direct_sales_manual_modified not between", value1, value2, "isDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedIsNull() {
            addCriterion("is_channel_sales_manual_modified is null");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedIsNotNull() {
            addCriterion("is_channel_sales_manual_modified is not null");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedEqualTo(Integer value) {
            addCriterion("is_channel_sales_manual_modified =", value, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedNotEqualTo(Integer value) {
            addCriterion("is_channel_sales_manual_modified <>", value, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedGreaterThan(Integer value) {
            addCriterion("is_channel_sales_manual_modified >", value, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_channel_sales_manual_modified >=", value, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedLessThan(Integer value) {
            addCriterion("is_channel_sales_manual_modified <", value, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedLessThanOrEqualTo(Integer value) {
            addCriterion("is_channel_sales_manual_modified <=", value, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedIn(List<Integer> values) {
            addCriterion("is_channel_sales_manual_modified in", values, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedNotIn(List<Integer> values) {
            addCriterion("is_channel_sales_manual_modified not in", values, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedBetween(Integer value1, Integer value2) {
            addCriterion("is_channel_sales_manual_modified between", value1, value2, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsChannelSalesManualModifiedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_channel_sales_manual_modified not between", value1, value2, "isChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andCrmVerIsNull() {
            addCriterion("crm_ver is null");
            return (Criteria) this;
        }

        public Criteria andCrmVerIsNotNull() {
            addCriterion("crm_ver is not null");
            return (Criteria) this;
        }

        public Criteria andCrmVerEqualTo(Integer value) {
            addCriterion("crm_ver =", value, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerNotEqualTo(Integer value) {
            addCriterion("crm_ver <>", value, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerGreaterThan(Integer value) {
            addCriterion("crm_ver >", value, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerGreaterThanOrEqualTo(Integer value) {
            addCriterion("crm_ver >=", value, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerLessThan(Integer value) {
            addCriterion("crm_ver <", value, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerLessThanOrEqualTo(Integer value) {
            addCriterion("crm_ver <=", value, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerIn(List<Integer> values) {
            addCriterion("crm_ver in", values, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerNotIn(List<Integer> values) {
            addCriterion("crm_ver not in", values, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerBetween(Integer value1, Integer value2) {
            addCriterion("crm_ver between", value1, value2, "crmVer");
            return (Criteria) this;
        }

        public Criteria andCrmVerNotBetween(Integer value1, Integer value2) {
            addCriterion("crm_ver not between", value1, value2, "crmVer");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedIsNull() {
            addCriterion("is_biz_direct_sales_manual_modified is null");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedIsNotNull() {
            addCriterion("is_biz_direct_sales_manual_modified is not null");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedEqualTo(Integer value) {
            addCriterion("is_biz_direct_sales_manual_modified =", value, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedNotEqualTo(Integer value) {
            addCriterion("is_biz_direct_sales_manual_modified <>", value, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedGreaterThan(Integer value) {
            addCriterion("is_biz_direct_sales_manual_modified >", value, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_biz_direct_sales_manual_modified >=", value, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedLessThan(Integer value) {
            addCriterion("is_biz_direct_sales_manual_modified <", value, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedLessThanOrEqualTo(Integer value) {
            addCriterion("is_biz_direct_sales_manual_modified <=", value, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedIn(List<Integer> values) {
            addCriterion("is_biz_direct_sales_manual_modified in", values, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedNotIn(List<Integer> values) {
            addCriterion("is_biz_direct_sales_manual_modified not in", values, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedBetween(Integer value1, Integer value2) {
            addCriterion("is_biz_direct_sales_manual_modified between", value1, value2, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizDirectSalesManualModifiedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_biz_direct_sales_manual_modified not between", value1, value2, "isBizDirectSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedIsNull() {
            addCriterion("is_biz_channel_sales_manual_modified is null");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedIsNotNull() {
            addCriterion("is_biz_channel_sales_manual_modified is not null");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedEqualTo(Integer value) {
            addCriterion("is_biz_channel_sales_manual_modified =", value, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedNotEqualTo(Integer value) {
            addCriterion("is_biz_channel_sales_manual_modified <>", value, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedGreaterThan(Integer value) {
            addCriterion("is_biz_channel_sales_manual_modified >", value, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_biz_channel_sales_manual_modified >=", value, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedLessThan(Integer value) {
            addCriterion("is_biz_channel_sales_manual_modified <", value, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedLessThanOrEqualTo(Integer value) {
            addCriterion("is_biz_channel_sales_manual_modified <=", value, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedIn(List<Integer> values) {
            addCriterion("is_biz_channel_sales_manual_modified in", values, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedNotIn(List<Integer> values) {
            addCriterion("is_biz_channel_sales_manual_modified not in", values, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedBetween(Integer value1, Integer value2) {
            addCriterion("is_biz_channel_sales_manual_modified between", value1, value2, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andIsBizChannelSalesManualModifiedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_biz_channel_sales_manual_modified not between", value1, value2, "isBizChannelSalesManualModified");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetIsNull() {
            addCriterion("pre_cm_order_budget is null");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetIsNotNull() {
            addCriterion("pre_cm_order_budget is not null");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetEqualTo(Long value) {
            addCriterion("pre_cm_order_budget =", value, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetNotEqualTo(Long value) {
            addCriterion("pre_cm_order_budget <>", value, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetGreaterThan(Long value) {
            addCriterion("pre_cm_order_budget >", value, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetGreaterThanOrEqualTo(Long value) {
            addCriterion("pre_cm_order_budget >=", value, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetLessThan(Long value) {
            addCriterion("pre_cm_order_budget <", value, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetLessThanOrEqualTo(Long value) {
            addCriterion("pre_cm_order_budget <=", value, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetIn(List<Long> values) {
            addCriterion("pre_cm_order_budget in", values, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetNotIn(List<Long> values) {
            addCriterion("pre_cm_order_budget not in", values, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetBetween(Long value1, Long value2) {
            addCriterion("pre_cm_order_budget between", value1, value2, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andPreCmOrderBudgetNotBetween(Long value1, Long value2) {
            addCriterion("pre_cm_order_budget not between", value1, value2, "preCmOrderBudget");
            return (Criteria) this;
        }

        public Criteria andModifyFlagIsNull() {
            addCriterion("modify_flag is null");
            return (Criteria) this;
        }

        public Criteria andModifyFlagIsNotNull() {
            addCriterion("modify_flag is not null");
            return (Criteria) this;
        }

        public Criteria andModifyFlagEqualTo(Integer value) {
            addCriterion("modify_flag =", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagNotEqualTo(Integer value) {
            addCriterion("modify_flag <>", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagGreaterThan(Integer value) {
            addCriterion("modify_flag >", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagGreaterThanOrEqualTo(Integer value) {
            addCriterion("modify_flag >=", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagLessThan(Integer value) {
            addCriterion("modify_flag <", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagLessThanOrEqualTo(Integer value) {
            addCriterion("modify_flag <=", value, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagIn(List<Integer> values) {
            addCriterion("modify_flag in", values, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagNotIn(List<Integer> values) {
            addCriterion("modify_flag not in", values, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagBetween(Integer value1, Integer value2) {
            addCriterion("modify_flag between", value1, value2, "modifyFlag");
            return (Criteria) this;
        }

        public Criteria andModifyFlagNotBetween(Integer value1, Integer value2) {
            addCriterion("modify_flag not between", value1, value2, "modifyFlag");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}