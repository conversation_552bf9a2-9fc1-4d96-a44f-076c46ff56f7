package com.bilibili.crm.platform.biz.extractor;

import com.bilibili.crm.platform.api.dss.dto.DssAdOverviewDto;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.metrics.sum.Sum;
import org.springframework.data.elasticsearch.core.ResultsExtractor;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;

public class DssAdOverviewQuotaExtractor implements ResultsExtractor<DssAdOverviewDto> {
    @Override
    public DssAdOverviewDto extract(SearchResponse searchResponse) {
        List<Aggregation> list = searchResponse.getAggregations().asList();
        if (CollectionUtils.isEmpty(list)) {
            return DssAdOverviewDto.builder().build();
        }
        Sum pv = searchResponse.getAggregations().get("costAgg");
        Sum show = searchResponse.getAggregations().get("showCountAgg");
        Sum click = searchResponse.getAggregations().get("clickCountAgg");

        return DssAdOverviewDto.builder()
                .totalConsume(BigDecimal.valueOf(pv.getValue()))
                .showCount(new Double(show.getValue()).longValue())
                .clickCount(new Double(click.getValue()).longValue())
                .build();
    }
}
