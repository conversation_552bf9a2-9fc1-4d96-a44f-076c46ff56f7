package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmEasTransRecordPo implements Serializable {
    /**
     * 主键Id
     */
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 传输业务类型
     */
    @NotEmpty
    private Integer bizType;

    /**
     * 单据类型 1 应收单 2 收款单 3 结算单
     */
    @NotEmpty
    private Integer billType;

    /**
     * 传输月份 
     */
    @NotEmpty
    private Timestamp transMonth;

    /**
     *  0 待传输 1 传输中 2 传输完成
     */
    @NotEmpty
    private Integer transStatus;

    /**
     * 操作人
     */
    @NotEmpty
    private String operator;

    /**
     * 是否删除 0 未删除 1 已删除
     */
    @NotEmpty
    private Integer isDeleted;

    /**
     * 创建时间
     */
    @NotEmpty
    private Timestamp ctime;

    /**
     * 更新时间
     */
    @NotEmpty
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}