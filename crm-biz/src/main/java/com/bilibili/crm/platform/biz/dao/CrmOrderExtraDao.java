package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmOrderExtraPo;
import com.bilibili.crm.platform.biz.po.CrmOrderExtraPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmOrderExtraDao {
    long countByExample(CrmOrderExtraPoExample example);

    int deleteByExample(CrmOrderExtraPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmOrderExtraPo record);

    int insertBatch(List<CrmOrderExtraPo> records);

    int insertUpdateBatch(List<CrmOrderExtraPo> records);

    int insert(CrmOrderExtraPo record);

    int insertUpdateSelective(CrmOrderExtraPo record);

    int insertSelective(CrmOrderExtraPo record);

    List<CrmOrderExtraPo> selectByExample(CrmOrderExtraPoExample example);

    CrmOrderExtraPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmOrderExtraPo record, @Param("example") CrmOrderExtraPoExample example);

    int updateByExample(@Param("record") CrmOrderExtraPo record, @Param("example") CrmOrderExtraPoExample example);

    int updateByPrimaryKeySelective(CrmOrderExtraPo record);

    int updateByPrimaryKey(CrmOrderExtraPo record);
}