package com.bilibili.crm.platform.biz.crm.account.dao.read;

import com.bilibili.crm.platform.biz.po.CrmAgentPo;
import com.bilibili.crm.platform.biz.po.CrmAgentPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface CrmAgentReadOnlyDao {
    long countByExample(CrmAgentPoExample example);

    int deleteByExample(CrmAgentPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmAgentPo record);

    int insertBatch(List<CrmAgentPo> records);

    int insertUpdateBatch(List<CrmAgentPo> records);

    int insert(CrmAgentPo record);

    int insertUpdateSelective(CrmAgentPo record);

    int insertSelective(CrmAgentPo record);

    List<CrmAgentPo> selectByExample(CrmAgentPoExample example);

    CrmAgentPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmAgentPo record, @Param("example") CrmAgentPoExample example);

    int updateByExample(@Param("record") CrmAgentPo record, @Param("example") CrmAgentPoExample example);

    int updateByPrimaryKeySelective(CrmAgentPo record);

    int updateByPrimaryKey(CrmAgentPo record);
}