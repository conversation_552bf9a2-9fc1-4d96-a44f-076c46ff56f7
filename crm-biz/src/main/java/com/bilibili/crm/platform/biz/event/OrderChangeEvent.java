package com.bilibili.crm.platform.biz.event;

import lombok.Getter;
import lombok.Setter;
import org.springframework.context.ApplicationEvent;

/**
 * @author: brady
 * @time: 2025/4/8 19:44
 */
@Getter
@Setter
public class OrderChangeEvent extends ApplicationEvent {
    private static final long serialVersionUID = -1L;

    public OrderChangeEvent(Object source) {
        super(source);
    }

    private Integer crmOrderId;
}
