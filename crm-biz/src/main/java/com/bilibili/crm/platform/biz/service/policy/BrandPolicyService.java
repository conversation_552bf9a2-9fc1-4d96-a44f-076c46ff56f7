package com.bilibili.crm.platform.biz.service.policy;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.policy.IBrandPolicyService;
import com.bilibili.crm.platform.api.policy.dto.*;
import com.bilibili.crm.platform.biz.dao.CrmBrandCustomerPolicyDao;
import com.bilibili.crm.platform.biz.dao.CrmBrandCustomerPolicyDiscountDao;
import com.bilibili.crm.platform.biz.dao.CrmBrandCustomerPolicyMappingDao;
import com.bilibili.crm.platform.biz.po.CrmBrandCustomerPolicyDiscountPo;
import com.bilibili.crm.platform.biz.po.CrmBrandCustomerPolicyMappingPo;
import com.bilibili.crm.platform.biz.po.CrmBrandCustomerPolicyPo;
import com.bilibili.crm.platform.biz.repo.policy.BrandCustomerPolicyDiscountRepo;
import com.bilibili.crm.platform.biz.repo.policy.BrandCustomerPolicyMappingRepo;
import com.bilibili.crm.platform.biz.repo.policy.BrandCustomerPolicyRepo;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchievementHelper;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.Module;
import com.bilibili.crm.platform.common.income.IncomeProductEnum;
import com.bilibili.crm.platform.common.policy.BrandCustomerMainType;
import com.bilibili.crm.platform.common.policy.BrandPolicyMappingCategory;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.List;
import java.util.stream.Collectors;

/**
 * 品牌直客政策
 *
 * @author: brady
 * @time: 2021/4/30 2:07 下午
 */
@Slf4j
@Service
public class BrandPolicyService implements IBrandPolicyService {

    @Autowired
    private BrandCustomerPolicyRepo BrandPolicyRepo;
    @Autowired
    private BrandCustomerPolicyMappingRepo BrandPolicyMappingRepo;
    @Autowired
    private BrandCustomerPolicyDiscountRepo brandPolicyDiscountRepo;
    @Autowired
    private CrmBrandCustomerPolicyDao brandCustomerPolicyDao;
    @Autowired
    private CrmBrandCustomerPolicyMappingDao brandCustomerPolicyMappingDao;
    @Autowired
    private CrmBrandCustomerPolicyDiscountDao brandCustomerPolicyDiscountDao;
    @Autowired
    private AchievementHelper achievementHelper;
    @Autowired
    private ILogOperatorService logOperatorService;

    /**
     * 分页查询政策列表
     *
     * @param queryDto
     * @return
     */
    @Override
    public PageResult<BrandCustomerPolicyDto> queryPolicyPage(BrandCustomerPolicyQueryDto queryDto) {
        //checkParams(queryDto);
        try {
            BrandCustomerPolicyQueryDto brandCustomerPolicyQueryDto = changeQueryDto(queryDto);

            Long total = BrandPolicyRepo.countPolicy(brandCustomerPolicyQueryDto);
            if (!Utils.isPositive(total)) {
                return new PageResult(0, Collections.emptyList());
            }

            List<CrmBrandCustomerPolicyPo> policyPos = BrandPolicyRepo.queryPolicy(brandCustomerPolicyQueryDto);
            List<BrandCustomerPolicyDto> policies = policyPos.stream().map(this::covertPolicyDto).collect(Collectors.toList());
            this.patchPolicyDto(policies);
            return new PageResult<>(total.intValue(), policies);
        } catch (IncomeConditionInvalidException e) {
            return new PageResult(0, Collections.emptyList());
        }
    }

    /**
     * 分页查询政策列表
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<BrandCustomerPolicyDto> queryPolicy(BrandCustomerPolicyQueryDto queryDto) {
        //checkParams(queryDto);
        try {
            BrandCustomerPolicyQueryDto brandCustomerPolicyQueryDto = changeQueryDto(queryDto);

            Long total = BrandPolicyRepo.countPolicy(brandCustomerPolicyQueryDto);
            if (!Utils.isPositive(total)) {
                return Collections.emptyList();
            }

            List<CrmBrandCustomerPolicyPo> policyPos = BrandPolicyRepo.queryPolicy(brandCustomerPolicyQueryDto);
            List<BrandCustomerPolicyDto> policies = policyPos.stream().map(this::covertPolicyDto).collect(Collectors.toList());
            this.patchPolicyDto(policies);
            return policies;
        } catch (IncomeConditionInvalidException e) {
            return Collections.emptyList();
        }

    }

    @Override
    public BrandCustomerPolicyDto getPolicyById(Integer policyId) {
        CrmBrandCustomerPolicyPo policy = BrandPolicyRepo.getPolicyById(policyId);
        Assert.notNull(policy, "政策不存在");
        BrandCustomerPolicyDto brandCustomerPolicyDto = covertPolicyDto(policy);
        patchPolicyDto(brandCustomerPolicyDto);
        return brandCustomerPolicyDto;
    }

    private void patchPolicyDto(BrandCustomerPolicyDto policy) {

        List<CrmBrandCustomerPolicyMappingPo> mappingPos = BrandPolicyMappingRepo.queryMapping(BrandCustomerPolicyMappingQueryDto.builder().policyId(policy.getId()).build());

        List<CrmBrandCustomerPolicyDiscountPo> discountPos = brandPolicyDiscountRepo.queryMapping(BrandCustomerPolicyDiscountQueryDto.builder().policyId(policy.getId()).build());

        //关联主体
        policy.setPolicyRelatedSubject(buildPolicyRelatedSubjectDto(mappingPos, policy.getId()));

        //关联产品
        policy.setIncomeProductTypes(buildIncomeProductTypes(mappingPos, policy.getId()));

        //折扣政策
        policy.setPolicyDiscount(buildBrandPolicyDto(discountPos, policy));

        //服务代理
        policy.setServiceAgent(buildPolicyServiceAgent(mappingPos, policy.getId()));
    }

    /**
     * 政策关联信息
     *
     * @param policies
     */
    public void patchPolicyDto(List<BrandCustomerPolicyDto> policies) {
        List<Integer> policyIds = policies.stream().map(BrandCustomerPolicyDto::getId).collect(Collectors.toList());
        List<CrmBrandCustomerPolicyMappingPo> mappingPos = BrandPolicyMappingRepo.queryMapping(BrandCustomerPolicyMappingQueryDto.builder().policyIds(policyIds).build());

        List<CrmBrandCustomerPolicyDiscountPo> discountPos = brandPolicyDiscountRepo.queryMapping(BrandCustomerPolicyDiscountQueryDto.builder().policyIds(policyIds).build());
        policies.forEach(policy -> {
            //关联主体
            policy.setPolicyRelatedSubject(buildPolicyRelatedSubjectDto(mappingPos, policy.getId()));

            //关联产品
            policy.setIncomeProductTypes(buildIncomeProductTypes(mappingPos, policy.getId()));

            //折扣政策
            policy.setPolicyDiscount(buildBrandPolicyDto(discountPos, policy));

            //服务代理
            policy.setServiceAgent(buildPolicyServiceAgent(mappingPos, policy.getId()));
        });

    }

    private BrandPolicyDto buildBrandPolicyDto(List<CrmBrandCustomerPolicyDiscountPo> discountPos, BrandCustomerPolicyDto policy) {
        List<CrmBrandCustomerPolicyDiscountPo> pos = discountPos.stream().filter(item -> {
            return policy.getId().equals(item.getPolicyId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pos)) {
            BrandPolicyDto init = BrandPolicyDto.init();
            return init;
        }

        List<BrandPolicyDetailDto> discountDetails = pos.stream().map(item -> {
            return BrandPolicyDetailDto.builder()
                    .discountAmount(item.getDiscountAmount())
                    .distributionAmount(item.getDistributionAmount())
                    .lowerLimitType(item.getLowerLimitType())
                    .lowerLimitAmount(BigDecimal.valueOf(item.getLowerLimitAmount()))
                    .upperLimitType(item.getUpperLimitType())
                    .upperLimitAmount(BigDecimal.valueOf(item.getUpperLimitAmount()))
                    .build();
        }).collect(Collectors.toList());
        return BrandPolicyDto.builder()
                .type(policy.getDiscountType())
                .traceBack(policy.getIsTraceBack())
                .policies(discountDetails)
                .build();
    }

    private List<Integer> buildIncomeProductTypes(List<CrmBrandCustomerPolicyMappingPo> mappingPos, Integer policyId) {
        List<CrmBrandCustomerPolicyMappingPo> pos = mappingPos.stream().filter(item -> {
            return BrandPolicyMappingCategory.PRODUCT.getCode().equals(item.getFirstCategory()) && policyId.equals(item.getPolicyId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        return pos.stream().map(CrmBrandCustomerPolicyMappingPo::getTargetId).collect(Collectors.toList());
    }


    private List<Integer> buildPolicyServiceAgent(List<CrmBrandCustomerPolicyMappingPo> mappingPos, Integer policyId) {
        List<CrmBrandCustomerPolicyMappingPo> pos = mappingPos.stream().filter(item -> {
            return BrandPolicyMappingCategory.AGENT.getCode().equals(item.getFirstCategory()) && policyId.equals(item.getPolicyId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }

        List<Integer> targetIds = pos.stream().map(CrmBrandCustomerPolicyMappingPo::getTargetId).collect(Collectors.toList());
        return targetIds;
    }

    private PolicyRelatedSubjectDto buildPolicyRelatedSubjectDto(List<CrmBrandCustomerPolicyMappingPo> mappingPos, Integer policyId) {
        List<CrmBrandCustomerPolicyMappingPo> pos = mappingPos.stream().filter(item -> {
            return BrandPolicyMappingCategory.SUBJECT.getCode().equals(item.getFirstCategory()) && policyId.equals(item.getPolicyId());
        }).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(pos)) {
            PolicyRelatedSubjectDto init = PolicyRelatedSubjectDto.init();
            return init;
        }

        List<Integer> targetIds = pos.stream().map(CrmBrandCustomerPolicyMappingPo::getTargetId).collect(Collectors.toList());
        return PolicyRelatedSubjectDto.builder()
                .policyRelatedType(pos.get(0).getSecondCategory())
                .policyRelatedIds(targetIds)
                .build();
    }


    private BrandCustomerPolicyDto covertPolicyDto(CrmBrandCustomerPolicyPo po) {

        BrandCustomerPolicyDto dto = BrandCustomerPolicyDto.builder().build();
        BeanUtils.copyProperties(po, dto);
        dto.setEarnestMoneyAmount(BigDecimal.valueOf(po.getEarnestMoneyAmount()));
        dto.setIncomeTargetAmount(BigDecimal.valueOf(po.getIncomeTargetAmount()));
        return dto;
    }


    private BrandCustomerPolicyQueryDto changeQueryDto(BrandCustomerPolicyQueryDto queryDto) throws IncomeConditionInvalidException {
        List<Integer> mappingPolicyIdsByAccount = getMappingPolicyIdsByAccount(queryDto.getAccountIds());
        List<Integer> mappingPolicyIdsByCustomer = getMappingPolicyIdsByCustomer(queryDto.getCustomerIds());
        List<Integer> mappingPolicyIdsByGroup = getMappingPolicyIdsByGroup(queryDto.getGroupIds());
        List<Integer> policyIds = achievementHelper.retainList(achievementHelper.retainList(mappingPolicyIdsByAccount, mappingPolicyIdsByCustomer), mappingPolicyIdsByGroup);

        List<Integer> queryPolicyIds = achievementHelper.retainList(queryDto.getIds(), policyIds);
        return BrandCustomerPolicyQueryDto.builder()
                .Id(queryDto.getId())
                .Ids(queryPolicyIds)
                .nameLike(queryDto.getNameLike())
                .incomeBeginTime(queryDto.getIncomeBeginTime())
                .incomeEndTime(queryDto.getIncomeEndTime())
                .page(queryDto.getPage())
                .size(queryDto.getSize())
                .build();
    }


    private List<Integer> getMappingPolicyIdsByAccount(List<Integer> targetIds) throws IncomeConditionInvalidException {
        if (!CollectionUtils.isEmpty(targetIds)) {
            List<Integer> policyIds = BrandPolicyMappingRepo.queryMappingPolicyIds(BrandCustomerPolicyMappingQueryDto.builder()
                    .targetIds(targetIds)
                    .firstCategory(BrandPolicyMappingCategory.SUBJECT.getCode())
                    .secondCategory(BrandCustomerMainType.ACCOUNT.getCode())
                    .build());

            if (CollectionUtils.isEmpty(policyIds)) {
                throw new IncomeConditionInvalidException();
            }

            return policyIds;
        }
        return Collections.emptyList();
    }

    private List<Integer> getMappingPolicyIdsByCustomer(List<Integer> targetIds) throws IncomeConditionInvalidException {
        if (!CollectionUtils.isEmpty(targetIds)) {
            List<Integer> policyIds = BrandPolicyMappingRepo.queryMappingPolicyIds(BrandCustomerPolicyMappingQueryDto.builder()
                    .targetIds(targetIds)
                    .firstCategory(BrandPolicyMappingCategory.SUBJECT.getCode())
                    .secondCategory(BrandCustomerMainType.CUSTOMER.getCode())
                    .build());

            if (CollectionUtils.isEmpty(policyIds)) {
                throw new IncomeConditionInvalidException();
            }

            return policyIds;
        }
        return Collections.emptyList();
    }

    private List<Integer> getMappingPolicyIdsByGroup(List<Integer> targetIds) throws IncomeConditionInvalidException {
        if (!CollectionUtils.isEmpty(targetIds)) {
            List<Integer> policyIds = BrandPolicyMappingRepo.queryMappingPolicyIds(BrandCustomerPolicyMappingQueryDto.builder()
                    .targetIds(targetIds)
                    .firstCategory(BrandPolicyMappingCategory.SUBJECT.getCode())
                    .secondCategory(BrandCustomerMainType.GROUP.getCode())
                    .build());

            if (CollectionUtils.isEmpty(policyIds)) {
                throw new IncomeConditionInvalidException();
            }

            return policyIds;
        }
        return Collections.emptyList();
    }


    /**
     * 启用/禁用 政策
     *
     * @param policyId
     * @param status
     * @param operator
     */
    @Override
    public void updatePolicyStatus(Integer policyId, Integer status, Operator operator) {
        //创建政策记录
        CrmBrandCustomerPolicyPo po = CrmBrandCustomerPolicyPo.builder()
                .id(policyId)
                .policyStatus(status)
                .operator(operator.getOperatorName())
                .build();
        brandCustomerPolicyDao.updateByPrimaryKeySelective(po);

        //记录状态变更日志
        insertLog(ModifyType.BRAND_POLICY_STATUS, operator, po.getId(), BrandCustomerPolicyDto.builder().id(policyId).policyStatus(status).build());
    }


    /**
     * 创建政策
     *
     * @param brandCustomerPolicyDto
     */
    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void createPolicy(BrandCustomerPolicyDto brandCustomerPolicyDto, Operator operator) {
        //创建政策记录
        CrmBrandCustomerPolicyPo po = buildBrandCustomerPolicyPo(brandCustomerPolicyDto, operator);
        brandCustomerPolicyDao.insertSelective(po);
        //创建政策关联记录
        createPolicyMapping(brandCustomerPolicyDto, po.getId(), operator);
        //创建政策折扣记录
        createPolicyDiscount(brandCustomerPolicyDto, po.getId(), operator);
        //记录日志
        insertLog(ModifyType.CREATE_BRAND_POLICY, operator, po.getId(), brandCustomerPolicyDto);
    }

    /**
     * 记录操作日志
     */
    public void insertLog(ModifyType modifyType, Operator operator, Integer policyId, BrandCustomerPolicyDto policyDto) {
        //
        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(modifyType)
                .module(Module.BRAND_POLICY)
                .obj(policyDto)
                .objId(policyId)
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logDto);
    }


    /**
     * 编辑政策
     *
     * @param brandCustomerPolicyDto
     */
    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void editPolicy(BrandCustomerPolicyDto brandCustomerPolicyDto, Operator operator) {
        //更新政策记录
        CrmBrandCustomerPolicyPo po = buildBrandCustomerPolicyPo(brandCustomerPolicyDto, operator);

        brandCustomerPolicyDao.updateByPrimaryKeySelective(po);
        //更新政策关联记录
        updatePolicyMapping(brandCustomerPolicyDto, po.getId(), operator);
        //更新政策折扣记录
        updatePolicyDiscount(brandCustomerPolicyDto, po.getId(), operator);

        //记录日志
        insertLog(ModifyType.EDIT_BRAND_POLICY, operator, po.getId(), brandCustomerPolicyDto);
    }

    private void updatePolicyMapping(BrandCustomerPolicyDto dto, Integer policyId, Operator operator) {
        //删除已有政策mapping记录
        BrandPolicyMappingRepo.deleteMappingByPolicyId(policyId);
        //创建政策mapping记录
        createPolicyMapping(dto, policyId, operator);
    }

    private void updatePolicyDiscount(BrandCustomerPolicyDto dto, Integer policyId, Operator operator) {
        //删除已有政策折扣记录
        brandPolicyDiscountRepo.deleteMappingByPolicyId(policyId);
        //创建政策折扣记录
        createPolicyDiscount(dto, policyId, operator);
    }


    private CrmBrandCustomerPolicyPo buildBrandCustomerPolicyPo(BrandCustomerPolicyDto dto, Operator operator) {
        CrmBrandCustomerPolicyPo po = CrmBrandCustomerPolicyPo.builder().build();
        BeanUtils.copyProperties(dto, po);
        //政策关联主题
        po.setRelatedSubject(null != dto.getPolicyRelatedSubject() ? dto.getPolicyRelatedSubject().getPolicyRelatedType() : 0);

        if (null != dto.getPolicyDiscount()) {
            po.setIsTraceBack(null != dto.getPolicyDiscount().getTraceBack() ? dto.getPolicyDiscount().getTraceBack() : 0);
            po.setDiscountType(null != dto.getPolicyDiscount().getType() ? dto.getPolicyDiscount().getType() : 0);
        }
        if (null != dto.getEarnestMoneyAmount()) {
            po.setEarnestMoneyAmount(Utils.fromYuanToFen(dto.getEarnestMoneyAmount()));
        }
        po.setIncomeTargetAmount(Utils.fromYuanToFen(dto.getIncomeTargetAmount()));
        po.setOperator(operator.getOperatorName());
        return po;
    }

    private void createPolicyDiscount(BrandCustomerPolicyDto dto, Integer policyId, Operator operator) {
        BrandPolicyDto policyDiscount = dto.getPolicyDiscount();
        List<BrandPolicyDetailDto> policies = policyDiscount.getPolicies();
        policies.stream().forEach(policy -> {
            CrmBrandCustomerPolicyDiscountPo discountPo = CrmBrandCustomerPolicyDiscountPo.builder()
                    .policyId(policyId)
                    .discountType(policyDiscount.getType())
                    .upperLimitType(policy.getUpperLimitType())
                    .lowerLimitType(policy.getLowerLimitType())
                    .upperLimitAmount(Utils.fromYuanToFen(policy.getUpperLimitAmount()))
                    .lowerLimitAmount(Utils.fromYuanToFen(policy.getLowerLimitAmount()))
                    .discountAmount(policy.getDiscountAmount().intValue())
                    .distributionAmount(policy.getDistributionAmount().intValue())
                    .operator(operator.getOperatorName())
                    .build();
            brandCustomerPolicyDiscountDao.insertSelective(discountPo);
        });
    }

    private void createPolicyMapping(BrandCustomerPolicyDto dto, Integer policyId, Operator operator) {
        List<Integer> serviceAgent = dto.getServiceAgent();
        if (!CollectionUtils.isEmpty(serviceAgent)) {
            serviceAgent.stream().forEach(agentId -> {
                CrmBrandCustomerPolicyMappingPo agentPo = CrmBrandCustomerPolicyMappingPo.builder()
                        .policyId(policyId)
                        .targetId(agentId)
                        .firstCategory(BrandPolicyMappingCategory.AGENT.getCode())
                        .operator(operator.getOperatorName())
                        .build();
                brandCustomerPolicyMappingDao.insertSelective(agentPo);
            });

        }

        List<Integer> productTypes = dto.getIncomeProductTypes();
        if (!CollectionUtils.isEmpty(productTypes)) {
            productTypes.stream().forEach(product -> {
                CrmBrandCustomerPolicyMappingPo productPo = CrmBrandCustomerPolicyMappingPo.builder()
                        .policyId(policyId)
                        .targetId(product)
                        .firstCategory(BrandPolicyMappingCategory.PRODUCT.getCode())
                        .secondCategory(IncomeProductEnum.getByCode(product).getType().getCode())
                        .operator(operator.getOperatorName())
                        .build();
                brandCustomerPolicyMappingDao.insertSelective(productPo);
            });
        }

        PolicyRelatedSubjectDto relatedSubject = dto.getPolicyRelatedSubject();
        if (null != relatedSubject && !CollectionUtils.isEmpty(relatedSubject.getPolicyRelatedIds())) {
            Integer policyRelatedType = relatedSubject.getPolicyRelatedType();
            List<Integer> policyRelatedIds = relatedSubject.getPolicyRelatedIds();

            policyRelatedIds.stream().forEach(relateId -> {
                CrmBrandCustomerPolicyMappingPo subjectPo = CrmBrandCustomerPolicyMappingPo.builder()
                        .policyId(policyId)
                        .targetId(relateId)
                        .firstCategory(BrandPolicyMappingCategory.SUBJECT.getCode())
                        .secondCategory(policyRelatedType)
                        .operator(operator.getOperatorName())
                        .build();
                brandCustomerPolicyMappingDao.insertSelective(subjectPo);
            });
        }
    }
}
