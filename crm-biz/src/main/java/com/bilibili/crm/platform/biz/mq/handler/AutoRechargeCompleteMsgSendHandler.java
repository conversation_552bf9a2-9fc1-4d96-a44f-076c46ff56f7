package com.bilibili.crm.platform.biz.mq.handler;

import com.bilibili.crm.platform.biz.mq.message.AutoRechargeCompleteMessage;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * oa 自动充值完成消息发送处理器
 *
 * <AUTHOR>
 */
@Slf4j
@Deprecated
public class AutoRechargeCompleteMsgSendHandler extends AbstractMqMessageHandler<AutoRechargeCompleteMessage> {

    @Value("${crm.recharge.auto.complete.exchange.name:crm.autoRecharge.complete.exchange}")
    private String autoRechargeCompleteExchangeName;

    @Override
    public Boolean messageValidate(AutoRechargeCompleteMessage msg) {
        return null;
    }

    @Override
    public void convertAndSend(AutoRechargeCompleteMessage msg) {
        log.info("=====> convertAndSend, send pick up account auto recharge msg, msg:{}", msg);
        Assert.notNull(msg, "oa 自动充值完成消息体不能为空");

        this.sendMsgWithRetry(msg, autoRechargeCompleteExchangeName, "");
    }
}
