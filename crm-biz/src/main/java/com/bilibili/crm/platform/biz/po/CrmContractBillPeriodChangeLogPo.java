package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmContractBillPeriodChangeLogPo implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 帐期id
     */
    private Long billPeriodId;

    /**
     * 上一版本
     */
    private Integer lastVersion;

    /**
     * 调整后 截止日期
     */
    private Timestamp adjustDeadline;

    /**
     * 文件oss key
     */
    private String ossKey;

    private String operator;

    /**
     * 文件名称
     */
    private String fileName;

    private String url;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否删除
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}