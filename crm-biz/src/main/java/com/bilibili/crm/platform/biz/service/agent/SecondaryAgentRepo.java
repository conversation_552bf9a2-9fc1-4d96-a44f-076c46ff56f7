package com.bilibili.crm.platform.biz.service.agent;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.agent.dto.SecondaryAgentDto;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CrmAgentDao;
import com.bilibili.crm.platform.biz.dao.CrmSecondaryAgentRoleDao;
import com.bilibili.crm.platform.biz.po.CrmAgentPo;
import com.bilibili.crm.platform.biz.po.CrmAgentPoExample;
import com.bilibili.crm.platform.biz.po.CrmSecondaryAgentRolePo;
import com.bilibili.crm.platform.biz.po.CrmSecondaryAgentRolePoExample;
import com.bilibili.crm.platform.common.AgentType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * @Description 二级代理商
 * <AUTHOR>
 * @DATE 2022/3/28 10:28 下午
 **/
@Slf4j
@Repository
public class SecondaryAgentRepo {

    @Autowired
    private CrmAgentDao crmAgentDao;
    @Autowired
    private CrmSecondaryAgentRoleDao crmSecondaryAgentRoleDao;

    public SecondaryAgentDto getSecondaryAgentDtoByMid(Long mid) {
        Assert.notNull(mid, "二级代理的B站UID不可为空");
        CrmSecondaryAgentRolePo po = this.getByMid(mid);
        if(po == null){
            return null;
        }

        // 通过agentId直接查询agent服务
        CrmAgentPo agentPo = this.getAgentByAgentId(po.getAgentId());
        if(Objects.isNull(agentPo)){
            return null;
        }
        SecondaryAgentDto secondaryAgentDto = new SecondaryAgentDto();
        BeanUtils.copyProperties(po, secondaryAgentDto);
        secondaryAgentDto.setParentAgentAccountId(agentPo.getSysAgentId());
        secondaryAgentDto.setAgentName(agentPo.getName());
        return secondaryAgentDto;
    }

    public List<SecondaryAgentDto> getAllMidValidSecondaryAgentDto() {
        List<CrmSecondaryAgentRolePo> pos = getAllMidValid();
        if(CollectionUtils.isEmpty(pos)){
            return null;
        }
        return pos.stream().map(po -> {
            SecondaryAgentDto secondaryAgentDto = new SecondaryAgentDto();
            BeanUtils.copyProperties(po, secondaryAgentDto);
            return secondaryAgentDto;
        }).collect(Collectors.toList());
    }

    public List<SecondaryAgentDto> getAllSecondaryAgentDto() {
        CrmSecondaryAgentRolePoExample example = new CrmSecondaryAgentRolePoExample();
        example.createCriteria();
        List<CrmSecondaryAgentRolePo> pos = crmSecondaryAgentRoleDao.selectByExample(example);
        if(CollectionUtils.isEmpty(pos)){
            return null;
        }
        return pos.stream().map(po -> {
            SecondaryAgentDto secondaryAgentDto = new SecondaryAgentDto();
            BeanUtils.copyProperties(po, secondaryAgentDto);
            return secondaryAgentDto;
        }).collect(Collectors.toList());
    }

    private CrmSecondaryAgentRolePo getByMid(Long secondaryAgentMid){
        Assert.notNull(secondaryAgentMid);
        CrmSecondaryAgentRolePoExample example = new CrmSecondaryAgentRolePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andMidEqualTo(secondaryAgentMid);
        List<CrmSecondaryAgentRolePo> pos = crmSecondaryAgentRoleDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)){
            return null;
        }
        return pos.get(0);
    }


    private CrmAgentPo getAgentByAgentId(Integer agentId) {
        Assert.notNull(agentId, "代理商ID不可为空");
        log.info("SecondaryAgentRepo.getAgentByAgentId agentId {}", agentId);
        CrmAgentPoExample example = new CrmAgentPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIdEqualTo(agentId)
                .andTypeEqualTo(AgentType.OTHER.getCode());
        List<CrmAgentPo> agentPos = crmAgentDao.selectByExample(example);
        if (CollectionUtils.isEmpty(agentPos)) {
            return null;
        }
        return agentPos.get(0);
    }

    private List<CrmSecondaryAgentRolePo> getAllMidValid(){
        CrmSecondaryAgentRolePoExample example = new CrmSecondaryAgentRolePoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andMidGreaterThan(0L);
        return crmSecondaryAgentRoleDao.selectByExample(example);
    }
}
