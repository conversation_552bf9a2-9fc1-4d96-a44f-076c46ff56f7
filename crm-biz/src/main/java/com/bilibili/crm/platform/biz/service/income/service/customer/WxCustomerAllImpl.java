package com.bilibili.crm.platform.biz.service.income.service.customer;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz_common.olap.migrate.enums.MigrateBizScene;
import com.bilibili.crm.platform.api.achievement.dto.AchieveTimeParam;
import com.bilibili.crm.platform.api.achievement.dto.AchievementRtbData;
import com.bilibili.crm.platform.api.achievement.dto.QueryAchieveDto;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromBillService;
import com.bilibili.crm.platform.api.es.agg.helper.bean.AggregationCondition;
import com.bilibili.crm.platform.api.es.agg.helper.bean.AggregationField;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationBucketType;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationMetricType;
import com.bilibili.crm.platform.api.income.bo.customer.CategoryCustomerStatBo;
import com.bilibili.crm.platform.api.income.dto.*;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.api.income.service.IWxCustomerService;
import com.bilibili.crm.platform.biz.es.agg.helper.aggregation.AggregationConditionBuilder;
import com.bilibili.crm.platform.biz.service.achievement.query.AchieveFromWalletService;
import com.bilibili.crm.platform.biz.service.income.config.customer.CustomerIncomeConfig;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeEnhanceHelper;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeServiceParamHelper;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeServiceQueryHelper;
import com.bilibili.crm.platform.biz.service.income.service.IncomeQueryService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.settle_account.common.CheckingType;
import com.bilibili.crm.platform.common.income.IncomeProductEnum;
import com.bilibili.crm.platform.common.income.IncomeType;
import com.google.common.collect.Lists;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collection;
import java.util.List;
import java.util.function.BiFunction;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Slf4j
@Service
public class WxCustomerAllImpl implements IWxCustomerService {
    @Autowired
    private CustomerIncomeConfig customerIncomeConfig;
    @Autowired
    private IncomeServiceParamHelper incomeServiceParamHelper;
    @Autowired
    private IncomeServiceQueryHelper incomeServiceQueryHelper;
    @Autowired
    private CustomerIncomeServiceHelper customerServiceHelper;
    @Autowired
    private IncomeEnhanceHelper incomeEnhanceHelper;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private IAchieveFromBillService achieveFromBillService;
    @Autowired
    private AchieveFromWalletService iAchieveFromWalletService;
    @Autowired
    private IncomeQueryService incomeQueryService;

    @Override
    public TeamType team() {
        return TeamType.ALL;
    }

//    @Override
//    public List<ClassifyIncome> queryCategoryIncomeList(IncomeQueryParam param) {
//        //品牌
//        List<CustomerAdIncome> contractIncomes = getUpInComeV2(param);
//        customerServiceHelper.patchCategory(contractIncomes, incomeEnhanceHelper::enhanceContractAccount);
////        //效果
////        List<CustomerAdIncome> rtbIncomes = getUpInCome(param, customerIncomeConfig.getUpConsumeIncomeComposition(), this::getUpCompositionAdInCome);
////        customerServiceHelper.patchCategory(rtbIncomes, incomeEnhanceHelper::enhanceRtbAccount);
//
//        List<CustomerAdIncome> adIncome = Stream.of(contractIncomes).flatMap(Collection::stream).collect(Collectors.toList());
//        return customerServiceHelper.buildCategoryClassifyIncome(adIncome);
//    }

    @Override
    public List<ClassifyIncome> queryRiskCategoryIncomeList(IncomeQueryParam param) {
        return Collections.emptyList();
    }

//    @Override
//    public List<ClassifyIncome> queryCustomerIncomeList(IncomeQueryParam param) {
//        //品牌
//        List<CustomerAdIncome> contractIncomes = getUpInComeV2(param);
//        incomeEnhanceHelper.patchCustomer(contractIncomes, incomeEnhanceHelper::enhanceContractAccount);
//        //效果
////        List<CustomerAdIncome> rtbIncomes = getUpInCome(param, customerIncomeConfig.getUpConsumeIncomeComposition(), this::getUpCompositionAdInCome);
////        incomeEnhanceHelper.patchCustomer(rtbIncomes, incomeEnhanceHelper::enhanceRtbAccount);
//
//        List<CustomerAdIncome> adIncome = Stream.of(contractIncomes).flatMap(Collection::stream).collect(Collectors.toList());
//        return incomeEnhanceHelper.buildCustomerClassifyIncome(adIncome);
//    }


//    @Override
//    public List<CategoryCustomerStatBo> queryCategoryCustomerStat(IncomeQueryParam param) {
//
//        IncomeType incomeType = param.getIncomeType();
//        switch (incomeType) {
//            case CONSUME:
//                return queryFlyCategoryCustomerStat(param);
//            case CONTRACT:
//                return queryCategoryCustomerStatByContract(param);
//            default:
//                return queryCategoryCustomerStatAll(param);
//        }
//
//    }


//    private List<CategoryCustomerStatBo> queryCategoryCustomerStatAll(IncomeQueryParam param) {
//        //近30日的账单
//        customerServiceHelper.buildQueryThirtyDayParam(param);
//
//        List<CustomerAdIncome> contractIncomes = getUpInComeV2(param);
//
//        List<CategoryCustomerStatBo> statList = customerServiceHelper.buildCategoryCustomerStatByContract(contractIncomes);
//
//        return statList;
//    }

//    /**
//     * 客户运营-商单
//     *
//     * @param param
//     * @return
//     */
//    private List<CategoryCustomerStatBo> queryCategoryCustomerStatByContract(IncomeQueryParam param) {
//        //近30日的账单
//        customerServiceHelper.buildQueryThirtyDayParam(param);
//
//        List<CustomerAdIncome> contractIncomes = getUpInCome(param, customerIncomeConfig.getUpContractStatIncomeComposition(), this::getUpCompositionContractStat);
//
//        List<CategoryCustomerStatBo> statList = customerServiceHelper.buildCategoryCustomerStatByContract(contractIncomes);
//
//        return statList;
//    }


//    /**
//     * 客户运营-商业起飞
//     *
//     * @param param
//     * @return
//     */
//    private List<CategoryCustomerStatBo> queryFlyCategoryCustomerStat(IncomeQueryParam param) {
//        //近30日数据
//        customerServiceHelper.buildQueryThirtyDayParam(param);
//        List<CustomerAdIncome> contractIncomes = getUpInCome(param, customerIncomeConfig.getUpConsumeIncomeComposition(), this::getUpCompositionAdInCome);
//
//        //[-180,30] 的历史客户
//        List<Integer> historyCustomer = queryHistoryCustomer(param);
//        List<CategoryCustomerStatBo> statList = customerServiceHelper.buildCategoryCustomerStatByConsume(contractIncomes, historyCustomer);
//
//        return statList;
//    }

//    private List<Integer> queryHistoryCustomer(IncomeQueryParam param) {
//
//        List<CustomerAdIncome> customerAdIncomes = getUpInCome(customerServiceHelper.buildQueryHistoryParam(param), customerIncomeConfig.getUpConsumeIncomeComposition(), this::getUpCompositionAdInCome);
//        //补充客户信息
//        //customerServiceHelper.patchCustomer(customerAdIncomes, customerServiceHelper::enhanceRtbAccount);
//        //补充公司组
//        customerServiceHelper.patchCompanyGroupId(customerAdIncomes);
//
//        return customerAdIncomes.stream().map(CustomerAdIncome::getCompanyGroupId).distinct().collect(Collectors.toList());
//    }

//    /**
//     * up分组成部分收入 handler
//     */
//    private List<CustomerAdIncome> getUpInCome(IncomeQueryParam param, List<CustomerIncomeComposition> compositions,
//                                               BiFunction<CustomerIncomeComposition, IncomeQueryParam, List<CustomerAdIncome>> handler) {
//
//        List<CustomerAdIncome> customerAdIncomes = new ArrayList<>();
//        compositions.forEach(composition -> {
//            List<CustomerAdIncome> adIncomes = handler.apply(composition, param);
//            customerAdIncomes.addAll(adIncomes);
//        });
//        return customerAdIncomes;
//    }


//    public List<CustomerAdIncome> getUpInComeV2(IncomeQueryParam param) {
//
//        List<CustomerAdIncome> customerAdIncomes = new ArrayList<>();
//        List<IncomeComposition> all = Stream.of(IncomeQueryService.CONTENT_TYPE, IncomeQueryService.CONVERSION_TYPE, IncomeQueryService.EXPOSE_TYPE).flatMap(Collection::stream).collect(Collectors.toList());
//        for (IncomeComposition incomeComposition : all) {
//            List<AchievementRtbData> achievementRtbData = incomeQueryService.queryFinicalIncome(IncomeUtil.buildSuperAdmin(), QueryAchieveDto.builder()
//                    .dateBegin(param.getDateBegin())
//                    .dateEnd(param.getDateEnd())
//                    .isContractCheck(false)
//                    .customerIds(null == param.getCustomerId() ? null : Lists.newArrayList(param.getCustomerId()))
//                    .groupIds(null == param.getCompanyGroupId() ? null : Lists.newArrayList(param.getCompanyGroupId()))
//                    .build(), incomeComposition);
//            List<CustomerAdIncome> data = achievementRtbData.stream().map(r -> {
//                CustomerAdIncome customerAdIncome = new CustomerAdIncome();
//                customerAdIncome.setAccountId(r.getAccountId());
//                customerAdIncome.setAgentId(r.getAgentId());
//                customerAdIncome.setCustomerId(r.getCustomerId());
//                customerAdIncome.setCategoryFirstId(r.getCategoryFirstId());
//                customerAdIncome.setAmount(r.getTotalConsume());
//                return customerAdIncome;
//            }).collect(Collectors.toList());
//            customerAdIncomes.addAll(data);
//        }
//        return customerAdIncomes;
//    }


//    public List<CustomerAdIncome> getUpCompositionAdInCome(CustomerIncomeComposition composition, IncomeQueryParam param) {
//        IncomeCompositionQueryParam incomeParam = incomeServiceParamHelper.buildIncomeCompositionQueryParam(param);
//        switch (composition) {
//            case UP_BUSINESS_FLY_CONTRACT:
//                try {
//                    List<ContractAdIncome> contractFly = incomeServiceQueryHelper.getOuterContractAdIncome(incomeParam, () -> {
//                        try {
//                            return customerIncomeConfig.businessFlyContractCondition(param);
//                        } catch (IncomeConditionInvalidException e) {
//                            return null;
//                        }
//                    });
//                    return incomeEnhanceHelper.contract2CustomerAdIncome(contractFly);
//                } catch (IncomeConditionInvalidException e) {
//                    return Collections.emptyList();
//                }
//            case UP_BUSINESS_FLY_CONSUME:
//                List<RtbAdIncome> consumeFly = incomeServiceQueryHelper.getConsumeAdIncome(incomeParam, () -> {
//                    try {
//                        return customerIncomeConfig.businessFlyConsumeCondition(param);
//                    } catch (IncomeConditionInvalidException e) {
//                        return null;
//                    }
//                });
//                return incomeEnhanceHelper.rtb2CustomerAdIncome(consumeFly);
//                //09-07号改版前商单口径
//            case UP_PICK_UP:
//                try {
//                    List<ContractAdIncome> pickUpContractAdIncome = incomeServiceQueryHelper.getOuterContractAdIncome(incomeParam, () -> {
//                        try {
//                            return customerIncomeConfig.upPickUp(param);
//                        } catch (IncomeConditionInvalidException e) {
//                            return null;
//                        }
//                    });
//                    return incomeEnhanceHelper.contract2CustomerAdIncome(pickUpContractAdIncome);
//                } catch (IncomeConditionInvalidException e) {
//                    return Collections.emptyList();
//                }
//            case UP_NON_STANDARD_INVITE:
//                try {
//                    List<ContractAdIncome> inviteRtbAdIncome = incomeServiceQueryHelper.getOuterContractAdIncome(incomeParam, () -> {
//                        try {
//                            return customerIncomeConfig.upInvite(param);
//                        } catch (IncomeConditionInvalidException e) {
//                            return null;
//                        }
//                    });
//                    return incomeEnhanceHelper.contract2CustomerAdIncome(inviteRtbAdIncome);
//                } catch (IncomeConditionInvalidException e) {
//                    return Collections.emptyList();
//                }
//            //花火商单
//            case PICK_UP_ORDER:
//                //商单
//                List<ContractAdIncome> pickUpOrderIncome = buildBillIncome(AdIncomeQueryParam.builder()
//                        .dateBegin(param.getDateBegin())
//                        .dateEnd(param.getDateEnd())
//                        .isChecking(CheckingType.ENABLE.getCode())
//                        .productTypes(Lists.newArrayList(IncomeProductEnum.PICKUP_ORDER.getCode()))
//                        .build());
//
//                return pickUpOrderIncome.stream().map(item -> {
//                            CustomerAdIncome tmp = new CustomerAdIncome();
//                            tmp.setAccountId(item.getAccountId());
//                            tmp.setAgentId(item.getAgentId());
//                            tmp.setCustomerId(item.getCustomerId());
//                            tmp.setAmount(item.getAmount());
//                            return tmp;
//                        }
//                ).collect(Collectors.toList());
//            case CONTENT_FLY:
//                List<RtbAdIncome> contentFlyIncome = buildConsumeIncome(AdIncomeQueryParam.builder()
//                        .dateBegin(param.getDateBegin())
//                        .dateEnd(param.getDateEnd())
//                        .productTypes(Lists.newArrayList(IncomeProductEnum.CONTENT_FLY.getCode()))
//                        .build());
//                return contentFlyIncome.stream().map(
//                        item -> {
//                            CustomerAdIncome tmp = new CustomerAdIncome();
//                            tmp.setAccountId(item.getAccountId());
//                            tmp.setAgentId(item.getAgentId());
//                            tmp.setCustomerId(item.getCustomerId());
//                            tmp.setAmount(item.getAmount());
//                            return tmp;
//                        }
//                ).collect(Collectors.toList());
//            case PERSON_FLY:
//                List<RtbAdIncome> personFlyIncome = buildConsumeIncome(AdIncomeQueryParam.builder()
//                        .dateBegin(param.getDateBegin())
//                        .dateEnd(param.getDateEnd())
//                        .productTypes(Lists.newArrayList(IncomeProductEnum.PERSON_FLY.getCode()))
//                        .build());
//                return personFlyIncome.stream().map(
//                        item -> {
//                            CustomerAdIncome tmp = new CustomerAdIncome();
//                            tmp.setAccountId(item.getAccountId());
//                            tmp.setAgentId(item.getAgentId());
//                            tmp.setCustomerId(item.getCustomerId());
//                            tmp.setAmount(item.getAmount());
//                            return tmp;
//                        }
//                ).collect(Collectors.toList());
//        }
//        return Collections.emptyList();
//    }

//    /**
//     * 账单类客户运营
//     *
//     * @param composition
//     * @param param
//     * @return
//     */
//    private List<CustomerAdIncome> getUpCompositionContractStat(CustomerIncomeComposition composition, IncomeQueryParam param) {
//        IncomeCompositionQueryParam incomeParam = incomeServiceParamHelper.buildIncomeCompositionQueryParam(param);
//        switch (composition) {
//            case UP_PICK_UP:
//                try {
//                    List<ContractAdIncome> pickUpContractAdIncome = incomeServiceQueryHelper.getOuterContractStat(incomeParam, () -> {
//                        try {
//                            return customerIncomeConfig.upPickUp(param);
//                        } catch (IncomeConditionInvalidException e) {
//                            return null;
//                        }
//                    });
//                    return incomeEnhanceHelper.contract2CustomerAdIncome(pickUpContractAdIncome);
//                } catch (IncomeConditionInvalidException e) {
//                    return Collections.emptyList();
//                }
//            case UP_NON_STANDARD_INVITE:
//                try {
//                    List<ContractAdIncome> inviteRtbAdIncome = incomeServiceQueryHelper.getOuterContractStat(incomeParam, () -> {
//                        try {
//                            return customerIncomeConfig.upInvite(param);
//                        } catch (IncomeConditionInvalidException e) {
//                            return null;
//                        }
//                    });
//                    return incomeEnhanceHelper.contract2CustomerAdIncome(inviteRtbAdIncome);
//                } catch (IncomeConditionInvalidException e) {
//                    return Collections.emptyList();
//                }
//        }
//        return Collections.emptyList();
//    }


//    /**
//     * 消耗类客户运营
//     *
//     * @param composition
//     * @param param
//     * @return
//     */
//    private List<CustomerAdIncome> getUpCompositionConsumeStat(CustomerIncomeComposition composition, IncomeQueryParam param) {
//        IncomeCompositionQueryParam incomeParam = incomeServiceParamHelper.buildIncomeCompositionQueryParam(param);
//        switch (composition) {
//
//            case UP_BUSINESS_FLY_CONSUME:
//                List<RtbAdIncome> consumeFly = incomeServiceQueryHelper.getConsumeAdIncome(incomeParam, () -> {
//                    try {
//                        return customerIncomeConfig.businessFlyConsumeCondition(param);
//                    } catch (IncomeConditionInvalidException e) {
//                        return null;
//                    }
//                });
//                return incomeEnhanceHelper.rtb2CustomerAdIncome(consumeFly);
//        }
//        return Collections.emptyList();
//    }

//    public List<ContractAdIncome> buildQuarterBillIncome(AdIncomeQueryParam queryParam, AggregationCondition condition) {
//
//        //适用于业绩看板
//        try {
//            AchieveTimeParam param = incomeTimeUtil.getBeginEndTimeBefore(queryParam.getDateBegin(), queryParam.getDateEnd(), Utils.getYesteday());
//            queryParam.setDateBegin(param.getDateBegin());
//            queryParam.setDateEnd(param.getDateEnd());
//        } catch (IncomeConditionInvalidException e) {
//            log.error("error ", e);
//            return java.util.Collections.emptyList();
//        }
//
//        List<ContractAdIncome> closedBill = achieveFromBillService.queryCloseBillFormEs(queryParam, condition);
//        List<ContractAdIncome> unClosedBill = achieveFromBillService.queryUnCloseBillFormEs(queryParam, condition);
//        return Stream.of(closedBill, unClosedBill).flatMap(Collection::stream).peek(r -> r.setAmount(r.getAmount().multiply(BigDecimal.valueOf(100L)))).collect(Collectors.toList());
//    }

//    //分当日 和季度数据（包含关账和非关账）
//    private List<ContractAdIncome> buildBillIncome(AdIncomeQueryParam queryParam) {
//        AggregationCondition condition = AggregationConditionBuilder.build()
//                .add(AggregationField.builder()
//                        .aggType(AggregationBucketType.TERMS)
//                        .field("accountId")
//                        .order(false)
//                        .build())
//                .add(AggregationField.builder()
//                        .aggType(AggregationMetricType.SUM)
//                        .field("specialRedPacketConsume")
//                        .format("#")
//                        .build())
//                .add(AggregationField.builder()
//                        .aggType(AggregationMetricType.SUM)
//                        .field("cashConsume")
//                        .format("#")
//                        .build())
//                .add(AggregationField.builder()
//                        .aggType(AggregationMetricType.SUM)
//                        .field("redPacketConsume")
//                        .format("#")
//                        .build())
//                .add(AggregationField.builder()
//                        .aggType(AggregationMetricType.SUM)
//                        .field("consumeAmount")
//                        .format("#")
//                        .build());
//
//        queryParam.setMigrateBizScene(MigrateBizScene.QueryBillExtendService_accountIdSpecialRedPacketConsumeCashConsumeRedPacketConsumeConsumeAmount.getName());
//
//        if (queryParam.getDateEnd().getTime() - queryParam.getDateBegin().getTime() < 24 * 3600000) {
//            List<AchievementRtbData> incomeData = achieveFromBillService.getCheckingBillIncomeByWideES(queryParam, condition);
//            return incomeData.stream().map(item -> {
//                ContractAdIncome tmp = new ContractAdIncome();
//                tmp.setAgentId(item.getAgentId());
//                tmp.setAccountId(item.getAccountId());
//                tmp.setAmount(item.getTotalConsume().multiply(BigDecimal.valueOf(100L)));
//                return tmp;
//            }).collect(Collectors.toList());
//        }
//        return buildQuarterBillIncome(queryParam, condition);
//    }

//    private List<RtbAdIncome> buildConsumeIncome(AdIncomeQueryParam queryParam) {
//        List<AchievementRtbData> walletSumByWideES = iAchieveFromWalletService.getWalletAggByAccountAgentByWideES(queryParam);
//        return walletSumByWideES.stream().map(item -> {
//            RtbAdIncome rtbAdIncome = new RtbAdIncome();
//            rtbAdIncome.setAccountId(item.getAccountId());
//            rtbAdIncome.setAgentId(item.getAgentId());
//            rtbAdIncome.setCustomerId(item.getCustomerId());
//            rtbAdIncome.setAmount(item.getTotalConsume().multiply(BigDecimal.valueOf(100L)));
//            return rtbAdIncome;
//        }).collect(Collectors.toList());
//    }

}
