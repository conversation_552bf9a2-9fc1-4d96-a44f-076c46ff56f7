package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCustomerDomainPo;
import com.bilibili.crm.platform.biz.po.CrmCustomerDomainPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmCustomerDomainDao {
    long countByExample(CrmCustomerDomainPoExample example);

    int deleteByExample(CrmCustomerDomainPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmCustomerDomainPo record);

    int insertBatch(List<CrmCustomerDomainPo> records);

    int insertUpdateBatch(List<CrmCustomerDomainPo> records);

    int insert(CrmCustomerDomainPo record);

    int insertUpdateSelective(CrmCustomerDomainPo record);

    int insertSelective(CrmCustomerDomainPo record);

    List<CrmCustomerDomainPo> selectByExample(CrmCustomerDomainPoExample example);

    CrmCustomerDomainPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmCustomerDomainPo record, @Param("example") CrmCustomerDomainPoExample example);

    int updateByExample(@Param("record") CrmCustomerDomainPo record, @Param("example") CrmCustomerDomainPoExample example);

    int updateByPrimaryKeySelective(CrmCustomerDomainPo record);

    int updateByPrimaryKey(CrmCustomerDomainPo record);
}