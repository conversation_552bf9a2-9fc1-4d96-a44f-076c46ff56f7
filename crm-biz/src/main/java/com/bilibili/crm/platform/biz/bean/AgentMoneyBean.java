package com.bilibili.crm.platform.biz.bean;

import com.bilibili.crm.platform.api.stat.dto.AgentMoneyDataDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-11-02
 **/
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentMoneyBean {
    /**
     * 代理商的账号ID
     */
    private Integer accountId;

    /**
     * 代理商的代理商ID
     */
    private Integer agentId;

    /**
     * 代理商的代理商名
     */
    private String agentName;

    private String dateStr;

    /**
     * 代理商资金数据
     */
    private AgentMoneyDataDto agentDto;

    /**
     * 代理商下属广告主资金数据集合
     */
    private List<AgentMoneyDataDto> childrenDto;
}
