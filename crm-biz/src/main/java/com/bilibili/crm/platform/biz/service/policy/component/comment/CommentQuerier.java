package com.bilibili.crm.platform.biz.service.policy.component.comment;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.automation.AttachmentResultDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CommonAttachmentUploadDto;
import com.bilibili.crm.platform.api.policy.dto.*;
import com.bilibili.crm.platform.api.policy.enums.FlowNodeStatusEnum;
import com.bilibili.crm.platform.api.policy.enums.FlowTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.NodeOperateTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowNodeTypeEnum;
import com.bilibili.crm.platform.biz.Component.attachment.AttachmentUploadFacade;
import com.bilibili.crm.platform.biz.dao.CrmBrandReturnDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.flowable.ActHiActInstRepo;
import com.bilibili.crm.platform.biz.repo.flowable.ActHiCommentRepo;
import com.bilibili.crm.platform.biz.repo.policy.CrmContractPolicyInfoRepo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyRepo;
import com.bilibili.crm.platform.biz.service.policy.component.user.PreExamineFlowUserQuerierImpl;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.AttachmentUploadBizModuleEnum;
import com.bilibili.crm.platform.common.CrmConstant;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.TaskService;
import org.flowable.engine.task.Attachment;
import org.flowable.task.api.Task;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * 评论查询器
 *
 * <AUTHOR>
 * @date 2021/9/6 下午5:11
 */
@Slf4j
@Component
public class CommentQuerier {

    @Autowired
    private TaskService taskService;
    @Autowired
    private CrmContractPolicyInfoRepo crmContractPolicyInfoRepo;
    @Autowired
    private CrmReturnApplyRepo crmReturnApplyRepo;
    @Autowired
    private ActHiCommentRepo actHiCommentRepo;
    @Autowired
    private AttachmentUploadFacade attachmentUploadFacade;
    @Autowired
    private ActHiActInstRepo actHiActInstRepo;
    @Autowired
    private PreExamineFlowUserQuerierImpl preExamineFlowUserQuerier;
    @Autowired
    private CrmBrandReturnDao crmBrandReturnDao;

    public PolicyFlowProcessDto queryComments(Integer flowType, String processInstanceId) {
        FlowTypeEnum flowTypeEnum = FlowTypeEnum.getByCode(flowType);
        PolicyFlowProcessDto policyFlowProcessDto = PolicyFlowProcessDto.builder().build();

        AttachmentUploadBizModuleEnum attachmentUploadBizModuleEnum = null;
        if (FlowTypeEnum.CONTRACT_FLOW.equals(flowTypeEnum)) {
            attachmentUploadBizModuleEnum = AttachmentUploadBizModuleEnum.POLICY_FLOW_NODE_PIC;
            CrmContractPolicyInfoPo contractPolicyInfoPo = crmContractPolicyInfoRepo.queryByProcessInstanceId(processInstanceId);
            if (contractPolicyInfoPo == null) {
                throw new ServiceRuntimeException("政策不存在！");
            }
        } else if (FlowTypeEnum.BRAND_RETURN.equals(flowTypeEnum)) {
            attachmentUploadBizModuleEnum = AttachmentUploadBizModuleEnum.BRAND_RETURN_COMMENT;
            CrmBrandReturnPoExample crmBrandReturnPoExample = new CrmBrandReturnPoExample();
            crmBrandReturnPoExample.createCriteria().andProcessInstanceIdEqualTo(processInstanceId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<CrmBrandReturnPo> crmBrandReturnPos = crmBrandReturnDao.selectByExample(crmBrandReturnPoExample);
            Assert.isTrue(org.apache.commons.collections4.CollectionUtils.isNotEmpty(crmBrandReturnPos), "品牌返货不存在");
        } else {
            attachmentUploadBizModuleEnum = AttachmentUploadBizModuleEnum.RETURN_FLOW_NODE_PIC;
            CrmReturnApplyPo crmReturnApplyPo = crmReturnApplyRepo.queryByProcessInstanceId(processInstanceId);
            if (crmReturnApplyPo == null) {
                throw new ServiceRuntimeException("返货申请不存在！");
            }
        }

        // 流程当前的任务
        Task curTask = null;
        List<Task> tasks = taskService.createTaskQuery().processInstanceId(processInstanceId).orderByTaskCreateTime().desc().list();
        if (!CollectionUtils.isEmpty(tasks)) {
            curTask = tasks.get(0);
        }

        // 获取 action = addComment 的评论
        List<ACTHICOMMENTPo> acthicommentPos =
                actHiCommentRepo.queryHiAddCommentTypeCommentsByProcessInstId(processInstanceId);

        // 创建于修改的评论
        List<ACTHICOMMENTPo> createOrUpdateComments = acthicommentPos.stream().filter(t -> NodeOperateTypeEnum.getCreateOrUpdateOperateTypes().contains(t.getType())).collect(Collectors.toList());
        List<String> returnNodeTaskIds =
                acthicommentPos.stream().filter(t -> StringUtils.isNotEmpty(t.getTaskId())).map(t -> t.getTaskId()).distinct().collect(Collectors.toList());
        Map<String, ACTHIACTINSTPo> actHiActInstPoMap = actHiActInstRepo.queryMapByTaskIds(returnNodeTaskIds);
        List<FlowableCommentDto> commentDtos = new ArrayList<>();
        for (ACTHICOMMENTPo curComment : acthicommentPos) {
            // 查找 create or update 的评论
            Integer eqIndex = 0;
            for (int i = 0; i < createOrUpdateComments.size(); i++) {
                if (!createOrUpdateComments.get(i).getId().equals(curComment.getId())) {
                    continue;
                }
                eqIndex = i;
            }
            // create or update 的前一个评论
            ACTHICOMMENTPo preCommmentPo = ACTHICOMMENTPo.builder().build();
            if (eqIndex > 0) {
                preCommmentPo = createOrUpdateComments.get(eqIndex - 1);
            }

            // message 的格式 操作:内容，解析获取内容
            String message = curComment.getMessage();
            String msgContent = null;
            if (StringUtils.isNotEmpty(message)) {
                String[] msgArr = curComment.getMessage().split(":");
                if (msgArr != null && msgArr.length > 1) {
//                    msgContent = msgArr[1];
                    msgContent = message.substring(message.indexOf(":") + 1);
                }
            }

            // create or update 获取 diffMap
            Map<String, Object> nodeDiffMap = null;
            if (NodeOperateTypeEnum.UPDATE.getCode().equals(curComment.getType())) {
                nodeDiffMap = getNodeDiffMap(curComment, preCommmentPo);
            }

            String curFullMsg = new String(curComment.getFullMsg());
            PolicyFlowProcessSaveDto curDto = JSON.parseObject(curFullMsg, PolicyFlowProcessSaveDto.class);

            FlowableCommentDto flowableCommentDto = FlowableCommentDto.builder()
                    .taskId(curComment.getTaskId())
                    .processInstanceId(curComment.getProcInstId())
                    .userId(curComment.getUserId())
                    .userName(curComment.getUserId())
                    .nodeTaskStatus(getNodeTaskStatus(curTask, curComment))
                    .diffMap(nodeDiffMap)
                    .operateTypeCode(curComment.getType())
                    .operateTypeName(NodeOperateTypeEnum.getByCode(curComment.getType()).getDesc())
                    .ctime(curComment.getTime())
                    .message(msgContent)
//                    .fullMsg(new String(comment.getFullMsg()))
                    .noticeUsers(curDto.getNoticeUsers())
                    .preExampleUsers(curDto.getPreExamineUsers())
                    .build();

            // 如果是退回获取退回节点信息
            String contentJsonStr = new String(curComment.getFullMsg());

            List<String> addSignUsers = null;
            List<CommonAttachmentUploadDto> commonAttachmentUploadDtos = null;
            Integer attachmentRelId = null;
            if (NodeOperateTypeEnum.getCreateOrUpdateOperateTypes().contains(flowableCommentDto.getOperateTypeCode())) {
                PolicyFlowProcessSaveDto flowProcessSaveDto = JSON.parseObject(contentJsonStr, PolicyFlowProcessSaveDto.class);
                commonAttachmentUploadDtos = flowProcessSaveDto.getCommonAttachmentUploadDtos();
                attachmentRelId = flowProcessSaveDto.getId();
            } else {
                PolicyFlowProcessActionDto processActionDto = JSON.parseObject(contentJsonStr, PolicyFlowProcessActionDto.class);
                addSignUsers = processActionDto.getAddSignUsers();
                commonAttachmentUploadDtos = processActionDto.getCommonAttachmentUploadDtos();
                if (StringUtils.isNotEmpty(flowableCommentDto.getTaskId())) {
                    attachmentRelId = Integer.parseInt(flowableCommentDto.getTaskId());
                }
            }
            if (NodeOperateTypeEnum.RETURN_NODE.getCode().equals(curComment.getType())) {
                PolicyFlowProcessActionDto processActionDto = JSON.parseObject(contentJsonStr, PolicyFlowProcessActionDto.class);
                ACTHIACTINSTPo returnActHiActInstPo = actHiActInstPoMap.get(processActionDto.getTargetNodeId());
                if (returnActHiActInstPo != null) {
                    String userNames = returnActHiActInstPo.getAssignee();
                    if (PolicyFlowNodeTypeEnum.PRE_EXAMINE.getName().equals(returnActHiActInstPo.getActName()) && FlowTypeEnum.BRAND_RETURN != processActionDto.getFlowTypeEnum()) {
                        List<String> preExampleUsers = preExamineFlowUserQuerier.queryUsers(flowTypeEnum, curComment.getProcInstId(), null);
                        userNames = preExampleUsers.stream().collect(Collectors.joining(","));
                    }
                    PolicyFlowNodeDto policyFlowNodeDto = PolicyFlowNodeDto.builder()
                            .taskNodeId(processActionDto.getTargetNodeId())
                            .taskNodeName(returnActHiActInstPo.getActName())
                            .userName(userNames)
                            .startTime(returnActHiActInstPo.getStartTime())
                            .endTime(returnActHiActInstPo.getEndTime())
                            .build();
                    if (StringUtils.isNotEmpty(returnActHiActInstPo.getActName())) {
                        policyFlowNodeDto.setTaskNameChinese(PolicyFlowNodeTypeEnum.getByName(returnActHiActInstPo.getActName()).getDesc());
                    }
                    flowableCommentDto.setReturnNodeInfo(policyFlowNodeDto);
                }
            } else if (NodeOperateTypeEnum.ADD_SIGN.getCode().equals(curComment.getType())) {
                // 获取加签人
                flowableCommentDto.setAddSignUsers(addSignUsers);
            }

            List<Attachment> taskAttachments = taskService.getTaskAttachments(curComment.getTaskId());
            flowableCommentDto.setAttachmentIds(taskAttachments.stream().map(t -> t.getId()).collect(Collectors.toList()));

            // todo 优化 附件信息 不同的流程类型
            if (!CollectionUtils.isEmpty(commonAttachmentUploadDtos)) {
                if (attachmentRelId != null) {
                    List<AttachmentResultDto> attachmentResultDtos =
                            attachmentUploadFacade.queryAttachmentInfo(attachmentRelId, attachmentUploadBizModuleEnum);
                    flowableCommentDto.setAttachmentResultDtos(attachmentResultDtos);
                }
            }
            commentDtos.add(flowableCommentDto);
        }

        policyFlowProcessDto.setProcessInstanceId(processInstanceId);
        policyFlowProcessDto.setCommentDtoList(commentDtos);
        return policyFlowProcessDto;
    }

    /**
     * 获取节点内容的 diff
     *
     * @param curCommentPo
     * @param preCommentPo
     * @return
     */
    private Map<String, Object> getNodeDiffMap(ACTHICOMMENTPo curCommentPo, ACTHICOMMENTPo preCommentPo) {
        Map<String, Object> diffMap = new HashMap<>();

        // 解析 full msg
        String curFullMsg = new String(curCommentPo.getFullMsg());
        PolicyFlowProcessSaveDto curDto = JSON.parseObject(curFullMsg, PolicyFlowProcessSaveDto.class);
        if (curDto != null) {
            curDto.setNodeOperateTypeEnum(null);
        }

        // 存在前一个对比 comment
        if (preCommentPo != null && preCommentPo.getFullMsg() != null) {
            try {
                String preFullMsg = new String(preCommentPo.getFullMsg());
                PolicyFlowProcessSaveDto preDto = JSON.parseObject(preFullMsg, PolicyFlowProcessSaveDto.class);
                if (preDto != null) {
                    preDto.setNodeOperateTypeEnum(null);
                }

                // 对比两个解析后的 对象
                Map<String, Object> maps = CrmUtils.compareObject(curDto, preDto);
                diffMap.put(CrmConstant.FORMER, JSON.parseObject(JSON.toJSONString(maps), Object.class));
            } catch (Exception e) {
                log.error("=====> compareObject error, commentId:{}, e:{}", curCommentPo.getId(), e);
            }
        } else {
            diffMap.put(CrmConstant.FORMER, null);
        }
        diffMap.put(CrmConstant.CURRENT, curDto);
        return diffMap;
    }

    /**
     * 获取评论节点的状态
     *
     * @param curTask
     * @param comment
     * @return
     */
    private Integer getNodeTaskStatus(Task curTask, ACTHICOMMENTPo comment) {
        if (true) {
            return FlowNodeStatusEnum.DONE.getCode();
        }

        // 没有运行中节点，则所有的都为已完成
        if (curTask == null) {
            return FlowNodeStatusEnum.DONE.getCode();
        }
        // 该评论没有taskId，则为已完成
        if (comment.getTaskId() == null) {
            return FlowNodeStatusEnum.DONE.getCode();
        } else {
            // 评论的节点在当前节点之前，则为已完成；之后，则为待处理(现根据时间比较，时间一样，根据 task id 比较)
            if (comment.getTime().compareTo(curTask.getCreateTime()) > 0) {
                return FlowNodeStatusEnum.WAIT.getCode();
            } else if (comment.getTime().compareTo(curTask.getCreateTime()) == 0) {
                if (comment.getTaskId().compareTo(curTask.getId()) > 0) {
                    return FlowNodeStatusEnum.WAIT.getCode();
                } else if (comment.getTaskId().compareTo(curTask.getId()) == 0) {
                    return FlowNodeStatusEnum.DOING.getCode();
                } else {
                    return FlowNodeStatusEnum.DONE.getCode();
                }
            } else {
                return FlowNodeStatusEnum.DONE.getCode();
            }
        }
    }
}
