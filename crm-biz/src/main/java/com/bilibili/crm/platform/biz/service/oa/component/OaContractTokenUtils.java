package com.bilibili.crm.platform.biz.service.oa.component;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.dianping.cat.Cat;
import com.google.gson.annotations.SerializedName;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.io.Serializable;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/13
 **/
@Slf4j
@Component
public class OaContractTokenUtils {

    @Value("${oa.contract.token.url:http://uat-eeapi.bilibili.co/open-api/auth/v1/jwt?destClient=ops.fin-api.contract&client=sycpb.cpm.crm-portal&secret=%s}")
    private String tokenUrl;


    @Value("${oa.contract.secret:nc2JhP-4Ihj6u-O8q2DaFCbQQNia6L6Xvcr99wj9ioc=}")
    private String secret;

    @Resource(name = "stringRedisTemplate")
    private StringRedisTemplate stringRedisTemplate;

    /**
     * 公钥 redis key
     */
    private final static String SECRET_REDIS_KEY = "crm_oa_contract_token_value";


    public String buildOaFlowToken(int retry) {
        Assert.isTrue(retry < 10, "重试次数不能大于10");
        return buildOaFlowToken(0, retry, false);
    }

    public String buildOaFlowToken(int retry, boolean refresh) {
        Assert.isTrue(retry < 10, "重试次数不能大于10");
        return buildOaFlowToken(0, retry, refresh);
    }

    private String buildOaFlowToken(int i, int retry, boolean refresh) {

        String crmOaFlowTokenValue = stringRedisTemplate.opsForValue().get(SECRET_REDIS_KEY);
        TokenData tokenData = GsonUtils.getGson().fromJson(crmOaFlowTokenValue, TokenData.class);


        if (!refresh && null != tokenData && System.currentTimeMillis() - tokenData.getCtime() < (tokenData.getExpiredReal() - 600) * 1000) {
            return tokenData.getToken();
        }

        CommonReturn<TokenData> dataCommonReturn = OkHttpUtils.get(String.format(tokenUrl, secret)).callForObject(new TypeToken<CommonReturn<TokenData>>() {
        });

        Cat.logEvent("OA_CONTRACT_TOKEN", "request", "0", String.format(tokenUrl, secret));
        log.info("return {}", dataCommonReturn);

        if (0 == dataCommonReturn.getCode()) {
            tokenData = dataCommonReturn.getData();
            tokenData.setCtime(System.currentTimeMillis());
            stringRedisTemplate.opsForValue().set(SECRET_REDIS_KEY, JSON.toJSONString(tokenData));
            return tokenData.getToken();
        } else {
            if (i > retry) {
                log.error("error retry {}", dataCommonReturn);
                return null;
            }
            return buildOaFlowToken(i += 1, retry, refresh);
        }

    }

    public static class CommonReturn<T> implements Serializable {

        private static final long serialVersionUID = 64983486444195092L;

        private Integer code;

        private String message;

        private T data;

        public Integer getCode() {
            return code;
        }

        public void setCode(Integer code) {
            this.code = code;
        }

        public String getMessage() {
            return message;
        }

        public void setMessage(String message) {
            this.message = message;
        }

        public T getData() {
            return data;
        }

        public void setData(T data) {
            this.data = data;
        }
    }

    @Data
    public class TokenData implements Serializable {

        private static final long serialVersionUID = -4747442032048614396L;

        @SerializedName("expired_in(s)")
        private Integer expired;

        @SerializedName("expired")
        private Integer expiredReal;

        private String token;

        private Long ctime;
    }
}
