package com.bilibili.crm.platform.biz.service.wallet.budget;

import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.AccountWalletDayLogDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.dto.QueryAccountWalletLogParam;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.wallet.service.IAccountWalletDayLogService;
import com.bilibili.crm.platform.biz.service.account.arrear.AccountArrearDelegateService;
import com.bilibili.crm.platform.common.WalletOperationType;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.report.platform.api.dto.StatAccountChargeDto;
import com.bilibili.report.platform.api.soa.ISoaAdStatAccountService;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * @author: brady
 * @time: 2024/12/9 17:38
 */
@Service
@Slf4j
public class BudgetFixService {
    @Autowired
    private IFastQueryAccountService fastQueryAccountService;
    @Autowired
    private BudgetOutExtraDelegateService budgetOutExtraDelegateService;
    @Autowired
    private ISoaAdStatAccountService soaAdStatAccountService;
    @Autowired
    private IAccountWalletDayLogService accountWalletDayLogService;
    @Autowired
    private AccountArrearDelegateService delegateService;

    public void test(){

    }

//    public void accountOutBudgetCompensation(Timestamp date,List<Integer> accountIds,Integer flag){
//        //T+2 计费超阈值数据
//        List<StatAccountChargeDto> accountChargesSoa = soaAdStatAccountService.queryStatAccountBudgetChargedCost(date);
//
//        if (CollectionUtils.isEmpty(accountChargesSoa)) {
//            return;
//        }
//
//        List<StatAccountChargeDto> accountCharges = accountChargesSoa.stream().filter(it -> accountIds.equals(it.getAccountId())).collect(Collectors.toList());
//        List<Integer> budgetAccountIds = accountCharges.stream().map(StatAccountChargeDto::getAccountId).collect(Collectors.toList());
//
//        //账号
//        List<AccountBaseDto> accounts = fastQueryAccountService.fetch(QueryAccountParam.builder().accountIds(budgetAccountIds).build(),
//                AccountFieldMapping.accountId, AccountFieldMapping.dependencyAgentId, AccountFieldMapping.isInner, AccountFieldMapping.financeType);
//
//        Map<Integer, AccountBaseDto> accountMap = accounts.stream().collect(Collectors.toMap(AccountBaseDto::getAccountId, Function.identity()));
//        //crm扣款
//        List<AccountWalletDayLogDto> accountWalletDayLogs = accountWalletDayLogService.queryWalletDayLogs(QueryAccountWalletLogParam.builder()
//                .accountIds(budgetAccountIds)
//                .date(date)
//                .operationType(WalletOperationType.CONSUME.getType())
//                .build());
//        Map<Integer, List<AccountWalletDayLogDto>> accountWalletMap = accountWalletDayLogs.stream().collect(Collectors.groupingBy(AccountWalletDayLogDto::getAccountId));
//
//
//        if(1==flag){
//            accountCharges.forEach(item -> {
//                    //账号
//                    AccountBaseDto account = accountMap.get(item.getAccountId());
//                    if (null == account) {
//                        return;
//                    }
//                    //扣款
//                    List<AccountWalletDayLogDto> accountWalletDayLog = accountWalletMap.getOrDefault(item.getAccountId(), Collections.emptyList());
//
//                    depositOutBudget(date, account, item, accountWalletDayLog);
//
//            });
//        }
//
//        return;
//    }
}



