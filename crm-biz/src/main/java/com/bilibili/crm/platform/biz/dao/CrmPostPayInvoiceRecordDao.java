package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmPostPayInvoiceRecordPo;
import com.bilibili.crm.platform.biz.po.CrmPostPayInvoiceRecordPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmPostPayInvoiceRecordDao {
    long countByExample(CrmPostPayInvoiceRecordPoExample example);

    int deleteByExample(CrmPostPayInvoiceRecordPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmPostPayInvoiceRecordPo record);

    int insertBatch(List<CrmPostPayInvoiceRecordPo> records);

    int insertUpdateBatch(List<CrmPostPayInvoiceRecordPo> records);

    int insert(CrmPostPayInvoiceRecordPo record);

    int insertUpdateSelective(CrmPostPayInvoiceRecordPo record);

    int insertSelective(CrmPostPayInvoiceRecordPo record);

    List<CrmPostPayInvoiceRecordPo> selectByExample(CrmPostPayInvoiceRecordPoExample example);

    CrmPostPayInvoiceRecordPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmPostPayInvoiceRecordPo record, @Param("example") CrmPostPayInvoiceRecordPoExample example);

    int updateByExample(@Param("record") CrmPostPayInvoiceRecordPo record, @Param("example") CrmPostPayInvoiceRecordPoExample example);

    int updateByPrimaryKeySelective(CrmPostPayInvoiceRecordPo record);

    int updateByPrimaryKey(CrmPostPayInvoiceRecordPo record);
}