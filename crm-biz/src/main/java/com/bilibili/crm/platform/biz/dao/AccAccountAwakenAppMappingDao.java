package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.AccAccountAwakenAppMappingPo;
import com.bilibili.crm.platform.biz.po.AccAccountAwakenAppMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

@Repository
public interface AccAccountAwakenAppMappingDao {
    long countByExample(AccAccountAwakenAppMappingPoExample example);

    int deleteByExample(AccAccountAwakenAppMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AccAccountAwakenAppMappingPo record);

    int insertBatch(List<AccAccountAwakenAppMappingPo> records);

    int insertUpdateBatch(List<AccAccountAwakenAppMappingPo> records);

    int insert(AccAccountAwakenAppMappingPo record);

    int insertUpdateSelective(AccAccountAwakenAppMappingPo record);

    int insertSelective(AccAccountAwakenAppMappingPo record);

    List<AccAccountAwakenAppMappingPo> selectByExample(AccAccountAwakenAppMappingPoExample example);

    AccAccountAwakenAppMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccAccountAwakenAppMappingPo record, @Param("example") AccAccountAwakenAppMappingPoExample example);

    int updateByExample(@Param("record") AccAccountAwakenAppMappingPo record, @Param("example") AccAccountAwakenAppMappingPoExample example);

    int updateByPrimaryKeySelective(AccAccountAwakenAppMappingPo record);

    int updateByPrimaryKey(AccAccountAwakenAppMappingPo record);
}