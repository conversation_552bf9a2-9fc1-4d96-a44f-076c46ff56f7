package com.bilibili.crm.platform.biz.service.customer.customeraudit;

import com.bilibili.crm.platform.biz.common.AbstractBeanFactory;
import com.bilibili.crm.platform.biz.common.WorkOrderMappingType;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

/**
 * <AUTHOR>
 * @date 2021/3/17 1:37 下午
 */
@Component
public class CustomerAuditPassFactory extends AbstractBeanFactory<BaseCustomerAuditPass> {

    public static final String CREATE_CUSTOMER_AUDIT_PASS = "CreateCustomerAuditPass";
    public static final String EDIT_CUSTOMER_AUDIT_PASS = "EditCustomerAuditPass";

    @Override
    public BaseCustomerAuditPass getDefault() {
        return null;
    }

    public BaseCustomerAuditPass getByWorkOrderType(WorkOrderMappingType workOrderMappingType) {
        BaseCustomerAuditPass baseCustomerAuditPass = null;
        if (WorkOrderMappingType.CREATE_CUSTOMER.equals(workOrderMappingType)) {
            baseCustomerAuditPass = this.getByName(CREATE_CUSTOMER_AUDIT_PASS);
        } else if (WorkOrderMappingType.UPDATE_CUSTOMER.equals(workOrderMappingType)) {
            baseCustomerAuditPass = this.getByName(EDIT_CUSTOMER_AUDIT_PASS);
        }
        Assert.notNull(baseCustomerAuditPass, "工单类型不存在！");
        return baseCustomerAuditPass;
    }
}
