package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmCustomerDepartmentPo implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 部门名称
     */
    private String deptName;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 是否删除 0 未删除 1 删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 更新人
     */
    private String updater;

    /**
     * 上级部门id
     */
    private Long superiorDeptId;

    private static final long serialVersionUID = 1L;
}