package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.finance.dto.RefundAttachmentDto;
import com.bilibili.crm.platform.api.finance.service.IAccountRefundAttachmentService;
import com.bilibili.crm.platform.biz.dao.CrmAccountRefundAttachmentDao;
import com.bilibili.crm.platform.biz.po.CrmAccountRefundAttachmentPo;
import com.bilibili.crm.platform.biz.po.CrmAccountRefundAttachmentPoExample;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
public class AccountRefundAttachmentService implements IAccountRefundAttachmentService {

    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());

    @Autowired
    private CrmAccountRefundAttachmentDao crmAccountRefundAttachmentDao;
    @Override
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public void create(RefundAttachmentDto dto) {
        LOGGER.info("AccountRefundAttachmentService create param RefundAttachmentDto-{}",dto);
        this.validateRefundAttachment(dto);
        CrmAccountRefundAttachmentPo po = new CrmAccountRefundAttachmentPo();
        po.setOssKey(dto.getOssKey());
        po.setFileName(dto.getFileName());
        po.setRefundId(dto.getRefundId());
        Integer result = crmAccountRefundAttachmentDao.insertSelective(po);
        Assert.isTrue(result > 0,"退款附件插入失败");
    }

    @Override
    public List<RefundAttachmentDto> getByRefundId(Integer refundId) {
        Assert.notNull(refundId,"扣款记录ID不可为空");
        CrmAccountRefundAttachmentPoExample crmAccountRefundAttachmentPoExample = new CrmAccountRefundAttachmentPoExample();
        crmAccountRefundAttachmentPoExample.or().andRefundIdEqualTo(refundId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmAccountRefundAttachmentPo> crmAccountRefundAttachmentPos = crmAccountRefundAttachmentDao.selectByExample(crmAccountRefundAttachmentPoExample);
        return this.crmAccountRefundAttachmentPos2Dtos(crmAccountRefundAttachmentPos);
    }

    @Override
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public void deletedByRefundId(Integer refundId) {
        Assert.notNull(refundId,"扣款记录ID不可为空");
        CrmAccountRefundAttachmentPoExample crmAccountRefundAttachmentPoExample = new CrmAccountRefundAttachmentPoExample();
        crmAccountRefundAttachmentPoExample.or().andRefundIdEqualTo(refundId);
        CrmAccountRefundAttachmentPo po = new CrmAccountRefundAttachmentPo();
        po.setIsDeleted(IsDeleted.DELETED.getCode());
        crmAccountRefundAttachmentDao.updateByExampleSelective(po,crmAccountRefundAttachmentPoExample);
    }

    @Override
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class, timeout = 30)
    public void updateRefundAttachment(Integer refundId, List<RefundAttachmentDto> dtos) {
        Assert.notNull(refundId,"扣款记录ID不可为空");
        Assert.notEmpty(dtos,"附件信息不可为空");
        this.deletedByRefundId(refundId);
        for(RefundAttachmentDto refundAttachmentDto :dtos){
            refundAttachmentDto.setRefundId(refundId);
            this.create(refundAttachmentDto);
        }
    }

    @Override
    public Map<Integer, List<RefundAttachmentDto>> getMapByRefundIds(List<Integer> refundIds) {
        if(CollectionUtils.isEmpty(refundIds)){
            return Collections.emptyMap();
        }
        CrmAccountRefundAttachmentPoExample crmAccountRefundAttachmentPoExample = new CrmAccountRefundAttachmentPoExample();
        crmAccountRefundAttachmentPoExample.or().andRefundIdIn(refundIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmAccountRefundAttachmentPo> crmAccountRefundAttachmentPos = crmAccountRefundAttachmentDao.selectByExample(crmAccountRefundAttachmentPoExample);
        Map<Integer, List<RefundAttachmentDto>> result = crmAccountRefundAttachmentPos.stream()
                .collect(Collectors.groupingBy(CrmAccountRefundAttachmentPo::getRefundId, Collectors.mapping(po ->this.crmAccountRefundAttachmentPo2Dto(po), Collectors.toList())));
        return result;
    }

    private  RefundAttachmentDto crmAccountRefundAttachmentPo2Dto(CrmAccountRefundAttachmentPo po){
        return  RefundAttachmentDto.builder()
                .ossKey(po.getOssKey())
                .fileName(po.getFileName())
                .id(po.getId())
                .refundId(po.getRefundId())
                .build();
    }

    private  List<RefundAttachmentDto> crmAccountRefundAttachmentPos2Dtos(List<CrmAccountRefundAttachmentPo> pos){
        return pos.stream().map(po -> this.crmAccountRefundAttachmentPo2Dto(po)).collect(Collectors.toList());
    }
    private void validateRefundAttachment(RefundAttachmentDto dto) {
        Assert.notNull(dto,"附件信息不可为空");
        Assert.notNull(dto.getRefundId(), "退款ID不可为空");
        Assert.hasText(dto.getOssKey(),"附件OSSKEY不可为空");
        Assert.hasText(dto.getFileName(),"附件名称不可为空");
    }
}
