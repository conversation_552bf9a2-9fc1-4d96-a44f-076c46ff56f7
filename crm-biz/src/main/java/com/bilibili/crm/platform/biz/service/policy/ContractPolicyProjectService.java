package com.bilibili.crm.platform.biz.service.policy;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.contract.dto.ContractBaseDto;
import com.bilibili.crm.platform.api.contract.dto.QueryContractParam;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.cost_management.dto.CrmBtpProjectIncome;
import com.bilibili.crm.platform.api.policy.dto.*;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowStatusEnum;
import com.bilibili.crm.platform.api.policy.service.IContractPolicyProjectService;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.po.CrmContractPolicyInfoPo;
import com.bilibili.crm.platform.biz.po.CrmContractPolicyOrderTypePo;
import com.bilibili.crm.platform.biz.po.CrmContractPolicyProjectPo;
import com.bilibili.crm.platform.biz.repo.policy.ContractPolicyProjectRepo;
import com.bilibili.crm.platform.biz.repo.policy.CrmContractPolicyInfoRepo;
import com.bilibili.crm.platform.biz.repo.policy.CrmContractPolicyOrderTypeRepo;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.tuple.Pair;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import java.math.BigDecimal;
import java.util.Collections;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: brady
 * @time: 2025/2/11 18:06
 */
@Slf4j
@Service
public class ContractPolicyProjectService implements IContractPolicyProjectService {
    @Autowired
    private ContractPolicyProjectRepo contractPolicyProjectRepo;
    @Autowired
    private CrmContractPolicyInfoRepo crmContractPolicyInfoRepo;
    @Autowired
    private CrmContractPolicyOrderTypeRepo crmContractPolicyOrderTypeRepo;
    @Autowired
    private PolicyCalcService policyCalcService;
    @Autowired
    private IContractQueryService contractQueryService;

    @Override
    public void savePolicyProject(Integer policyId, String taskNodeId, PolicyFlowProcessActionDto dto) {

        List<ProjectSplitView> projectSplits = dto.getProjectSplits();
        projectSplits.forEach(item -> {
            ContractPolicyIncome policyIncome = item.getPolicy_income();
            CrmContractPolicyProjectPo po = CrmContractPolicyProjectPo.builder()
                    .policyId(policyId)
                    .processInstanceId(dto.getProcessInstanceId())
                    .creator(item.getBusiness_contact())
                    .merchantsProjectId(item.getMerchants_project_id())
                    .merchantsProjectName(item.getMerchants_project_name())
                    .hardAdIncome(Utils.fromYuanToFen(policyIncome.getHard_ad_income()))
                    .notStandIncome(Utils.fromYuanToFen(policyIncome.getNot_stand_income()))
                    .liveBroadcastCommercialIncome(Utils.fromYuanToFen(policyIncome.getLive_broadcast_commercial_income()))
                    .pickupCommercialIncome(Utils.fromYuanToFen(policyIncome.getPickup_commercial_income()))
                    .liveHardIncome(Utils.fromYuanToFen(policyIncome.getLive_hard_income()))
                    .prePickupIncome(Utils.fromYuanToFen(policyIncome.getPre_pickup_income()))
                    .taskNodeId(taskNodeId)
                    .build();
            contractPolicyProjectRepo.insert(po);
        });
    }

    @Override
    public ContractPolicyProjectView getPolicyProject(Integer policyId, String processInstanceId, String taskNodeId, String businessContact, Operator operator) {
        List<CrmContractPolicyProjectPo> pos = Lists.newArrayList();
        if (!Strings.isNullOrEmpty(taskNodeId)) {
            //查询流程节点 拆分金额 ，用于展示，包含已软删的数据
            pos = contractPolicyProjectRepo.queryListByProcessTask(processInstanceId, taskNodeId);
        } else {
            //项目拆分金额
            pos = contractPolicyProjectRepo.queryListByProcessInstanceId(processInstanceId);
            if (!Strings.isNullOrEmpty(businessContact)) {
                pos = pos.stream()
                        .filter(po -> businessContact.equals(po.getCreator()))
                        .collect(Collectors.toList());
            }
        }

        List<ProjectSplitView> projects = pos.stream().map(item -> {
            ProjectSplitView project = ProjectSplitView.builder()
                    .merchants_project_id(item.getMerchantsProjectId())
                    .merchants_project_name(item.getMerchantsProjectName())
                    .business_contact(item.getCreator())
                    .policy_income(ContractPolicyIncome.builder()
                            .hard_ad_income(Utils.fromFenToYuan(item.getHardAdIncome()))
                            .not_stand_income(Utils.fromFenToYuan(item.getNotStandIncome()))
                            .pickup_commercial_income(Utils.fromFenToYuan(item.getPickupCommercialIncome()))
                            .live_broadcast_commercial_income(Utils.fromFenToYuan(item.getLiveBroadcastCommercialIncome()))
                            .pre_pickup_income(Utils.fromFenToYuan(item.getPrePickupIncome()))
                            .live_hard_income(Utils.fromFenToYuan(item.getLiveHardIncome()))
                            .build())
                    .build();
            return project;
        }).collect(Collectors.toList());

        //政策信息
        ContractPolicyIncome contractPolicyIncome = getContractPolicyIncome(policyId);
        //合同总金额
        BigDecimal calcContractSumIncome = policyCalcService.calcContractSumIncome(contractPolicyIncome.getHard_ad_income(), contractPolicyIncome.getNot_stand_income(),
                contractPolicyIncome.getLive_broadcast_commercial_income(), contractPolicyIncome.getPickup_commercial_income(), contractPolicyIncome.getLive_hard_income());

        return ContractPolicyProjectView.builder()
                .contract_total_income(calcContractSumIncome)
                .contract_policy_income(contractPolicyIncome)
                .project_splits(projects)
                .build();
    }

    public ContractPolicyIncome getContractPolicyIncome(Integer policyId) {
        CrmContractPolicyInfoPo policyInfoPo = crmContractPolicyInfoRepo.queryById(policyId);
        List<CrmContractPolicyOrderTypePo> policyOrderPos = crmContractPolicyOrderTypeRepo.queryByPolicyId(policyInfoPo.getId());

        BigDecimal hardAdIncome = BigDecimal.ZERO;
        BigDecimal notStandIncome = BigDecimal.ZERO;
        BigDecimal liveBroadcastCommercialIncome = BigDecimal.ZERO;
        BigDecimal pickupCommercialIncome = BigDecimal.ZERO;
        BigDecimal liveHardIncome = BigDecimal.ZERO;
        BigDecimal prePickupIncome = BigDecimal.ZERO;

        for (CrmContractPolicyOrderTypePo item : policyOrderPos) {
            hardAdIncome = hardAdIncome.add(Utils.fromFenToYuan(item.getHardAdIncome()));
            notStandIncome = notStandIncome.add(Utils.fromFenToYuan(item.getNotStandIncome()));
            liveBroadcastCommercialIncome = liveBroadcastCommercialIncome.add(Utils.fromFenToYuan(item.getLiveBroadcastCommercialIncome()));
            pickupCommercialIncome = pickupCommercialIncome.add(Utils.fromFenToYuan(item.getPickupCommercialIncome()));
            liveHardIncome = liveHardIncome.add(Utils.fromFenToYuan(item.getLiveHardIncome()));
            prePickupIncome = prePickupIncome.add(Utils.fromFenToYuan(item.getPrePickupIncome()));
        }

        return ContractPolicyIncome.builder()
                .hard_ad_income(hardAdIncome)
                .not_stand_income(notStandIncome)
                .live_hard_income(liveHardIncome)
                .live_broadcast_commercial_income(liveBroadcastCommercialIncome)
                .pre_pickup_income(prePickupIncome)
                .pickup_commercial_income(pickupCommercialIncome)
                .build();
    }

    @Override
    public void updateContractNumber(Long contractNumber, List<String> processInstanceIds) {
        log.info("updateContractNumber contractNumber {}, processInstanceIds {}", contractNumber, processInstanceIds);
        //取最后一个流程号 一个合同只能绑一个项目流程拆分
        if (!CollectionUtils.isEmpty(processInstanceIds)) {
            List<CrmContractPolicyInfoPo> contractPolicyInfos = crmContractPolicyInfoRepo.queryListByProcessInstanceIds(processInstanceIds);
            CrmContractPolicyInfoPo maxInfoPo = contractPolicyInfos.stream().max(Comparator.comparing(CrmContractPolicyInfoPo::getId)).get();
            processInstanceIds = Lists.newArrayList(maxInfoPo.getProcessInstanceId());
        }
        //合同与政策一对多, 一个合同可以关联多个流程号,一个流程号只能关联一个合同号
        // 删除老的关系
        List<CrmContractPolicyProjectPo> crmContractPolicyProjectPos = contractPolicyProjectRepo.queryListByContractNo(contractNumber);
        crmContractPolicyProjectPos.forEach(po -> {
            contractPolicyProjectRepo.updateById(CrmContractPolicyProjectPo.builder()
                    .id(po.getId()).contractNumber(0L)
                    .build());
        });
        //更新合同号
        if (!CollectionUtils.isEmpty(processInstanceIds)) {
            List<CrmContractPolicyProjectPo> pos = contractPolicyProjectRepo.queryListByProcessInstanceIds(processInstanceIds);
            pos.forEach(po -> {
                contractPolicyProjectRepo.updateById(CrmContractPolicyProjectPo.builder()
                        .id(po.getId())
                        .contractNumber(contractNumber)
                        .build());
            });
        }
    }


    @Override
    public void invalidPolicyProject(String processInstanceId) {
        log.info("invalidPolicyProject  processInstanceIds {}", processInstanceId);

        if (Strings.isNullOrEmpty(processInstanceId)) {
            return;
        }
        //更新合同号
        List<CrmContractPolicyProjectPo> pos = contractPolicyProjectRepo.queryListByProcessInstanceId(processInstanceId);
        pos.forEach(po -> {
            contractPolicyProjectRepo.updateById(CrmContractPolicyProjectPo.builder()
                    .id(po.getId())
                    .isDeleted(IsDeleted.DELETED.getCode())
                    .build());
        });
    }

    @Override
    public List<CrmBtpProjectIncome> getCrmBtpProjectIncome(Integer merchantsProjectId, List<Long> crmContractIds) {
        //确保最新一条
        List<CrmContractPolicyProjectPo> poList = contractPolicyProjectRepo.queryListByReq(ContractPolicyProjectReq.builder()
                .contractNumbers(crmContractIds)
                .merchantsProjectId(merchantsProjectId)
                .build());

        List<Integer> policyIds = poList.stream().map(CrmContractPolicyProjectPo::getPolicyId).distinct().collect(Collectors.toList());
        List<CrmContractPolicyInfoPo> crmContractPolicyInfoPos = crmContractPolicyInfoRepo.queryByIds(policyIds);

        Map<Integer, CrmContractPolicyInfoPo> policyMap = crmContractPolicyInfoPos.stream().collect(Collectors.toMap(CrmContractPolicyInfoPo::getId, Function.identity()));

        List<CrmBtpProjectIncome> resList = poList.stream().filter(it -> {
            CrmContractPolicyInfoPo policyInfoPo = policyMap.get(it.getPolicyId());
            return PolicyFlowStatusEnum.getStatusForBtp().contains(policyInfoPo.getStatus());
        }).map(it -> {
            return CrmBtpProjectIncome.builder()
                    .crmContractNo(it.getContractNumber())
                    .projectContractTotalIncome(calcContractSumIncomeForBtp(it))
                    .projectContractPreIncome(Utils.fromFenToYuan(it.getPrePickupIncome()))
                    .build();
        }).collect(Collectors.toList());
        return resList;
    }

    private BigDecimal calcContractSumIncomeForBtp(CrmContractPolicyProjectPo policyProjectPo) {
        Long reduce = Stream.of(policyProjectPo.getHardAdIncome(), policyProjectPo.getLiveHardIncome(), policyProjectPo.getNotStandIncome(),
                        policyProjectPo.getPickupCommercialIncome(), policyProjectPo.getPrePickupIncome(), policyProjectPo.getLiveBroadcastCommercialIncome())
                .reduce(0L, Long::sum);
        return Utils.fromFenToYuan(reduce);
    }

    @Override
    public List<Pair<Long, String>> getContractListForBtp(Integer merchantsProjectId, Long crmContractId) {
        // 第一步：根据CRM项目ID找到运营初审环节拆分项目金额的最新核价流程（限定核价流程状态 = 已归档或审批通过的流程）
        // 第二步：读取核价项目拆分上绑定的CRM合同号（限定合同状态 ≠ 废弃）
        List<CrmContractPolicyProjectPo> poList = contractPolicyProjectRepo.queryListByReq(ContractPolicyProjectReq.builder()
                .contractNumber(crmContractId)
                .merchantsProjectId(merchantsProjectId)
                .build());
        if (CollectionUtils.isEmpty(poList)) {
            return Collections.emptyList();
        }

        List<Integer> policyIds = poList.stream().map(CrmContractPolicyProjectPo::getPolicyId).distinct().collect(Collectors.toList());

        List<CrmContractPolicyInfoPo> crmContractPolicyInfoPos = crmContractPolicyInfoRepo.queryByIds(policyIds);

        Map<Integer, CrmContractPolicyInfoPo> policyMap = crmContractPolicyInfoPos.stream().collect(Collectors.toMap(CrmContractPolicyInfoPo::getId, Function.identity()));

        List<Long> contractNos = poList.stream().filter(it -> {
            CrmContractPolicyInfoPo policyInfoPo = policyMap.get(it.getPolicyId());
            return PolicyFlowStatusEnum.getStatusForBtp().contains(policyInfoPo.getStatus());
        }).map(CrmContractPolicyProjectPo::getContractNumber).distinct().collect(Collectors.toList());

        //查询合同
        List<ContractBaseDto> contracts = CollectionUtils.isEmpty(contractNos) ? Collections.emptyList()
                : contractQueryService.getContractBaseDtoByQueryParam(QueryContractParam.builder()
                .contractNumberList(contractNos)
                .build());
        //数据返回
        List<Pair<Long, String>> res = contracts.stream()
                .filter(it -> !ContractBusStatus.DELETED.getCode().equals(it.getBusStatus()))
                .map(it -> Pair.of(it.getContractNumber(), it.getName())).collect(Collectors.toList());
        return res;
    }
}
