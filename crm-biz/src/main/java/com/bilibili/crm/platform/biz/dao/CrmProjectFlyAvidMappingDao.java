package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmProjectFlyAvidMappingPo;
import com.bilibili.crm.platform.biz.po.CrmProjectFlyAvidMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmProjectFlyAvidMappingDao {
    long countByExample(CrmProjectFlyAvidMappingPoExample example);

    int deleteByExample(CrmProjectFlyAvidMappingPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmProjectFlyAvidMappingPo record);

    int insertBatch(List<CrmProjectFlyAvidMappingPo> records);

    int insertUpdateBatch(List<CrmProjectFlyAvidMappingPo> records);

    int insert(CrmProjectFlyAvidMappingPo record);

    int insertUpdateSelective(CrmProjectFlyAvidMappingPo record);

    int insertSelective(CrmProjectFlyAvidMappingPo record);

    List<CrmProjectFlyAvidMappingPo> selectByExample(CrmProjectFlyAvidMappingPoExample example);

    CrmProjectFlyAvidMappingPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmProjectFlyAvidMappingPo record, @Param("example") CrmProjectFlyAvidMappingPoExample example);

    int updateByExample(@Param("record") CrmProjectFlyAvidMappingPo record, @Param("example") CrmProjectFlyAvidMappingPoExample example);

    int updateByPrimaryKeySelective(CrmProjectFlyAvidMappingPo record);

    int updateByPrimaryKey(CrmProjectFlyAvidMappingPo record);
}