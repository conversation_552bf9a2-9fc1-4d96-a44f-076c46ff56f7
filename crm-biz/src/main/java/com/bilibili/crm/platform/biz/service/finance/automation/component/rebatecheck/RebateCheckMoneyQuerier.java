package com.bilibili.crm.platform.biz.service.finance.automation.component.rebatecheck;

import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.crm.platform.api.finance.dto.automation.RebateCheckDeductContractSaveDto;
import com.bilibili.crm.platform.biz.po.CrmRebateCheckRecordPo;
import com.bilibili.crm.platform.biz.repo.finance.CrmRebateCheckRecordRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @date 2021/6/11 5:47 下午
 */
@Slf4j
@Component
public class RebateCheckMoneyQuerier {

    @Autowired
    private CrmRebateCheckRecordRepo crmRebateCheckRecordRepo;

    public Boolean isDeductCompleted(RebateCheckDeductContractSaveDto rebateCheckInfoSaveDto) {
        Boolean isCompletedClaim;
        CrmRebateCheckRecordPo rebateCheckRecordPo = crmRebateCheckRecordRepo.queryByRebateCheckId(rebateCheckInfoSaveDto.getRebateCheckId());
        if (rebateCheckRecordPo == null) {
            throw new ServiceRuntimeException("返点核算记录不存在！flowId:" + rebateCheckInfoSaveDto.getRebateCheckId());
        }
        BigDecimal notClaimMoney =
                new BigDecimal(Math.abs(rebateCheckRecordPo.getCheckAmount()) - rebateCheckRecordPo.getDeductedCheckAmount());
        if (notClaimMoney.equals(BigDecimal.ZERO)) {
            isCompletedClaim = true;
        } else {
            isCompletedClaim = false;
        }
        log.info("=====> isDeductCompleted:{}, flowId:{}", isCompletedClaim, rebateCheckInfoSaveDto.getRebateCheckId());
        return isCompletedClaim;
    }
}
