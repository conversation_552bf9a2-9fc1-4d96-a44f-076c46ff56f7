package com.bilibili.crm.platform.biz.flowable.listener.task.customer.sea;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.biz.flowable.IUserTaskActionService;
import com.bilibili.crm.platform.biz.flowable.listener.task.ILeaderTaskListener;
import com.bilibili.crm.platform.biz.po.CustomerSeaClaimPo;
import com.bilibili.crm.platform.biz.repo.customer.CustomerSeaClaimRepo;
import com.bilibili.crm.platform.biz.repo.customer.CustomerSeaRepo;
import com.bilibili.crm.platform.biz.service.customer.sea.helper.CustomerSeaClaimMsgHelper;
import com.bilibili.crm.platform.biz.statusmachine.customer.sea.CustomerSeaClaimStateMachine;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.sycpb.acc.api.dict.common.YesNoEnum;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.TaskService;
import org.flowable.engine.task.Comment;
import org.flowable.identitylink.api.IdentityLink;
import org.flowable.identitylink.api.IdentityLinkInfo;
import org.flowable.task.service.delegate.DelegateTask;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @date 2023/2/28 20:13
 */
@Slf4j
@Component("customerSeaClaimLeaderTaskListener")
public class CustomerSeaClaimLeaderTaskListener implements ILeaderTaskListener {

    @Resource
    private CustomerSeaClaimMsgHelper customerSeaClaimMsgHelper;

    @Resource
    private CustomerSeaClaimRepo customerSeaClaimRepo;

    @Resource
    private CustomerSeaClaimStateMachine customerSeaClaimStateMachine;

    @Resource
    private TaskService taskService;

    @Resource
    private CustomerSeaRepo customerSeaRepo;

    /**
     * 任务完成时发送通知
     *
     * @param delegateTask 任务信息
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "crmPlatformTransactionManager")
    @Override
    public void notifyComplete(DelegateTask delegateTask) {
        String value = String.valueOf(delegateTask.getVariable(IUserTaskActionService.APPROVE_KEY));
        if (YesNoEnum.NO.getCode().toString().equals(value)) {
            // 发送驳回通知
            String procInstId = delegateTask.getProcessInstanceId();
            CustomerSeaClaimPo po = customerSeaClaimRepo.queryByProcInstId(procInstId);
            Assert.notNull(po, "认领不存在");

            // 更新公海状态
            customerSeaRepo.visible(po.getCustomerSeaId());

            String assignee = delegateTask.getAssignee();
            Long claimId = po.getId();

            List<Comment> comments = taskService.getTaskComments(delegateTask.getId());
            String comment = CollectionUtils.isNotEmpty(comments) ? comments.get(0).getFullMessage() : "";

            Operator operator = Operator.builder().operatorName(assignee).build();
            // 流转到已驳回
            customerSeaClaimStateMachine.turnToRejected(operator, claimId, comment);
        }
    }

    /**
     * 发送待办通知
     *
     * @param delegateTask 任务
     */
    @Transactional(rollbackFor = Exception.class, transactionManager = "crmPlatformTransactionManager")
    @Override
    public void notifyCreate(DelegateTask delegateTask) {
        log.info("delegateTask {}", JSON.toJSONString(delegateTask));
        // 流转到当前节点
        CustomerSeaClaimPo po = customerSeaClaimRepo.queryByProcInstId(delegateTask.getProcessInstanceId());
        if (Objects.isNull(po)) {
            log.info(">>> 没有找到有效的报备信息，{}", delegateTask.getProcessInstanceId());
            return;
        }

        // 发送待办消息
        List<String> receivers = Lists.newArrayList();
        String assignee = delegateTask.getAssignee();
        if (StringUtils.isNotEmpty(assignee)) {
            // 任务处理人
            receivers.add(assignee);
        } else {
            // 可能有多个候选人
            List<IdentityLink> candidates = Lists.newArrayList(delegateTask.getCandidates());
            receivers = CrmUtils.convertDistinct(candidates, IdentityLinkInfo::getUserId);
        }
        customerSeaClaimMsgHelper.publishEventTodo(receivers, po);
    }
}
