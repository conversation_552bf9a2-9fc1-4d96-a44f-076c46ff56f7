package com.bilibili.crm.platform.biz.contactor.infrastructure;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaBelongingService;
import com.bilibili.crm.platform.api.sale_sea.dto.SaleSeaBelonging;
import com.bilibili.crm.platform.biz.contactor.domain.ContactorExportInfoDTO;
import com.bilibili.crm.platform.biz.contactor.domain.ContactorInfo;
import com.bilibili.crm.platform.biz.contactor.domain.ContactorRepo;
import com.bilibili.crm.platform.biz.dao.AccCompanyGroupDao;
import com.bilibili.crm.platform.biz.dao.CrmContactorDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.service.SaleService;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description TODO
 * @Date 2023/11/21
 **/
@Repository
public class ContactorRepoJdbc implements ContactorRepo {

    @Autowired
    private CrmContactorDao crmContactorDao;
    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private AccCompanyGroupDao accCompanyGroupDao;
    @Autowired
    private AccAccountDao accAccountDao;
    @Resource
    private ISaleSeaBelongingService saleSeaBelongingService;
    @Autowired
    private ISaleService saleService;
    @Override
    public ContactorInfo insert(ContactorInfo contactorInfo) {
        CrmContactorPo po = contactorInfo.toPo();
        crmContactorDao.insertSelective(po);
        return ContactorInfo.fromPo(po);
    }

    @Override
    public ContactorInfo update(ContactorInfo contactorInfo) {
        CrmContactorPo po = contactorInfo.toPo();
        crmContactorDao.updateByPrimaryKeySelective(po);
        return ContactorInfo.fromPo(po);
    }

    @Override
    public ContactorInfo selectById(Long id) {
        CrmContactorPo po = crmContactorDao.selectByPrimaryKey(id);
        return ContactorInfo.fromPo(po);
    }

    @Override
    public List<ContactorInfo> selectByExample(CrmContactorPoExample example) {
        List<CrmContactorPo> pos = crmContactorDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(ContactorInfo::fromPo).collect(Collectors.toList());
    }

    @Override
    public long countByExample(CrmContactorPoExample example) {
        return crmContactorDao.countByExample(example);
    }

    @Override
    public List<ContactorInfo> selectByDeptId(Long deptId) {
        return selectByDeptId(deptId, false);
    }

    @Override
    public List<ContactorInfo> selectByDeptId(Long deptId, Boolean containsDisable) {
        CrmContactorPoExample example = new CrmContactorPoExample();
        CrmContactorPoExample.Criteria criteria = example.createCriteria();
        criteria.andDeptIdEqualTo(deptId);
        if (BooleanUtils.isNotTrue(containsDisable)) {
            // false or null
            criteria.andIsEnableEqualTo(0);
        }
        example.setOrderByClause("mtime desc");
        return selectByExample(example);
    }

    @Override
    public List<ContactorInfo> selectByOrgId(Long orgId) {
        return selectByOrgId(orgId, false);
    }

    @Override
    public List<ContactorInfo> selectByOrgId(Long orgId, Boolean containsDisable) {
        CrmContactorPoExample example = new CrmContactorPoExample();
        CrmContactorPoExample.Criteria criteria = example.createCriteria();
        criteria.andOrgIdEqualTo(orgId);
        example.setOrderByClause("mtime desc");
        if (BooleanUtils.isNotTrue(containsDisable)) {
            // false or null
            criteria.andIsEnableEqualTo(0);
        }
        return selectByExample(example);
    }

    @Override
    public Map<Integer, List<ContactorInfo>> selectCustomerMaps() {
        CrmContactorPoExample example = new CrmContactorPoExample();
        CrmContactorPoExample.Criteria criteria = example.createCriteria();
        criteria.andCustomerIdGreaterThan(0);
        List<ContactorInfo> contactorInfos = selectByExample(example);
        if (CollectionUtils.isEmpty(contactorInfos)) {
            return Collections.emptyMap();
        }
        return contactorInfos.stream().collect(Collectors.groupingBy(ContactorInfo::getCustomerId));
    }

    @Override
    public List<ContactorInfo> selectByCustomerId(Integer customerId) {
        CrmContactorPoExample example = new CrmContactorPoExample();
        CrmContactorPoExample.Criteria criteria = example.createCriteria();
        criteria.andCustomerIdEqualTo(customerId);
        return selectByExample(example);
    }

    @Override
    public List<ContactorInfo> selectCustomerContactor(Integer customerId) {
        CrmContactorPoExample example = new CrmContactorPoExample();
        CrmContactorPoExample.Criteria criteria = example.createCriteria();
        criteria.andCustomerIdEqualTo(customerId);
        criteria.andOrgIdEqualTo(0L);
        criteria.andDeptIdEqualTo(0L);
        return selectByExample(example);
    }

//    public List<ContactorExportInfoDTO> getAllContactorInfoList() {
//        List<ContactorExportInfoDTO> result = new ArrayList<>();
//        CrmContactorPoExample crmContactorPoExample = new CrmContactorPoExample();
//        crmContactorPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
//        List<CrmContactorPo> pos = crmContactorDao.selectByExample(crmContactorPoExample);
//        List<Integer> customerIds = pos.stream().map(CrmContactorPo::getCustomerId).filter(Utils::isPositive).collect(Collectors.toList());
//        Map<Integer, List<CustomerPo>> customerPoMap = new HashMap<>();
//        Map<Integer, List<AccCompanyGroupPo>> groupPoMap = new HashMap<>();
//        Map<Integer, List<AccAccountPo>> accountPoMap = new HashMap<>();
//        List<AccAccountPo> accAccountPoList = new ArrayList<>();
//        Map<Integer, SaleSeaBelonging> saleSeaBelongingMap = new HashMap<>();
//        if (CollectionUtils.isNotEmpty(customerIds)) {
//            CustomerPoExample customerPoExample = new CustomerPoExample();
//            customerPoExample.createCriteria().andIdIn(customerIds);
//            customerPoMap = customerDao.selectByExample(customerPoExample).stream().collect(Collectors.groupingBy(CustomerPo::getId));
//        }
//        if (CollectionUtils.isNotEmpty(customerIds)) {
//            AccAccountPoExample accountPoExample = new AccAccountPoExample();
//            accountPoExample.createCriteria().andCustomerIdIn(customerIds);
//            accAccountPoList = accAccountDao.selectByExample(accountPoExample);
//            accountPoMap = accAccountPoList.stream().collect(Collectors.groupingBy(AccAccountPo::getCustomerId));
//        }
//        Map<Integer, List<CustomerPo>> finalCustomerPoMap = customerPoMap;
//        List<Integer> groupIds = customerPoMap.keySet().stream().map(e -> finalCustomerPoMap.get(e).get(0).getGroupId()).filter(Utils::isPositive).collect(Collectors.toList());
//        if (CollectionUtils.isNotEmpty(groupIds)) {
//            AccCompanyGroupPoExample groupPoExample = new AccCompanyGroupPoExample();
//            groupPoExample.createCriteria().andIdIn(groupIds);
//            groupPoMap = accCompanyGroupDao.selectByExample(groupPoExample).stream().collect(Collectors.groupingBy(AccCompanyGroupPo::getId));
//        }
//        if (CollectionUtils.isNotEmpty(accAccountPoList)) {
//            saleSeaBelongingMap = saleSeaBelongingService.queryAccountBelongingMap(accAccountPoList.stream().map(AccAccountPo::getAccountId).distinct().collect(Collectors.toList()));
//        }
//        Map<Integer, List<AccCompanyGroupPo>> finalGroupPoMap = groupPoMap;
//        Map<Integer, List<AccAccountPo>> finalAccountPoMap = accountPoMap;
//        Map<Integer, SaleSeaBelonging> finalSaleSeaBelongingMap = saleSeaBelongingMap;
//        Map<Integer, SaleDto> saleDtoMap = saleService.getAllSaleMapWithCache();
//        pos.forEach(t -> {
//            ContactorExportInfoDTO dto = new ContactorExportInfoDTO();
//            CustomerPo customerPo = finalCustomerPoMap.getOrDefault(t.getCustomerId(), Collections.singletonList(CustomerPo.builder().build())).get(0);
//            AccCompanyGroupPo groupPo = finalGroupPoMap.getOrDefault(customerPo.getGroupId(), Collections.singletonList(AccCompanyGroupPo.builder().build())).get(0);
//            List<AccAccountPo> accountPos = finalAccountPoMap.getOrDefault(t.getCustomerId(), Collections.emptyList());
//            dto.setId(t.getId());
//            dto.setCustomerId(t.getCustomerId());
//            dto.setCustomerName(customerPo.getUsername());
//            dto.setGroupId(customerPo.getGroupId());
//            dto.setGroupName(groupPo.getName());
//            dto.setContactor(t.getContactor());
//            dto.setContactorPhone(t.getContactorPhoneCode() + "-" + t.getContactorPhone());
//            dto.setCustomerUnderAccountDirectSaleNames(SaleService.buildSaleName(accountPos.stream()
//                    .map(a -> finalSaleSeaBelongingMap.get(a.getAccountId()))
//                    .filter(Objects::nonNull).map(b -> b.getProductSaleMap().get(SaleSeaProductCategory.BRAND))
//                    .filter(Objects::nonNull).map(c -> c.get(BiliUserType.STRAIGHT_MANAGER))
//                    .filter(Objects::nonNull).flatMap(List::stream).distinct().collect(Collectors.toList()), saleDtoMap));
//            dto.setCustomerUnderAccountChannelSaleNames(SaleService.buildSaleName(accountPos.stream()
//                    .map(a -> finalSaleSeaBelongingMap.get(a.getAccountId()))
//                    .filter(Objects::nonNull).map(b -> b.getProductSaleMap().get(SaleSeaProductCategory.RTB))
//                    .filter(Objects::nonNull).map(c -> c.get(BiliUserType.CHANNEL_MANAGER))
//                    .filter(Objects::nonNull).flatMap(List::stream).distinct().collect(Collectors.toList()), saleDtoMap));
//            result.add(dto);
//        });
//        return result;
//    }
}
