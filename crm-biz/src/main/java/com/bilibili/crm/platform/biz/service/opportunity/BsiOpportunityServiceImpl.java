package com.bilibili.crm.platform.biz.service.opportunity;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.ListUtils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.account.dto.NewOldCategoryMappingDto;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.account.service.IAccountService;
import com.bilibili.crm.platform.api.account.service.IFastQueryAccountService;
import com.bilibili.crm.platform.api.account.service.INewOldCategoryMappingService;
import com.bilibili.crm.platform.api.agent.dto.AgentDto;
import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.api.bsiopportunity.*;
import com.bilibili.crm.platform.api.bsiopportunity.dto.*;
import com.bilibili.crm.platform.api.clue.dto.BsiOpportunityPlannerDto;
import com.bilibili.crm.platform.api.clue.dto.ProductModelLabel;
import com.bilibili.crm.platform.api.contactor.CrmContactorMappingService;
import com.bilibili.crm.platform.api.contactor.CrmContactorService;
import com.bilibili.crm.platform.api.contactor.dto.*;
import com.bilibili.crm.platform.api.contract.dto.ContractFileOssDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.CustomerExtDto;
import com.bilibili.crm.platform.api.customer.dto.report.NewCustomerReportCustomerStateDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.enums.FollowPlannerStatus;
import com.bilibili.crm.platform.api.enums.bsi.*;
import com.bilibili.crm.platform.api.finance.enums.SaleTypeEnum;
import com.bilibili.crm.platform.api.finance.enums.YesOrNoEnum;
import com.bilibili.crm.platform.api.ka_belonging.dto.KaBelongIngQueryDTO;
import com.bilibili.crm.platform.api.ka_belonging.enums.KaBelongingMappingTypeEnum;
import com.bilibili.crm.platform.api.ka_belonging.service.IKaBelongIngService;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.rbac.IDomainUserService;
import com.bilibili.crm.platform.api.rbac.dto.DataStrategy;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaService;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.clickhouse.dao.AdsCrmBrandBsiSnapshotDao;
import com.bilibili.crm.platform.biz.common.BsiOppAbandonTypeEnum;
import com.bilibili.crm.platform.biz.common.FollowStage;
import com.bilibili.crm.platform.biz.common.IntentEffect;
import com.bilibili.crm.platform.biz.convertor.CrmCommonAttachmentConvertor;
import com.bilibili.crm.platform.biz.dao.*;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountDao;
import com.bilibili.crm.platform.biz.industry.enums.IndustryMappingEnum;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPoExample.Criteria;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsBrandBsiSnapshotPO;
import com.bilibili.crm.platform.biz.repo.CrmCommonAttachmentRepo;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.repo.bsi.BsiOpportunityPlannerRepo;
import com.bilibili.crm.platform.biz.service.clue.BsiOptClueStrategyService;
import com.bilibili.crm.platform.biz.service.customer.ext.ICustomerExtService;
import com.bilibili.crm.platform.biz.service.customer.report.NewCustomerReportServiceImpl;
import com.bilibili.crm.platform.biz.service.follow_manage.config.BsiOppConfig;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.opportunity.component.BsiOppComponent;
import com.bilibili.crm.platform.biz.service.opportunity.enums.BsiFollowStageEnum;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.biz.util.MDCDecoratorUtil;
import com.bilibili.crm.platform.biz.util.MailUtils;
import com.bilibili.crm.platform.biz.util.ThreadPoolManager;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.common.account.AccountFieldMapping;
import com.bilibili.crm.platform.common.clue.BsiAttachmentType;
import com.bilibili.crm.platform.common.customer.CustomerCategoryType;
import com.bilibili.crm.platform.common.sale_sea.SaleSeaProductCategory;
import com.bilibili.crm.platform.utils.CrmPageUtils;
import com.google.common.base.Preconditions;
import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.aop.framework.AopContext;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.Map.Entry;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.atomic.AtomicReference;
import java.util.function.Function;
import java.util.regex.Pattern;
import java.util.stream.Collectors;
import java.util.stream.Stream;

import static com.bilibili.crm.platform.biz.service.follow_manage.config.BsiOppConfig.BSI_OPPORTUNITY_INVALID;

@Slf4j
@Service
public class BsiOpportunityServiceImpl implements IBsiOpportunityService {

    @Autowired
    private IKaBelongIngService kaBelongIngService;
    @Autowired
    private BsiOpportunityDao bsiOpportunityDao;
    @Autowired
    private BsiOpportunitySaleMappingDao bsiOpportunitySaleMappingDao;
    @Autowired
    private BsiOpportunityFollowRecordDao bsiOpportunityFollowRecordDao;
    @Autowired
    private BsiOpportunityAttachmentDao bsiOpportunityAttachmentDao;
    @Autowired
    private IBsiOpportunityIntentProductService bsiOpportunityIntentProductService;
    @Autowired
    private IAccountService accountService;
    @Autowired
    private IAgentService agentService;
    @Autowired
    private MailUtils mailUtils;
    @Autowired
    private AccAccountDao accountDao;
    @Resource
    private ISaleSeaService saleSeaService;
    @Autowired
    private ILogOperatorService logOperatorService;
    @Autowired
    private INewOldCategoryMappingService newOldCategoryMappingService;
    @Resource
    private CrmCacheManager cacheManager;
    @Autowired
    private CrmMainPartConfigDao crmMainPartConfigDao;
    @Resource
    private ISaleService saleService;

    @Resource
    private ICustomerQueryService customerQueryService;

    @Resource
    private CrmCommonAttachmentRepo crmCommonAttachmentRepo;

    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private BsiOpportunityPlannerRepo bsiOpportunityPlannerRepo;

    @Resource
    private CustomerRepo customerRepo;

    @Autowired
    private ISaleGroupService saleGroupService;

    @Resource
    private ICustomerExtService iCustomerExtService;

    @Resource
    private IFastQueryAccountService fastQueryAccountService;

    @Resource
    private BsiOpportunityBizMappingDao bsiOpportunityBizMappingDao;

    @Autowired
    private IDomainUserService domainUserService;

    @Autowired
    private CrmContactorMappingService contactorMappingService;

    @Autowired
    private CrmContactorService contactorService;

    @Autowired
    private BsiOptClueStrategyService bsiOptClueStrategyService;

    @Autowired
    private IBsiOpsPlannerService bsiOpsPlannerService;

    @Autowired
    private IBsiOpsMediumService bsiOpsMediumService;

    @Autowired
    private IBsiOpsOgvService bsiOpsOgvService;

    @Autowired
    private IBsiOpsPugvService bsiOpsPugvService;
    @Resource
    private NewCustomerReportServiceImpl newCustomerReportService;

    @Autowired
    private AdsCrmBrandBsiSnapshotDao adsCrmBrandBsiSnapshotDao;

    @Autowired
    private WxRobotService wxRobotService;

    @Resource
    private BsiOppComponent bsiOppComponent;

    public static final String CRM_ALL_MAIN_PART_MAP_KEY = "CRM_ALL_MAIN_PART_MAP_KEY";

    public final static String PRODUCT_MODEL_TO_CLARIFIED_WX_TMP = "【CRM：商机产品型号维护通知】\n" +
            "您好，系统检测到以下商机内产品型号不完整，请尽快对相关行业产品库底表进行补充维护，并通知商机提交销售及时修改：\n" +
            "商机项目名称：%s（ID：%s）\n" +
            "广告主客户名称：%s（ID：%s）\n" +
            "账户名称：%s（ID：%s）\n" +
            "商机产品型号：%s-%s\n" +
            "提交人：%s" +
            "\n";

    @Value("${product.model.to.clarified.notify.robot.url:}")
    private String notifyRobotUrl;

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public int saveBsiOpportunity(BsiOpportunityDto bsiOpportunity, Operator operator, boolean newVersion) {
        if (bsiOpportunity.getCustomerId() != null) {
            bsiOpportunity.setCustomerName(StringUtils.EMPTY);
        }
        BsiOpportunityPo existBsi = bsiOpportunity.getId() == null ? null : bsiOpportunityDao.selectByPrimaryKey(bsiOpportunity.getId());
        BsiOpportunityDto existDto = existBsi == null ? null : buildDetail(po2Dto(existBsi), bsiOpportunity);
        if (bsiOpportunity.getId() != null) {
            Assert.notNull(existBsi, "需要修改的商机不存在");
        }

        if (FollowStage.COMPLETE_ORDER.getCode().equals(bsiOpportunity.getFollowStage()) && !bsiOptClueStrategyService.isAdmin(operator.getOperatorName())) {
            Assert.isTrue((System.currentTimeMillis() - existBsi.getCtime().getTime() >= 24 * 3600 * 1000L), "录入时间与成单时间需至少相隔24小时，请到期后再流转或者邮件发给总监特批后，提交给产品Eliot进行流转。");
        }

        BsiOpportunityPo bsiOpportunityPo = convert2PoFromDto(bsiOpportunity, operator);
        BsiOpportunityFollowRecordPo followRecordPo = convert2FollowRecordPo(existBsi, bsiOpportunityPo, operator, null, null);
        List<BsiOpportunityAttachmentPo> neddAddAttachmentList = new ArrayList<>();
        List<Integer> needDelAttachmentList = new ArrayList<>();

        ContactorSourceEnum source = fetchContactorSource(bsiOpportunity, operator.getOperatorName());
        separateAddAndDelAttachment(neddAddAttachmentList, needDelAttachmentList, bsiOpportunity, operator);
        this.saveOldCategory(bsiOpportunityPo);
        if (isNewCustomer(bsiOpportunity)) {
            NewCustomerReportCustomerStateDto stateDto = newCustomerReportService.queryCustomerReportState(IncomeUtil.buildSuperAdmin(), bsiOpportunityPo.getCompanyFullName());
            if (stateDto != null) {
                bsiOpportunityPo.setReportId(stateDto.getReport_id());
            }
        }
        boolean isCreate = false;
        if (bsiOpportunity.getId() == null) { // 新增商机
            isCreate = true;
            bsiOpportunityDao.insertSelective(bsiOpportunityPo);
            Integer opportunityPoId = bsiOpportunityPo.getId();
            followRecordPo.setBusiOpportunityId(opportunityPoId);
            bsiOpportunityFollowRecordDao.insertSelective(followRecordPo);
            Long contactorId;
            // 判断是否是新客
            if (isNewCustomer(bsiOpportunity)) {
                // 新客 - 创建商机 -> 创建联系人
                contactorId = createContactor(operator, bsiOpportunity, source);
            } else {
                // 已有客户 - 创建商机
                contactorId = Preconditions.checkNotNull(bsiOpportunity.getContactorId(), "新建商机-已有客户缺少联系人信息");
            }
            // 联系人信息映射
            bsiOpportunity.setId(opportunityPoId);
            createContactorMapping(bsiOpportunity, contactorId, source);
        } else {
            Preconditions.checkNotNull(existBsi, "需要修改的商机不存在");
            bsiOpportunityPo.setCreateUser(existBsi.getCreateUser());
            if (!existBsi.getFollowStage().equals(bsiOpportunityPo.getFollowStage()) && !newVersion) {
                bsiOpportunityFollowRecordDao.insertSelective(followRecordPo);
            }
            if (!existBsi.getNewFollowStage().equals(bsiOpportunityPo.getNewFollowStage()) && newVersion) {
                bsiOpportunityFollowRecordDao.insertSelective(followRecordPo);
            }

            Integer opportunityPoId = bsiOpportunityPo.getId();
            ContactorFullInfoDto contactorInfo = contactorMappingService.getBizContactorBySource(
                    ContactorMappingBizTypeEnum.BUSINESS_OPPORTUNITIES,
                    Long.valueOf(opportunityPoId),
                    source);
            bsiOpportunity.setId(opportunityPoId);
            // 是否是新客
            if (contactorInfo == null) {
                // 处理历史商机
                handleHistoryBsiContactor(operator, source, existDto, bsiOpportunity);
            } else if (isNewCustomer(bsiOpportunity)) {
                // 编辑 - 新客 - 存在 -> 更新联系人信息
                updateContactorForNewCustomer(operator, bsiOpportunity, contactorInfo.getId(), source);
            } else {
                // 编辑 - 已有客户 - 存在 -> 联系人id不同 -> 更改映射关系
                if (!Objects.equals(contactorInfo.getId(), bsiOpportunity.getContactorId())) {
                    updateContactorMapping(bsiOpportunity, contactorInfo.getId());
                }
            }
            bsiOpportunityDao.updateByPrimaryKeySelective(bsiOpportunityPo);

            // 商机一级产品型号名称或二级产品型号名称含“其它”字样，异步发送企业微信提醒
            asyncSendProductToSupplementWxBot(bsiOpportunityPo, operator);
        }
        // 保存商机和销售之间的关系
        bsiOpportunity.setId(bsiOpportunityPo.getId());
        addChannelSale(bsiOpportunity, operator);
        insertOrUpdateBsiOpportunitySaleMapping(bsiOpportunity, Operator.SYSTEM);
        if (CollectionUtils.isNotEmpty(needDelAttachmentList)) {
            BsiOpportunityAttachmentPoExample example = new BsiOpportunityAttachmentPoExample();
            example.or().andIdIn(needDelAttachmentList).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            bsiOpportunityAttachmentDao.deleteByExample(example);
        }
        if (CollectionUtils.isNotEmpty(neddAddAttachmentList)) {
            neddAddAttachmentList.forEach(po -> {
                po.setBusiOpportunityId(bsiOpportunityPo.getId());
                bsiOpportunityAttachmentDao.insertSelective(po);
            });
        }
        // 商机与意向产品类型-23年Q4后修改为售卖类型
        bsiOpportunity.setId(bsiOpportunityPo.getId());
        bsiOpportunityIntentProductService.saveBsiOpportunityIntentProductMappingDto(bsiOpportunity, operator);
        // 更新附件
        if (CollectionUtils.isNotEmpty(bsiOpportunity.getAttachments())) {
            // 先删除
            crmCommonAttachmentRepo.delete(bsiOpportunityPo.getId(), AttachmentUploadBizModuleEnum.BSI_ATTACHMENT);
            // 再新增
            crmCommonAttachmentRepo.batchAdd(CrmCommonAttachmentConvertor.convertDtos2Pos(bsiOpportunity.getId(), bsiOpportunity.getAttachments(), AttachmentUploadBizModuleEnum.BSI_ATTACHMENT));
        }
        //商机策划对接人
        bsiOpsPlannerService.plannerHandle(OpsPlannerSaveDto.builder().handleType(BsiOppPlannerHandleEnums.HANDLE_0.getCode()).opsId(bsiOpportunityPo.getId()).build(), operator);
        //商机媒介对接人
        bsiOpsMediumService.mediumHandle(OpsMediumSaveDTO.builder().handleType(BsiOppMediumHandleEnums.HANDLE_0.getCode()).opsId(bsiOpportunityPo.getId()).build(), operator);
        //商机OGV对接人
        bsiOpsOgvService.ogvHandle(OpsOgvSaveDTO.builder().handleType(BsiOppOGVHandleEnums.HANDLE_0.getCode()).opsId(bsiOpportunityPo.getId()).build(), operator);
        //商机PUGV对接人
        bsiOpsPugvService.pugvHandle(OpsPugvSaveDTO.builder().handleType(BsiOppPUGVHandleEnums.HANDLE_0.getCode()).opsId(bsiOpportunityPo.getId()).build(), operator);
        if (Objects.nonNull(existBsi) &&
                !Objects.equals(existBsi.getFollowStage(), bsiOpportunityPo.getFollowStage()) &&
                Objects.equals(bsiOpportunityPo.getFollowStage(), FollowStage.ABANDON_ORDER.getCode())) {
            bsiOpsMediumService.abandonBsi(bsiOpportunityPo.getId());
        }
        BsiOpportunityDto newDto = buildDetail(po2Dto(bsiOpportunityPo), bsiOpportunity);
        fillBsiContactor(newDto, operator.getOperatorName());
        if (isCreate) {
            //记录操作日志
            NewLogOperatorDto logDto = NewLogOperatorDto
                    .builder()
                    .modifyType(ModifyType.BSI_OPPORTUNITY_ADD)
                    .module(Module.BSI_OPPORTUNITY)
                    .obj(bsiOpportunity)
                    .objId(bsiOpportunityPo.getId())
                    .systemType(SystemType.CRM)
                    .build();
            logOperatorService.insertLog(operator, logDto);
        } else {
            //记录操作日志
            NewLogOperatorDto logDto = NewLogOperatorDto
                    .builder()
                    .modifyType(ModifyType.BSI_OPPORTUNITY_EDIT)
                    .module(Module.BSI_OPPORTUNITY)
                    .obj(newDto)
                    .oldObj(existDto)
                    .objId(bsiOpportunityPo.getId())
                    .systemType(SystemType.CRM)
                    .build();
            logOperatorService.insertLog(operator, logDto);
        }
        for (Integer cooperationProject : bsiOpportunity.getCooperationProjects()) {
            BsiOpportunityBizMappingPoExample example = new BsiOpportunityBizMappingPoExample();
            BsiOpportunityBizMappingPoExample.Criteria criteria = example.createCriteria();
            criteria.andBsiOpportunityIdEqualTo(bsiOpportunity.getId());
            bsiOpportunityBizMappingDao.deleteByExample(example);

            BsiOpportunityBizMappingPo bsiOpportunityBizMappingPo = new BsiOpportunityBizMappingPo();
            bsiOpportunityBizMappingPo.setBizId(cooperationProject);
            bsiOpportunityBizMappingPo.setBizType(BsiOpportunityBizMappingTypeEnum.COOPERATION_PROJECT.getId());
            bsiOpportunityBizMappingPo.setBsiOpportunityId(bsiOpportunity.getId());
            bsiOpportunityBizMappingPo.setIsDeleted(IsDeleted.VALID.getCode());
            bsiOpportunityBizMappingDao.insertUpdateSelective(bsiOpportunityBizMappingPo);
        }

        if (!newVersion) {
            updateFollowStageTime(bsiOpportunityPo.getId());
        } else {
            bsiOppComponent.updateFollowStageTime(bsiOpportunityPo.getId());
        }

        if (!newVersion) {
            updateAmount(bsiOpportunityPo.getId());
        } else {
            bsiOppComponent.updateAmount(bsiOpportunityPo);
        }
        return bsiOpportunityPo.getId();
    }

    private void asyncSendProductToSupplementWxBot(BsiOpportunityPo bsiOpportunityPo, Operator operator) {
        if (Objects.isNull(bsiOpportunityPo.getCustomerId()) || Objects.isNull(bsiOpportunityPo.getAccountId())) {
            return;
        }
        if ((StringUtils.isNotEmpty(bsiOpportunityPo.getFirstProductModel()) && (bsiOpportunityPo.getFirstProductModel().contains("其它") || bsiOpportunityPo.getFirstProductModel().contains("其他")))
                || (StringUtils.isNotEmpty(bsiOpportunityPo.getSecondProductModel()) && (bsiOpportunityPo.getSecondProductModel().contains("其它") || bsiOpportunityPo.getSecondProductModel().contains("其他")))) {
            log.info("bsiOpportunity 产品类型包含<其他> {}", bsiOpportunityPo);
            CompletableFuture.runAsync(MDCDecoratorUtil.runnable(() -> {
                try {
                    CustomerBaseDto customerBaseDto = customerQueryService.getCustomerBaseDtoById(bsiOpportunityPo.getCustomerId());
                    Assert.notNull(customerBaseDto, "商机客户不存在:" + bsiOpportunityPo.getCustomerId());
                    AccAccountPo accAccountPo = accountDao.selectByPrimaryKey(bsiOpportunityPo.getAccountId());
                    String content = String.format(PRODUCT_MODEL_TO_CLARIFIED_WX_TMP,
                            bsiOpportunityPo.getName(), bsiOpportunityPo.getId(),
                            customerBaseDto.getUsername(), customerBaseDto.getId(),
                            Objects.nonNull(accAccountPo) ? accAccountPo.getUsername() : "未知", Objects.nonNull(accAccountPo) ? accAccountPo.getAccountId() : "未知",
                            bsiOpportunityPo.getFirstProductModel(), bsiOpportunityPo.getSecondProductModel(),
                            operator.getOperatorName()
                    );
                    wxRobotService.sendWeChatWork(WxRobotMsg.builder()
                            .msgtype(MsgType.MARKDOWN.getType())
                            .markdown(new Text(content))
                            .build(), notifyRobotUrl);
                } catch (Exception e) {
                    log.error("bsiOpportunity 发送wx通知失败：{}", e.getMessage(), e);
                }
            }), ThreadPoolManager.DEFAULT_THREAD_POOL_DEPRECATED);
        }
    }

    /**
     * 处理历史商机
     *
     * @param operator       操作人
     * @param existDto       历史商机dto
     * @param bsiOpportunity 当前修改的商机dto
     */
    private void handleHistoryBsiContactor(Operator operator, ContactorSourceEnum source, BsiOpportunityDto existDto, BsiOpportunityDto bsiOpportunity) {
        if (needCreateContactor(existDto, bsiOpportunity)) {
            Long existsContactorId = bsiOpportunity.getContactorId();
            if (!Utils.isPositive(existsContactorId)) {
                // 没传联系人id，则创建该联系人
                existsContactorId = createContactor(operator, bsiOpportunity, source);
            }
            // 联系人信息映射 - 目前只需要映射bizId
            createContactorMapping(bsiOpportunity, existsContactorId, source);
        }
    }

    private boolean needCreateContactor(BsiOpportunityDto existDto, BsiOpportunityDto bsiOpportunity) {
        return !StringUtils.equals(existDto.getContactName(), bsiOpportunity.getContactName());
    }

    private void updateContactorMapping(BsiOpportunityDto bsiOpportunity, Long contactorId) {
        contactorMappingService.updateContactor(ContactorMappingBizTypeEnum.BUSINESS_OPPORTUNITIES,
                Long.valueOf(bsiOpportunity.getId()),
                contactorId,
                ContactorMappingDto.builder().contactorId(bsiOpportunity.getContactorId()).build());
    }

    private void updateContactorForNewCustomer(Operator operator, BsiOpportunityDto bsiOpportunity, Long id, ContactorSourceEnum source) {
        ContactorDto contactorDto = buildContactor(bsiOpportunity, source);
        contactorDto.setId(id);
        contactorService.updateForNewCustomer(operator, contactorDto);
    }

    private void createContactorMapping(BsiOpportunityDto bsiOpportunity, Long contactorId, ContactorSourceEnum source) {
        contactorMappingService.createBizMapping(ContactorMappingDto.builder()
                .bizType(ContactorMappingBizTypeEnum.BUSINESS_OPPORTUNITIES)
                .bizId(Long.valueOf(bsiOpportunity.getId()))
                .contactorId(contactorId)
                .contactorSource(source)
                .build());
    }

    public Long createContactor(Operator operator, BsiOpportunityDto bsiOpportunity, ContactorSourceEnum source) {
        ContactorDto contactorDto = buildContactor(bsiOpportunity, source);
        return Utils.isPositive(contactorDto.getCustomerId()) ?
                contactorService.create(operator, contactorDto) : contactorService.createForNewCustomer(operator, contactorDto);
    }

    private ContactorDto buildContactor(BsiOpportunityDto bsiOpportunity, ContactorSourceEnum contactotSource) {
        Integer customerId = bsiOpportunity.getCustomerId();
        Integer agentId = bsiOpportunity.getAgentId();
        if (contactotSource == ContactorSourceEnum.AGENT && Utils.isPositive(agentId)) {
            // 联系人来源是代理商并且配置了代理商 需要通过代理商查找到所属的代理商客户id
            AgentDto agent = agentService.getAgentByAgentId(agentId);
            Preconditions.checkNotNull(agent, String.format("代理商「%s」不存在", agentId));
            AccountBaseDto baseDto = fastQueryAccountService.fetchOne(agent.getSysAgentId(), AccountFieldMapping.customerId);
            Preconditions.checkNotNull(baseDto, String.format("代理商「%s」帐户「%s」不存在", agentId, agent.getSysAgentId()));
            customerId = baseDto.getCustomerId();
        }
        return ContactorDto.builder()
                .customerId(customerId)
                .contactor(bsiOpportunity.getContactName())
                .contactorPosition(bsiOpportunity.getPosition())
                .contactorDepartment(bsiOpportunity.getDepartment())
                .contactorPhoneCode(bsiOpportunity.getMobileCode())
                .contactorRole(bsiOpportunity.getRole())
                .contactorPhone(bsiOpportunity.getMobile())
                .contactorEmail(bsiOpportunity.getEmail())
                .contactorAddressArea(bsiOpportunity.getAddressCity())
                .contactorAddressDetail(bsiOpportunity.getAddressDetail())
                .contactorLandline(bsiOpportunity.getPhone())
                .contactorWechat(bsiOpportunity.getWechat())
                .build();
    }

    private boolean isNewCustomer(BsiOpportunityDto bsiOpportunity) {
        return !Utils.isPositive(bsiOpportunity.getCustomerId());
    }

    private BsiOpportunityDto buildDetail(BsiOpportunityDto dto, BsiOpportunityDto saveDto) {
        if (dto == null) {
            return null;
        }
        Map<Integer, List<BsiOpportunityIntentProductMappingDto>> intentProductDtoMap = bsiOpportunityIntentProductService.getOpportunityIntentProductDtoMap(BsiIntentProductQueryDto.builder()
                .bsiOpportunityIds(Lists.newArrayList(dto.getId()))
                .build());
        Map<Integer, BsiOpportunityPlannerDto> plannerDtoMap = bsiOpportunityPlannerRepo.queryPlanMap(Lists.newArrayList(dto.getId()));
        BsiOpportunityPlannerDto plannerDto = plannerDtoMap.getOrDefault(dto.getId(), new BsiOpportunityPlannerDto());
        List<BsiOpportunityIntentProductMappingDto> intentInfoList = Optional.of(intentProductDtoMap)
                .orElse(Collections.emptyMap()).getOrDefault(dto.getId(), Collections.emptyList());
        dto.setPlannerLeaderName(plannerDto.getPlannerLeaderName());
        dto.setAddressForLog(dto.getAddressCity() + dto.getAddressDetail());
        dto.setExpectLaunchDateForLog(CrmUtils.formatDate(dto.getExpectLaunchStartDate()) + "~" + CrmUtils.formatDate(dto.getExpectLaunchEndDate()));
        dto.setIntentProductInfoForLog(buildProduct(intentInfoList));
        if (Objects.nonNull(saveDto.getAccountId())) {
            dto.setProductLineId(saveDto.getProductLineId());
            dto.setProductLineName(saveDto.getProductLineName());
            dto.setProductId(saveDto.getProductId());
            dto.setProductName(saveDto.getProductName());
            dto.setCommerceCategoryFirstId(saveDto.getCommerceCategoryFirstId());
            dto.setCommerceCategorySecondId(saveDto.getCommerceCategorySecondId());
            dto.setBrandDomain(saveDto.getBrandDomain());
        }
        return dto;
    }

    private String buildProduct(List<BsiOpportunityIntentProductMappingDto> valueList) {
        if (!org.springframework.util.CollectionUtils.isEmpty(valueList)) {
            AtomicReference<String> oldValue = new AtomicReference<>("");
            if (valueList.get(0).getUnpackQuarter().equals(IsValid.FALSE.getCode())) {
                oldValue.set(oldValue + "不拆分季度,");
                valueList.forEach(a -> {
                    oldValue.set(oldValue + OpsProductCategory.getByCode(a.getIntentProductType()).getDesc() + ":" + Utils.fromFenToYuan(Optional.ofNullable(a.getTotalAmount()).orElse(0L)) + "元, ");
                });
            } else {
                oldValue.set(oldValue + "拆分季度,");
                valueList.forEach(a -> {
                    oldValue.set(oldValue + OpsProductCategory.getByCode(a.getIntentProductType()).getDesc() + "本季度:" + Utils.fromFenToYuan(Optional.ofNullable(a.getQuarterAmount()).orElse(0L)) + "元, " +
                            OpsProductCategory.getByCode(a.getIntentProductType()).getDesc() + "其他季度:" + Utils.fromFenToYuan(Optional.ofNullable(a.getOtherQuarterAmount()).orElse(0L)) + "元, ");
                });
            }
            String value = oldValue.get();
            int idx = value.lastIndexOf(",");
            return value.substring(0, idx);
        }
        return "";
    }

    public void insertOrUpdateBsiOpportunitySaleMapping(BsiOpportunityDto bsiOpportunity, Operator operator) {
        Assert.notNull(bsiOpportunity.getId(), "商机id不能为空");
        AlarmHelper.log("InsertBsiOpSaleMapping", bsiOpportunity, operator);
        if (bsiOpportunity.getDirectSales() != null) {
            //先判断跟原来相比是否需要变更
            BsiOpportunitySaleMappingPoExample selectDirectExample = new BsiOpportunitySaleMappingPoExample();
            selectDirectExample.createCriteria().andIsDeletedEqualTo(IsValid.FALSE.getCode())
                    .andBsiOpportunityIdEqualTo(bsiOpportunity.getId())
                    .andSaleTypeIn(Lists.newArrayList(BiliUserType.STRAIGHT_MANAGER.getCode()));
            List<BsiOpportunitySaleMappingPo> saleMappingPoList = bsiOpportunitySaleMappingDao.selectByExample(selectDirectExample);
            List<Integer> oldSaleList = saleMappingPoList.stream().map(BsiOpportunitySaleMappingPo::getSaleId).distinct().collect(Collectors.toList());
            List<Integer> newSaleList = bsiOpportunity.getDirectSales().stream().distinct().collect(Collectors.toList());
            AlarmHelper.log("JudgeExists", oldSaleList, newSaleList);
            if (!(oldSaleList.containsAll(newSaleList) && newSaleList.containsAll(oldSaleList))) {
                //先删除
                bsiOpportunitySaleMappingDao.deleteByExample(selectDirectExample);
                List<BsiOpportunitySaleMappingPo> directRecords = bsiOpportunity.getDirectSales().stream().distinct().map(e -> BsiOpportunitySaleMappingPo.builder()
                        .bsiOpportunityId(bsiOpportunity.getId())
                        .operator(operator.getOperatorName())
                        .saleId(e)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .ctime(Utils.getNow())
                        .mtime(Utils.getNow())
                        .saleType(BiliUserType.STRAIGHT_MANAGER.getCode()).build()).collect(Collectors.toList());
                AlarmHelper.log("InsertMapping", directRecords);
                if (CollectionUtils.isNotEmpty(directRecords)) {
                    bsiOpportunitySaleMappingDao.insertBatch(directRecords);
                }
                //记录操作日志
                NewLogOperatorDto logDto = NewLogOperatorDto
                        .builder()
                        .modifyType(ModifyType.BSI_OPPORTUNITY_DIRECT_SALE_EDIT)
                        .module(Module.BSI_OPPORTUNITY)
                        .obj(bsiOpportunity)
                        .objId(bsiOpportunity.getId())
                        .systemType(SystemType.CRM)
                        .oldObj(saleMappingPoList.stream()
                                .filter(e -> BiliUserType.STRAIGHT_MANAGER.getCode().equals(e.getSaleType()))
                                .map(BsiOpportunitySaleMappingPo::getSaleId)
                                .map(Object::toString).collect(Collectors.joining(",")))
                        .obj(bsiOpportunity.getDirectSales().stream().distinct().map(Object::toString).collect(Collectors.joining(",")))
                        .build();
                logOperatorService.insertLog(operator, logDto);
            }
        }
        if (bsiOpportunity.getChannelSales() != null) {
            //先判断跟原来相比是否需要变更
            BsiOpportunitySaleMappingPoExample selectChannelExample = new BsiOpportunitySaleMappingPoExample();
            selectChannelExample.createCriteria().andIsDeletedEqualTo(IsValid.FALSE.getCode())
                    .andBsiOpportunityIdEqualTo(bsiOpportunity.getId())
                    .andSaleTypeIn(Lists.newArrayList(BiliUserType.CHANNEL_MANAGER.getCode()));
            List<BsiOpportunitySaleMappingPo> saleMappingPoList = bsiOpportunitySaleMappingDao.selectByExample(selectChannelExample);
            List<Integer> oldSaleList = saleMappingPoList.stream().map(BsiOpportunitySaleMappingPo::getSaleId).distinct().collect(Collectors.toList());
            List<Integer> newSaleList = bsiOpportunity.getChannelSales().stream().distinct().collect(Collectors.toList());
            AlarmHelper.log("JudgeExists", oldSaleList, newSaleList);
            if (!(oldSaleList.containsAll(newSaleList) && newSaleList.containsAll(oldSaleList))) {
                //先删除
                bsiOpportunitySaleMappingDao.deleteByExample(selectChannelExample);
                List<BsiOpportunitySaleMappingPo> channelRecords = bsiOpportunity.getChannelSales().stream().distinct().map(e -> BsiOpportunitySaleMappingPo.builder()
                        .bsiOpportunityId(bsiOpportunity.getId())
                        .operator(operator.getOperatorName())
                        .saleId(e)
                        .isDeleted(IsDeleted.VALID.getCode())
                        .ctime(Utils.getNow())
                        .mtime(Utils.getNow())
                        .saleType(BiliUserType.CHANNEL_MANAGER.getCode()).build()).collect(Collectors.toList());
                AlarmHelper.log("InsertMapping", channelRecords);
                if (CollectionUtils.isNotEmpty(channelRecords)) {
                    bsiOpportunitySaleMappingDao.insertBatch(channelRecords);
                }
                //记录操作日志
                NewLogOperatorDto logDto = NewLogOperatorDto
                        .builder()
                        .modifyType(ModifyType.BSI_OPPORTUNITY_CHANNEL_SALE_EDIT)
                        .module(Module.BSI_OPPORTUNITY)
                        .obj(bsiOpportunity)
                        .objId(bsiOpportunity.getId())
                        .systemType(SystemType.CRM)
                        .oldObj(saleMappingPoList.stream()
                                .map(BsiOpportunitySaleMappingPo::getSaleId)
                                .map(Object::toString).collect(Collectors.joining(",")))
                        .obj(bsiOpportunity.getChannelSales().stream().distinct().map(Object::toString).collect(Collectors.joining(",")))
                        .build();
                logOperatorService.insertLog(operator, logDto);
            }
        }
    }

    public BsiOpportunityFollowRecordPo getLastBsiFollowRecordPo(Integer bsiId, Integer oldFollowStage) {
        //计算上一次跟进状态记录的创建时间
        BsiOpportunityFollowRecordPoExample recordPoExample = new BsiOpportunityFollowRecordPoExample();
        recordPoExample.createCriteria().andBusiOpportunityIdEqualTo(bsiId)
                .andNowFollowStageEqualTo(oldFollowStage);
        recordPoExample.setOrderByClause("id desc");
        List<BsiOpportunityFollowRecordPo> bsiOpportunityFollowRecordPoList = bsiOpportunityFollowRecordDao.selectByExample(recordPoExample);
        if (CollectionUtils.isEmpty(bsiOpportunityFollowRecordPoList)) {
            return null;
        }
        return bsiOpportunityFollowRecordPoList.get(0);
    }

    public BsiOpportunityFollowRecordPo getNewLastBsiFollowRecordPo(Integer bsiId, Integer oldNewFollowStage) {
        //计算上一次跟进状态记录的创建时间
        BsiOpportunityFollowRecordPoExample recordPoExample = new BsiOpportunityFollowRecordPoExample();
        recordPoExample.createCriteria().andBusiOpportunityIdEqualTo(bsiId)
                .andNewNowFollowStageEqualTo(oldNewFollowStage);
        recordPoExample.setOrderByClause("id desc");
        List<BsiOpportunityFollowRecordPo> bsiOpportunityFollowRecordPoList = bsiOpportunityFollowRecordDao.selectByExample(recordPoExample);
        if (CollectionUtils.isEmpty(bsiOpportunityFollowRecordPoList)) {
            return null;
        }
        return bsiOpportunityFollowRecordPoList.get(0);
    }

    public List<BsiOpportunityFollowRecordPo> getBsiFollowRecordPos(List<Integer> bsiIds, Integer followStage) {
        //计算上一次跟进状态记录的创建时间
        BsiOpportunityFollowRecordPoExample recordPoExample = new BsiOpportunityFollowRecordPoExample();
        recordPoExample.createCriteria().andBusiOpportunityIdIn(bsiIds)
                .andNowFollowStageEqualTo(followStage);
        recordPoExample.setOrderByClause("id desc");
        return bsiOpportunityFollowRecordDao.selectByExample(recordPoExample);
    }

    public Map<Integer, List<BsiOpportunityFollowRecordPo>> getBsiFollow(Integer bsiId) {
        //计算上一次跟进状态记录的创建时间
        BsiOpportunityFollowRecordPoExample recordPoExample = new BsiOpportunityFollowRecordPoExample();
        recordPoExample.createCriteria().andBusiOpportunityIdEqualTo(bsiId);
        recordPoExample.setOrderByClause("id desc");
        List<BsiOpportunityFollowRecordPo> recordPos = bsiOpportunityFollowRecordDao.selectByExample(recordPoExample);
        recordPos = recordPos.stream().peek(a -> {
            if (a.getNowFollowStage() == 1000) {// 未知
                a.setNowFollowStage(FollowStage.queryByFollowRecord(a.getFollowRecord()));
            }
        }).collect(Collectors.toList());
        return Optional.ofNullable(recordPos).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(BsiOpportunityFollowRecordPo::getNowFollowStage));
    }

    @Override
    public void buildRecordToOrderGap(List<BsiOpportunityDto> datas) {
        List<Integer> ids = datas.stream().map(BsiOpportunityDto::getId).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(ids)) {
            return;
        }
        List<BsiOpportunityFollowRecordPo> bsiFollowRecordPos = getBsiFollowRecordPos(ids, FollowStage.COMPLETE_ORDER.getCode());
        Map<Integer, BsiOpportunityFollowRecordPo> followRecordPoMap = bsiFollowRecordPos.stream()
                .collect(Collectors.toMap(BsiOpportunityFollowRecordPo::getBusiOpportunityId, Function.identity(), (r1, r2) -> r2));
        datas.forEach(item -> {
            BsiOpportunityFollowRecordPo followRecordPo = followRecordPoMap.get(item.getId());
            if (null == followRecordPo) {
                item.setRecordToOrderGap(0);
                return;
            }
            item.setRecordToOrderGap(new BigDecimal((followRecordPo.getCtime().getTime() - item.getCtime().getTime()) / (3600 * 1000L))
                    .setScale(0, BigDecimal.ROUND_HALF_UP).intValue());
        });
    }

    /**
     * @param oppIds
     * @return
     */
    public List<BsiOpportunityBizMappingPo> queryOppBizMapping(List<Integer> oppIds, BsiOpportunityBizMappingTypeEnum bsiOpportunityBizMappingTypeEnum) {
        if (CollectionUtils.isEmpty(oppIds)) {
            return Collections.emptyList();
        }
        BsiOpportunityBizMappingPoExample bsiOpportunityBizMappingPoExample = new BsiOpportunityBizMappingPoExample();
        bsiOpportunityBizMappingPoExample.createCriteria()
                .andBsiOpportunityIdIn(oppIds)
                .andBizTypeEqualTo(bsiOpportunityBizMappingTypeEnum.getId())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return bsiOpportunityBizMappingDao.selectByExample(bsiOpportunityBizMappingPoExample);
    }

    @Override
    public Map<Integer, List<Integer>> queryBizOppMap(List<Integer> bizIds, BsiOpportunityBizMappingTypeEnum bsiOpportunityBizMappingTypeEnum) {
        if (CollectionUtils.isEmpty(bizIds)) {
            return Maps.newHashMap();
        }
        BsiOpportunityBizMappingPoExample poExample = new BsiOpportunityBizMappingPoExample();
        BsiOpportunityBizMappingPoExample.Criteria criteria = poExample.createCriteria();
        criteria.andBizIdIn(bizIds);
        criteria.andBizTypeEqualTo(bsiOpportunityBizMappingTypeEnum.getId());
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<BsiOpportunityBizMappingPo> mappingPos = bsiOpportunityBizMappingDao.selectByExample(poExample);
        return Optional.ofNullable(mappingPos).orElse(new ArrayList<>()).stream().collect(Collectors.groupingBy(BsiOpportunityBizMappingPo::getBizId, Collectors.mapping(BsiOpportunityBizMappingPo::getBsiOpportunityId, Collectors.toList())));
    }

    @Override
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    public int bindBsiOpportunityAccount(BsiOpportunityDto bsiOpportunity, Operator operator) {

        BsiOpportunityPo existBsi = bsiOpportunity.getId() == null ? null : bsiOpportunityDao.selectByPrimaryKey(bsiOpportunity.getId());
        Assert.notNull(existBsi, "需要修改的商机不存在");

        BsiOpportunityPo bsiOpportunityPo = new BsiOpportunityPo();
        Assert.notNull(bsiOpportunity.getAccountId(), "账户ID未填写");
        AccountBaseDto accountBaseDto = accountService.queryAccountByAccountId(bsiOpportunity.getAccountId());
        Assert.notNull(accountBaseDto, "账户ID不存在");
        if (existBsi.getCustomerId() != null) {
            Assert.isTrue(Optional.ofNullable(accountBaseDto.getCustomerId()).orElse(0) - Optional.ofNullable(existBsi.getCustomerId()).orElse(0) == 0, "商机绑定账户与商机绑定客户不匹配");
        }
        bsiOpportunityPo.setAccountId(bsiOpportunity.getAccountId());
        bsiOpportunityPo.setProductId(accountBaseDto.getProductId());
        bsiOpportunityPo.setBrandDomain(accountBaseDto.getBrandDomain());
        bsiOpportunityPo.setCommerceCategoryFirstId(accountBaseDto.getCommerceCategoryFirstId());
        bsiOpportunityPo.setCommerceCategorySecondId(accountBaseDto.getCommerceCategorySecondId());
        bsiOpportunityPo.setProductLineId(accountBaseDto.getProductLineId());
        bsiOpportunityPo.setStatus(1);
        bsiOpportunityPo.setId(bsiOpportunity.getId());

        bsiOpportunityPo.setUpdateUser(operator.getOperatorName());

        bsiOpportunityDao.updateByPrimaryKeySelective(bsiOpportunityPo);

        //记录操作日志，商机绑定账号
        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.BSI_OPPORTUNITY_BIND_ACCOUNT)
                .module(Module.BSI_OPPORTUNITY)
                .obj(bsiOpportunity.getAccountId())
                .objId(bsiOpportunity.getId())
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logDto);
        return bsiOpportunityPo.getId();
    }


    private void separateAddAndDelAttachment(List<BsiOpportunityAttachmentPo> neddAddAttachmentList, List<Integer> needDelAttachmentList,
                                             BsiOpportunityDto bsiOpportunity, Operator operator) {

        Map<Integer, List<BsiOpportunityAttachmentPo>> typeMapPo = queryAttachmentsByBsiOpporId(bsiOpportunity.getId());

        //附件类型：1、营业执照照片；2、其他资质; 3、附件
        BsiOpportunityAttachmentPo existPo = CollectionUtils.isNotEmpty(typeMapPo.get(1)) ? typeMapPo.get(1).get(0) : null;

        BsiOpportunityAttachmentPo updatePo = convert2AttachmentPoFromDto(bsiOpportunity.getBusiLicensePic(), BsiAttachmentType.LICENSE_PIC.getCode(), operator);
        if (updatePo == null && existPo != null) {
            needDelAttachmentList.add(existPo.getId());
        } else if (updatePo != null && existPo == null) {
            neddAddAttachmentList.add(updatePo);
        } else if (updatePo != null && existPo != null && !existPo.equals(updatePo)) {
            // 编辑中的附件和数据库中的附件都不为空，且不相同，则需要删除数据库中的附件，新增编辑中的附件
            needDelAttachmentList.add(existPo.getId());
            neddAddAttachmentList.add(updatePo);
        }

        List<BsiOpportunityAttachmentPo> updatePoList = new ArrayList<>();
        if (CollectionUtils.isNotEmpty(bsiOpportunity.getOtherQualifys())) {
            bsiOpportunity.getOtherQualifys().forEach(dto -> updatePoList.add(convert2AttachmentPoFromDto(dto, BsiAttachmentType.OTHER_QUALIFICATION.getCode(), operator)));
        }
        List<BsiOpportunityAttachmentPo> existPoList = typeMapPo.get(2);
        if (CollectionUtils.isEmpty(updatePoList) && CollectionUtils.isNotEmpty(existPoList)) {
            existPoList.forEach(po -> needDelAttachmentList.add(po.getId()));
        } else if (CollectionUtils.isNotEmpty(updatePoList) && CollectionUtils.isEmpty(existPoList)) {
            neddAddAttachmentList.addAll(updatePoList);
        } else if (CollectionUtils.isNotEmpty(updatePoList) && CollectionUtils.isNotEmpty(existPoList)) {
            Map<String, BsiOpportunityAttachmentPo> existPoMap = new HashMap<>();
            for (BsiOpportunityAttachmentPo po : existPoList) {
                existPoMap.put(StringUtils.join(po.getFileName(), "#", po.getOssKey()), po);
            }

            for (BsiOpportunityAttachmentPo po : updatePoList) {
                String key = StringUtils.join(po.getFileName(), "#", po.getOssKey());
                if (!existPoMap.containsKey(key)) { // 数据库中不存在需要修改的
                    neddAddAttachmentList.add(po);
                    continue;
                }
                existPoMap.remove(key);
            }

            for (Entry<String, BsiOpportunityAttachmentPo> entry : existPoMap.entrySet()) {
                needDelAttachmentList.add(entry.getValue().getId());
            }
        }
    }

    private BsiOpportunityAttachmentPo convert2AttachmentPoFromDto(String ossKey, Integer attachmentType, Operator operator) {
        if (StringUtils.isNotBlank(ossKey)) {
            return convert2AttachmentPoFromDto(new BsiOpportunityAttachmentDto("", ossKey), attachmentType, operator);
        }
        return null;
    }

    private BsiOpportunityAttachmentPo convert2AttachmentPoFromDto(BsiOpportunityAttachmentDto attachmentDto, Integer attachmentType, Operator operator) {
        if (attachmentDto == null) {
            return null;
        }
        BsiOpportunityAttachmentPo attachmentPo = new BsiOpportunityAttachmentPo();
        attachmentPo.setAttachmentType(attachmentType);
        attachmentPo.setFileName(attachmentDto.getFileName());
        attachmentPo.setOssKey(attachmentDto.getOssKey());
        attachmentPo.setCreateUser(operator.getOperatorName());
        attachmentPo.setUpdateUser(operator.getOperatorName());

        return attachmentPo;
    }

    private Map<Integer, List<BsiOpportunityAttachmentPo>> queryAttachmentsByBsiOpporId(Integer opporId) {
        List<BsiOpportunityAttachmentPo> list = null;
        if (opporId != null) {
            BsiOpportunityAttachmentPoExample example = new BsiOpportunityAttachmentPoExample();
            example.or().andBusiOpportunityIdEqualTo(opporId).andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            list = bsiOpportunityAttachmentDao.selectByExample(example);
        }

        if (CollectionUtils.isNotEmpty(list)) {
            return list.stream().collect(Collectors.groupingBy(BsiOpportunityAttachmentPo::getAttachmentType));
        }
        return Collections.emptyMap();
    }

    private BsiOpportunityFollowRecordPo convert2FollowRecordPo(BsiOpportunityPo existBsi, BsiOpportunityPo bsiOpportunityPo, Operator operator, Integer bizType, Long bizId) {
        BsiOpportunityFollowRecordPo followRecordPo = new BsiOpportunityFollowRecordPo();
        followRecordPo.setCreateUser(operator.getOperatorName());
        followRecordPo.setUpdateUser(operator.getOperatorName());
        followRecordPo.setBusiOpportunityId(bsiOpportunityPo.getId());
        followRecordPo.setRelateBizId(bizId);
        followRecordPo.setRelateBizType(bizType);

        StringBuilder builder = new StringBuilder();
        if (null == bsiOpportunityPo.getNewFollowStage()) {
            builder.append("跟进阶段：").append(FollowStage.getByCode(bsiOpportunityPo.getFollowStage()).getName())
                    .append("; 备注：").append(bsiOpportunityPo.getRecentFollowRecord());
        } else {
            builder.append("跟进阶段：").append(BsiFollowStageEnum.getEnumByCode(bsiOpportunityPo.getNewFollowStage()).getDesc())
                    .append("; 备注：").append(bsiOpportunityPo.getRecentFollowRecord());
        }

        followRecordPo.setFollowRecord(builder.toString());
        followRecordPo.setFollowRecordType(bsiOpportunityPo.getId() == null ? 1 : 2); // 商机更新记录类型，1新建商机跟进记录，2商机编辑跟进记录
        if (existBsi == null && FollowStage.getThirtyAndOneHundredStatus().contains(bsiOpportunityPo.getFollowStage())) {
            followRecordPo.setOldFollowStage(FollowStage.BRIEF.getCode());
            followRecordPo.setNowFollowStage(bsiOpportunityPo.getFollowStage());
            //如果商机新建的时候，比如就选了30%，那算从10%-30%用了1天吧
            followRecordPo.setConvertDays(1);
            followRecordPo.setNewOldFollowStage(BsiFollowStageEnum.BRIEF.getCode());
            followRecordPo.setNewNowFollowStage(bsiOpportunityPo.getNewFollowStage());
        } else if (existBsi != null && !existBsi.getFollowStage().equals(bsiOpportunityPo.getFollowStage())
                && FollowStage.getThirtyAndOneHundredStatus().contains(bsiOpportunityPo.getFollowStage())) {
            followRecordPo.setOldFollowStage(existBsi.getFollowStage());
            followRecordPo.setNewOldFollowStage(existBsi.getNewFollowStage());
            followRecordPo.setNowFollowStage(bsiOpportunityPo.getFollowStage());
            followRecordPo.setNewNowFollowStage(bsiOpportunityPo.getNewFollowStage());
            //计算上一次跟进状态记录的创建时间
            BsiOpportunityFollowRecordPo lastBsiFollowRecordPo = getLastBsiFollowRecordPo(bsiOpportunityPo.getId(), existBsi.getFollowStage());
            if (lastBsiFollowRecordPo != null) {
                followRecordPo.setConvertDays(Utils.getDateSpace(lastBsiFollowRecordPo.getCtime(), Utils.getNow()));
            } else {
                // TODO 异常数据暂不报错
                followRecordPo.setConvertDays(Utils.getDateSpace(existBsi.getCtime(), Utils.getNow()));
            }
        } else if (existBsi != null && existBsi.getFollowStage().equals(bsiOpportunityPo.getFollowStage())) {
            //计算上一次跟进状态记录的创建时间
            BsiOpportunityFollowRecordPo lastBsiFollowRecordPo = getLastBsiFollowRecordPo(bsiOpportunityPo.getId(), existBsi.getFollowStage());
            if (lastBsiFollowRecordPo != null) {
                followRecordPo.setOldFollowStage(lastBsiFollowRecordPo.getOldFollowStage());
                followRecordPo.setNowFollowStage(lastBsiFollowRecordPo.getNowFollowStage());
                followRecordPo.setConvertDays(lastBsiFollowRecordPo.getConvertDays());
            }
            BsiOpportunityFollowRecordPo newLastBsiFollowRecordPo = getNewLastBsiFollowRecordPo(bsiOpportunityPo.getId(), existBsi.getNewFollowStage());
            if (newLastBsiFollowRecordPo != null) {
                followRecordPo.setNewOldFollowStage(newLastBsiFollowRecordPo.getNewOldFollowStage());
                followRecordPo.setNewNowFollowStage(newLastBsiFollowRecordPo.getNewNowFollowStage());
                followRecordPo.setConvertDays(newLastBsiFollowRecordPo.getConvertDays());
            }
        } else {
            followRecordPo.setOldFollowStage(existBsi == null ? null : existBsi.getFollowStage());
            followRecordPo.setNowFollowStage(bsiOpportunityPo.getFollowStage());
            followRecordPo.setNewOldFollowStage(existBsi == null ? null : existBsi.getNewFollowStage());
            followRecordPo.setNewNowFollowStage(bsiOpportunityPo.getNewFollowStage());
        }
        return followRecordPo;
    }

    private BsiOpportunityPo convert2PoFromDto(BsiOpportunityDto bsiOpportunity, Operator operator) {
        BsiOpportunityPo bsiOpportunityPo = new BsiOpportunityPo();
        BeanUtils.copyProperties(bsiOpportunity, bsiOpportunityPo);
        if (bsiOpportunity.getId() == null) {
            bsiOpportunityPo.setCreateUser(operator.getOperatorName());
        }
        if (bsiOpportunityPo.getAccountId() != null && bsiOpportunityPo.getAccountId() > 0) {
            bsiOpportunityPo.setStatus(1);
        }
        bsiOpportunityPo.setFirstProductModel(bsiOpportunity.getFirstProductModelLabel());
        bsiOpportunityPo.setSecondProductModel(bsiOpportunity.getSecondProductModelLabel());
        bsiOpportunityPo.setFirstProductModelId(bsiOpportunity.getFirstProductModelId());
        bsiOpportunityPo.setSecondProductModelId(bsiOpportunity.getSecondProductModelId());
        // TODO ID【4300357】全量发布后删除，须确认上游其他系统是否依赖（不限于前端）
        bsiOpportunityPo.setProductLabelType(bsiOpportunity.getProductLabelType());
        bsiOpportunityPo.setUpdateUser(operator.getOperatorName());
        bsiOpportunityPo.setIsUnpackQuarter(bsiOpportunity.getIsUnpackQuarter());
        bsiOpportunityPo.setCategoryFirstId(Optional.ofNullable(bsiOpportunity.getCategoryFirstId()).orElse(0));
        bsiOpportunityPo.setCategorySecondId(Optional.ofNullable(bsiOpportunity.getCategorySecondId()).orElse(0));
        bsiOpportunityPo.setUnitedFirstIndustryId(Optional.ofNullable(bsiOpportunity.getUnitedFirstIndustryId()).orElse(0));
        bsiOpportunityPo.setUnitedSecondIndustryId(Optional.ofNullable(bsiOpportunity.getUnitedSecondIndustryId()).orElse(0));
        bsiOpportunityPo.setUnitedThirdIndustryId(Optional.ofNullable(bsiOpportunity.getUnitedThirdIndustryId()).orElse(0));

        bsiOpportunityPo.setCustomerId(Optional.ofNullable(bsiOpportunity.getCustomerId()).orElse(0));
        bsiOpportunityPo.setGroupId(Optional.ofNullable(bsiOpportunity.getGroupId()).orElse(0));
        bsiOpportunityPo.setProductId(Optional.ofNullable(bsiOpportunity.getProductId()).orElse(0));
        if (bsiOpportunityPo.getFollowStage() != null && FollowStage.ABANDON_ORDER.getCode().equals(bsiOpportunityPo.getFollowStage())) {
            bsiOpportunityPo.setBsiStatus(BsiOpportunityStatus.ABANDON_ORDER.getCode());
        }
        return bsiOpportunityPo;
    }

    public void validateNewCustomerReport(Operator operator, String companyFullName) {
        bsiOppComponent.validateNewCustomerReport(operator, companyFullName);
    }

    @Override
    public PageResult<BsiOpportunityDto> queryBrandBsiOpportunityList(BsiOpportunityQueryDto queryDto, Integer page, Integer size) {
        BsiOpportunityPoExample example = createBranchExampleFromQueryDto(queryDto);
        List<BsiOpportunityDto> dtoList = new ArrayList<>();
        Long total = bsiOpportunityDao.countByExample(example);
        if (total > 0) {
            Page pg = Page.valueOf(page, size);
            example.setOffset(pg.getOffset());
            example.setLimit(pg.getLimit());
            example.setOrderByClause("mtime desc");
            this.setOrderByClause(queryDto, example);
            List<BsiOpportunityPo> dbResultList = bsiOpportunityDao.selectByExample(example);
            dtoList = poToDto(dbResultList);
        }
        return new PageResult<>(total.intValue(), dtoList);
    }


    public List<Integer> getIntersection(List<Integer> list1, List<Integer> list2) {
        // 使用Stream API的filter方法过滤出同时存在于两个集合中的元素
        return list1.stream().filter(list2::contains).collect(Collectors.toList());
    }

    @Override
    public PageResult<BsiOpportunityDto> queryBsiOpportunityListForFunnel(BsiOpportunityQueryDto queryDto) {
        log.info("bsi_funnel,queryBsiOpportunityListForFunnel_queryDto_start={}", queryDto);
        //如果有销售组条件
        if (Objects.nonNull(queryDto.getSaleGroupId())) {
//            List<Integer> saleIdByGroup = saleGroupService.getSaleIdByGroupId(Lists.newArrayList(queryDto.getSaleGroupId()));
            List<Integer> saleIdByGroup = saleGroupService.getChildSaleIdByGroupId(Lists.newArrayList(queryDto.getSaleGroupId()));
            if (CollectionUtils.isNotEmpty(queryDto.getSaleIdList()) && CollectionUtils.isEmpty(getIntersection(saleIdByGroup, queryDto.getSaleIdList()))) {
                return PageResult.emptyPageResult();
            }
            if (CollectionUtils.isEmpty(queryDto.getSaleIdList())) {
                queryDto.setSaleIdList(saleIdByGroup);
            } else {
                queryDto.setSaleIdList(saleIdByGroup.stream().filter(queryDto.getSaleIdList()::contains).collect(Collectors.toList()));
            }
        }
        //带了saleIdList转成商机idList
        if (CollectionUtils.isNotEmpty(queryDto.getSaleIdList())) {
            List<Integer> saleByBsiIdList = buildHaveSaleIdListCondition(queryDto.getSaleIdList());
            if (CollectionUtils.isEmpty(saleByBsiIdList)) {
                queryDto.setId(-1);
            } else {
                if (CollectionUtils.isNotEmpty(queryDto.getIds())) {
                    queryDto.getIds().retainAll(saleByBsiIdList);//保留列表中与指定集合相同的元素
                } else {
                    queryDto.setIds(saleByBsiIdList);
                }
            }
        }
        //如果有代理商客户id条件 换成代理商agent_id
        if (CollectionUtils.isNotEmpty(queryDto.getAgentCustomerIdList())) {
            List<AgentDto> agentIdList = agentService.getAgentDtoIdListByAgentCustomerId(queryDto.getAgentCustomerIdList());
            if (CollectionUtils.isEmpty(agentIdList)) {
                return PageResult.emptyPageResult();
            }
            queryDto.setAgentIds(agentIdList.stream().map(AgentDto::getId).collect(Collectors.toList()));
        }
        //如果有KA标签
        if (Objects.nonNull(queryDto.getKaLevel())) {
            Assert.isTrue(Objects.nonNull(queryDto.getKaBeginTime()) && Objects.nonNull(queryDto.getKaEndTime()), "ka生效时间不能为空");
            List<Integer> mappingIds = kaBelongIngService.queryOnlyMappingIdByQueryDTO(KaBelongIngQueryDTO.builder()
                    .configLevel(queryDto.getKaLevel())
                    .mappingType(KaBelongingMappingTypeEnum.CUSTOMER.getCode())
                    .beginTime(queryDto.getKaBeginTime())
                    .endTime(queryDto.getKaEndTime()).build());
            if (Objects.equals(queryDto.getKaLevel(), 0)) {
                queryDto.setExcludeCustomerIds(mappingIds);
            } else {
                if (CollectionUtils.isEmpty(mappingIds)) {
                    return PageResult.emptyPageResult();
                }
                if (!CollectionUtils.isEmpty(queryDto.getAccountCustomerIdList())) {
                    mappingIds.retainAll(queryDto.getAccountCustomerIdList());
                    if (CollectionUtils.isEmpty(mappingIds)) {
                        return PageResult.emptyPageResult();
                    }
                }
                queryDto.setAccountCustomerIdList(mappingIds);
            }
        }
        BsiOpportunityPoExample bsiExample = createBsiExampleForFunnelQueryDto(queryDto);
        long total = bsiOpportunityDao.countByExample(bsiExample);
        if (total <= 0) {
            return PageResult.emptyPageResult();
        }

        if (null != queryDto.getPage() && null != queryDto.getSize()) {
            Page page = Page.valueOf(queryDto.getPage(), queryDto.getSize());
            bsiExample.setLimit(page.getLimit());
            bsiExample.setOffset(page.getOffset());
        }

        bsiExample.setOrderByClause(" id DESC ");
        List<BsiOpportunityPo> dbResultList = bsiOpportunityDao.selectByExample(bsiExample);
        if (CollectionUtils.isEmpty(dbResultList)) {
            return PageResult.emptyPageResult();
        }
        //构建所属销售
        List<Integer> bsiIds = dbResultList.stream().map(BsiOpportunityPo::getId).collect(Collectors.toList());
        Map<Integer, List<Integer>> channelSaleMap = queryBsiOpportunitySaleMapByType(bsiIds, BiliUserType.CHANNEL_MANAGER.getCode());
        Map<Integer, List<Integer>> dircetSaleMap = queryBsiOpportunitySaleMapByType(bsiIds, BiliUserType.STRAIGHT_MANAGER.getCode());
        List<BsiOpportunityDto> dtoList = dbResultList.stream().map(po -> {
            BsiOpportunityDto dto = new BsiOpportunityDto();
            BeanUtils.copyProperties(po, dto);
            if (FollowStage.getTenAndNinetyStatus().contains(po.getFollowStage())) {
                dto.setExpireDate(Utils.getSomeDayAfter(po.getCtime(), BSI_OPPORTUNITY_INVALID));//商机过期时间(数据表没有字段，计算逻辑得出 创建时间+60天)
            }
            dto.setDirectSales(dircetSaleMap.getOrDefault(po.getId(), Collections.emptyList()));
            dto.setChannelSales(channelSaleMap.getOrDefault(po.getId(), Collections.emptyList()));
            return dto;
        }).collect(Collectors.toList());

        return new PageResult<BsiOpportunityDto>(Integer.parseInt(String.valueOf(total)), dtoList);
    }

    @Override
    public List<BsiOpportunityDto> queryBsiOpportunityListForNewCustomer(String customerName, boolean isAgent) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        Criteria criteria = example.createCriteria();
        criteria.andCompanyFullNameEqualTo(customerName);
        // 查询默认创建时间排序
        example.setOrderByClause("ctime desc");
        List<BsiOpportunityPo> pos = bsiOpportunityDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::po2Dto).map(this::fillBsiContactor).collect(Collectors.toList());
    }

    @Override
    public List<BsiOpportunityDto> queryBsiOpportunityListFroCustomer(Integer customerId, boolean isAgent) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        Criteria criteria = example.createCriteria();
        if (isAgent) {
            handleAgentCustomer(criteria, customerId);
        } else {
            criteria.andCustomerIdEqualTo(customerId);
        }
        // 查询默认创建时间排序
        example.setOrderByClause("ctime desc");
        List<BsiOpportunityPo> pos = bsiOpportunityDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::po2Dto).map(this::fillBsiContactor).collect(Collectors.toList());
    }

    private void handleAgentCustomer(Criteria criteria, Integer customerId) {
        // 查询代理商客户下所有的账号
        List<AccountBaseDto> accounts = fastQueryAccountService.fetch(QueryAccountParam.builder().customerId(customerId).build(), AccountFieldMapping.accountId);
        // 查询账号关联的crm_agent代理商id
        if (CollectionUtils.isNotEmpty(accounts)) {
            List<Integer> agentIds = agentService.getAgentIdListByAgentAccountIds(accounts.stream().map(AccountBaseDto::getAccountId).collect(Collectors.toList()));
            if (CollectionUtils.isNotEmpty(agentIds)) {
                criteria.andAgentIdIn(agentIds);
            }
        }

    }

    @Override
    public PageResult<BsiOpportunityDto> queryBsiOpportunityList(BsiOpportunityQueryDto queryDto, Integer page, Integer size) {
        setOpportunityType(queryDto);

        checkParams(queryDto);
        log.info("queryBsiOpportunityListQuery:{}", queryDto);
        BsiOpportunityPoExample example = createExampleFromQueryDto(queryDto);
        log.info("queryBsiOpportunityListExample:{}", example);

        List<BsiOpportunityDto> dtoList = new ArrayList<>();
        Long total = bsiOpportunityDao.countByExample(example);
        if (total > 0) {
            Page pg = Page.valueOf(page, size);
            example.setOffset(pg.getOffset());
            example.setLimit(pg.getLimit());
            example.setOrderByClause("mtime desc");
            this.setOrderByClause(queryDto, example);

            List<BsiOpportunityPo> dbResultList = bsiOpportunityDao.selectByExample(example);
            dtoList = poToDto(dbResultList);
        }
        return new PageResult<>(total.intValue(), dtoList);
    }

    @Override
    public Map<Integer, BsiOpportunityDto> queryByBsiOpportunityIds(List<Integer> bsiOppIds) {
        if (CollectionUtils.isEmpty(bsiOppIds)) {
            return Maps.newHashMap();
        }
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        BsiOpportunityPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andIdIn(bsiOppIds);
        List<BsiOpportunityPo> dbResultList = bsiOpportunityDao.selectByExample(example);
        List<BsiOpportunityDto> dtoList = poToDto(dbResultList);
        return dtoList.stream().collect(Collectors.toMap(BsiOpportunityDto::getId, Function.identity()));
    }

    @Override
    public PageResult<BsiOpportunityDto> queryBrandBsiOpportunityListWithMixTerm(BsiOpportunityQueryDto queryDto, Integer page, Integer size) {
        if (isInteger(queryDto.getMixTerm())) {
            queryDto.setId(Integer.valueOf(queryDto.getMixTerm()));
            return queryBrandBsiOpportunityList(queryDto, page, size);
        } else {
            BsiOpportunityQueryDto queryDto1 = new BsiOpportunityQueryDto();
            BeanUtils.copyProperties(queryDto, queryDto1);
            queryDto1.setCreateUserLike(queryDto.getMixTerm());
            PageResult<BsiOpportunityDto> dtoPageResult1 = queryBrandBsiOpportunityList(queryDto1, 1, Integer.MAX_VALUE);
            BsiOpportunityQueryDto queryDto2 = new BsiOpportunityQueryDto();
            BeanUtils.copyProperties(queryDto, queryDto2);
            queryDto2.setCompanyFullName(queryDto.getMixTerm());
            PageResult<BsiOpportunityDto> dtoPageResult2 = queryBrandBsiOpportunityList(queryDto2, 1, Integer.MAX_VALUE);
            List<BsiOpportunityDto> total = Optional.ofNullable(dtoPageResult1.getRecords()).orElse(new ArrayList<>());
            total.addAll(dtoPageResult2.getRecords());
            return CrmPageUtils.extractorWithPage(total, page, size);
        }
    }

    /**
     * 移动端品牌商机Example构建
     *
     * @param queryDto
     * @return
     */
    private BsiOpportunityPoExample buildWxBrandExample(BsiOpportunityQueryDto queryDto) {

        BsiOpportunityPoExample example = new BsiOpportunityPoExample();

        example.or();

        Criteria criteria = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Criteria criteria1 = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Criteria criteria2 = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Criteria criteria3 = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        Criteria criteria4 = example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (!CollectionUtils.isEmpty(queryDto.getCreateUsers())) {
            criteria.andCreateUserIn(queryDto.getCreateUsers());
            criteria1.andCreateUserIn(queryDto.getCreateUsers());
            criteria2.andCreateUserIn(queryDto.getCreateUsers());
            criteria3.andCreateUserIn(queryDto.getCreateUsers());
            criteria4.andCreateUserIn(queryDto.getCreateUsers());
        }

        //跟进阶段
        if (CollectionUtils.isNotEmpty(queryDto.getFollowStages())) {
            criteria.andFollowStageIn(queryDto.getFollowStages());
            criteria1.andFollowStageIn(queryDto.getFollowStages());
            criteria2.andFollowStageIn(queryDto.getFollowStages());
            criteria3.andFollowStageIn(queryDto.getFollowStages());
            criteria4.andFollowStageIn(queryDto.getFollowStages());
        }

        if (CollectionUtils.isNotEmpty(queryDto.getNewFollowStages())) {
            criteria.andNewFollowStageIn(queryDto.getNewFollowStages());
            criteria1.andNewFollowStageIn(queryDto.getNewFollowStages());
            criteria2.andNewFollowStageIn(queryDto.getNewFollowStages());
            criteria3.andNewFollowStageIn(queryDto.getNewFollowStages());
            criteria4.andNewFollowStageIn(queryDto.getNewFollowStages());
        }


        //录入时间
        if (queryDto.getCreateMinDate() != null) {
            criteria.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
            criteria1.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
            criteria2.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
            criteria3.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
            criteria4.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
        }

        if (queryDto.getCreateMaxDate() != null) {
            criteria.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
            criteria1.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
            criteria2.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
            criteria3.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
            criteria4.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
        }

        if (null != queryDto.getBsiOpportunityType()) {
            criteria.andBsiOpportunityTypeEqualTo(queryDto.getBsiOpportunityType());
            criteria1.andBsiOpportunityTypeEqualTo(queryDto.getBsiOpportunityType());
            criteria2.andBsiOpportunityTypeEqualTo(queryDto.getBsiOpportunityType());
            criteria3.andBsiOpportunityTypeEqualTo(queryDto.getBsiOpportunityType());
            criteria4.andBsiOpportunityTypeEqualTo(queryDto.getBsiOpportunityType());
        }

        //混合字段为空，返回example
        if (StringUtils.isEmpty(queryDto.getMixTerm())) {
            example.or(criteria);
            return example;
        }

        if (StringUtils.isNotBlank(queryDto.getCustomerNameLike())) {
            criteria2.andCustomerNameLike("%" + queryDto.getCustomerNameLike() + "%");
        }

        if (StringUtils.isNotBlank(queryDto.getCreateUserLike())) {
            criteria3.andCreateUserLike("%" + queryDto.getCreateUserLike() + "%");
        }

        if (StringUtils.isNotBlank(queryDto.getCompanyFullNameLike())) {
            criteria4.andCompanyFullNameLike("%" + queryDto.getCompanyFullNameLike() + "%");
        }

        if (queryDto.getIdLike() == null) {
            example.or(criteria2);
            example.or(criteria3);
            return example;
        }

        criteria1.andIdEqualTo(queryDto.getIdLike());

        example.or(criteria1);
        example.or(criteria2);
        example.or(criteria3);
        return example;
    }

    public static boolean isInteger(String str) {
        Pattern pattern = Pattern.compile("^[-\\+]?[\\d]*$");
        return pattern.matcher(str).matches();
    }

    @Override
    public List<BsiOpportunityDto> queryBsiOpportunityList(BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample example = createExampleFromQueryDto(queryDto);
        List<BsiOpportunityPo> pos = bsiOpportunityDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::po2Dto).collect(Collectors.toList());
    }

    public List<BsiOpportunityDto> aggConvertBsiPoListById(List<AdsBrandBsiSnapshotPO> poList) {
        if (CollectionUtils.isEmpty(poList)) {
            return new ArrayList<>();
        }
        List<BsiOpportunityDto> result = new ArrayList<>();
        Map<Integer, List<AdsBrandBsiSnapshotPO>> poMap = poList.stream().collect(Collectors.groupingBy(AdsBrandBsiSnapshotPO::getBsi_opportunity_id));
        for (Map.Entry<Integer, List<AdsBrandBsiSnapshotPO>> m : poMap.entrySet()) {
            List<AdsBrandBsiSnapshotPO> bsiSnapshotPOList = m.getValue();
            AdsBrandBsiSnapshotPO po = bsiSnapshotPOList.get(0);
            BsiOpportunityDto dto = new BsiOpportunityDto();
            dto.setId(m.getKey());
            dto.setName(po.getBsi_name());
            dto.setOpportunitySource(po.getOpportunity_source());
            dto.setAccountId(po.getAccount_id());
            dto.setCustomerId(po.getCustomer_id());
            dto.setCustomerName(po.getCustomer_name());
            dto.setReportId(Long.valueOf(po.getReport_id()));
            dto.setReportCustomerName(po.getReport_customer_name());
            dto.setAgentAccountId(po.getAgent_account_id());
            dto.setAgentCustomerId(po.getAgent_customer_id());
            dto.setAgentCustomerName(po.getAgent_customer_name());
            dto.setGroupId(po.getGroup_id());
            dto.setGroupName(po.getGroup_name());
            dto.setProductId(po.getProduct_id());
            dto.setProductName(po.getProduct_name());
            dto.setCreateUser(po.getCreate_user());
            dto.setCreateUserGroupIds(ListUtils.strToListOrDefault(po.getCreate_user_group_id_list(), po.getCreate_user_group_id()));
            dto.setExpectLaunchAmount(po.getExpect_launch_amount().intValue());
            dto.setExpectLaunchDateForLog(po.getExpect_launch_start_date() + " ~ " + po.getExpect_launch_end_date());
            dto.setExpectLaunchStartDateForLog(po.getExpect_launch_start_date());
            dto.setExpectLaunchEndDateForLog(po.getExpect_launch_end_date());
            dto.setAbandonType(po.getAbandon_type());
            dto.setAbandonDetail(po.getAbandon_detail());
            dto.setDirectSales(bsiSnapshotPOList.stream().filter(e -> Objects.equals(e.getSale_type(), BiliUserType.STRAIGHT_MANAGER.getCode())).map(AdsBrandBsiSnapshotPO::getSale_id).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            dto.setDirectSaleNames(bsiSnapshotPOList.stream().filter(e -> Objects.equals(e.getSale_type(), BiliUserType.STRAIGHT_MANAGER.getCode())).map(AdsBrandBsiSnapshotPO::getSale_name).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));
            dto.setDirectSaleGroupNames(bsiSnapshotPOList.stream().filter(e -> Objects.equals(e.getSale_type(), BiliUserType.STRAIGHT_MANAGER.getCode()))
                    .map(e-> CrmUtils.takeOneListString(e.getSale_group_name_list(), e.getSale_group_name()))
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(",")));
            dto.setChannelSales(bsiSnapshotPOList.stream().filter(e -> Objects.equals(e.getSale_type(), BiliUserType.CHANNEL_MANAGER.getCode())).map(AdsBrandBsiSnapshotPO::getSale_id).filter(Objects::nonNull).distinct().collect(Collectors.toList()));
            dto.setChannelSaleNames(bsiSnapshotPOList.stream().filter(e -> Objects.equals(e.getSale_type(), BiliUserType.CHANNEL_MANAGER.getCode())).map(AdsBrandBsiSnapshotPO::getSale_name).filter(Objects::nonNull).distinct().collect(Collectors.joining(",")));
            dto.setChannelSaleGroupNames(bsiSnapshotPOList.stream().filter(e -> Objects.equals(e.getSale_type(), BiliUserType.CHANNEL_MANAGER.getCode()))
                    .map(e-> CrmUtils.takeOneListString(e.getSale_group_name_list(), e.getSale_group_name()))
                    .filter(StringUtils::isNotBlank).distinct().collect(Collectors.joining(",")));
            dto.setCtime(CrmUtils.parseTimestamp(po.getCtime()));
            dto.setMtime(CrmUtils.parseTimestamp(po.getMtime()));
            dto.setFollowStage(po.getFollow_stage());
            dto.setFollowStageDesc(FollowStage.getByCode(po.getFollow_stage()).getName());
            dto.setExpectOrderDate(CrmUtils.parseTimestamp(po.getExpect_order_date()));
            dto.setExpectOrderDateStr(po.getExpect_order_date());
            dto.setExpireDate(Utils.getSomeDayAfter(CrmUtils.parseTimestamp(po.getCtime()), BSI_OPPORTUNITY_INVALID));
            dto.setContractNumberList(po.getContract_number_list());
            dto.setContractIdList(po.getContract_id_list());
            dto.setContractRelatedTime(po.getFirst_contract_related_time());
            dto.setCurrentQuarterExpectLaunchAmountBig(CrmUtils.doubleToBigDecimal(po.getCurrent_quarter_expect_launch_amount()));
            dto.setOtherQuarterExpectLaunchAmountBig(CrmUtils.doubleToBigDecimal(po.getOther_quarter_expect_launch_amount()));
            dto.setExpectLaunchAmountFactorBig(CrmUtils.doubleToBigDecimal(po.getExpect_launch_amount_factor()));
            dto.setReportCustomerName(po.getReport_customer_name());
            dto.setPlannerReceiptOrderTime(CrmUtils.parseTimestamp(po.getPlanner_receipt_order_time()));
            dto.setPlannerStateStr(FollowPlannerStatus.queryByCode(po.getPlanner_status()).getDesc());
            dto.setPlannerLeaderName(po.getPlanner_leader_name());
            dto.setPlannerSpecialistName(po.getPlanner_specialist_name());
            dto.setAllConvertDays0(po.getAll_convert_days_0());
            dto.setAllConvertDays30(po.getAll_convert_days_30());
            dto.setAllConvertDays50(po.getAll_convert_days_50());
            dto.setAllConvertDays70(po.getAll_convert_days_70());
            dto.setAllConvertDays90(po.getAll_convert_days_90());
            dto.setAllConvertDays100(po.getAll_convert_days_100());
            dto.setCooperation_project_ids_str(po.getCooperation_project_id_list());
            dto.setCooperation_project_names_str(po.getCooperation_project_name_list());
            dto.setCategoryFirstName(po.getCommerce_category_first_name());
            dto.setCategorySecondName(po.getCommerce_category_second_name());
            dto.setBusinessIndustryFirstName(po.getBusiness_industry_first_name());
            dto.setBusinessIndustrySecondName(po.getBusiness_industry_second_name());
            dto.setSellTypeIdStr(po.getSell_type());
            dto.setSellTypeNameStr(StringUtils.isEmpty(po.getSell_type()) ? "" : Arrays.stream(po.getSell_type().split(",")).filter(StringUtils::isNotBlank).map(t -> BsiOpportunityProductType.getByCode(Integer.valueOf(t)).getDesc()).collect(Collectors.joining(",")));
            dto.setNewCustomer(po.getCustomer_source());
            dto.setUnitedFirstIndustryName(po.getAcc_united_first_industry_name());
            dto.setUnitedSecondIndustryName(po.getAcc_united_second_industry_name());
            dto.setUnitedThirdIndustryName(po.getAcc_united_third_industry_name());
            dto.setNewFollowStage(po.getNew_follow_stage());
            dto.setNewExpectLaunchAmountFactorBig(CrmUtils.doubleToBigDecimal(po.getNew_expect_launch_amount_factor()));
            dto.setAllConvertDays80(po.getAll_convert_days_80());
            dto.setNewAllConvertDays30(po.getNew_all_convert_days_30());
            dto.setNewAllConvertDays50(po.getNew_all_convert_days_50());
            dto.setNewFollowStageDesc(BsiFollowStageEnum.getEnumByCode(po.getNew_follow_stage()).getDesc());
            result.add(dto);
        }
        return result;
    }

    @Override
    public List<BsiOpportunityDto> queryBsiOpportunityListForCK(BsiOpportunityCKQueryDTO queryDto) {
        if (StringUtils.isBlank(queryDto.getLogDate())) {
            throw new IllegalArgumentException("queryBsiOpportunityListForCK_缺少必要的_log_date");
        }
        List<AdsBrandBsiSnapshotPO> adsBrandBsiSnapshotPOList = adsCrmBrandBsiSnapshotDao.queryBrandBsiSnapshotPOList(queryDto);
        return aggConvertBsiPoListById(adsBrandBsiSnapshotPOList);
    }

    @Override
    public List<BsiOpportunityDto> queryBrandBsiSnapshotPOListForThisQuarter(BsiOpportunityCKQueryDTO queryDto) {
        if (StringUtils.isBlank(queryDto.getLogDate())) {
            throw new IllegalArgumentException("queryBsiOpportunityListForCK_缺少必要的_log_date");
        }
        List<AdsBrandBsiSnapshotPO> adsBrandBsiSnapshotPOList = adsCrmBrandBsiSnapshotDao.queryBrandBsiTeamSnapshotPOList(queryDto);
        return aggConvertBsiPoListById(adsBrandBsiSnapshotPOList);
    }

    @Override
    public List<BsiOpportunityTeamItemDTO> queryBrandBsiTeamSnapshotPOListForCK(BsiOpportunityCKQueryDTO queryDto) {
        log.info("queryBrandBsiTeamSnapshotPOListForCK:{}", queryDto);
        List<AdsBrandBsiSnapshotPO> poList = getFilterBrandBsiTeamSnapshotPOList(queryDto);
        List<BsiOpportunityTeamItemDTO> itemDTOList = new ArrayList<>();
        //没销售的是0
        Map<Integer, List<AdsBrandBsiSnapshotPO>> poBySaleIdMap = poList.stream().collect(Collectors.groupingBy(AdsBrandBsiSnapshotPO::getSale_id));
        for (Map.Entry<Integer, List<AdsBrandBsiSnapshotPO>> m : poBySaleIdMap.entrySet()) {
            List<AdsBrandBsiSnapshotPO> bsiList = m.getValue();
            Map<Integer, AdsBrandBsiSnapshotPO> poByBsiIdMap = bsiList.stream().collect(Collectors.toMap(AdsBrandBsiSnapshotPO::getBsi_opportunity_id, t -> t, (o, n) -> n));
            poByBsiIdMap.values().forEach(e -> {
                BsiOpportunityTeamItemDTO dto = new BsiOpportunityTeamItemDTO();
                dto.setSaleId(e.getSale_id());
                dto.setSaleName(e.getSale_name());
                dto.setSaleGroupIds(ListUtils.strToListOrDefault(e.getSale_group_id_list(), e.getSale_group_id()));
                dto.setSaleGroupName(CrmUtils.takeOneListString(e.getSale_group_name_list(), e.getSale_group_name()));
                dto.setSaleType(e.getSale_type());
                dto.setBsiId(e.getBsi_opportunity_id());
                dto.setContractIdList(e.getContract_id_list());
                dto.setFollowStage(e.getFollow_stage());
                dto.setNewFollowStage(e.getNew_follow_stage());
                if (Objects.equals(queryDto.getOldNowFeature(), 1)) {
                    dto.setExpectLaunchAmount(e.getExpect_launch_amount());
                } else {
                    dto.setExpectLaunchAmount(e.getCurrent_quarter_expect_launch_amount());
                }
                dto.setExpectLaunchFactorAmount(e.getExpect_launch_amount_factor());
                dto.setNewExpectLaunchFactorAmount(e.getNew_expect_launch_amount_factor());
                itemDTOList.add(dto);
            });
        }
        return itemDTOList;
    }

    public List<AdsBrandBsiSnapshotPO> getFilterBrandBsiTeamSnapshotPOList(BsiOpportunityCKQueryDTO queryDto) {
        if (StringUtils.isBlank(queryDto.getLogDate())) {
            throw new IllegalArgumentException("queryBsiOpportunityListForCK_缺少必要的_log_date");
        }
        if (Objects.isNull(queryDto.getQuarterBeginDate()) || Objects.isNull(queryDto.getQuarterEndDate())) {
            return new ArrayList<>();
        }
        if (Objects.equals(queryDto.getOldNowFeature(), 1)) {
            return adsCrmBrandBsiSnapshotDao.queryBrandBsiTeamSnapshotFeaturePOList(queryDto);
        }
        return adsCrmBrandBsiSnapshotDao.queryBrandBsiTeamSnapshotPOList(queryDto);
    }

    /**
     * 商机互查
     *
     * @param queryDto
     * @return
     */
    @Override
    public List<BsiOpportunityDto> bsiOpportunityMutualCheck(BsiOpportunityQueryDto queryDto) {

        BsiOpportunityPoExample example = createExampleForQueryBsiMachine(queryDto);
        List<BsiOpportunityPo> pos = bsiOpportunityDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyList();
        }
        return pos.stream().map(this::po2Dto).collect(Collectors.toList());
    }


    private BsiOpportunityDto po2Dto(BsiOpportunityPo po) {
        BsiOpportunityDto dto = new BsiOpportunityDto();
        BeanUtils.copyProperties(po, dto);
        dto.setFirstProductModelLabel(po.getFirstProductModel());
        dto.setSecondProductModelLabel(po.getSecondProductModel());
        dto.setFirstProductModelId(po.getFirstProductModelId());
        dto.setSecondProductModelId(po.getSecondProductModelId());
        dto.setProductLabelType(po.getProductLabelType());
        return dto;
    }

    /**
     * 商机预估成单金额总计统计
     *
     * @param queryDto
     * @return
     */
    @Override
    public BsiOpportunityStatisticsDto queryExpectAmountStatistics(BsiOpportunityQueryDto queryDto) {
        setOpportunityType(queryDto);

        checkParams(queryDto);

        BsiOpportunityPoExample example = createExampleFromQueryDto(queryDto);
        List<BsiOpportunityPo> dbResultList = bsiOpportunityDao.selectByExample(example);

        // 累计expect_launch_amount字段
        BsiOpportunityStatisticsDto statisticsDto = new BsiOpportunityStatisticsDto();
        if (CollectionUtils.isNotEmpty(dbResultList)) {
            Long sum = dbResultList.parallelStream().mapToLong(BsiOpportunityPo::getExpectLaunchAmount).sum();
            statisticsDto.setTotal_expect_launch_amount(sum);
        }
        return statisticsDto;
    }

    @Override
    public BsiOpportunityStatisticsDto queryBrandExpectAmountStatistics(BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample example = createBranchExampleFromQueryDto(queryDto);
        List<BsiOpportunityPo> dbResultList = bsiOpportunityDao.selectByExample(example);

        // 累计expect_launch_amount字段
        BsiOpportunityStatisticsDto statisticsDto = new BsiOpportunityStatisticsDto();
        if (CollectionUtils.isNotEmpty(dbResultList)) {
            Long sum = dbResultList.parallelStream().mapToLong(BsiOpportunityPo::getExpectLaunchAmount).sum();
            statisticsDto.setTotal_expect_launch_amount(sum);
        }
        return statisticsDto;
    }

    private void checkParams(BsiOpportunityQueryDto queryDto) {
        Assert.notNull(queryDto, "查询商机列表入参为空");
        if (CollectionUtils.isNotEmpty(queryDto.getFollowStages())) {
            queryDto.getFollowStages().forEach(FollowStage::getByCode);
        }
        if (CollectionUtils.isNotEmpty(queryDto.getNewFollowStages())) {
            queryDto.getNewFollowStages().forEach(BsiFollowStageEnum::getEnumByCode);
        }
        Integer type = queryDto.getBsiOpportunityType();
        Assert.isTrue(type != null && (type == 1 || type == 2), "查询商机列表商机类型不合法");

        if (StringUtils.isNotBlank(queryDto.getOrder())) {
            Assert.isTrue(queryDto.getOrder().equals("desc") || queryDto.getOrder().equals("asc"), "排序字段错误");
        }
    }

    private void setOpportunityType(BsiOpportunityQueryDto queryDto) {
        if (queryDto.getDataSources() != null && queryDto.getDataSources() == 2) {
            // 商机来源：1-crm系统 2-代理商系统， 代理商系统非管理员只能查看自己的数据
            if (queryDto.getIsAgentAdmin() == null || !queryDto.getIsAgentAdmin()) {
                Assert.isTrue(CollectionUtils.isNotEmpty(queryDto.getCreateUsers()), "代理商查看商机列表操作人不能为空");
            }
            queryDto.setBsiOpportunityType(1); // 商机类型：1-效果商机 2-品牌商机   代理商系统只能看效果商机
        }
    }

    public BsiOpportunityPoExample createBranchExampleFromQueryDto(BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        Criteria criteria = buildCommonCriteria(example, queryDto);
        //销售权限只能看到自己提交的
        if (queryDto.getSaleOperator() != null) {
            criteria.andCreateUserEqualTo(queryDto.getSaleOperator());
        }
        //销售权限能看到销售所属是自己或者下属的
        if (queryDto.getSaleOperator() != null && (CollectionUtils.isNotEmpty(queryDto.getDirectSales()) || CollectionUtils.isNotEmpty(queryDto.getChannelSales()))) {
            BsiOpportunityQueryDto queryDto2 = new BsiOpportunityQueryDto();
            BeanUtils.copyProperties(queryDto, queryDto2);
            buildSalesCondition(queryDto2);
            buildCommonCriteria(example, queryDto2);
        }
        //类似策划权限媒介权限
        if (CollectionUtils.isNotEmpty(queryDto.getIdsOr())) {
            BsiOpportunityQueryDto queryDto3 = new BsiOpportunityQueryDto();
            BeanUtils.copyProperties(queryDto, queryDto3);
            queryDto3.setIds(queryDto.getIdsOr());
            buildCommonCriteria(example, queryDto3);
        }
        return example;
    }

    public BsiOpportunityPoExample createBsiExampleForFunnelQueryDto(BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        Criteria criteria = buildCommonForFunnelCriteria(example, queryDto);
        //销售权限只能看到自己提交的
        if (queryDto.getSaleOperator() != null) {
            criteria.andCreateUserEqualTo(queryDto.getSaleOperator());
        }
        //销售权限能看到销售所属是自己或者下属的
        if (queryDto.getSaleOperator() != null && (CollectionUtils.isNotEmpty(queryDto.getDirectSales()) || CollectionUtils.isNotEmpty(queryDto.getChannelSales()))) {
            BsiOpportunityQueryDto queryDto2 = new BsiOpportunityQueryDto();
            BeanUtils.copyProperties(queryDto, queryDto2);
            //把销售条件转成商机idList
            buildSalesCondition(queryDto2);
            buildCommonForFunnelCriteria(example, queryDto2);
        }
        return example;
    }

    public BsiOpportunityPoExample createExampleFromQueryDto(BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        buildNewCriteria(example, queryDto);
        if (BsiOpportunityType.EFFECT.getCode().equals(queryDto.getBsiOpportunityType()) && CollectionUtils.isNotEmpty(queryDto.getAgentIds())) {
            Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            if (queryDto.getAgentId() != null) {
                criteria.andAgentIdEqualTo(queryDto.getAgentId());
            }
            if (StringUtils.isNotBlank(queryDto.getCustomerName())) {
                criteria.andCustomerNameLike("%" + queryDto.getCustomerName() + "%");
            }
            if (StringUtils.isNotBlank(queryDto.getCompanyFullName())) {
                criteria.andCompanyFullNameLike("%" + queryDto.getCompanyFullName() + "%");
            }
            if (StringUtils.isNotBlank(queryDto.getNameLike())) {
                criteria.andNameLike("%" + queryDto.getCompanyFullName() + "%");
            }
            if (queryDto.getFollowStages() != null) {
                criteria.andFollowStageIn(queryDto.getFollowStages());
            }
            if (queryDto.getNewFollowStages() != null) {
                criteria.andNewFollowStageIn(queryDto.getNewFollowStages());
            }
            if (queryDto.getCreateMinDate() != null) {
                criteria.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
            }
            if (queryDto.getCreateMaxDate() != null) {
                criteria.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
            }
            if (queryDto.getUpdateMinDate() != null) {
                criteria.andMtimeGreaterThan(queryDto.getUpdateMinDate());
            }
            if (queryDto.getUpdateMaxDate() != null) {
                criteria.andMtimeLessThanOrEqualTo(queryDto.getUpdateMaxDate());
            }
            if (CollectionUtils.isNotEmpty(queryDto.getAgentIds())) {
                criteria.andAgentIdIn(queryDto.getAgentIds());
            }
            criteria.andBsiOpportunityTypeEqualTo(queryDto.getBsiOpportunityType());

            ObjectUtils.setList(queryDto::getFollowStages, criteria::andFollowStageIn);
            ObjectUtils.setList(queryDto::getNewFollowStages, criteria::andNewFollowStageIn);

            //预计投放日期与时间范围有交集
            ObjectUtils.setObject(queryDto::getExpectLaunchStartDate, criteria::andExpectLaunchEndDateGreaterThanOrEqualTo);
            ObjectUtils.setObject(queryDto::getExpectLaunchEndDate, criteria::andExpectLaunchStartDateLessThanOrEqualTo);
        }
        if (!CollectionUtils.isEmpty(queryDto.getCoopSale())) {
            queryDto.setIsNeedCoopSaleQueryUnion(Boolean.TRUE);
            buildNewCriteria(example, queryDto);
        }
        if (!CollectionUtils.isEmpty(queryDto.getIdsOr())) {
            queryDto.setIsNeedCoopSaleQueryUnion(Boolean.FALSE);
            queryDto.setIsNeedPlannerQueryUnion(Boolean.TRUE);
            buildNewCriteria(example, queryDto);
        }
        return example;
    }

    private List<Integer> queryBsiOpportunityIdByCreator(List<Integer> salesIds) {
        if (CollectionUtils.isEmpty(salesIds)) {
            return Collections.emptyList();
        }
        Map<Integer, String> saleEmailMap = saleService.getSalesEmailInIds(salesIds);
        if (saleEmailMap.isEmpty()) {
            AlarmHelper.log("SalesEmpty", salesIds);
            return Collections.emptyList();
        }
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        example.createCriteria().andCreateUserIn(new ArrayList<>(saleEmailMap.values()))
                        .andIsDeletedEqualTo(0);
        List<Integer> opportunityIds = bsiOpportunityDao.selectByExample(example)
                .stream()
                .map(BsiOpportunityPo::getId)
                .distinct()
                .collect(Collectors.toList());
        AlarmHelper.log("SalesIdsOppIds", opportunityIds);
        return opportunityIds;
    }

    /**
     * 构建所属销售条件
     */
    private void buildSalesCondition(BsiOpportunityQueryDto queryDto) {
        if (queryDto.getDirectSales() == null && queryDto.getChannelSales() == null) {
            return;
        }
        List<Integer> saleTypeList = Lists.newArrayList(BiliUserType.STRAIGHT_MANAGER.getCode(), BiliUserType.CHANNEL_MANAGER.getCode());
        if (queryDto.getDirectSales() == null) {
            queryDto.setDirectSales(Collections.emptyList());
            saleTypeList.remove(BiliUserType.STRAIGHT_MANAGER.getCode());
        }
        if (queryDto.getChannelSales() == null) {
            queryDto.setChannelSales(Collections.emptyList());
            saleTypeList.remove(BiliUserType.CHANNEL_MANAGER.getCode());
        }
        List<Integer> mergedSaleList = Stream.concat(queryDto.getDirectSales().stream(), queryDto.getChannelSales().stream())
                .distinct()
                .collect(Collectors.toList());
        //如果所属直客销售或者渠道销售不为空
        if (CollectionUtils.isNotEmpty(mergedSaleList)) {
            BsiOpportunitySaleMappingPoExample exampleSales = new BsiOpportunitySaleMappingPoExample();
            exampleSales.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andSaleIdIn(mergedSaleList).andSaleTypeIn(saleTypeList);
            List<Integer> saleSubordinateOpIds = queryDto.isQuerySubordinate()
                    ? queryBsiOpportunityIdByCreator(mergedSaleList) : Collections.emptyList();

            Stream<Integer> saleMappingOpportunityIds = bsiOpportunitySaleMappingDao
                    .selectByExample(exampleSales)
                    .stream()
                    .map(BsiOpportunitySaleMappingPo::getBsiOpportunityId);
            List<Integer> bsiOpportunityIds =
                    Stream.concat(saleMappingOpportunityIds, saleSubordinateOpIds.stream())
                    .distinct().collect(Collectors.toList());

            if (CollectionUtils.isNotEmpty(bsiOpportunityIds)) {
                if (CollectionUtils.isNotEmpty(queryDto.getIds())) {
                    queryDto.getIds().retainAll(bsiOpportunityIds);//保留列表中与指定集合相同的元素
                } else {
                    queryDto.setIds(bsiOpportunityIds);
                }
            } else {
                //这里填-1是防止那种没有商机属于自己的，就会把全部数据查出来
                queryDto.setId(-1);
            }
        }
    }


    private List<Integer> buildHaveSaleIdListCondition(List<Integer> saleIdList) {
        if (CollectionUtils.isEmpty(saleIdList)) {
            return Collections.emptyList();
        }
        BsiOpportunitySaleMappingPoExample exampleSales = new BsiOpportunitySaleMappingPoExample();
        exampleSales.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSaleIdIn(saleIdList);
        List<BsiOpportunitySaleMappingPo> bsiOpportunitySaleMappingPos = bsiOpportunitySaleMappingDao.selectByExample(exampleSales);
        if (CollectionUtils.isNotEmpty(bsiOpportunitySaleMappingPos)) {
            return bsiOpportunitySaleMappingPos.stream().map(BsiOpportunitySaleMappingPo::getBsiOpportunityId).distinct().collect(Collectors.toList());
        }
        //这里填-1是防止那种没有商机属于自己的，但是他是销售角色，就会把全部数据查出来
        return Collections.emptyList();
    }

    private Criteria buildCommonCriteria(BsiOpportunityPoExample example, BsiOpportunityQueryDto queryDto) {
        Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        ObjectUtils.setObject(queryDto::getId, criteria::andIdEqualTo);
        ObjectUtils.setObject(queryDto::getAgentId, criteria::andAgentIdEqualTo);
        ObjectUtils.setObject(queryDto::getOperatorModifySalesFlag, criteria::andOperatorModifySalesFlagEqualTo);
        ObjectUtils.setObject(queryDto::getCtimeEnd, criteria::andCtimeLessThan);
        ObjectUtils.setObject(queryDto::getCtimeBegin, criteria::andCtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getCreateMinDate, criteria::andCtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getCreateMaxDate, criteria::andCtimeLessThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getUpdateMinDate, criteria::andMtimeGreaterThan);
        ObjectUtils.setObject(queryDto::getUpdateMaxDate, criteria::andMtimeLessThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getExpectLaunchStartDate, criteria::andExpectLaunchEndDateGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getExpectLaunchEndDate, criteria::andExpectLaunchStartDateLessThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getBsiOpportunityType, criteria::andBsiOpportunityTypeEqualTo);
        ObjectUtils.setList(queryDto::getAgentIds, criteria::andAgentIdIn);
        ObjectUtils.setList(queryDto::getCreateUsers, criteria::andCreateUserIn);
        ObjectUtils.setList(queryDto::getIds, criteria::andIdIn);
        ObjectUtils.setList(queryDto::getFollowStages, criteria::andFollowStageIn);
        ObjectUtils.setList(queryDto::getNewFollowStages, criteria::andNewFollowStageIn);
        if (StringUtils.isNotBlank(queryDto.getCustomerName())) {
            criteria.andCustomerNameLike("%" + queryDto.getCustomerName() + "%");
        }
        if (StringUtils.isNotBlank(queryDto.getCustomerNameLike())) {
            criteria.andCustomerNameLike("%" + queryDto.getCustomerNameLike() + "%");
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyFullName())) {
            criteria.andCompanyFullNameLike("%" + queryDto.getCompanyFullName() + "%");
        }
        if (StringUtils.isNotBlank(queryDto.getNameLike())) {
            criteria.andNameLike("%" + queryDto.getNameLike() + "%");
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyFullNameLike())) {
            criteria.andCompanyFullNameLike("%" + queryDto.getCompanyFullNameLike() + "%");
        }
        if (StringUtils.isNotBlank(queryDto.getCreateUserLike())) {
            criteria.andCreateUserLike("%" + queryDto.getCreateUserLike() + "%");
        }
        return criteria;
    }

    private Criteria buildCommonForFunnelCriteria(BsiOpportunityPoExample example, BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (CollectionUtils.isNotEmpty(queryDto.getIds())) {
            criteria.andIdIn(queryDto.getIds());
        }
        if (StringUtils.isNotBlank(queryDto.getBsiNameLike())) {
            criteria.andNameLike("%" + queryDto.getBsiNameLike() + "%");
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAgentIds())) {
            criteria.andAgentIdIn(queryDto.getAgentIds());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAccountCustomerIdList())) {
            criteria.andCustomerIdIn(queryDto.getAccountCustomerIdList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getAccountIdList())) {
            criteria.andAccountIdIn(queryDto.getAccountIdList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getGroupIdList())) {
            criteria.andGroupIdIn(queryDto.getGroupIdList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getProductIdList())) {
            criteria.andProductIdIn(queryDto.getProductIdList());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getFollowStages())) {
            criteria.andFollowStageIn(queryDto.getFollowStages());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getNewFollowStages())) {
            criteria.andNewFollowStageIn(queryDto.getNewFollowStages());
        }
        if (queryDto.getId() != null) {
            criteria.andIdEqualTo(queryDto.getId());
        }
        if (queryDto.getCreateMinDate() != null) {
            criteria.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
        }
        if (queryDto.getCreateMaxDate() != null) {
            criteria.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getCommerceCategoryFirstIds())) {
            criteria.andCommerceCategoryFirstIdIn(queryDto.getCommerceCategoryFirstIds());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getCommerceCategorySecondIds())) {
            criteria.andCommerceCategorySecondIdIn(queryDto.getCommerceCategorySecondIds());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getBizCategoryFirstIds())) {
            criteria.andBusinessIndustryFirstIdIn(queryDto.getBizCategoryFirstIds());
        }
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(queryDto.getBizCategorySecondIds())) {
            criteria.andBusinessIndustrySecondIdIn(queryDto.getBizCategorySecondIds());
        }
        if (queryDto.getExpireMinDate() != null) {
            criteria.andCtimeGreaterThanOrEqualTo(Utils.getSomeDayAgo(queryDto.getExpireMinDate(), BSI_OPPORTUNITY_INVALID));
            criteria.andFollowStageIn(FollowStage.getTenAndNinetyStatus());
        }
        if (queryDto.getExpireMaxDate() != null) {
            criteria.andCtimeLessThanOrEqualTo(Utils.getSomeDayAgo(queryDto.getExpireMaxDate(), BSI_OPPORTUNITY_INVALID));
        }
        if (queryDto.getExpectOrderMinDate() != null) {
            criteria.andExpectOrderDateGreaterThanOrEqualTo(queryDto.getExpectOrderMinDate());
        }
        if (queryDto.getExpectOrderMaxDate() != null) {
            criteria.andExpectOrderDateLessThanOrEqualTo(queryDto.getExpectOrderMaxDate());
        }
        if (queryDto.getExpectLaunchMinDate() != null) {
            criteria.andExpectLaunchEndDateGreaterThanOrEqualTo(queryDto.getExpectLaunchMinDate());
        }
        if (queryDto.getExpectLaunchMaxDate() != null) {
            criteria.andExpectLaunchStartDateLessThanOrEqualTo(queryDto.getExpectLaunchMaxDate());
        }
        if (queryDto.isAbandonTypeExistCon()) {
            criteria.andAbandonTypeGreaterThan(BsiOppAbandonTypeEnum.DEFAULT.getCode());
        }
        return criteria;
    }


    private Criteria buildNewCriteria(BsiOpportunityPoExample example, BsiOpportunityQueryDto queryDto) {
        Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());

        if (queryDto.getAgentId() != null) {
            criteria.andAgentIdEqualTo(queryDto.getAgentId());
        }
        if (StringUtils.isNotBlank(queryDto.getCustomerName())) {
            criteria.andCustomerNameLike("%" + queryDto.getCustomerName() + "%");
        }
        if (StringUtils.isNotBlank(queryDto.getCompanyFullName())) {
            criteria.andCompanyFullNameLike("%" + queryDto.getCompanyFullName() + "%");
        }
        if (CollectionUtils.isNotEmpty(queryDto.getFollowStages())) {
            criteria.andFollowStageIn(queryDto.getFollowStages());
        }
        if (CollectionUtils.isNotEmpty(queryDto.getNewFollowStages())) {
            criteria.andNewFollowStageIn(queryDto.getNewFollowStages());
        }
        if (queryDto.getCreateMinDate() != null) {
            criteria.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
        }
        if (queryDto.getCreateMaxDate() != null) {
            criteria.andCtimeLessThanOrEqualTo(queryDto.getCreateMaxDate());
        }

        if (queryDto.getUpdateMinDate() != null) {
            criteria.andMtimeGreaterThan(queryDto.getUpdateMinDate());
        }
        if (queryDto.getUpdateMaxDate() != null) {
            criteria.andMtimeLessThanOrEqualTo(queryDto.getUpdateMaxDate());
        }
        if (queryDto.getId() != null) {
            criteria.andIdEqualTo(queryDto.getId());
        }
        if (queryDto.getExpectLaunchStartDate() != null) {
            criteria.andExpectLaunchEndDateGreaterThanOrEqualTo(queryDto.getExpectLaunchStartDate());
        }
        if (queryDto.getExpectLaunchEndDate() != null) {
            criteria.andExpectLaunchStartDateLessThanOrEqualTo(queryDto.getExpectLaunchEndDate());
        }

        if (CollectionUtils.isNotEmpty(queryDto.getCreateUsers()) &&
                (queryDto.getIsNeedCoopSaleQueryUnion() == null || !queryDto.getIsNeedCoopSaleQueryUnion())
                && (queryDto.getIsNeedPlannerQueryUnion() == null || !queryDto.getIsNeedPlannerQueryUnion())) {
            criteria.andCreateUserIn(queryDto.getCreateUsers());
        }
        //商机类型
        ObjectUtils.setObject(queryDto::getBsiOpportunityType, criteria::andBsiOpportunityTypeEqualTo);

        //预计投放日期与时间范围有交集
        ObjectUtils.setObject(queryDto::getExpectLaunchStartDate, criteria::andExpectLaunchEndDateGreaterThanOrEqualTo);
        ObjectUtils.setObject(queryDto::getExpectLaunchEndDate, criteria::andExpectLaunchStartDateLessThanOrEqualTo);

        ObjectUtils.setList(queryDto::getIds, criteria::andIdIn);
        if (CollectionUtils.isNotEmpty(queryDto.getCoopSale()) && queryDto.getIsNeedCoopSaleQueryUnion() != null && queryDto.getIsNeedCoopSaleQueryUnion()) {
            criteria.andSalesmanIdIn(queryDto.getCoopSale());
        }

        ObjectUtils.setObject(queryDto::getCtimeEnd, criteria::andCtimeLessThan);
        ObjectUtils.setObject(queryDto::getCtimeBegin, criteria::andCtimeGreaterThanOrEqualTo);

        if (StringUtils.isNotBlank(queryDto.getCustomerNameLike())) {
            criteria.andCustomerNameLike("%" + queryDto.getCustomerNameLike() + "%");
        }
        if (queryDto.getOperatorModifySalesFlag() != null) {
            criteria.andOperatorModifySalesFlagEqualTo(queryDto.getOperatorModifySalesFlag());
        }
        if (!CollectionUtils.isEmpty(queryDto.getIdsOr()) && queryDto.getIsNeedPlannerQueryUnion() != null && queryDto.getIsNeedPlannerQueryUnion()) {
            criteria.andIdIn(queryDto.getIdsOr());
        }
        return criteria;
    }

    private BsiOpportunityPoExample createExampleForQueryBsiMachine(BsiOpportunityQueryDto queryDto) {
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andCompanyFullNameLike("%" + queryDto.getCompanyFullName() + "%");
        criteria.andNewFollowStageIn(queryDto.getNewFollowStages());
        criteria.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
        ObjectUtils.setObject(queryDto::getBsiOpportunityType, criteria::andBsiOpportunityTypeEqualTo);
        Criteria criteria1 = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria1.andNameLike("%" + queryDto.getCompanyFullName() + "%");
        criteria1.andNewFollowStageIn(queryDto.getNewFollowStages());
        criteria1.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
        ObjectUtils.setObject(queryDto::getBsiOpportunityType, criteria1::andBsiOpportunityTypeEqualTo);
        Criteria criteria2 = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria2.andCustomerNameLike("%" + queryDto.getCustomerName() + "%");
        criteria.andNewFollowStageIn(queryDto.getNewFollowStages());
        criteria2.andCtimeGreaterThanOrEqualTo(queryDto.getCreateMinDate());
        ObjectUtils.setObject(queryDto::getBsiOpportunityType, criteria2::andBsiOpportunityTypeEqualTo);
        return example;
    }

    @Override
    public boolean checkExistById(Integer bsiOpportunityId, Integer bsiOpportunityType) {
        Assert.isTrue(bsiOpportunityId != null, "查询商机详情商机id不能为空");
        BsiOpportunityPo bsiOpportunityPo = bsiOpportunityDao.selectByPrimaryKey(bsiOpportunityId);
        return null != bsiOpportunityPo;
    }

    @Override
    public BsiOpportunityDto getBsiOpportunityById(Integer bsiOpportunityId) {
        BsiOpportunityDto dto = getBsiOpportunityBaseInfoById(bsiOpportunityId);
        if (dto != null) {
            if (StringUtils.isNotBlank(dto.getIntentEffect())) {
                String[] intentEffects = dto.getIntentEffect().split(":");
                dto.setIntentEffectCode(IntentEffect.getdescMapCode().get(intentEffects[0]));
                dto.setIntentEffect(intentEffects.length == 2 ? intentEffects[1] : intentEffects[0]);
            }

            // 附件类型：1、营业执照照片；2、其他资质
            Map<Integer, List<BsiOpportunityAttachmentPo>> typeMapAttachments = queryAttachmentsByBsiOpporId(bsiOpportunityId);
            List<BsiOpportunityAttachmentPo> busiLicensePics = typeMapAttachments.get(1);
            if (CollectionUtils.isNotEmpty(busiLicensePics) && busiLicensePics.get(0) != null) {
                dto.setBusiLicensePic(busiLicensePics.get(0).getOssKey());
            }

            List<BsiOpportunityAttachmentPo> otherQualifys = typeMapAttachments.get(2);
            if (CollectionUtils.isNotEmpty(otherQualifys)) {
                List<BsiOpportunityAttachmentDto> attachmentDtos = new ArrayList<>(otherQualifys.size());
                otherQualifys.forEach(po -> {
                    attachmentDtos.add(convert2AttachmentDtoFromPo(po));
                });

                dto.setOtherQualifys(attachmentDtos);
            }

            List<BsiOpportunityBizMappingPo> bsiOpportunityBizMappingPos = queryOppBizMapping(Lists.newArrayList(bsiOpportunityId), BsiOpportunityBizMappingTypeEnum.COOPERATION_PROJECT);
            if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(bsiOpportunityBizMappingPos)) {
                dto.setCooperationProjects(bsiOpportunityBizMappingPos.stream().map(BsiOpportunityBizMappingPo::getBizId).collect(Collectors.toList()));
            }
            //策划专案附件
            List<CrmCommonnAttachmentPo> crmCommonnAttachmentPos = crmCommonAttachmentRepo.queryList(bsiOpportunityId, AttachmentUploadBizModuleEnum.BSI_PLAN_SCHEME_ATTACHMENT);
            dto.setPlanSchemeAttachments(crmCommonnAttachmentPos.stream().map(r -> ContractFileOssDto.builder()
                    .ossUrl(r.getUrl())
                    .ossKey(r.getOssKey())
                    .fileName(r.getFileName())
                    .build()).collect(Collectors.toList()));

            //OGV专案附件
            List<CrmCommonnAttachmentPo> ogvFiles = crmCommonAttachmentRepo.queryList(bsiOpportunityId, AttachmentUploadBizModuleEnum.BSI_OGV_SCHEME_ATTACHMENT);
            dto.setOgvSchemeAttachments(ogvFiles.stream().map(r -> ContractFileOssDto.builder()
                    .ossUrl(r.getUrl())
                    .ossKey(r.getOssKey())
                    .fileName(r.getFileName())
                    .build()).collect(Collectors.toList()));

            //PUGV专案附件
            List<CrmCommonnAttachmentPo> pugvFiles = crmCommonAttachmentRepo.queryList(bsiOpportunityId, AttachmentUploadBizModuleEnum.BSI_PUGV_SCHEME_ATTACHMENT);
            dto.setPugvSchemeAttachments(pugvFiles.stream().map(r -> ContractFileOssDto.builder()
                    .ossUrl(r.getUrl())
                    .ossKey(r.getOssKey())
                    .fileName(r.getFileName())
                    .build()).collect(Collectors.toList()));
            return dto;
        }
        return null;
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer saveBsiOppFileByType(Integer bsiOppId, List<ContractFileOssDto> dtos, AttachmentUploadBizModuleEnum attachmentUploadBizModuleEnum) {
        // 更新附件
        if (CollectionUtils.isNotEmpty(dtos)) {
            // 先删除
            crmCommonAttachmentRepo.delete(bsiOppId, attachmentUploadBizModuleEnum);
            // 再新增
            return crmCommonAttachmentRepo.batchAdd(CrmCommonAttachmentConvertor.convertDtos2Pos(bsiOppId, dtos, attachmentUploadBizModuleEnum));
        }
        return 0;
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer abandonBsiOppEdit(Integer bsiOppId, Integer abandonType, String abandonDetail) {
        BsiOpportunityPo bsiOpportunityPo = bsiOpportunityDao.selectByPrimaryKey(bsiOppId);
        Assert.isTrue(BsiOpportunityStatus.ABANDON_ORDER.getCode().equals(bsiOpportunityPo.getBsiStatus()), "必须是弃单的商机，才能编辑弃单原因");
        BsiOpportunityPo record = BsiOpportunityPo.builder()
                .id(bsiOppId)
                .abandonType(abandonType)
                .abandonDetail(abandonDetail).build();
        return bsiOpportunityDao.updateByPrimaryKeySelective(record);
    }

    @Override
    public BsiOpportunityDto getBsiOpportunityBaseInfoById(Integer bsiOpportunityId) {
        Assert.isTrue(bsiOpportunityId != null, "查询商机详情商机id不能为空");
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(bsiOpportunityId);
        Assert.notNull(po, "商机不存在");
        BsiOpportunityDto dto = new BsiOpportunityDto();
        BeanUtils.copyProperties(po, dto);
        Map<Integer, List<Integer>> channelSaleMap = queryBsiOpportunitySaleMapByType(Collections.singletonList(bsiOpportunityId), BiliUserType.CHANNEL_MANAGER.getCode());
        Map<Integer, List<Integer>> dircetSaleMap = queryBsiOpportunitySaleMapByType(Collections.singletonList(bsiOpportunityId), BiliUserType.STRAIGHT_MANAGER.getCode());
        dto.setDirectSales(dircetSaleMap.getOrDefault(bsiOpportunityId, Collections.emptyList()));
        dto.setChannelSales(channelSaleMap.getOrDefault(bsiOpportunityId, Collections.emptyList()));
        dto.setFirstProductModelLabel(po.getFirstProductModel());
        dto.setSecondProductModelLabel(po.getSecondProductModel());
        dto.setFirstProductModelId(po.getFirstProductModelId());
        dto.setSecondProductModelId(po.getSecondProductModelId());
        // TODO ID【4300357】全量发布后删除，须确认上游其他系统是否依赖（不限于前端）
        dto.setProductLabelType(po.getProductLabelType());
        SaleDto saleDto = saleService.getSaleByEmail(dto.getCreateUser());
        if (Objects.nonNull(saleDto)) {
            dto.setCreateSaleId(saleDto.getId());
        }
        return dto;
    }

    private BsiOpportunityDto fillBsiContactor(BsiOpportunityDto dto) {
        return fillBsiContactor(dto, dto.getCreateUser());
    }

    /**
     * 填充商机中联系人信息
     * 查询商机详情 ｜ 通过当前操作人销售类型查询是否有相关联系人信息
     *
     * @param dto
     * @param operatorName
     * @return
     */
    @Override
    public BsiOpportunityDto fillBsiContactor(BsiOpportunityDto dto, String operatorName) {
        ContactorFullInfoDto contactor = null;
        ContactorSourceEnum source = null;
        try {
            source = fetchContactorSource(dto, operatorName);
            contactor = contactorMappingService.getBizContactorBySource(ContactorMappingBizTypeEnum.BUSINESS_OPPORTUNITIES,
                    Long.valueOf(dto.getId()), source);
        } catch (Exception e) {
            log.error(">>> 查询商机「{}」联系人「{}」映射关系失败: {}", dto.getName(), dto.getContactName(), e.getMessage());
        }
        if (contactor != null) {
            dto.setContactorId(contactor.getId());
            dto.setContactName(contactor.getContactor());
            dto.setPosition(contactor.getContactorPosition());
            dto.setDepartment(contactor.getContactorDepartment());
            dto.setRole(contactor.getContactorRole());
            dto.setMobileCode(contactor.getContactorPhoneCode());
            dto.setMobile(contactor.getContactorPhone());
            dto.setAddressCity(contactor.getContactorAddressArea());
            dto.setAddressDetail(contactor.getContactorAddressDetail());
            dto.setPhone(contactor.getContactorLandline());
            dto.setEmail(contactor.getContactorEmail());
            dto.setWechat(contactor.getContactorWechat());
        }
        if (source != null) {
            dto.setContactorSource(source.getCode());
        }
        return dto;
    }

    @Override
    public void updateOpsModel(BsiOpportunityDto dto) {
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(dto.getId());
        Assert.notNull(po, "商机不存在");
        po.setFirstProductModelId(dto.getFirstProductModelId());
        po.setFirstProductModel(dto.getFirstProductModelLabel());
        po.setSecondProductModel(dto.getSecondProductModelLabel());
        po.setSecondProductModelId(dto.getSecondProductModelId());
        bsiOpportunityDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public void updateContractCompleteTime(Integer bsiId) {
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(bsiId);
        Assert.notNull(po, "商机不存在");
        Integer days = incomeTimeUtil.getIntervalDays(po.getCtime(), new Timestamp(System.currentTimeMillis()));
        po.setCreateCompleteDays(days);
        bsiOpportunityDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public void updateFollowStageTime(Integer bsiId) {
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(bsiId);
        Assert.notNull(po, "商机不存在");
        Map<Integer, List<BsiOpportunityFollowRecordPo>> integerListMap = getBsiFollow(bsiId);
        for (Integer key : integerListMap.keySet()) {
            List<BsiOpportunityFollowRecordPo> followRecordPos = integerListMap.get(key);
            if (CollectionUtils.isEmpty(followRecordPos)) {
                continue;
            }
            followRecordPos = followRecordPos.stream().sorted(Comparator.comparing(BsiOpportunityFollowRecordPo::getCtime)).collect(Collectors.toList());
            if (Objects.equals(key, FollowStage.ABANDON_ORDER.getCode())) {
                po.setAllConvertDays0(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
                po.setAbandonTime(followRecordPos.get(0).getCtime());
            }
            if (Objects.equals(key, FollowStage.BRIEF.getCode())) {
                po.setAllConvertDays10(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
            }
            if (Objects.equals(key, FollowStage.PRELIMINARY_PLAN.getCode())) {
                po.setAllConvertDays30(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
            }
            if (Objects.equals(key, FollowStage.PLAN_COMMUNICATE.getCode())) {
                po.setAllConvertDays50(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
            }
            if (Objects.equals(key, FollowStage.PROVIDE_PACKAGE.getCode())) {
                po.setAllConvertDays70(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
            }
            if (Objects.equals(key, FollowStage.PROVIDE_SCHEDULE.getCode())) {
                po.setAllConvertDays90(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
            }
            if (Objects.equals(key, FollowStage.COMPLETE_ORDER.getCode())) {
                po.setAllConvertDays100(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
                po.setCreateCompleteDays(incomeTimeUtil.getIntervalDays(po.getCtime(), followRecordPos.get(0).getCtime()));
            }
        }
        bsiOpportunityDao.updateByPrimaryKeySelective(po);
    }


    public Timestamp querySnapshotTime(Integer bsiId) {
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(bsiId);
        Assert.notNull(po, "商机不存在");
        Map<Integer, List<BsiOpportunityFollowRecordPo>> integerListMap = getBsiFollow(bsiId);
        for (Integer key : integerListMap.keySet()) {
            List<BsiOpportunityFollowRecordPo> followRecordPos = integerListMap.get(key);
            if (CollectionUtils.isEmpty(followRecordPos)) {
                continue;
            }
            followRecordPos = followRecordPos.stream().sorted(Comparator.comparing(BsiOpportunityFollowRecordPo::getCtime)).collect(Collectors.toList());
            if (Objects.equals(key, FollowStage.ABANDON_ORDER.getCode()) || Objects.equals(key, FollowStage.COMPLETE_ORDER.getCode())) {
                return followRecordPos.get(0).getCtime();
            }
        }
        return null;
    }

    @Override
    public void updateAmount(Integer bsiId) {
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(bsiId);
        Assert.notNull(po, "商机不存在");
        BsiOpportunityDto dto = po2Dto(po);
        Map<Integer, BsiOpportunityDto> bsiOpportunityDtoMap = new HashMap<>();
        bsiOpportunityDtoMap.put(bsiId, dto);
        List<BsiOpportunityIntentProductMappingDto> intentProductDtoList = bsiOpportunityIntentProductService.getBsiIntentProductMapping(BsiIntentProductQueryDto.builder()
                .bsiOpportunityIds(Lists.newArrayList(bsiId))
                .build());
        List<BsiOpportunityIntentProductDto> intentProductMappingDtoList = intentProductDtoList.stream()
                .map(item -> calculateQuarterAmount(dto, BsiOpportunityIntentProductDto.builder()
                        .bsiOpportunityId(item.getBsiOpportunityId())
                        .type(item.getIntentProductType())
                        .totalAmount(item.getTotalAmount())
                        .quarterAmount(item.getQuarterAmount())
                        .otherQuarterAmount(item.getOtherQuarterAmount())
                        .entryTime(item.getCtime())
                        .productIds(StringUtils.isNotEmpty(item.getIntentProductId()) ? Arrays.stream(item.getIntentProductId().split(",")).map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList())
                        .isQuarterUnpack(item.getUnpackQuarter())
                        .build())).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(intentProductMappingDtoList)) {
            return;
        }
        po.setCurrentQuarterName(intentProductMappingDtoList.get(0).getQuarterStr());
        po.setOtherQuarterName(intentProductMappingDtoList.get(0).getOtherQuarterStr());
        po.setCurrentQuarterExpectLaunchAmount(intentProductMappingDtoList.stream().filter(a -> Objects.nonNull(a.getQuarterAmount())).mapToLong(BsiOpportunityIntentProductDto::getQuarterAmount).sum());
        po.setOtherQuarterExpectLaunchAmount(intentProductMappingDtoList.stream().filter(a -> Objects.nonNull(a.getOtherQuarterAmount())).mapToLong(BsiOpportunityIntentProductDto::getOtherQuarterAmount).sum());
        po.setExpectLaunchAmount(Utils.fromFenToYuan(intentProductMappingDtoList.stream().filter(e -> Utils.isPositive(e.getTotalAmount())).mapToLong(BsiOpportunityIntentProductDto::getTotalAmount).sum()).intValue());
        Long expectLaunchAmountFactor = 0L;
        // 预计成单日期在下季度 本季度预计成单金额为0
        if (po.getCurrentQuarterExpectLaunchAmount() == 0L && po.getExpectOrderDate().compareTo(Utils.getEndOfDay(incomeTimeUtil.getQuarterEndDate(Utils.getNow()))) > 0) {
            expectLaunchAmountFactor = bsiOpportunityIntentProductService.getBsiQuarterAmountSellTypeAll(intentProductMappingDtoList, Lists.newArrayList(po.getId()), bsiOpportunityDtoMap, IsValid.TRUE.getCode());
            log.info("expectLaunchAmountFactor:{}", bsiId);
        } else {
            expectLaunchAmountFactor = bsiOpportunityIntentProductService.getBsiQuarterAmountSellType(intentProductMappingDtoList, Lists.newArrayList(po.getId()), bsiOpportunityDtoMap, IsValid.TRUE.getCode());
        }
        po.setExpectLaunchAmountFactor(expectLaunchAmountFactor);
        po.setExpectLaunchAmountCoefficient(getCoefficient(po, intentProductDtoList));
        bsiOpportunityDao.updateByPrimaryKeySelective(po);
    }

    @Override
    public void updateAllAmount() {
        BsiOpportunityPoExample bsiExample = new BsiOpportunityPoExample();
        bsiExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andFollowStageIn(FollowStage.getTenAndNinetyStatus());
        List<BsiOpportunityPo> bsiOpportunityPoList = bsiOpportunityDao.selectByExample(bsiExample);
        for (BsiOpportunityPo po : bsiOpportunityPoList) {
            bsiOppComponent.updateAmount(po);
        }
    }

    @Override
    public Map<Long, CrmMainPartConfigResultDTO> getAllCrmMainPartConfigMap() {
        Object cash = cacheManager.getValue(CRM_ALL_MAIN_PART_MAP_KEY);
        Map<Long, CrmMainPartConfigResultDTO> result;
        if (Objects.nonNull(cash)) {
            return JSON.parseObject(cash.toString(), new TypeReference<Map<Long, CrmMainPartConfigResultDTO>>() {
            });
        }
        CrmMainPartConfigPoExample example = new CrmMainPartConfigPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmMainPartConfigPo> poList = crmMainPartConfigDao.selectByExample(example);
        result = poList.stream().collect(Collectors.toMap(CrmMainPartConfigPo::getId, t -> CrmMainPartConfigResultDTO.builder().id(t.getId()).mainPartName(t.getMainPartName()).build()));
        cacheManager.setValueWithTime(CRM_ALL_MAIN_PART_MAP_KEY, JSON.toJSONString(result), TimeUnit.MINUTES, 15);
        return result;
    }

    @Override
    public BsiOpportunityDto getBaseBsiOpportunityById(Integer bsiOpportunityId, BsiBaseQueryDto dto) {
        Assert.isTrue(bsiOpportunityId != null, "查询商机详情商机id不能为空");
        BsiOpportunityPo po = bsiOpportunityDao.selectByPrimaryKey(bsiOpportunityId);
        Assert.notNull(po, "商机不存在");
        BsiOpportunityDto result = new BsiOpportunityDto();
        BeanUtils.copyProperties(po, result);
        if (dto.isQueryProject()) {
            List<BsiOpportunityBizMappingPo> bsiOpportunityBizMappingPos = queryOppBizMapping(Lists.newArrayList(bsiOpportunityId), BsiOpportunityBizMappingTypeEnum.COOPERATION_PROJECT);
            if (CollectionUtils.isNotEmpty(bsiOpportunityBizMappingPos)) {
                BsiOpportunityBizMappingPo bizMappingPo = bsiOpportunityBizMappingPos.get(0);
                result.setCooperationProjects(Lists.newArrayList(bizMappingPo.getBizId()));
            }
        }
        if (dto.isQueryIntent()) {
            List<BsiOpportunityIntentProductMappingDto> intentProductDtoList = bsiOpportunityIntentProductService.getBsiIntentProductMapping(BsiIntentProductQueryDto.builder()
                    .bsiOpportunityIds(Lists.newArrayList(bsiOpportunityId))
                    .build());
            if (CollectionUtils.isNotEmpty(intentProductDtoList)) {
                List<BsiOpportunityIntentProductDto> productDtos = intentProductDtoList.stream().map(item -> {
                    BsiOpportunityIntentProductDto productDto = new BsiOpportunityIntentProductDto();
                    productDto.setProductIds(StringUtils.isNotEmpty(item.getIntentProductId()) ? Arrays.stream(item.getIntentProductId().split(",")).map(Integer::valueOf).collect(Collectors.toList()) : Collections.emptyList());
                    return productDto;
                }).collect(Collectors.toList());
                result.setIntentProductInfo(productDtos);
            }
        }
        return result;
    }

    private BsiOpportunityAttachmentDto convert2AttachmentDtoFromPo(BsiOpportunityAttachmentPo po) {
        if (po == null) {
            return null;
        }
        BsiOpportunityAttachmentDto dto = new BsiOpportunityAttachmentDto();
        dto.setFileName(po.getFileName());
        dto.setOssKey(po.getOssKey());
        return dto;
    }

    @Override
    public PageResult<BsiOpportunityFollowRecordDto> queryFollowRecordListByBsiOpporId(Integer bsiOpporId, Integer page,
                                                                                       Integer size) {
        Assert.isTrue(bsiOpporId != null, "查询商机跟进记录商机id不能为空");

        BsiOpportunityFollowRecordPoExample example = new BsiOpportunityFollowRecordPoExample();
        example.or().andBusiOpportunityIdEqualTo(bsiOpporId);

        Long total = bsiOpportunityFollowRecordDao.countByExample(example);
        if (total > 0) {
            Page pg = Page.valueOf(page, size);
            example.setOffset(pg.getOffset());
            example.setLimit(pg.getLimit());
            example.setOrderByClause("ctime desc");

            List<BsiOpportunityFollowRecordPo> dbResults = bsiOpportunityFollowRecordDao.selectByExample(example);
            if (CollectionUtils.isNotEmpty(dbResults)) {
                List<BsiOpportunityFollowRecordDto> recordDtos = new ArrayList<>(dbResults.size());
                dbResults.forEach(po -> {
                    BsiOpportunityFollowRecordDto dto = new BsiOpportunityFollowRecordDto();
                    BeanUtils.copyProperties(po, dto);
                    dto.setBindBizId(po.getRelateBizId());
                    recordDtos.add(dto);
                });

                return new PageResult<>(total.intValue(), recordDtos);
            }
        }
        return new PageResult<>(total.intValue(), Collections.emptyList());
    }

    /**
     * 商机创建之后分别在53天和第58天进行废弃提醒
     */
    @Override
    public void bsiOpportunityNotify() {
        //53天发送通知
        sendBsiOpportunityNotifyMail(BsiOppConfig.BSI_OPPORTUNITY_FIRST_NOTIFY);
        //58天发送通知
        sendBsiOpportunityNotifyMail(BsiOppConfig.BSI_OPPORTUNITY_SECOND_NOTIFY);
    }

    private void sendBsiOpportunityNotifyMail(Integer beforeDay) {
        Timestamp day = IncomeTimeUtil.getPeriodStartTimeBeforeToday(beforeDay, new Timestamp(System.currentTimeMillis()));
        List<BsiOpportunityDto> bsiOpportunityDtos = this.queryBsiOpportunityList(BsiOpportunityQueryDto.builder()
                .followStages(FollowStage.getNotCompleteStatus())
                .ctimeBegin(day)
                .ctimeEnd(Utils.getEndOfDay(day))
                .bsiOpportunityType(BsiOpportunityType.BRAND.getCode())
                .build());
        bsiOpportunityDtos.forEach(item -> {
            //发送给商机提交人
            //Integer salesmanId = item.getSalesmanId();
            //List<String> toEmails = mailUtils.saleIdConvert2Email(salesmanId);
            String createUser = item.getCreateUser() + "@bilibili.com";
            List<String> toEmails = Lists.newArrayList(createUser);

            //商机名称
            String name = item.getName();
            //企业全称
            String companyFullName = item.getCompanyFullName();

            // 标题
            String title = String.format("商机-%s,%s天后将过期", item.getName(), buildNotifyLeftDay(beforeDay));
            // 内容
            String url = String.format("<a href=\"http://cm-mng.bilibili.co/crm/#/opportunity.manage/brandlist/edit/%s\" target=\"_blank\">点击处理</a>", item.getId());
            String content = String.format("您录入[%s]的商机-(%s,%s) 超过%s天未成单," +
                    "超过60天未成单,商机将会被自动废弃，相关商机保护将失效，如已成单请及时登录CRM系统修改商机阶段<br/> [%s]", companyFullName, name, item.getId(), beforeDay, url);
            mailUtils.sendEmail(title, content, toEmails);
        });
    }

    private String buildNotifyLeftDay(Integer beforeDay) {
        if (BsiOppConfig.BSI_OPPORTUNITY_FIRST_NOTIFY.equals(beforeDay)) {
            return String.valueOf(7);
        }
        if (BsiOppConfig.BSI_OPPORTUNITY_SECOND_NOTIFY.equals(beforeDay)) {
            return String.valueOf(2);
        }
        return "";
    }


    @Override
    //@Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void bsiOpportunityInvalid(Long time) {
        Timestamp invalidTime = IncomeTimeUtil.getPeriodStartTimeBeforeToday(BSI_OPPORTUNITY_INVALID, new Timestamp(System.currentTimeMillis()));
        if (time != null) {
            invalidTime = new Timestamp(time);
        }
        //60天弃单
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        example.createCriteria().andFollowStageIn(FollowStage.getNotCompleteStatus())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCtimeGreaterThanOrEqualTo(invalidTime)
                .andCtimeLessThanOrEqualTo(Utils.getEndOfDay(invalidTime));
        List<BsiOpportunityPo> pos = bsiOpportunityDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return;
        }

        pos.forEach(item -> {
            ((BsiOpportunityServiceImpl) AopContext.currentProxy()).bsiOpportunityInvalidBySystem(item);
        });
    }

    //@Override
    //@Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public Integer bsiOpportunityInvalidMock(Long time, Integer flag) {
        Timestamp invalidTime = IncomeTimeUtil.getPeriodStartTimeBeforeToday(BSI_OPPORTUNITY_INVALID, new Timestamp(System.currentTimeMillis()));
        if (time != null) {
            invalidTime = new Timestamp(time);
        }
        //60天弃单
        BsiOpportunityPoExample example = new BsiOpportunityPoExample();
        example.createCriteria().andFollowStageIn(FollowStage.getNotCompleteStatus())
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                //.andCtimeGreaterThanOrEqualTo(invalidTime)
                .andCtimeLessThanOrEqualTo(Utils.getEndOfDay(invalidTime));
        List<BsiOpportunityPo> pos = bsiOpportunityDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return 0;
        }

        if (YesOrNoEnum.YES.getCode().equals(flag)) {
            pos.forEach(item -> {
                ((BsiOpportunityServiceImpl) AopContext.currentProxy()).bsiOpportunityInvalidBySystem(item);
            });
        }
        return pos.size();
    }


    /**
     * 商机状态流转
     *
     * @param operator
     * @param stage
     * @param bsiOpportunityId
     * @param recentFollowRecord
     */
    @Override
    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    public void followStageChange(Operator operator, Integer stage, Integer bsiOpportunityId, String recentFollowRecord, Integer bizType, Long bizId, String abandonReason, Integer abandonType, Integer newFollowStage) {
        BsiOpportunityPo existBsi = bsiOpportunityDao.selectByPrimaryKey(bsiOpportunityId);
        BsiOpportunityDto existBsiDto = po2Dto(existBsi);
        Assert.isTrue(existBsi != null, "商机不存在");
        BsiOpportunityPo bsiOpportunityPo = new BsiOpportunityPo();
        bsiOpportunityPo.setFollowStage(stage);
        bsiOpportunityPo.setId(bsiOpportunityId);
        bsiOpportunityPo.setNewFollowStage(newFollowStage);
        if (stage.equals(FollowStage.ABANDON_ORDER.getCode())) {
            bsiOpportunityPo.setAbandonDetail(abandonReason);
            bsiOpportunityPo.setAbandonType(abandonType);
            bsiOpportunityPo.setBsiStatus(BsiOpportunityStatus.ABANDON_ORDER.getCode());
        }
        if (bsiOpportunityPo.getAccountId() != null && bsiOpportunityPo.getAccountId() > 0 && !bsiOpportunityPo.getFollowStage().equals(FollowStage.COMPLETE_ORDER.getCode())) {
            AccountBaseDto accountBaseDto = accountService.queryAccountByAccountId(bsiOpportunityPo.getAccountId());
            bsiOpportunityPo.setCommerceCategoryFirstId(accountBaseDto.getCommerceCategoryFirstId());
            bsiOpportunityPo.setCommerceCategorySecondId(accountBaseDto.getCommerceCategorySecondId());
            bsiOpportunityPo.setProductId(accountBaseDto.getProductId());
            bsiOpportunityPo.setProductLineId(accountBaseDto.getProductLineId());
            bsiOpportunityPo.setBrandDomain(accountBaseDto.getBrandDomain());
        }
        if (bsiOpportunityPo.getCustomerId() != null && bsiOpportunityPo.getCustomerId() > 0 && !bsiOpportunityPo.getFollowStage().equals(FollowStage.COMPLETE_ORDER.getCode())) {
            CustomerPo customerPo = customerRepo.queryCustomerById(bsiOpportunityPo.getCustomerId(), IsDeleted.VALID.getCode());
            bsiOpportunityPo.setGroupId(customerPo.getGroupId());
            bsiOpportunityPo.setCompanyFullName(customerPo.getUsername());
            bsiOpportunityPo.setBusinessIndustryFirstId(customerPo.getBizIndustryCategoryFirstId());
            bsiOpportunityPo.setBusinessIndustrySecondId(customerPo.getBizIndustryCategorySecondId());
            bsiOpportunityPo.setAreaId(customerPo.getAreaId());
        }
        bsiOpportunityDao.updateByPrimaryKeySelective(bsiOpportunityPo);
        existBsi.setFollowStage(stage);
        existBsi.setNewFollowStage(newFollowStage);
        BsiOpportunityDto newBsiDto = po2Dto(existBsi);
        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.BSI_OPPORTUNITY_EDIT)
                .module(Module.BSI_OPPORTUNITY)
                .obj(newBsiDto)
                .oldObj(existBsiDto)
                .objId(bsiOpportunityPo.getId())
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logDto);

        //TODO 插入流转记录
        bsiOpportunityPo.setRecentFollowRecord(recentFollowRecord);
        BsiOpportunityFollowRecordPo followRecordPo = convert2FollowRecordPo(existBsi, bsiOpportunityPo, operator, bizType, bizId);
        bsiOpportunityFollowRecordDao.insertSelective(followRecordPo);
        bsiOppComponent.updateFollowStageTime(bsiOpportunityPo.getId());
    }


    @Override
    public BsiOpportunityIntentProductDto calculateQuarterAmount(BsiOpportunityDto dto, BsiOpportunityIntentProductDto mappingDto) {
        Timestamp snapshotTime = querySnapshotTime(dto.getId());
        if (snapshotTime == null) {
            snapshotTime = mappingDto.getEntryTime();
        }
        Timestamp validTime;
        // 商机成单或者弃单 取快照时间
        if (Lists.newArrayList(FollowStage.COMPLETE_ORDER.getCode(), FollowStage.ABANDON_ORDER.getCode()).contains(dto.getFollowStage())) {
            validTime = snapshotTime;
            // 未成单 取当前时间
        } else {
            validTime = new Timestamp(System.currentTimeMillis());
        }
        // 拆分季度 以录入时间为准
        if (mappingDto.getIsQuarterUnpack().equals(IsValid.TRUE.getCode())) {
            // 录入时间且生效时间在本季度在本季度
            if (Utils.getBeginOfDay(incomeTimeUtil.getQuarterFirstDate(mappingDto.getEntryTime())).compareTo(Utils.getBeginOfDay(incomeTimeUtil.getQuarterFirstDate(new Timestamp(System.currentTimeMillis())))) == 0
                    && Utils.getBeginOfDay(incomeTimeUtil.getQuarterFirstDate(validTime)).compareTo(Utils.getBeginOfDay(incomeTimeUtil.getQuarterFirstDate(new Timestamp(System.currentTimeMillis())))) == 0) {
                if (mappingDto.getQuarterAmount() != null || mappingDto.getOtherQuarterAmount() != null) {
                    mappingDto.setTotalAmount(Optional.ofNullable(mappingDto.getQuarterAmount()).orElse(0L) + Optional.ofNullable(mappingDto.getOtherQuarterAmount()).orElse(0L));
                }
                List<String> quarterList = queryQuarterStr(validTime, dto);
                mappingDto.setQuarterStr(quarterList.get(0));
                mappingDto.setOtherQuarterStr(quarterList.get(1));
                mappingDto.setQuarterAmount(mappingDto.getQuarterAmount());
                mappingDto.setOtherQuarterAmount(mappingDto.getOtherQuarterAmount());
                // 不在录入季度
            } else {
                // 没有base时间全部算到其他季度
                mappingDto.setQuarterAmount(0L);
                mappingDto.setOtherQuarterAmount(Optional.ofNullable(mappingDto.getQuarterAmount()).orElse(0L) + Optional.ofNullable(mappingDto.getOtherQuarterAmount()).orElse(0L));
                mappingDto.setTotalAmount(Optional.ofNullable(mappingDto.getQuarterAmount()).orElse(0L) + Optional.ofNullable(mappingDto.getOtherQuarterAmount()).orElse(0L));
                List<String> quarterList = queryQuarterStr(validTime, dto);
                mappingDto.setQuarterStr(quarterList.get(0));
                mappingDto.setOtherQuarterStr(quarterList.get(1));
            }
            // 不拆分季度 以生效时间为准
        } else {
            if (mappingDto.getTotalAmount() == null) {
                return mappingDto;
            }
            List<String> quarterList = queryQuarterStr(validTime, dto);
            mappingDto.setQuarterStr(quarterList.get(0));
            mappingDto.setOtherQuarterStr(quarterList.get(1));
            Timestamp quarterEndTime = incomeTimeUtil.getQuarterEndDate(validTime);
            Timestamp quarterStartTime = incomeTimeUtil.getQuarterFirstDate(validTime);
            // 计算生效开始时间
            Timestamp startTime = dto.getExpectLaunchStartDate();
            if (quarterStartTime.after(dto.getExpectLaunchStartDate())) {
                startTime = quarterStartTime;
            }
            // 计算生效结束时间
            Timestamp endTime = quarterEndTime;
            if (endTime.after(dto.getExpectLaunchEndDate())) {
                endTime = dto.getExpectLaunchEndDate();
            }
            Integer validDays = (int) ((Utils.getBeginOfDay(endTime).getTime() - Utils.getBeginOfDay(startTime).getTime()) / (1000 * 60 * 60 * 24)) + 1;
            Integer allDays = (int) ((Utils.getBeginOfDay(dto.getExpectLaunchEndDate()).getTime() - Utils.getBeginOfDay(dto.getExpectLaunchStartDate()).getTime()) / (1000 * 60 * 60 * 24)) + 1;
            if (validDays <= 0) {
                mappingDto.setQuarterAmount(0L);
                mappingDto.setOtherQuarterAmount(mappingDto.getTotalAmount());
            } else {
                if (validDays >= allDays) {
                    mappingDto.setQuarterAmount(mappingDto.getTotalAmount());
                    mappingDto.setOtherQuarterAmount(0L);
                } else {
                    mappingDto.setQuarterAmount((validDays * mappingDto.getTotalAmount()) / allDays);
                    mappingDto.setOtherQuarterAmount(mappingDto.getTotalAmount() - mappingDto.getQuarterAmount());
                }
            }
        }
        return mappingDto;
    }


    @Override
    public void fillNewReportBsiInfo(String customerName) {
        try {
            List<CustomerPo> customerPos = customerRepo.queryListByUserName(Lists.newArrayList(customerName), Lists.newArrayList(CustomerCategoryType.ORG_CUSTOMER.getCode(), CustomerCategoryType.PERSONAL_CUSTOMER.getCode()));
            if (CollectionUtils.isEmpty(customerPos)) {
                return;
            }
            CustomerPo customerPo = customerPos.get(0);
            Integer customerId = customerPo.getId();
            Map<Integer, CustomerExtDto> customerExtDtoMap = iCustomerExtService.queryCustomerExtMap(Lists.newArrayList(customerId));
            BsiOpportunityPoExample example = new BsiOpportunityPoExample();
            BsiOpportunityPoExample.Criteria criteria = example.createCriteria();
            criteria.andCompanyFullNameEqualTo(customerPo.getUsername());
            criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            criteria.andBsiStatusEqualTo(BsiOpportunityStatus.NORMAL.getCode());
            List<BsiOpportunityPo> bsiOpportunityPos = bsiOpportunityDao.selectByExample(example);
            if (CollectionUtils.isEmpty(bsiOpportunityPos)) {
                return;
            }
            CustomerExtDto customerExtDto = customerExtDtoMap.get(customerId);
            bsiOpportunityPos.forEach(po -> {
                po.setCustomerId(customerId);
//                po.setNewCustomer(BsiCustomerSourceEnum.EXIST_CUSTOMER.getCode());
                po.setBusinessIndustryFirstId(customerPo.getBizIndustryCategoryFirstId());
                po.setBusinessIndustrySecondId(customerPo.getBizIndustryCategorySecondId());
                po.setAreaId(customerPo.getAreaId());
                po.setGroupId(customerPo.getGroupId());
                po.setCommerceCategoryFirstId(customerPo.getCommerceCategoryFirstId());
                po.setCommerceCategorySecondId(customerPo.getCommerceCategorySecondId());
                po.setCustomerUnitedFirstIndustryId(customerPo.getUnitedFirstIndustryId());
                po.setCustomerUnitedSecondIndustryId(customerPo.getUnitedSecondIndustryId());
                po.setCustomerUnitedThirdIndustryId(customerPo.getUnitedThirdIndustryId());
                if (customerExtDto != null) {
                    po.setProvince(customerExtDto.getProvince());
                    po.setCity(customerExtDto.getCity());
                }
                bsiOpportunityDao.updateByPrimaryKeySelective(po);
            });
        } catch (Exception e) {
            log.error("fillNewReportBsiInfoError", e);
        }
    }

    private List<BsiOpportunityDto> poToDto(List<BsiOpportunityPo> bsiOpportunityPos) {
        List<BsiOpportunityDto> dtoList = new ArrayList<>();
        Map<Integer, String> codeMapName = FollowStage.getCodeMapName();
        if (CollectionUtils.isNotEmpty(bsiOpportunityPos)) {
            //构建所属销售
            List<Integer> bsiIds = bsiOpportunityPos.stream().map(BsiOpportunityPo::getId).collect(Collectors.toList());
            Map<Integer, List<Integer>> channelSaleMap = queryBsiOpportunitySaleMapByType(bsiIds, BiliUserType.CHANNEL_MANAGER.getCode());
            Map<Integer, List<Integer>> dircetSaleMap = queryBsiOpportunitySaleMapByType(bsiIds, BiliUserType.STRAIGHT_MANAGER.getCode());

            bsiOpportunityPos.forEach(po -> {
                BsiOpportunityDto dto = new BsiOpportunityDto();
                BeanUtils.copyProperties(po, dto);
                dto.setFollowStageDesc(codeMapName.get(po.getFollowStage()));
                dto.setNewFollowStageDesc(BsiFollowStageEnum.getEnumByCode(po.getNewFollowStage()).getDesc());
                dto.setIsUnpackQuarter(po.getIsUnpackQuarter());
                dto.setDirectSales(dircetSaleMap.getOrDefault(po.getId(), Collections.emptyList()));
                dto.setChannelSales(channelSaleMap.getOrDefault(po.getId(), Collections.emptyList()));
                dto.setFirstProductModelLabel(po.getFirstProductModel());
                dto.setSecondProductModelLabel(po.getSecondProductModel());
                dto.setFirstProductModelId(po.getFirstProductModelId());
                dto.setSecondProductModelId(po.getSecondProductModelId());
                // TODO ID【4300357】全量发布后删除，须确认上游其他系统是否依赖（不限于前端）
                dto.setProductLabelType(po.getProductLabelType());
                dto.setReportId(po.getReportId());
                dtoList.add(dto);
            });
        }
        return dtoList;
    }

    @Override
    public Map<Integer, List<Integer>> queryBsiOpportunitySaleMapByType(List<Integer> ids, Integer saleType) {
        Map<Integer, List<Integer>> result = new HashMap<>();
        if (CollectionUtils.isEmpty(ids) || saleType == null) {
            return result;
        }
        BsiOpportunitySaleMappingPoExample saleMappingPoExample = new BsiOpportunitySaleMappingPoExample();
        saleMappingPoExample.or().andBsiOpportunityIdIn(ids).andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSaleTypeEqualTo(saleType);
        List<BsiOpportunitySaleMappingPo> saleMappingPos = bsiOpportunitySaleMappingDao.selectByExample(saleMappingPoExample);
        if (CollectionUtils.isEmpty(saleMappingPos)) {
            return result;
        }
        return saleMappingPos.stream().collect(Collectors.groupingBy(BsiOpportunitySaleMappingPo::getBsiOpportunityId,
                Collectors.mapping(BsiOpportunitySaleMappingPo::getSaleId, Collectors.toList())));
    }

    @Override
    public void updateBsiOpportunitySaleInfoForOperator(BsiOpportunityDto dto, Operator operator) {
        insertOrUpdateBsiOpportunitySaleMapping(dto, operator);
        //修改商机人为修改了的标识
        bsiOpportunityDao.updateByPrimaryKeySelective(BsiOpportunityPo.builder().id(dto.getId()).operatorModifySalesFlag(IsValid.TRUE.getCode()).build());
    }

    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updateBsiCustomerInfo(BsiOpportunityChangeCustomerDTO changeCustomerDTO, Operator operator) {
        Assert.notNull(changeCustomerDTO, "changeCustomerDTO is null");
        Assert.isTrue(changeCustomerDTO.getId() != null, "商机id不能为空");
        BsiOpportunityPo oldPO = bsiOpportunityDao.selectByPrimaryKey(changeCustomerDTO.getId());
        Assert.notNull(oldPO, "商机不存在");
        Assert.isTrue(Utils.isPositive(oldPO.getCustomerId()), "商机未关联广告主客户");
        Assert.isTrue(FollowStage.getTenAndHundredStatus().contains(oldPO.getFollowStage()), "商机阶段不为10%-100%");
        CustomerPo customerPo = customerRepo.queryCustomerById(changeCustomerDTO.getNewCustomerId(), IsDeleted.VALID.getCode());
        Assert.isTrue(Objects.nonNull(customerPo), "关联广告主客户不存在");
        AccountBaseDto accountBaseDto = accountService.queryAccountByAccountId(changeCustomerDTO.getNewAccountId());
        Assert.isTrue(Objects.nonNull(accountBaseDto), "关联广告主账户不存在");

        // 如果根据广告主帐户查询到产品型号列表不为空，则入参必须强制指定产品型号。
        List<ProductModelLabel> modelLabels = bsiOpsPlannerService.queryAllProductModels(changeCustomerDTO.getNewAccountId());
        if (!CollectionUtils.isEmpty(modelLabels)) {
            Assert.isTrue(StringUtils.isNotEmpty(changeCustomerDTO.getFirstProductModelLabel()) && StringUtils.isNotEmpty(changeCustomerDTO.getSecondProductModelLabel()),
                    "商机产品型号缺失，请在【编辑】或【更换绑定客户】入口补充");
        }

        BsiOpportunityPo updatePo = new BsiOpportunityPo();
        updatePo.setId(oldPO.getId());
        updatePo.setCustomerId(changeCustomerDTO.getNewCustomerId());
        updatePo.setCompanyFullName(customerPo.getUsername());
        updatePo.setGroupId(customerPo.getGroupId()); // 集团
        updatePo.setAccountId(changeCustomerDTO.getNewAccountId());
        updatePo.setStatus(1);
        updatePo.setProductId(accountBaseDto.getProductId()); // 品牌
        updatePo.setFirstProductModelId(Optional.ofNullable(changeCustomerDTO.getFirstProductModelLabelId()).orElse(0).longValue());
        updatePo.setFirstProductModel(changeCustomerDTO.getFirstProductModelLabel());
        updatePo.setSecondProductModel(changeCustomerDTO.getSecondProductModelLabel());
        updatePo.setSecondProductModelId(Optional.ofNullable(changeCustomerDTO.getSecondProductModelLabelId()).orElse(0).longValue());
        updatePo.setCustomerUnitedFirstIndustryId(customerPo.getUnitedFirstIndustryId());
        updatePo.setCustomerUnitedSecondIndustryId(customerPo.getUnitedSecondIndustryId());
        updatePo.setCustomerUnitedThirdIndustryId(customerPo.getUnitedThirdIndustryId());
        updatePo.setUnitedFirstIndustryId(accountBaseDto.getUnitedFirstIndustryId());
        updatePo.setUnitedSecondIndustryId(accountBaseDto.getUnitedSecondIndustryId());
        updatePo.setUnitedThirdIndustryId(accountBaseDto.getUnitedThirdIndustryId());
        bsiOpportunityDao.updateByPrimaryKeySelective(updatePo);

        if (!FollowStage.ABANDON_ORDER.getCode().equals(oldPO.getFollowStage()) && IsValid.FALSE.getCode().equals(oldPO.getOperatorModifySalesFlag())) {
            List<SaleDto> saleDtoList = saleSeaService.getDirectSalesByCustomerIdAndAccountId(changeCustomerDTO.getNewCustomerId(), changeCustomerDTO.getNewAccountId(), SaleSeaProductCategory.CONTRACT);
            List<Integer> saleIds = saleDtoList.stream().map(SaleDto::getId).distinct().collect(Collectors.toList());
            insertOrUpdateBsiOpportunitySaleMapping(BsiOpportunityDto.builder()
                    .id(oldPO.getId())
                    .directSales(saleIds)
                    .build(), operator);
        }

        JSONObject oldJson = new JSONObject();
        JSONObject newJson = new JSONObject();
        oldJson.put("customerId", oldPO.getCustomerId());
        oldJson.put("accountId", oldPO.getAccountId());
        newJson.put("customerId", updatePo.getCustomerId());
        newJson.put("accountId", updatePo.getAccountId());
        NewLogOperatorDto logDto = NewLogOperatorDto
                .builder()
                .modifyType(ModifyType.BSI_OPPORTUNITY_CHANGE_CUSTOMER_INFO)
                .module(Module.BSI_OPPORTUNITY)
                .obj(newJson)
                .oldObj(oldJson)
                .objId(oldPO.getId())
                .systemType(SystemType.CRM)
                .build();
        logOperatorService.insertLog(operator, logDto);
    }

    public List<String> queryQuarterStr(Timestamp baseTime, BsiOpportunityDto dto) {
        String quarterStr = incomeTimeUtil.getQuarterDescNow(baseTime);
        long nextQuarter = incomeTimeUtil.getStartOrEndDayOfQuarter(baseTime, 1, Boolean.TRUE);
        long beforeQuarter = incomeTimeUtil.getStartOrEndDayOfQuarter(baseTime, -1, Boolean.TRUE);
        String beforeQuarterStr = incomeTimeUtil.getQuarterDescNow(new Timestamp(beforeQuarter));
        String nextQuarterStr = incomeTimeUtil.getQuarterDescNow(new Timestamp(nextQuarter));
        String endQuarterStr = incomeTimeUtil.getQuarterDescNow(dto.getExpectLaunchEndDate());
        String startQuarterStr = incomeTimeUtil.getQuarterDescNow(dto.getExpectLaunchStartDate());
        // 预计成单时间和基准时间在同一季度 或者在其后
        if (incomeTimeUtil.getQuarterFirstDate(baseTime).compareTo(incomeTimeUtil.getQuarterFirstDate(dto.getExpectLaunchEndDate())) <= 0) {
            // 只有本季度
            if (new Timestamp(nextQuarter).after(dto.getExpectLaunchEndDate())) {
                return Lists.newArrayList(quarterStr, Strings.EMPTY);
                // 结束时间在下季度
            } else if (nextQuarterStr.equals(endQuarterStr)) {
                return Lists.newArrayList(quarterStr, nextQuarterStr);
                // 结束时间在下季度以后
            } else {
                String result = nextQuarterStr + "-" + endQuarterStr;
                return Lists.newArrayList(quarterStr, result);
            }
            // 跨周期成单的情况
        } else {
            // 只有本季度
            if (incomeTimeUtil.getQuarterFirstDate(baseTime).compareTo(dto.getExpectLaunchStartDate()) <= 0) {
                return Lists.newArrayList(quarterStr, Strings.EMPTY);
                // 上季度 + 本季度
            } else if (beforeQuarterStr.equals(startQuarterStr)) {
                return Lists.newArrayList(quarterStr, startQuarterStr);
                // 开始时间在上季度之前
            } else {
                String result = startQuarterStr + "-" + beforeQuarterStr;
                return Lists.newArrayList(quarterStr, result);
            }

        }
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void bsiOpportunityInvalidBySystem(BsiOpportunityPo item) {

        BsiOpportunityPo bsiOpportunityPo = new BsiOpportunityPo();
        bsiOpportunityPo.setBsiStatus(BsiOpportunityStatus.ABANDON_ORDER.getCode());
        bsiOpportunityPo.setFollowStage(FollowStage.ABANDON_ORDER.getCode());
        bsiOpportunityPo.setNewFollowStage(BsiFollowStageEnum.ABANDON_ORDER.getCode());
        bsiOpportunityPo.setId(item.getId());
        bsiOpportunityDao.updateByPrimaryKeySelective(bsiOpportunityPo);
        //商机跟进阶段流转记录
        bsiOpportunityPo.setRecentFollowRecord("商机60天未成单,系统弃单");
        BsiOpportunityPo existBsi = BsiOpportunityPo.builder().id(item.getId()).followStage(item.getFollowStage()).build();
        BsiOpportunityFollowRecordPo followRecordPo = convert2FollowRecordPo(existBsi, bsiOpportunityPo, Operator.SYSTEM, null, null);

        bsiOpportunityFollowRecordDao.insertSelective(followRecordPo);
        log.info("bsiOpportunityInvalidBySystem 60day invalid {}", item.getId());
    }

    private void setOrderByClause(BsiOpportunityQueryDto queryDto, BsiOpportunityPoExample example) {
        if (!Objects.isNull(queryDto.getKey()) && !Objects.isNull(queryDto.getOrder())) {
            String key = "";
            if (BsiOppConfig.ADD_TIME.equals(queryDto.getKey())) {
                key = BsiOppConfig.CTIME + " ";
            } else if (BsiOppConfig.UPDATE_TIME.equals(queryDto.getKey())) {
                key = BsiOppConfig.MTIME + " ";
            }
            example.setOrderByClause(key + " " + queryDto.getOrder());
        } else {
            example.setOrderByClause("mtime desc");
        }
    }

    // 切换新行业后，旧行业还需要继续维护一段时间
    private void saveOldCategory(BsiOpportunityPo po) {
        if (Utils.isPositive(po.getCommerceCategoryFirstId()) && Utils.isPositive(po.getCommerceCategorySecondId())) {
            NewOldCategoryMappingDto oldCategoryByNew = newOldCategoryMappingService.getOldCategoryByNew(po.getCommerceCategorySecondId());
            if (oldCategoryByNew != null) {
                po.setCategoryFirstId(oldCategoryByNew.getOldCategoryFirstId());
                po.setCategorySecondId(oldCategoryByNew.getOldCategorySecondId());
            }
        }

        if (Utils.isPositive(po.getUnitedThirdIndustryId())) {
            NewOldCategoryMappingDto oldCategoryByNew = newOldCategoryMappingService.getOldCategoryByNew(po.getUnitedThirdIndustryId(), IndustryMappingEnum.UNITED_COMMERCE_MAPPING.getDesc());
            if (null != oldCategoryByNew) {
                po.setCommerceCategoryFirstId(oldCategoryByNew.getOldCategoryFirstId());
                po.setCommerceCategorySecondId(oldCategoryByNew.getOldCategorySecondId());
            }
        }
    }

    private void addChannelSale(BsiOpportunityDto bsiOpportunity, Operator operator) {
        if (bsiOpportunity.getOpportunitySource().equals(OpportunitySource.CHANNEL.getCode())) {
            DataStrategy dataStrategy = domainUserService.getDataStrategy(operator.getOperatorName());
            if (dataStrategy.getOrgType().equals(DomainOrgType.SALE) && SaleTypeEnum.channelSaleTypes.contains(dataStrategy.getSaleDto().getType())) {
                List<Integer> channelSales = Optional.ofNullable(bsiOpportunity.getChannelSales()).orElse(new ArrayList<>());
                if (CollectionUtils.isNotEmpty(channelSales)) {
                    return;
                }
                channelSales.add(dataStrategy.getSaleDto().getId());
                bsiOpportunity.setChannelSales(channelSales);
            }
        }
    }

    /**
     * 把有策划文案的商机策划状态修改为 策划案已上传
     */
    public String handleHaveUpLoadBsi(List<Integer> ids) {
        if (CollectionUtils.isEmpty(ids)) {
            List<Integer> bisIds = bsiOpportunityPlannerRepo.queryBsiIdListByCondition(BsiOpportunityPlannerQueryDTO.builder().status(Collections.singletonList(BsiOppPlannerStatus.RECEIPT_ORDER.getCode())).build());
            List<CrmCommonnAttachmentPo> crmCommonnAttachmentPos = crmCommonAttachmentRepo.queryList(bisIds, AttachmentUploadBizModuleEnum.BSI_PLAN_SCHEME_ATTACHMENT.getModuleCode(), AttachmentUploadBizModuleEnum.BSI_PLAN_SCHEME_ATTACHMENT.getSubBizCode());
            ids = crmCommonnAttachmentPos.stream().map(CrmCommonnAttachmentPo::getRefId).distinct().collect(Collectors.toList());
        }
        if (CollectionUtils.isEmpty(ids)) {
            return "ids未空";
        }
        ids.forEach(e -> {
            bsiOpportunityPlannerRepo.uploadPlannerScheme(e);
        });
        return "成功：" + JSON.toJSONString(ids);
    }

    public ContactorSourceEnum fetchContactorSource(BsiOpportunityDto dto, String operatorName) {
        Integer contactorSource = dto.getContactorSource();
        ContactorSourceEnum res;
        if (Utils.isPositive(contactorSource) && (res = ContactorSourceEnum.getByCode(contactorSource)) != null) {
            return res;
        }
        // 获取当前角色的数据权限
        // 获取不到销售角色就判断商机是否配置了代理，如果配置代理则认为是代理商，否则为广告主
        DataStrategy dataStrategy;
        try {
            dataStrategy = domainUserService.getDataStrategy(operatorName);
        } catch (Exception e) {
            return Utils.isPositive(dto.getAgentId()) ? ContactorSourceEnum.AGENT : ContactorSourceEnum.ADVERTISERS;
        }
        // 渠道销售 返回代理商
        // 直客销售 返回广告主
        if (dataStrategy.getOrgType().equals(DomainOrgType.SALE) && SaleTypeEnum.channelSaleTypes.contains(dataStrategy.getSaleDto().getType())) {
            return ContactorSourceEnum.AGENT;
        }
        return ContactorSourceEnum.ADVERTISERS;
    }

    private Double getCoefficient(BsiOpportunityPo po, List<BsiOpportunityIntentProductMappingDto> intentProductDtoList) {
        Double coefficient = (double) 0;
        if (CollectionUtils.isEmpty(intentProductDtoList)) {
            return coefficient;
        }
        if (BsiOpportunityProductType.SELL_OGV.getCode().equals(Integer.parseInt(intentProductDtoList.get(0).getIntentProductId()))) {
            coefficient = 0.95;
        } else if (BsiOpportunityProductType.SELL_PUGV.getCode().equals(Integer.parseInt(intentProductDtoList.get(0).getIntentProductId()))) {
            coefficient = 0.5;
        } else if (BsiOpportunityProductType.SELL_HARD.getCode().equals(Integer.parseInt(intentProductDtoList.get(0).getIntentProductId()))) {
            coefficient = 1.0;
        }
        // 若跟进阶段>=50%-90%，对应商机的本季度预计投放金额*50%
        //若跟进阶段>10%-50%，对应商机的本季度预计投放金额*10%
        //若根据阶段=10%，对应商机的本季度预计投放金额*0%
        if (po.getFollowStage() <= 10) {
            coefficient = 0.0;
        } else if (po.getFollowStage() < 50) {
            coefficient = coefficient * 0.1;
        } else if (po.getFollowStage() >= 50 && po.getFollowStage() <= 90) {
            coefficient = coefficient * 0.5;
        }
        return coefficient;
    }

    public String fixBsiGroupId() {
        StringBuilder groupStr = new StringBuilder();
        BsiOpportunityPoExample example1 = new BsiOpportunityPoExample();
        example1.createCriteria().andGroupIdEqualTo(0);
        List<BsiOpportunityPo> bsiOpportunityPoList1 = bsiOpportunityDao.selectByExample(example1);
        bsiOpportunityPoList1.forEach(e -> {
            if (Utils.isPositive(e.getCustomerId())) {
                CustomerPo customerPo = customerRepo.queryCustomerById(e.getCustomerId(), null);
                if (Objects.isNull(customerPo)) {
                    return;
                }
                BsiOpportunityPo upo = new BsiOpportunityPo();
                upo.setId(e.getId());
                upo.setGroupId(customerPo.getGroupId());
                bsiOpportunityDao.updateByPrimaryKeySelective(upo);
                groupStr.append(e.getId()).append(",");
            }
        });
        log.info("fixBsiGroupId,{}", groupStr);
        return "执行修改Group的商机id" + groupStr;
    }

    public String fixBsiProductId() {
        StringBuilder productStr = new StringBuilder();
        BsiOpportunityPoExample example2 = new BsiOpportunityPoExample();
        example2.createCriteria().andProductIdEqualTo(0);
        List<BsiOpportunityPo> bsiOpportunityPoList2 = bsiOpportunityDao.selectByExample(example2);
        bsiOpportunityPoList2.forEach(e -> {
            if (Utils.isPositive(e.getAccountId())) {
                AccAccountPo accAccountPo = accountDao.selectByPrimaryKey(e.getAccountId());
                if (Objects.isNull(accAccountPo)) {
                    return;
                }
                BsiOpportunityPo upo = new BsiOpportunityPo();
                upo.setId(e.getId());
                upo.setProductId(accAccountPo.getProductId());
                bsiOpportunityDao.updateByPrimaryKeySelective(upo);
                productStr.append(e.getId()).append(",");
            }
        });
        log.info("fixBsiProductId,{}", productStr);
        return "执行修改Product的商机id" + productStr;
    }
}
