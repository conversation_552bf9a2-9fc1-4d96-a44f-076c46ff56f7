package com.bilibili.crm.platform.biz.soa;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.platform.api.mas.dto.CrmOrderNameDto;
import com.bilibili.crm.platform.api.mas.service.IMutualAdSystemService;
import com.bilibili.crm.platform.api.order.dto.OgvOrderDto;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.soa.ISoaMutualAdSystemService;
import com.bilibili.crm.platform.soa.dto.SoaOgvOrderQueryDto;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.sql.Timestamp;
import java.util.List;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-10-17
 **/
@Service
public class SoaMutualAdSystemService implements ISoaMutualAdSystemService {
    @Autowired
    private IMutualAdSystemService mutualAdSystemService;

    @Override
    public void validMasOrderByOrderId(Integer orderId) {
        CatUtils.newTransaction(CatUtils.SOA, getClass().getSimpleName(), (t) ->mutualAdSystemService.validMasOrderByOrderId(orderId));
    }

    @Override
    public void updateOrderStartEndTimeByOrderId(Integer orderId, Timestamp startTime, Timestamp endTime) {
        CatUtils.newTransaction(CatUtils.SOA, getClass().getSimpleName(), (t) -> mutualAdSystemService.updateOrderStartEndTimeByOrderId(orderId, startTime, endTime));
    }

    @Override
    public List<CrmOrderNameDto> queryMasOrderByOrderNameLike(String orderNameLike) {
        return CatUtils.newTransactionAndReturn(CatUtils.SOA, getClass().getSimpleName(), (t) ->mutualAdSystemService.queryMasOrderByOrderNameLike(orderNameLike));
    }

    @Override
    public CrmOrderNameDto queryMasOrderByOrderId(Integer id) {
        return CatUtils.newTransactionAndReturn(CatUtils.SOA, getClass().getSimpleName(), (t) ->mutualAdSystemService.queryMasOrderByOrderId(id));
    }

    @Override
    public List<CrmOrderNameDto> queryOgvOrderByParam(SoaOgvOrderQueryDto dto) {
        return mutualAdSystemService.queryOgvOrderByParam(dto);
    }

    @Override
    public PageResult<OgvOrderDto> queryOgvOrderByPage(Integer accountId, Integer currentPage, Integer pageSize) {
        return mutualAdSystemService.queryOgvOrderByPage(accountId,currentPage,pageSize);
    }
}
