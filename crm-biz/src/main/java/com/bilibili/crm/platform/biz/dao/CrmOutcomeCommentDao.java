package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmOutcomeCommentPo;
import com.bilibili.crm.platform.biz.po.CrmOutcomeCommentPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CrmOutcomeCommentDao {
    long countByExample(CrmOutcomeCommentPoExample example);

    int deleteByExample(CrmOutcomeCommentPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CrmOutcomeCommentPo record);

    int insertSelective(CrmOutcomeCommentPo record);

    List<CrmOutcomeCommentPo> selectByExample(CrmOutcomeCommentPoExample example);

    CrmOutcomeCommentPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmOutcomeCommentPo record, @Param("example") CrmOutcomeCommentPoExample example);

    int updateByExample(@Param("record") CrmOutcomeCommentPo record, @Param("example") CrmOutcomeCommentPoExample example);

    int updateByPrimaryKeySelective(CrmOutcomeCommentPo record);

    int updateByPrimaryKey(CrmOutcomeCommentPo record);
}