package com.bilibili.crm.platform.biz.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmContractBillSplitDetailPo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 拆分后的账单ID，状态=已确认才有值，状态=未确认默认置空
     */
    private Integer billId;

    /**
     * 原账单ID（拆分前）
     */
    private Integer originBillId;

    /**
     * 原实结/调整账单拆分明细序号
     */
    private Integer serialNo;

    /**
     * 原实结/调整账单的计收日期
     */
    private Timestamp billDate;

    /**
     * 订单ID
     */
    private Integer orderId;

    /**
     * 合同ID
     */
    private Integer contractId;

    /**
     * 拆分后账单金额，单位毫分
     */
    private Long billAmount;

    /**
     * 账单来源
     */
    private Integer billSource;

    /**
     * 直客销售ID
     */
    private Integer directSaleId;

    /**
     * 渠道销售ID
     */
    private Integer channelSaleId;

    /**
     * 拆分类型，0-系统拆分；1-手动拆分；
     */
    private Integer bizType;

    /**
     * 状态，0-未确认；1-已确认，默认为未确认
     */
    private Integer status;

    /**
     * 是否删除，0-否；1-是；
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 修改时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}
