package com.bilibili.crm.platform.biz.util;

import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.adp.passport.biz.manager.bean.MainResponseData;
import com.bilibili.crm.platform.biz.util.bean.PlayUrlInfo;
import com.google.common.collect.Lists;
import com.google.gson.reflect.TypeToken;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.net.Inet4Address;
import java.net.InetAddress;
import java.net.NetworkInterface;
import java.net.SocketException;
import java.util.Enumeration;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2020/10/22 11:05
 */
@Service("crmOssSignService")
public class OssSignService {


    private static final Logger LOGGER = LoggerFactory.getLogger(OssSignService.class);

    @Value("${oss.signUrl:http://business-sign-playurl.bilibili.co/playurl}")
    private String ossSignUrl;

    @Value("${oss.bucket:f5d7e2532cc9ad16bc2a41222d76f269}")
    private String defaultBucket;

    @Value("${oss.environment:prod}")
    private String environment;

    private static final String ENVIRONMENT_UAT = "uat";
    private static final String ENVIRONMENT_OFFICE = "office";

    private static final String LOCALHOST = "127.0.0.1";

    private static final String PLATFORM_H5 = "html5";

    private static final String REQUEST_PARAM_UP_OS_URL = "upos_uri";
    private static final String REQUEST_PARAM_BILI_BUCKET = "bili_bucket";
    private static final String REQUEST_PARAM_UIP = "uip";
    private static final String REQUEST_PARAM_PLATFORM = "platform";
    private static final String REQUEST_PARAM_CDN = "cdn";

    private static final String UP_OS_HEADER = "upos://";
    private static final String SLASH = "/";

    public String getPlayUrl(String originOssKey) {
        return getUrls(Lists.newArrayList(originOssKey), ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : null, this.defaultBucket)
                .get(originOssKey);
    }

    public String getPlayUrl(String originOssKey, String bucket) {
        return getUrls(Lists.newArrayList(originOssKey), ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : null, bucket)
                .get(originOssKey);
    }

    public String getOfficePlayUrl(String originOssKey) {
        return getUrls(Lists.newArrayList(originOssKey), ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : ENVIRONMENT_OFFICE, this.defaultBucket)
                .get(originOssKey);
    }

    public String getOfficePlayUrl(String originOssKey, String bucket) {
        return getUrls(Lists.newArrayList(originOssKey), ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : ENVIRONMENT_OFFICE, bucket)
                .get(originOssKey);
    }

    public Map<String, String> getPlayUrls(List<String> originOssKeyList) {
        return getUrls(originOssKeyList, ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : null, this.defaultBucket);
    }

    public Map<String, String> getPlayUrls(List<String> originOssKeyList, String bucket) {
        return getUrls(originOssKeyList, ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : null, bucket);
    }

    public Map<String, String> getOfficePlayUrls(List<String> originOssKeyList) {
        return getUrls(originOssKeyList, ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : ENVIRONMENT_OFFICE, this.defaultBucket);
    }

    public Map<String, String> getOfficePlayUrls(List<String> originOssKeyList, String bucket) {
        return getUrls(originOssKeyList, ENVIRONMENT_UAT.equals(environment) ? ENVIRONMENT_UAT : ENVIRONMENT_OFFICE, bucket);
    }

    /**
     * @param originOssKeyList 兼容upos://bucket/file.mp4  /bucket/file.mp4  bucket/file.mp4
     * @link https://info.bilibili.co/pages/viewpage.action?pageId=129820576
     */
    private Map<String, String> getUrls(List<String> originOssKeyList, String cdn, String bucket) {
        LOGGER.info("getPlayUrl ossKey is {}.", originOssKeyList);

        Assert.notEmpty(originOssKeyList, "ossKey不能为空");

        Map<String, String> originUpOsHeaderMap = originOssKeyList.stream()
                .filter(StringUtils::isNotEmpty)
                .distinct()
                .collect(Collectors.toMap(originOssKey -> originOssKey, this::getUpOsHeaderUrl));
        Assert.notEmpty(originUpOsHeaderMap, "ossKey不能为空");

        String uriParamValue = String.join(",", originUpOsHeaderMap.values());
        try {
            Map<String, Object> paramMap = new HashMap<>();
            paramMap.put(REQUEST_PARAM_UP_OS_URL, uriParamValue);
            paramMap.put(REQUEST_PARAM_BILI_BUCKET, bucket);
            paramMap.put(REQUEST_PARAM_UIP, getServerIP());
            paramMap.put(REQUEST_PARAM_PLATFORM, PLATFORM_H5);

            if (StringUtils.isNotBlank(cdn)) {
                paramMap.put(REQUEST_PARAM_CDN, cdn);
            }

            MainResponseData<Map<String, PlayUrlInfo>> response = OkHttpUtils.get(ossSignUrl)
                    .map(paramMap)
                    .callForObject(new TypeToken<MainResponseData<Map<String, PlayUrlInfo>>>() {
                    });
            if (response == null
                    || CollectionUtils.isEmpty(response.getData())) {
                LOGGER.error("getPlayUrl error ossKey {}", uriParamValue);
                LOGGER.info("response {} , paramMap {} ", response, paramMap);
                throw new RuntimeException("调用OSS服务获取URL失败");
            }

            Map<String, String> playUrlMap = new HashMap<>(originUpOsHeaderMap.size());
            Map<String, PlayUrlInfo> resultData = response.getData();
            for (Map.Entry<String, String> entry : originUpOsHeaderMap.entrySet()) {
                String bucketHeaderUrl = getBucketHeaderUrl(entry.getValue());
                List<String> urlList = resultData.getOrDefault(bucketHeaderUrl, new PlayUrlInfo()).getUrl();
                if (!CollectionUtils.isEmpty(urlList)) {
                    playUrlMap.put(entry.getKey(), urlList.get(0));
                }
            }

            return playUrlMap;
        } catch (Exception e) {
            LOGGER.error("getPlayUrl ossKey is {},error!Exception is {}.", uriParamValue, e);
            throw new RuntimeException("调用OSS服务获取URL失败");
        }
    }

    private String getUpOsHeaderUrl(String originOssKey) {
        if (originOssKey.startsWith(UP_OS_HEADER)) {
            return originOssKey;
        }
        if (originOssKey.startsWith(SLASH)) {
            return UP_OS_HEADER + originOssKey.substring(1);
        }
        return UP_OS_HEADER + originOssKey;
    }

    private String getBucketHeaderUrl(String upOsHeaderUrl) {
        return upOsHeaderUrl.substring(UP_OS_HEADER.length());
    }

    private String getServerIP() {
        Enumeration<NetworkInterface> allNetInterfaces = null;
        try {
            allNetInterfaces = NetworkInterface.getNetworkInterfaces();
        } catch (SocketException e) {
            LOGGER.error("getServerIP error", e);
            throw new RuntimeException(e);
        }
        while (allNetInterfaces.hasMoreElements()) {
            NetworkInterface networkInterface = allNetInterfaces.nextElement();
            Enumeration<InetAddress> address = networkInterface.getInetAddresses();
            while (address.hasMoreElements()) {
                InetAddress inetAddress = address.nextElement();
                if (inetAddress instanceof Inet4Address
                        && !LOCALHOST.equals(inetAddress.getHostAddress())) {
                    return inetAddress.getHostAddress();
                }
            }
        }
        return StringUtils.EMPTY;
    }

    public static void main(String[] args) {



        OssSignService signService = new OssSignService();
        signService.defaultBucket = "f5d7e2532cc9ad16bc2a41222d76f269";
//        signService.bucket = "dcb0ebca1ed0fb539a71a2442298afaa";
        signService.ossSignUrl = "http://uat-business-sign-playurl.bilibili.co/playurl";
        signService.environment = "uat";
//        signService.environment = "prod";

        System.out.println("single url: " + signService.getPlayUrl("upos://business/m200806a22w721um0sczky35xyn6js01.mp4"));
//        System.out.println("single office url: " + signService.getOfficePlayUrl("upos://business/m200806a22w721um0sczky35xyn6js01.mp4"));
//        System.out.println("urls: " + signService.getPlayUrls(Lists.newArrayList("upos://business/m200806a22w721um0sczky35xyn6js01.mp4")));
//        System.out.println("office urls: " + signService.getOfficePlayUrls(Lists.newArrayList("upos://business/m200806a22w721um0sczky35xyn6js01.mp4")));
    }
}
