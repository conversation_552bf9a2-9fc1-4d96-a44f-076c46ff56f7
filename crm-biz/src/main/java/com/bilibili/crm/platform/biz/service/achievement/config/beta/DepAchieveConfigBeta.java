package com.bilibili.crm.platform.biz.service.achievement.config.beta;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.commercialorder.api.order.enums.BoostingStatus;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.achievement.IAchieveConfigBeta;
import com.bilibili.crm.platform.api.achievement.dto.AchieveType;
import com.bilibili.crm.platform.api.income.dto.AdIncomeQueryParam;
import com.bilibili.crm.platform.api.income.dto.IncomeComposition;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.biz.service.achievement.helper.AchieveConsumeParamHelper;
import com.bilibili.crm.platform.common.AdSupportStatus;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.UserType;
import com.bilibili.crm.platform.common.income.ExtendBillType;
import com.bilibili.crm.platform.common.pickup.PickupOrderCooperationType;
import com.google.common.collect.Lists;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @description: 部门业绩 （品牌营销部，效果营销部，平台代理商）
 * @author: brady
 * @time: 2020/11/1 5:30 下午
 */
@Component
public class DepAchieveConfigBeta implements IAchieveConfigBeta {

    private static final Logger logger = LoggerFactory.getLogger(DepAchieveConfigBeta.class);

    @Value("#{'${achieve.dep.composition.contract.ad.dept.id.beta:1,95,101,311}'.split(',')}")
    private List<Integer> depAchieveDepIds;

    public static List<Integer> CONTENT_EXCLUDE_DEPIDS = Arrays.asList(160, 19, 110);

    public static List<Integer> FINICAL_EXCLUDE_DEPIDS = Arrays.asList(160, 19, 110, 278);

    public static List<Integer> EXCLUDE_TEST_DEPIDS = Arrays.asList(160);


    public static List<Integer> CPC_CPM_EXCLUDE_ACCOUNTIDS = Arrays.asList(29);

    /**
     * 合同销售包含媒介资源预占（ID137）
     */
    public static List<Integer> EXCLUDE_SALEIDS = Arrays.asList(137);

    public static List<Integer> EXCLUDE_SALE_GROUP_IDS = Arrays.asList(35, 80);

    public static List<Integer> EXCLUDE_ACC_GROUP_IDS = Arrays.asList(4317, 4072, 240);

    private static List<Integer> rtbAchieveDepIds = Arrays.asList(95);

    // 商单 直播主播商单、直播主播
    public static List<Integer> CONTRACT_ORDER_PICKUP_ALL_FIRST_CATE = Lists.newArrayList(28, 44, 144);
    //商单
    public static Integer CONTRACT_ORDER_PICKUP_FIRST_CATE = 28;
    //直播主播商单
    public static Integer CONTRACT_ORDER_LIVE_ANCHOR_PICKUP_FIRST_CATE = 44;

    //二级产品类目 热搜第八 第五位
    public static List<Integer> HOT_SEARCH_SECOND_CATE = Lists.newArrayList(466, 467);

    // "全屏视频闪屏PD", "全屏图文闪屏PD", "半屏视频闪屏PD", "半屏图文闪屏PD", "信息流大卡PD", "信息流小卡PD"
    public static List<Integer> HARD_PROGRAM_SECOND_PRODUCT = Lists.newArrayList(417, 416, 415, 414, 359, 360);

    // "直播主播商单", "商单", "直播常规硬广"
    public static List<Integer> NON_STANDARD_FOR_EXPORT = Lists.newArrayList(44, 28, 43);

    // 常规硬广 - 非标一级 - 硬广/ 非标一级 - OTT / 非标一级 - 效果
    public static List<Integer> NORMAL_BRAND_FIRST_CATE = Lists.newArrayList(30, 444, 510);

    public static Integer LIVING_NORMAL_HARD_AD = 43;

    public static final Integer DIRECT_GROUP_ID = 46;

    public static List<Integer> OTHER_PICKUP_ORDER_TYPE = Lists.newArrayList(CrmOrderType.PICK_UP.getCode(), CrmOrderType.UP_REQUIREMENT.getCode());

    @Value("#{'${content.exclude.dep.id:160,19}'.split(',')}")
    private List<Integer> contentExcludeDepIds;
    @Autowired
    private AchieveConsumeParamHelper achieveConsumeParamHelper;
    @Autowired
    private DepAchieveConfigBeta depAchieveConfigBeta;
    @Autowired
    private ISaleGroupService iSaleGroupService;


    @Override
    public AchieveType achieveType() {
        return AchieveType.ORDINARY;
    }

    @Override
    public AdIncomeQueryParam condition(IncomeComposition composition) throws IncomeConditionInvalidException {

        switch (composition) {
            case CONTRACT_AD:
                return depAchieveConfigBeta.contractAdCondition();
            case CPC:
                return cpcCondition();
            case CPM:
                return cpmCondition();
            case BUSINESS_FLY:
                return businessFlyCondition();
            case SAN_LIAN_OTHER:
                return sanLianOtherCondition();
            case ADX:
                return adxCondition();
            case DPA:
                return dpaCondition();
            case REPEAT_CPC:
                return repeatCpcCondition();
            case REPEAT_CPM:
                return repeatCpmCondition();
            case REPEAT_BUSINESS_FLY:
                return repeatBsiFlyCondition();
            case PICK_UP_HUA_HUO:
                return huaHuoCondition();
            case CONTENT_FLY:
                return contentFlyCondition();
            case PERSON_FLY:
                return personFlyCon();
            case PICK_UP_UNCONFIRMED:
                return pickUpConfirmCon();
            case PICK_UP_UNCONFIRMED_BIZ:
                return pickUpConfirmCon();
            case PICK_UP_UNAUDIT:
                return pickUpUnConfirmCon();
            case PICK_UP_UNAUDIT_BIZ:
                return pickUpUnConfirmCon();
            case PICK_UP_UNPAID:
                return pickUpUnPaidCon();
            case PICK_UP_UNPAID_BIZ:
                return pickUpUnPaidCon();
            case PICK_UP_PAYED_UNDONE:
                return pickUpUnPaidCon();
            case PICK_UP_UNPAY_UNORDER:
                return pickUpUnPaidCon();
            case PICK_UP_AUDIT_REJECT:
                return pickUpUnPaidCon();
            case PICKUP_ORDER:
                ArrayList<Integer> pickUpAndBoosting = new ArrayList<>(PICK_PRODUCT_TYPES);
                pickUpAndBoosting.addAll(BOOSTING_PICK_PRODUCT_TYPES);
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .agentIsInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .productTypes(pickUpAndBoosting)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case PICK_UP_CONFIRMED:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .agentIsInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .productTypes(PICK_PRODUCT_TYPES).build();
            case BOOSTING_CONFIRMED:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .agentIsInner(IsInnerEnum.OUTER.getCode())
                        .pickupOrderStatus(Lists.newArrayList(BoostingStatus.BOOSTING_COMPLETED.getCode()))
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .productTypes(BOOSTING_PICK_PRODUCT_TYPES).build();
            case BOOSTING_UNAUDIT:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .agentIsInner(IsInnerEnum.OUTER.getCode())
                        .pickupOrderStatus(Lists.newArrayList(BoostingStatus.TO_START.getCode()))
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .productTypes(BOOSTING_PICK_PRODUCT_TYPES).build();
            case BOOSTING_UNDONE:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .agentIsInner(IsInnerEnum.OUTER.getCode())
                        .pickupOrderStatus(Lists.newArrayList(BoostingStatus.BOOSTING.getCode()))
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .productTypes(BOOSTING_PICK_PRODUCT_TYPES).build();
            case SSA:
                return AdIncomeQueryParam.builder().productTypes(SSA_PRODUCT_TYPES)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS).build();
            case TOP_VIEW:
                return AdIncomeQueryParam.builder()
                        .productTypes(TOP_VIEW_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .build();
            case SEARCH_CPT:
                return AdIncomeQueryParam.builder()
                        .productTypes(SEARCH_CPT_PRODUCT_TYPES)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case LIVE:
                return AdIncomeQueryParam.builder()
                        .productTypes(LIVE_PRODUCT_TYPES)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case OTT_TOP_VIEW:
                return AdIncomeQueryParam.builder()
                        .productTypes(OTT_TOP_VIEW_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case LIVE_PICK_UP:
                return AdIncomeQueryParam.builder().productTypes(LIVE_PICK_UP_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case BUSINESS_FLY_POST_PAY:
                return AdIncomeQueryParam.builder().productTypes(BUSINESS_FLY_POST_PAY_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case BUSINESS_FLY_ADVANCE_PAY:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .productTypes(BUSINESS_FLY_ADVANCE_PAY_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case BRADN:
                return AdIncomeQueryParam.builder().productTypes(BRAND_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case CPT:
                return AdIncomeQueryParam.builder()
                        .productTypes(CPT_PRODUCT_TYPES)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case NON_STANDARD:
                return AdIncomeQueryParam.builder().productTypes(NON_STANDARD_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case NON_STANDARD_ALL_FINICAL:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .orderType(Lists.newArrayList(CrmOrderType.NON_STANDARD.getCode()))
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeOrderFirstCategoryIds(Lists.newArrayList(28, 44, 144)) //商单
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case NON_STANDARD_ALL:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .orderType(Lists.newArrayList(CrmOrderType.NON_STANDARD.getCode()))
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeOrderFirstCategoryIds(Lists.newArrayList(28, 44, 144)) //排除商单
                        .excludeOrderSecondCategoryIds(Lists.newArrayList(129, 417, 416, 415, 414, 360, 359)) // 去除商业起飞、全屏视频闪屏PD、全屏图文闪屏PD、半屏视频闪屏PD、半屏图文闪屏PD、信息流小卡PD、信息流大卡PD
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case NON_STANDARD_FLOW:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .orderType(Lists.newArrayList(CrmOrderType.NON_STANDARD.getCode()))
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderFirstCategoryIds(Lists.newArrayList(30, 43))  //硬广 直播常规硬广
                        .excludeOrderSecondCategoryIds(Lists.newArrayList(129, 417, 416, 415, 414, 360, 359))  // 去除商业起飞、全屏视频闪屏PD、全屏图文闪屏PD、半屏视频闪屏PD、半屏图文闪屏PD、信息流小卡PD、信息流大卡PD
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case NON_STANDARD_NO_FLOW:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .orderType(Lists.newArrayList(CrmOrderType.NON_STANDARD.getCode()))
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeOrderFirstCategoryIds(Lists.newArrayList(28, 30, 43, 44, 144)) //商单 硬广 直播常规硬广 44 直播主播商单  144 直播主播
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case PD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(CrmOrderType.PD.getCode(), CrmOrderType.SSA_PD.getCode()))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case PDB:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(CrmOrderType.PDB.getCode(), CrmOrderType.SSA_PDB.getCode()))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case OGV_GD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(CrmOrderType.OGV_GD.getCode()))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case OGV_CPT:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(CrmOrderType.OGV_CPT.getCode()))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case OTT_SSA_GD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(CrmOrderType.OTT_SSA_GD.getCode()))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case GD:
                return AdIncomeQueryParam.builder()
                        .productTypes(GD_PRODUCT_TYPES)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case SSA_CPT:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.SSA_CPT.getCode()
                        )).build();
            case TOP_VIEW_GD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.TOP_VIEW_GD.getCode()
                        )).build();
            case OGV:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.OGV.getCode()
                        )).build();
            case MAS:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.MAS.getCode()
                        )).build();
            case RTB:
                return AdIncomeQueryParam.builder().productTypes(RTB_PRODUCT_TYPES)
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case GD_PLUS:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .salesType(Lists.newArrayList(
                                SalesType.FLY_PRE_PAY_GD_PLUS.getCode()
                        ))  //起飞预付费gd+
                        .build();
            case SSA_CPM:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.SSA_CPM.getCode()
                        ))
                        .build();
            case SSA_GD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(
                                CrmOrderType.SSA_GD.getCode()
                        ))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case LIVE_HARD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .orderType(Lists.newArrayList(
                                CrmOrderType.LIVE.getCode()
                        ))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case INVESTMENT_HARD_AD:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.SSA_CPT.getCode(),
                                CrmOrderType.TOP_VIEW.getCode(),
                                CrmOrderType.OTT_TOP_VIEW.getCode(),
                                CrmOrderType.LIVE.getCode(),
                                CrmOrderType.SEARCH_CPT.getCode(),
                                CrmOrderType.CPT.getCode(),
                                CrmOrderType.OGV.getCode(),
                                CrmOrderType.MAS.getCode(),
                                CrmOrderType.GD.getCode(),
                                CrmOrderType.SSA_GD.getCode(),
                                CrmOrderType.FLY_GD.getCode(),
                                CrmOrderType.SSA_CPM.getCode()
                        ))
                        .build();
            case INVESTMENT_NO_STAND:  //包含内容型
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.NON_STANDARD.getCode(),
                                CrmOrderType.PICK_UP.getCode(),
                                CrmOrderType.UP_REQUIREMENT.getCode()
                        ))
                        .orderFirstCategoryIdsOr(Lists.newArrayList(28, 44))
                        .shouldField(Lists.newArrayList("orderType", "orderFirstCategoryId"))
                        .build();
            case INVESTMENT_CONTENT_VIDEO_CRM:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.PICK_UP.getCode(),
                                CrmOrderType.UP_REQUIREMENT.getCode()
                        ))
                        //商单 一级产品类型 28
                        .orderFirstCategoryIdsOr(Lists.newArrayList(28))
                        .shouldField(Lists.newArrayList("orderType", "orderFirstCategoryId"))
                        .build();
            case INVESTMENT_CONTENT_LIVE_CRM:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIds())
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        // 直播主播商单 一级产品类型 44
                        .orderFirstCategoryIdsOr(Lists.newArrayList(28, 44))
                        .shouldField(Lists.newArrayList("orderFirstCategoryId"))
                        .build();
            case VIDEO_BIZ_ORDER_PICKUP:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .pickupCooperationTypes(Lists.newArrayList(
                                PickupOrderCooperationType.CONTENT_CUSTOMIZED.getCode(),
                                PickupOrderCooperationType.BRAND_EMBEDDING.getCode()
                        ))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case VIDEO_BIZ_ORDER_PICKUP_CRM:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .orderType(Lists.newArrayList(
                                CrmOrderType.PICK_UP.getCode(),
                                CrmOrderType.UP_REQUIREMENT.getCode(),
                                CrmOrderType.UP_MAKE.getCode()
                        ))
                        //商单 一级产品类型 28
                        .orderFirstCategoryIdsOr(Lists.newArrayList(28))
                        // 排除 直播商单影响
                        .excludeOrderFirstCategoryIds(Lists.newArrayList(CONTRACT_ORDER_LIVE_ANCHOR_PICKUP_FIRST_CATE))
                        .shouldField(Lists.newArrayList("orderType", "orderFirstCategoryId"))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case LIVE_BIZ_ORDER_CRM:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        // 直播主播商单 一级产品类型 44
                        .orderFirstCategoryIds(Lists.newArrayList(44, 144)) // 44  直播主播商单, 144 直播主播
                        .shouldField(Lists.newArrayList("orderFirstCategoryId"))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case LIVE_BIZ_ORDER_PICKUP:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .pickupCooperationTypes(Lists.newArrayList(
                                PickupOrderCooperationType.ONLINE_LIVE_BROADCAST.getCode(),
                                PickupOrderCooperationType.OFFLINE_LIVE_BROADCAST.getCode(),
                                PickupOrderCooperationType.LIVE_BROADCAST.getCode()
                        ))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case CONTENT_PICKUP_OTHER:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .pickupCooperationTypes(Lists.newArrayList(
                                PickupOrderCooperationType.UNKNOWN.getCode(),
                                PickupOrderCooperationType.DIRECT_DYNAMIC.getCode(),
                                PickupOrderCooperationType.FORWARD_DYNAMIC.getCode(),
                                PickupOrderCooperationType.OFF_LINE_ACTIVITY.getCode(),
                                PickupOrderCooperationType.TAKE_PART_IN_FILMING.getCode(),
                                PickupOrderCooperationType.HELP_TO_DELIVERY.getCode(),
                                PickupOrderCooperationType.ARTISTIC_LICENSE.getCode(),
                                PickupOrderCooperationType.HAND_PAINTED.getCode(),
                                PickupOrderCooperationType.OTHERS.getCode()
                        ))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case SELL_GOODS:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .productTypes(FLY_PRODUCT_TYPES)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .salesType(Lists.newArrayList(SalesType.CPC.getCode(),
                                SalesType.CPM.getCode(),
                                SalesType.FLY_PRE_PAY_GD_PLUS.getCode()
                        ))
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case RTB_FLY_CLOSE:
                return AdIncomeQueryParam.builder()
                        .productTypes(RTB_CLOSE_PRODUCT_TYPES)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeDepIds(DepAchieveConfigBeta.FINICAL_EXCLUDE_DEPIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case ADX_CLOSE:
                return AdIncomeQueryParam.builder()
                        .productTypes(ADX_CLOSE_PRODUCT_TYPES)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case DPA_CLOSE:
                return AdIncomeQueryParam.builder()
                        .productTypes(DPA_CLOSE_PRODUCT_TYPES)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case EFFECT_CLOSE:
                return AdIncomeQueryParam.builder()
                        .productTypes(EFFECT_CLOSE_PRODUCT_TYPES)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeDepIds(DepAchieveConfigBeta.FINICAL_EXCLUDE_DEPIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case ADX_WITH_CLOSE:
                return AdIncomeQueryParam.builder()
                        .productTypes(ADX_WITH_CLOSE_PRODUCT_TYPES)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case DPA_WITH_CLOSE:
                return AdIncomeQueryParam.builder()
                        .productTypes(DPA_WITH_CLOSE_PRODUCT_TYPES)
                        .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();
            case CLUE_PASS:
                return AdIncomeQueryParam.builder()
                        .isInner(IsInnerEnum.OUTER.getCode())
                        .productTypes(CLUE_PASS_PRODUCT_TYPES)
                        .excludeDepIds(DepAchieveConfigBeta.FINICAL_EXCLUDE_DEPIDS)
                        .excludeSaleIds(buildExcludeSaleIds())
                        .excludeAccountIds(DepAchieveConfigBeta.CPC_CPM_EXCLUDE_ACCOUNTIDS)
                        .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                        .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                        .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                        .build();

            default:
                logger.error("unknown IncomeComposition {}", composition);
                return null;
        }
    }

    private AdIncomeQueryParam personFlyCon() {
        return AdIncomeQueryParam.builder()
                .userTypes(Lists.newArrayList(UserType.PERSONAL_FLY.getCode()))
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .productTypes(PERSON_FLY_PRODUCT_TYPES)
                .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .build();
    }


    /**
     * 部门：
     * 账号内外部属性为外部，账号部门不为MCN激励、测试部（部门id：160,19）且具有内容起飞投放权限的广告主账号的消耗
     *
     * @return
     * @throws IncomeConditionInvalidException
     */
    private AdIncomeQueryParam contentFlyCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                .productTypes(CONTENT_FLY_PRODUCT_TYPES)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .build();
    }


    public AdIncomeQueryParam contractAdCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .isInner(IsInnerEnum.OUTER.getCode())
                .extendBillTypes(Lists.newArrayList(ExtendBillType.CONTRACT_BILL.getCode()))
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeAllExistSaleIds(buildExcludeSaleIds())
                .excludeOrderFirstCategoryIds(Lists.newArrayList(28, 44, 144)) //排除商单
                .build();
    }


    /**
     * 品牌营销部、效果营销部、平台代理商部门所有配置了直客销售账号产品类型为CPC（含现金、返货、专返）的消耗
     *
     * @return
     * @throws IncomeConditionInvalidException
     */
    public AdIncomeQueryParam cpcCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .productTypes(CPC_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .build();
    }


    public AdIncomeQueryParam cpmCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .productTypes(CPM_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .build();
    }


    public AdIncomeQueryParam businessFlyCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .productTypes(BUSINESS_FLY_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(Lists.newArrayList(CONTENT_EXCLUDE_DEPIDS))
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .build();
    }

    public AdIncomeQueryParam sanLianOtherCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .productTypes(SAN_LIAN_OTHER_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(Lists.newArrayList(CONTENT_EXCLUDE_DEPIDS))
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .build();
    }

    public AdIncomeQueryParam dpaCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .productTypes(DPA_PRODUCT_TYPES)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .build();
    }


    public AdIncomeQueryParam adxCondition() throws IncomeConditionInvalidException {
        return AdIncomeQueryParam.builder()
                .productTypes(ADX_PRODUCT_TYPES)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeDepIds(FINICAL_EXCLUDE_DEPIDS)
                .isInner(IsInnerEnum.OUTER.getCode())
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .build();
    }

    /**
     * cpc效果复计收入, 必选和商业起飞消耗所属账号对应的私海销售为品牌团队销售部分  cpc+dpa
     *
     * @return
     * @throws IncomeConditionInvalidException
     */

    public AdIncomeQueryParam repeatCpcCondition() throws IncomeConditionInvalidException {
        //效果营销部下的账号 关联销售为品牌团队的
        List<Integer> accountIdByComposition = achieveConsumeParamHelper.getAccountId(QueryAccountParam.builder()
                .userTypes(UserType.getNotPersonalFly())
                .departmentIds(rtbAchieveDepIds)
                .build());

        //查询销售团队的账号
        List<Integer> accountId = retainBrandAccountBySale(accountIdByComposition);

        return AdIncomeQueryParam.builder()
                .salesType(Lists.newArrayList(SalesType.CPC.getCode()))
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .accountId(accountId)
                .build();
    }


    public AdIncomeQueryParam repeatCpmCondition() throws IncomeConditionInvalidException {
        //效果营销部下的账号 关联销售为品牌团队的
        List<Integer> accountIdByComposition = achieveConsumeParamHelper.getAccountId(QueryAccountParam.builder()
                .isSupportFly(AdSupportStatus.DISABLE.getCode())
                .isSupportContent(AdSupportStatus.DISABLE.getCode())
                .userTypes(UserType.getNotPersonalFly())
                .departmentIds(rtbAchieveDepIds)
                .build());
        //公私海属于品牌团队销售部分的账号
        List<Integer> accountId = retainBrandAccountBySale(accountIdByComposition);

        return AdIncomeQueryParam.builder()
                .salesType(Lists.newArrayList(SalesType.CPM.getCode()))
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .accountId(accountId)
                .build();
    }


    public AdIncomeQueryParam repeatBsiFlyCondition() throws IncomeConditionInvalidException {
        //效果营销部下的账号 关联销售为品牌团队的

        List<Integer> accountIdByComposition = achieveConsumeParamHelper.getAccountId(QueryAccountParam.builder()
                .isSupportFly(AdSupportStatus.ENABLE.getCode())
                .departmentIds(rtbAchieveDepIds)
                .build());
        List<Integer> accountId = retainBrandAccountBySale(accountIdByComposition);

        return AdIncomeQueryParam.builder()
                .salesType(Lists.newArrayList(SalesType.CPM.getCode()))
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .accountId(accountId)
                .build();
    }

    private List<Integer> retainBrandAccountBySale(List<Integer> accountIdByComposition) throws IncomeConditionInvalidException {
        //品牌销售关联的广告主账号
        List<Integer> accountIdByTeamSaleType = achieveConsumeParamHelper.getAccountIdByBrandSaleType();
        List<Integer> accountId = accountIdByComposition.stream().filter(accountIdByTeamSaleType::contains).collect(Collectors.toList());

        if (CollectionUtils.isEmpty(accountId)) {
            throw new IncomeConditionInvalidException();
        }
        return accountId;
    }


    /**
     * 花火业绩口径
     *
     * @return
     * @throws IncomeConditionInvalidException
     */
    public AdIncomeQueryParam huaHuoCondition() throws IncomeConditionInvalidException {
        //合作类型为（线下直播、线上直播)  CooperationType
        return AdIncomeQueryParam.builder()
                .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .build();
    }


    private AdIncomeQueryParam pickUpUnPaidCon() {
        return AdIncomeQueryParam.builder()
                .productTypes(PICK_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .agentIsInner(IsInnerEnum.OUTER.getCode())
                .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .build();
    }

    private AdIncomeQueryParam pickUpUnConfirmCon() {
        return AdIncomeQueryParam.builder()
                .productTypes(PICK_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .agentIsInner(IsInnerEnum.OUTER.getCode())
                .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .build();
    }

    private AdIncomeQueryParam pickUpConfirmCon() {
        return AdIncomeQueryParam.builder()
                .productTypes(PICK_PRODUCT_TYPES)
                .isInner(IsInnerEnum.OUTER.getCode())
                .agentIsInner(IsInnerEnum.OUTER.getCode())
                .excludeDepIds(CONTENT_EXCLUDE_DEPIDS)
                .excludeAccountIds(CPC_CPM_EXCLUDE_ACCOUNTIDS)
                .pickupCrmOrderIds(PICKUP_ORDER_WITHOUT_CONTRACT)
                .excludeGroupIds(EXCLUDE_ACC_GROUP_IDS)
                .excludeAllExistSaleIds(buildExcludeSaleIdsNot137())
                .excludeTestAgentIds(achieveConsumeParamHelper.getTestAgentIds())
                .build();
    }


    public List<Integer> buildExcludeSaleIds() {
        List<Integer> salIds = iSaleGroupService.getChildSaleIdByGroupIdWithProhibit(EXCLUDE_SALE_GROUP_IDS);
        if (org.apache.commons.collections4.CollectionUtils.isNotEmpty(salIds)) {
            salIds.addAll(EXCLUDE_SALEIDS);
            return salIds;
        } else {
            return EXCLUDE_SALEIDS;
        }
    }

    public List<Integer> buildExcludeSaleIdsNot137() {
        //查询测试组下面的所有销售salIds
        List<Integer> salIds = iSaleGroupService.getChildSaleIdByGroupIdWithProhibit(EXCLUDE_SALE_GROUP_IDS);
        if (CollectionUtils.isEmpty(salIds)) {
            return Collections.emptyList();
        }
        return salIds;
    }
}
