package com.bilibili.crm.platform.biz.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 9/20/23
 **/
@Builder
@Data
@NoArgsConstructor
@AllArgsConstructor
public class NewFlyUpOrderDataDto implements Serializable {
    private static final long serialVersionUID = 8134120444130365156L;

    private List<NewFlyUpOrderDetailDto> list;
    private Integer total;
}
