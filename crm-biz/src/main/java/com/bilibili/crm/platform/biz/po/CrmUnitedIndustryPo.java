package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmUnitedIndustryPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 行业名
     */
    private String name;

    /**
     * 层级（0-一级 1-二级 2-三级）
     */
    private Integer level;

    /**
     * 父级id
     */
    private Integer pId;

    /**
     * 资质提示文案
     */
    private String prompt;

    /**
     * 备注
     */
    private String remark;

    /**
     * 1:正常 2:封禁
     */
    private Integer status;

    /**
     * 软删除 0 否 1是
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 是否为垂直行业，0——否，1-是
     */
    private Integer isVertical;

    /**
     * 行业业绩归属销售组id
     */
    private String achieveBelongSaleGroupIds;

    /**
     * 行业业绩归属销售组名称
     */
    private String achieveBelongSaleGroupNames;

    private static final long serialVersionUID = 1L;
}