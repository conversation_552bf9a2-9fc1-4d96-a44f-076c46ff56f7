package com.bilibili.crm.platform.biz.config;

import com.bapis.ad.adp.budget.BudgetServiceGrpc;
import com.bapis.ad.commercialorder.cmorder.CmOrderServiceGrpc;
import com.bapis.ad.crm.platform.coupon.PlatformCouponServiceGrpc;
import com.bapis.ad.crm.platform.project.ProjectServiceGrpc;
import com.bapis.ad.crm.wallet.coupon.query.CouponQueryServiceGrpc;
import com.bapis.ad.crm.wallet.soa.coupon.ISoaCouponServiceGrpc;
import com.bapis.ad.crm.wallet.soa.wallet.ISoaAccountWalletServiceGrpc;
import com.bapis.ad.mgk.LandingPageServiceGrpc;
import com.bapis.ad.mng.awaken_app.AwakenAppLabelConfigServiceGrpc;
import com.bapis.ad.pandora.service.LongTermConversionServiceGrpc;
import com.bapis.archive.service.ArchiveGrpc;
import com.bapis.copyright.right.service.work.WorkGrpc;
import com.bapis.cpm.bdata.service.BDataServiceGrpc;
import com.bapis.cpm.partner.task.TaskServiceApiGrpc;
import com.bapis.datacenter.service.oneservice.OneServiceOpenApiManagerGrpc;
import com.bapis.infra.service.taishan.TaishanProxyGrpc;
import com.bapis.topic.service.TopicGrpc;
import com.bapis.videoup.open.service.VideoUpOpenGrpc;
import com.bilibili.sycpb.plugin.sycpb.accesslog.enums.AccessLogFileld;
import com.bilibili.warp.grpc.client.channel.GrpcChannelBuilder;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import io.grpc.*;
import org.slf4j.MDC;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import pleiades.component.env.v1.Env;
import pleiades.component.rpc.client.ChannelBuilder;
import pleiades.component.rpc.client.naming.RPCNamingClientNameResolverFactory;
import pleiades.venus.naming.client.NamingClient;
import pleiades.venus.naming.client.Namings;
import pleiades.venus.naming.client.resolve.NamingResolver;

import javax.annotation.Resource;
import java.util.concurrent.TimeUnit;

import static io.grpc.netty.NettyChannelBuilder.DEFAULT_FLOW_CONTROL_WINDOW;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-12-28 21:24:33
 * @description:
 **/
@Configuration
public class CrmGrpcClientConfig {

    @Resource
    private NamingClient namingClient;

    @Bean
    public ClientInterceptor grpcTraceInterceptor() {
        return new ClientInterceptor() {
            @Override
            public <ReqT, RespT> ClientCall<ReqT, RespT> interceptCall(
                    MethodDescriptor<ReqT, RespT> method,
                    CallOptions callOptions,
                    Channel next) {

                return new ForwardingClientCall.SimpleForwardingClientCall<ReqT, RespT>(
                        next.newCall(method, callOptions)) {

                    @Override
                    public void start(Listener<RespT> responseListener, Metadata headers) {
                        String methodName = method.getFullMethodName();
                        Transaction t = Cat.newTransaction("GRPC.CALL", methodName);
                        t.addData(AccessLogFileld.req_trace_id.name(), MDC.get(AccessLogFileld.req_trace_id.name()));

                        try {
                            MDC.put("cat", Cat.getCurrentMessageId());
                            super.start(new ForwardingClientCallListener.SimpleForwardingClientCallListener<RespT>(responseListener) {
                                @Override
                                public void onClose(Status status, Metadata trailers) {
                                    try {
                                        if (status.isOk()) {
                                            t.setStatus(Transaction.SUCCESS);
                                        } else {
                                            Cat.logError(new RuntimeException(status.getDescription()));
                                            t.setStatus(status.toString());
                                        }
                                        super.onClose(status, trailers);
                                    } finally {
                                        MDC.remove("cat");
                                        t.complete();
                                    }
                                }
                            }, headers);
                        } catch (Throwable e) {
                            Cat.logError(e);
                            t.setStatus(e);
                            throw e;
                        }
                    }
                };
            }
        };
    }


    /**
     * 麦哲伦作品GRPC服务
     *
     * @param rightWorkChannel
     * @return
     */
    @Bean
    public WorkGrpc.WorkBlockingStub workServiceBlockingStub(Channel rightWorkChannel) {
        return WorkGrpc.newBlockingStub(rightWorkChannel);
    }

    @Bean("longTermConversionServiceBlockingStub")
    public LongTermConversionServiceGrpc.LongTermConversionServiceBlockingStub longTermConversionServiceBlockingStub(Channel longTermConversionChannel) {
        return LongTermConversionServiceGrpc.newBlockingStub(longTermConversionChannel);
    }

    @Bean("longTermConversionChannel")
    public Channel longTermConversionChannel() {
        NamingResolver resolver = namingClient.resolveForReady("sycpb.platform.cpm-pandora", Namings.Scheme.GRPC);
        return ChannelBuilder.forTarget("sycpb.platform.cpm-pandora")
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean
    public TaishanProxyGrpc.TaishanProxyBlockingStub taiShanBlockingStub(Channel taiShanChannel) {
        return TaishanProxyGrpc.newBlockingStub(taiShanChannel);
    }

    @Bean("taiShanChannel")
    public Channel taiShanChannel(NamingClient namingClient) {
        NamingResolver resolver = namingClient.resolveForReady("inf.taishan.proxy", Namings.Scheme.GRPC);

        return ChannelBuilder.forTarget("inf.taishan.proxy")
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .flowControlWindow(3 * DEFAULT_FLOW_CONTROL_WINDOW)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean
    public TaskServiceApiGrpc.TaskServiceApiBlockingStub masTaskBlockingStub(Channel masTaskChannel) {
        return TaskServiceApiGrpc.newBlockingStub(masTaskChannel);
    }

    @Bean("masTaskChannel")
    public Channel masTaskChannel(NamingClient namingClient) {
        NamingResolver resolver = namingClient.resolveForReady("sycpb.cpm.business-partner"
                , Namings.Scheme.GRPC
                , NamingResolver.Args.builder().optional(true).build());

        return ChannelBuilder.forTarget("sycpb.cpm.business-partner")
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .flowControlWindow(3 * DEFAULT_FLOW_CONTROL_WINDOW)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean
    public ArchiveGrpc.ArchiveBlockingStub archiveBlockingStub(Channel archiveChannel) {
        return ArchiveGrpc.newBlockingStub(archiveChannel);
    }

    @Bean("topicChannel")
    public Channel topicChannel(NamingClient namingClient) {
        NamingResolver resolver = namingClient.resolveForReady("main.topic.new-topic-service", Namings.Scheme.GRPC);

        return ChannelBuilder.forTarget("main.topic.new-topic-service")
                .disableRetry()
                .directExecutor()
                .usePlaintext()
                .defaultLoadBalancingPolicy("round_robin")
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .keepAliveTime(1, TimeUnit.SECONDS)
                .flowControlWindow(3 * DEFAULT_FLOW_CONTROL_WINDOW)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .idleTimeout(1, TimeUnit.MINUTES)
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean
    public TopicGrpc.TopicBlockingStub topicBlockingStub(Channel topicChannel) {
        return TopicGrpc.newBlockingStub(topicChannel);
    }

    @Bean("videoChannel")
    public Channel videoChannel(NamingClient namingClient) {
        NamingResolver resolver = namingClient.resolveForReady("videoup.open.service", Namings.Scheme.GRPC);

        return ChannelBuilder.forTarget("videoup.open.service")
                .disableRetry()
                .directExecutor()
                .usePlaintext()
                .defaultLoadBalancingPolicy("round_robin")
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .keepAliveTime(1, TimeUnit.SECONDS)
                .flowControlWindow(3 * DEFAULT_FLOW_CONTROL_WINDOW)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .idleTimeout(1, TimeUnit.MINUTES)
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean
    public VideoUpOpenGrpc.VideoUpOpenBlockingStub videoUpOpenBlockingStub(Channel videoChannel) {
        return VideoUpOpenGrpc.newBlockingStub(videoChannel);
    }

    @Bean("bDataChannel")
    public Channel bDataChannel(NamingClient namingClient) {
        NamingResolver resolver = namingClient.resolveForReady("sycpb.up-income.bdata-service", Namings.Scheme.GRPC);

        return ChannelBuilder.forTarget("sycpb.up-income.bdata-service")
                .disableRetry()
                .directExecutor()
                .usePlaintext()
                .defaultLoadBalancingPolicy("round_robin")
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .keepAliveTime(1, TimeUnit.SECONDS)
                .flowControlWindow(3 * DEFAULT_FLOW_CONTROL_WINDOW)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .idleTimeout(1, TimeUnit.MINUTES)
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean
    public BDataServiceGrpc.BDataServiceBlockingStub bDataBlockingStub(Channel bDataChannel) {
        return BDataServiceGrpc.newBlockingStub(bDataChannel);
    }

    @Bean("rightWorkChannel")
    public Channel rightWorkChannel(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "copyright.rights.service");
    }

    @Bean
    public OneServiceOpenApiManagerGrpc.OneServiceOpenApiManagerBlockingStub serviceBlockingStub(Channel dataCenterChannel) {
        return OneServiceOpenApiManagerGrpc.newBlockingStub(dataCenterChannel);
    }

    @Bean("dataCenterChannel")
    public Channel dataCenterChannel(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "datacenter.oneservice.akuya-dispatch-service");
    }

    private Channel buildDefaultChannel(NamingClient namingClient, String serviceName) {
        NamingResolver resolver = namingClient.resolveForReady(serviceName, Namings.Scheme.GRPC,NamingResolver.Args.builder().optional(true).build());

        return ChannelBuilder.forTarget(serviceName)
                .directExecutor()
                .disableRetry()
                .defaultLoadBalancingPolicy("round_robin")
                .usePlaintext()
                .keepAliveTime(1, TimeUnit.SECONDS)
                .keepAliveTimeout(100, TimeUnit.MILLISECONDS)
                .idleTimeout(1, TimeUnit.MINUTES)
                .nameResolverFactory(new RPCNamingClientNameResolverFactory(Env.getZone(), resolver))
                .intercept(grpcTraceInterceptor()) // 添加 CAT 监控拦截器
                .build();
    }

    @Bean("adpPlatformChannel")
    public Channel adpPlatformChannel(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "sycpb.cpm.cpm-adp");
    }

    @Bean
    public BudgetServiceGrpc.BudgetServiceBlockingStub budgetBlockingStub(Channel adpPlatformChannel) {
        return BudgetServiceGrpc.newBlockingStub(adpPlatformChannel);
    }

    @Bean("crmPlatformChannel")
    public Channel crmPlatformChannel(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "sycpb.platform.crm-platform");
    }

    @Bean
    public ProjectServiceGrpc.ProjectServiceBlockingStub projectServiceBlockingStub(Channel crmPlatformChannel) {
        return ProjectServiceGrpc.newBlockingStub(crmPlatformChannel);
    }

    @Bean
    public PlatformCouponServiceGrpc.PlatformCouponServiceBlockingStub platformCouponServiceBlockingStub(Channel crmPlatformChannel) {
        return PlatformCouponServiceGrpc.newBlockingStub(crmPlatformChannel);
    }

    @Bean("accountOfflineNotify")
    public Channel accountOfflineNotify(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "sycpb.cpm.mgk-portal");
    }

    @Bean
    public LandingPageServiceGrpc.LandingPageServiceBlockingStub landingPageServiceStub(Channel accountOfflineNotify) {
        return LandingPageServiceGrpc.newBlockingStub(accountOfflineNotify);
    }

    @Bean("accountAwakeConfig")
    public Channel accountAwakeConfig(NamingClient namingClient) {
        return buildDefaultChannel(namingClient, "sycpb.cpm.cpm-mng");
    }

    @Bean
    public AwakenAppLabelConfigServiceGrpc.AwakenAppLabelConfigServiceBlockingStub awakenAppLabelConfigServiceStub(Channel accountAwakeConfig) {
        return AwakenAppLabelConfigServiceGrpc.newBlockingStub(accountAwakeConfig);
    }


    @Bean("appSoaCouponGrpc")
    public ISoaCouponServiceGrpc.ISoaCouponServiceBlockingStub crmCouponBlockingStub(Channel crmWalletChannel) {
        return ISoaCouponServiceGrpc.newBlockingStub(crmWalletChannel);
    }

    @Bean("appSoaAccountWalletGrpc")
    public ISoaAccountWalletServiceGrpc.ISoaAccountWalletServiceBlockingStub crmWalletBlockingStub(Channel crmWalletChannel) {
        return ISoaAccountWalletServiceGrpc.newBlockingStub(crmWalletChannel);
    }

    @Bean("crmWalletChannel")
    public Channel crmWalletChannel() {
        return buildDefaultChannel(namingClient, "sycpb.platform.crm-wallet");
    }

    @Bean("couponQueryServiceGrpc")
    public CouponQueryServiceGrpc.CouponQueryServiceBlockingStub couponQueryServiceBlockingStub(Channel crmWalletChannel) {
        return CouponQueryServiceGrpc.newBlockingStub(crmWalletChannel);
    }


    @Bean("cmOrderServiceGrpc")
    public CmOrderServiceGrpc.CmOrderServiceBlockingStub cmOrderBlockingStub(Channel pickupChannel) {
        return CmOrderServiceGrpc.newBlockingStub(pickupChannel);
    }

    /**
     * 花火
     *
     * @return
     */
    @Bean("pickupChannel")
    public Channel pickupChannel() {
        return buildDefaultChannel(namingClient, "sycpb.cpm.commercial-order-portal");
    }

}
