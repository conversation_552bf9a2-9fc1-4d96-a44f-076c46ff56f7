package com.bilibili.crm.platform.biz.service.return_online;

import com.bilibili.crm.platform.api.policy.dto.EnterpriseWechatNoticeInfo;
import com.bilibili.crm.platform.biz.mq.message.flow.SendEnterpriseWechatNoticeEvent;
import com.bilibili.crm.platform.biz.service.policy.component.config.PolicyFlowConfig;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

/**
 * 企业微信通知发送器
 *
 * <AUTHOR>
 * @date 2022/1/26 下午9:16
 */
@Slf4j
@Component
public class EnterWechatMsgSender implements ApplicationEventPublisherAware {

    private ApplicationEventPublisher applicationEventPublisher;

    @Autowired
    private PolicyFlowConfig policyFlowConfig;

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }

    public void send(String title, String content) {
        SendEnterpriseWechatNoticeEvent sendEnterpriseWechatNoticeEvent = new SendEnterpriseWechatNoticeEvent(this,
                EnterpriseWechatNoticeInfo.builder()
                        .receivers(policyFlowConfig.getStableNoticeUser())
                        .title(title)
                        .content(content + String.format(" env:%s", policyFlowConfig.getEnv()))
                        .build());
        applicationEventPublisher.publishEvent(sendEnterpriseWechatNoticeEvent);
    }
}
