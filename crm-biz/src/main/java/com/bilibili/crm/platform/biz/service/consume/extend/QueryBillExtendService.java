package com.bilibili.crm.platform.biz.service.consume.extend;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.crm.biz.es_migrate.bean.BillConsumeDayExtendAggEsBean;
import com.bilibili.crm.biz.es_migrate.bean.BillConsumeDayExtendEsBean;
import com.bilibili.crm.biz.es_migrate.bean.BillConsumeDayExtendQuery;
import com.bilibili.crm.biz.es_migrate.service.BillConsumeDayExtendAggOlapService;
import com.bilibili.crm.biz.es_migrate.service.BillConsumeDayExtendOlapService;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.biz_common.olap.migrate.enums.MigrateBizScene;
import com.bilibili.crm.biz_common.olap.migrate.util.GenericComparator;
import com.bilibili.crm.biz_common.olap.migrate.util.MigrateUtil;
import com.bilibili.crm.platform.api.consume.dto.QueryConsumeParam;
import com.bilibili.crm.platform.api.es.agg.helper.bean.AggregationCondition;
import com.bilibili.crm.platform.api.es.agg.helper.bean.AggregationField;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationBucketType;
import com.bilibili.crm.platform.api.es.agg.helper.enums.AggregationMetricType;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.ESBillConsumeExtendPo;
import com.bilibili.crm.platform.biz.es.agg.helper.EsAggregationSearchHelper;
import com.bilibili.crm.platform.biz.es.agg.helper.aggregation.AggregationConditionBuilder;
import com.bilibili.crm.platform.biz.service.consume.config.ConsumeExtendConfig;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.SearchResultMapper;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
import org.springframework.data.elasticsearch.core.query.NativeSearchQuery;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @author: brady
 * @time: 2021/3/18 3:48 下午
 */
@Slf4j
@Service
public class QueryBillExtendService {
    /**
     * scroll游标快照超时时间，单位ms
     */
    private static final long SCROLL_TIMEOUT = 5 * 60 * 1000;

    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsAggregationSearchHelper esSearchHelper;
    @Autowired
    private ConsumerQueryBuilder consumerQueryBuilder;
    @Autowired
    private PaladinConfig paladinConfig;
    @Autowired
    private MigrateUtil migrateUtil;
    @Autowired
    private GenericComparator genericComparator;
    @Autowired
    private BillConsumeDayExtendAggOlapService billConsumeDayExtendAggOlapService;
    @Autowired
    private BillConsumeDayExtendOlapService billConsumeDayExtendOlapService;

    private final static Integer BATCH_QUERY_SIZE = 10000;

    /**
     * 查询账单数据 from extend es
     *
     * @param param
     * @return
     */
    public List<ESBillConsumeExtendPo> queryBillFromEs(QueryConsumeParam param) {

        List<ESBillConsumeExtendPo> result;

        // es集群迁移 bill_consume_day_extend
        if (paladinConfig.switchOn(HotKey.billConsumeDayExtendEsMigrateSwitch)) {
            result = this.queryBillFromEs4Migrate(param);
        } else {
            BoolQueryBuilder boolQueryBuilder = consumerQueryBuilder.getBoolQueryBuilder(param);
            log.info("queryBillFromEs {}", boolQueryBuilder);
            result = esSearchHelper.queryAggregation(
                    elasticsearchTemplate,
                    ConsumeExtendConfig.BILL_INDEX,
                    ESBillConsumeExtendPo.class,
                    boolQueryBuilder,
                    param.getAggregationCondition()
            );

            if (migrateUtil.canSimulation(HotKey.billConsumeDayExtendEsMigrateSwitch_simulation, param.getMigrateBizScene())) {
                List<ESBillConsumeExtendPo> finalResult = result;
                migrateUtil.doSimulation(param.getMigrateBizScene(), (n) -> {
                    // 仿真查询
                    List<ESBillConsumeExtendPo> newResult = this.queryBillFromEs4Migrate(param);

                    // 仿真比对
                    if (MigrateBizScene.QueryBillExtendService_groupTimeConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.valueOf(esBean.getGroupTime()),
                                esBean -> String.valueOf(esBean.getGroupTime()),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_accountIdAgentIdCustomerIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.join("_",
                                        esBean.getAccountId().toString(),
                                        esBean.getAgentId().toString(),
                                        esBean.getCustomerId().toString()

                                ),
                                esBean -> String.join("_",
                                        esBean.getAccountId().toString(),
                                        esBean.getAgentId().toString(),
                                        esBean.getCustomerId().toString()

                                ),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_projectItemIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.valueOf(esBean.getProjectItemId()),
                                esBean -> String.valueOf(esBean.getProjectItemId()),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_bizIndustryCategoryFirstIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.valueOf(esBean.getBizIndustryCategoryFirstId()),
                                esBean -> String.valueOf(esBean.getBizIndustryCategoryFirstId()),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_orderTypeConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.valueOf(esBean.getOrderType()),
                                esBean -> String.valueOf(esBean.getOrderType()),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_agentIdAccountIdCustomerIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.join("_",
                                        esBean.getAgentId().toString(),
                                        esBean.getAccountId().toString(),
                                        esBean.getCustomerId().toString()

                                ),
                                esBean -> String.join("_",
                                        esBean.getAgentId().toString(),
                                        esBean.getAccountId().toString(),
                                        esBean.getCustomerId().toString()

                                ),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_accountIdSpecialRedPacketConsumeCashConsumeRedPacketConsumeConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> esBean.getAccountId().toString(),
                                esBean -> esBean.getAccountId().toString(),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_groupTimeSpecialRedPacketConsumeCashConsumeRedPacketConsumeConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.valueOf(esBean.getGroupTime()),
                                esBean -> String.valueOf(esBean.getGroupTime()),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_accountIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> esBean.getAccountId().toString(),
                                esBean -> esBean.getAccountId().toString(),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_accountIdGroupTimeConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.join("_",
                                        esBean.getAccountId().toString(),
                                        String.valueOf(esBean.getGroupTime())

                                ),
                                esBean -> String.join("_",
                                        esBean.getAccountId().toString(),
                                        String.valueOf(esBean.getGroupTime())

                                ),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_accountIdAgentIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.join("_",
                                        esBean.getAccountId().toString(),
                                        esBean.getAgentId().toString()

                                ),
                                esBean -> String.join("_",
                                        esBean.getAccountId().toString(),
                                        esBean.getAgentId().toString()

                                ),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_productIdConsumeAmount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> esBean.getProductId().toString(),
                                esBean -> esBean.getProductId().toString(),
                                null,null
                        );
                    } else if (MigrateBizScene.QueryBillExtendService_groupTimeIdConsumeAmountClickCountShowCount.getName().equals(param.getMigrateBizScene())) {
                        genericComparator.compareAllPairs(
                                finalResult, newResult,
                                param.getMigrateBizScene(),
                                esBean -> String.join("_",
                                        String.valueOf(esBean.getGroupTime()),
                                        esBean.getId().toString()

                                ),
                                esBean -> String.join("_",
                                        String.valueOf(esBean.getGroupTime()),
                                        esBean.getId().toString()

                                ),
                                null,null
                        );
                    } else {
                        throw new RuntimeException("bizScene场景缺失");
                    }

                });
            }
        }
        return result;
    }

    public List<ESBillConsumeExtendPo> queryBillFromEs4Migrate(QueryConsumeParam param) {
        BoolQueryBuilder boolQueryBuilder = consumerQueryBuilder.getBoolQueryBuilder(param);

        List<BillConsumeDayExtendAggEsBean> esBeanList = billConsumeDayExtendAggOlapService.doSearchWithAgg(
                BillConsumeDayExtendQuery.builder()
                        .bizScene(param.getMigrateBizScene())
                        .appId(MigrateBizScene.CRM_APP_ID.getName())
                        .boolQueryBuilder(boolQueryBuilder)
                        .build(),
                param.getAggregationCondition()
        );
        if (org.springframework.util.CollectionUtils.isEmpty(esBeanList)) {
            return java.util.Collections.emptyList();
        }

        return esBeanList.stream().map(this::convertAggEsBeanToPo).collect(Collectors.toList());
    }

    public List<ESBillConsumeExtendPo> localTestQueryBillFromEs4Migrate() {

        AggregationCondition condition = AggregationConditionBuilder.build()
                .add(AggregationField.builder()
                        .aggType(AggregationBucketType.DATE_HISTOGRAM_DAY)
                        .field("groupTime")
                        .order(false)
                        .minDocCount(1L)
                        .build())
                .add(AggregationField.builder()
                        .aggType(AggregationBucketType.TERMS)
                        .field("id")
                        .build())
                .add(AggregationField.builder()
                        .aggType(AggregationMetricType.SUM)
                        .field("consumeAmount")
                        .format("#")
                        .build())
                .add(AggregationField.builder()
                        .aggType(AggregationMetricType.SUM)
                        .field("showCount")
                        .format("#")
                        .build())
                .add(AggregationField.builder()
                        .aggType(AggregationMetricType.SUM)
                        .field("clickCount")
                        .format("#")
                        .build());

        List<BillConsumeDayExtendAggEsBean> esBeanList = billConsumeDayExtendAggOlapService.mockSearchWithAgg(
                BillConsumeDayExtendQuery.builder()
                        .bizScene(null)
                        .appId(MigrateBizScene.CRM_APP_ID.getName())
                        .boolQueryBuilder(null)
                        .build(),
                condition,
                "bill_extend_test_topHits"
        );
        if (org.springframework.util.CollectionUtils.isEmpty(esBeanList)) {
            return java.util.Collections.emptyList();
        }

        return esBeanList.stream().map(this::convertAggEsBeanToPo).collect(Collectors.toList());
    }

    public List<ESBillConsumeExtendPo> queryBillAggWithSalesFromEs(QueryConsumeParam param) {
        List<ESBillConsumeExtendPo> result;

        // es集群迁移 bill_consume_day_extend
        if (paladinConfig.switchOn(HotKey.billConsumeDayExtendEsMigrateSwitch)) {
            result = this.queryBillAggWithSalesFromEs4Migrate(param);
        } else {
            BoolQueryBuilder boolQueryBuilder = consumerQueryBuilder.getBoolQueryBuilder(param);
            log.info("queryBillFromEs {}", boolQueryBuilder);

            NativeSearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withIndices(ConsumeExtendConfig.BILL_INDEX)
                    .addAggregation(esSearchHelper.createAggregationBuilder(param.getAggregationCondition()))
                    .withQuery(boolQueryBuilder)
                    .build();
            log.info("dsl {} ", searchQuery.getQuery().toString());
            result = elasticsearchTemplate.query(searchQuery, searchResponse -> {
                List<ESBillConsumeExtendPo> data = new ArrayList<>();
                List<? extends Terms.Bucket> accBuckets = ((Terms) searchResponse.getAggregations().get("agg_accountId")).getBuckets();
                for (Terms.Bucket accBucket : accBuckets) {
                    Integer accountId = Integer.parseInt(accBucket.getKey().toString());
                    List<? extends Terms.Bucket> agentBuckets = ((Terms) accBucket.getAggregations().get("agg_agentId")).getBuckets();
                    for (Terms.Bucket agentBucket : agentBuckets) {
                        Integer agentId = Integer.parseInt(agentBucket.getKey().toString());
                        List<? extends Terms.Bucket> saleBuckets = ((Terms) agentBucket.getAggregations().get("agg_sales")).getBuckets();
                        for (Terms.Bucket saleBucket : saleBuckets) {

                            Integer saleId = Integer.parseInt(saleBucket.getKey().toString());
                            if (!org.springframework.util.CollectionUtils.isEmpty(param.getSaleIds())) {
                                if (!param.getSaleIds().contains(saleId)) {
                                    continue;
                                }
                            }

                            long consumeAmount = (long) ((InternalSum) saleBucket.getAggregations().get("agg_consumeAmount")).getValue();
                            data.add(ESBillConsumeExtendPo.builder()
                                    .accountId(accountId)
                                    .agentId(agentId)
                                    .sales(Lists.newArrayList(saleId))
                                    .consumeAmount(consumeAmount)
                                    .build());
                        }

                    }

                }

                return data;
            });

            if (migrateUtil.canSimulation(HotKey.billConsumeDayExtendEsMigrateSwitch_simulation, MigrateBizScene.QueryBillExtendService_queryBillAggWithSalesFromEs.getName())) {
                List<ESBillConsumeExtendPo> finalResult = result;
                migrateUtil.doSimulation(MigrateBizScene.QueryBillExtendService_queryBillAggWithSalesFromEs.getName(), (n) -> {
                    // 仿真查询
                    List<ESBillConsumeExtendPo> newResult = this.queryBillAggWithSalesFromEs4Migrate(param);
                    // 仿真比对
                    genericComparator.compareAllPairs(
                            finalResult, newResult,
                            MigrateBizScene.QueryBillExtendService_queryBillAggWithSalesFromEs.getName(),
                            esBean -> String.join("_",
                                    esBean.getAccountId().toString(),
                                    esBean.getAgentId().toString(),
                                    esBean.getSales().toString()

                            ),
                            esBean -> String.join("_",
                                    esBean.getAccountId().toString(),
                                    esBean.getAgentId().toString(),
                                    esBean.getSales().toString()

                            ),
                            null,null
                    );
                });
            }
        }

        return result;
    }

    public List<ESBillConsumeExtendPo> queryBillAggWithSalesFromEs4Migrate(QueryConsumeParam param) {
        BoolQueryBuilder boolQueryBuilder = consumerQueryBuilder.getBoolQueryBuilder(param);

        List<BillConsumeDayExtendAggEsBean> esBeanList = billConsumeDayExtendAggOlapService.doSearchWithAgg(
                BillConsumeDayExtendQuery.builder()
                        .bizScene(MigrateBizScene.QueryBillExtendService_queryBillAggWithSalesFromEs.getName())
                        .appId(MigrateBizScene.CRM_APP_ID.getName())
                        .boolQueryBuilder(boolQueryBuilder)
                        .build(),
                param.getAggregationCondition()
        );
        if (CollectionUtils.isEmpty(esBeanList)) {
            return java.util.Collections.emptyList();
        }

        return esBeanList.stream().map(this::convertAggEsBeanToPo).collect(Collectors.toList());
    }

    public List<ESBillConsumeExtendPo> scrollQuery(QueryConsumeParam param) {
        List<ESBillConsumeExtendPo> result = new ArrayList<>();

        // es集群迁移 bill_consume_day_extend
        if (paladinConfig.switchOn(HotKey.billConsumeDayExtendEsMigrateSwitch)) {
            result = this.scrollQuery4Migrate(param);
        } else {
            BoolQueryBuilder boolQueryBuilder = consumerQueryBuilder.getBoolQueryBuilder(param);
            log.info("scrollQuery {}", boolQueryBuilder);
            SearchQuery searchQuery = new NativeSearchQueryBuilder()
                    .withIndices(ConsumeExtendConfig.BILL_INDEX)
                    .withQuery(boolQueryBuilder)
                    .withPageable(PageRequest.of(0, 2000))//从0页开始查
                    .build();

            ScrolledPage<ESBillConsumeExtendPo> scroll = (ScrolledPage<ESBillConsumeExtendPo>) elasticsearchTemplate.startScroll(SCROLL_TIMEOUT,
                    searchQuery, ESBillConsumeExtendPo.class, searchResultMapper);
            log.info("查询总命中数：{}", scroll.getTotalElements());

            while (scroll.hasContent()) {
                for (ESBillConsumeExtendPo dto : scroll.getContent()) {
                    result.add(dto);
                }
                //取下一页，scrollId在es服务器上可能会发生变化，需要用最新的。发起continueScroll请求会重新刷新快照保留时间
                scroll = (ScrolledPage<ESBillConsumeExtendPo>) elasticsearchTemplate.continueScroll(scroll.getScrollId(),
                        SCROLL_TIMEOUT, ESBillConsumeExtendPo.class, searchResultMapper);
            }
            //及时释放es服务器资源
            elasticsearchTemplate.clearScroll(scroll.getScrollId());
            log.info("scrollQuery end  {} ", result.size());

            if (migrateUtil.canSimulation(HotKey.billConsumeDayExtendEsMigrateSwitch_simulation, MigrateBizScene.QueryBillExtendService_scrollQuery.getName())) {
                List<ESBillConsumeExtendPo> finalResult = result;
                migrateUtil.doSimulation(MigrateBizScene.QueryBillExtendService_scrollQuery.getName(), (n) -> {
                    // 仿真查询
                    List<ESBillConsumeExtendPo> newResult = this.scrollQuery4Migrate(param);
                    // 仿真比对
                    genericComparator.compareAllPairs(
                            finalResult, newResult,
                            MigrateBizScene.QueryBillExtendService_scrollQuery.getName(),
                            ESBillConsumeExtendPo::getId,
                            ESBillConsumeExtendPo::getId,
                            null,null
                    );
                });
            }
        }

        return result;
    }

    public List<ESBillConsumeExtendPo> scrollQuery4Migrate(QueryConsumeParam param) {
        BoolQueryBuilder boolQueryBuilder = consumerQueryBuilder.getBoolQueryBuilder(param);

        List<BillConsumeDayExtendEsBean> esBeanList = billConsumeDayExtendOlapService.doBatchSearch(
                BillConsumeDayExtendQuery.builder()
                        .bizScene(MigrateBizScene.QueryBillExtendService_scrollQuery.getName())
                        .appId(MigrateBizScene.CRM_APP_ID.getName())
                        .boolQueryBuilder(boolQueryBuilder)
                        .build()
        );
        if (CollectionUtils.isEmpty(esBeanList)) {
            return java.util.Collections.emptyList();
        }

        return esBeanList.stream().map(this::convertEsBeanToPo).collect(Collectors.toList());
    }

    private ESBillConsumeExtendPo convertAggEsBeanToPo(BillConsumeDayExtendAggEsBean esBean) {
        return ESBillConsumeExtendPo.builder()
                .id(esBean.getId())
                .extendBillType(esBean.getExtendBillType())
                .billId(esBean.getBillId())
                .groupTime(esBean.getGroupTime())
                .consumeAmount(esBean.getConsumeAmount())
                .contractId(esBean.getContractId())
                .orderId(esBean.getOrderId())
                .pickupCrmOrderId(esBean.getPickupCrmOrderId())
                .orderType(esBean.getOrderType())
                .resourceType(esBean.getResourceType())
                .isChecking(esBean.getIsChecking())
                .closingDate(esBean.getClosingDate())
                .billSource(esBean.getBillSource())
                .billCloseType(esBean.getBillCloseType())
                .accountId(esBean.getAccountId())
                .agentId(esBean.getAgentId())
                .optAgentId(esBean.getOptAgentId())
                .optAgentName(esBean.getOptAgentName())
                .productType(esBean.getProductType())
                .sales(esBean.getSales())
                .directSales(esBean.getDirectSales())
                .channelSales(esBean.getChannelSales())
                .deps(esBean.getDeps())
                .isInner(esBean.getIsInner())
                .customerId(esBean.getCustomerId())
                .userType(esBean.getUserType())
                .isAgent(esBean.getIsAgent())
                .categoryFirstId(esBean.getCategoryFirstId())
                .categorySecondId(esBean.getCategorySecondId())
                .bizIndustryCategorySecondId(esBean.getBizIndustryCategorySecondId())
                .bizIndustryCategoryFirstId(esBean.getBizIndustryCategoryFirstId())
                .groupId(esBean.getGroupId())
                .accountGroupId(esBean.getAccountGroupId())
                .companyGroupId(esBean.getCompanyGroupId())
                .productLineId(esBean.getProductLineId())
                .productId(esBean.getProductId())
                .scheduleId(esBean.getScheduleId())
                .ctime(esBean.getCtime())
                .projectItemId(esBean.getProjectItemId())
                .attractInvestmentType(esBean.getAttractInvestmentType())
                .showCount(esBean.getShowCount())
                .clickCount(esBean.getClickCount())
                .isClosed(esBean.getIsClosed())
                .achieveTypes(esBean.getAchieveTypes())
                .orderNo(esBean.getOrderNo())
                .pickupCooperationType(esBean.getPickupCooperationType())
                .pickupOrderType(esBean.getPickupOrderType())
                .pickupExpectFinishTime(esBean.getPickupExpectFinishTime())
                .pickupOnlineTime(esBean.getPickupOnlineTime())
                .pickupOrderStatus(esBean.getPickupOrderStatus())
                .pickupPubTime(esBean.getPickupPubTime())
                .payDate(esBean.getPayDate())
                .orderFirstCategoryId(esBean.getOrderFirstCategoryId())
                .orderSecondCategoryId(esBean.getOrderSecondCategoryId())
                .isVerifiedMember(esBean.getIsVerifiedMember())
                .financeType(esBean.getFinanceType())
                .agentIsInner(esBean.getAgentIsInner())
                .completedTime(esBean.getCompletedTime())
                .build();
    }

    private ESBillConsumeExtendPo convertEsBeanToPo(BillConsumeDayExtendEsBean esBean) {
        return ESBillConsumeExtendPo.builder()
                .id(esBean.getId())
                .extendBillType(esBean.getExtendBillType())
                .billId(esBean.getBillId())
                .groupTime(esBean.getGroupTime())
                .consumeAmount(esBean.getConsumeAmount())
                .contractId(esBean.getContractId())
                .orderId(esBean.getOrderId())
                .pickupCrmOrderId(esBean.getPickupCrmOrderId())
                .orderType(esBean.getOrderType())
                .resourceType(esBean.getResourceType())
                .isChecking(esBean.getIsChecking())
                .closingDate(esBean.getClosingDate())
                .billSource(esBean.getBillSource())
                .billCloseType(esBean.getBillCloseType())
                .accountId(esBean.getAccountId())
                .agentId(esBean.getAgentId())
                .optAgentId(esBean.getOptAgentId())
                .optAgentName(esBean.getOptAgentName())
                .productType(esBean.getProductType())
                .sales(esBean.getSales())
                .directSales(esBean.getDirectSales())
                .channelSales(esBean.getChannelSales())
                .deps(esBean.getDeps())
                .isInner(esBean.getIsInner())
                .customerId(esBean.getCustomerId())
                .userType(esBean.getUserType())
                .isAgent(esBean.getIsAgent())
                .categoryFirstId(esBean.getCategoryFirstId())
                .categorySecondId(esBean.getCategorySecondId())
                .bizIndustryCategorySecondId(esBean.getBizIndustryCategorySecondId())
                .bizIndustryCategoryFirstId(esBean.getBizIndustryCategoryFirstId())
                .groupId(esBean.getGroupId())
                .accountGroupId(esBean.getAccountGroupId())
                .companyGroupId(esBean.getCompanyGroupId())
                .productLineId(esBean.getProductLineId())
                .productId(esBean.getProductId())
                .scheduleId(esBean.getScheduleId())
                .ctime(esBean.getCtime())
                .projectItemId(esBean.getProjectItemId())
                .attractInvestmentType(esBean.getAttractInvestmentType())
                .showCount(esBean.getShowCount())
                .clickCount(esBean.getClickCount())
                .isClosed(esBean.getIsClosed())
                .achieveTypes(esBean.getAchieveTypes())
                .orderNo(esBean.getOrderNo())
                .pickupCooperationType(esBean.getPickupCooperationType())
                .pickupOrderType(esBean.getPickupOrderType())
                .pickupExpectFinishTime(esBean.getPickupExpectFinishTime())
                .pickupOnlineTime(esBean.getPickupOnlineTime())
                .pickupOrderStatus(esBean.getPickupOrderStatus())
                .pickupPubTime(esBean.getPickupPubTime())
                .payDate(esBean.getPayDate())
                .orderFirstCategoryId(esBean.getOrderFirstCategoryId())
                .orderSecondCategoryId(esBean.getOrderSecondCategoryId())
                .isVerifiedMember(esBean.getIsVerifiedMember())
                .financeType(esBean.getFinanceType())
                .agentIsInner(esBean.getAgentIsInner())
                .completedTime(esBean.getCompletedTime())
                .build();
    }

    private final SearchResultMapper searchResultMapper = new SearchResultMapper() {
        @Override
        public <T> AggregatedPage<T> mapResults(SearchResponse response, Class<T> aClass, Pageable pageable) {
            List<ESBillConsumeExtendPo> result = new ArrayList<>();
            for (SearchHit hit : response.getHits()) {
                if (response.getHits().getHits().length <= 0) {
                    return new AggregatedPageImpl<T>(java.util.Collections.EMPTY_LIST, pageable, response.getHits().getTotalHits(), response.getScrollId());
                }
                //可以做更复杂的映射逻辑
                JSONObject jsonObject = JSONObject.parseObject(JSON.toJSONString(hit.getSourceAsMap()));

                ESBillConsumeExtendPo po = JSONObject.parseObject(jsonObject.toJSONString(), ESBillConsumeExtendPo.class);
                result.add(po);
            }
            if (result.isEmpty()) {
                return new AggregatedPageImpl<T>(java.util.Collections.EMPTY_LIST, pageable, response.getHits().getTotalHits(), response.getScrollId());
            }
            return new AggregatedPageImpl<T>((List<T>) result, pageable, response.getHits().getTotalHits(), response.getScrollId());
        }
    };
}
