package com.bilibili.crm.platform.biz.service.finance.automation.component;

import com.bilibili.crm.platform.biz.mq.message.ContractFlowReceivableNoticeInfoEvent;
import com.bilibili.crm.platform.biz.repo.finance.CrmFlowAdditionInfoRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationEventPublisher;
import org.springframework.context.ApplicationEventPublisherAware;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/6/3 3:18 下午
 */
@Slf4j
@Component
public class ContractFlowReceivableMsgSender implements ApplicationEventPublisherAware {

    @Autowired
    protected CrmFlowAdditionInfoRepo crmFlowAdditionInfoRepo;

    private ApplicationEventPublisher applicationEventPublisher;

    public void send(Integer flowId) {
        // 合约回款款项到账后的销售通知
        ContractFlowReceivableNoticeInfoEvent tradeEvent = new ContractFlowReceivableNoticeInfoEvent(this, flowId);
        this.applicationEventPublisher.publishEvent(tradeEvent);
    }

    @Override
    public void setApplicationEventPublisher(ApplicationEventPublisher applicationEventPublisher) {
        this.applicationEventPublisher = applicationEventPublisher;
    }
}
