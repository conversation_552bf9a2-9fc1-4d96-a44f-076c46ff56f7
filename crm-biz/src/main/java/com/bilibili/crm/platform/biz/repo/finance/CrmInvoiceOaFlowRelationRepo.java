package com.bilibili.crm.platform.biz.repo.finance;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.finance.dto.CrmInvoiceOaFlowRelationDto;
import com.bilibili.crm.platform.biz.dao.CrmInvoiceOaFlowRelationDao;
import com.bilibili.crm.platform.biz.po.CrmInvoiceOaFlowRelationPo;
import com.bilibili.crm.platform.biz.po.CrmInvoiceOaFlowRelationPoExample;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/4/23 3:36 下午
 */
@Repository
public class CrmInvoiceOaFlowRelationRepo {

    @Autowired
    private CrmInvoiceOaFlowRelationDao crmInvoiceOaFlowRelationDao;

    public List<CrmInvoiceOaFlowRelationPo> queryListByExample(CrmInvoiceOaFlowRelationPoExample example) {
        Assert.notNull(example, "example 不能为空！");
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationDao.selectByExample(example);
        return crmInvoiceOaFlowRelationPos;
    }

    public List<CrmInvoiceOaFlowRelationPo> queryListByRefIds(Integer invoiceBizType, List<Integer> invoiceIds) {
        Assert.isTrue(invoiceBizType != null, "开票业务类型不能为空！");

        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.EMPTY_LIST;
        }

        CrmInvoiceOaFlowRelationPoExample example = new CrmInvoiceOaFlowRelationPoExample();
        example.or().andInvoiceBizTypeEqualTo(invoiceBizType).andOaInvoiceIdIn(invoiceIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("id desc");
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationDao.selectByExample(example);
        return crmInvoiceOaFlowRelationPos;
    }

    /**
     * 获取 rechargeIds 的所有的开票的关系，按新到老排序
     *
     * @param invoiceBizTypes
     * @param relIds
     * @return
     */
    public List<CrmInvoiceOaFlowRelationPo> queryListByRefIds(List<Integer> invoiceBizTypes, List<Integer> relIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(invoiceBizTypes), "开票业务类型不能为空！");

        if (CollectionUtils.isEmpty(relIds)) {
            return Collections.EMPTY_LIST;
        }

        CrmInvoiceOaFlowRelationPoExample example = new CrmInvoiceOaFlowRelationPoExample();
        example.or().andInvoiceBizTypeIn(invoiceBizTypes).andRelIdIn(relIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("id desc");
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationDao.selectByExample(example);
        return crmInvoiceOaFlowRelationPos;
    }

    public List<CrmInvoiceOaFlowRelationPo> queryLatestListByRefIds(List<Integer> invoiceBizTypes, List<Integer> relIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(invoiceBizTypes), "开票业务类型不能为空！");

        if (CollectionUtils.isEmpty(relIds)) {
            return Collections.EMPTY_LIST;
        }

        CrmInvoiceOaFlowRelationPoExample example = new CrmInvoiceOaFlowRelationPoExample();
        example.or().andInvoiceBizTypeIn(invoiceBizTypes).andRelIdIn(relIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        example.setOrderByClause("id desc");
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationDao.selectByExample(example);

        List<CrmInvoiceOaFlowRelationPo> invoiceOaFlowRelationPos = listOperateRepeatByRelId(crmInvoiceOaFlowRelationPos);
        return invoiceOaFlowRelationPos;
    }

    public List<CrmInvoiceOaFlowRelationPo> listOperateRepeatByRelId(List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos) {
        Map<Integer, CrmInvoiceOaFlowRelationPo> invoiceOaFlowRelationPoMap =
                crmInvoiceOaFlowRelationPos.stream().collect(Collectors.toMap(t -> t.getRelId(), t -> t, (t1, t2) -> t1));
        return invoiceOaFlowRelationPoMap.values().stream().collect(Collectors.toList());
    }

    public List<CrmInvoiceOaFlowRelationPo> queryListByOaFlowIds(List<Integer> invoiceBizTypes, List<Integer> oaFlowIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(invoiceBizTypes), "开票业务类型不能为空！");
        if (CollectionUtils.isEmpty(oaFlowIds)) {
            return Collections.EMPTY_LIST;
        }

        CrmInvoiceOaFlowRelationPoExample example = new CrmInvoiceOaFlowRelationPoExample();
        example.or().andInvoiceBizTypeIn(invoiceBizTypes).andCrmOaFlowIdIn(oaFlowIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationDao.selectByExample(example);
        return crmInvoiceOaFlowRelationPos;
    }

    public Map<Integer, List<CrmInvoiceOaFlowRelationPo>> queryMapByOaFlowIds(List<Integer> invoiceBizTypes,
                                                                              List<Integer> oaFlowIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(invoiceBizTypes), "开票业务类型不能为空！");
        if (CollectionUtils.isEmpty(oaFlowIds)) {
            return Collections.EMPTY_MAP;
        }

        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = this.queryListByOaFlowIds(invoiceBizTypes, oaFlowIds);
        Map<Integer, List<CrmInvoiceOaFlowRelationPo>> integerListMap = crmInvoiceOaFlowRelationPos.stream().collect(Collectors.groupingBy(t -> t.getCrmOaFlowId()));
        return integerListMap;
    }

    public void insertInvoiceOaFlowRelation(CrmInvoiceOaFlowRelationDto crmInvoiceOaFlowRelationDto) {
        CrmInvoiceOaFlowRelationPo crmInvoiceOaFlowRelationPo = new CrmInvoiceOaFlowRelationPo();
        BeanUtils.copyProperties(crmInvoiceOaFlowRelationDto, crmInvoiceOaFlowRelationPo);
        crmInvoiceOaFlowRelationDao.insertSelective(crmInvoiceOaFlowRelationPo);
    }

    public long countByExample(CrmInvoiceOaFlowRelationPoExample crmInvoiceOaFlowRelationPoExample) {
        return crmInvoiceOaFlowRelationDao.countByExample(crmInvoiceOaFlowRelationPoExample);
    }
}
