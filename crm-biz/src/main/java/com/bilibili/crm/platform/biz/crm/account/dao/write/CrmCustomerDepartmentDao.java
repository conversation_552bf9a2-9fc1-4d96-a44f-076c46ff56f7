package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCustomerDepartmentPo;
import com.bilibili.crm.platform.biz.po.CrmCustomerDepartmentPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmCustomerDepartmentDao {
    long countByExample(CrmCustomerDepartmentPoExample example);

    int deleteByExample(CrmCustomerDepartmentPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmCustomerDepartmentPo record);

    int insertBatch(List<CrmCustomerDepartmentPo> records);

    int insertUpdateBatch(List<CrmCustomerDepartmentPo> records);

    int insert(CrmCustomerDepartmentPo record);

    int insertUpdateSelective(CrmCustomerDepartmentPo record);

    int insertSelective(CrmCustomerDepartmentPo record);

    List<CrmCustomerDepartmentPo> selectByExample(CrmCustomerDepartmentPoExample example);

    CrmCustomerDepartmentPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmCustomerDepartmentPo record, @Param("example") CrmCustomerDepartmentPoExample example);

    int updateByExample(@Param("record") CrmCustomerDepartmentPo record, @Param("example") CrmCustomerDepartmentPoExample example);

    int updateByPrimaryKeySelective(CrmCustomerDepartmentPo record);

    int updateByPrimaryKey(CrmCustomerDepartmentPo record);
}