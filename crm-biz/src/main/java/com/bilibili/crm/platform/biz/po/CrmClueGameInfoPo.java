package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmClueGameInfoPo implements Serializable {
    /**
     * id
     */
    private Long id;

    /**
     * 游戏id
     */
    private Long gameBaseId;

    /**
     * 游戏名称
     */
    private String gameName;

    /**
     * 游戏封面(图标)
     */
    private String gameIcon;

    /**
     * 游戏来源 0平台SDK 1谷歌游戏 2H5轻文游戏
     */
    private Integer source;

    /**
     * 上下架状态 0上架 1下架
     */
    private Integer onlineStatus;

    /**
     * 所属cp的企业名称
     */
    private String companyName;

    /**
     * 开放平台游戏状态
     */
    private Integer gameState;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除: 0 否 1是
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}