package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.OperatorType;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.util.Page;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.AccountBaseDto;
import com.bilibili.crm.platform.api.account.dto.AccountInvoiceRecordDto;
import com.bilibili.crm.platform.api.account.dto.NewAccountInvoiceRecordDto;
import com.bilibili.crm.platform.api.account.dto.UpdateAccountInvoiceRecordDto;
import com.bilibili.crm.platform.api.account.service.IAccountInvoiceService;
import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.api.finance.dto.NewFinanceTaskDto;
import com.bilibili.crm.platform.api.finance.dto.UpdateFinanceTaskDto;
import com.bilibili.crm.platform.api.finance.service.IFinanceService;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.biz.constant.InvoiceConstants;
import com.bilibili.crm.platform.biz.dao.AccAccountInvoiceRecordDao;
import com.bilibili.crm.platform.biz.dao.AccAccountInvoiceRecordNumberDao;
import com.bilibili.crm.platform.biz.dao.AccAccountInvoiceRecordSaleMappingDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.common.*;
import com.google.common.collect.Lists;
import org.redisson.api.RBucket;
import org.redisson.api.RLock;
import org.redisson.api.RedissonClient;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @time 2017/9/13 12:06
 */
@Component
public class AccountInvoiceService implements IAccountInvoiceService{

    private static final Logger LOGGER = LoggerFactory.getLogger(AccountInvoiceService.class);

    @Autowired
    private AccAccountInvoiceRecordDao invoiceRecordDao;
    @Autowired
    private AccAccountInvoiceRecordNumberDao invoiceRecordNumberDao;
    @Autowired
    private AccAccountInvoiceRecordSaleMappingDao invoiceRecordSaleMappingDao;
    @Autowired
    private IQueryAccountService queryAccountService;
    @Autowired
    private ISaleService saleService;
    @Autowired
    private IFinanceService financeService;
    @Autowired
    private ILogOperatorService logOperatorService;
    @Autowired
    private RedissonClient redissonClient;
    @Autowired
    private AccountInvoiceServiceDelegate accountInvoiceServiceDelegate;


    @Override
    public PageResult<AccountInvoiceRecordDto> getInvoiceListByAccountId(Integer accountId, Integer status, Integer page, Integer size) {
        Assert.notNull(accountId, "账号ID不可为空");
        Assert.notNull(page, "页码不可为空");
        Assert.notNull(size, "页长不可为空");
        if (status != null){
            FinanceAuditStatus.getByCode(status);
        }

        LOGGER.info("getInvoiceListByAccountId accountId {}, status {}, page {}, size {}", accountId, status, page, size);

        Page pageBean = Page.valueOf(page, size);

        AccAccountInvoiceRecordPoExample example = new AccAccountInvoiceRecordPoExample();
        AccAccountInvoiceRecordPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId);
        if (status != null){
            criteria.andStatusEqualTo(status);
        }
        example.setOrderByClause("mtime desc");

        long total = invoiceRecordDao.countByExample(example);

        example.setOffset(pageBean.getOffset());
        example.setLimit(pageBean.getLimit());

        List<AccAccountInvoiceRecordPo> rechargeRecordPos = invoiceRecordDao.selectByExample(example);

        return PageResult.<AccountInvoiceRecordDto>builder().records(this.getAccountInvoiceRecordDtosFromPos(rechargeRecordPos)).total((int) total).build();
    }

    @Override
    public Map<Integer, List<AccountInvoiceRecordDto>> getInvoiceRecordDtoMapInAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            Collections.emptyList();
        }
        return this.getInvoiceRecordDtos(accountIds, null)
                .stream().collect(Collectors.groupingBy(AccountInvoiceRecordDto::getAccountId));
    }

    private List<AccountInvoiceRecordDto> getInvoiceRecordDtos(List<Integer> accountIds, Integer status) {
        List<AccAccountInvoiceRecordPo> invoiceRecordPos = this.getInvoiceRecordPosInAccountIds(accountIds, status);
        if (CollectionUtils.isEmpty(invoiceRecordPos)) {
            return Collections.emptyList();
        }
        return this.getAccountInvoiceRecordDtosFromPos(invoiceRecordPos);
    }

    private List<AccountInvoiceRecordDto> getAccountInvoiceRecordDtosFromPos(List<AccAccountInvoiceRecordPo> invoiceRecordPos) {
        if (CollectionUtils.isEmpty(invoiceRecordPos)){
            return Collections.emptyList();
        }
        List<Integer> invoiceRecordIds = invoiceRecordPos.stream().map(AccAccountInvoiceRecordPo::getId).collect(Collectors.toList());
        Map<Integer, List<String>> numberMap = this.getInvoiceRecordNumbersMap(invoiceRecordIds);
        Map<Integer, List<Integer>> invoiceSaleIdsMap = this.getInvoiceRecordSaleIdsMap(invoiceRecordIds);

        Set<Integer> saleIdSet = invoiceSaleIdsMap.values().stream().reduce((a, b) -> {
            a.addAll(b);
            return a;
        }).orElse(Collections.emptyList()).stream().collect(Collectors.toSet());

        Map<Integer, SaleDto> saleDtoMap = saleService.getSaleMapInIds(saleIdSet.stream().collect(Collectors.toList()));

        Map<Integer, List<SaleDto>> invoiceSaleMap = new HashMap<>(invoiceSaleIdsMap.size());
        invoiceSaleIdsMap.forEach((k, v) -> {
            invoiceSaleMap.put(k, saleDtoMap.entrySet().stream()
                    .filter(entry -> v.stream().collect(Collectors.toSet()).contains(entry.getKey()))
                    .map(entry -> entry.getValue())
                    .collect(Collectors.toList()));
        });

        List<AccountInvoiceRecordDto> dtos = new ArrayList<>(invoiceRecordPos.size());
        invoiceRecordPos.forEach(po -> {
            AccountInvoiceRecordDto dto = new AccountInvoiceRecordDto();
            BeanUtils.copyProperties(po, dto);
            dto.setInvoiceNumbers(numberMap.get(dto.getId()));
            dto.setSaleDtos(invoiceSaleMap.get(dto.getId()));
            dtos.add(dto);
        });
        return dtos;
    }

    private Map<Integer, List<String>> getInvoiceRecordNumbersMap(List<Integer> invoiceRecordsIds) {
        AccAccountInvoiceRecordNumberPoExample example = new AccAccountInvoiceRecordNumberPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andInvoiceRecordIdIn(invoiceRecordsIds);
        List<AccAccountInvoiceRecordNumberPo> invoiceRecordNumberPos = invoiceRecordNumberDao.selectByExample(example);
        if (CollectionUtils.isEmpty(invoiceRecordNumberPos)) {
            return Collections.emptyMap();
        }
        return invoiceRecordNumberPos.stream().collect(
                Collectors.groupingBy(AccAccountInvoiceRecordNumberPo::getInvoiceRecordId,
                        Collectors.mapping(s -> s.getInvoiceNumber(), Collectors.toList())
                )
        );
    }

    private Map<Integer, List<Integer>> getInvoiceRecordSaleIdsMap(List<Integer> invoiceRecordsIds) {
        AccAccountInvoiceRecordSaleMappingPoExample example = new AccAccountInvoiceRecordSaleMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andInvoiceRecordIdIn(invoiceRecordsIds);
        List<AccAccountInvoiceRecordSaleMappingPo> pos = invoiceRecordSaleMappingDao.selectByExample(example);
        if (CollectionUtils.isEmpty(pos)) {
            return Collections.emptyMap();
        }
        return pos.stream().collect(
                Collectors.groupingBy(AccAccountInvoiceRecordSaleMappingPo::getInvoiceRecordId
                        , Collectors.mapping(AccAccountInvoiceRecordSaleMappingPo::getCrmSaleId, Collectors.toList())
                )
        );
    }

    private List<AccAccountInvoiceRecordPo> getInvoiceRecordPosInAccountIds(List<Integer> accountIds, Integer status) {
        AccAccountInvoiceRecordPoExample invoiceRecordPoExample = new AccAccountInvoiceRecordPoExample();
        AccAccountInvoiceRecordPoExample.Criteria criteria = invoiceRecordPoExample.or();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdIn(accountIds);
        if (status != null && status > 0) {
            criteria.andStatusEqualTo(status);
        }

        List<AccAccountInvoiceRecordPo> invoiceRecordPos = invoiceRecordDao.selectByExample(invoiceRecordPoExample);
        if (CollectionUtils.isEmpty(invoiceRecordPos)) {
            return Collections.emptyList();
        }
        return invoiceRecordPos;
    }

    @Override
    public List<AccountInvoiceRecordDto> getInvoiceListByAccountId(Integer accountId, Integer status) {
        Assert.notNull(accountId, "账号ID不可为空");
        Map<Integer, List<AccountInvoiceRecordDto>> invoiceRecordMap = this.getInvoiceRecordDtos(Lists.newArrayList
                (accountId), status).stream().collect(Collectors.groupingBy(AccountInvoiceRecordDto::getAccountId));
        if (CollectionUtils.isEmpty(invoiceRecordMap)){
            return Collections.emptyList();
        }
        return CollectionUtils.isEmpty(invoiceRecordMap.get(accountId)) ? Collections.emptyList():invoiceRecordMap.get(accountId);
    }

    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class, timeout = 30)
    @Override
    public Integer createInvoiceRecord(NewAccountInvoiceRecordDto newAccountInvoiceRecordDto, Operator operator) {

        this.validateCreateParam(newAccountInvoiceRecordDto, operator);

        Integer accountId = newAccountInvoiceRecordDto.getAccountId();
        AccountBaseDto accountBase = queryAccountService.getAccountBaseDtoById(accountId);
        Assert.notNull(accountBase, InvoiceConstants.ACCOUNT_NOT_EXIST);

        AccAccountInvoiceRecordPo po = this.addInvoiceRecord(newAccountInvoiceRecordDto, accountBase.getDependencyAgentId());

        accountInvoiceServiceDelegate.insertInvoiceSaleRecord(newAccountInvoiceRecordDto.getSaleIds(), po.getId());

        this.addFinanceTask(newAccountInvoiceRecordDto, operator, accountBase, po);

        accountInvoiceServiceDelegate.insertLog(operator, newAccountInvoiceRecordDto, po.getId(), ModifyType.CREATE_ACCOUNT_INVOICE_RECORD);

        return po.getId();
    }

    private void addFinanceTask(
            NewAccountInvoiceRecordDto newAccountInvoiceRecordDto,
            Operator operator, AccountBaseDto accountBase, AccAccountInvoiceRecordPo po) {
        FinanceTaskType taskType = newAccountInvoiceRecordDto.getSource().getFinanceTaskType();
        NewFinanceTaskDto newFinanceTaskDto = NewFinanceTaskDto.builder()
                .entityId(newAccountInvoiceRecordDto.getAccountId())
                .applicationId(po.getId())
                .taskName(accountBase.getUsername().concat("-").concat(taskType.getDesc()))
                .type(taskType.getCode())
                .status(AccountInvoiceAuditStatus.TO_BE_AUDIT.getCode())
                .build();
        financeService.createFinanceTask(operator, newFinanceTaskDto);
    }

    private AccAccountInvoiceRecordPo addInvoiceRecord(
            NewAccountInvoiceRecordDto newAccountInvoiceRecordDto, Integer agentId) {
        AccAccountInvoiceRecordPo po = new AccAccountInvoiceRecordPo();
        BeanUtils.copyProperties(newAccountInvoiceRecordDto, po);
        po.setSource(newAccountInvoiceRecordDto.getSource().getCode());
        po.setStatus(AccountInvoiceAuditStatus.TO_BE_AUDIT.getCode());
        po.setAgentId(agentId);

        invoiceRecordDao.insertSelective(po);
        return po;
    }

    private void validateCreateParam(NewAccountInvoiceRecordDto newInvoiceRecordDto, Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "operator can not be null");
        Assert.notNull(newInvoiceRecordDto);
        Assert.hasText(newInvoiceRecordDto.getAddress());
        Assert.notNull(newInvoiceRecordDto.getAccountId());
        Assert.notNull(newInvoiceRecordDto.getType());
        Assert.notNull(newInvoiceRecordDto.getAmount());
        Assert.notNull(newInvoiceRecordDto.getBank());
        Assert.notNull(newInvoiceRecordDto.getMobile());
        Assert.notNull(newInvoiceRecordDto.getTaxNumber());
        Assert.notNull(newInvoiceRecordDto.getCompany());

        InvoiceRecordType.getByCode(newInvoiceRecordDto.getType());

        Assert.notNull(newInvoiceRecordDto.getSource(), "充值来源不可为空");
        Assert.isTrue(newInvoiceRecordDto.getSource().isValidHandleSales(newInvoiceRecordDto.getSaleIds()), "经手销售不可为空");

    }

    @Override
    public void updateInvoiceRecord(UpdateAccountInvoiceRecordDto updateAccountInvoiceRecordDto, Operator operator) {
        accountInvoiceServiceDelegate.validateUpdateParam(updateAccountInvoiceRecordDto, operator);

        if (!OperatorType.AGENT.equals(operator.getOperatorType())){
            Assert.isTrue(RechargeSource.BILIBILI_SALE.isValidHandleSales(updateAccountInvoiceRecordDto.getSaleIds()), "经手销售不可为空");
        }

        Integer invoiceId = updateAccountInvoiceRecordDto.getId();
        RLock lock = this.getLock(invoiceId, CrmConstant.ACCOUNT_INVOICE_LOCK_SUFFIX);

        try {
            accountInvoiceServiceDelegate.updateInvoiceRecord(updateAccountInvoiceRecordDto, operator);
        } finally {
            LOGGER.info(System.currentTimeMillis() + "-account invoice update--unLock----");
            lock.unlock();
        }
    }

    @Override
    public void forbidden(Integer invoiceRecordId, String deleteRemark, Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "operator can not be null");
        Assert.notNull(invoiceRecordId);
        Assert.hasText(deleteRemark);

        RLock lock = this.getLock(invoiceRecordId, CrmConstant.ACCOUNT_INVOICE_LOCK_SUFFIX);
        try {
            accountInvoiceServiceDelegate.forbidden(invoiceRecordId, deleteRemark, operator);
        } finally {
            lock.unlock();
            LOGGER.info("account invoice forbidden unlock");
        }
    }

    @Override
    public void reject(Integer invoiceRecordId, String reason, Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "operator can not be null");
        Assert.notNull(invoiceRecordId);
        Assert.hasText(reason);

        RLock lock = this.getLock(invoiceRecordId, CrmConstant.ACCOUNT_INVOICE_LOCK_SUFFIX);

        try {
            accountInvoiceServiceDelegate.reject(invoiceRecordId, reason, operator);
        } finally {
            LOGGER.info(System.currentTimeMillis() + "-account invoice reject--unLock----");
            lock.unlock();
        }
    }

    @Override
    public void pass(Integer invoiceRecordId, List<String> invoiceNumbers, Long billingDate, Operator operator) {
        Assert.isTrue(!Operator.validateParamIsNull(operator), "operator can not be null");
        Assert.notNull(invoiceRecordId);
        Assert.notNull(billingDate);
        Assert.notEmpty(invoiceNumbers);

        RLock lock = this.getLock(invoiceRecordId, CrmConstant.ACCOUNT_INVOICE_LOCK_SUFFIX);
        try {
            accountInvoiceServiceDelegate.pass(invoiceRecordId, invoiceNumbers, billingDate, operator);
        } finally {
            LOGGER.info(System.currentTimeMillis() + "-account invoice pass--unLock----");
            lock.unlock();
        }
    }

    @Override
    public AccountInvoiceRecordDto load(Integer invoiceId) {
        AccAccountInvoiceRecordPo recordPo = invoiceRecordDao.selectByPrimaryKey(invoiceId);
        Assert.notNull(recordPo, InvoiceConstants.INVOICE_NOT_EXIST);

        return this.getAccountInvoiceRecordDto(recordPo);
    }

    private AccountInvoiceRecordDto getAccountInvoiceRecordDto(AccAccountInvoiceRecordPo recordPo) {
        AccountInvoiceRecordDto recordDto = AccountInvoiceRecordDto.builder().build();
        BeanUtils.copyProperties(recordPo, recordDto);

        setInvoiceNUmbers(recordPo.getId(), recordDto);

        setInvoiceSaleDtos(recordPo.getId(), recordDto);
        return recordDto;
    }

    private void setInvoiceSaleDtos(Integer invoiceId, AccountInvoiceRecordDto recordDto) {
        AccAccountInvoiceRecordSaleMappingPoExample mappingPoExample = new AccAccountInvoiceRecordSaleMappingPoExample();
        mappingPoExample.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andInvoiceRecordIdEqualTo(invoiceId);
        List<AccAccountInvoiceRecordSaleMappingPo> mappingPos = invoiceRecordSaleMappingDao.selectByExample(mappingPoExample);
        List<Integer> saleIds = mappingPos.stream().map(AccAccountInvoiceRecordSaleMappingPo::getCrmSaleId).collect(Collectors.toList());

        recordDto.setSaleDtos(saleService.getSalesInIds(saleIds));
    }

    private void setInvoiceNUmbers(Integer invoiceId, AccountInvoiceRecordDto recordDto) {
        AccAccountInvoiceRecordNumberPoExample example = new AccAccountInvoiceRecordNumberPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andInvoiceRecordIdEqualTo(invoiceId);
        List<AccAccountInvoiceRecordNumberPo> recordNumberPos = invoiceRecordNumberDao.selectByExample(example);
        recordDto.setInvoiceNumbers(recordNumberPos.stream().map(s -> s.getInvoiceNumber()).collect(Collectors.toList()));
    }


    private RLock getLock(Integer id, String lockSuffix) {
        RLock lock = redissonClient.getLock(id + lockSuffix);
        boolean isSuccess = true;
        try {
            isSuccess = lock.tryLock(5, 5, TimeUnit.SECONDS);
            LOGGER.info(System.currentTimeMillis() + "isSuccess: "+isSuccess+"---getLock----" + Thread.currentThread().getName());
        } catch (Exception e) {
            LOGGER.error("rLock.tryLock.e", e);
            isSuccess = false;
            LOGGER.info(System.currentTimeMillis() + "---getLock false----"+ Thread.currentThread().getName());
        }
        Assert.isTrue(isSuccess, "资源正在被其他人编辑，请稍后重试");
        return lock;
    }
}
