package com.bilibili.crm.platform.biz.repo.contract;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.biz.new_platform.dao.ContractBillCheckingMappingDao;
import com.bilibili.crm.platform.biz.new_platform.po.ContractBillCheckingMappingPo;
import com.bilibili.crm.platform.biz.new_platform.po.ContractBillCheckingMappingPoExample;
import org.springframework.stereotype.Repository;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-06-06 20:51:00
 * @description:
 **/

@Repository
public class ContractBillCheckRepo {

    @Resource
    private ContractBillCheckingMappingDao contractBillCheckingMappingDao;

    public List<ContractBillCheckingMappingPo> queryByCheckIds(List<Integer> checkIds) {
        if (CollectionUtils.isEmpty(checkIds)) {
            return new ArrayList<>();
        }
        List<Long> longCheckingIds = checkIds.stream().map(Long::valueOf).collect(Collectors.toList());
        ContractBillCheckingMappingPoExample example = new ContractBillCheckingMappingPoExample();
        ContractBillCheckingMappingPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andCheckingIdIn(longCheckingIds);
        return contractBillCheckingMappingDao.selectByExample(example);
    }

    public List<ContractBillCheckingMappingPo> queryByPeriod(Long periodId) {
        ContractBillCheckingMappingPoExample example = new ContractBillCheckingMappingPoExample();
        ContractBillCheckingMappingPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andContractBillIdEqualTo(periodId);
        return contractBillCheckingMappingDao.selectByExample(example);
    }

//    @Transactional(value = "crmPlatformNewPlatformTransactionManager", rollbackFor = Exception.class)
    public void insert(Map<Long, List<Integer>> moneyMap) {
        for (Long key : moneyMap.keySet()) {
            List<Integer> checkIds = moneyMap.getOrDefault(key, new ArrayList<>());
            for (Integer checkId : checkIds) {
                ContractBillCheckingMappingPoExample example = new ContractBillCheckingMappingPoExample();
                ContractBillCheckingMappingPoExample.Criteria criteria = example.createCriteria();
                criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                criteria.andCheckingIdEqualTo(checkId.longValue());
                List<ContractBillCheckingMappingPo> mappingPos = contractBillCheckingMappingDao.selectByExample(example);
                if (CollectionUtils.isEmpty(mappingPos)) {
                    contractBillCheckingMappingDao.insertSelective(ContractBillCheckingMappingPo.builder()
                            .checkingId(checkId.longValue())
                            .contractBillId(key)
                            .ctime(new Timestamp(System.currentTimeMillis()))
                            .mtime(new Timestamp(System.currentTimeMillis()))
                            .isDeleted(IsDeleted.VALID.getCode())
                            .build());
                }
            }
        }
    }
}
