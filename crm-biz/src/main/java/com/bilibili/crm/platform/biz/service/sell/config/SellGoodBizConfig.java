package com.bilibili.crm.platform.biz.service.sell.config;

import lombok.Getter;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-03-14 22:18:32
 * @description:
 **/

@Getter
@Component
public class SellGoodBizConfig {

    /**
     * 起飞推广一级行业
     */
    @Value("${commerce.category.id.fly.first:1760}")
    private Integer flyCategoryFirstId;

    /**
     * 起飞推广二级行业
     */
    @Value("${commerce.category.id.fly.second:1761}")
    private Integer flyCategorySecondId;

    /**
     * 起飞账号的所属部门
     */
    @Value("${sell.good.account.departmentId:5}")
    private Integer departmentId;

    /**
     * 起飞账号的账期
     */
    @Value("${sell.good.account.payment.period:90}")
    private Integer paymentPeriod;

    /**
     * 所属代理商
     */
    @Value("${sell.good.account.agent.id:23}")
    private Integer agentId;

    @Value("${united.industry.id.sell.first:1126}")
    private Integer unitedFirstIndustryId;

    @Value("${united.industry.id.sell.second:1127}")
    private Integer unitedSecondIndustryId;

    @Value("${united.industry.id.sell.third:1129}")
    private Integer unitedThirdIndustryId;
}
