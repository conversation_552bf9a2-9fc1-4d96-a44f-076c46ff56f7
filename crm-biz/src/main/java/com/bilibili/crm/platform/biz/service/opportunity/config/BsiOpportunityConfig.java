package com.bilibili.crm.platform.biz.service.opportunity.config;

import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2024/12/18
 */
@Component
public class BsiOpportunityConfig {

    public String SALE_BSI_UPDATE_MESSAGE = "你组内%s负责的%s项目的%s商机发生了商机信息更新,但不涉及到金额调整/项目调整/商机进度倒退, 系统已自动通过审核, 详情可前往" +
            "【CRM系统-商机管理】确认%s";

    public String SALE_BSI_CORE_UPDATE_MESSAGE = "你组内%s负责的%s项目的%s商机发生了%s, 详情可前往" +
            "【CRM系统-商机管理】确认%s";

    public String POC_BIS_UPDATE_MESSAGE = "你负责的%s(%s) 项目的%s(%s) 商机发生了信息更新，请前往【招商系统-商机管理】确认%s";

    public String BSI_UPDATE_ERROR_MESSAGE = "系统识别到本次商机修改涉及到金额倒退（或换项目、季度间商机金额调整），请先单独针对金额、项目修改提交审批，审批通过后再将商机跟进阶段流转为100%。如有疑问可咨询PM：皮妞宝";

    @Value("${bsi.approve:luxinxin-皮妞宝}")
    public String approve;

    @Value("${bsi.carbon.copy:zhuguangwen-思谱}")
    public String carbonCopy;

    @Value("${bsi.detail.url:http://uat-cm-mng.bilibili.co/crm/?#/opportunity.manage/brandlist/view/%s}")
    public String bsiDetailUrl;

    @Value("${merchants.bsi.detail.url:https://uat-cm-mng.bilibili.co/poc#/opportunity}")
    public String merchantsBsiDetailUrl;
}
