package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCustomerExtPo;
import com.bilibili.crm.platform.biz.po.CrmCustomerExtPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmCustomerExtDao {
    long countByExample(CrmCustomerExtPoExample example);

    int deleteByExample(CrmCustomerExtPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmCustomerExtPo record);

    int insertBatch(List<CrmCustomerExtPo> records);

    int insertUpdateBatch(List<CrmCustomerExtPo> records);

    int insert(CrmCustomerExtPo record);

    int insertUpdateSelective(CrmCustomerExtPo record);

    int insertSelective(CrmCustomerExtPo record);

    List<CrmCustomerExtPo> selectByExampleWithBLOBs(CrmCustomerExtPoExample example);

    List<CrmCustomerExtPo> selectByExample(CrmCustomerExtPoExample example);

    CrmCustomerExtPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmCustomerExtPo record, @Param("example") CrmCustomerExtPoExample example);

    int updateByExampleWithBLOBs(@Param("record") CrmCustomerExtPo record, @Param("example") CrmCustomerExtPoExample example);

    int updateByExample(@Param("record") CrmCustomerExtPo record, @Param("example") CrmCustomerExtPoExample example);

    int updateByPrimaryKeySelective(CrmCustomerExtPo record);

    int updateByPrimaryKeyWithBLOBs(CrmCustomerExtPo record);

    int updateByPrimaryKey(CrmCustomerExtPo record);
}