package com.bilibili.crm.platform.biz.service.wallet.trade;

import com.bilibili.crm.platform.api.wallet.dto.AccountWalletTradeDto;
import com.bilibili.crm.platform.api.wallet.dto.TradeRes;
import com.bilibili.crm.platform.biz.po.AccAccountWalletPo;
import com.bilibili.crm.platform.common.wallet.WalletTradeAction;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;

/**
 * @author: brady
 * @time: 2021/8/31 8:23 下午
 */
@Slf4j
@Service
public class PayArrearTradeService extends AbstractAccountWalletTradeService {
    @Override
    public WalletTradeAction tradeRoute() {
        return WalletTradeAction.PAY_ARREAR;
    }

    @Override
    protected void validateParams(AccountWalletTradeDto tradeDto) {

    }

    @Override
    public AccAccountWalletPo buildWalletChange(AccAccountWalletPo originWalletPo, AccountWalletTradeDto tradeDto) {
        Long arrearAmount = tradeDto.getAmount();
        // 按优先级分摊欠费
        Long cash = arrearAmount, redPacket = 0L, specialRedPacket = 0L;
        Long margin = arrearAmount - originWalletPo.getCash();
        if (margin > 0) {
            cash = originWalletPo.getCash();
            redPacket = margin;
            margin = margin - originWalletPo.getRedPacket();
            if (margin > 0) {
                redPacket = originWalletPo.getRedPacket();
                specialRedPacket = margin;
                margin = margin - originWalletPo.getSpecialRedPacket();
                if (margin > 0) {
                    specialRedPacket = originWalletPo.getSpecialRedPacket();
                }
            }
        }
        log.info("AccountWalletService.payArrear allocate, accountId:{}, arrearAmount:{}, cash:{}, redPacket:{}, specialRedPacket:{}",
                tradeDto.getAccountId(), arrearAmount, cash, redPacket, specialRedPacket);

        AccAccountWalletPo record = new AccAccountWalletPo();
        record.setAccountId(tradeDto.getAccountId());
        record.setCash(originWalletPo.getCash() - cash);
        record.setRedPacket(originWalletPo.getRedPacket() - redPacket);
        record.setSpecialRedPacket(originWalletPo.getSpecialRedPacket() - specialRedPacket);
        record.setVersion(originWalletPo.getVersion() + 1);

        tradeDto.setCash(cash);
        tradeDto.setRedPacket(redPacket);
        tradeDto.setSpecialRedPacket(specialRedPacket);

        return record;
    }

    @Override
    public TradeRes buildTradeRes(AccAccountWalletPo record, AccAccountWalletPo originWalletPo, Long walletLogId) {
        //实际欠款金额
        Long actualAmount = originWalletPo.getCash() + originWalletPo.getRedPacket() + originWalletPo.getSpecialRedPacket()
                - record.getCash() - record.getRedPacket() - record.getSpecialRedPacket();
        return TradeRes.builder().actualAmount(actualAmount).walletLogId(walletLogId).build();
    }

}
