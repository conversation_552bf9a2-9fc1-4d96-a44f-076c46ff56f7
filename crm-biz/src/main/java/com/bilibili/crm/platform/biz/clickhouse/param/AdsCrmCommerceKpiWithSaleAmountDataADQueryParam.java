package com.bilibili.crm.platform.biz.clickhouse.param;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;

/**
 * 业绩宽表含有销售的查询请求体
 *
 * <AUTHOR>
 * date 2024/5/23 15:25.
 * Contact: <EMAIL>.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdsCrmCommerceKpiWithSaleAmountDataADQueryParam {
    @NotNull
    private String logDate;
    @NotNull
    private String busAccountDateBegin;
    @NotNull
    private String busAccountDateEnd;
}

