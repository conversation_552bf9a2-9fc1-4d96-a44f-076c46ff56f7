package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmPeriodicityProjectConfigPo implements Serializable {
    /**
     * 主键ID
     */
    private Long id;

    /**
     * 周期性项目周期类型:枚举=年度/季度
     */
    private String periodicityType;

    /**
     * 周期性项目类型名称
     */
    private String projectName;

    /**
     * 周期性项目状态:0是有效，1是无效
     */
    private Integer projectStatus;

    /**
     * 适用周期开始日期
     */
    private Timestamp periodicityBeginTime;

    /**
     * 适用周期结束日期
     */
    private Timestamp periodicityEndTime;

    /**
     * 周期性项目类型备注
     */
    private String note;

    /**
     * 软删除，0是有效，1是删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 9065946027331458062L;
}