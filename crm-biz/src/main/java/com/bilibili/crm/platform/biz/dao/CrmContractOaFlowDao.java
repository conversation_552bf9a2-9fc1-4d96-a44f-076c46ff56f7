package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmContractOaFlowPo;
import com.bilibili.crm.platform.biz.po.CrmContractOaFlowPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmContractOaFlowDao {
    long countByExample(CrmContractOaFlowPoExample example);

    int deleteByExample(CrmContractOaFlowPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CrmContractOaFlowPo record);

    int insertBatch(List<CrmContractOaFlowPo> records);

    int insertUpdateBatch(List<CrmContractOaFlowPo> records);

    int insert(CrmContractOaFlowPo record);

    int insertUpdateSelective(CrmContractOaFlowPo record);

    int insertSelective(CrmContractOaFlowPo record);

    List<CrmContractOaFlowPo> selectByExample(CrmContractOaFlowPoExample example);

    CrmContractOaFlowPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmContractOaFlowPo record, @Param("example") CrmContractOaFlowPoExample example);

    int updateByExample(@Param("record") CrmContractOaFlowPo record, @Param("example") CrmContractOaFlowPoExample example);

    int updateByPrimaryKeySelective(CrmContractOaFlowPo record);

    int updateByPrimaryKey(CrmContractOaFlowPo record);
}