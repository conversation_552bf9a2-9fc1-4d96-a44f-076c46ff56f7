package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPo;
import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AccAccountLabelMappingDao {
    long countByExample(AccAccountLabelMappingPoExample example);

    int deleteByExample(AccAccountLabelMappingPoExample example);

    int deleteByExampleLimit(AccAccountLabelMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AccAccountLabelMappingPo record);

    int insertBatch(List<AccAccountLabelMappingPo> records);

    int insertUpdateBatch(List<AccAccountLabelMappingPo> records);

    int insert(AccAccountLabelMappingPo record);

    int insertUpdateSelective(AccAccountLabelMappingPo record);

    int insertSelective(AccAccountLabelMappingPo record);

    List<AccAccountLabelMappingPo> selectByExample(AccAccountLabelMappingPoExample example);

    AccAccountLabelMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccAccountLabelMappingPo record, @Param("example") AccAccountLabelMappingPoExample example);

    int updateByExample(@Param("record") AccAccountLabelMappingPo record, @Param("example") AccAccountLabelMappingPoExample example);

    int updateByPrimaryKeySelective(AccAccountLabelMappingPo record);

    int updateByPrimaryKey(AccAccountLabelMappingPo record);
}