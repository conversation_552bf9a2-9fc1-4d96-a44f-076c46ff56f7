package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.AccAccountExtendPo;
import com.bilibili.crm.platform.biz.po.AccAccountExtendPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface AccAccountExtendDao {
    long countByExample(AccAccountExtendPoExample example);

    int deleteByExample(AccAccountExtendPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(AccAccountExtendPo record);

    int insertSelective(AccAccountExtendPo record);

    List<AccAccountExtendPo> selectByExample(AccAccountExtendPoExample example);

    AccAccountExtendPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccAccountExtendPo record, @Param("example") AccAccountExtendPoExample example);

    int updateByExample(@Param("record") AccAccountExtendPo record, @Param("example") AccAccountExtendPoExample example);

    int updateByPrimaryKeySelective(AccAccountExtendPo record);

    int updateByPrimaryKey(AccAccountExtendPo record);
}