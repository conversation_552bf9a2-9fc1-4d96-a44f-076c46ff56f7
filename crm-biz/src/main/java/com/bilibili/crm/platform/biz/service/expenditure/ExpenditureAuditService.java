package com.bilibili.crm.platform.biz.service.expenditure;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.ObjectUtils;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.cost_management.dto.BTPPurchaseBatchDto;
import com.bilibili.crm.platform.api.cost_management.service.IBTPIntegrationService;
import com.bilibili.crm.platform.api.cost_management.service.ICostManagementDataStrategyService;
import com.bilibili.crm.platform.api.expenditure.dto.*;
import com.bilibili.crm.platform.api.expenditure.service.IExpenditureAuditService;
import com.bilibili.crm.platform.api.expenditure.service.IExpenditureService;
import com.bilibili.crm.platform.api.oa.service.IOaService;
import com.bilibili.crm.platform.api.order.dto.CrmOrderExtraDto;
import com.bilibili.crm.platform.api.order.dto.OrderBaseDto;
import com.bilibili.crm.platform.api.order.dto.QueryOrderDto;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.biz.dao.CrmExpenditureAuidtDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmExpenditureDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmExpenditureExtDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.po.expenditure.CrmExpenditurePo;
import com.bilibili.crm.platform.biz.repo.CrmOrderExtraRepo;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.*;
import com.google.common.collect.Lists;
import com.google.common.collect.Sets;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

@Slf4j
@Service
public class ExpenditureAuditService implements IExpenditureAuditService {
    @Autowired
    private CrmExpenditureAuidtDao crmExpenditureAuidtDao;
    @Autowired
    private IExpenditureService expenditureService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private CrmOrderExtraRepo crmOrderExtraRepo;
    @Autowired
    private IOaService iOaService;
    @Autowired
    private ICostManagementDataStrategyService iCostManagementDataStrategyService;
    @Autowired
    private CrmExpenditureExtDao crmExpenditureExtDao;
    @Autowired
    private CrmExpenditureDao crmExpenditureDao;
    @Autowired
    private IBTPIntegrationService ibtpIntegrationService;

    /**
     * 查询合同支出项待审核列表
     */
    @Override
    public PageResult<ExpenditureAuditInfoDto> queryExpenditureAuditInfoList(QueryExpenditureParam param, Integer page, Integer size) {

        PageResult<ExpenditureDto> expenditureDtoPageResult = expenditureService.queryExpenditureByParamPageResult(param, page, size);

        List<ExpenditureDto> records = expenditureDtoPageResult.getRecords();
        List<ExpenditureAuditInfoDto> dtos = buildFromExpenditure(records);
        return PageResult.<ExpenditureAuditInfoDto>builder().records(dtos).total(expenditureDtoPageResult.getTotal()).build();
    }

    @Override
    public List<ExpenditureAuditInfoDto> queryExpenditureAuditInfoList(QueryExpenditureParam param) {
        List<ExpenditureDto> expenditureDtos = expenditureService.queryExpenditureByParam(param);
        List<ExpenditureAuditInfoDto> dtos = buildFromExpenditure(expenditureDtos);
        return dtos;
    }


    private List<ExpenditureAuditInfoDto> buildFromExpenditure(List<ExpenditureDto> expenditureDtos) {
        List<ExpenditureAuditInfoDto> expenditureInfos = Lists.newArrayList();
        expenditureDtos.forEach(dto -> expenditureInfos.add(ExpenditureAuditInfoDto.builder()
                .id(dto.getId())
                .ctime(dto.getCtime())
                .contractId(dto.getCrmContractId())
                .name(dto.getName())
                .expenditureType(dto.getType())
                .expenditureSecondType(dto.getSecondType())
                .departmentId(dto.getDepartmentId())
                .price(dto.getPrice())
                .status(dto.getStatus())
                .orderId(dto.getCrmOrderId())
                .operator(dto.getOperator())
                .beginTime(dto.getBeginTime() != null ? new Timestamp(dto.getBeginTime()) : null)
                .endTime(dto.getEndTime() != null ? new Timestamp(dto.getEndTime()) : null)
                .payFlag(dto.getPayFlag())
                .adCostFlag(dto.getAdCostFlag())
                .remark(dto.getRemark())
                .build()));
        return expenditureInfos;
    }

    @Override
    public void expenditureInvalidSchedule() {
        //状态为编辑中的支出项
        List<ExpenditureDto> expenditureDtos = expenditureService.queryExpenditureByParam(QueryExpenditureParam.builder()
                .status(ExpenditureAuditStatus.EDIT.getCode()).build());
        if (CollectionUtils.isEmpty(expenditureDtos)) {
            return;
        }
        List<Integer> orderIds = expenditureDtos.stream().map(ExpenditureDto::getCrmOrderId).filter(Objects::nonNull).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            return;
        }
        //支出-订单,一笔支出对应一笔订单或者无关联订单，一笔订单对应多笔支出,
        Map<Integer, Integer> expenditureOrderMap = expenditureDtos.stream().collect(Collectors.toMap(ExpenditureDto::getId, ExpenditureDto::getCrmOrderId));

        //查询关联订单
        List<OrderBaseDto> baseOrderDtos = orderService.getBaseOrderDtos(QueryOrderDto.builder()
                .statuses(Arrays.asList(CrmOrderStatus.COMPLETED.getCode()))
                .orderIds(orderIds)
                .build());
        // List<OrderStatusRecordDto> orderStatusRecordDtos = orderService.queryOrderStatusRecord(QueryOrderStatusRecordParam.builder().orderIds(orderIds).statues(Arrays.asList(CrmOrderStatus.COMPLETED.getCode())).build());

        if (CollectionUtils.isEmpty(baseOrderDtos)) {
            return;
        }
        //关联订单状态=已完成，检查订单完成日期，若为上个月及以前，则支出项作废
        Timestamp firstMonthDayOfTime = CrmUtils.getFirstMonthDayOfTime(new Timestamp(System.currentTimeMillis()));
        List<Integer> invalidOrderIds = baseOrderDtos.stream().filter(dto -> null != dto.getEndTime())
                .filter(dto -> dto.getEndTime().before(firstMonthDayOfTime)).map(OrderBaseDto::getId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(invalidOrderIds)) {
            return;
        }
        List<Integer> invalidExpenditureIds = expenditureOrderMap.entrySet().stream()
                .filter(entry -> invalidOrderIds.contains(entry.getValue())).map(Map.Entry::getKey).collect(Collectors.toList());
        //批量更新支出表
        List<ExpenditureDto> invalidDtos = expenditureDtos.stream().filter(dto -> invalidExpenditureIds.contains(dto.getId())).collect(Collectors.toList());

        invalidExpenditureStatus(invalidDtos);
        insertInvalidExpenditureAudit(invalidDtos, "OrderOverTime_Invalid");
    }

    //订单关闭，关联支出项作废
    @Override
    public void invalidExpenditureFromOrder(Integer orderId) {
        if (!Utils.isPositive(orderId)) {
            return;
        }
        //查询支出表
        List<ExpenditureDto> dtosFromOrder = expenditureService.queryExpenditureByParam(QueryExpenditureParam.builder()
                .orderIds(Collections.singletonList(orderId)).build());
        if (CollectionUtils.isEmpty(dtosFromOrder)) {
            return;
        }
        //更新支出表状态
        invalidExpenditureStatus(dtosFromOrder);
        //更新审核表状态
        insertInvalidExpenditureAudit(dtosFromOrder, "OrderClose_Invalid");
        //虚拟金订单金额设置为0
        expenditureService.updateVirtualGoldOrderAmount(orderId, 0L);
    }

    //合同废弃，关联支出项作废
    @Override
    public void invalidExpenditureFromContract(Integer contractId) {
        if (!Utils.isPositive(contractId)) {
            return;
        }
        //查询支出表
        List<ExpenditureDto> dtosFromContract = expenditureService.queryExpenditureByParam(QueryExpenditureParam.builder()
                .contractIds(Collections.singletonList(contractId)).build());
        if (CollectionUtils.isEmpty(dtosFromContract)) {
            return;
        }
        //更新状态
        invalidExpenditureStatus(dtosFromContract);
        insertInvalidExpenditureAudit(dtosFromContract, "ContractDisabled_Invalid");
    }


    private void insertInvalidExpenditureAudit(List<ExpenditureDto> invalidDtos, String invalidReason) {
        invalidDtos.forEach(dto -> {
            CrmExpenditureAuidtPo po = CrmExpenditureAuidtPo.builder()
                    .expenditureId(dto.getId())
                    .auditStatus(ExpenditureAuditStatus.INVALID.getCode())
                    .auditor(Operator.SYSTEM.getOperatorName())
                    .reason(invalidReason).build();
            crmExpenditureAuidtDao.insertSelective(po);
        });
    }

    private void invalidExpenditureStatus(List<ExpenditureDto> invalidDtos) {
        invalidDtos.forEach(dto -> {
            dto.setStatus(ExpenditureAuditStatus.INVALID.getCode());
            dto.setExpenditureStatus(ExpenditureStatus.ABORT.getCode());
            dto.setAuditStatus(NewExpenditureAuditStatus.ABORT.getCode());
            expenditureService.updateExpenditureAuditStatus(dto);
        });
    }

    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    @Override
    public void updAuditStatus(ExpenditureAuditDto dto) {

        //查询支出项信息
        ExpenditureDto expenditureDto = expenditureService.getByExpenditureId(dto.getId());
        //Assert.notNull(expenditureDto,"无该支出项信息");
        //校验
//        if(expenditureAuditHelper.auditValidate(expenditureDto.getStatus(),dto)){
//            return;
//        }

        //根据原审核状态和操作类型，得到目标审核状态
        Integer toAuditStatus = ExpenditureAuditOperateType.getByCode(dto.getOperateType()).getToAuditStatus(expenditureDto.getStatus());
        expenditureDto.setStatus(toAuditStatus);
        dto.setToAuditStatus(toAuditStatus);

        //支出表更新审核状态
        expenditureService.updateExpenditureAuditStatus(expenditureDto);
        //审核表插入操作记录
        CrmExpenditureAuidtPo po = this.convertExpenditureAuditDto2Po(dto);
        crmExpenditureAuidtDao.insertSelective(po);
    }

    @Override
    public void repairBtpExpenditure(List<Integer> expenditureIds) {
        if (CollectionUtils.isEmpty(expenditureIds)) {
            return;
        }
        log.info("repairBtpExpenditure ids:{}", expenditureIds);
        Set<String> btpBatchCodes = Sets.newHashSet();
        expenditureIds.forEach(expenditureId -> {
            CrmExpenditurePo expenditureDto = crmExpenditureDao.selectByPrimaryKey(expenditureId);
            btpBatchCodes.add(expenditureDto.getBtpBatchCode());
        });
        btpBatchCodes.forEach(btpBatchCode -> {
            BTPPurchaseBatchDto btpPurchaseBatchDto = ibtpIntegrationService.
                    getBTPPurchaseBatchByCodeAndVersion(null, btpBatchCode, null)
                    .get(0);
            ibtpIntegrationService.repairBTPPurchaseBatch(btpPurchaseBatchDto);
        });
    }

    private CrmExpenditureAuidtPo convertExpenditureAuditDto2Po(ExpenditureAuditDto dto) {
        return CrmExpenditureAuidtPo.builder().auditStatus(dto.getToAuditStatus())
                .expenditureId(dto.getId())
                .auditor(dto.getOperator())
                .reason(dto.getRejectReason())
                .build();
    }

    @Override
    public List<Integer> getExpenditureIds(QueryExpenditureAuditParam param) {
        CrmExpenditureAuidtPoExample example = createExampleFromQueryDto(param);
        List<CrmExpenditureAuidtPo> pos = crmExpenditureAuidtDao.selectByExample(example);
        return pos.stream().map(CrmExpenditureAuidtPo::getExpenditureId).distinct().collect(Collectors.toList());
    }

    @Override
    public Map<Integer, Timestamp> auditPassTimeMap(List<Integer> expenditureIds) {
        if (CollectionUtils.isEmpty(expenditureIds)) {
            return Collections.emptyMap();
        }
        CrmExpenditureAuidtPoExample example = new CrmExpenditureAuidtPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAuditStatusEqualTo(ExpenditureAuditStatus.RECHECK_PASS.getCode())
                .andExpenditureIdIn(expenditureIds);
        List<CrmExpenditureAuidtPo> pos = crmExpenditureAuidtDao.selectByExample(example);

        return pos.stream().collect(Collectors.toMap(CrmExpenditureAuidtPo::getExpenditureId, CrmExpenditureAuidtPo::getCtime));
    }

    @Override
    @Transactional(value = "crmPlatformTransactionManager", rollbackFor = Exception.class)
    public void batchCommitAudit(List<Integer> expenditureIds, Operator operator) throws IllegalArgumentException {
        List<ExpenditureDto> expenditures = expenditureService.queryExpenditureByParam(QueryExpenditureParam.builder().ids(expenditureIds).build());
        // 合同维度成本项
        Set<Integer> crmContractIds = expenditures.stream().map(ExpenditureDto::getCrmContractId).collect(Collectors.toSet());
        expenditures.addAll(expenditureService.queryExpenditureByParam(QueryExpenditureParam.builder().contractIds(new ArrayList<>(crmContractIds)).build()));
        List<Integer> allRelatedExpenditureIds = expenditures.stream().map(ExpenditureDto::getId).distinct().collect(Collectors.toList());
        // 项目维度成本项
        Map<Integer, Integer> expenditureOrderIdMap = expenditures.stream()
                .collect(Collectors.toMap(ExpenditureDto::getId, ExpenditureDto::getCrmOrderId));
        Map<Integer, Integer> orderProjectIdMap = orderService.queryOrderExtra(new ArrayList<>(expenditureOrderIdMap.values())).values().stream()
                .collect(Collectors.toMap(CrmOrderExtraDto::getCrmOrderId, CrmOrderExtraDto::getProjectItemId));

        // 校验合同 x 项目的维度成本项
        Set<Integer> projectIds = new HashSet<>(orderProjectIdMap.values());
        Set<Integer> allRelatedOrderIds = crmOrderExtraRepo.queryByProjectIds(new ArrayList<>(projectIds)).stream().map(CrmOrderExtraPo::getCrmOrderId).collect(Collectors.toSet());
        List<ExpenditureDto> allRelatedExpenditures = expenditureService.queryExpenditureByParam(QueryExpenditureParam.builder().orderIds(new ArrayList<>(allRelatedOrderIds)).build());

        if (allRelatedExpenditures.stream().anyMatch(expenditure -> expenditure.getStatus() >= NewExpenditureAuditStatus.AUDITING.getCode())) {
            throw new IllegalArgumentException("存在合同 x 项目的成本项审批中");
        }

        //金额比率碰线校验
        List<AuditResultDto> auditResultDtos = iCostManagementDataStrategyService.buildCostJudgeInfo(allRelatedExpenditureIds);
        Map<Integer, Integer> auditResultMap = auditResultDtos.stream().collect(Collectors.toMap(AuditResultDto::getId, AuditResultDto::getCode));

        //需要审批
        List<Integer> needAuditIds = new ArrayList<>();
        expenditures.forEach(expenditure -> {
            //需要审批
            if (auditResultMap.containsKey(expenditure.getId()) && auditResultMap.get(expenditure.getId()).equals(ExpenditureAuditResultType.COST_NO_PASS.getCode())) {
                //todo
                needAuditIds.add(expenditure.getId());
                ExpenditureOaDto oaDto = iCostManagementDataStrategyService.getOaCostJudgeInfo(allRelatedExpenditureIds);
                iOaService.createExpenditureOaFlow(oaDto, operator);
                expenditure.setExpenditureStatus(ExpenditureStatus.WAITING_FOR_APPROVAL.getCode());
                expenditure.setAuditStatus(NewExpenditureAuditStatus.AUDITING.getCode());
                expenditureService.updateExpenditureAuditStatus(expenditure);
                return;
            }
            expenditure.setExpenditureStatus(ExpenditureStatus.EXECUTING.getCode());
            expenditure.setAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getCode());
            expenditureService.updateExpenditureAuditStatus(expenditure);

        });

        if (!CollectionUtils.isEmpty(needAuditIds)) {
            //todo
            ExpenditureOaDto oaDto = iCostManagementDataStrategyService.getOaCostJudgeInfo(needAuditIds);
            iOaService.createExpenditureOaFlow(oaDto, operator);
        }

    }

    private CrmExpenditureAuidtPoExample createExampleFromQueryDto(QueryExpenditureAuditParam param) {
        CrmExpenditureAuidtPoExample example = new CrmExpenditureAuidtPoExample();
        CrmExpenditureAuidtPoExample.Criteria criteria = example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        if (param == null) {
            return example;
        }

        ObjectUtils.setObject(param::getAuditTimeFrom, criteria::andCtimeGreaterThanOrEqualTo);
        ObjectUtils.setObject(param::getAuditTimeTo, criteria::andCtimeLessThanOrEqualTo);
        ObjectUtils.setObject(param::getAuditStatus, criteria::andAuditStatusEqualTo);

        return example;
    }
}
