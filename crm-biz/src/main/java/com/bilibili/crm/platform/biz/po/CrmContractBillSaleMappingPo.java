package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmContractBillSaleMappingPo implements Serializable {
    /**
     * 自增主键
     */
    private Long id;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 账单id，已关帐账单/实结账单/账单调整账单值为账单id，其他未关帐账单值为0
     */
    private Integer billId;

    /**
     * 是否手动调整，0-否；1-是
     */
    private Integer isManual;

    /**
     * 是否已关帐，0-未关帐；1-已关帐
     */
    @Deprecated
    private Integer isClosed;

    /**
     * 是否是当前业绩销售，0-否；1-是
     */
    private Integer isCurrent;

    /**
     * 销售类型，2-直客；1-渠道
     * {@link BiliUserType}
     */
    private Integer saleTypes;

    /**
     * 销售id
     */
    private Integer saleId;

    /**
     * 补充信息
     */
    private String ext;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}
