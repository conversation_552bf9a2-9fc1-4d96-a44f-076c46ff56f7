package com.bilibili.crm.platform.biz.service.finance.automation.component.contracttype;

import com.bilibili.crm.platform.biz.po.CrmContractPo;

import java.util.Comparator;

/**
 * crm 认款合同比较器
 *
 * <AUTHOR>
 * @date 2021/3/8 3:31 下午
 */
public class ClaimContractComparator implements Comparator<CrmContractPo> {

    /**
     * 排序 end_time asc, bill_amount desc, ctime asc
     *
     * @param o1
     * @param o2
     * @return
     */
    @Override
    public int compare(CrmContractPo o1, CrmContractPo o2) {
        // end_time asc
        int endTimeCompareResult = o1.getEndTime().compareTo(o2.getEndTime());
        if (endTimeCompareResult != 0) {
            return endTimeCompareResult;
        }
        // 开票金额 降序
        Long o2TotalBillAmount = o2.getBillAmount() + o2.getOfflineBillAmount();
        long o1TotalBillAmount = o1.getBillAmount() + o1.getOfflineBillAmount();
        int billAmountCompareResult = o2TotalBillAmount.compareTo(o1TotalBillAmount);
        if (billAmountCompareResult != 0) {
            return billAmountCompareResult;
        }
        // ctime asc
        int ctimeCompareResult = o1.getCtime().compareTo(o2.getCtime());
        if (ctimeCompareResult != 0) {
            return ctimeCompareResult;
        }
        return 0;
    }
}
