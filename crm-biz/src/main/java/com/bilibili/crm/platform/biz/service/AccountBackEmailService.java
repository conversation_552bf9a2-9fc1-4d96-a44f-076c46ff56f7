package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.finance.dto.EmailScreenshotDto;
import com.bilibili.crm.platform.api.finance.service.IAccountBackEmailService;
import com.bilibili.crm.platform.biz.dao.CrmAccountBackEmailScreenshotDao;
import com.bilibili.crm.platform.biz.po.CrmAccountBackEmailScreenshotPo;
import com.bilibili.crm.platform.biz.po.CrmAccountBackEmailScreenshotPoExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Service
public class AccountBackEmailService implements IAccountBackEmailService {


    @Autowired
    private CrmAccountBackEmailScreenshotDao crmAccountBackEmailScreenshotDao;

    @Override
    public void insertEmails(List<EmailScreenshotDto> emails) {
        for (EmailScreenshotDto email : emails){
            CrmAccountBackEmailScreenshotPo po = new CrmAccountBackEmailScreenshotPo();
            po.setRecordId(email.getRecordId());
            po.setImageUrl(email.getUrl());
            po.setImageName(email.getName());

            crmAccountBackEmailScreenshotDao.insertSelective(po);
        }
    }

    @Override
    public int deleteEmailByRecordId(Integer recordId) {
        Assert.notNull(recordId, "record ID can not be null");
        CrmAccountBackEmailScreenshotPoExample example = new CrmAccountBackEmailScreenshotPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andRecordIdEqualTo(recordId);

        CrmAccountBackEmailScreenshotPo update = new CrmAccountBackEmailScreenshotPo();
        update.setIsDeleted(IsDeleted.DELETED.getCode());

        return crmAccountBackEmailScreenshotDao.updateByExampleSelective(update, example);
    }

    @Override
    public List<EmailScreenshotDto> getEmailsByRecordId(Integer recordId) {
        CrmAccountBackEmailScreenshotPoExample example = new CrmAccountBackEmailScreenshotPoExample();
        example.or()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andRecordIdEqualTo(recordId);
        List<CrmAccountBackEmailScreenshotPo> pos = crmAccountBackEmailScreenshotDao.selectByExample(example);
        return pos.stream().map(po -> {
            return EmailScreenshotDto.builder()
                    .name(po.getImageName())
                    .recordId(po.getRecordId())
                    .url(po.getImageUrl())
                    .build();
        }).collect(Collectors.toList());
    }

    /**
     * update操作步骤:
     *  1. delete all by recordId
     *  2. add all emails
     * @param recordId
     * @param emails
     */
    @Override
    public void updateEmailsByRecordId(Integer recordId, List<EmailScreenshotDto> emails) {
        Assert.notNull(recordId, "record ID can not be null");
        this.deleteEmailByRecordId(recordId);
        if (!CollectionUtils.isEmpty(emails)){
            emails.forEach(email -> {
                CrmAccountBackEmailScreenshotPo po = new CrmAccountBackEmailScreenshotPo();
                po.setRecordId(recordId);
                po.setImageName(email.getName());
                po.setImageUrl(email.getUrl());
                crmAccountBackEmailScreenshotDao.insertSelective(po);
            });
        }
    }

}
