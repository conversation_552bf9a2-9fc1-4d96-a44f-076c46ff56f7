package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.AccAccountArrearPo;
import com.bilibili.crm.platform.biz.po.AccAccountArrearPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AccAccountArrearDao {
    long countByExample(AccAccountArrearPoExample example);

    int deleteByExample(AccAccountArrearPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AccAccountArrearPo record);

    int insertBatch(List<AccAccountArrearPo> records);

    int insertUpdateBatch(List<AccAccountArrearPo> records);

    int insert(AccAccountArrearPo record);

    int insertUpdateSelective(AccAccountArrearPo record);

    int insertSelective(AccAccountArrearPo record);

    List<AccAccountArrearPo> selectByExample(AccAccountArrearPoExample example);

    AccAccountArrearPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AccAccountArrearPo record, @Param("example") AccAccountArrearPoExample example);

    int updateByExample(@Param("record") AccAccountArrearPo record, @Param("example") AccAccountArrearPoExample example);

    int updateByPrimaryKeySelective(AccAccountArrearPo record);

    int updateByPrimaryKey(AccAccountArrearPo record);
}