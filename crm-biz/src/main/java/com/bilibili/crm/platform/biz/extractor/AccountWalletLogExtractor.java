package com.bilibili.crm.platform.biz.extractor;

import com.bilibili.crm.platform.api.account.dto.AccountWalletLogDto;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.search.aggregations.Aggregation;
import org.elasticsearch.search.aggregations.bucket.MultiBucketsAggregation;
import org.elasticsearch.search.aggregations.bucket.histogram.Histogram;
import org.elasticsearch.search.aggregations.bucket.histogram.InternalDateHistogram;
import org.elasticsearch.search.aggregations.bucket.terms.Terms;
import org.elasticsearch.search.aggregations.metrics.sum.InternalSum;
import org.springframework.data.elasticsearch.core.ResultsExtractor;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

/**
 * @description:
 * @author: brady
 * @time: 2020/11/22 2:21 下午
 */
@Slf4j
public class AccountWalletLogExtractor implements ResultsExtractor<List<AccountWalletLogDto>> {

    private static ThreadLocal<SimpleDateFormat> SDF_HOUR = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd HH");
        }
    };
    private static ThreadLocal<SimpleDateFormat> SDF_HOUR_T = new ThreadLocal<SimpleDateFormat>() {
        @Override
        protected SimpleDateFormat initialValue() {
            return new SimpleDateFormat("yyyy-MM-dd'T'HH");
        }
    };

    @Override
    public List<AccountWalletLogDto> extract(SearchResponse response) {
        List<AccountWalletLogDto> result = new ArrayList<>();
        List<Aggregation> list = response.getAggregations().asList();
        if (CollectionUtils.isEmpty(list)) {
            return Collections.EMPTY_LIST;
        }

        //account bucket
        Terms terms = (Terms) list.get(0);
        for (Terms.Bucket bucket : terms.getBuckets()) {
            Long accountId = (Long) bucket.getKey();

            //agent bucket
            Terms agentIdTerms = bucket.getAggregations().get("agg_agent_id");
            for (Terms.Bucket agentIdBuket : agentIdTerms.getBuckets()) {
                Long agentId = (Long) agentIdBuket.getKey();
                Terms salesTypeTerms = agentIdBuket.getAggregations().get("agg_sales_type");
                List<? extends Terms.Bucket> salesTypeBuckets = salesTypeTerms.getBuckets();
                for (Terms.Bucket salesTypeBucket : salesTypeBuckets) {
                    Long salesType = (Long) salesTypeBucket.getKey();
                    InternalDateHistogram aggDate = salesTypeBucket.getAggregations().get("agg_ctime");
                    if (aggDate == null) {
                        AccountWalletLogDto dto = this.buildWalletLogDto(salesTypeBucket);
                        dto.setAccountId(accountId.intValue());
                        dto.setSalesType(salesType.intValue());
                        dto.setAgentId(agentId.intValue());
                        result.add(dto);
                    } else {
                        List<? extends Histogram.Bucket> aggDateBuckets = aggDate.getBuckets();
                        for (Histogram.Bucket aggDateBucket : aggDateBuckets) {
                            //Object key1 = aggDateBucket.getKey();

                            String timestamp = aggDateBucket.getKeyAsString();
                            AccountWalletLogDto dto = this.buildWalletLogDto(aggDateBucket);
                            dto.setAccountId(accountId.intValue());
                            dto.setSalesType(salesType.intValue());
                            dto.setAgentId(agentId.intValue());

                            try {
                                Timestamp timestamp1 = new Timestamp(SDF_HOUR_T.get().parse(timestamp).getTime());
                                dto.setDate(timestamp1);
                                dto.setCtime(timestamp1);
                                result.add(dto);
                            } catch (ParseException e) {
                                log.error("failed to parse date {},account {}", timestamp, accountId.intValue(), e);
                                continue;
                            }
                        }
                    }
                }
            }
        }
        return result;
    }

    private AccountWalletLogDto buildWalletLogDto(MultiBucketsAggregation.Bucket bucket) {

        InternalSum cashConsume = bucket.getAggregations().get("cashSum");
        InternalSum redPacketConsume = bucket.getAggregations().get("redPacketSum");
        InternalSum specialRedPacketConsume = bucket.getAggregations().get("specialRedPacketSum");
        InternalSum creditConsume = bucket.getAggregations().get("creditSum");

        AccountWalletLogDto dto = AccountWalletLogDto.builder()
                .cash(new Double(cashConsume.getValue()).longValue())
                .redPacket(new Double(redPacketConsume.getValue()).longValue())
                .specialRedPacket(new Double(specialRedPacketConsume.getValue()).longValue())
                .credit(new Double(creditConsume.getValue()).longValue())
                .build();

        return dto;
    }

}
