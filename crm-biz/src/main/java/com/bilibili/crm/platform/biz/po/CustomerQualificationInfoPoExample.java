package com.bilibili.crm.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CustomerQualificationInfoPoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CustomerQualificationInfoPoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNull() {
            addCriterion("customer_id is null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIsNotNull() {
            addCriterion("customer_id is not null");
            return (Criteria) this;
        }

        public Criteria andCustomerIdEqualTo(Integer value) {
            addCriterion("customer_id =", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotEqualTo(Integer value) {
            addCriterion("customer_id <>", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThan(Integer value) {
            addCriterion("customer_id >", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("customer_id >=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThan(Integer value) {
            addCriterion("customer_id <", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdLessThanOrEqualTo(Integer value) {
            addCriterion("customer_id <=", value, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdIn(List<Integer> values) {
            addCriterion("customer_id in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotIn(List<Integer> values) {
            addCriterion("customer_id not in", values, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdBetween(Integer value1, Integer value2) {
            addCriterion("customer_id between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andCustomerIdNotBetween(Integer value1, Integer value2) {
            addCriterion("customer_id not between", value1, value2, "customerId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdIsNull() {
            addCriterion("qualification_type_id is null");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdIsNotNull() {
            addCriterion("qualification_type_id is not null");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdEqualTo(Integer value) {
            addCriterion("qualification_type_id =", value, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdNotEqualTo(Integer value) {
            addCriterion("qualification_type_id <>", value, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdGreaterThan(Integer value) {
            addCriterion("qualification_type_id >", value, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("qualification_type_id >=", value, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdLessThan(Integer value) {
            addCriterion("qualification_type_id <", value, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdLessThanOrEqualTo(Integer value) {
            addCriterion("qualification_type_id <=", value, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdIn(List<Integer> values) {
            addCriterion("qualification_type_id in", values, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdNotIn(List<Integer> values) {
            addCriterion("qualification_type_id not in", values, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdBetween(Integer value1, Integer value2) {
            addCriterion("qualification_type_id between", value1, value2, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andQualificationTypeIdNotBetween(Integer value1, Integer value2) {
            addCriterion("qualification_type_id not between", value1, value2, "qualificationTypeId");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeIsNull() {
            addCriterion("business_licence_code is null");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeIsNotNull() {
            addCriterion("business_licence_code is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeEqualTo(String value) {
            addCriterion("business_licence_code =", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeNotEqualTo(String value) {
            addCriterion("business_licence_code <>", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeGreaterThan(String value) {
            addCriterion("business_licence_code >", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeGreaterThanOrEqualTo(String value) {
            addCriterion("business_licence_code >=", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeLessThan(String value) {
            addCriterion("business_licence_code <", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeLessThanOrEqualTo(String value) {
            addCriterion("business_licence_code <=", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeLike(String value) {
            addCriterion("business_licence_code like", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeNotLike(String value) {
            addCriterion("business_licence_code not like", value, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeIn(List<String> values) {
            addCriterion("business_licence_code in", values, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeNotIn(List<String> values) {
            addCriterion("business_licence_code not in", values, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeBetween(String value1, String value2) {
            addCriterion("business_licence_code between", value1, value2, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceCodeNotBetween(String value1, String value2) {
            addCriterion("business_licence_code not between", value1, value2, "businessLicenceCode");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteIsNull() {
            addCriterion("is_business_licence_indefinite is null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteIsNotNull() {
            addCriterion("is_business_licence_indefinite is not null");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteEqualTo(Integer value) {
            addCriterion("is_business_licence_indefinite =", value, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteNotEqualTo(Integer value) {
            addCriterion("is_business_licence_indefinite <>", value, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteGreaterThan(Integer value) {
            addCriterion("is_business_licence_indefinite >", value, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_business_licence_indefinite >=", value, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteLessThan(Integer value) {
            addCriterion("is_business_licence_indefinite <", value, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteLessThanOrEqualTo(Integer value) {
            addCriterion("is_business_licence_indefinite <=", value, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteIn(List<Integer> values) {
            addCriterion("is_business_licence_indefinite in", values, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteNotIn(List<Integer> values) {
            addCriterion("is_business_licence_indefinite not in", values, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteBetween(Integer value1, Integer value2) {
            addCriterion("is_business_licence_indefinite between", value1, value2, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsBusinessLicenceIndefiniteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_business_licence_indefinite not between", value1, value2, "isBusinessLicenceIndefinite");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateIsNull() {
            addCriterion("business_licence_expire_date is null");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateIsNotNull() {
            addCriterion("business_licence_expire_date is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateEqualTo(Timestamp value) {
            addCriterion("business_licence_expire_date =", value, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateNotEqualTo(Timestamp value) {
            addCriterion("business_licence_expire_date <>", value, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateGreaterThan(Timestamp value) {
            addCriterion("business_licence_expire_date >", value, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("business_licence_expire_date >=", value, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateLessThan(Timestamp value) {
            addCriterion("business_licence_expire_date <", value, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("business_licence_expire_date <=", value, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateIn(List<Timestamp> values) {
            addCriterion("business_licence_expire_date in", values, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateNotIn(List<Timestamp> values) {
            addCriterion("business_licence_expire_date not in", values, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("business_licence_expire_date between", value1, value2, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andBusinessLicenceExpireDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("business_licence_expire_date not between", value1, value2, "businessLicenceExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameIsNull() {
            addCriterion("legal_person_name is null");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameIsNotNull() {
            addCriterion("legal_person_name is not null");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameEqualTo(String value) {
            addCriterion("legal_person_name =", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameNotEqualTo(String value) {
            addCriterion("legal_person_name <>", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameGreaterThan(String value) {
            addCriterion("legal_person_name >", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameGreaterThanOrEqualTo(String value) {
            addCriterion("legal_person_name >=", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameLessThan(String value) {
            addCriterion("legal_person_name <", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameLessThanOrEqualTo(String value) {
            addCriterion("legal_person_name <=", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameLike(String value) {
            addCriterion("legal_person_name like", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameNotLike(String value) {
            addCriterion("legal_person_name not like", value, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameIn(List<String> values) {
            addCriterion("legal_person_name in", values, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameNotIn(List<String> values) {
            addCriterion("legal_person_name not in", values, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameBetween(String value1, String value2) {
            addCriterion("legal_person_name between", value1, value2, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andLegalPersonNameNotBetween(String value1, String value2) {
            addCriterion("legal_person_name not between", value1, value2, "legalPersonName");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteIsNull() {
            addCriterion("is_legal_person_id_card_indefinite is null");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteIsNotNull() {
            addCriterion("is_legal_person_id_card_indefinite is not null");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteEqualTo(Integer value) {
            addCriterion("is_legal_person_id_card_indefinite =", value, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteNotEqualTo(Integer value) {
            addCriterion("is_legal_person_id_card_indefinite <>", value, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteGreaterThan(Integer value) {
            addCriterion("is_legal_person_id_card_indefinite >", value, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_legal_person_id_card_indefinite >=", value, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteLessThan(Integer value) {
            addCriterion("is_legal_person_id_card_indefinite <", value, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteLessThanOrEqualTo(Integer value) {
            addCriterion("is_legal_person_id_card_indefinite <=", value, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteIn(List<Integer> values) {
            addCriterion("is_legal_person_id_card_indefinite in", values, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteNotIn(List<Integer> values) {
            addCriterion("is_legal_person_id_card_indefinite not in", values, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteBetween(Integer value1, Integer value2) {
            addCriterion("is_legal_person_id_card_indefinite between", value1, value2, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsLegalPersonIdCardIndefiniteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_legal_person_id_card_indefinite not between", value1, value2, "isLegalPersonIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateIsNull() {
            addCriterion("legal_person_id_card_expire_date is null");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateIsNotNull() {
            addCriterion("legal_person_id_card_expire_date is not null");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateEqualTo(Timestamp value) {
            addCriterion("legal_person_id_card_expire_date =", value, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateNotEqualTo(Timestamp value) {
            addCriterion("legal_person_id_card_expire_date <>", value, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateGreaterThan(Timestamp value) {
            addCriterion("legal_person_id_card_expire_date >", value, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("legal_person_id_card_expire_date >=", value, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateLessThan(Timestamp value) {
            addCriterion("legal_person_id_card_expire_date <", value, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("legal_person_id_card_expire_date <=", value, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateIn(List<Timestamp> values) {
            addCriterion("legal_person_id_card_expire_date in", values, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateNotIn(List<Timestamp> values) {
            addCriterion("legal_person_id_card_expire_date not in", values, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("legal_person_id_card_expire_date between", value1, value2, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andLegalPersonIdCardExpireDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("legal_person_id_card_expire_date not between", value1, value2, "legalPersonIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberIsNull() {
            addCriterion("icp_record_number is null");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberIsNotNull() {
            addCriterion("icp_record_number is not null");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberEqualTo(String value) {
            addCriterion("icp_record_number =", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberNotEqualTo(String value) {
            addCriterion("icp_record_number <>", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberGreaterThan(String value) {
            addCriterion("icp_record_number >", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberGreaterThanOrEqualTo(String value) {
            addCriterion("icp_record_number >=", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberLessThan(String value) {
            addCriterion("icp_record_number <", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberLessThanOrEqualTo(String value) {
            addCriterion("icp_record_number <=", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberLike(String value) {
            addCriterion("icp_record_number like", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberNotLike(String value) {
            addCriterion("icp_record_number not like", value, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberIn(List<String> values) {
            addCriterion("icp_record_number in", values, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberNotIn(List<String> values) {
            addCriterion("icp_record_number not in", values, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberBetween(String value1, String value2) {
            addCriterion("icp_record_number between", value1, value2, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andIcpRecordNumberNotBetween(String value1, String value2) {
            addCriterion("icp_record_number not between", value1, value2, "icpRecordNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeIsNull() {
            addCriterion("personal_id_card_type is null");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeIsNotNull() {
            addCriterion("personal_id_card_type is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeEqualTo(Integer value) {
            addCriterion("personal_id_card_type =", value, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeNotEqualTo(Integer value) {
            addCriterion("personal_id_card_type <>", value, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeGreaterThan(Integer value) {
            addCriterion("personal_id_card_type >", value, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeGreaterThanOrEqualTo(Integer value) {
            addCriterion("personal_id_card_type >=", value, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeLessThan(Integer value) {
            addCriterion("personal_id_card_type <", value, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeLessThanOrEqualTo(Integer value) {
            addCriterion("personal_id_card_type <=", value, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeIn(List<Integer> values) {
            addCriterion("personal_id_card_type in", values, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeNotIn(List<Integer> values) {
            addCriterion("personal_id_card_type not in", values, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeBetween(Integer value1, Integer value2) {
            addCriterion("personal_id_card_type between", value1, value2, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardTypeNotBetween(Integer value1, Integer value2) {
            addCriterion("personal_id_card_type not between", value1, value2, "personalIdCardType");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberIsNull() {
            addCriterion("personal_id_card_number is null");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberIsNotNull() {
            addCriterion("personal_id_card_number is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberEqualTo(String value) {
            addCriterion("personal_id_card_number =", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberNotEqualTo(String value) {
            addCriterion("personal_id_card_number <>", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberGreaterThan(String value) {
            addCriterion("personal_id_card_number >", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberGreaterThanOrEqualTo(String value) {
            addCriterion("personal_id_card_number >=", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberLessThan(String value) {
            addCriterion("personal_id_card_number <", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberLessThanOrEqualTo(String value) {
            addCriterion("personal_id_card_number <=", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberLike(String value) {
            addCriterion("personal_id_card_number like", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberNotLike(String value) {
            addCriterion("personal_id_card_number not like", value, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberIn(List<String> values) {
            addCriterion("personal_id_card_number in", values, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberNotIn(List<String> values) {
            addCriterion("personal_id_card_number not in", values, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberBetween(String value1, String value2) {
            addCriterion("personal_id_card_number between", value1, value2, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardNumberNotBetween(String value1, String value2) {
            addCriterion("personal_id_card_number not between", value1, value2, "personalIdCardNumber");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteIsNull() {
            addCriterion("is_personal_id_card_indefinite is null");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteIsNotNull() {
            addCriterion("is_personal_id_card_indefinite is not null");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteEqualTo(Integer value) {
            addCriterion("is_personal_id_card_indefinite =", value, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteNotEqualTo(Integer value) {
            addCriterion("is_personal_id_card_indefinite <>", value, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteGreaterThan(Integer value) {
            addCriterion("is_personal_id_card_indefinite >", value, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_personal_id_card_indefinite >=", value, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteLessThan(Integer value) {
            addCriterion("is_personal_id_card_indefinite <", value, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteLessThanOrEqualTo(Integer value) {
            addCriterion("is_personal_id_card_indefinite <=", value, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteIn(List<Integer> values) {
            addCriterion("is_personal_id_card_indefinite in", values, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteNotIn(List<Integer> values) {
            addCriterion("is_personal_id_card_indefinite not in", values, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteBetween(Integer value1, Integer value2) {
            addCriterion("is_personal_id_card_indefinite between", value1, value2, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andIsPersonalIdCardIndefiniteNotBetween(Integer value1, Integer value2) {
            addCriterion("is_personal_id_card_indefinite not between", value1, value2, "isPersonalIdCardIndefinite");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateIsNull() {
            addCriterion("personal_id_card_expire_date is null");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateIsNotNull() {
            addCriterion("personal_id_card_expire_date is not null");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateEqualTo(Timestamp value) {
            addCriterion("personal_id_card_expire_date =", value, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateNotEqualTo(Timestamp value) {
            addCriterion("personal_id_card_expire_date <>", value, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateGreaterThan(Timestamp value) {
            addCriterion("personal_id_card_expire_date >", value, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("personal_id_card_expire_date >=", value, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateLessThan(Timestamp value) {
            addCriterion("personal_id_card_expire_date <", value, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateLessThanOrEqualTo(Timestamp value) {
            addCriterion("personal_id_card_expire_date <=", value, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateIn(List<Timestamp> values) {
            addCriterion("personal_id_card_expire_date in", values, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateNotIn(List<Timestamp> values) {
            addCriterion("personal_id_card_expire_date not in", values, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateBetween(Timestamp value1, Timestamp value2) {
            addCriterion("personal_id_card_expire_date between", value1, value2, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andPersonalIdCardExpireDateNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("personal_id_card_expire_date not between", value1, value2, "personalIdCardExpireDate");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrIsNull() {
            addCriterion("operation_phone_number_addr is null");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrIsNotNull() {
            addCriterion("operation_phone_number_addr is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrEqualTo(String value) {
            addCriterion("operation_phone_number_addr =", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrNotEqualTo(String value) {
            addCriterion("operation_phone_number_addr <>", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrGreaterThan(String value) {
            addCriterion("operation_phone_number_addr >", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrGreaterThanOrEqualTo(String value) {
            addCriterion("operation_phone_number_addr >=", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrLessThan(String value) {
            addCriterion("operation_phone_number_addr <", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrLessThanOrEqualTo(String value) {
            addCriterion("operation_phone_number_addr <=", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrLike(String value) {
            addCriterion("operation_phone_number_addr like", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrNotLike(String value) {
            addCriterion("operation_phone_number_addr not like", value, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrIn(List<String> values) {
            addCriterion("operation_phone_number_addr in", values, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrNotIn(List<String> values) {
            addCriterion("operation_phone_number_addr not in", values, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrBetween(String value1, String value2) {
            addCriterion("operation_phone_number_addr between", value1, value2, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberAddrNotBetween(String value1, String value2) {
            addCriterion("operation_phone_number_addr not between", value1, value2, "operationPhoneNumberAddr");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberIsNull() {
            addCriterion("operation_phone_number is null");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberIsNotNull() {
            addCriterion("operation_phone_number is not null");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberEqualTo(String value) {
            addCriterion("operation_phone_number =", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberNotEqualTo(String value) {
            addCriterion("operation_phone_number <>", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberGreaterThan(String value) {
            addCriterion("operation_phone_number >", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberGreaterThanOrEqualTo(String value) {
            addCriterion("operation_phone_number >=", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberLessThan(String value) {
            addCriterion("operation_phone_number <", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberLessThanOrEqualTo(String value) {
            addCriterion("operation_phone_number <=", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberLike(String value) {
            addCriterion("operation_phone_number like", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberNotLike(String value) {
            addCriterion("operation_phone_number not like", value, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberIn(List<String> values) {
            addCriterion("operation_phone_number in", values, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberNotIn(List<String> values) {
            addCriterion("operation_phone_number not in", values, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberBetween(String value1, String value2) {
            addCriterion("operation_phone_number between", value1, value2, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andOperationPhoneNumberNotBetween(String value1, String value2) {
            addCriterion("operation_phone_number not between", value1, value2, "operationPhoneNumber");
            return (Criteria) this;
        }

        public Criteria andEmailIsNull() {
            addCriterion("email is null");
            return (Criteria) this;
        }

        public Criteria andEmailIsNotNull() {
            addCriterion("email is not null");
            return (Criteria) this;
        }

        public Criteria andEmailEqualTo(String value) {
            addCriterion("email =", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotEqualTo(String value) {
            addCriterion("email <>", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThan(String value) {
            addCriterion("email >", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailGreaterThanOrEqualTo(String value) {
            addCriterion("email >=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThan(String value) {
            addCriterion("email <", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLessThanOrEqualTo(String value) {
            addCriterion("email <=", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailLike(String value) {
            addCriterion("email like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotLike(String value) {
            addCriterion("email not like", value, "email");
            return (Criteria) this;
        }

        public Criteria andEmailIn(List<String> values) {
            addCriterion("email in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotIn(List<String> values) {
            addCriterion("email not in", values, "email");
            return (Criteria) this;
        }

        public Criteria andEmailBetween(String value1, String value2) {
            addCriterion("email between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andEmailNotBetween(String value1, String value2) {
            addCriterion("email not between", value1, value2, "email");
            return (Criteria) this;
        }

        public Criteria andAddressIsNull() {
            addCriterion("address is null");
            return (Criteria) this;
        }

        public Criteria andAddressIsNotNull() {
            addCriterion("address is not null");
            return (Criteria) this;
        }

        public Criteria andAddressEqualTo(String value) {
            addCriterion("address =", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotEqualTo(String value) {
            addCriterion("address <>", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThan(String value) {
            addCriterion("address >", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressGreaterThanOrEqualTo(String value) {
            addCriterion("address >=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThan(String value) {
            addCriterion("address <", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLessThanOrEqualTo(String value) {
            addCriterion("address <=", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressLike(String value) {
            addCriterion("address like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotLike(String value) {
            addCriterion("address not like", value, "address");
            return (Criteria) this;
        }

        public Criteria andAddressIn(List<String> values) {
            addCriterion("address in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotIn(List<String> values) {
            addCriterion("address not in", values, "address");
            return (Criteria) this;
        }

        public Criteria andAddressBetween(String value1, String value2) {
            addCriterion("address between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andAddressNotBetween(String value1, String value2) {
            addCriterion("address not between", value1, value2, "address");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameIsNull() {
            addCriterion("enterprise_name is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameIsNotNull() {
            addCriterion("enterprise_name is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameEqualTo(String value) {
            addCriterion("enterprise_name =", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameNotEqualTo(String value) {
            addCriterion("enterprise_name <>", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameGreaterThan(String value) {
            addCriterion("enterprise_name >", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise_name >=", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameLessThan(String value) {
            addCriterion("enterprise_name <", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameLessThanOrEqualTo(String value) {
            addCriterion("enterprise_name <=", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameLike(String value) {
            addCriterion("enterprise_name like", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameNotLike(String value) {
            addCriterion("enterprise_name not like", value, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameIn(List<String> values) {
            addCriterion("enterprise_name in", values, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameNotIn(List<String> values) {
            addCriterion("enterprise_name not in", values, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameBetween(String value1, String value2) {
            addCriterion("enterprise_name between", value1, value2, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseNameNotBetween(String value1, String value2) {
            addCriterion("enterprise_name not between", value1, value2, "enterpriseName");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryIsNull() {
            addCriterion("enterprise_first_industry is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryIsNotNull() {
            addCriterion("enterprise_first_industry is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryEqualTo(String value) {
            addCriterion("enterprise_first_industry =", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryNotEqualTo(String value) {
            addCriterion("enterprise_first_industry <>", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryGreaterThan(String value) {
            addCriterion("enterprise_first_industry >", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise_first_industry >=", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryLessThan(String value) {
            addCriterion("enterprise_first_industry <", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryLessThanOrEqualTo(String value) {
            addCriterion("enterprise_first_industry <=", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryLike(String value) {
            addCriterion("enterprise_first_industry like", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryNotLike(String value) {
            addCriterion("enterprise_first_industry not like", value, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryIn(List<String> values) {
            addCriterion("enterprise_first_industry in", values, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryNotIn(List<String> values) {
            addCriterion("enterprise_first_industry not in", values, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryBetween(String value1, String value2) {
            addCriterion("enterprise_first_industry between", value1, value2, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseFirstIndustryNotBetween(String value1, String value2) {
            addCriterion("enterprise_first_industry not between", value1, value2, "enterpriseFirstIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryIsNull() {
            addCriterion("enterprise_second_industry is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryIsNotNull() {
            addCriterion("enterprise_second_industry is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryEqualTo(String value) {
            addCriterion("enterprise_second_industry =", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryNotEqualTo(String value) {
            addCriterion("enterprise_second_industry <>", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryGreaterThan(String value) {
            addCriterion("enterprise_second_industry >", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise_second_industry >=", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryLessThan(String value) {
            addCriterion("enterprise_second_industry <", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryLessThanOrEqualTo(String value) {
            addCriterion("enterprise_second_industry <=", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryLike(String value) {
            addCriterion("enterprise_second_industry like", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryNotLike(String value) {
            addCriterion("enterprise_second_industry not like", value, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryIn(List<String> values) {
            addCriterion("enterprise_second_industry in", values, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryNotIn(List<String> values) {
            addCriterion("enterprise_second_industry not in", values, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryBetween(String value1, String value2) {
            addCriterion("enterprise_second_industry between", value1, value2, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andEnterpriseSecondIndustryNotBetween(String value1, String value2) {
            addCriterion("enterprise_second_industry not between", value1, value2, "enterpriseSecondIndustry");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeIsNull() {
            addCriterion("organization_type is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeIsNotNull() {
            addCriterion("organization_type is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeEqualTo(String value) {
            addCriterion("organization_type =", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeNotEqualTo(String value) {
            addCriterion("organization_type <>", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeGreaterThan(String value) {
            addCriterion("organization_type >", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeGreaterThanOrEqualTo(String value) {
            addCriterion("organization_type >=", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeLessThan(String value) {
            addCriterion("organization_type <", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeLessThanOrEqualTo(String value) {
            addCriterion("organization_type <=", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeLike(String value) {
            addCriterion("organization_type like", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeNotLike(String value) {
            addCriterion("organization_type not like", value, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeIn(List<String> values) {
            addCriterion("organization_type in", values, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeNotIn(List<String> values) {
            addCriterion("organization_type not in", values, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeBetween(String value1, String value2) {
            addCriterion("organization_type between", value1, value2, "organizationType");
            return (Criteria) this;
        }

        public Criteria andOrganizationTypeNotBetween(String value1, String value2) {
            addCriterion("organization_type not between", value1, value2, "organizationType");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleIsNull() {
            addCriterion("enterprise_scale is null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleIsNotNull() {
            addCriterion("enterprise_scale is not null");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleEqualTo(String value) {
            addCriterion("enterprise_scale =", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleNotEqualTo(String value) {
            addCriterion("enterprise_scale <>", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleGreaterThan(String value) {
            addCriterion("enterprise_scale >", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleGreaterThanOrEqualTo(String value) {
            addCriterion("enterprise_scale >=", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleLessThan(String value) {
            addCriterion("enterprise_scale <", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleLessThanOrEqualTo(String value) {
            addCriterion("enterprise_scale <=", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleLike(String value) {
            addCriterion("enterprise_scale like", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleNotLike(String value) {
            addCriterion("enterprise_scale not like", value, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleIn(List<String> values) {
            addCriterion("enterprise_scale in", values, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleNotIn(List<String> values) {
            addCriterion("enterprise_scale not in", values, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleBetween(String value1, String value2) {
            addCriterion("enterprise_scale between", value1, value2, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andEnterpriseScaleNotBetween(String value1, String value2) {
            addCriterion("enterprise_scale not between", value1, value2, "enterpriseScale");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalIsNull() {
            addCriterion("registered_capital is null");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalIsNotNull() {
            addCriterion("registered_capital is not null");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalEqualTo(Integer value) {
            addCriterion("registered_capital =", value, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalNotEqualTo(Integer value) {
            addCriterion("registered_capital <>", value, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalGreaterThan(Integer value) {
            addCriterion("registered_capital >", value, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalGreaterThanOrEqualTo(Integer value) {
            addCriterion("registered_capital >=", value, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalLessThan(Integer value) {
            addCriterion("registered_capital <", value, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalLessThanOrEqualTo(Integer value) {
            addCriterion("registered_capital <=", value, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalIn(List<Integer> values) {
            addCriterion("registered_capital in", values, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalNotIn(List<Integer> values) {
            addCriterion("registered_capital not in", values, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalBetween(Integer value1, Integer value2) {
            addCriterion("registered_capital between", value1, value2, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andRegisteredCapitalNotBetween(Integer value1, Integer value2) {
            addCriterion("registered_capital not between", value1, value2, "registeredCapital");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteIsNull() {
            addCriterion("official_website is null");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteIsNotNull() {
            addCriterion("official_website is not null");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteEqualTo(String value) {
            addCriterion("official_website =", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteNotEqualTo(String value) {
            addCriterion("official_website <>", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteGreaterThan(String value) {
            addCriterion("official_website >", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteGreaterThanOrEqualTo(String value) {
            addCriterion("official_website >=", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteLessThan(String value) {
            addCriterion("official_website <", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteLessThanOrEqualTo(String value) {
            addCriterion("official_website <=", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteLike(String value) {
            addCriterion("official_website like", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteNotLike(String value) {
            addCriterion("official_website not like", value, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteIn(List<String> values) {
            addCriterion("official_website in", values, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteNotIn(List<String> values) {
            addCriterion("official_website not in", values, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteBetween(String value1, String value2) {
            addCriterion("official_website between", value1, value2, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOfficialWebsiteNotBetween(String value1, String value2) {
            addCriterion("official_website not between", value1, value2, "officialWebsite");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureIsNull() {
            addCriterion("organization_register_picture is null");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureIsNotNull() {
            addCriterion("organization_register_picture is not null");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureEqualTo(String value) {
            addCriterion("organization_register_picture =", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureNotEqualTo(String value) {
            addCriterion("organization_register_picture <>", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureGreaterThan(String value) {
            addCriterion("organization_register_picture >", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureGreaterThanOrEqualTo(String value) {
            addCriterion("organization_register_picture >=", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureLessThan(String value) {
            addCriterion("organization_register_picture <", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureLessThanOrEqualTo(String value) {
            addCriterion("organization_register_picture <=", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureLike(String value) {
            addCriterion("organization_register_picture like", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureNotLike(String value) {
            addCriterion("organization_register_picture not like", value, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureIn(List<String> values) {
            addCriterion("organization_register_picture in", values, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureNotIn(List<String> values) {
            addCriterion("organization_register_picture not in", values, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureBetween(String value1, String value2) {
            addCriterion("organization_register_picture between", value1, value2, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andOrganizationRegisterPictureNotBetween(String value1, String value2) {
            addCriterion("organization_register_picture not between", value1, value2, "organizationRegisterPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureIsNull() {
            addCriterion("supplement_picture is null");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureIsNotNull() {
            addCriterion("supplement_picture is not null");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureEqualTo(String value) {
            addCriterion("supplement_picture =", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureNotEqualTo(String value) {
            addCriterion("supplement_picture <>", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureGreaterThan(String value) {
            addCriterion("supplement_picture >", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureGreaterThanOrEqualTo(String value) {
            addCriterion("supplement_picture >=", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureLessThan(String value) {
            addCriterion("supplement_picture <", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureLessThanOrEqualTo(String value) {
            addCriterion("supplement_picture <=", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureLike(String value) {
            addCriterion("supplement_picture like", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureNotLike(String value) {
            addCriterion("supplement_picture not like", value, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureIn(List<String> values) {
            addCriterion("supplement_picture in", values, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureNotIn(List<String> values) {
            addCriterion("supplement_picture not in", values, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureBetween(String value1, String value2) {
            addCriterion("supplement_picture between", value1, value2, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andSupplementPictureNotBetween(String value1, String value2) {
            addCriterion("supplement_picture not between", value1, value2, "supplementPicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureIsNull() {
            addCriterion("business_licence_picture is null");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureIsNotNull() {
            addCriterion("business_licence_picture is not null");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureEqualTo(String value) {
            addCriterion("business_licence_picture =", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureNotEqualTo(String value) {
            addCriterion("business_licence_picture <>", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureGreaterThan(String value) {
            addCriterion("business_licence_picture >", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureGreaterThanOrEqualTo(String value) {
            addCriterion("business_licence_picture >=", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureLessThan(String value) {
            addCriterion("business_licence_picture <", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureLessThanOrEqualTo(String value) {
            addCriterion("business_licence_picture <=", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureLike(String value) {
            addCriterion("business_licence_picture like", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureNotLike(String value) {
            addCriterion("business_licence_picture not like", value, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureIn(List<String> values) {
            addCriterion("business_licence_picture in", values, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureNotIn(List<String> values) {
            addCriterion("business_licence_picture not in", values, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureBetween(String value1, String value2) {
            addCriterion("business_licence_picture between", value1, value2, "businessLicencePicture");
            return (Criteria) this;
        }

        public Criteria andBusinessLicencePictureNotBetween(String value1, String value2) {
            addCriterion("business_licence_picture not between", value1, value2, "businessLicencePicture");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}