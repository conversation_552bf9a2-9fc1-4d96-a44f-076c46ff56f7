package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.AgentCustomerAuthMappingPo;
import com.bilibili.crm.platform.biz.po.AgentCustomerAuthMappingPoExample;
import org.apache.ibatis.annotations.Param;

import java.util.List;

public interface AgentCustomerAuthMappingDao {
    long countByExample(AgentCustomerAuthMappingPoExample example);

    int deleteByExample(AgentCustomerAuthMappingPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(AgentCustomerAuthMappingPo record);

    int insertBatch(List<AgentCustomerAuthMappingPo> records);

    int insertUpdateBatch(List<AgentCustomerAuthMappingPo> records);

    int insert(AgentCustomerAuthMappingPo record);

    int insertUpdateSelective(AgentCustomerAuthMappingPo record);

    int insertSelective(AgentCustomerAuthMappingPo record);

    List<AgentCustomerAuthMappingPo> selectByExample(AgentCustomerAuthMappingPoExample example);

    AgentCustomerAuthMappingPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") AgentCustomerAuthMappingPo record, @Param("example") AgentCustomerAuthMappingPoExample example);

    int updateByExample(@Param("record") AgentCustomerAuthMappingPo record, @Param("example") AgentCustomerAuthMappingPoExample example);

    int updateByPrimaryKeySelective(AgentCustomerAuthMappingPo record);

    int updateByPrimaryKey(AgentCustomerAuthMappingPo record);
}