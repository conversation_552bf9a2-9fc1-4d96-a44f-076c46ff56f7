package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomerAttachmentInfoPo implements Serializable {
    /**
     * 主键
     */
    private Integer id;

    /**
     * 客户id
     */
    private Integer customerId;

    /**
     * 附件url
     */
    private String url;

    /**
     * 附件类型 0-企业营业执照 1-icp备案附件 2-法人身份证正面 3-法人身份证反面 4-个人身份证 5-线下店铺产品照片 6-线下店铺实体照片
     */
    private Integer type;

    /**
     * 软删除，0-有效，1-删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}