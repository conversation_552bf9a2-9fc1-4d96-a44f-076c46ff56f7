package com.bilibili.crm.platform.biz.bean;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;

/**
 * agentAccountId和他的运营人员邮箱前缀的映射
 *
 * <AUTHOR>
 * date 2024/7/9 17:43.
 * Contact: <EMAIL>.
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AgentIdAccountIdUsernameExtPO implements Serializable {
    private static final long serialVersionUID = -5624175631929853644L;

    private Integer agentAccountId;
    private String email;

}

