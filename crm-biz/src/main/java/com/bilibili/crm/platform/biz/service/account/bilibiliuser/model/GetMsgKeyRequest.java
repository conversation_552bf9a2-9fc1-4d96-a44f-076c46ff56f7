package com.bilibili.crm.platform.biz.service.account.bilibiliuser.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;

/**
 * @see <a href="https://info.bilibili.co/pages/viewpage.action?pageId=*********">业务方私信接口文档</a>
 */
@Data
public class GetMsgKeyRequest implements Serializable {
    private static final long serialVersionUID = 1L;
    @JSONField(ordinal = 1)
    private Integer sender_uid;
    @JSONField(ordinal = 2)
    private Integer msg_type;
    @JSONField(ordinal = 3)
    private String content;
}
