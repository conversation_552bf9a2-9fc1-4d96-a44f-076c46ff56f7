package com.bilibili.crm.platform.biz.Component.account;

import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.account.dto.BiliUserBaseInfoDto;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.service.account.bilibiliuser.BilibiliUserQuerier;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/3 5:41 下午
 */
@Slf4j
@Component
@Deprecated
public class MidValidator {

    @Autowired
    private AccAccountRepo accAccountRepo;
    @Resource
    private BilibiliUserQuerier bilibiliUserQuerier;

    /**
     * mid 合法性校验，1. mid在主站是否存在 2. 一个mid只能绑定一个账号
     *
     * @param accountId accountId存在则是新增；否则是修改
     * @param mid
     * @return
     */
    public BiliUserBaseInfoDto checkMidIfLegal(Integer accountId, Long mid) {
        log.info("=====> checkMidIfLegal[检查mid是否合法], accountId:{}, mid:{}", accountId, mid);
        Assert.isTrue(Utils.isPositive(mid), "合作up主mid不合法");

        // 对于修改账号，如果该mid存在不是该accountId的，则唯一性不通过；否则通过 对于新增，只要存在则不通过
        List<AccAccountPo> accAccountPos = accAccountRepo.queryListByMid(mid);

        if (accAccountPos.stream().anyMatch(accAccountPo -> !accAccountPo.getAccountId().equals(accountId))) {
            log.info("=====> checkMidIfLegal[mid已经存在], accountId:{}, mid:{}", accountId, mid);
            throw new ServiceRuntimeException("该mid已经存在！");
        }

        BiliUserBaseInfoDto biliUserBaseInfoDto = bilibiliUserQuerier.getBiliUserInfoByMid(mid);
        return biliUserBaseInfoDto;
    }
}
