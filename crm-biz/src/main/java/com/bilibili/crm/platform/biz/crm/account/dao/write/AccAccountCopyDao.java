package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.AccAccountCopyPo;
import com.bilibili.crm.platform.biz.po.AccAccountCopyPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface AccAccountCopyDao {
    long countByExample(AccAccountCopyPoExample example);

    int deleteByExample(AccAccountCopyPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(AccAccountCopyPo record);

    int insertBatch(List<AccAccountCopyPo> records);

    int insertUpdateBatch(List<AccAccountCopyPo> records);

    int insert(AccAccountCopyPo record);

    int insertUpdateSelective(AccAccountCopyPo record);

    int insertSelective(AccAccountCopyPo record);

    List<AccAccountCopyPo> selectByExample(AccAccountCopyPoExample example);

    AccAccountCopyPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") AccAccountCopyPo record, @Param("example") AccAccountCopyPoExample example);

    int updateByExample(@Param("record") AccAccountCopyPo record, @Param("example") AccAccountCopyPoExample example);

    int updateByPrimaryKeySelective(AccAccountCopyPo record);

    int updateByPrimaryKey(AccAccountCopyPo record);
}