package com.bilibili.crm.platform.biz.dto;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class BillSplitNoticeDto {

    @ExcelResources(title = "账单ID")
    private Integer bill_id;

    @ExcelResources(title = "实结/调整金额（元）")
    private BigDecimal bill_amount;

    @ExcelResources(title = "账单来源")
    private String bill_source;

    @ExcelResources(title = "合同号")
    private Long contract_number;

    @ExcelResources(title = "合同名称")
    private String contract_name;

    @ExcelResources(title = "订单ID")
    private Integer order_id;

    @ExcelResources(title = "订单名称")
    private String order_name;

    @ExcelResources(title = "业绩归属详情")
    private String detail;
}
