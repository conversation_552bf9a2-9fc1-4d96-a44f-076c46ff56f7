package com.bilibili.crm.platform.biz.po;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

public class CrmContractPolicyOrderTypePoExample {
    protected String orderByClause;

    protected boolean distinct;

    protected List<Criteria> oredCriteria;

    private Integer limit;

    private Integer offset;

    public CrmContractPolicyOrderTypePoExample() {
        oredCriteria = new ArrayList<Criteria>();
    }

    public void setOrderByClause(String orderByClause) {
        this.orderByClause = orderByClause;
    }

    public String getOrderByClause() {
        return orderByClause;
    }

    public void setDistinct(boolean distinct) {
        this.distinct = distinct;
    }

    public boolean isDistinct() {
        return distinct;
    }

    public List<Criteria> getOredCriteria() {
        return oredCriteria;
    }

    public void or(Criteria criteria) {
        oredCriteria.add(criteria);
    }

    public Criteria or() {
        Criteria criteria = createCriteriaInternal();
        oredCriteria.add(criteria);
        return criteria;
    }

    public Criteria createCriteria() {
        Criteria criteria = createCriteriaInternal();
        if (oredCriteria.size() == 0) {
            oredCriteria.add(criteria);
        }
        return criteria;
    }

    protected Criteria createCriteriaInternal() {
        Criteria criteria = new Criteria();
        return criteria;
    }

    public void clear() {
        oredCriteria.clear();
        orderByClause = null;
        distinct = false;
    }

    public void setLimit(Integer limit) {
        this.limit = limit;
    }

    public Integer getLimit() {
        return limit;
    }

    public void setOffset(Integer offset) {
        this.offset = offset;
    }

    public Integer getOffset() {
        return offset;
    }

    protected abstract static class GeneratedCriteria {
        protected List<Criterion> criteria;

        protected GeneratedCriteria() {
            super();
            criteria = new ArrayList<Criterion>();
        }

        public boolean isValid() {
            return criteria.size() > 0;
        }

        public List<Criterion> getAllCriteria() {
            return criteria;
        }

        public List<Criterion> getCriteria() {
            return criteria;
        }

        protected void addCriterion(String condition) {
            if (condition == null) {
                throw new RuntimeException("Value for condition cannot be null");
            }
            criteria.add(new Criterion(condition));
        }

        protected void addCriterion(String condition, Object value, String property) {
            if (value == null) {
                throw new RuntimeException("Value for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value));
        }

        protected void addCriterion(String condition, Object value1, Object value2, String property) {
            if (value1 == null || value2 == null) {
                throw new RuntimeException("Between values for " + property + " cannot be null");
            }
            criteria.add(new Criterion(condition, value1, value2));
        }

        public Criteria andIdIsNull() {
            addCriterion("id is null");
            return (Criteria) this;
        }

        public Criteria andIdIsNotNull() {
            addCriterion("id is not null");
            return (Criteria) this;
        }

        public Criteria andIdEqualTo(Integer value) {
            addCriterion("id =", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotEqualTo(Integer value) {
            addCriterion("id <>", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThan(Integer value) {
            addCriterion("id >", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("id >=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThan(Integer value) {
            addCriterion("id <", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdLessThanOrEqualTo(Integer value) {
            addCriterion("id <=", value, "id");
            return (Criteria) this;
        }

        public Criteria andIdIn(List<Integer> values) {
            addCriterion("id in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotIn(List<Integer> values) {
            addCriterion("id not in", values, "id");
            return (Criteria) this;
        }

        public Criteria andIdBetween(Integer value1, Integer value2) {
            addCriterion("id between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andIdNotBetween(Integer value1, Integer value2) {
            addCriterion("id not between", value1, value2, "id");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIsNull() {
            addCriterion("policy_id is null");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIsNotNull() {
            addCriterion("policy_id is not null");
            return (Criteria) this;
        }

        public Criteria andPolicyIdEqualTo(Integer value) {
            addCriterion("policy_id =", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotEqualTo(Integer value) {
            addCriterion("policy_id <>", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdGreaterThan(Integer value) {
            addCriterion("policy_id >", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdGreaterThanOrEqualTo(Integer value) {
            addCriterion("policy_id >=", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLessThan(Integer value) {
            addCriterion("policy_id <", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdLessThanOrEqualTo(Integer value) {
            addCriterion("policy_id <=", value, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdIn(List<Integer> values) {
            addCriterion("policy_id in", values, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotIn(List<Integer> values) {
            addCriterion("policy_id not in", values, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdBetween(Integer value1, Integer value2) {
            addCriterion("policy_id between", value1, value2, "policyId");
            return (Criteria) this;
        }

        public Criteria andPolicyIdNotBetween(Integer value1, Integer value2) {
            addCriterion("policy_id not between", value1, value2, "policyId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdIsNull() {
            addCriterion("process_instance_id is null");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdIsNotNull() {
            addCriterion("process_instance_id is not null");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdEqualTo(String value) {
            addCriterion("process_instance_id =", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdNotEqualTo(String value) {
            addCriterion("process_instance_id <>", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdGreaterThan(String value) {
            addCriterion("process_instance_id >", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdGreaterThanOrEqualTo(String value) {
            addCriterion("process_instance_id >=", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdLessThan(String value) {
            addCriterion("process_instance_id <", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdLessThanOrEqualTo(String value) {
            addCriterion("process_instance_id <=", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdLike(String value) {
            addCriterion("process_instance_id like", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdNotLike(String value) {
            addCriterion("process_instance_id not like", value, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdIn(List<String> values) {
            addCriterion("process_instance_id in", values, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdNotIn(List<String> values) {
            addCriterion("process_instance_id not in", values, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdBetween(String value1, String value2) {
            addCriterion("process_instance_id between", value1, value2, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andProcessInstanceIdNotBetween(String value1, String value2) {
            addCriterion("process_instance_id not between", value1, value2, "processInstanceId");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignIsNull() {
            addCriterion("is_direct_sign is null");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignIsNotNull() {
            addCriterion("is_direct_sign is not null");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignEqualTo(Integer value) {
            addCriterion("is_direct_sign =", value, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignNotEqualTo(Integer value) {
            addCriterion("is_direct_sign <>", value, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignGreaterThan(Integer value) {
            addCriterion("is_direct_sign >", value, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_direct_sign >=", value, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignLessThan(Integer value) {
            addCriterion("is_direct_sign <", value, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignLessThanOrEqualTo(Integer value) {
            addCriterion("is_direct_sign <=", value, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignIn(List<Integer> values) {
            addCriterion("is_direct_sign in", values, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignNotIn(List<Integer> values) {
            addCriterion("is_direct_sign not in", values, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignBetween(Integer value1, Integer value2) {
            addCriterion("is_direct_sign between", value1, value2, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andIsDirectSignNotBetween(Integer value1, Integer value2) {
            addCriterion("is_direct_sign not between", value1, value2, "isDirectSign");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsIsNull() {
            addCriterion("direct_sale_ids is null");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsIsNotNull() {
            addCriterion("direct_sale_ids is not null");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsEqualTo(String value) {
            addCriterion("direct_sale_ids =", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsNotEqualTo(String value) {
            addCriterion("direct_sale_ids <>", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsGreaterThan(String value) {
            addCriterion("direct_sale_ids >", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsGreaterThanOrEqualTo(String value) {
            addCriterion("direct_sale_ids >=", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsLessThan(String value) {
            addCriterion("direct_sale_ids <", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsLessThanOrEqualTo(String value) {
            addCriterion("direct_sale_ids <=", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsLike(String value) {
            addCriterion("direct_sale_ids like", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsNotLike(String value) {
            addCriterion("direct_sale_ids not like", value, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsIn(List<String> values) {
            addCriterion("direct_sale_ids in", values, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsNotIn(List<String> values) {
            addCriterion("direct_sale_ids not in", values, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsBetween(String value1, String value2) {
            addCriterion("direct_sale_ids between", value1, value2, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andDirectSaleIdsNotBetween(String value1, String value2) {
            addCriterion("direct_sale_ids not between", value1, value2, "directSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsIsNull() {
            addCriterion("channel_sale_ids is null");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsIsNotNull() {
            addCriterion("channel_sale_ids is not null");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsEqualTo(String value) {
            addCriterion("channel_sale_ids =", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsNotEqualTo(String value) {
            addCriterion("channel_sale_ids <>", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsGreaterThan(String value) {
            addCriterion("channel_sale_ids >", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsGreaterThanOrEqualTo(String value) {
            addCriterion("channel_sale_ids >=", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsLessThan(String value) {
            addCriterion("channel_sale_ids <", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsLessThanOrEqualTo(String value) {
            addCriterion("channel_sale_ids <=", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsLike(String value) {
            addCriterion("channel_sale_ids like", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsNotLike(String value) {
            addCriterion("channel_sale_ids not like", value, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsIn(List<String> values) {
            addCriterion("channel_sale_ids in", values, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsNotIn(List<String> values) {
            addCriterion("channel_sale_ids not in", values, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsBetween(String value1, String value2) {
            addCriterion("channel_sale_ids between", value1, value2, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andChannelSaleIdsNotBetween(String value1, String value2) {
            addCriterion("channel_sale_ids not between", value1, value2, "channelSaleIds");
            return (Criteria) this;
        }

        public Criteria andExecMediumsIsNull() {
            addCriterion("exec_mediums is null");
            return (Criteria) this;
        }

        public Criteria andExecMediumsIsNotNull() {
            addCriterion("exec_mediums is not null");
            return (Criteria) this;
        }

        public Criteria andExecMediumsEqualTo(String value) {
            addCriterion("exec_mediums =", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsNotEqualTo(String value) {
            addCriterion("exec_mediums <>", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsGreaterThan(String value) {
            addCriterion("exec_mediums >", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsGreaterThanOrEqualTo(String value) {
            addCriterion("exec_mediums >=", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsLessThan(String value) {
            addCriterion("exec_mediums <", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsLessThanOrEqualTo(String value) {
            addCriterion("exec_mediums <=", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsLike(String value) {
            addCriterion("exec_mediums like", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsNotLike(String value) {
            addCriterion("exec_mediums not like", value, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsIn(List<String> values) {
            addCriterion("exec_mediums in", values, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsNotIn(List<String> values) {
            addCriterion("exec_mediums not in", values, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsBetween(String value1, String value2) {
            addCriterion("exec_mediums between", value1, value2, "execMediums");
            return (Criteria) this;
        }

        public Criteria andExecMediumsNotBetween(String value1, String value2) {
            addCriterion("exec_mediums not between", value1, value2, "execMediums");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeIsNull() {
            addCriterion("hard_ad_income is null");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeIsNotNull() {
            addCriterion("hard_ad_income is not null");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeEqualTo(Long value) {
            addCriterion("hard_ad_income =", value, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeNotEqualTo(Long value) {
            addCriterion("hard_ad_income <>", value, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeGreaterThan(Long value) {
            addCriterion("hard_ad_income >", value, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("hard_ad_income >=", value, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeLessThan(Long value) {
            addCriterion("hard_ad_income <", value, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeLessThanOrEqualTo(Long value) {
            addCriterion("hard_ad_income <=", value, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeIn(List<Long> values) {
            addCriterion("hard_ad_income in", values, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeNotIn(List<Long> values) {
            addCriterion("hard_ad_income not in", values, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeBetween(Long value1, Long value2) {
            addCriterion("hard_ad_income between", value1, value2, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andHardAdIncomeNotBetween(Long value1, Long value2) {
            addCriterion("hard_ad_income not between", value1, value2, "hardAdIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeIsNull() {
            addCriterion("not_stand_income is null");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeIsNotNull() {
            addCriterion("not_stand_income is not null");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeEqualTo(Long value) {
            addCriterion("not_stand_income =", value, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeNotEqualTo(Long value) {
            addCriterion("not_stand_income <>", value, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeGreaterThan(Long value) {
            addCriterion("not_stand_income >", value, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("not_stand_income >=", value, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeLessThan(Long value) {
            addCriterion("not_stand_income <", value, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeLessThanOrEqualTo(Long value) {
            addCriterion("not_stand_income <=", value, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeIn(List<Long> values) {
            addCriterion("not_stand_income in", values, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeNotIn(List<Long> values) {
            addCriterion("not_stand_income not in", values, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeBetween(Long value1, Long value2) {
            addCriterion("not_stand_income between", value1, value2, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andNotStandIncomeNotBetween(Long value1, Long value2) {
            addCriterion("not_stand_income not between", value1, value2, "notStandIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeIsNull() {
            addCriterion("live_broadcast_commercial_income is null");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeIsNotNull() {
            addCriterion("live_broadcast_commercial_income is not null");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeEqualTo(Long value) {
            addCriterion("live_broadcast_commercial_income =", value, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeNotEqualTo(Long value) {
            addCriterion("live_broadcast_commercial_income <>", value, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeGreaterThan(Long value) {
            addCriterion("live_broadcast_commercial_income >", value, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("live_broadcast_commercial_income >=", value, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeLessThan(Long value) {
            addCriterion("live_broadcast_commercial_income <", value, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeLessThanOrEqualTo(Long value) {
            addCriterion("live_broadcast_commercial_income <=", value, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeIn(List<Long> values) {
            addCriterion("live_broadcast_commercial_income in", values, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeNotIn(List<Long> values) {
            addCriterion("live_broadcast_commercial_income not in", values, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeBetween(Long value1, Long value2) {
            addCriterion("live_broadcast_commercial_income between", value1, value2, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andLiveBroadcastCommercialIncomeNotBetween(Long value1, Long value2) {
            addCriterion("live_broadcast_commercial_income not between", value1, value2, "liveBroadcastCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeIsNull() {
            addCriterion("pickup_commercial_income is null");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeIsNotNull() {
            addCriterion("pickup_commercial_income is not null");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeEqualTo(Long value) {
            addCriterion("pickup_commercial_income =", value, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeNotEqualTo(Long value) {
            addCriterion("pickup_commercial_income <>", value, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeGreaterThan(Long value) {
            addCriterion("pickup_commercial_income >", value, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("pickup_commercial_income >=", value, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeLessThan(Long value) {
            addCriterion("pickup_commercial_income <", value, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeLessThanOrEqualTo(Long value) {
            addCriterion("pickup_commercial_income <=", value, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeIn(List<Long> values) {
            addCriterion("pickup_commercial_income in", values, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeNotIn(List<Long> values) {
            addCriterion("pickup_commercial_income not in", values, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeBetween(Long value1, Long value2) {
            addCriterion("pickup_commercial_income between", value1, value2, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andPickupCommercialIncomeNotBetween(Long value1, Long value2) {
            addCriterion("pickup_commercial_income not between", value1, value2, "pickupCommercialIncome");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNull() {
            addCriterion("creator is null");
            return (Criteria) this;
        }

        public Criteria andCreatorIsNotNull() {
            addCriterion("creator is not null");
            return (Criteria) this;
        }

        public Criteria andCreatorEqualTo(String value) {
            addCriterion("creator =", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotEqualTo(String value) {
            addCriterion("creator <>", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThan(String value) {
            addCriterion("creator >", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorGreaterThanOrEqualTo(String value) {
            addCriterion("creator >=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThan(String value) {
            addCriterion("creator <", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLessThanOrEqualTo(String value) {
            addCriterion("creator <=", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorLike(String value) {
            addCriterion("creator like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotLike(String value) {
            addCriterion("creator not like", value, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorIn(List<String> values) {
            addCriterion("creator in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotIn(List<String> values) {
            addCriterion("creator not in", values, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorBetween(String value1, String value2) {
            addCriterion("creator between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCreatorNotBetween(String value1, String value2) {
            addCriterion("creator not between", value1, value2, "creator");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNull() {
            addCriterion("ctime is null");
            return (Criteria) this;
        }

        public Criteria andCtimeIsNotNull() {
            addCriterion("ctime is not null");
            return (Criteria) this;
        }

        public Criteria andCtimeEqualTo(Timestamp value) {
            addCriterion("ctime =", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotEqualTo(Timestamp value) {
            addCriterion("ctime <>", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThan(Timestamp value) {
            addCriterion("ctime >", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("ctime >=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThan(Timestamp value) {
            addCriterion("ctime <", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("ctime <=", value, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeIn(List<Timestamp> values) {
            addCriterion("ctime in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotIn(List<Timestamp> values) {
            addCriterion("ctime not in", values, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andCtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("ctime not between", value1, value2, "ctime");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNull() {
            addCriterion("mtime is null");
            return (Criteria) this;
        }

        public Criteria andMtimeIsNotNull() {
            addCriterion("mtime is not null");
            return (Criteria) this;
        }

        public Criteria andMtimeEqualTo(Timestamp value) {
            addCriterion("mtime =", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotEqualTo(Timestamp value) {
            addCriterion("mtime <>", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThan(Timestamp value) {
            addCriterion("mtime >", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeGreaterThanOrEqualTo(Timestamp value) {
            addCriterion("mtime >=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThan(Timestamp value) {
            addCriterion("mtime <", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeLessThanOrEqualTo(Timestamp value) {
            addCriterion("mtime <=", value, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeIn(List<Timestamp> values) {
            addCriterion("mtime in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotIn(List<Timestamp> values) {
            addCriterion("mtime not in", values, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andMtimeNotBetween(Timestamp value1, Timestamp value2) {
            addCriterion("mtime not between", value1, value2, "mtime");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNull() {
            addCriterion("is_deleted is null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIsNotNull() {
            addCriterion("is_deleted is not null");
            return (Criteria) this;
        }

        public Criteria andIsDeletedEqualTo(Integer value) {
            addCriterion("is_deleted =", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotEqualTo(Integer value) {
            addCriterion("is_deleted <>", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThan(Integer value) {
            addCriterion("is_deleted >", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedGreaterThanOrEqualTo(Integer value) {
            addCriterion("is_deleted >=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThan(Integer value) {
            addCriterion("is_deleted <", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedLessThanOrEqualTo(Integer value) {
            addCriterion("is_deleted <=", value, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedIn(List<Integer> values) {
            addCriterion("is_deleted in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotIn(List<Integer> values) {
            addCriterion("is_deleted not in", values, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andIsDeletedNotBetween(Integer value1, Integer value2) {
            addCriterion("is_deleted not between", value1, value2, "isDeleted");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsIsNull() {
            addCriterion("agent_account_ids is null");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsIsNotNull() {
            addCriterion("agent_account_ids is not null");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsEqualTo(String value) {
            addCriterion("agent_account_ids =", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsNotEqualTo(String value) {
            addCriterion("agent_account_ids <>", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsGreaterThan(String value) {
            addCriterion("agent_account_ids >", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsGreaterThanOrEqualTo(String value) {
            addCriterion("agent_account_ids >=", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsLessThan(String value) {
            addCriterion("agent_account_ids <", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsLessThanOrEqualTo(String value) {
            addCriterion("agent_account_ids <=", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsLike(String value) {
            addCriterion("agent_account_ids like", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsNotLike(String value) {
            addCriterion("agent_account_ids not like", value, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsIn(List<String> values) {
            addCriterion("agent_account_ids in", values, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsNotIn(List<String> values) {
            addCriterion("agent_account_ids not in", values, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsBetween(String value1, String value2) {
            addCriterion("agent_account_ids between", value1, value2, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andAgentAccountIdsNotBetween(String value1, String value2) {
            addCriterion("agent_account_ids not between", value1, value2, "agentAccountIds");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeIsNull() {
            addCriterion("live_hard_income is null");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeIsNotNull() {
            addCriterion("live_hard_income is not null");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeEqualTo(Long value) {
            addCriterion("live_hard_income =", value, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeNotEqualTo(Long value) {
            addCriterion("live_hard_income <>", value, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeGreaterThan(Long value) {
            addCriterion("live_hard_income >", value, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("live_hard_income >=", value, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeLessThan(Long value) {
            addCriterion("live_hard_income <", value, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeLessThanOrEqualTo(Long value) {
            addCriterion("live_hard_income <=", value, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeIn(List<Long> values) {
            addCriterion("live_hard_income in", values, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeNotIn(List<Long> values) {
            addCriterion("live_hard_income not in", values, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeBetween(Long value1, Long value2) {
            addCriterion("live_hard_income between", value1, value2, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andLiveHardIncomeNotBetween(Long value1, Long value2) {
            addCriterion("live_hard_income not between", value1, value2, "liveHardIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeIsNull() {
            addCriterion("pre_pickup_income is null");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeIsNotNull() {
            addCriterion("pre_pickup_income is not null");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeEqualTo(Long value) {
            addCriterion("pre_pickup_income =", value, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeNotEqualTo(Long value) {
            addCriterion("pre_pickup_income <>", value, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeGreaterThan(Long value) {
            addCriterion("pre_pickup_income >", value, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeGreaterThanOrEqualTo(Long value) {
            addCriterion("pre_pickup_income >=", value, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeLessThan(Long value) {
            addCriterion("pre_pickup_income <", value, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeLessThanOrEqualTo(Long value) {
            addCriterion("pre_pickup_income <=", value, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeIn(List<Long> values) {
            addCriterion("pre_pickup_income in", values, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeNotIn(List<Long> values) {
            addCriterion("pre_pickup_income not in", values, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeBetween(Long value1, Long value2) {
            addCriterion("pre_pickup_income between", value1, value2, "prePickupIncome");
            return (Criteria) this;
        }

        public Criteria andPrePickupIncomeNotBetween(Long value1, Long value2) {
            addCriterion("pre_pickup_income not between", value1, value2, "prePickupIncome");
            return (Criteria) this;
        }
    }

    /**
     */
    public static class Criteria extends GeneratedCriteria {

        protected Criteria() {
            super();
        }
    }

    public static class Criterion {
        private String condition;

        private Object value;

        private Object secondValue;

        private boolean noValue;

        private boolean singleValue;

        private boolean betweenValue;

        private boolean listValue;

        private String typeHandler;

        public String getCondition() {
            return condition;
        }

        public Object getValue() {
            return value;
        }

        public Object getSecondValue() {
            return secondValue;
        }

        public boolean isNoValue() {
            return noValue;
        }

        public boolean isSingleValue() {
            return singleValue;
        }

        public boolean isBetweenValue() {
            return betweenValue;
        }

        public boolean isListValue() {
            return listValue;
        }

        public String getTypeHandler() {
            return typeHandler;
        }

        protected Criterion(String condition) {
            super();
            this.condition = condition;
            this.typeHandler = null;
            this.noValue = true;
        }

        protected Criterion(String condition, Object value, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.typeHandler = typeHandler;
            if (value instanceof List<?>) {
                this.listValue = true;
            } else {
                this.singleValue = true;
            }
        }

        protected Criterion(String condition, Object value) {
            this(condition, value, null);
        }

        protected Criterion(String condition, Object value, Object secondValue, String typeHandler) {
            super();
            this.condition = condition;
            this.value = value;
            this.secondValue = secondValue;
            this.typeHandler = typeHandler;
            this.betweenValue = true;
        }

        protected Criterion(String condition, Object value, Object secondValue) {
            this(condition, value, secondValue, null);
        }
    }
}