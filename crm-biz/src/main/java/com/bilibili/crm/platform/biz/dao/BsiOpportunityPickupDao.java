package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.BsiOpportunityPickupPo;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPickupPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface BsiOpportunityPickupDao {
    long countByExample(BsiOpportunityPickupPoExample example);

    int deleteByExample(BsiOpportunityPickupPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(BsiOpportunityPickupPo record);

    int insertBatch(List<BsiOpportunityPickupPo> records);

    int insertUpdateBatch(List<BsiOpportunityPickupPo> records);

    int insert(BsiOpportunityPickupPo record);

    int insertUpdateSelective(BsiOpportunityPickupPo record);

    int insertSelective(BsiOpportunityPickupPo record);

    List<BsiOpportunityPickupPo> selectByExample(BsiOpportunityPickupPoExample example);

    BsiOpportunityPickupPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") BsiOpportunityPickupPo record, @Param("example") BsiOpportunityPickupPoExample example);

    int updateByExample(@Param("record") BsiOpportunityPickupPo record, @Param("example") BsiOpportunityPickupPoExample example);

    int updateByPrimaryKeySelective(BsiOpportunityPickupPo record);

    int updateByPrimaryKey(BsiOpportunityPickupPo record);
}