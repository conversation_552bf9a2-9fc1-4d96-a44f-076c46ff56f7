package com.bilibili.crm.platform.biz.finance.facade;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.util.GsonUtils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.finance.common.FinanceBizType;
import com.google.gson.Gson;
import lombok.extern.slf4j.Slf4j;
import okhttp3.OkHttpClient;
import org.apache.http.HttpResponse;
import org.apache.http.client.methods.HttpPost;
import org.apache.http.entity.ContentType;
import org.apache.http.entity.StringEntity;
import org.apache.http.impl.client.CloseableHttpClient;
import org.apache.http.impl.client.HttpClients;
import org.apache.http.util.EntityUtils;
import org.apache.logging.log4j.util.Strings;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import java.io.IOException;
import java.nio.charset.StandardCharsets;
import java.util.Base64;
import java.util.Objects;

import static com.bilibili.crm.platform.biz.finance.common.FinanceCenterConstant.DISCOVERY_ID;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-03-09 11:44:20
 * @description:
 **/

@Slf4j
@Service
public class FinanceCenterFacade {

    @Value("${finance.center.token.url:http://ee.bilibili.co/api/payproxy/token}")
    private String tokenUrl;

    @Value("${finance.center.relay.url:http://ee.bilibili.co/api/payproxy/relay}")
    private String relayUrl;

    @Value("${finance.center.secret.key:WCChnYWuvvC86263BdQnLvzelCZayRRc}")
    private String financeCenterSecret;

    @Value("${finance.center.effect.secret.key:secret}")
    private  String effectFinanceCenterSecret;

    @Value("${finance.center.effect.grant.type:client_credentials}")
    private  String effectFinanceCenterGrantType;

    @Value("${finance.center.effect.client.id:client-qx}")
    private  String effectFinanceCenterClientId;

    @Value("${finance.center.effect.url:http://************:8080}")
    private  String effectUrl;
    public static final Gson gson = GsonUtils.getGson();


    public String buildToken() {
        long time = System.currentTimeMillis();
        String string = DISCOVERY_ID + ":" + financeCenterSecret + ":" + time;
        byte[] bytes = string.getBytes(StandardCharsets.UTF_8);
        String askString = Base64.getEncoder().encodeToString(bytes);
        //定义发送数据
        JSONObject param = new JSONObject();
        param.put("askString", askString);
        //定义接收数据
        JSONObject result = new JSONObject();

        HttpPost httpPost = new HttpPost(tokenUrl);
        CloseableHttpClient client = HttpClients.createDefault();
        //请求参数转JOSN字符串
        StringEntity entity = new StringEntity(param.toString(), "UTF-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setEntity(entity);
        try {
            HttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = JSON.parseObject(EntityUtils.toString(response.getEntity(), "UTF-8"));
            }
            if (result.getString("message").equals("success")) {
                return result.getJSONObject("data").getString("token");
            }
            log.error("FinanceCenterFacadeBuildTokenError:{}", gson.toJson(result));
            client.close();
        } catch (IOException e) {
            e.printStackTrace();
            result.put("error", "连接错误！");
        }
        return Strings.EMPTY;
    }


    public String doPost(String httpUrl, String paramName, String paramValue, String token)  {
        HttpPost httpPost = new HttpPost(relayUrl + httpUrl);
        CloseableHttpClient client = HttpClients.createDefault();
        JSONObject param = new JSONObject();
        JSONObject result = new JSONObject();
        param.put(paramName, paramValue);
        //请求参数转JOSN字符串
        StringEntity entity = new StringEntity(param.toString(), "UTF-8");
        entity.setContentEncoding("UTF-8");
        entity.setContentType("application/json");
        httpPost.setHeader("Authorization", token);
        httpPost.setEntity(entity);
        log.info("executePost:{}", httpPost);
        try {
            HttpResponse response = client.execute(httpPost);
            log.info("executePost:{}", response);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = JSON.parseObject(EntityUtils.toString(response.getEntity(), "UTF-8"));
            } else {
                log.error("FinanceCenterFacadeInfo_have_error:{}", gson.toJson(result));
                throw new RuntimeException("和中台交互报错");
            }
            log.info("FinanceCenterFacadeInfo:{}", gson.toJson(result));
            client.close();
            return result.toJSONString();
        } catch (IOException e) {
            log.error("FinanceCenterFacadeInfo_have_error:", e);
           throw new RuntimeException("和中台交互报错");
        }
    }

    /**
     * 构建效果广告消耗和充值数据推送给财务中台的令牌
     */
    public String buildEffectToken() {
        HttpPost httpPost = new HttpPost(effectUrl+"/oauth/oauth/token");
        // 设置请求参数
        String param = "grant_type=" + effectFinanceCenterGrantType + "&" +
                "client_id=" + effectFinanceCenterClientId + "&" +
                "client_secret=" + effectFinanceCenterSecret;
        StringEntity entity = new StringEntity(param, ContentType.APPLICATION_FORM_URLENCODED);
        httpPost.setEntity(entity);
        JSONObject result = new JSONObject();
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = JSON.parseObject(EntityUtils.toString(response.getEntity(), "UTF-8"));
                String token = result.getString("access_token");
                if (Objects.nonNull(token)) {
                    return token;
                }
            }
            if (response.getStatusLine().getStatusCode() == 401) {
                log.error("Issued_Finance,FinanceCenterFacadeBuildEffectToken-Unauthorized:{}", response.getStatusLine());
                return Strings.EMPTY;
            }
            log.error("Issued_Finance,FinanceCenterFacadeBuildEffectTokenError:{}", response.getStatusLine());
        } catch (IOException e) {
            e.printStackTrace();
            result.put("error", "连接错误！");
        }
        return Strings.EMPTY;
    }

    /**
     * 效果广告消耗和充值数据推送给财务中台
     *
     * @param financeBizType 业务类型
     * @param paramValue     参数值
     * @param token          令牌
     */
    public boolean doEffectPost(FinanceBizType financeBizType, String paramValue, String token) {
        log.info("Issued_Finance,effectUrl:{}", effectUrl);
        boolean postResult = true;
        String uri = effectUrl + financeBizType.getUrl();
        HttpPost httpPost = new HttpPost(uri);
        StringEntity entity = new StringEntity(paramValue, ContentType.APPLICATION_JSON);
        httpPost.setHeader("Authorization", token);
        httpPost.setEntity(entity);
        JSONObject result = new JSONObject();
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = JSON.parseObject(EntityUtils.toString(response.getEntity(), "UTF-8"));
                if(!result.getBoolean("successFlag")){
                    postResult = false;
                }
            }
            AlarmHelper.log("DoPostEffect", postResult, response.getStatusLine().getStatusCode(),
                    financeBizType.getDesc(), uri, token, paramValue, result);
        } catch (IOException e) {
            postResult = false;
            result.put("error", "连接错误！");
        }
        return postResult;
    }

    public static void main(String[] args) {
        HttpPost httpPost = new HttpPost("http://************:8080/oauth/oauth/token");
        // 设置请求参数
        String param = "grant_type=" + "client_credentials" + "&" +
                "client_id=" + "client-qx" + "&" +
                "client_secret=" + "secret";
        StringEntity entity = new StringEntity(param, ContentType.APPLICATION_FORM_URLENCODED);
        httpPost.setEntity(entity);
        JSONObject result = new JSONObject();
        try (CloseableHttpClient client = HttpClients.createDefault()) {
            HttpResponse response = client.execute(httpPost);
            if (response.getStatusLine().getStatusCode() == 200) {
                result = JSON.parseObject(EntityUtils.toString(response.getEntity(), "UTF-8"));
                String token = result.getString("access_token");
                if (Objects.nonNull(token)) {
                    System.out.println(token);
                }
            }
            if (response.getStatusLine().getStatusCode() == 401) {
                log.error("FinanceCenterFacadeBuildEffectToken-Unauthorized:{}", response.getStatusLine());
                return;
            }
            log.error("FinanceCenterFacadeBuildEffectTokenError:{}", response.getStatusLine());
        } catch (IOException e) {
            e.printStackTrace();
            result.put("error", "连接错误！");
        }
    }
}
