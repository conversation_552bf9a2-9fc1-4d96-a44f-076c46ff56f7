package com.bilibili.crm.platform.biz.repo.idempotent.impl;

import com.bilibili.crm.platform.biz.dao.IdempotentDao;
import com.bilibili.crm.platform.biz.po.IdempotentPo;
import com.bilibili.crm.platform.biz.po.IdempotentPoExample;
import com.bilibili.crm.platform.biz.repo.idempotent.IdempotentRepository;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Repository;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2024/2/29
 */
@Repository
public class IdempotentRepositoryImpl implements IdempotentRepository {

    @Resource
    private IdempotentDao idempotentDao;

    @Override
    public long insert(String bizNo, String bizType) {
        IdempotentPo idempotentPo = new IdempotentPo();
        idempotentPo.setBizNo(bizNo);
        idempotentPo.setBizType(bizType);
        idempotentDao.insertSelective(idempotentPo);
        return idempotentPo.getId();
    }

    @Override
    public IdempotentPo queryByBizNoType(String bizNo, String bizType) {
        IdempotentPoExample example = new IdempotentPoExample();
        IdempotentPoExample.Criteria criteria = example.createCriteria();
        criteria.andBizTypeEqualTo(bizType);
        criteria.andBizNoEqualTo(bizNo);
        List<IdempotentPo> idempotentPos = idempotentDao.selectByExample(example);
        if (CollectionUtils.isEmpty(idempotentPos)) {
            return null;
        }
        return idempotentPos.get(0);
    }

    @Override
    public boolean updateExtInfoById(String extInfo, long id) {
        IdempotentPo idempotentPo = new IdempotentPo();
        idempotentPo.setExtInfo(extInfo);
        idempotentPo.setId(id);
        idempotentDao.updateByPrimaryKeySelective(idempotentPo);
        return false;
    }

    @Override
    public long save(IdempotentPo idempotentPo) {
        idempotentDao.insertSelective(idempotentPo);
        return idempotentPo.getId();
    }
}
