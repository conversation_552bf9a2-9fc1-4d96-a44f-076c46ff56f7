package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccAccountBizMidMappingPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 广告主账号id
     */
    private Integer accountId;

    /**
     * 主站mid
     */
    private Long mid;

    /**
     * 业务类型 1蓝v账号商业起飞 2游戏 3电商 4花火MCN
     */
    private Integer bizType;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除: 0 否 1是
     */
    private Integer isDeleted;

    private static final long serialVersionUID = 1L;
}