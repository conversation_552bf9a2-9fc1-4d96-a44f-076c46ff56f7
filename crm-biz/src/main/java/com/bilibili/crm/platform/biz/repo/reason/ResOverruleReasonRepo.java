package com.bilibili.crm.platform.biz.repo.reason;

import com.bilibili.adp.common.DbTable;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.enums.Status;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.log.dto.OperationType;
import com.bilibili.adp.resource.api.remark.NewOverruleReasonDto;
import com.bilibili.adp.resource.api.remark.RemarkCategoryDto;
import com.bilibili.crm.platform.biz.dao.ResOverruleReasonDao;
import com.bilibili.crm.platform.biz.po.ResOverruleReasonPo;
import com.bilibili.crm.platform.biz.po.ResOverruleReasonPoExample;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;

import java.util.Collections;
import java.util.List;
import java.util.Objects;

/**
 * @Description 驳回理由配置
 * <AUTHOR>
 * @DATE 2022/9/20 7:27 下午
 **/
@Repository
public class ResOverruleReasonRepo {

    @Autowired
    private ResOverruleReasonDao resOverruleReasonDao;

    public List<ResOverruleReasonPo> getReasonByCid(Integer categoryId){
        if(!Utils.isPositive(categoryId)){
            return Collections.emptyList();
        }
        ResOverruleReasonPoExample example = new ResOverruleReasonPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andStatusEqualTo(Status.VALID.getCode())
                .andCIdEqualTo(categoryId);
        return resOverruleReasonDao.selectByExample(example);
    }

    public Integer save(ResOverruleReasonPo po, Integer remarkType) {
        if(Objects.isNull(po) || !Utils.isPositive(remarkType)){
            return 0;
        }
        po.setType(remarkType);
        po.setStatus(Status.VALID.getCode());
        resOverruleReasonDao.insertSelective(po);

        return po.getId();
    }

}
