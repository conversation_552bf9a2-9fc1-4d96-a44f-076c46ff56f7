package com.bilibili.crm.platform.biz.new_platform.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DeductionInfoBillsAllocatePo implements Serializable {
    /**
     * 自增主键ID
     */
    private Long id;

    /**
     * 合同对应的账期id
     */
    private Long contractBillPeriodId;

    /**
     * 账期金额
     */
    private Long billAmount;

    /**
     * 分摊账期金额
     */
    private Long allocateBillAmount;

    /**
     * 分摊来源 1-银企直连 2-返点核算
     */
    private Integer allocateSource;

    /**
     * 抵扣id
     */
    private Integer deductId;

    /**
     * crm合同号
     */
    private Integer crmContractId;

    /**
     * 抵扣收款时间戳
     */
    private Long deductTime;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 数据生成时间
     */
    private Timestamp logDate;

    /**
     * 环境
     */
    private String env;

    private static final long serialVersionUID = 1L;
}