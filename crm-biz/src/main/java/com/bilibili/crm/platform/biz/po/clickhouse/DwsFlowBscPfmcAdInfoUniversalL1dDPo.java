package com.bilibili.crm.platform.biz.po.clickhouse;

import java.io.Serializable;
import java.sql.Timestamp;
import javax.persistence.Column;
import javax.persistence.GeneratedValue;
import javax.persistence.Id;
import javax.persistence.Table;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.hibernate.validator.constraints.NotEmpty;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DwsFlowBscPfmcAdInfoUniversalL1dDPo implements Serializable {
    @Id
    @GeneratedValue
    private Long id;

    /**
     * 账户ID
     */
    @NotEmpty
    private String accountId;

    /**
     * 账户名
     */
    @NotEmpty
    private String accountName;

    /**
     * 品牌ID
     */
    @NotEmpty
    private Long productId;

    /**
     * 品牌名
     */
    @NotEmpty
    private String productName;

    /**
     * 集团ID
     */
    @NotEmpty
    private Long groupId;

    /**
     * 集团名
     */
    @NotEmpty
    private String groupName;

    /**
     * 品效类型
     */
    @NotEmpty
    private Long advertisementType;

    /**
     * 模版名称
     */
    @NotEmpty
    private String templateName;

    /**
     * 模版ID
     */
    @NotEmpty
    private Long templateId;

    /**
     * 计费类型
     */
    @NotEmpty
    private Long salesModel;

    /**
     * 交易产品类型
     */
    @NotEmpty
    private Long salesCategory;

    /**
     * 产品大类
     */
    @NotEmpty
    private Long productType;

    /**
     * 内外广
     */
    @NotEmpty
    private Long isInner;

    /**
     * 资源平台
     */
    @NotEmpty
    private Long platformId;

    /**
     * 资源终端
     */
    @NotEmpty
    private Long srcPlatform;

    /**
     * 资源位类型
     */
    @NotEmpty
    private Long srcType;

    /**
     * 位置名称
     */
    @NotEmpty
    private String resName;

    /**
     * 页面名称
     */
    @NotEmpty
    private String pageName;

    /**
     * 售卖渠道
     */
    @NotEmpty
    private Long salesChannel;

    /**
     * 资源分类
     */
    @NotEmpty
    private Long srcCategory;

    /**
     * 1级行业标签
     */
    @NotEmpty
    private String commerceCategoryFirstName;

    /**
     * 2级行业标签
     */
    @NotEmpty
    private String commerceCategorySecondName;

    /**
     * 3级行业标签
     */
    @NotEmpty
    private String thirdIndustryTagName;

    /**
     * 客户ID
     */
    @NotEmpty
    private Long customerId;

    /**
     * 客户名称
     */
    @NotEmpty
    private String customerName;

    /**
     * 代理商
     */
    @NotEmpty
    private String crmAgentName;

    /**
     * 部门名称
     */
    @NotEmpty
    private String departmentName;

    /**
     * 总曝光
     */
    @NotEmpty
    private Double pvSum;

    /**
     * 总点击
     */
    @NotEmpty
    private Double clickSum;

    /**
     * 总收入
     */
    @NotEmpty
    private Double costMilliSum;

    /**
     * 起飞类型
     */
    @NotEmpty
    private Long flyType;

    /**
     * 分区
     */
    @NotEmpty
    private Timestamp logDate;

    /**
     * 	支付方式
     */
    @NotEmpty
    private Integer userFlyPayWay;

    private static final long serialVersionUID = 1L;
}