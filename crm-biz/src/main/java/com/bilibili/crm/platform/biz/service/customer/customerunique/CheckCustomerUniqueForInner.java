package com.bilibili.crm.platform.biz.service.customer.customerunique;

import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;

/**
 * 内广唯一性校验
 * <AUTHOR>
 * @date 2020/9/24 10:39 上午
 */
@Slf4j
@Component(value = "checkCustomerUniqueForInner")
public class CheckCustomerUniqueForInner implements ICheckCustomerUnique {

    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private CheckCustomerUniqueParamsValidator checkCustomerUniqueParamsValidator;

    /**
     * 内广
     * 	1. 新增
     * 		不存在，则通过；
     * 		存在，不通过；
     * 	2. 修改
     * 		不存在，则通过；
     * 		存在
     * 			是否有非自己
     * 				没有通过
     * 				有，不通过；
     * @param customerUniqueInfo
     * @return
     */
    @Override
    public Integer checkUnique(CustomerUniqueInfo customerUniqueInfo) {
        checkCustomerUniqueParamsValidator.checkUniqueParams(customerUniqueInfo);

        List<CustomerPo> customerPos = customerRepo.queryEnableListByUserName(customerUniqueInfo.getUserName());

        if (customerUniqueInfo.getIfUpdate()) {
            // 修改
            if (CollectionUtils.isEmpty(customerPos)) {
                return null;
            } else {
                if (customerUniqueInfo.getIfNeedCheck()) {
                    // 存在一个不是自己的
                    if (customerPos.stream().map(t -> t.getId()).anyMatch(t -> !t.equals(customerUniqueInfo.getCustomerId()))) {
                        log.info("=====> checkUnique[内部-修改,name存在其他id的,不通过], info:{}", customerUniqueInfo);
                        throw new ServiceRuntimeException("客户信息不满足唯一性");
                    }
                }
            }
        } else {
            // 新增
            if (CollectionUtils.isEmpty(customerPos)) {
                return null;
            } else {
                if (customerUniqueInfo.getIfReUse()) {
                    return customerPos.get(0).getId();
                }
                if (customerUniqueInfo.getIfNeedCheck()) {
                    log.info("=====> checkUnique[内部-新增,name存在,不通过], info:{}", customerUniqueInfo);
                    throw new ServiceRuntimeException("客户信息不满足唯一性");
                }
            }
        }

        return null;
    }
}
