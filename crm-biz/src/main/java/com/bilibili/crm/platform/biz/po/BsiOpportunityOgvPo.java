package com.bilibili.crm.platform.biz.po;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.sql.Timestamp;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class BsiOpportunityOgvPo implements Serializable {
    /**
     * 主键id
     */
    private Long id;

    /**
     * 商机id
     */
    private Integer bsiOpportunityId;

    /**
     * 商机创建者email前缀
     */
    private String bsiOpportunityCreator;

    /**
     * 商机OGV领导Leader姓名
     */
    private String ogvLeaderName;

    /**
     * 商机OGV领导Leader的email前缀
     */
    private String ogvLeaderEmail;

    /**
     * 商机OGV协作专员姓名
     */
    private String ogvActorName;

    /**
     * 商机OGV协作专员的email前缀
     */
    private String ogvActorEmail;

    /**
     * ogv主站分区
     */
    private String mainPartId;

    /**
     * OGV协作处理的状态 详情见枚举 BsiOppOGVStatus
     */
    private Integer status;

    /**
     * 驳回原因
     */
    private String rejectReason;

    /**
     * 是否删除 0 未删除 1 已删除
     */
    private Integer isDeleted;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    private static final long serialVersionUID = 1L;
}