package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmFlowContractDeductRelationPo implements Serializable {
    /**
     * id
     */
    private Integer id;

    /**
     * 关联 id: 回款流水 id/ 核算 id
     */
    private Integer flowId;

    /**
     * 合同id
     */
    private Integer contractId;

    /**
     * 合同帐期id
     */
    private Long contractBillPeriodId;

    /**
     * 抵扣金额
     */
    private Long deductAmount;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除: 0 否 1是
     */
    private Integer isDeleted;

    /**
     * 处理批次号
     */
    private Long optBatchNo;

    /**
     * 操作人
     */
    private String operator;

    /**
     * 抵扣类型: 1-银企直连 2-返点核算
     */
    private Integer type;

    /**
     * 回退来源抵扣ID
     */
    private Integer deductSourceId;

    /**
     * 已回退抵扣金额（分）
     */
    private Long alreadyReversedDeductAmount;


    private static final long serialVersionUID = 1L;
}
