package com.bilibili.crm.platform.biz.dao;

import com.bilibili.crm.platform.biz.po.CrmBrandIncomeConfirmationPo;
import com.bilibili.crm.platform.biz.po.CrmBrandIncomeConfirmationPoExample;
import java.util.List;
import org.apache.ibatis.annotations.Param;

public interface CrmBrandIncomeConfirmationDao {
    long countByExample(CrmBrandIncomeConfirmationPoExample example);

    int deleteByExample(CrmBrandIncomeConfirmationPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insert(CrmBrandIncomeConfirmationPo record);

    int insertSelective(CrmBrandIncomeConfirmationPo record);

    List<CrmBrandIncomeConfirmationPo> selectByExample(CrmBrandIncomeConfirmationPoExample example);

    CrmBrandIncomeConfirmationPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CrmBrandIncomeConfirmationPo record, @Param("example") CrmBrandIncomeConfirmationPoExample example);

    int updateByExample(@Param("record") CrmBrandIncomeConfirmationPo record, @Param("example") CrmBrandIncomeConfirmationPoExample example);

    int updateByPrimaryKeySelective(CrmBrandIncomeConfirmationPo record);

    int updateByPrimaryKey(CrmBrandIncomeConfirmationPo record);
}