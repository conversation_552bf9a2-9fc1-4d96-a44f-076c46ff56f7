package com.bilibili.crm.platform.biz.po.clickhouse;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.Objects;

/**
 * 业绩查询含有销售的明细信息PO
 *
 * <AUTHOR>
 * date 2024/8/23 15:23.
 * Contact: <EMAIL>.
 */
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AdsCrmCommerceKpiWithSaleInfoExportADPO implements Serializable {
    private static final long serialVersionUID = 6215619154125808094L;
    private String amount_date;
    private String closing_date;
    private String new_customer_compl_date;//新客完成时间
    private Double kpi_amount; //单位元
    private Long is_checking;
    private String amount_type;
    private String bill_source;
    private Long personal_fly_cash_consume;
    private Long personal_fly_fly_support_consume;//（毫分）
    private Long contract_package_amount;
    private Long contract_amount;
    //未录入点位
    private Long order_amount;
    private Double order_amount_after_discount;
    //品牌未录入点位
    //花火未录入点位
    private Long cash_consume;
    private Long credit_consume;
    private Long red_packet_consume;
    private Long special_red_packet_consume;

    private String product_type;
    private String product_first_category_type;
    private String product_second_category_type;
    private String product_third_category_type;
    private String first_category_name;
    private String second_category_name;
    private String contract_number;
    private String contract_name;
    private String contract_ctime;
    private String contract_begin_time;
    private String contract_end_time;
    private String contract_type;
    private String contract_status;//合同状态
    private String contract_archive_status;//存档状态
    private String contract_creator;
    private String contract_period_start;
    private String contract_period_end;
    private String contract_execute;
    private String order_project_item_name;
    private String order_department_name;
    private String order_second_department_name;
    private String project_department_name;
    private String new_order_origin_project_type;
    private String new_order_project_first_level_category_name;
    private String new_order_project_second_level_category_name;
    private String new_order_project_third_level_category_name;
    private String new_order_project_fourth_level_category_name;
    private String project_general_type;
    private String merchant_marketing_team_id;
    private String project_create_type;
    private String project_first_area_name;
    private String project_second_area_name;
    private String order_id;
    private String order_name;
    private String order_type;
    private String order_ctime;
    private String order_begin_time;
    private String order_end_time;
    private String order_status;
    private String order_resource_type;
    private String amount_id;//花火账单ID 仅 产品三级_new = 花火商单 时取值
    private String spark_order_status;
    private String spark_order_no;
    private String spark_task_id;
    private String spark_task_name;
    private String spark_cooperation_type;
    private String spark_ctime;
    private String spark_online_time;
    private String spark_actual_online_time;
    private String spark_status_mtime;
    private String spark_partial_refund_time;
    private String spark_av_id;
    private String spark_upper_mid;
    private String spark_nickname;
    private String spark_out_task_cm;
    private String spark_out_task_no;
    private String spark_tag;
    private String spark_bvid;
    private String xst_task_id;
    private String xst_begin_date;
    private String xst_end_date;
    private String account_id;
    private String account_name;
    private String customer_id;
    private String customer_name;
    private String customer_united_first_industry_name;
    private String customer_united_second_industry_name;
    private String customer_united_third_industry_name;
    private String acc_united_first_industry_name;
    private String acc_united_second_industry_name;
    private String acc_united_third_industry_name;
    private String commerce_category_first_name;
    private String commerce_category_second_name;
    private String group_id;
    private String group_name;
    private Long parent_group_id;
    private String parent_group_name;
    private String product_id;
    private String product_name;
    private String department_id;
    private String department_name;
    private String cus_biz_industry_category_first_name;
    private String cus_biz_industry_category_second_name;
    private String cus_commerce_category_first_name;
    private String cus_commerce_category_second_name;
    private String user_type;
    private String agent_id;
    private String agent_name;
    private String agent_customer_id;
    private String agent_customer_name;
    private String opt_agent_id;
    private String opt_agent_name;
    private Long opt_agent_group_id;
    private String opt_agent_group_name;
    private String agent_user_type;
    private Long agent_group_id;
    private String agent_group_name;
    private String kpi_zk_sale_name;
    private String kpi_qd_sale_name;
    private String xg_sale_name;//效果运营
    private String kpi_zk_sale_group_name_v1_list;
    private String kpi_zk_sale_group_name_v1;
    private String kpi_qd_sale_group_name_v1_list;
    private String kpi_qd_sale_group_name_v1;
    private String yjgh_zk_sale_group_name_new;
    private String yjgh_zk_sale_group_parent_name_new;
    private String yjgh_qd_sale_group_name;

    private String busi_account_date;
    private String unique_id;
    private String customer_service_type;
    private Long group_is_dealer;
    private Long crm_contract_sign_subject_id;

    // 重写equals方法
    @Override
    public boolean equals(Object o) {
        if (this == o) {
            return true;
        }
        if (o == null || getClass() != o.getClass()) {
            return false;
        }
        AdsCrmCommerceKpiWithSaleInfoExportADPO that = (AdsCrmCommerceKpiWithSaleInfoExportADPO) o;
        return Objects.equals(unique_id, that.unique_id);
    }

    // 重写hashCode方法
    @Override
    public int hashCode() {
        return Objects.hash(unique_id);
    }

}

