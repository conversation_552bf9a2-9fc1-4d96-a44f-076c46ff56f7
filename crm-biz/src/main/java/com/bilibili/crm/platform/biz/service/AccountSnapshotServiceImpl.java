package com.bilibili.crm.platform.biz.service;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.sales.bo.CrmSaleBO;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.platform.api.account.dto.AccountSnapshotDTO;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.account.enums.AccountSnapshotMappingTypeEnum;
import com.bilibili.crm.platform.api.account.service.IAccountSnapshotService;
import com.bilibili.crm.platform.biz.common.ExportBizUtils;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CrmAgentDao;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerDao;
import com.bilibili.crm.platform.biz.dao.*;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.common.IsValid;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.*;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * date 2023/10/20 11:32.
 * Contact: <EMAIL>.
 */
@Service
@Slf4j
public class AccountSnapshotServiceImpl implements IAccountSnapshotService {

    @Autowired
    private CrmPlaceOrderAccountSnapshotDao crmPlaceOrderAccountSnapshotDao;
    @Autowired
    private AccAccountDao accountDao;
    @Autowired
    private CrmSaleSeaBelongingDao crmSaleSeaBelongingDao;
    @Autowired
    private CrmAgentDao crmAgentDao;
    @Autowired
    private CrmSaleDao crmSaleDao;
    @Autowired
    private CrmSaleGroupDao crmSaleGroupDao;
    @Autowired
    private ExportBizUtils exportBizUtils;
    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private AccCompanyGroupDao accCompanyGroupDao;
    @Autowired
    private AccProductLineDao accProductLineDao;
    @Autowired
    private AccProductDao accProductDao;
    @Autowired
    private CrmDepartmentDao departmentDao;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;

    public CrmPlaceOrderAccountSnapshotPo DTO2PO(AccountSnapshotDTO dto) {
        return CrmPlaceOrderAccountSnapshotPo.builder()
                .accountId(dto.getAccountId())
                .mappingId(dto.getMappingId())
                .mappingType(dto.getMappingType().getCode())
                .mappingValidStartTime(dto.getMappingValidStartTime())
                .accCustomerId(dto.getAccCustomerId())
                .accDepartmentId(dto.getAccDepartmentId())
                .accGroupId(dto.getAccGroupId())
                .accProductId(dto.getAccProductId())
                .accProductLineId(dto.getAccProductLineId())
                .accDirectSaleId(dto.getAccDirectSaleId())
                .accDirectSaleUpGroupId(dto.getAccDirectSaleUpGroupId())
                .accDirectSaleUpTeamId(dto.getAccDirectSaleUpTeamId())
                .agentAccountId(dto.getAgentAccountId())
                .agentCustomerId(dto.getAgentCustomerId())
                .agentChannelSaleId(dto.getAgentChannelSaleId())
                .agentChannelSaleUpGroupId(dto.getAgentChannelSaleUpGroupId())
                .agentChannelSaleUpTeamId(dto.getAgentChannelSaleUpTeamId())
                .ctime(dto.getCtime())
                .mtime(dto.getMtime())
                .isDeleted(dto.getIsDeleted())
                .build();
    }

    public List<CrmSaleSeaBelongingPo> getCrmSaleSeaBelongingPoList(Integer accountId, BiliUserType userType) {
        CrmSaleSeaBelongingPoExample belongingPoExample = new CrmSaleSeaBelongingPoExample();
        belongingPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId)
                .andBiliUserTypeEqualTo(userType.getCode())
                .andIsCurrentEqualTo(IsValid.TRUE.getCode());
        return crmSaleSeaBelongingDao.selectByExample(belongingPoExample);
    }

    public Map<Integer, Integer> getSaleIdGroupIdMap(List<Integer> saleIds) {
        CrmSalePoExample salePoExample = new CrmSalePoExample();
        salePoExample.createCriteria().andIdIn(saleIds);
        List<CrmSalePo> salePoList = crmSaleDao.selectByExample(salePoExample);
        saleGroupMappingServiceImpl.fillSalePoLevel(salePoList);
        return salePoList.stream().collect(Collectors.toMap(CrmSalePo::getId, CrmSalePo::getGroupId));
    }

    public Map<Integer, Integer> getSaleGroupIdParentIdMap(List<Integer> groupIds) {
        CrmSaleGroupPoExample saleGroupPoExample = new CrmSaleGroupPoExample();
        saleGroupPoExample.createCriteria().andIdIn(groupIds);
        List<CrmSaleGroupPo> saleGroupPoList = crmSaleGroupDao.selectByExample(saleGroupPoExample);
        return saleGroupPoList.stream().collect(Collectors.toMap(CrmSaleGroupPo::getId, CrmSaleGroupPo::getParentId));
    }

    @Override
    public int addAccountSnapshotByDTO(AccountSnapshotDTO dto) {
        return crmPlaceOrderAccountSnapshotDao.insertSelective(DTO2PO(dto));
    }

    @Override
    public int addAccountSnapshotByAccountId(Integer accountId, String mappingId, AccountSnapshotMappingTypeEnum mappingType, Long mappingValidStartTime) {
        Assert.notNull(accountId, "accountId不能为空");
        Assert.notNull(mappingId, "mappingId不能为空");
        Assert.notNull(mappingType, "mappingType不能为空");
        Assert.notNull(mappingValidStartTime, "mappingValidStartTime不能为空");
        AccAccountPo po = accountDao.selectByPrimaryKey(accountId);
        Assert.notNull(po, "账户不存在");
        AccountSnapshotDTO dto = new AccountSnapshotDTO();
        dto.setAccountId(accountId);
        dto.setMappingId(mappingId);
        dto.setMappingType(mappingType);
        dto.setMappingValidStartTime(new Timestamp(mappingValidStartTime));
        dto.setAccCustomerId(po.getCustomerId());
        dto.setAccDepartmentId(po.getDepartmentId());
        dto.setAccGroupId(po.getGroupId());
        dto.setAccProductId(po.getProductId());
        dto.setAccProductLineId(po.getProductLineId());
        List<CrmSaleSeaBelongingPo> saleSeaBelongingPoList = getCrmSaleSeaBelongingPoList(accountId, BiliUserType.STRAIGHT_MANAGER);
        if (CollectionUtils.isNotEmpty(saleSeaBelongingPoList)) {
            List<Integer> saleIds = saleSeaBelongingPoList.stream().map(CrmSaleSeaBelongingPo::getSaleId).distinct().collect(Collectors.toList());
            dto.setAccDirectSaleId(saleIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
            Map<Integer, Integer> salePoMap = getSaleIdGroupIdMap(saleIds);
            dto.setAccDirectSaleUpGroupId(saleIds.stream().map(e -> salePoMap.getOrDefault(e, 0)).map(Objects::toString).collect(Collectors.joining(",")));
            List<Integer> groupIds = new ArrayList<>(salePoMap.values());
            Map<Integer, Integer> saleGroupPoMap = getSaleGroupIdParentIdMap(groupIds);
            dto.setAccDirectSaleUpTeamId(groupIds.stream().map(e -> saleGroupPoMap.getOrDefault(e, 0)).map(Objects::toString).collect(Collectors.joining(",")));
        }
        if (Utils.isPositive(po.getDependencyAgentId())) {
            CrmAgentPo agentPo = crmAgentDao.selectByPrimaryKey(po.getDependencyAgentId());
            dto.setAgentAccountId(agentPo.getSysAgentId());
            AccAccountPo agentAccountPo = accountDao.selectByPrimaryKey(agentPo.getSysAgentId());
            dto.setAgentCustomerId(agentAccountPo.getCustomerId());
            List<CrmSaleSeaBelongingPo> agentSaleSeaBelongingPoList = getCrmSaleSeaBelongingPoList(agentPo.getSysAgentId(), BiliUserType.CHANNEL_MANAGER);
            if (CollectionUtils.isNotEmpty(agentSaleSeaBelongingPoList)) {
                List<Integer> saleIds = agentSaleSeaBelongingPoList.stream().map(CrmSaleSeaBelongingPo::getSaleId).distinct().collect(Collectors.toList());
                dto.setAgentChannelSaleId(saleIds.stream().map(Objects::toString).collect(Collectors.joining(",")));
                Map<Integer, Integer> salePoMap = getSaleIdGroupIdMap(saleIds);
                dto.setAgentChannelSaleUpGroupId(saleIds.stream().map(e -> salePoMap.getOrDefault(e, 0)).map(Objects::toString).collect(Collectors.joining(",")));
                List<Integer> groupIds = new ArrayList<>(salePoMap.values());
                Map<Integer, Integer> saleGroupPoMap = getSaleGroupIdParentIdMap(groupIds);
                dto.setAgentChannelSaleUpTeamId(groupIds.stream().map(e -> saleGroupPoMap.getOrDefault(e, 0)).map(Objects::toString).collect(Collectors.joining(",")));
            }
        }
        return addAccountSnapshotByDTO(dto);
    }
}

