package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.math.BigDecimal;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CrmContractPo implements Serializable {
    /**
     * 自增id
     */
    private Integer id;

    /**
     * 合同号（14到18位）
     */
    private Long contractNumber;

    /**
     * 客户id
     */
    private Integer accountId;

    /**
     * 合同名称
     */
    private String name;

    /**
     * 合同类型 0 普通合同 1 up主制作合同
     */
    private Integer type;

    /**
     * 代理商id
     */
    private Integer agentId;

    /**
     * 法务审批时填写的合同ID，用于线下合同流转的标识
     */
    private String legalContractId;

    /**
     * 开始时间
     */
    private Timestamp beginTime;

    /**
     * 结束时间
     */
    private Timestamp endTime;

    /**
     * 0-未上传、1-已上传待审核、2-已上传审核拒绝、3-已存档
     */
    private Integer archiveStatus;

    /**
     * 0编辑中、1待审核、2已拒绝、3审核通过、4执行中、5可执行完成、6待收款、7已完成、8禁用
     */
    private Integer status;

    /**
     * 备注
     */
    private String remark;

    /**
     * 合同金额（单位 分）
     */
    private Long amount;

    /**
     * pd金额
     */
    private Long pdAmount;

    /**
     * 整包金额
     */
    private Long packageAmount;

    /**
     * 金额备注
     */
    private String amountRemark;

    /**
     * 审核备注
     */
    private String reviewRemark;

    /**
     * 版本号
     */
    private Integer version;

    /**
     * 创建时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 软删除0未删除1 删除
     */
    private Integer isDeleted;

    /**
     * 创建人
     */
    private String creator;

    /**
     * 财务结算title 代理商id
     */
    private Integer financeTitleAgentId;

    /**
     * 合同对应的配送政策
     */
    private Integer distribution;

    /**
     * 合同对应的折扣政策
     */
    private BigDecimal discount;

    /**
     * 内控额度id
     */
    private Integer quotaId;

    /**
     * 上次扣除额度
     */
    private Long deductQuota;

    /**
     * 项目名称
     */
    private String projectName;

    /**
     * 订单开始时间
     */
    private Timestamp orderBeginTime;

    /**
     * 订单结束时间
     */
    private Timestamp orderEndTime;

    /**
     * 促销政策：（0-无 1-坚冰行业 2-破冰蓝海 3-单笔订单促销政策 4-其他）
     */
    private Integer promotionPolicy;

    /**
     * 集团id
     */
    private Integer groupId;

    /**
     * 产品线id
     */
    private Integer productLineId;

    /**
     * 产品id
     */
    private Integer productId;

    /**
     * 项目执行-用户
     */
    private Integer executeId;

    /**
     * 0:等待中, 1:执行中, 2:可执行完成, 3:待收款, 4:已完成, 5:废弃
     */
    private Integer busStatus;

    /**
     * 0:编辑中, 1:待审核, 2:审核通过, 3:审核拒绝, 4:废弃
     */
    private Integer auditStatus;

    /**
     * 历史代理商id
     */
    private Integer oldAgentId;

    /**
     * 线上开票金额（分），不包含红字发票（返点核算开票）(审批通过)
     */
    private Long billAmount;

    /**
     * 合同总认款金额（合同线上已抵扣金额）（分）
     */
    private Long totalClaimAmount;

    /**
     * 合同初始化抵扣金额(线下抵扣金额)(分)
     */
    private Long initDeductedAmount;

    /**
     * 是否oa开票：0-未开票，1-开票
     */
    private Integer isOaOpenBill;

    /**
     * 是否抵扣完成：0-未完成，1-抵扣完成
     */
    private Integer isDeductCompleted;

    /**
     * 线下开票金额（分）
     */
    private Long offlineBillAmount;

    /**
     * 营销中心合同签约主体ID
     */
    private Integer signSubjectId;

    /**
     * 项目节目字段ID
     */
    private Integer projectItemId;

    /**
     * 召集令tag（预付费商单）
     */
    private Long tagId;

    /**
     * 召集令lite版活动开始时间
     */
    private Timestamp litePickupBeginTime;

    /**
     * 召集令lite版活动结束时间
     */
    private Timestamp litePickupEndTime;

    /**
     * 合同账期
     */
    private Integer billPeriod;

    /**
     * 下单邮箱
     */
    private String orderEmail;

    /**
     * 内外部排期是否一致
     */
    private Integer isScheduleTheSame;

    /**
     * 外部排期开始时间
     */
    private Timestamp externalScheduleStartTime;

    /**
     * 外部排期结束时间
     */
    private Timestamp externalScheduleEndTime;

    /**
     * 项目周期开始时间
     */
    private Timestamp periodStart;

    /**
     * 项目周期结束时间
     */
    private Timestamp periodEnd;

    /**
     * 促销政策 多选 @see com.bilibili.crm.platform.biz.common.PromotionPolicy
     */
    private String promotionPolicyList;

    /**
     * 是否有商机代理
     */
    private Integer hasOptAgent;

    /**
     * 商机代理ID
     */
    private Integer optAgentId;

    /**
     * 商机代理名称
     */
    private String optAgentName;

    /**
     * 花火商单预算总金额
     */
    private Long pickupAmount;

    /**
     * 合约上线日期
     */
    private Timestamp launchTime;

    /**
     * 未录入金额总计（仅共数据使用
     */
    private Long unRecordAmount;

    /**
     * 商单未录入金额（仅共数据使用
     */
    private Long pickupUnRecordAmount;

    /**
     * 品牌未录入金额（仅共数据使用
     */
    private Long brandUnRecordAmount;

    /**
     * 花火商单已经使用的预算总金额
     */
    private Long pickupBudgetOccupiedAmount;

    /**
     * 花火商单剩余可以使用的预算总金额
     */
    private Long pickupBudgetRemainingAmount;

    /**
     * 部门ID
     */
    private Integer departmentId;

    /**
     * 标记直客销售是否被手动修改
     */
    private Integer isDirectSalesManualModified;

    /**
     * 标记渠道销售是否被手动修改
     */
    private Integer isChannelSalesManualModified;

    /**
     * 合同版本（用于产品版本迭代）
     */
    private Integer crmVer;

    /**
     * 标记业绩直客销售是否被手动修改
     */
    private Integer isBizDirectSalesManualModified;

    /**
     * 标记业绩渠道销售是否被手动修改
     */
    private Integer isBizChannelSalesManualModified;

    /**
     * btp预付费商单项目预算金额
     */
    private Long preCmOrderBudget;

    /**
     * 合同修改标记 ContractModifyFlag
     */
    private Integer modifyFlag;

    private static final long serialVersionUID = 1L;
}