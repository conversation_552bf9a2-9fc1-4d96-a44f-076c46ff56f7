package com.bilibili.crm.platform.biz.convertor;

import com.bilibili.crm.platform.api.finance.dto.automation.AccountReceiptInfoDto;
import com.bilibili.crm.platform.api.finance.dto.automation.AccountReceiptInfoSaveDto;
import com.bilibili.crm.platform.biz.po.CrmAccountReceiptInfoPo;
import org.springframework.beans.BeanUtils;

/**
 * <AUTHOR>
 * @date 2021/4/23 1:58 下午
 */
public class AccountReceiptDtoConvertor {

    public static AccountReceiptInfoDto convertPo2Dto(CrmAccountReceiptInfoPo crmAccountReceiptInfoPo) {
        if (crmAccountReceiptInfoPo == null) {
            return null;
        }
        AccountReceiptInfoDto target = new AccountReceiptInfoDto();
        BeanUtils.copyProperties(crmAccountReceiptInfoPo, target);
        return target;
    }

    public static CrmAccountReceiptInfoPo convertDto2Po(AccountReceiptInfoDto accountReceiptAddressInfoDto) {
        if (accountReceiptAddressInfoDto == null) {
            return null;
        }
        CrmAccountReceiptInfoPo target = new CrmAccountReceiptInfoPo();
        BeanUtils.copyProperties(accountReceiptAddressInfoDto, target);
        return target;
    }

    public static CrmAccountReceiptInfoPo convertSaveDto2Po(AccountReceiptInfoSaveDto accountReceiptAddressInfoDto) {
        if (accountReceiptAddressInfoDto == null) {
            return null;
        }
        CrmAccountReceiptInfoPo target = new CrmAccountReceiptInfoPo();
        BeanUtils.copyProperties(accountReceiptAddressInfoDto, target);
        return target;
    }
}
