package com.bilibili.crm.platform.biz.service.oa.component;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.achievement.dto.SimpleCrmInterlayerMoneyPo;
import com.bilibili.crm.platform.api.contract.dto.*;
import com.bilibili.crm.platform.api.finance.dto.automation.CrmContractInfoForOaDto;
import com.bilibili.crm.platform.api.finance.enums.FlowStatusEnum;
import com.bilibili.crm.platform.api.finance.enums.InvoiceBizTypeEnum;
import com.bilibili.crm.platform.api.finance.enums.OAFlowStatusEnum;
import com.bilibili.crm.platform.api.finance.enums.YesOrNoEnum;
import com.bilibili.crm.platform.api.oa.dto.OaBillSituationInfoDto;
import com.bilibili.crm.platform.api.oa.dto.OaContractBillPreInfoQueryDto;
import com.bilibili.crm.platform.api.oa.dto.OaContractBillSituationInfoDto;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractAuditStatus;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.Component.contract.ContractInfoQueryDto;
import com.bilibili.crm.platform.biz.Component.contract.DefaultContractInfoQuerier;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.*;
import com.bilibili.crm.platform.biz.repo.finance.*;
import joptsimple.internal.Strings;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 合同开票信息查询器
 *
 * <AUTHOR>
 * @date 2021/4/13 3:00 下午
 */
@Component
public class ContractBillInfoQuerier implements IContractBillInfoQuerier {

    @Autowired
    private DefaultContractInfoQuerier defaultContractInfoQuerier;
    @Autowired
    private CrmContractRepo crmContractRepo;
    @Autowired
    private CrmInvoiceOaFlowRelationRepo crmInvoiceOaFlowRelationRepo;
    @Autowired
    private CrmRebateCheckRecordRepo crmRebateCheckRecordRepo;
    @Autowired
    private CrmOaFlowRepo crmOaFlowRepo;
    @Autowired
    private CrmInterlayerMoneyRepo crmInterlayerMoneyRepo;
    @Autowired
    private AccAccountRepo accAccountRepo;
    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private CrmAgentRepo crmAgentRepo;


    /**
     * 检查这些合同的代理商客户/广告主客户是否一致
     *
     * @param oaBillPreInfoQueryDto
     * @return 如果这些合同的代理商客户/广告主客户一致，则返回客户的id与部门id
     */
    @Override
    public OaBillSituationInfoDto queryContractsBillSituations(OaContractBillPreInfoQueryDto oaBillPreInfoQueryDto) {
        Set<Integer> customerIdOfContracts = new HashSet<>();
        Set<Integer> departmentIdOfContracts = new HashSet<>();
        Set<Integer> signProjectIdOfContracts = new HashSet<>();
        List<Integer> contractIds = oaBillPreInfoQueryDto.getContractIds();

        List<OaContractBillSituationInfoDto> contractBillSituationInfos = new ArrayList<>();
        ContractInfoQueryDto contractInfoQueryDto = ContractInfoQueryDto.builder()
                .contractIds(contractIds)
                .needContractOaInfo(true)
                .needContractUserInfo(true)
                .build();

        // oa 合同信息
        List<ContractRelationInfo> contractRelationInfos = defaultContractInfoQuerier.queryContractOaInfos(contractInfoQueryDto);
        if (CollectionUtils.isEmpty(oaBillPreInfoQueryDto.getContractNumbers())) {
            Map<Long, Integer> tmpContractNumberMap = contractRelationInfos.stream().collect(Collectors.toMap(t -> t.getContractNumber(), t -> t.getContractId()));
            oaBillPreInfoQueryDto.setContractNumberMap(tmpContractNumberMap);
            oaBillPreInfoQueryDto.setContractNumbers(contractRelationInfos.stream().map(t -> t.getContractNumber()).collect(Collectors.toList()));
        }
        Map<Long, ContractRelationInfo> contractRelationInfoMap = contractRelationInfos.stream().collect(Collectors.toMap(t -> t.getContractNumber(), t -> t));

        Boolean isHasAgentCustomer = false;
        if (CollectionUtils.isNotEmpty(oaBillPreInfoQueryDto.getContractNumbers())) {
            Iterator<Map.Entry<Long, Integer>> iterator = oaBillPreInfoQueryDto.getContractNumberMap().entrySet().iterator();
            while (iterator.hasNext()) {
                Map.Entry<Long, Integer> next = iterator.next();
                StringBuilder errorMsg = new StringBuilder();
                OaContractBillSituationInfoDto oaContractBillSituationInfoDto = OaContractBillSituationInfoDto.builder()
                        .contractId(next.getValue())
                        .contractNumber(next.getKey())
                        .contractCustomerIsSame(true)
                        .contractDepartmentIsSame(true)
                        .contractStatusIsCanOpenBill(true)
                        .contractSignIsSame(true)
                        .hasOpenBill(false)
                        .errorMsg("")
                        .isContractNotExist(false).build();
                // 合同 id 是否存在
                if (!Utils.isPositive(next.getValue())) {
                    oaContractBillSituationInfoDto.setIsContractNotExist(true);
                    errorMsg.append("合同号不存在！");
                    if (errorMsg.length() > 0) {
                        oaContractBillSituationInfoDto.setErrorMsg(errorMsg.toString());
                    }
                }
                // 合同是否已经开票
                ContractRelationInfo contractRelInfo = contractRelationInfoMap.get(next.getKey());
                if (contractRelInfo != null) {
                    ContractOaInfo contractOaInfo = contractRelInfo.getContractOaInfo();
                    // 是否已经开票
                    if (contractOaInfo != null) {
                        oaContractBillSituationInfoDto.setHasOpenBill(false);
                    }
                    // 合同的业务状态需为可执行完成，审核状态需为审核通过
                    oaContractBillSituationInfoDto.setContractRelationInfo(contractRelInfo);
                    ContractBaseInfo contractBaseInfo = contractRelInfo.getContractBaseInfo();
                    signProjectIdOfContracts.add(contractBaseInfo.getSignSubjectId());
                    if (!ContractBusStatus.DELETED.getCode().equals(contractBaseInfo.getBusStatus()) && ContractAuditStatus.SUCCESS.getCode().equals(contractBaseInfo.getAuditStatus())) {
                        oaContractBillSituationInfoDto.setContractStatusIsCanOpenBill(true);
                    } else {
                        oaContractBillSituationInfoDto.setContractStatusIsCanOpenBill(false);
                    }
                    // 合同开票公司名称（合同中有代理商则为代理商客户名称，合同中无代理商，则为广告主客户名称）与其它合同的开票公司名称不一致，提示“开票公司不一致”
                    ContractUserInfo contractUserInfo = contractRelInfo.getContractUserInfo();
                    if (contractUserInfo != null) {
                        if (contractUserInfo.getHasAgent()) {
                            isHasAgentCustomer = true;
                            customerIdOfContracts.add(contractUserInfo.getAgentCustomerId());
                            departmentIdOfContracts.add(contractUserInfo.getAgentDepartmentId());
                        } else {
                            customerIdOfContracts.add(contractUserInfo.getAdvertiseCustomerId());
                            departmentIdOfContracts.add(contractUserInfo.getAdvertiseDepartmentId());
                        }
                    }
                    if (oaContractBillSituationInfoDto.getIsContractNotExist()) {
                        errorMsg.append("该合同不存在！");
                    }
                    if (!oaContractBillSituationInfoDto.getContractStatusIsCanOpenBill()) {
                        errorMsg.append("合同的业务状态需为可执行完成，审核状态需为审核通过！");
                    }
                    if (errorMsg.length() > 0) {
                        oaContractBillSituationInfoDto.setErrorMsg(errorMsg.toString());
                    }
                }
                contractBillSituationInfos.add(oaContractBillSituationInfoDto);
            }
            OaBillSituationInfoDto customerAndDepartmentOfContractsBill = setContractsIsCanBill(contractBillSituationInfos, customerIdOfContracts, departmentIdOfContracts, signProjectIdOfContracts);
            if (customerAndDepartmentOfContractsBill.getIfCanBill() == false) {
                return customerAndDepartmentOfContractsBill;
            }
        } else {
            Assert.isTrue(CollectionUtils.isNotEmpty(contractIds), "合同不能为空！");
        }

        if (customerIdOfContracts.size() == 0) {
            throw new ServiceRuntimeException("合同列表未绑定客户[可能代理商或广告主的账号/客户不存在]！");
        }
        OaBillSituationInfoDto customerAndDepartmentOfContractsBill = setContractsIsCanBill(contractBillSituationInfos, customerIdOfContracts, departmentIdOfContracts, signProjectIdOfContracts);
        return customerAndDepartmentOfContractsBill;
    }

    @Override
    public ContractBillDto queryContractBillInfo(Long contractNumber) {
        Assert.isTrue(Utils.isPositive(contractNumber), "合同号不能为空！");

        // 合同是否存在
        ContractBillDto contractBillDto = ContractBillDto.builder().build();
        CrmContractPo contractPo = crmContractRepo.queryByByNumber(contractNumber);
        if (contractPo == null) {
            throw new ServiceRuntimeException("该合同号不存在！");
        }
        if (YesOrNoEnum.NO.getCode().equals(contractPo.getIsOaOpenBill())) {
            throw new ServiceRuntimeException("该合同号未开过正数发票");
        }

        // 获取开票信息
        String customerName = queryContractCustomerName(contractPo);
        contractBillDto.setBillCompany(customerName);
        contractBillDto.setSignProjectId(contractPo.getSignSubjectId());

        contractBillDto.setContractId(contractPo.getId());
        contractBillDto.setContractNo(contractPo.getContractNumber());
        // 合同打包价
        contractBillDto.setContractPackageAmount(new BigDecimal(contractPo.getAmount() + contractPo.getPdAmount()));
        // 历史开票金额(分)[合同的正数开票金额] = 线上开票金额 + 线下开票金额
        contractBillDto.setHistoryBillAmount(new BigDecimal(contractPo.getBillAmount() + contractPo.getOfflineBillAmount()));
        contractBillDto.setHasDeductAmount(new BigDecimal(contractPo.getTotalClaimAmount()));
        contractBillDto.setToDeductAmount(contractBillDto.getHistoryBillAmount().subtract(contractBillDto.getHasDeductAmount()));
        // 历史核算开票金额
        CrmRebateCheckRecordPoExample crmRebateCheckRecordPoExample = new CrmRebateCheckRecordPoExample();
        crmRebateCheckRecordPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andContractNumberEqualTo(contractNumber)
                .andOaFlowStatusEqualTo(OAFlowStatusEnum.COMPLETED.getCode());
        List<CrmRebateCheckRecordPo> crmRebateCheckRecordPos = crmRebateCheckRecordRepo.queryByExample(crmRebateCheckRecordPoExample);
        long sumRebateCheckBillAmount = crmRebateCheckRecordPos.stream().mapToLong(t -> t.getCheckAmount()).sum();
        contractBillDto.setHistoryCheckBillAmount(new BigDecimal(sumRebateCheckBillAmount));
        return contractBillDto;
    }

    /**
     * 获取合同的开票公司
     * 如果绑定了代理商，获取代理商客户名；如果绑定了广告主，获取广告主的客户名
     *
     * @param contractPo
     * @return
     */
    public String queryContractCustomerName(CrmContractPo contractPo) {
        Assert.isTrue(contractPo != null, "contractPo 不能为空！");

        if (Utils.isPositive(contractPo.getAgentId())) {
            CrmAgentPo crmAgentPo = crmAgentRepo.queryAgentById(contractPo.getAgentId());
            if (crmAgentPo == null) {
                return Strings.EMPTY;
            }
            return queryCustomerNameByAccountId(crmAgentPo.getSysAgentId());
        } else {
            return queryCustomerNameByAccountId(contractPo.getAccountId());
        }
    }

    private String queryCustomerNameByAccountId(Integer accountId) {
        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(accountId);
        if (accAccountPo == null) {
            return Strings.EMPTY;
        }
        CustomerPo customerPo = customerRepo.queryNotDeletedCustomerById(accAccountPo.getCustomerId());
        if (customerPo == null) {
            return Strings.EMPTY;
        }
        return customerPo.getUsername();
    }

    @Override
    public List<ContractBillDto> queryContractBillInfo(List<Long> contractNumbers) {
        List<CrmContractPo> crmContractPos = crmContractRepo.queryByByNumbers(contractNumbers);
        if (CollectionUtils.isEmpty(crmContractPos)) {
            return Collections.EMPTY_LIST;
        }
        List<Integer> contractIds = crmContractPos.stream().map(t -> t.getId()).collect(Collectors.toList());
        // 获取账单
        Map<Integer, List<SimpleCrmInterlayerMoneyPo>> interlayerMoneyPoMap = crmInterlayerMoneyRepo.queryInterlayerMoneySimpleMapByContractIds(contractIds);
        // 历史核算开票金额
        CrmRebateCheckRecordPoExample crmRebateCheckRecordPoExample = new CrmRebateCheckRecordPoExample();
        crmRebateCheckRecordPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCrmContractIdIn(contractIds).andOaFlowStatusEqualTo(OAFlowStatusEnum.COMPLETED.getCode());
        List<CrmRebateCheckRecordPo> crmRebateCheckRecordPos = crmRebateCheckRecordRepo.queryByExample(crmRebateCheckRecordPoExample);
        Map<Integer, List<CrmRebateCheckRecordPo>> rebateCheckMap = crmRebateCheckRecordPos.stream().collect(Collectors.groupingBy(t -> t.getCrmContractId()));

        List<ContractBillDto> contractBillDtos = new ArrayList<>(crmContractPos.size());
        // 获取一个合同的开票信息，所有的合同的开票公司应该一样
        String customerName = queryContractCustomerName(crmContractPos.get(0));
        for (CrmContractPo contractPo : crmContractPos) {
            ContractBillDto contractBillDto = ContractBillDto.builder().build();

            contractBillDto.setContractId(contractPo.getId());
            contractBillDto.setContractNo(contractPo.getContractNumber());
            contractBillDto.setContractName(contractPo.getName());
            contractBillDto.setSignProjectId(contractPo.getSignSubjectId());
            // 合同打包价
            contractBillDto.setContractPackageAmount(new BigDecimal(contractPo.getAmount() + contractPo.getPdAmount()));
            // 历史开票金额(分) = 线上开票金额 + 线下开票金额
            contractBillDto.setHistoryBillAmount(new BigDecimal(contractPo.getBillAmount() + contractPo.getOfflineBillAmount()));
            contractBillDto.setHasDeductAmount(new BigDecimal(contractPo.getTotalClaimAmount()));
            contractBillDto.setToDeductAmount(contractBillDto.getHistoryBillAmount().subtract(contractBillDto.getHasDeductAmount()));

            // 关账金额，账单上
            List<SimpleCrmInterlayerMoneyPo> allInterlayerMoneyPos = interlayerMoneyPoMap.get(contractPo.getId());
            if (CollectionUtils.isNotEmpty(allInterlayerMoneyPos)) {
                Long sumCloseAmount = allInterlayerMoneyPos.stream().filter(t -> YesOrNoEnum.YES.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);
                Long sumNotCloseAmount = allInterlayerMoneyPos.stream().filter(t -> YesOrNoEnum.NO.getCode().equals(t.getIsClosed())).map(t -> t.getDailyPackageAmount()).reduce(Long::sum).orElse(0L);
                contractBillDto.setContractClosedAmount(new BigDecimal(sumCloseAmount));
                contractBillDto.setContractNotClosedAmount(new BigDecimal(sumNotCloseAmount));
            } else {
                contractBillDto.setContractClosedAmount(new BigDecimal(0));
                contractBillDto.setContractNotClosedAmount(new BigDecimal(0));
            }
            // 开票公司名称则为客户名
            contractBillDto.setBillCompany(customerName);

            // 根据合同获取该合同的返点核算开票金额
            List<CrmRebateCheckRecordPo> crmRebateCheckRecordPosOfContract = rebateCheckMap.get(contractPo.getId());
            if (CollectionUtils.isNotEmpty(crmRebateCheckRecordPosOfContract)) {
                long sumRebateCheckBillAmount = crmRebateCheckRecordPos.stream().mapToLong(t -> t.getCheckAmount()).sum();
                contractBillDto.setHistoryCheckBillAmount(new BigDecimal(sumRebateCheckBillAmount));
            } else {
                contractBillDto.setHistoryCheckBillAmount(new BigDecimal(0));
            }
            contractBillDtos.add(contractBillDto);
        }
        return contractBillDtos;
    }

    private OaBillSituationInfoDto setContractsIsCanBill(List<OaContractBillSituationInfoDto> contractBillSituationInfos, Set<Integer> customerIdOfContracts, Set<Integer> departmentIdOfContracts, Set<Integer> signProjectIdOfContracts) {
        Boolean thisBatchContractIfCanBill = setBatchContractsIfCanBill(contractBillSituationInfos, customerIdOfContracts, departmentIdOfContracts, signProjectIdOfContracts);
        return buildBillSituationInfo(contractBillSituationInfos, customerIdOfContracts, departmentIdOfContracts, thisBatchContractIfCanBill, signProjectIdOfContracts);
    }

    private OaBillSituationInfoDto buildBillSituationInfo(List<OaContractBillSituationInfoDto> contractBillSituationInfos, Set<Integer> customerIdOfContracts, Set<Integer> departmentIdOfContracts, Boolean thisBatchContractIfCanBill, Set<Integer> signProjectIdOfContracts) {
        OaBillSituationInfoDto customerAndDepartmentOfContractsBill =
                OaBillSituationInfoDto.builder().build();
        if (!CollectionUtils.isEmpty(customerIdOfContracts)) {
            customerAndDepartmentOfContractsBill.setCustomerId(customerIdOfContracts.iterator().next());
        }
        if (!CollectionUtils.isEmpty(departmentIdOfContracts)) {
            customerAndDepartmentOfContractsBill.setDepartmentId(departmentIdOfContracts.iterator().next());
        }
        if(!CollectionUtils.isEmpty(signProjectIdOfContracts)){
            customerAndDepartmentOfContractsBill.setSignProjectId(signProjectIdOfContracts.iterator().next());
        }
        // 这批合同是否可以开票
        customerAndDepartmentOfContractsBill.setIfCanBill(thisBatchContractIfCanBill);
        customerAndDepartmentOfContractsBill.setContractBillSituationInfos(contractBillSituationInfos);
        return customerAndDepartmentOfContractsBill;
    }

    /**
     * 设置该批合同是否可以开票
     *
     * @param contractBillSituationInfos
     * @param customerIdOfContracts
     * @param departmentIdOfContracts
     * @return
     */
    private Boolean setBatchContractsIfCanBill(List<OaContractBillSituationInfoDto> contractBillSituationInfos, Set<Integer> customerIdOfContracts, Set<Integer> departmentIdOfContracts, Set<Integer> signProjectIdOfContracts) {
        Boolean thisBatchContractIfCanBill = true;
        for (OaContractBillSituationInfoDto billSituationInfoDto : contractBillSituationInfos) {
            // 更新合同的开票客户是否一致
            if (customerIdOfContracts.size() > 1) {
                billSituationInfoDto.setContractCustomerIsSame(false);
                billSituationInfoDto.setErrorMsg(billSituationInfoDto.getErrorMsg() + "开票公司不一致！");
            }
            if(signProjectIdOfContracts.size() > 1){
                billSituationInfoDto.setContractSignIsSame(false);
                billSituationInfoDto.setErrorMsg(billSituationInfoDto.getErrorMsg() + "您提交的合同属于多个我方主体，请分主体提交");
            }
            // 更新合同的部门是否一致
            if (departmentIdOfContracts.size() > 1) {
                billSituationInfoDto.setContractDepartmentIsSame(false);
                billSituationInfoDto.setErrorMsg(billSituationInfoDto.getErrorMsg() + "合同的账号属于部门不一致！");
            }
            if (!billSituationInfoDto.getIsContractNotExist() && !billSituationInfoDto.getHasOpenBill() && billSituationInfoDto.getContractStatusIsCanOpenBill() && billSituationInfoDto.getContractCustomerIsSame() &&
                    billSituationInfoDto.getContractDepartmentIsSame() && billSituationInfoDto.getContractSignIsSame()) {
                billSituationInfoDto.setIfCanBill(true);
            } else {
                billSituationInfoDto.setIfCanBill(false);
                thisBatchContractIfCanBill = false;
            }
        }
        return thisBatchContractIfCanBill;
    }

}
