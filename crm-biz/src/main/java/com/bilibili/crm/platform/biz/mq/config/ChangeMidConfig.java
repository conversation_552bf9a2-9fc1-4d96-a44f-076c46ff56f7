package com.bilibili.crm.platform.biz.mq.config;

import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.FanoutExchange;
import org.springframework.amqp.core.Queue;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 * description：自动开户发送rabbitMq消息配置
 * date       ：2020/11/12 5:01 下午
 */
/*@Configuration*/
@Deprecated
public class ChangeMidConfig {
    @Value("${crm.change.mid.queue.name:crm.change.mid.queue}")
    private String crmChangeMidQueueName;

    @Value("${crm.change.mid.exchange.name:crm.change.mid.exchange}")
    private String crmChangeMidExchangeName ;

    @Bean
    public Queue crmChangeMidQueue() {
        return new Queue(crmChangeMidQueueName);
    }

    @Bean
    public FanoutExchange crmChangeMidExchange() {
        return new FanoutExchange(crmChangeMidExchangeName, true, false);
    }

    @Bean
    public Binding accountChangeMidMqBinding(@Qualifier("crmChangeMidQueue") Queue queue,
                                              @Qualifier("crmChangeMidExchange") FanoutExchange exchange) {
        return BindingBuilder.bind(queue).to(exchange);
    }
}
