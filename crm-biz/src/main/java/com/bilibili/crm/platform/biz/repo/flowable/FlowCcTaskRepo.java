package com.bilibili.crm.platform.biz.repo.flowable;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.dao.CrmFlowCcTaskDao;
import com.bilibili.crm.platform.biz.po.CrmFlowCcTaskPo;
import com.bilibili.crm.platform.biz.po.CrmFlowCcTaskPoExample;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * 流程抄送 repo
 *
 * <AUTHOR>
 * @date 2023/2/28 16:19
 */
@Slf4j
@Component
public class FlowCcTaskRepo {

    @Resource
    private CrmFlowCcTaskDao crmFlowCcTaskDao;

    /**
     * 批量写入抄送记录
     *
     * @param procInstId 流程ID
     * @param procDefKey 流程定义key
     * @param usernames  抄送用户名
     * @return 插入数量
     */
    public int batchInsert(String procInstId, String procDefKey, List<String> usernames) {
        if (CollectionUtils.isEmpty(usernames)) {
            return 0;
        }
        List<CrmFlowCcTaskPo> records = CrmUtils.convertDistinct(usernames, username ->
                CrmFlowCcTaskPo.builder()
                        .isDeleted(IsDeleted.VALID.getCode())
                        .ctime(Utils.getNow())
                        .mtime(Utils.getNow())
                        .procInstId(procInstId)
                        .procDefKey(procDefKey)
                        .ccUsername(username)
                        .build()
        );

        return crmFlowCcTaskDao.insertBatch(records);
    }

    /**
     * 根据流程定义 key 查询用户的抄送任务
     *
     * @param username   用户域账号
     * @param procDefKey 流程定义key
     * @return 用户抄送流程IDs
     */
    public List<String> queryUserCcTasks(String username, String procDefKey) {
        CrmFlowCcTaskPoExample example = new CrmFlowCcTaskPoExample();
        example.createCriteria().andCcUsernameEqualTo(username).andProcDefKeyEqualTo(procDefKey);

        List<CrmFlowCcTaskPo> pos = crmFlowCcTaskDao.selectByExample(example);

        return CrmUtils.convertDistinct(pos, CrmFlowCcTaskPo::getProcInstId);
    }
}
