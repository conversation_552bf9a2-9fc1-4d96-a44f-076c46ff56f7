package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
public class CrmBusinessRolePo implements Serializable {
	/**
	 * 自增ID
	 */
	private Integer id;

	/**
	 * 业务角色名称
	 */
	private String name;

	/**
	 * 描述
	 */
	private String description;

	/**
	 * 状态：1-有效 2-封禁
	 */
	private Integer status;

	/**
	 * 软删除,0是有效,1是删除
	 */
	private Integer isDeleted;

	/**
	 * 创建时间
	 */
	private Timestamp ctime;

	/**
	 * 最后修改时间
	 */
	private Timestamp mtime;

	private static final long serialVersionUID = 1L;

	public Integer getId() {
		return id;
	}

	public void setId(Integer id) {
		this.id = id;
	}

	public String getName() {
		return name;
	}

	public void setName(String name) {
		this.name = name;
	}

	public String getDescription() {
		return description;
	}

	public void setDescription(String description) {
		this.description = description;
	}

	public Integer getStatus() {
		return status;
	}

	public void setStatus(Integer status) {
		this.status = status;
	}

	public Integer getIsDeleted() {
		return isDeleted;
	}

	public void setIsDeleted(Integer isDeleted) {
		this.isDeleted = isDeleted;
	}

	public Timestamp getCtime() {
		return ctime;
	}

	public void setCtime(Timestamp ctime) {
		this.ctime = ctime;
	}

	public Timestamp getMtime() {
		return mtime;
	}

	public void setMtime(Timestamp mtime) {
		this.mtime = mtime;
	}

	public static long getSerialversionuid() {
		return serialVersionUID;
	}
	
	
}