package com.bilibili.crm.platform.biz.statusmachine.return_money;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.crm.platform.api.statusmachine.return_money.IReturnMoneyStateEvent;
import com.bilibili.crm.platform.api.statusmachine.return_money.ReturnMoneyStateContext;
import com.bilibili.crm.platform.api.statusmachine.return_money.enums.ReturnMoneyConfirmStateEnum;
import com.bilibili.crm.platform.api.statusmachine.return_money.enums.ReturnMoneyInvoiceStateEnum;
import com.bilibili.crm.platform.api.statusmachine.return_money.validator.IReturnMoneyStateValidator;
import com.bilibili.crm.platform.biz.po.CrmReturnMoneyEffectPo;
import com.bilibili.crm.platform.biz.repo.return_online.effect.CrmReturnMoneyEffectRepo;
import com.bilibili.crm.platform.biz.statusmachine.return_money.updater.ReturnMoneyUpdater;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/1/29 18:44
 */
@Slf4j
@Component
public class ReturnMoneyStateMachine implements IReturnMoneyStateEvent {

    @Resource
    private IReturnMoneyStateValidator returnMoneyStateValidator;

    @Resource
    private ReturnMoneyUpdater returnMoneyUpdater;

    @Resource
    private CrmReturnMoneyEffectRepo crmReturnMoneyEffectRepo;

    /// 确认状态流转

    @Override
    public void turnToBeConfirmed(Operator operator, Long returnId) {
        proceedConfirmStateChange(operator, returnId, ReturnMoneyConfirmStateEnum.TO_BE_CONFIRMED, null);
    }

    @Override
    public void turnConfirmed(Operator operator, Long returnId) {
        proceedConfirmStateChange(operator, returnId, ReturnMoneyConfirmStateEnum.CONFIRMED, null);
    }

    @Override
    public void turnRejected(Operator operator, Long returnId, String remark) {
        proceedConfirmStateChange(operator, returnId, ReturnMoneyConfirmStateEnum.REJECTED, remark);
    }

    /**
     * 执行状态流转
     *
     * @param operator 操作人
     * @param returnId 返现ID
     * @param toState  目标状态
     */
    private void proceedConfirmStateChange(Operator operator, Long returnId, ReturnMoneyConfirmStateEnum toState, String remark) {
        Assert.notNull(operator, "操作人不能为空");
        Assert.notNull(toState, "目标状态不能为空");

        CrmReturnMoneyEffectPo po = crmReturnMoneyEffectRepo.queryById(returnId);
        Assert.notNull(po, "返现信息不能为空");

        // 构建上下文
        ReturnMoneyStateContext context = getContext(operator, po);
        // 添加备注
        context.setRemark(remark);
        // 执行状态流转
        changeConfirmState(context, toState);
    }

    /**
     * 构造上下文
     *
     * @param operator 操作人
     * @param po       返现信息
     * @return context
     */
    private static ReturnMoneyStateContext getContext(Operator operator, CrmReturnMoneyEffectPo po) {
        return ReturnMoneyStateContext.builder()
                .operator(operator)
                .returnId(po.getId())
                .currentConfirmState(ReturnMoneyConfirmStateEnum.getByCode(po.getConfirmState()))
                .currentInvoiceState(ReturnMoneyInvoiceStateEnum.getByCode(po.getInvoiceState()))
                .build();
    }

    /**
     * 变更确认状态
     *
     * @param context 上下文
     * @param toState 目标状态
     */
    private void changeConfirmState(ReturnMoneyStateContext context, ReturnMoneyConfirmStateEnum toState) {
        // 校验目标状态
        returnMoneyStateValidator.validateConfirmState(context, toState);

        // 状态更新
        returnMoneyUpdater.updateConfirmState(context, toState);
    }
}
