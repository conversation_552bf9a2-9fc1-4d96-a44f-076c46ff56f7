package com.bilibili.crm.platform.biz.handler;

import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.biz.bean.AccountWalletTradingBean;
import com.bilibili.crm.platform.common.WalletTradingOperateType;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/4/18
 **/
@Slf4j
@Component("couponDeductHandler")
public class CouponDeductHandler extends AbstractAccountWalletTradingHandler {


    @Override
    protected void validateParams(AccountWalletTradingBean tradingBean) {

    }

    @Override
    //@Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class, timeout = 30)
    protected void trade(AccountWalletTradingBean tradingBean) {
        log.info("CouponDeductHandler-" + JSON.toJSONString(tradingBean));

    }

    @Override
    protected WalletTradingOperateType getOperateType() {
        return WalletTradingOperateType.DEDUCT;
    }
}
