package com.bilibili.crm.platform.biz.service.finance.automation.component.claim;

import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.bjcom.util.sensitive.SensitiveHandler;
import com.bilibili.crm.platform.api.contract.dto.ContractBillCanDeductDto;
import com.bilibili.crm.platform.api.contract.service.ContractBillPeriodService;
import com.bilibili.crm.platform.api.finance.dto.automation.*;
import com.bilibili.crm.platform.api.finance.enums.*;
import com.bilibili.crm.platform.biz.common.WorkFlowTypeEnum;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.repo.finance.CrmFlowAdditionInfoRepo;
import com.bilibili.crm.platform.biz.repo.finance.CrmRevenueExpenditureFlowRepo;
import com.bilibili.crm.platform.biz.repo.workflow.CrmWorkFlowRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.config.ReceiveMoneyConfig;
import com.bilibili.crm.platform.biz.service.finance.automation.component.contracttype.ContractDeductQuerier;
import com.bilibili.crm.platform.biz.service.finance.automation.component.contracttype.ContractInfoQuerier;
import com.bilibili.crm.platform.biz.service.finance.automation.component.statemachine.FlowAdditionInfoStateValidator;
import com.bilibili.crm.platform.common.ContractSigningSubject;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 认款查询器
 *
 * <AUTHOR>
 * @date 2021/1/25 4:19 下午
 */
@Slf4j
@Component
public class FlowClaimMoneyQuerier {

    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private CrmRevenueExpenditureFlowRepo crmRevenueExpenditureFlowRepo;
    @Autowired
    private CrmFlowAdditionInfoRepo crmFlowAdditionInfoRepo;
    @Autowired
    private CrmWorkFlowRepo crmWorkFlowRepo;

    @Autowired
    private ContractInfoQuerier contractInfoQuerier;
    @Autowired
    private ContractDeductQuerier contractDeductQuerier;
    @Autowired
    private FlowAdditionInfoStateValidator flowAdditionInfoStateValidator;

    @Resource
    private ContractBillPeriodService contractBillPeriodService;

    @Autowired
    private ReceiveMoneyConfig receiveMoneyConfig;

    /**
     * 检查是否可以进行认款
     *
     * @param claimFlowDto
     */
    public void checkIfCanClaim(ClaimFlowDto claimFlowDto) {
        if (claimFlowDto.getOperator() == null) {
            throw new ServiceRuntimeException("操作人不允许为空！");
        }
        CrmRevenueExpenditureFlowPo flowPo = crmRevenueExpenditureFlowRepo.queryById(claimFlowDto.getFlowId());
        if (flowPo == null) {
            throw new ServiceRuntimeException("流水不存在！");
        }
        if (!Lists.newArrayList(ReceiveMoneyFlowTypeEnum.CONTRACT_RECEIVE_MONEY.getCode(), ReceiveMoneyFlowTypeEnum.CREDIT_RECEIVE_MONEY.getCode()).contains(flowPo.getType())) {
            throw new ServiceRuntimeException("该流水不是合约类型流水，不允许进行认款！");
        }
        // 设置 bill id
        claimFlowDto.setBillId(flowPo.getBillId());
        FlowBizStatusEnum flowBizStatusEnum = flowAdditionInfoStateValidator.ifCanClaim(flowPo.getBizStatus());
        log.info("=====> checkIfCanClaim[can claim], flowId:{}, bizStatus:{}", claimFlowDto.getFlowId(), flowBizStatusEnum);
    }

    public void checkCanReversedClaim(ContractReversedDeductDto reversedDto) {

        // 输入数字需要小于等于未回退抵扣金额；前后端需要同时校验，如果不符合校验提示，“您输入的数字大于未回退抵扣金额，请检查后重新输入。”
        if (reversedDto.getNotReversedDeductAmount().compareTo(reversedDto.getNowDeductAmount()) < 0) {
            log.warn("reversedClaim amount is error reversedDto={}", reversedDto);
            throw new ServiceRuntimeException("您输入的数字大于未回退抵扣金额，请检查后重新输入。");
        }

         /*
            获取抵扣流水的那一条记录进行判断，判断是否回退操作，回退操作须满足：
            1. 抵扣来源为银企直联
            2. 抵扣金额为正数
            3. 未回退金额大于0的部分

            疑问：是否永远展示回退操作按钮，有没有已经关帐的就不需要了 或者 计收完成的就不需要了
        */
        if (!ContractDeductType.BANK_ENTERPRISE_DIRECT_CONNECT_DEDUCT.getCode().equals(reversedDto.getType())
                || reversedDto.getDeductAmount() <= 0
                || reversedDto.getNotReversedDeductAmount() <= 0) {
            throw new ServiceRuntimeException("当前抵扣明细不能再进行回退操作");
        }
    }

    /**
     * 根据流水 id 查询回款流水信息
     * 手动的方式：推荐抵扣 = 0
     * 自动的方式：会根据流水的待抵扣，合同的抵扣顺序与合同的待抵扣计算出合同推荐抵扣
     *
     * @param flowId
     * @return 单位分
     */
    public CanDeductFlowDto queryClaimInfoByFlowId(Integer flowId, ClaimMoneyTypeEnum claimMoneyTypeEnum) {
        CanDeductFlowDto flowContractDto = buildDeductFlowInfo(flowId);
        CustomerPo customerPo = customerRepo.queryNotDeletedCustomerById(flowContractDto.getBindCustomerId());
        if (customerPo == null) {
            return flowContractDto;
        }
        // 根据流水匹配合同
        List<CrmContractPo> contractPos = contractInfoQuerier.getSortOaBilledContractListByBindCustomerId(customerPo);
        List<CanDeductContractDto> contractOfFlowDtos =
                contractDeductQuerier.convertCanDeductContractPos2ContractDtos(contractPos, false, null, null);

        // 过滤掉待抵扣金额为负数和0的
        contractOfFlowDtos = filterLtZeroToClaimAmount(flowContractDto, contractOfFlowDtos);

        // 自动认款方式，自动计算合同的推荐抵扣金额；手动方式不需要推荐抵扣金额
        if (ClaimMoneyTypeEnum.AUTO.equals(claimMoneyTypeEnum)) {
            calculateCanDeductAmountForAutoClaim(flowContractDto, contractOfFlowDtos);
        }
        return flowContractDto;
    }

    public CanDeductFlowDto queryClaimBillByFlowId(Integer flowId, ClaimMoneyTypeEnum claimMoneyTypeEnum) {
        CanDeductFlowDto canDeductFlowDto = buildDeductFlowInfo(flowId);
        CustomerPo customerPo = customerRepo.queryNotDeletedCustomerById(canDeductFlowDto.getBindCustomerId());
        if (customerPo == null) {
            return canDeductFlowDto;
        }
        // 根据流水匹配合同
        List<CrmContractPo> contractPos = contractInfoQuerier.queryRelatedContractListByBindCustomerId(customerPo);
        List<CanDeductContractDto> contractOfFlowDtos =
                contractDeductQuerier.convertCanDeductContractPos2ContractDtos(contractPos, false, null, null);
        // 珠海友电文化传播有限公司的流水，则只可抵扣我方主体为珠海友电文化传播有限公司的合同
        if (canDeductFlowDto.getReceiveAccountNo().equals(receiveMoneyConfig.getYdReceiveMoneyAccount())) {
            contractOfFlowDtos = contractOfFlowDtos.stream().filter(a -> a.getSignProjectId().equals(ContractSigningSubject.YD_COMPANY.getCode())).collect(Collectors.toList());
        } else {
            contractOfFlowDtos = contractOfFlowDtos.stream().filter(a -> !a.getSignProjectId().equals(ContractSigningSubject.YD_COMPANY.getCode())).collect(Collectors.toList());
        }
        contractOfFlowDtos = filterLtZeroToClaimAmount(canDeductFlowDto, contractOfFlowDtos);
        List<Integer> crmContractIds = contractOfFlowDtos.stream().map(CanDeductContractDto::getContractId).distinct().collect(Collectors.toList());
        List<ContractBillCanDeductDto> canDeductBillList = contractBillPeriodService.queryCanDeductBill(crmContractIds, null);
        Map<Integer, List<ContractBillCanDeductDto>> canDeductMap = canDeductBillList.stream().collect(Collectors.groupingBy(ContractBillCanDeductDto::getCrmContractId, Collectors.toList()));
        for (CanDeductContractDto dto : contractOfFlowDtos) {
            dto.setCanDeductBillList(canDeductMap.getOrDefault(dto.getContractId(), new ArrayList<>()).stream().sorted(Comparator.comparing(ContractBillCanDeductDto::getBillEndTime)).collect(Collectors.toList()));
        }
        if (ClaimMoneyTypeEnum.AUTO.equals(claimMoneyTypeEnum)) {
            calculateCanDeductAmountBillForAutoClaim(canDeductFlowDto, contractOfFlowDtos);
        }
        canDeductFlowDto.setCanDeductContractDtos(contractOfFlowDtos);
        return canDeductFlowDto;
    }

    private CanDeductFlowDto buildDeductFlowInfo(Integer flowId) {
        // 获取流水信息
        CrmRevenueExpenditureFlowPo flowPo = crmRevenueExpenditureFlowRepo.queryById(flowId);
        CrmContractInfoForOaDto crmContractInfoForOaDto = new CrmContractInfoForOaDto();
        if (flowPo.getBindContractId() != null && flowPo.getBindContractId() > 0) {
            crmContractInfoForOaDto = contractInfoQuerier.queryContractInfo(flowPo.getBindContractId());
        }
        checkFlow(flowId, flowPo);
        CanDeductFlowDto canDeductFlowDto = CanDeductFlowDto.builder().build();
        canDeductFlowDto.setFlowId(flowPo.getId());
        canDeductFlowDto.setBillId(flowPo.getBillId());
        canDeductFlowDto.setFlowNo(flowPo.getFlowNo());
        canDeductFlowDto.setBindCustomerId(flowPo.getBindCustomerId());
        canDeductFlowDto.setPayAccountNo(flowPo.getPayAccountNo());
        canDeductFlowDto.setPayAccountName(flowPo.getPayAccountName());
        canDeductFlowDto.setReceiveAccountNo(flowPo.getReceiveAccountNo());
        canDeductFlowDto.setReceiveAccountName(flowPo.getReceiveAccountName());
        canDeductFlowDto.setReceiveSubAccountNo(flowPo.getReceiveSubAccountNo());
        canDeductFlowDto.setReceiveSubAccountName(flowPo.getReceiveSubAccountName());
        canDeductFlowDto.setAmount(new BigDecimal(flowPo.getAmount()));
        canDeductFlowDto.setRemark(flowPo.getRemark());
        canDeductFlowDto.setContractNumber(crmContractInfoForOaDto.getContractNo());
        canDeductFlowDto.setContractName(crmContractInfoForOaDto.getContractName());

        // 获取流水的额外信息，获取已抵扣与未抵扣金额
        CrmFlowAdditionInfoPo crmFlowAdditionInfoPo = crmFlowAdditionInfoRepo.queryByFlowId(flowPo.getId());
        canDeductFlowDto.setFlowAdditionInfoId(crmFlowAdditionInfoPo.getId());
        canDeductFlowDto.setTotalDeductedAmount(new BigDecimal(crmFlowAdditionInfoPo.getTotalDeductedAmount()));
        canDeductFlowDto.setRefundAmount(new BigDecimal(crmFlowAdditionInfoPo.getRefundAmount()));
        canDeductFlowDto.setToDeductAmount(new BigDecimal(flowPo.getAmount() - crmFlowAdditionInfoPo.getTotalDeductedAmount() - crmFlowAdditionInfoPo.getRefundAmount()));
        // 解密
        canDeductFlowDto = SensitiveHandler.decrypt(canDeductFlowDto);
        return canDeductFlowDto;
    }

    public List<CanDeductContractDto> filterLtZeroToClaimAmount(CanDeductFlowDto flowContractDto,
                                                                List<CanDeductContractDto> contractOfFlowDtos) {
        Integer beforeSize = contractOfFlowDtos.size();
        contractOfFlowDtos = contractOfFlowDtos.stream().filter(t -> t.getBizToDeductAmount().compareTo(BigDecimal.ZERO) > 0).collect(Collectors.toList());
        log.info("=====> queryClaimInfoByFlowId, filter before, size:{}, filter after size:{}", beforeSize, contractOfFlowDtos.size());
        flowContractDto.setCanDeductContractDtos(contractOfFlowDtos);
        return contractOfFlowDtos;
    }

    private void calculateCanDeductAmountBillForAutoClaim(CanDeductFlowDto flowContractDto, List<CanDeductContractDto> contractOfFlowDtos) {
        BigDecimal flowToDeductAmount = flowContractDto.getToDeductAmount(); // 流水待抵扣
        for (CanDeductContractDto contractOfFlowDto : contractOfFlowDtos) {
            if (contractOfFlowDto.getBizToDeductAmount().compareTo(flowToDeductAmount) <= 0) {
                contractOfFlowDto.setCanDeductAmount(contractOfFlowDto.getBizToDeductAmount());
            } else {
                contractOfFlowDto.setCanDeductAmount(flowToDeductAmount);
            }

            flowToDeductAmount = flowToDeductAmount.subtract(contractOfFlowDto.getCanDeductAmount());
        }
    }

    /**
     * 为自动认款方式计算可以抵扣金额
     *
     * @param flowContractDto
     * @param contractOfFlowDtos
     */
    private void calculateCanDeductAmountForAutoClaim(CanDeductFlowDto flowContractDto, List<CanDeductContractDto> contractOfFlowDtos) {
        BigDecimal flowHasDeductAmount = flowContractDto.getTotalDeductedAmount(); // 流水已抵扣
        BigDecimal flowToDeductAmount; // 流水待抵扣
        for (CanDeductContractDto contractOfFlowDto : contractOfFlowDtos) {
            // 针对每个抵扣合同重新计算流水待抵扣
            // FIXME flowToDeductAmount 循环里不做扣减？
            flowToDeductAmount = flowContractDto.getToDeductAmount();
            // 合同待抵扣 <= 流水待抵扣，就扣合同的待抵扣；否则就抵扣流水的待抵扣
            if (contractOfFlowDto.getToDeductAmount().compareTo(flowToDeductAmount) <= 0) {
                contractOfFlowDto.setCanDeductAmount(contractOfFlowDto.getToDeductAmount());
                flowHasDeductAmount = flowHasDeductAmount.add(contractOfFlowDto.getToDeductAmount());
            } else {
                contractOfFlowDto.setCanDeductAmount(flowToDeductAmount);
                flowHasDeductAmount = flowHasDeductAmount.add(flowToDeductAmount);
            }
        }
    }

    /**
     * 根据流水 id 查询简单的回款流水信息，没有合同层级的信息
     *
     * @param flowId
     * @return
     */
    public CanDeductFlowDto querySimpleClaimInfoByFlowId(Integer flowId) {
        if (!Utils.isPositive(flowId)) {
            throw new ServiceRuntimeException("flow id can't be null!");
        }

        CanDeductFlowDto canDeductFlowDto = CanDeductFlowDto.builder().flowId(flowId).build();
        // 流水维度
        CrmFlowAdditionInfoPo crmFlowAdditionInfoPo = crmFlowAdditionInfoRepo.queryByFlowId(flowId);
        canDeductFlowDto.setFlowId(flowId);
        canDeductFlowDto.setAmount(new BigDecimal(crmFlowAdditionInfoPo.getAmount()));
        canDeductFlowDto.setRefundAmount(new BigDecimal(crmFlowAdditionInfoPo.getRefundAmount()));
        canDeductFlowDto.setTotalDeductedAmount(new BigDecimal(crmFlowAdditionInfoPo.getTotalDeductedAmount()));
        canDeductFlowDto.setToDeductAmount(new BigDecimal(crmFlowAdditionInfoPo.getAmount() - crmFlowAdditionInfoPo.getTotalDeductedAmount() - crmFlowAdditionInfoPo.getRefundAmount()));
        return canDeductFlowDto;
    }

    /**
     * 判断：是否是合约流水；是否匹配到了客户；如果是代打款，子流程是否通过；
     *
     * @param flowId
     * @param flowPo
     */
    private void checkFlow(Integer flowId, CrmRevenueExpenditureFlowPo flowPo) {
        if (flowPo == null) {
            throw new ServiceRuntimeException("流水不存在！");
        }

        if (!Lists.newArrayList(ReceiveMoneyFlowTypeEnum.CONTRACT_RECEIVE_MONEY.getCode(), ReceiveMoneyFlowTypeEnum.CREDIT_RECEIVE_MONEY.getCode()).contains(flowPo.getType())) {
            throw new ServiceRuntimeException("该流水不是合约流水！");
        }
        if (!Utils.isPositive(flowPo.getBindCustomerId())) {
            throw new ServiceRuntimeException("该流水没有绑定客户！");
        }
        if (IsProxyPayEnum.PROXY_PAY.getCode().equals(flowPo.getIsProxyPay())) {
            CrmWorkFlowPo crmWorkFlowPo = crmWorkFlowRepo.queryProcessingFlowByRefIdAndType(flowId, WorkFlowTypeEnum.PROXY_PAY_FLOW.getCode());
            if (crmWorkFlowPo != null) {
                throw new ServiceRuntimeException("该合约流水存在未审核完的代打款证明流程！");
            }
//            if (AuditStatusEnum.PASS.getCode().equals(crmWorkFlowPo.getAuditStatus())) {
//                throw new ServiceRuntimeException("该合约流水代打款流程未审核通过！");
//            }
        }
    }

    /**
     * 是否扣款完成
     *
     * @param claimFlowDto
     * @return
     */
    public Boolean isDeductCompleted(ClaimFlowDto claimFlowDto) {
        Boolean isCompletedClaim;
        CanDeductFlowDto claimInfoAfterClaim = this.querySimpleClaimInfoByFlowId(claimFlowDto.getFlowId());
        if (claimInfoAfterClaim == null) {
            throw new ServiceRuntimeException("流水不存在！flowId:" + claimFlowDto.getFlowId());
        }
        BigDecimal notClaimMoney = claimInfoAfterClaim.getToDeductAmount();
        isCompletedClaim = notClaimMoney.equals(BigDecimal.ZERO);
        log.info("=====> isDeductCompleted:{}, flowId:{}", isCompletedClaim, claimFlowDto.getFlowId());
        return isCompletedClaim;
    }
}
