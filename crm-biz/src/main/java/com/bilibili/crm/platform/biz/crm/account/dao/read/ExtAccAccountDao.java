package com.bilibili.crm.platform.biz.crm.account.dao.read;

import com.bapis.ad.crm.account.AccountIdsItem;
import com.bilibili.crm.platform.biz.bean.*;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.sql.Timestamp;
import java.util.List;

@Repository
public interface ExtAccAccountDao {

    List<AccAccountPo> getAllAccountIdsByCustomerIds(@Param("customerIds") List<Integer> customerIds);

    List<AccAccountPo> getAllAccountByCustomerIdsAndStatus(@Param("customerIds") List<Integer> customerIds,@Param("status") List<Integer> status);

    List<AccountAgentExtPO> getAllAccountAndAgentMapList();

    List<AccountAgentExtPO> getAllOrgAccountAndAgentMapList();

    List<AccountGroupExtPO> getAllAccountAndGroupMapList();

    List<AgentIdAccountExtPO> getAllAgentAndAccountMapList();

    List<AccountCustomerExtPO> getAllOrgAccountMapList();

    List<AccountCustomerExtPO> getAccCustomerIdByAccountIds(@Param("accountIds") List<Integer> accountIds);
//    List<AgentIdAccountIdUsernameExtPO> getAllAgentAccountIdAndOperateList();

    List<Integer> getAllInnerAgentAccountIdList();

    List<Integer> getAllOrgAdAccountIdList();
//    List<Integer> getAllOrgAgentAccountIdList();

    List<Integer> getAllHaveAgentCustomerIdsWithAccountId();

//    List<Integer> queryAllHaveChangeAgentOperateList(@Param("mTime") Timestamp mTime);

    List<Integer> queryFilterAccountIdListWithIndustryForOrder(@Param("accUnitedFirstIndustryIds") List<Integer> accUnitedFirstIndustryIds,
                                                               @Param("accUnitedSecondIndustryIds") List<Integer> accUnitedSecondIndustryIds,
                                                               @Param("accUnitedThirdIndustryIds") List<Integer> accUnitedThirdIndustryIds,
                                                               @Param("accountIds") List<Integer> accountIds);

    List<Integer> getAllAgentIdByAgentAccountIds(@Param("agentAccountIds") List<Integer> agentAccountIds);


    List<AccountIdsItemInfoPO> getAccountIdsItemInfo(@Param("accountIds") List<Integer> accountIds);

    List<AccountIdsItemInfoPO> getAgentInfoByAgentIds(@Param("agentIds") List<Integer> agentIds);

    List<AccountIdsItemInfoPO> getLinkedAdAccountByAgentAccountIds(@Param("agentAccountIds") List<Integer> agentAccountIds);

}
