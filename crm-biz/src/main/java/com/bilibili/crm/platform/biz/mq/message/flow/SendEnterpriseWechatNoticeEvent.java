package com.bilibili.crm.platform.biz.mq.message.flow;

import com.bilibili.crm.platform.api.policy.dto.EnterpriseWechatNoticeInfo;
import lombok.Getter;
import lombok.extern.slf4j.Slf4j;
import org.springframework.context.ApplicationEvent;
import org.springframework.stereotype.Component;

import java.io.Serializable;

/**
 * <AUTHOR>
 * @date 2022/1/22 上午11:17
 */
@Slf4j
public class SendEnterpriseWechatNoticeEvent extends ApplicationEvent implements Serializable {

    private static final long serialVersionUID = 4172035675685933475L;

    @Getter
    private EnterpriseWechatNoticeInfo enterpriseWechatNoticeInfo;

    /**
     * Create a new ApplicationEvent.
     *
     * @param source the object on which the event initially occurred (never {@code null})
     */
    public SendEnterpriseWechatNoticeEvent(Object source, EnterpriseWechatNoticeInfo enterpriseWechatNoticeInfo) {
        super(source);

        this.enterpriseWechatNoticeInfo = enterpriseWechatNoticeInfo;
    }
}
