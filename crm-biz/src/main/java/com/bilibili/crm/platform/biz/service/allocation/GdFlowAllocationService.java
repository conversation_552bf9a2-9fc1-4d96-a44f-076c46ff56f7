package com.bilibili.crm.platform.biz.service.allocation;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.allocation.IGdFlowAllocationService;
import com.bilibili.crm.platform.api.allocation.dto.GdFlowAllocationDto;
import com.bilibili.crm.platform.biz.dao.GdFlowAllocationDao;
import com.bilibili.crm.platform.biz.po.GdFlowAllocationPo;
import com.bilibili.crm.platform.biz.po.GdFlowAllocationPoExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.stream.Collectors;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-06-19
 **/
@Service
public class GdFlowAllocationService implements IGdFlowAllocationService {

    @Autowired
    private GdFlowAllocationDao gdFlowAllocationDao;

    @Override
    public List<GdFlowAllocationDto> queryInScheduleIds(List<Integer> scheduleIds) {
        GdFlowAllocationPoExample example = new GdFlowAllocationPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andScheduleIdIn(scheduleIds);

        List<GdFlowAllocationPo> pos = gdFlowAllocationDao.selectByExample(example);
        return pos.stream().map(this::convertPo2Dto).collect(Collectors.toList());
    }

    private GdFlowAllocationDto convertPo2Dto(GdFlowAllocationPo po){
        return GdFlowAllocationDto.builder()
                .scheduleId(po.getScheduleId())
                .source(po.getSource())
                .cpms(po.getCpms())
                .launchDay(po.getLaunchDay())
                .build();
    }
}
