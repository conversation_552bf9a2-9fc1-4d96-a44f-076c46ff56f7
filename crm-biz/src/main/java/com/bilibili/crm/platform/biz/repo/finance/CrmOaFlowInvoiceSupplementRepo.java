package com.bilibili.crm.platform.biz.repo.finance;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.finance.dto.automation.OaFlowInvoiceSupplementDto;
import com.bilibili.crm.platform.biz.dao.CrmOaFlowInvoiceSupplementDao;
import com.bilibili.crm.platform.biz.po.CrmOaFlowInvoiceSupplementPo;
import com.bilibili.crm.platform.biz.po.CrmOaFlowInvoiceSupplementPoExample;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Repository;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-08-03 10:34:08
 * @description:
 **/

@Slf4j
@Repository
public class CrmOaFlowInvoiceSupplementRepo {

    @Resource
    private CrmOaFlowInvoiceSupplementDao crmOaFlowInvoiceSupplementDao;


    public Long insertUpdate(OaFlowInvoiceSupplementDto dto) {
        if (dto.getProjLineNo() == null) {
            dto.setProjLineNo(1);
        }
        CrmOaFlowInvoiceSupplementPoExample example = new CrmOaFlowInvoiceSupplementPoExample();
        CrmOaFlowInvoiceSupplementPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andCrmOaFlowIdEqualTo(dto.getCrmOaFlowId());
        if (!StringUtils.isBlank(dto.getInvoiceCode())) {
            criteria.andInvoiceCodeEqualTo(dto.getInvoiceCode());
        }
        criteria.andProjLineNoEqualTo(dto.getProjLineNo());
        List<CrmOaFlowInvoiceSupplementPo> invoiceSupplementPos = crmOaFlowInvoiceSupplementDao.selectByExample(example);
        if(!CollectionUtils.isEmpty(invoiceSupplementPos)){
            invoiceSupplementPos.forEach(po -> {
                po.setIsDeleted(IsDeleted.DELETED.getCode());
                po.setMtime(new Timestamp(System.currentTimeMillis()));
                crmOaFlowInvoiceSupplementDao.updateByPrimaryKeySelective(po);
            });
        }
        CrmOaFlowInvoiceSupplementPo po = new CrmOaFlowInvoiceSupplementPo();
        BeanUtils.copyProperties(dto, po);
        po.setIsDeleted(IsDeleted.VALID.getCode());
        po.setCtime(new Timestamp(System.currentTimeMillis()));
        po.setMtime(new Timestamp(System.currentTimeMillis()));
        crmOaFlowInvoiceSupplementDao.insertSelective(po);
        return po.getId();
    }

    public OaFlowInvoiceSupplementDto queryInvoiceNumber(Integer crmOaFlowId, String invoiceCode, Integer projLineNo) {
        if (projLineNo == null) {
            projLineNo = 1;
        }
        CrmOaFlowInvoiceSupplementPoExample example = new CrmOaFlowInvoiceSupplementPoExample();
        CrmOaFlowInvoiceSupplementPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andCrmOaFlowIdEqualTo(crmOaFlowId);
        criteria.andInvoiceCodeEqualTo(invoiceCode);
        criteria.andProjLineNoEqualTo(projLineNo);
        List<CrmOaFlowInvoiceSupplementPo> invoiceSupplementPos = crmOaFlowInvoiceSupplementDao.selectByExample(example);
        if (CollectionUtils.isEmpty(invoiceSupplementPos)) {
            return null;
        }
        return OaFlowInvoiceSupplementDto.builder()
                .suppleId(invoiceSupplementPos.get(0).getId())
                .invoiceNumber(invoiceSupplementPos.get(0).getInvoiceNumber())
                .build();
    }

    public List<OaFlowInvoiceSupplementDto> queryByFlow(Integer crmOaFlowId) {
        CrmOaFlowInvoiceSupplementPoExample example = new CrmOaFlowInvoiceSupplementPoExample();
        CrmOaFlowInvoiceSupplementPoExample.Criteria criteria = example.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        criteria.andCrmOaFlowIdEqualTo(crmOaFlowId);
        List<CrmOaFlowInvoiceSupplementPo> invoiceSupplementPos = crmOaFlowInvoiceSupplementDao.selectByExample(example);
        if (CollectionUtils.isEmpty(invoiceSupplementPos)) {
            return null;
        }
        return CrmUtils.convert(invoiceSupplementPos, a -> OaFlowInvoiceSupplementDto.builder()
                .suppleId(a.getId())
                .crmOaFlowId(crmOaFlowId)
                .invoiceCode(a.getInvoiceCode())
                .invoiceNumber(a.getInvoiceNumber())
                .projLineNo(a.getProjLineNo())
                .build());
    }


}
