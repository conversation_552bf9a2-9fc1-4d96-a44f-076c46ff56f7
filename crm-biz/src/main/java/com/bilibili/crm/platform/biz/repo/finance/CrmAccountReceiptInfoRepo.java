package com.bilibili.crm.platform.biz.repo.finance;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.dao.CrmAccountReceiptInfoDao;
import com.bilibili.crm.platform.biz.po.CrmAccountReceiptInfoPo;
import com.bilibili.crm.platform.biz.po.CrmAccountReceiptInfoPoExample;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.util.Collections;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2021/4/30 5:01 下午
 */
@Repository
public class CrmAccountReceiptInfoRepo {

    @Autowired
    private CrmAccountReceiptInfoDao crmAccountReceiptInfoDao;

    public List<CrmAccountReceiptInfoPo> queryByAccountIds(List<Integer> accountIds) {
        if (CollectionUtils.isEmpty(accountIds)) {
            return Collections.EMPTY_LIST;
        }
        CrmAccountReceiptInfoPoExample example = new CrmAccountReceiptInfoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdIn(accountIds);
        example.setOrderByClause("id desc");
        List<CrmAccountReceiptInfoPo> accountReceiptInfoPos = crmAccountReceiptInfoDao.selectByExample(example);
        return accountReceiptInfoPos;
    }

    public CrmAccountReceiptInfoPo queryByAccountId(Integer accountId) {
        if (!Utils.isPositive(accountId)) {
            return null;
        }
        CrmAccountReceiptInfoPoExample example = new CrmAccountReceiptInfoPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andAccountIdEqualTo(accountId);
        List<CrmAccountReceiptInfoPo> accountReceiptInfoPos = crmAccountReceiptInfoDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accountReceiptInfoPos)) {
            return null;
        }
        return accountReceiptInfoPos.get(0);
    }
}
