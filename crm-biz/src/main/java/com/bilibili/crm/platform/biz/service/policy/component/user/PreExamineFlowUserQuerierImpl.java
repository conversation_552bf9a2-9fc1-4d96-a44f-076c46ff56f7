package com.bilibili.crm.platform.biz.service.policy.component.user;

import com.bilibili.crm.platform.api.policy.enums.FlowTypeEnum;
import com.bilibili.crm.platform.biz.service.policy.component.core.querier.DefaultFlowQuerier;
import org.apache.commons.lang3.StringUtils;
import org.flowable.engine.ProcessEngine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.List;

/**
 * 流程的预选审核人：预审任务的 assignee
 *
 * <AUTHOR>
 * @date 2021/8/8 下午11:40
 */
@Component(value = "PreExamineFlowUserQuerierImpl")
public class PreExamineFlowUserQuerierImpl implements IFlowUserQuerier {

    @Autowired
    private ProcessEngine processEngine;
    @Autowired
    private DefaultFlowQuerier fwFlowProcessor;

    @Override
    public List<String> queryUsers(FlowTypeEnum flowTypeEnum, String processInstanceId, String nodeId) {
        Assert.isTrue(StringUtils.isNotEmpty(processInstanceId), "流程实例id不能为空！");

        // 获取预审节点人员
        return fwFlowProcessor.queryPreExampleUsersByProcessInstanceId(processInstanceId);
    }
}
