package com.bilibili.crm.platform.biz.service.income.config.product;

import com.bilibili.adp.common.enums.SalesType;
import com.bilibili.crm.platform.api.account.dto.QueryAccountParam;
import com.bilibili.crm.platform.api.income.dto.ProductIncomeComposition;
import com.bilibili.crm.platform.api.income.dto.IncomeQueryCondition;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.biz.annotation.CacheAchieveCondition;
import com.bilibili.crm.platform.biz.service.income.helper.IncomeConfigHelper;
import com.bilibili.crm.platform.common.AdSupportStatus;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.UserType;
import com.bilibili.location.common.ResourceTypeEnum;
import com.google.common.collect.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@Component
public class ProductPvConfig {
    @Autowired
    public IncomeConfigHelper incomeConfigHelper;

    public List<ProductIncomeComposition> getProductQuotaComposition() {
        return Stream.of(
                ProductIncomeComposition.CPC_INFORMATION,
                ProductIncomeComposition.CPC_PLAY_PAGE,
                ProductIncomeComposition.CPM,
                ProductIncomeComposition.DPA,
                ProductIncomeComposition.ADX,
//                ProductIncomeComposition.MAS,
                ProductIncomeComposition.CONTENT_FLY,
                ProductIncomeComposition.PERSON_FLY,
                ProductIncomeComposition.BUSINESS_FLY,
                ProductIncomeComposition.CPT,
                ProductIncomeComposition.SSA_CPT,
                ProductIncomeComposition.GD)
                .collect(Collectors.toList());
    }
    @CacheAchieveCondition
    public IncomeQueryCondition adxPvCondition() {
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.ADX.getCode()))
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition cptPvCondition() {
        List<Integer> contractIdByExternalAccount = incomeConfigHelper.getContractIdByExternalAccount();
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPT.getCode()))
                .orderTypeCondition(Lists.newArrayList(CrmOrderType.CPT.getCode()))
                .contractIdCondition(contractIdByExternalAccount)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition ssaCptPvCondition() {
        List<Integer> contractIdByExternalAccount = incomeConfigHelper.getContractIdByExternalAccount();
        return IncomeQueryCondition.builder()
                //.salesTypeCondition(Lists.newArrayList(SalesType.SSA_CPT.getCode()))
                .salesTypeCondition(Lists.newArrayList(SalesType.SSA_CPT_PLUS.getCode(), SalesType.SSA_CPT.getCode()))
                .orderTypeCondition(Lists.newArrayList(CrmOrderType.SSA_CPT.getCode()))
                .contractIdCondition(contractIdByExternalAccount)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition gdPvCondition() {

        List<Integer> contractIdByExternalAccount = incomeConfigHelper.getContractIdByExternalAccount();
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.GD.getCode()))
                .orderTypeCondition(Lists.newArrayList(CrmOrderType.GD.getCode()))
                .contractIdCondition(contractIdByExternalAccount)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition cpcPvCondition() {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isSupportDpa(AdSupportStatus.DISABLE.getCode())
                .isInner(IsValid.FALSE.getCode())
                .isSupportFly(AdSupportStatus.DISABLE.getCode())
                .userTypes(UserType.getNotPersonalFly())
                .build());
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPC.getCode()))
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition cpmPvCondition() {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isSupportContent(AdSupportStatus.DISABLE.getCode())
                .isSupportFly(AdSupportStatus.DISABLE.getCode())
                .userTypes(UserType.getNotPersonalFly())
                .isInner(IsValid.FALSE.getCode())
                .build());

        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPM.getCode()))
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition masPvCondition() {
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.MAS_CPM.getCode(),SalesType.MAS_CPC.getCode(),SalesType.MAS_CPS.getCode()))
                .build();
    }

    public IncomeQueryCondition topViewPvCondition() {
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(43))
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition dpaPvCondition() throws IncomeConditionInvalidException {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isInner(IsValid.FALSE.getCode())
                .isSupportDpa(IsValid.TRUE.getCode())
                .build());
        if (CollectionUtils.isEmpty(accountId)) {
            throw new IncomeConditionInvalidException();
        }
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPC.getCode()))
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition bsiFlyPvCondition() throws IncomeConditionInvalidException {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isInner(IsValid.FALSE.getCode())
                .isSupportFly(IsValid.TRUE.getCode())
                .build());
        if (CollectionUtils.isEmpty(accountId)) {
            throw new IncomeConditionInvalidException();
        }
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPM.getCode(), SalesType.CPC.getCode()))
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition personFlyPvCondition() throws IncomeConditionInvalidException {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isInner(IsValid.FALSE.getCode())
                .userType(UserType.PERSONAL_FLY.getCode())
                .build());
        if (CollectionUtils.isEmpty(accountId)) {
            throw new IncomeConditionInvalidException();
        }
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPM.getCode()))
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition contentFlyPvCondition() throws IncomeConditionInvalidException {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isInner(IsValid.FALSE.getCode())
                .isSupportContent(IsValid.TRUE.getCode())
                .build());
        if (CollectionUtils.isEmpty(accountId)) {
            throw new IncomeConditionInvalidException();
        }
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPM.getCode()))
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition cpcInformationPvCondition() {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isSupportDpa(AdSupportStatus.DISABLE.getCode())
                .isInner(IsValid.FALSE.getCode())
                .isSupportFly(AdSupportStatus.DISABLE.getCode())
                .userTypes(UserType.getNotPersonalFly())
                .build());

        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPC.getCode()))
                .srcTypeCondition(ResourceTypeEnum.FEEDS.getCode())
                .accountCondition(accountId)
                .build();
    }
    @CacheAchieveCondition
    public IncomeQueryCondition cpcPlayPagePvCondition() {
        List<Integer> accountId = incomeConfigHelper.getAccountId(QueryAccountParam.builder()
                .isSupportDpa(AdSupportStatus.DISABLE.getCode())
                .isInner(IsValid.FALSE.getCode())
                .isSupportFly(AdSupportStatus.DISABLE.getCode())
                .userTypes(UserType.getNotPersonalFly())
                .build());
        return IncomeQueryCondition.builder()
                .salesTypeCondition(Lists.newArrayList(SalesType.CPC.getCode()))
                .srcTypeCondition(ResourceTypeEnum.RELATED_SUGGESTION.getCode())
                .accountCondition(accountId)
                .build();
    }

}
