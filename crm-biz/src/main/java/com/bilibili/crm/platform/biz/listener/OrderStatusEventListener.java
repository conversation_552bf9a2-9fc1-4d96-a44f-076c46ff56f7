package com.bilibili.crm.platform.biz.listener;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.brand.api.schedule.soa.dto.GdScheduleDto;
import com.bilibili.brand.api.schedule.soa.service.ISoaGdScheduleService;
import com.bilibili.crm.platform.api.config.ISystemConfigService;
import com.bilibili.crm.platform.api.contract.dto.ContractBaseDto;
import com.bilibili.crm.platform.api.contract.service.IContractService;
import com.bilibili.crm.platform.api.order.dto.OrderBaseDto;
import com.bilibili.crm.platform.api.order.service.IOrderService;
import com.bilibili.crm.platform.biz.event.OrderStatusEvent;
import com.bilibili.crm.platform.common.CrmOrderStatus;
import com.bilibili.crm.platform.common.CrmOrderType;
import com.bilibili.crm.platform.common.SystemConfig;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.context.ApplicationListener;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.List;
import java.util.stream.Collectors;

/**
 * Created by fanwenbin on 2017/7/14.
 */
@Component
public class OrderStatusEventListener implements ApplicationListener<OrderStatusEvent> {
    private final Logger LOGGER = LoggerFactory.getLogger(this.getClass());
    @Autowired
    private ISoaGdScheduleService gdScheduleService;
    @Autowired
    private ISystemConfigService systemConfigService;
    @Autowired
    private IContractService contractService;
    @Autowired
    private IOrderService orderService;
    @Autowired
    private IMailService mailService;

    @Override
    public void onApplicationEvent(OrderStatusEvent event) {
        System.setProperty("es.set.netty.runtime.available.processors", "false");
        LOGGER.info("orderStatusEventListener event {}", event.toString());
        switch (CrmOrderStatus.getByCode(event.getToStatus())) {
            case COMPLETED:
                if (event.getOrderType().equals(CrmOrderType.GD.getCode())) {
                    try {
                        this.sendGdCompleteMail(event);
                    } catch (ServiceException e) {
                        LOGGER.error("sendGdCompleteMail.error", e);
                    }
                }
                break;
            default:
                break;
        }
    }

    private void sendGdCompleteMail(OrderStatusEvent event) throws ServiceException {
        List<GdScheduleDto> gdScheduleDtos = gdScheduleService.getGdScheduleDtosByCrmOrderId(event.getOrderId());

        boolean sendEmail = false;
        String sysValue = systemConfigService.getConfigValue(SystemConfig.GD_SCHEDULE_SUPPLY_THRESHOLD);
        Integer sysScheduleCpm = StringUtils.isEmpty(sysValue) ? 0 : Integer.parseInt(sysValue);
        for (GdScheduleDto gdScheduleDto : gdScheduleDtos) {
            if (gdScheduleDto.getTargetCpm() - gdScheduleDto.getActualCpm() >= sysScheduleCpm) {
                sendEmail = true;
                break;
            }
        }

        if (sendEmail) {
            List<String> persons = contractService.getEmailPersonByContractId(event.getContractId());
            if (CollectionUtils.isEmpty(persons)) {
                return;
            }
            ContractBaseDto contractBaseDto = contractService.getContractBaseDtoById(event.getContractId());
            OrderBaseDto orderBaseDto = orderService.getOrderByCrmOrderId(event.getOrderId());
            String title = "【CRM】" + contractBaseDto.getName() + "的GD订单出现'待补量'状态";
            String url = "http://cm-mng.bilibili.co/crm/#/contract/list/show/general/" + contractBaseDto.getId();
            String article = "您好，您负责的【CRM】" + contractBaseDto.getName() + "合同中的GD订单"
                    + " (" + event.getOrderId() + " : " + orderBaseDto.getExplanation() + ") "
                    + "出现‘待补量’状态，请与客户沟通当前待补量GD订单的处理结果后进行下一步操作。传送门："
                    + "<a href=\"" + url + "\">" + url + "</a></h2>";
            MailMessage mailMessage = new MailMessage();
            mailMessage.setUseHtml(true);
            mailMessage.setHasFile(false);
            mailMessage.setSubject(title);
            mailMessage.setText(article);
            mailMessage.setTos(persons.stream().map(person -> person + "@bilibili.com").collect(Collectors.toList()));
            try {
                mailService.send(mailMessage);
            } catch (ServiceException e) {
                LOGGER.error("mailService.send.error", e);
            }
        }
    }
}
