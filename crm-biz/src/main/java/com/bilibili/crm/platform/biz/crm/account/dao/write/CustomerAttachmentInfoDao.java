package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CustomerAttachmentInfoPo;
import com.bilibili.crm.platform.biz.po.CustomerAttachmentInfoPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CustomerAttachmentInfoDao {
    long countByExample(CustomerAttachmentInfoPoExample example);

    int deleteByExample(CustomerAttachmentInfoPoExample example);

    int deleteByPrimaryKey(Integer id);

    int insertUpdate(CustomerAttachmentInfoPo record);

    int insertBatch(List<CustomerAttachmentInfoPo> records);

    int insertUpdateBatch(List<CustomerAttachmentInfoPo> records);

    int insert(CustomerAttachmentInfoPo record);

    int insertUpdateSelective(CustomerAttachmentInfoPo record);

    int insertSelective(CustomerAttachmentInfoPo record);

    List<CustomerAttachmentInfoPo> selectByExample(CustomerAttachmentInfoPoExample example);

    CustomerAttachmentInfoPo selectByPrimaryKey(Integer id);

    int updateByExampleSelective(@Param("record") CustomerAttachmentInfoPo record, @Param("example") CustomerAttachmentInfoPoExample example);

    int updateByExample(@Param("record") CustomerAttachmentInfoPo record, @Param("example") CustomerAttachmentInfoPoExample example);

    int updateByPrimaryKeySelective(CustomerAttachmentInfoPo record);

    int updateByPrimaryKey(CustomerAttachmentInfoPo record);
}