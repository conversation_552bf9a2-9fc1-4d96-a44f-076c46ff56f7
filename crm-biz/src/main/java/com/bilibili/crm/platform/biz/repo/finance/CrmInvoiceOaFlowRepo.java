package com.bilibili.crm.platform.biz.repo.finance;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.finance.dto.CrmInvoiceOaFlowDto;
import com.bilibili.crm.platform.biz.dao.CrmInvoiceOaFlowDao;
import com.bilibili.crm.platform.biz.po.CrmInvoiceOaFlowPo;
import com.bilibili.crm.platform.biz.po.CrmInvoiceOaFlowPoExample;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Repository;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2021/2/23
 */
@Slf4j
@Repository
public class CrmInvoiceOaFlowRepo {
    @Autowired
    private CrmInvoiceOaFlowDao crmInvoiceOaFlowDao;

    public Long countByExample(CrmInvoiceOaFlowPoExample example) {
        return crmInvoiceOaFlowDao.countByExample(example);
    }

    public List<CrmInvoiceOaFlowPo> queryByExample(CrmInvoiceOaFlowPoExample example) {
        return crmInvoiceOaFlowDao.selectByExample(example);
    }

    public List<CrmInvoiceOaFlowPo> queryCrmInvoiceOaFlowByIds(List<Integer> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.EMPTY_LIST;
        }
        CrmInvoiceOaFlowPoExample example = new CrmInvoiceOaFlowPoExample();
        example.or().andIdIn(invoiceIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmInvoiceOaFlowDao.selectByExample(example);
    }

    public CrmInvoiceOaFlowPo queryCrmInvoiceOaFlowByCrmOaFlowId(Integer oaFlowId) {
        if (oaFlowId == null) {
            return null;
        }
        CrmInvoiceOaFlowPoExample example = new CrmInvoiceOaFlowPoExample();
        example.or().andCrmOaFlowIdEqualTo(oaFlowId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmInvoiceOaFlowPo> crmInvoiceOaFlowPos = crmInvoiceOaFlowDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmInvoiceOaFlowPos)) {
            return null;
        }
        return crmInvoiceOaFlowPos.get(0);
    }

    public CrmInvoiceOaFlowPo queryCrmInvoiceOaFlowById(Integer id) {
        if (id == null) {
            return null;
        }
        CrmInvoiceOaFlowPoExample example = new CrmInvoiceOaFlowPoExample();
        example.or().andIdEqualTo(id)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmInvoiceOaFlowPo> crmInvoiceOaFlowPos = crmInvoiceOaFlowDao.selectByExample(example);
        if (CollectionUtils.isEmpty(crmInvoiceOaFlowPos)) {
            return null;
        }
        return crmInvoiceOaFlowPos.get(0);
    }

    public List<CrmInvoiceOaFlowPo> queryCrmInvoiceOaFlowByCrmOaFlowIds(List<Integer> oaFlowIds) {
        if (CollectionUtils.isEmpty(oaFlowIds)) {
            return Collections.EMPTY_LIST;
        }
        CrmInvoiceOaFlowPoExample example = new CrmInvoiceOaFlowPoExample();
        example.or().andCrmOaFlowIdIn(oaFlowIds)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmInvoiceOaFlowDao.selectByExample(example);
    }

    public Map<Integer, CrmInvoiceOaFlowPo> queryCrmInvoiceOaFlowMapByCrmOaFlowIds(List<Integer> oaFlowIds) {
        if (CollectionUtils.isEmpty(oaFlowIds)) {
            return Collections.EMPTY_MAP;
        }
        List<CrmInvoiceOaFlowPo> crmInvoiceOaFlowPos = queryCrmInvoiceOaFlowByCrmOaFlowIds(oaFlowIds);
        Map<Integer, CrmInvoiceOaFlowPo> invoiceOaFlowPoMap = crmInvoiceOaFlowPos.stream().collect(Collectors.toMap(t -> t.getCrmOaFlowId(), t -> t));
        return invoiceOaFlowPoMap;
    }

    public Map<Integer, CrmInvoiceOaFlowPo> queryCrmInvoiceOaFlowMapByIds(List<Integer> invoiceIds) {
        if (CollectionUtils.isEmpty(invoiceIds)) {
            return Collections.EMPTY_MAP;
        }
        List<CrmInvoiceOaFlowPo> crmInvoiceOaFlowPos = queryCrmInvoiceOaFlowByIds(invoiceIds);
        Map<Integer, CrmInvoiceOaFlowPo> invoiceOaFlowPoMap =
                crmInvoiceOaFlowPos.stream().collect(Collectors.toMap(t -> t.getId(), t -> t));
        return invoiceOaFlowPoMap;
    }

    public Integer insertCrmInvoiceOaFlow(CrmInvoiceOaFlowDto crmInvoiceOaFlowDto) {
        CrmInvoiceOaFlowPo crmInvoiceOaFlowPo = new CrmInvoiceOaFlowPo();
        BeanUtils.copyProperties(crmInvoiceOaFlowDto, crmInvoiceOaFlowPo);
        crmInvoiceOaFlowDao.insertSelective(crmInvoiceOaFlowPo);
        crmInvoiceOaFlowDto.setId(crmInvoiceOaFlowPo.getId());
        return crmInvoiceOaFlowPo.getId();
    }

    /**
     * 根据 oa 开票的 order id 获取开票流程信息
     *
     * @param orderId
     * @return
     */
    public CrmInvoiceOaFlowPo queryInvoiceOaFlowByOaOrderId(String orderId) {
        if (StringUtils.isEmpty(orderId)) {
            return null;
        }
        CrmInvoiceOaFlowPoExample example = new CrmInvoiceOaFlowPoExample();
        example.or().andOaOrderIdEqualTo(orderId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmInvoiceOaFlowDao.selectByExample(example).stream().findFirst().orElse(null);
    }

    public Integer updateCompleteTime(Timestamp completeTime, Integer crmOaFlowId) {
        log.info("=====> updateCompleteTime, oaFlowId:{}, completeTime:{}", crmOaFlowId, completeTime);
        CrmInvoiceOaFlowPo crmInvoiceOaFlowPo = new CrmInvoiceOaFlowPo();
        crmInvoiceOaFlowPo.setCompleteTime(completeTime);
        CrmInvoiceOaFlowPoExample example = new CrmInvoiceOaFlowPoExample();
        example.or().andCrmOaFlowIdEqualTo(crmOaFlowId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmInvoiceOaFlowDao.updateByExampleSelective(crmInvoiceOaFlowPo, example);
    }
}
