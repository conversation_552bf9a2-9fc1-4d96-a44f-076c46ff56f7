package com.bilibili.crm.platform.biz.service.oa.component;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.SystemType;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.dto.*;
import com.bilibili.crm.platform.api.finance.dto.automation.AccountReceiptInfoDto;
import com.bilibili.crm.platform.api.finance.dto.automation.CrmContractInfoForOaDto;
import com.bilibili.crm.platform.api.finance.enums.*;
import com.bilibili.crm.platform.api.oa.dto.OaBillPreSystemInfoDto;
import com.bilibili.crm.platform.api.oa.dto.OaBillSituationInfoDto;
import com.bilibili.crm.platform.api.oa.dto.OaContractRebateCheckBillPreInfoDetail;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractAuditStatus;
import com.bilibili.crm.platform.biz.convertor.AccountReceiptDtoConvertor;
import com.bilibili.crm.platform.biz.dao.CrmAccountCreditRechargeRecordDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.repo.AccAccountRepo;
import com.bilibili.crm.platform.biz.repo.CrmContractRepo;
import com.bilibili.crm.platform.biz.repo.CustomerRepo;
import com.bilibili.crm.platform.biz.repo.finance.*;
import com.bilibili.crm.platform.common.CrmOaFlowType;
import com.bilibili.crm.platform.common.InvoiceTargetType;
import com.bilibili.crm.platform.common.InvoiceType;
import com.bilibili.crm.platform.common.RechargeMode;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.math.NumberUtils;
import org.elasticsearch.common.Strings;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;

/**
 * oa 开票校验器
 *
 * <AUTHOR>
 * @date 2021/5/21 6:47 下午
 */
@Component
public class OaInvoiceValidator {

    public static final int INVOICE_AMOUNT_SCALE = 2;

    @Autowired
    private CrmContractRepo crmContractRepo;
    @Autowired
    private AccAccountRepo accAccountRepo;
    @Autowired
    private CrmContractOaFlowMappingRepo crmContractOaFlowMappingRepo;
    @Autowired
    private CrmOaFlowRepo crmOaFlowRepo;
    @Autowired
    private CrmRevenueExpenditureFlowRepo crmRevenueExpenditureFlowRepo;
    @Autowired
    private CrmAccountRechargeRecordRepo crmAccountRechargeRecordRepo;
    @Autowired
    private CrmInvoiceOaFlowRelationRepo crmInvoiceOaFlowRelationRepo;
    @Autowired
    private CrmAccountReceiptInfoRepo crmAccountReceiptInfoRepo;
    @Autowired
    private CustomerRepo customerRepo;
    @Autowired
    private CrmAccountCreditRechargeRecordDao crmAccountCreditRechargeRecordDao;
    @Resource
    private OaPreInfoQuerier oaPreInfoQuerier;

    public void checkBindOaInvoiceParam(InvoiceBindingDto invoiceBindingDto) {
        //合同是否存在
        CrmContractPo crmContractPo = crmContractRepo.queryById(invoiceBindingDto.getCrmContractId());
        if (null == crmContractPo) {
            throw new ServiceRuntimeException(String.format("合同不存在！crmContractId:%d",
                    invoiceBindingDto.getCrmContractId()));
        }

        //合同状态是否可绑定
        if (!ContractAuditStatus.SUCCESS.getCode().equals(crmContractPo.getAuditStatus())) {
            throw new ServiceRuntimeException(String.format("非审核通过状态,无法绑定！auditStatus:%s",
                    ContractAuditStatus.getByCode(crmContractPo.getAuditStatus()).name()));
        }

        //合同是否已经绑定
        List<Integer> oaFlowIds =
                crmContractOaFlowMappingRepo.queryCrmContractOaFlowIdsByContractId(invoiceBindingDto.getCrmContractId());

        //没有绑定OA流程
        if (CollectionUtils.isEmpty(oaFlowIds)) {
            return;
        }

        //流程主表
        List<CrmOaFlowPo> crmOaFlowList = crmOaFlowRepo.queryOaFlowByOaFlowIds(oaFlowIds);

        boolean isInvoiced = crmOaFlowList.stream().anyMatch(crmOaFlowPo ->
                CrmOaFlowType.INVOICE.getCode().equals(crmOaFlowPo.getType())
                        && (crmOaFlowPo.getStatus().equals(OAFlowStatusEnum.COMPLETED.getCode()) || crmOaFlowPo.getStatus().equals(OAFlowStatusEnum.PROCESSING.getCode())));
        if (isInvoiced) {
            throw new ServiceRuntimeException(String.format("该合同已开票,无法绑定！crmContractId:%d",
                    invoiceBindingDto.getCrmContractId()));
        }
    }

    public void checkContractCreateOaInvoiceParam(ContractInvoiceCreateDto invoiceCreateDto, Operator operator) {
        if (Operator.validateParamIsNull(operator)) {
            throw new ServiceRuntimeException("开票操作人不能为空");
        }
        if (NumberUtils.createBigDecimal(invoiceCreateDto.getAmount()).scale() > INVOICE_AMOUNT_SCALE) {
            throw new ServiceRuntimeException("开票金额应精确到小数点后两位");
        }
        if (null == InvoiceType.getByDesc(invoiceCreateDto.getInvoice_type())) {
            throw new ServiceRuntimeException("发票类型错误");
        }
        if (null == InvoiceTargetType.getType(invoiceCreateDto.getInvoice_target_type())) {
            throw new ServiceRuntimeException("受票方类型错误");
        }
        //合同是否存在
        CrmContractPo crmContractPo = crmContractRepo.queryById(invoiceCreateDto.getCrm_contract_id());
        if (null == crmContractPo) {
            throw new ServiceRuntimeException(String.format("合同不存在！crmContractId:%d",
                    invoiceCreateDto.getCrm_contract_id()));
        }
        //合同状态是否可开票
        if (!ContractAuditStatus.SUCCESS.getCode().equals(crmContractPo.getAuditStatus())) {
            throw new ServiceRuntimeException(String.format("非审核通过状态,无法绑定！crmContractId:%d, auditStatus:%s",
                    invoiceCreateDto.getCrm_contract_id(),
                    ContractAuditStatus.getByCode(crmContractPo.getAuditStatus()).name()));
        }
        //开票金额校验
        invoiceCreateDto.getDetail_list().forEach(
                item -> {
                    Integer crmContractId = item.getCrm_contract_id();
                    CrmContractInfoForOaDto contractInfoForOaDto = oaPreInfoQuerier.buildCrmContractInfoForOaDto(crmContractId);
                    String msgStr = "合同 " + crmContractId;

                    Assert.isTrue(item.getTotal_amount_with_tax().compareTo(contractInfoForOaDto.getInvoiceableAmount()) <= 0,
                            msgStr + " 折扣前金额需小于等于可开票金额");

                    Assert.isTrue(item.getTotal_rebate_amount().compareTo(item.getTotal_amount_with_tax()) < 0,
                            msgStr + " 折扣金额需小于折扣前金额");

                    Assert.isTrue(item.getContract_real_amount().compareTo(contractInfoForOaDto.getContractRealAmount()) != 0,
                            msgStr + " 合同实际金额有变化，请刷新后重新提交开票申请");

                    Assert.isTrue(item.getInvoiced_amount().compareTo(contractInfoForOaDto.getInvoicedAmount()) != 0,
                            msgStr + " 已开票的折扣前金额累计有变化，请刷新后重新提交开票申请");

                    Assert.isTrue(item.getInvoicing_amount().compareTo(contractInfoForOaDto.getInvoicingAmount()) != 0,
                            msgStr + " 开票中的折扣前金额累计有变化，请刷新后重新提交开票申请");

                }
        );
    }

    public void checkBatchContractCreateOaInvoiceParam(ContractBatchInvoiceCreateDto invoiceCreateDto, Operator operator) {
        //checkInvoiceType(invoiceCreateDto.getRequest_company(), invoiceCreateDto.getInvoice_type());
        if (Operator.validateParamIsNull(operator)) {
            throw new ServiceRuntimeException("开票操作人不能为空");
        }
        if (null == InvoiceType.getByDesc(invoiceCreateDto.getInvoice_type())) {
            throw new ServiceRuntimeException("发票类型错误");
        }
        if (null == InvoiceTargetType.getType(invoiceCreateDto.getInvoice_target_type())) {
            throw new ServiceRuntimeException("受票方类型错误");
        }

        List<Integer> crmContractIds =
                invoiceCreateDto.getDetail_list()
                        .stream()
                        .map(ContractBatchInvoiceCreateDto.ContractBatchInvoiceCreateDetailInfo::getCrm_contract_id)
                        .collect(Collectors.toList());

        List<CrmContractOaFlowMappingPo> list =
                crmContractOaFlowMappingRepo.queryCrmContractOaFlowListByContractIds(crmContractIds);

        List<Integer> oaFlowIds =
                list.stream()
                        .map(CrmContractOaFlowMappingPo::getOaFlowId)
                        .distinct()
                        .collect(Collectors.toList());

        //没有关联OA流程
        if (CollectionUtils.isEmpty(oaFlowIds)) {
            return;
        }

        //流程主表
//        List<CrmOaFlowPo> crmOaFlowList = crmOaFlowRepo.queryOaFlowByOaFlowIds(oaFlowIds);

//        boolean isInvoiced = crmOaFlowList.stream().anyMatch(crmOaFlowPo ->
//                CrmOaFlowType.INVOICE.getCode().equals(crmOaFlowPo.getType())
//                        && (crmOaFlowPo.getStatus().equals(OAFlowStatusEnum.COMPLETED.getCode()) || crmOaFlowPo.getStatus().equals(OAFlowStatusEnum.PROCESSING.getCode())));
//        if (isInvoiced) {
//            throw new ServiceRuntimeException(String.format("已有合同已开票,无法开票！"));
//        }
        //开票金额校验  build 开票金额
        invoiceCreateDto.getDetail_list().forEach(
                item -> {
                    Integer crmContractId = item.getCrm_contract_id();
                    CrmContractInfoForOaDto contractInfoForOaDto = oaPreInfoQuerier.buildCrmContractInfoForOaDto(crmContractId);
                    String msgStr = "合同 " + contractInfoForOaDto.getContractNo();

//                    Assert.isTrue(YesOrNoEnum.NO.getCode().equals(contractInfoForOaDto.getIsDeductCompleted()),
//                            msgStr + " 回款已完成，不支持开票");

                    Assert.isTrue(item.getTotal_amount_with_tax().compareTo(Utils.fromFenToYuan(contractInfoForOaDto.getInvoiceableAmount())) <= 0,
                            msgStr + " 折扣前金额需小于等于可开票金额");

                    Assert.isTrue(item.getTotal_rebate_amount().compareTo(item.getTotal_amount_with_tax()) < 0,
                            msgStr + " 折扣金额需小于折扣前金额");

                    Assert.isTrue(item.getContract_real_amount().compareTo(Utils.fromFenToYuan(contractInfoForOaDto.getContractRealAmount())) == 0,
                            msgStr + " 合同实际金额有变化，请刷新后重新提交开票申请");

                    Assert.isTrue(item.getInvoiced_amount().compareTo(Utils.fromFenToYuan(contractInfoForOaDto.getInvoicedAmount())) == 0,
                            msgStr + " 已开票的折扣前金额累计有变化，请刷新后重新提交开票申请");

                    Assert.isTrue(item.getInvoicing_amount().compareTo(Utils.fromFenToYuan(contractInfoForOaDto.getInvoicingAmount())) == 0,
                            msgStr + " 开票中的折扣前金额累计有变化，请刷新后重新提交开票申请");

//                    build 开票金额 = 折扣前金额 - 折扣金额
                    Assert.isTrue(item.getDetail_amount().compareTo(item.getTotal_amount_with_tax().subtract(item.getTotal_rebate_amount())) == 0,
                            msgStr + " 开票金额不对");

                    Assert.isTrue(item.getDetail_amount().compareTo(BigDecimal.ZERO) > 0,
                            msgStr + " 开票金额应大于0");
                }
        );

    }

    /**
     * 校验系统来源
     *
     * @param oaBillPreInfoQueryDto
     */
    public void checkSystemSourceCommonParams(OaBillPreSystemInfoDto oaBillPreInfoQueryDto) {
        if (oaBillPreInfoQueryDto == null) {
            throw new ServiceRuntimeException("参数错误！");
        }
        if (oaBillPreInfoQueryDto.getInvoiceSource() == null) {
            throw new ServiceRuntimeException("开票系统来源不能为空！");
        }

        List<Integer> canBillSystemSourceList = Arrays.asList(SystemType.CRM.getCode(), SystemType.AGENT.getCode(),
                SystemType.COMMERCIAL.getCode());
        if (!canBillSystemSourceList.contains(oaBillPreInfoQueryDto.getInvoiceSource())) {
            throw new ServiceRuntimeException("开票来源不合法！invoiceSource" + oaBillPreInfoQueryDto.getInvoiceSource());
        }
        if (StringUtils.isEmpty(oaBillPreInfoQueryDto.getDomainAccountName())) {
            throw new ServiceRuntimeException("域账号名称不能为空！");
        }

        // 各个业务的必填参数
        if (InvoiceBizTypeEnum.RECHARGE.getCode().equals(oaBillPreInfoQueryDto.getInvoiceBizType())) {
            if (oaBillPreInfoQueryDto.getInvoiceSource().equals(SystemType.AGENT.getCode())) {
                if (!Utils.isPositive(oaBillPreInfoQueryDto.getAgentId())) {
                    throw new ServiceRuntimeException("代理商充值开票时，代理商账号id不能为空");
                }
            } else {
//                if (!Utils.isPositive(oaBillPreInfoQueryDto.getAccountId())) {
//                    throw new ServiceRuntimeException("crm充值开票时，账号id不能为空");
//                }
            }
        } else if (InvoiceBizTypeEnum.PICK_UP_ORDER.getCode().equals(oaBillPreInfoQueryDto.getInvoiceBizType())) {
            if (oaBillPreInfoQueryDto.getInvoiceSource().equals(SystemType.PICK_UP.getCode())) {
                if (!Utils.isPositive(oaBillPreInfoQueryDto.getAccountId())) {
                    throw new ServiceRuntimeException("花火开票时，代理商账号id不能为空");
                }
            }
        }

        InvoiceBizTypeEnum invoiceBizTypeEnum = InvoiceBizTypeEnum.getByCode(oaBillPreInfoQueryDto.getInvoiceBizType());
        oaBillPreInfoQueryDto.setInvoiceBizTypeDesc(invoiceBizTypeEnum.getDesc());
        SystemType systemType = SystemType.getByCode(oaBillPreInfoQueryDto.getInvoiceSource());
        oaBillPreInfoQueryDto.setInvoiceSourceDesc(systemType.getDescCN());
    }

    /**
     * check 账号非花火权限 & 自动充值 & 充值都是同一个收款公司 & 是否已经开票
     * 要求这些充值都是同一个账户 id 的
     *
     * @param rechargeIds
     * @return 充值记录的账户与客户信息
     */
    public OaBillSituationInfoDto checkAutoRechargeInvoiceInfo(List<Integer> rechargeIds, Integer invoiceSource) {
        OaBillSituationInfoDto oaBillSituationInfoDto = OaBillSituationInfoDto.builder().build();
        if (CollectionUtils.isEmpty(rechargeIds)) {
            throw new ServiceRuntimeException("开票的充值列表不能为空！");
        }
        // 根据充值 ids 获取对应的充值方式，账号权限
        List<CrmAccountRechargeRecordPo> crmAccountRechargeRecordPos = crmAccountRechargeRecordRepo.queryByIds(rechargeIds);
        if (CollectionUtils.isEmpty(crmAccountRechargeRecordPos)) {
            throw new ServiceRuntimeException("充值记录不存在！");
        }

        // 都是自动充值
        List<CrmAccountRechargeRecordPo> manualRechargeList = crmAccountRechargeRecordPos.stream().filter(t -> RechargeMode.MANUAL.getCode().equals(t.getMode())).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(manualRechargeList)) {
            String manualRechargeIdStr =
                    manualRechargeList.stream().map(t -> String.valueOf(t.getId())).collect(Collectors.joining(","));
            throw new ServiceRuntimeException("存在充值记录为手动充值，不允许开票！rechargeIds: " + manualRechargeIdStr);
        }
        List<Integer> accountIds =
                crmAccountRechargeRecordPos.stream().map(t -> t.getAccountId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountIds) && accountIds.size() > 1) {
            throw new ServiceRuntimeException("充值记录不是同一个账号的请检查！");
        }

        AccAccountPo accAccountPoOfRecharge = accAccountRepo.queryAccountById(accountIds.get(0));
        // 获取客户下所有的账号
        List<Integer> accountIdsOfCustomer = accAccountRepo.queryAccountIdsByCustomerId(accAccountPoOfRecharge.getCustomerId());
        List<CrmAccountReceiptInfoPo> crmAccountReceiptInfoPos = crmAccountReceiptInfoRepo.queryByAccountIds(accountIdsOfCustomer);
        if (SystemType.AGENT.getCode().equals(invoiceSource)) {
            if (CollectionUtils.isEmpty(crmAccountReceiptInfoPos)) {
                throw new ServiceRuntimeException("该客户下的账号没有保存收件地址！");
            }
        }

        oaBillSituationInfoDto.setAccountId(accAccountPoOfRecharge.getAccountId());
        oaBillSituationInfoDto.setCustomerId(accAccountPoOfRecharge.getCustomerId());
        oaBillSituationInfoDto.setDepartmentId(accAccountPoOfRecharge.getDepartmentId());

        // 账号非花火权限
        if (accAccountPoOfRecharge.getIsSupportPickup().equals(1)) {
            throw new ServiceRuntimeException("充值记录的账号有花火商单，不允许开票！accountId:" + accAccountPoOfRecharge.getAccountId());
        }

        // check 充值都是同一个收款公司 父账号
        // 根据 rechargeIds 获取充值流水的收款公司
        List<Integer> flowIds = crmAccountRechargeRecordPos.stream().map(t -> t.getFlowId()).distinct().collect(Collectors.toList());
        List<CrmRevenueExpenditureFlowPo> crmRevenueExpenditureFlowPos = crmRevenueExpenditureFlowRepo.queryListByIds(flowIds);
        Set<String> receiveAccountNoSet = crmRevenueExpenditureFlowPos.stream().map(t -> t.getReceiveAccountNo()).collect(Collectors.toSet());
        if (receiveAccountNoSet.size() > 1) {
            throw new ServiceRuntimeException("充值记录不是同一个收款公司，不允许开票！");
        }
        oaBillSituationInfoDto.setReceiveAccountName(crmRevenueExpenditureFlowPos.get(0).getReceiveAccountName());

        // check 是否已经开票
        List<Integer> hasBilledRechargeList =
                crmAccountRechargeRecordPos.stream().filter(t -> BillStatusEnum.getCanNotBillStatusList().contains(t.getBillStatus())).map(t -> t.getId()).collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(hasBilledRechargeList)) {
            String hasBilledRechargeIdStr = hasBilledRechargeList.stream().map(t -> String.valueOf(t)).collect(Collectors.joining(","));
            throw new ServiceRuntimeException("充值记录已经开票，不允许开票！rechargeIds:" + hasBilledRechargeIdStr);
        }
        return oaBillSituationInfoDto;
    }

    @Deprecated
    public OaBillSituationInfoDto checkContractRebateCheckInvoiceInfo(List<OaContractRebateCheckBillPreInfoDetail> details) {
        OaBillSituationInfoDto oaBillSituationInfoDto = OaBillSituationInfoDto.builder().build();
        if (CollectionUtils.isEmpty(details)) {
            throw new ServiceRuntimeException("返点核算合同列表不能为空！");
        }

        // 获取合同号
        List<Long> contractNos = details.stream().map(t -> t.getContractNumber()).collect(Collectors.toList());

        // 检查合同是否同一个账号
        List<CrmContractPo> crmContractPos = crmContractRepo.queryByByNumbers(contractNos);
        if (CollectionUtils.isEmpty(crmContractPos)) {
            throw new ServiceRuntimeException("合同列表不存在！");
        }

//        List<Integer> contractIds = crmContractPos.stream().map(t -> t.getId()).collect(Collectors.toList());

        // 检查合同是否已经开票
        Optional<CrmContractPo> firstNotOpenBill = crmContractPos.stream().filter(t -> YesOrNoEnum.NO.getCode().equals(t.getIsOaOpenBill())).findFirst();
        if (firstNotOpenBill.isPresent()) {
            throw new ServiceRuntimeException("存在合同未开票！请检查！");
        }
        // 重复开票(一个合同允许重复开返点核算的票)
//                    oaInvoiceValidator.checkRepeatInvoice(Arrays.asList(InvoiceBizTypeEnum.REBATE_CHECK.getCode()), contractIds);

        List<Integer> accountIds =
                crmContractPos.stream().map(t -> t.getAccountId()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountIds) && accountIds.size() > 1) {
            throw new ServiceRuntimeException("合同不是同一个账号的请检查！");
        }
        AccAccountPo accAccountPoOfRecharge = accAccountRepo.queryAccountById(accountIds.get(0));
        oaBillSituationInfoDto.setAccountId(accAccountPoOfRecharge.getAccountId());
        oaBillSituationInfoDto.setCustomerId(accAccountPoOfRecharge.getCustomerId());
        oaBillSituationInfoDto.setDepartmentId(accAccountPoOfRecharge.getDepartmentId());
        return oaBillSituationInfoDto;
    }

    /**
     * 检查 oa 合同是否可以绑定
     *
     * @param contractBindingDto
     */
    public void checkBindOaContractParam(ContractBindingDto contractBindingDto) {
        //合同是否存在
        CrmContractPo crmContractPo = crmContractRepo.queryById(contractBindingDto.getCrmContractId());
        if (null == crmContractPo) {
            throw new ServiceRuntimeException(String.format("合同不存在！crmContractId:%d",
                    contractBindingDto.getCrmContractId()));
        }
        //合同状态是否可绑定
        if (!ContractAuditStatus.SUCCESS.getCode().equals(crmContractPo.getAuditStatus())) {
            throw new ServiceRuntimeException(String.format("非审核通过状态,无法绑定！crmContractId:%d, auditStatus:%s",
                    contractBindingDto.getCrmContractId(),
                    ContractAuditStatus.getByCode(crmContractPo.getAuditStatus()).name()));
        }

        //合同是否已经绑定
        List<Integer> oaFlowIds =
                crmContractOaFlowMappingRepo.queryCrmContractOaFlowIdsByContractId(contractBindingDto.getCrmContractId());

        List<CrmOaFlowPo> crmOaFlowList = crmOaFlowRepo.queryOaFlowByOaFlowIds(oaFlowIds);

        if (crmOaFlowList.stream().filter(crmOaFlowPo -> CrmOaFlowType.CONTRACT.getCode().equals(crmOaFlowPo.getType())).count() > 0) {
            throw new ServiceRuntimeException(String.format("已绑定归档合同,无法绑定！crmContractId:%d",
                    contractBindingDto.getCrmContractId()));
        }
    }

    public void checkPickupInvoiceParams(PickupBatchInvoiceCreateDto batchInvoiceCreateDto) {
        //checkInvoiceType(batchInvoiceCreateDto.getRequest_company(), batchInvoiceCreateDto.getInvoice_type());
        StringBuilder sbMsg = new StringBuilder();
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getAmount())) {
            sbMsg.append("开票总金额不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getRequest_company())) {
            sbMsg.append("申请公司不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getInvoice_target_type())) {
            sbMsg.append("受票方类型不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getInvoice_type())) {
            sbMsg.append("发票类型不可为空!");
        }
        if (!Utils.isPositive(batchInvoiceCreateDto.getAccount_id())) {
            sbMsg.append("账号id不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getTaxpayer_id())) {
            sbMsg.append("纳税人识别号不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getTotal_rebate_amount())) {
            sbMsg.append("折扣金额不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getTotal_amount_with_tax())) {
            sbMsg.append("折扣前价税合计金额不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getService_name())) {
            sbMsg.append("货物或应税劳务、服务名称不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getFile_id())) {
            sbMsg.append("附件文件id不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getFile_name())) {
            sbMsg.append("附件文件名称不可为空!");
        }
        // check 账号非花火权限 & 自动充值
        List<Integer> orderIds =
                batchInvoiceCreateDto.getDetail_list().stream().map(t -> t.getOrder_id()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            sbMsg.append("花火订单 ids 不能为空!");
        }
        if (sbMsg.length() > 0) {
            throw new ServiceRuntimeException(sbMsg.toString());
        }
    }

    public void checkRechargeInvoiceParams(RechargeBatchInvoiceCreateDto batchInvoiceCreateDto) {
        //checkInvoiceType(batchInvoiceCreateDto.getRequest_company(), batchInvoiceCreateDto.getInvoice_type());
        StringBuilder sbMsg = new StringBuilder();
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getAmount())) {
            sbMsg.append("开票总金额不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getRequest_company())) {
            sbMsg.append("申请公司不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getInvoice_target_type())) {
            sbMsg.append("受票方类型不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getInvoice_type())) {
            sbMsg.append("发票类型不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getTaxpayer_id())) {
            sbMsg.append("纳税人识别号不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getTotal_rebate_amount())) {
            sbMsg.append("折扣金额不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getTotal_amount_with_tax())) {
            sbMsg.append("折扣前价税合计金额不可为空!");
        }
        if (StringUtils.isEmpty(batchInvoiceCreateDto.getService_name())) {
            sbMsg.append("货物或应税劳务、服务名称不可为空!");
        }
        // check 账号非花火权限 & 自动充值
        List<Integer> orderIds =
                batchInvoiceCreateDto.getDetail_list().stream().map(t -> t.getRecharge_id()).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(orderIds)) {
            sbMsg.append("充值 ids 不能为空!");
        }
        if (sbMsg.length() > 0) {
            throw new ServiceRuntimeException(sbMsg.toString());
        }
    }

    public void checkCreditRepeatInvoice(List<Integer> relIds) {
        Assert.isTrue(!CollectionUtils.isEmpty(relIds),"充值明细ID为空");
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationRepo.queryLatestListByRefIds(Collections.singletonList(InvoiceBizTypeEnum.CREDIT_RECHARGE.getCode()), relIds);
        if (CollectionUtils.isEmpty(crmInvoiceOaFlowRelationPos)) {
            return;
        }
        List<Integer> oaFlowIds = crmInvoiceOaFlowRelationPos.stream().map(CrmInvoiceOaFlowRelationPo::getCrmOaFlowId).distinct().collect(Collectors.toList());
        List<CrmOaFlowPo> crmOaFlowPos = crmOaFlowRepo.queryOaFlowByOaFlowIds(oaFlowIds);
        List<CrmOaFlowPo> filterCrmOaFlowPos = crmOaFlowPos.stream().filter(t -> OAFlowStatusEnum.getCannotOpenOaStatusList().contains(t.getStatus())).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(filterCrmOaFlowPos),"存在明细已经有oa流程，请检查！");
    }

    public void checkCreditRechargeInfo(List<CrmAccountCreditRechargeRecordPo> poList) {
        Assert.isTrue(!CollectionUtils.isEmpty(poList),"充值记录不存在");
        List<Integer> accountIds = poList.stream().map(CrmAccountCreditRechargeRecordPo::getAccountId).distinct().collect(Collectors.toList());
        if (CollectionUtils.isNotEmpty(accountIds) && accountIds.size() > 1) {
            throw new ServiceRuntimeException("充值记录不是同一个账号的请检查！");
        }
        List<Long> hasBilledRechargeList = poList.stream().filter(t -> BillStatusEnum.getCanNotBillStatusList().contains(t.getBillStatus())).map(CrmAccountCreditRechargeRecordPo::getId).collect(Collectors.toList());
        Assert.isTrue(CollectionUtils.isEmpty(hasBilledRechargeList),"有充值记录已经开票，不允许开票！");
        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(accountIds.get(0));
        Assert.notNull(accAccountPo,"充值记录关联的账号异常");
        List<Integer> accountIdsOfCustomer = accAccountRepo.queryAccountIdsByCustomerId(accAccountPo.getCustomerId());
        List<CrmAccountReceiptInfoPo> crmAccountReceiptInfoPos = crmAccountReceiptInfoRepo.queryByAccountIds(accountIdsOfCustomer);
        Assert.isTrue(CollectionUtils.isNotEmpty(crmAccountReceiptInfoPos),"该客户下的账号没有保存收件地址！");
    }

    public String calculateCreditrechargeAmount(List<CrmAccountCreditRechargeRecordPo> poList) {
        Assert.isTrue(!CollectionUtils.isEmpty(poList), "充值记录不存在");
        Long fenAmount = poList.stream().map(CrmAccountCreditRechargeRecordPo::getAmount).reduce(0L, Long::sum);
        return Utils.fromFenToYuan(fenAmount).toString();
    }

    public AccountReceiptInfoDto getAccountReceipt(List<CrmAccountCreditRechargeRecordPo> poList){
        AccountReceiptInfoDto accountReceiptInfoDto = AccountReceiptInfoDto.builder().build();
        Assert.isTrue(!CollectionUtils.isEmpty(poList), "充值记录不存在");
        List<Integer> accountIds = poList.stream().map(CrmAccountCreditRechargeRecordPo::getAccountId).distinct().collect(Collectors.toList());
        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(accountIds.get(0));
        CrmAccountReceiptInfoPo crmAccountReceiptInfoPoOfSelf = crmAccountReceiptInfoRepo.queryByAccountId(accAccountPo.getAccountId());
        // 账户层级没有，则从客户下的任何一个账户上获取
        if (crmAccountReceiptInfoPoOfSelf == null) {
            List<Integer> accountIdsByCustomerIdList = accAccountRepo.queryAccountIdsByCustomerId(accAccountPo.getCustomerId());
            List<CrmAccountReceiptInfoPo> crmAccountReceiptInfoPos = crmAccountReceiptInfoRepo.queryByAccountIds(accountIdsByCustomerIdList);
            Optional<CrmAccountReceiptInfoPo> first = crmAccountReceiptInfoPos.stream().max(Comparator.comparing(CrmAccountReceiptInfoPo::getMtime));
            if (first.isPresent()) {
                CrmAccountReceiptInfoPo crmAccountReceiptInfoPo = first.get();
                accountReceiptInfoDto = AccountReceiptDtoConvertor.convertPo2Dto(crmAccountReceiptInfoPo);
                accountReceiptInfoDto.setExistAccountReceiptInfo(true);
            } else {
                accountReceiptInfoDto.setExistAccountReceiptInfo(false);
            }
        } else {
            accountReceiptInfoDto = AccountReceiptDtoConvertor.convertPo2Dto(crmAccountReceiptInfoPoOfSelf);
            accountReceiptInfoDto.setExistAccountReceiptInfo(true);
        }
        return accountReceiptInfoDto;
    }

    public AccountInfoDto getAccountInfoDto(List<CrmAccountCreditRechargeRecordPo> poList) {
        AccountInfoDto accountInfoDto = new AccountInfoDto();
        Assert.isTrue(!CollectionUtils.isEmpty(poList), "充值记录不存在");
        List<Integer> accountIds = poList.stream().map(CrmAccountCreditRechargeRecordPo::getAccountId).distinct().collect(Collectors.toList());
        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(accountIds.get(0));
        if (accAccountPo == null) {
            throw new ServiceRuntimeException("账号id不存在！accountId:" + accountIds.get(0));
        }
        CustomerPo customerPo = customerRepo.queryNotDeletedCustomerById(accAccountPo.getCustomerId());
        if (customerPo == null) {
            throw new ServiceRuntimeException("客户不存在！accountId:" + accountIds.get(0));
        }
        accountInfoDto.setAccount_id(accountIds.get(0));
        accountInfoDto.setAccount_name(accAccountPo.getUsername());
        accountInfoDto.setCustomer_id(customerPo.getId());
        accountInfoDto.setCustomer_name(customerPo.getUsername());
        accountInfoDto.setDepartment_id(accAccountPo.getDepartmentId());
        return accountInfoDto;
    }
    /**
     * 检查是否重复开了 oa 流程
     *
     * @param invoiceBizTypes
     * @param relIds
     */
    public void checkRepeatInvoice(List<Integer> invoiceBizTypes, List<Integer> relIds) {
        if (CollectionUtils.isEmpty(invoiceBizTypes)) {
            throw new ServiceRuntimeException("开票类型不能为空！");
        }
        // rel ids -> crm oa flow id -> flow status
        List<CrmInvoiceOaFlowRelationPo> crmInvoiceOaFlowRelationPos = crmInvoiceOaFlowRelationRepo.queryLatestListByRefIds(invoiceBizTypes, relIds);

        List<Integer> oaFlowIds = crmInvoiceOaFlowRelationPos.stream().map(t -> t.getCrmOaFlowId()).distinct().collect(Collectors.toList());
        List<CrmOaFlowPo> crmOaFlowPos = crmOaFlowRepo.queryOaFlowByOaFlowIds(oaFlowIds);

        if (invoiceBizTypes.contains(InvoiceBizTypeEnum.REBATE_CHECK.getCode())) {
            List<CrmOaFlowPo> processingOaFlowPos =
                    crmOaFlowPos.stream().filter(t -> OAFlowStatusEnum.PROCESSING.getCode().equals(t.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(processingOaFlowPos)) {
                throw new ServiceRuntimeException("该合同存在未完成的返点核算开票流程，请检查！");
            }
        } else {
            List<CrmOaFlowPo> processingOaFlowPos =
                    crmOaFlowPos.stream().filter(t -> OAFlowStatusEnum.getCannotOpenOaStatusList().contains(t.getStatus())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(processingOaFlowPos)) {
                throw new ServiceRuntimeException("存在明细已经有oa流程，请检查！");
            }
        }
    }

    /**
     * 创建效果后付费参数校验
     *
     * @param invoiceCreateDto
     * @param operator
     */
    public void checkPosPayOaInvoiceParam(PostPayInvoiceCreateDto invoiceCreateDto, Operator operator) {
        //checkInvoiceType(invoiceCreateDto.getRequest_company(), invoiceCreateDto.getInvoice_type());
        if (Operator.validateParamIsNull(operator)) {
            throw new ServiceRuntimeException("开票操作人不能为空");
        }
        if (NumberUtils.createBigDecimal(invoiceCreateDto.getAmount()).scale() > INVOICE_AMOUNT_SCALE) {
            throw new ServiceRuntimeException("开票金额应精确到小数点后两位");
        }
        if (null == InvoiceType.getByDesc(invoiceCreateDto.getInvoice_type())) {
            throw new ServiceRuntimeException("发票类型错误");
        }
    }
}
