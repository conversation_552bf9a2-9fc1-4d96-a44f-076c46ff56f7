package com.bilibili.crm.platform.biz.common;

import lombok.AllArgsConstructor;
import lombok.Getter;

@AllArgsConstructor
public enum PickupPaymentEnum {
    CD_PAYMENT(0, "超电自行支付"),
    HD_PAYMENT(1, "幻电自行支付"),
    CDTOHD_PAYMENT(2, "超电代幻电支付"),
    HDTOCD_PAYMENT(3, "幻电代超电支付"),
    UNKNOW_PAYMENT(4, "未知"),
    NO_CHECK(5,"不支付"),
    PICKUP_PAYMENT(6,"花火代付");


    @Getter
    private Integer code;
    @Getter
    private String name;

    public static String getDesc(Integer code) {
        for (PickupPaymentEnum type : PickupPaymentEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.name;
            }
        }
        return "";
    }

    public static Integer getByCode(Integer code) {
        for (PickupPaymentEnum type : PickupPaymentEnum.values()) {
            if (type.getCode().equals(code)) {
                return type.code;
            }
        }
        throw new IllegalArgumentException("unknown code PickupPaymentEnum " + code);
    }
}
