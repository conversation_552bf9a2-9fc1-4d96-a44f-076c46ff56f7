package com.bilibili.crm.platform.biz.service.customer.sea.helper;

import com.bilibili.crm.platform.api.account.dto.CategoryDto;
import com.bilibili.crm.platform.api.account.dto.CrmDepartmentDto;
import com.bilibili.crm.platform.api.account.dto.UnitedIndustryDto;
import com.bilibili.crm.platform.api.account.service.ICrmDepartmentService;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.sea.CustomerSeaListDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.biz.industry.service.UnitedIndustryService;
import com.bilibili.crm.platform.biz.po.AccCompanyGroupPo;
import com.bilibili.crm.platform.biz.po.CustomerDepartmentMappingPo;
import com.bilibili.crm.platform.biz.po.CustomerSeaPo;
import com.bilibili.crm.platform.biz.repo.AccCompanyGroupRepo;
import com.bilibili.crm.platform.biz.repo.CustomerDepartmentMappingRepo;
import com.bilibili.crm.platform.biz.service.BizIndustryRepo;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.bilibili.crm.platform.common.customer.CustomerCategoryType;
import com.google.common.collect.Lists;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.Collection;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @date 2023/3/30 15:12
 */
@Slf4j
@Component
public class CustomerSeaConvertor {

    @Resource
    private ICustomerQueryService customerQueryService;

    @Resource
    private AccCompanyGroupRepo accCompanyGroupRepo;

    @Resource
    private BizIndustryRepo bizIndustryRepo;

    @Resource
    private CustomerDepartmentMappingRepo customerDepartmentMappingRepo;

    @Resource
    private ICrmDepartmentService crmDepartmentService;

    @Resource
    private UnitedIndustryService unitedIndustryService;


    public List<CustomerSeaListDto> pos2Dtos(List<CustomerSeaPo> pos) {
        if (CollectionUtils.isEmpty(pos)) {
            return Lists.newArrayList();
        }
        List<Integer> customerIds = CrmUtils.convertDistinct(pos, CustomerSeaPo::getCustomerId);

        if (CollectionUtils.isEmpty(customerIds)) {
            return Lists.newArrayList();
        }
        // 客户信息
        List<CustomerBaseDto> customers = customerQueryService.getCustomerBaseDtosByIds(customerIds);
        Map<Integer, CustomerBaseDto> customerMap = CrmUtils.toMapIdentity(customers, CustomerBaseDto::getId);

        // 业务行业信息
        List<Integer> bizIndustryFirstIds = CrmUtils.convertDistinct(customers, CustomerBaseDto::getBizIndustryCategoryFirstId);
        List<Integer> bizIndustrySecondIds = CrmUtils.convertDistinct(customers, CustomerBaseDto::getBizIndustryCategorySecondId);
        bizIndustryFirstIds.addAll(bizIndustrySecondIds);
        Map<Integer, CategoryDto> categoryMap = bizIndustryRepo.getCategoryDtoInIds(bizIndustryFirstIds);

        // 行业信息
        List<Integer> unitedFirstIndustryIds = CrmUtils.convertDistinct(customers, CustomerBaseDto::getUnitedFirstIndustryId);
        List<Integer> unitedSecondIndustryIds = CrmUtils.convertDistinct(customers, CustomerBaseDto::getUnitedSecondIndustryId);
        List<Integer> unitedThirdIndustryIds = CrmUtils.convertDistinct(customers, CustomerBaseDto::getUnitedThirdIndustryId);
        List<Integer> unitedIndustryIds = Stream.of(unitedFirstIndustryIds, unitedSecondIndustryIds, unitedThirdIndustryIds).flatMap(Collection::stream).distinct().collect(Collectors.toList());
        Map<Integer, UnitedIndustryDto> unitedIndustryDtoMap = unitedIndustryService.batchQueryIndustryByIds(unitedIndustryIds);

        // 所属集团
        List<Integer> groupIds = CrmUtils.convertDistinct(customers, CustomerBaseDto::getGroupId);
        Map<Integer, AccCompanyGroupPo> groupMap = accCompanyGroupRepo.queryGroupMapByIds(groupIds);

        // 所属部门
        List<CustomerDepartmentMappingPo> departmentMappings = customerDepartmentMappingRepo.queryCustomerDepartmentListByCustomerIds(customerIds);
        Map<Integer, List<Integer>> customerId2DepartIdsMap = CrmUtils.groupingByThen(departmentMappings, CustomerDepartmentMappingPo::getCustomerId, CustomerDepartmentMappingPo::getDepartmentId);
        List<Integer> departmentIds = CrmUtils.convertDistinct(departmentMappings, CustomerDepartmentMappingPo::getDepartmentId);
        Map<Integer, CrmDepartmentDto> departmentMap = crmDepartmentService.queryDepartmentMapByIds(departmentIds);

        return CrmUtils.convert(pos, po -> po2Dto(po, customerMap, unitedIndustryDtoMap, categoryMap, groupMap, customerId2DepartIdsMap, departmentMap));
    }

    public CustomerSeaListDto po2Dto(CustomerSeaPo po,
                                     Map<Integer, CustomerBaseDto> customerMap,
                                     Map<Integer, UnitedIndustryDto> unitedIndustryDtoMap,
                                     Map<Integer, CategoryDto> categoryMap,
                                     Map<Integer, AccCompanyGroupPo> groupMap,
                                     Map<Integer, List<Integer>> customerId2DepartIdsMap,
                                     Map<Integer, CrmDepartmentDto> departmentMap
    ) {
        Integer customerId = po.getCustomerId();
        CustomerBaseDto customer = customerMap.getOrDefault(customerId,
                CustomerBaseDto.builder().groupId(0).bizIndustryCategoryFirstId(0).bizIndustryCategorySecondId(0)
                        .unitedFirstIndustryId(0).unitedSecondIndustryId(0).unitedThirdIndustryId(0).build());
        CategoryDto categoryFirst = categoryMap.getOrDefault(customer.getBizIndustryCategoryFirstId(), CategoryDto.builder().build());
        CategoryDto categorySecond = categoryMap.getOrDefault(customer.getBizIndustryCategorySecondId(), CategoryDto.builder().build());
        UnitedIndustryDto unitedFirstIndustry = unitedIndustryDtoMap.getOrDefault(customer.getUnitedFirstIndustryId(), UnitedIndustryDto.builder().build());
        UnitedIndustryDto unitedSecondIndustry = unitedIndustryDtoMap.getOrDefault(customer.getUnitedSecondIndustryId(), UnitedIndustryDto.builder().build());
        UnitedIndustryDto unitedThirdIndustry = unitedIndustryDtoMap.getOrDefault(customer.getUnitedThirdIndustryId(), UnitedIndustryDto.builder().build());

        AccCompanyGroupPo group = groupMap.getOrDefault(customer.getGroupId(), AccCompanyGroupPo.builder().build());
        List<Integer> departmentIds = customerId2DepartIdsMap.getOrDefault(customerId, Lists.newArrayList());
        List<CrmDepartmentDto> departments = CrmUtils.convertDistinct(departmentIds, id -> departmentMap.getOrDefault(id, CrmDepartmentDto.builder().build()));
        return CustomerSeaListDto.builder()
                .id(po.getId())
                .customerId(po.getCustomerId())
                .customerName(customer.getUsername())
                .customerNickname(customer.getNickName())
                .isAgent(po.getIsAgent())
                .isInner(IsInnerEnum.getByCode(po.getIsInner()))
                .businessIndustryFirstId(categoryFirst.getId())
                .businessIndustryFirstName(categoryFirst.getName())
                .businessIndustrySecondId(categorySecond.getId())
                .businessIndustrySecondName(categorySecond.getName())
                .belongGroupId(group.getId())
                .belongGroup(group.getName())
                .belongDepartments(departments)
                .customerCategoryType(CustomerCategoryType.getByCode(po.getCustomerCategory()))
                .enterSeaTime(po.getInSeaTime())
                .creator(customer.getCreator())
                .createTime(customer.getCtime())
                .unitedFirstIndustryId(unitedFirstIndustry.getId())
                .unitedSecondIndustryId(unitedSecondIndustry.getId())
                .unitedThirdIndustryId(unitedThirdIndustry.getId())
                .unitedFirstIndustryName(unitedFirstIndustry.getName())
                .unitedSecondIndustryName(unitedSecondIndustry.getName())
                .unitedThirdIndustryName(unitedThirdIndustry.getName())
                .build();
    }
}
