package com.bilibili.crm.platform.biz.service.policy.component.notice;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.policy.dto.FlowNoticeContentInfo;
import com.bilibili.crm.platform.api.policy.dto.NodeOperateNoticeInfo;
import com.bilibili.crm.platform.api.policy.enums.FlowMsgTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowUserTypeEnum;
import com.bilibili.crm.platform.biz.po.AccAccountPo;
import com.bilibili.crm.platform.biz.po.CrmContractPolicyInfoPo;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import java.util.Arrays;
import java.util.List;

/**
 * @author: brady
 * @time: 2025/1/23 11:22
 */
@Slf4j
@Component(value = "OperationReviewContentProcessor")
public class OperationReviewContentProcessor extends AbstractContentProcessor implements IFlowableNoticeContentProcessor {
    @Override
    public FlowNoticeContentInfo queryAndFormatMsgContent(NodeOperateNoticeInfo nodeOperateNoticeInfo) {

        log.info("OperationReviewContentProcessor{}", JSON.toJSONString(nodeOperateNoticeInfo));

        FlowNoticeContentInfo flowNoticeContentInfo =
                FlowNoticeContentInfo.builder().isNeedNoticeMsg(true).isNeedTodoMsg(true).build();

        CrmContractPolicyInfoPo contractPolicyInfoPo = crmContractPolicyInfoRepo.queryByProcessInstanceId(nodeOperateNoticeInfo.getProcessInstanceId());
        if (contractPolicyInfoPo == null) {
            throw new ServiceRuntimeException("政策不存在！");
        }
        String ipInvestProject = contractPolicyInfoPo.getIpInvestProject();

        AccAccountPo accAccountPo = accAccountRepo.queryAccountById(contractPolicyInfoPo.getAccountId());
        String customerName = "";
        if (accAccountPo != null) {
            CustomerPo customerPo = customerRepo.queryNotDeletedCustomerById(accAccountPo.getCustomerId());
            customerName = customerPo != null ? customerPo.getUsername() : "";
        }

        String operatorName = nodeOperateNoticeInfo.getOperator();
        String operateTime = Utils.getTimestamp2String(Utils.getNow(), CrmUtils.YYYYMMDDHHMMSS);
        String creator = contractPolicyInfoPo.getCreator();


        String formatNoticeContent = String.format("请知晓：%s客户-单笔合同政策审批（提交人：%s，提交时间：%s，IP招商合作项目：%s）%s", customerName, operatorName,
                operateTime, ipInvestProject, getClickLookHyperlink(nodeOperateNoticeInfo.getFlowTypeEnum(), nodeOperateNoticeInfo.getProcessInstanceId(),
                        FlowMsgTypeEnum.NOTICE_MSG,
                        nodeOperateNoticeInfo.getNodeOperateTypeEnum()));
        String formatTodoContent = String.format("请处理：%s客户-单笔合同政策审批（提交人：%s，提交时间：%s，IP招商合作项目：%s）需要您审批。%s", customerName,
                operatorName, operateTime, ipInvestProject,
                getClickLookHyperlink(nodeOperateNoticeInfo.getFlowTypeEnum(), nodeOperateNoticeInfo.getProcessInstanceId(), FlowMsgTypeEnum.TODO_MSG, nodeOperateNoticeInfo.getNodeOperateTypeEnum()));

        flowNoticeContentInfo.setProcessInstanceId(nodeOperateNoticeInfo.getProcessInstanceId());
        flowNoticeContentInfo.setNoticeContent(formatNoticeContent);
        flowNoticeContentInfo.setTodoContent(formatTodoContent);

        log.info("OperationReviewContentProcessor res {}", JSON.toJSONString(flowNoticeContentInfo));

        return flowNoticeContentInfo;
    }

    @Override
    public List<PolicyFlowUserTypeEnum> getToDoUserTypes() {
        return Arrays.asList(PolicyFlowUserTypeEnum.NOW_OPERATOR);
    }

    @Override
    public List<PolicyFlowUserTypeEnum> getNoticeUserTypes() {
        return Arrays.asList(PolicyFlowUserTypeEnum.NOW_OPERATOR,PolicyFlowUserTypeEnum.NOTICE);
    }
}
