package com.bilibili.crm.platform.biz.service.finance.automation.component.flowtype;

import com.bilibili.crm.platform.api.finance.dto.automation.CrmRevenueExpenditureFlowDto;
import com.bilibili.crm.platform.biz.service.finance.automation.component.statemachine.FlowAdditionInfoStateMachine;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-05-08 21:13:06
 * @description:
 **/

@Service("mengPaiReceiveMoneyNotProxyProcessor")
public class MengPaiReceiveMoneyNotProxyProcessor implements INotProxyPayProcessor {

    @Autowired
    private FlowAdditionInfoStateMachine flowAdditionInfoStateMachine;

    public void process(CrmRevenueExpenditureFlowDto receiveMoneyFlowDataDto) {
        // 副表状态：处理完成
        flowAdditionInfoStateMachine.enterProcessCompleted(receiveMoneyFlowDataDto.getId());
    }

}
