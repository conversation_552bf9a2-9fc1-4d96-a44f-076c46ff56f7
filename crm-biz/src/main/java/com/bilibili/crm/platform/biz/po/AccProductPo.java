package com.bilibili.crm.platform.biz.po;

import java.io.Serializable;
import java.sql.Timestamp;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

/**
 * <AUTHOR>
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class AccProductPo implements Serializable {
    /**
     * 产品id
     */
    private Integer id;

    /**
     * 产品线id
     */
    private Integer productLineId;

    /**
     * 产品名称
     */
    private String name;

    /**
     * 产品标签
     */
    private String tag;

    /**
     * 软删除0未删除1 删除
     */
    private Integer isDeleted;

    /**
     * 添加时间
     */
    private Timestamp ctime;

    /**
     * 更新时间
     */
    private Timestamp mtime;

    /**
     * 创建人
     */
    private String createUser;

    /**
     * 更新人
     */
    private String updateUser;

    /**
     * 状态：1-启用 2-禁用
     */
    private Integer status;

    /**
     * 商业行业一级分类id(新版)
     */
    private Integer commerceCategoryFirstId;

    /**
     * 商业行业二级分类id（新版）
     */
    private Integer commerceCategorySecondId;

    private static final long serialVersionUID = 1L;
}