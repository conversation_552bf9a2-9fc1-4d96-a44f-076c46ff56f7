package com.bilibili.crm.platform.biz.crm.account.dao.read;

import com.bilibili.crm.platform.biz.bean.CommonIdAndNameExtPO;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.po.CustomerPoExample;
import com.bilibili.crm.platform.common.DbId;
import org.apache.ibatis.annotations.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ExtCustomerDao {

    /**
     * 获取所有机构客户 不包括代理商 只返回id username customer_status
     */
    List<CustomerPo> getAllOrgCustomerWithNameStatus();

    /**
     * 获取所有外部机构客户 只返回id 包含了代理商
     */
    List<Integer> getAllOrgOutCustomerWithAgent();

    /**
     * 获取所有机构和个人客户 只返回id 包含了代理商
     */
    List<Integer> getAllOrgPersonCustomerWithAgent();

    /**
     * 获取所有机构和个人客户 只返回id 去除代理商
     */
    List<Integer> getAllAdOrgPersonCustomer();

    /**
     * 获取所有外部 启用 代理客户
     */
    List<CustomerPo> getAllEnableOrgOutAgentCustomer();

    /**
     * 获取所有外部 冻结 代理客户
     */
    List<CustomerPo> getAllDisableOrgOutAgentCustomer();

    List<CommonIdAndNameExtPO> getCustomerIdWithNameByIds(@Param("customerIds") List<Integer> customerIds);

    DbId minMaxIdsByExample(CustomerPoExample example);
}
