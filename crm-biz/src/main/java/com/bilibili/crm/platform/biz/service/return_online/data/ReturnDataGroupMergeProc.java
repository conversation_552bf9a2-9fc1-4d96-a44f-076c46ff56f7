package com.bilibili.crm.platform.biz.service.return_online.data;

import com.bilibili.adp.common.util.SnowflakeIdWorker;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 返货数据合并组处理器
 *
 * <AUTHOR>
 * @date 2022/2/25 上午11:56
 */
@Slf4j
@Component
public class ReturnDataGroupMergeProc {

    @Autowired
    private SnowflakeIdWorker snowflakeIdWorker;

    /**
     * 生成合并组
     * 格式: @_12341234
     *
     * @return
     */
    public String generateMergeGroup() {

        return String.format("@_" + snowflakeIdWorker.nextId());
    }

    /**
     * 是否是自动生成的合并组
     * 以 @_ 开头的是自动生成的
     *
     * @param group
     * @return
     */
    public Boolean isAutoGenerateGroup(String group) {
        if (StringUtils.isEmpty(group)) {
            return true;
        }
        return group.startsWith("@_");
    }
}
