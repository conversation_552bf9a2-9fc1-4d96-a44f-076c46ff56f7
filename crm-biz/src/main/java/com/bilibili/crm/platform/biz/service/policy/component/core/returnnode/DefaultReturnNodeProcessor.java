package com.bilibili.crm.platform.biz.service.policy.component.core.returnnode;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.exception.ServiceRuntimeException;
import com.bilibili.crm.platform.api.policy.dto.NodeOperateNoticeInfo;
import com.bilibili.crm.platform.api.policy.dto.PolicyFlowProcessActionDto;
import com.bilibili.crm.platform.api.policy.enums.FlowTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.NodeOperateTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowNodeTypeEnum;
import com.bilibili.crm.platform.api.policy.enums.PolicyFlowStatusEnum;
import com.bilibili.crm.platform.biz.dao.flowable.IHisFlowableActinstDao;
import com.bilibili.crm.platform.biz.dao.flowable.IRunFlowableActinstDao;
import com.bilibili.crm.platform.biz.log.OperationLogProcessor;
import com.bilibili.crm.platform.biz.po.CrmContractPolicyInfoPo;
import com.bilibili.crm.platform.biz.po.CrmReturnApplyPo;
import com.bilibili.crm.platform.biz.repo.policy.CrmContractPolicyInfoRepo;
import com.bilibili.crm.platform.biz.repo.return_online.CrmReturnApplyRepo;
import com.bilibili.crm.platform.biz.service.policy.component.authority.IOperateAuthorityValidator;
import com.bilibili.crm.platform.biz.service.policy.component.core.FlowableBmpnModelProcessor;
import com.bilibili.crm.platform.biz.service.policy.component.core.FlowableCommentProcessor;
import com.bilibili.crm.platform.biz.service.policy.component.core.querier.DefaultFlowQuerier;
import com.bilibili.crm.platform.biz.service.policy.component.core.querier.IRunTaskQuerier;
import com.bilibili.crm.platform.biz.service.policy.component.notice.INoticeable;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.Module;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang.StringUtils;
import org.flowable.bpmn.model.Process;
import org.flowable.bpmn.model.*;
import org.flowable.engine.ManagementService;
import org.flowable.engine.ProcessEngine;
import org.flowable.engine.RuntimeService;
import org.flowable.engine.TaskService;
import org.flowable.engine.impl.persistence.entity.ActivityInstanceEntity;
import org.flowable.engine.runtime.ActivityInstance;
import org.flowable.engine.runtime.Execution;
import org.flowable.engine.runtime.ProcessInstance;
import org.flowable.task.api.Task;
import org.flowable.task.service.impl.persistence.entity.TaskEntity;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;

import java.util.ArrayList;
import java.util.Collection;
import java.util.List;

/**
 * 默认的任务节点退回处理器
 *
 * <AUTHOR>
 * @date 2021/8/21 下午8:24
 */
@Deprecated
@Slf4j
@Component
public class DefaultReturnNodeProcessor {

    @Autowired
    private ProcessEngine processEngine;
    @Autowired
    private RuntimeService runtimeService;
    @Autowired
    protected ManagementService managementService;
    @Autowired
    private FlowableCommentProcessor flowableCommentProcessor;
    @Autowired
    private TaskService taskService;
    @Autowired
    private FlowableBmpnModelProcessor flowableBmpnModelProcessor;
    @Autowired
    protected DefaultFlowQuerier fwFlowProcessor;
    @Autowired
    private INoticeable noticeable;
    @Autowired
    private CrmContractPolicyInfoRepo crmContractPolicyInfoRepo;
    @Autowired
    private CrmReturnApplyRepo crmReturnApplyRepo;
    @Autowired
    private BizDataProc bizDataProc;
    @Autowired
    private OperationLogProcessor operationLogProcessor;
    @Autowired
    private IRunTaskQuerier runTaskQuerier;
    @Autowired
    private IOperateAuthorityValidator operateAuthorityValidator;
    @Autowired
    private IHisFlowableActinstDao hisFlowableActinstDao;
    @Autowired
    private IRunFlowableActinstDao runFlowableActinstDao;

    public String returnNode(PolicyFlowProcessActionDto policyFlowProcessActionDto) {
        log.info("====> returnNode, policyFlowProcessActionDto:{}", JSON.toJSONString(policyFlowProcessActionDto));
        Assert.notNull(policyFlowProcessActionDto.getTargetNodeId(), "target node id not null! ");
        Assert.notNull(policyFlowProcessActionDto.getOperator(), "operator not null! ");
        Assert.isTrue(StringUtils.isNotEmpty(policyFlowProcessActionDto.getProcessInstanceId()), "process instance " +
                "can't be null! ");
        Assert.isTrue(StringUtils.isNotEmpty(policyFlowProcessActionDto.getContent()), "内容不能为空！");

        // 政策信息
        Integer bizId = 0;
        Integer flowStatus = null;
        if (FlowTypeEnum.CONTRACT_FLOW.equals(policyFlowProcessActionDto.getFlowTypeEnum())) {
            CrmContractPolicyInfoPo contractPolicyInfoPo =
                    crmContractPolicyInfoRepo.queryByProcessInstanceId(policyFlowProcessActionDto.getProcessInstanceId());
            if (contractPolicyInfoPo == null) {
                throw new ServiceRuntimeException("政策不存在！");
            }
            bizId = contractPolicyInfoPo.getId();
            flowStatus = contractPolicyInfoPo.getStatus();
        } else {
            CrmReturnApplyPo crmReturnApplyPo = crmReturnApplyRepo.queryByProcessInstanceId(policyFlowProcessActionDto.getProcessInstanceId());
            Assert.notNull(crmReturnApplyPo, "返货申请流程不存在！");
            bizId = crmReturnApplyPo.getId();
            flowStatus = crmReturnApplyPo.getFlowStatus();
        }

        // 运行中的任务实例
        Task runTask = runTaskQuerier.queryCurRunTask(policyFlowProcessActionDto.getProcessInstanceId());
        TaskEntity taskEntity = (TaskEntity) taskService.createTaskQuery().taskId(runTask.getId()).singleResult();
        //1.把当前的节点设置为空
        if (taskEntity == null) {
            throw new ServiceRuntimeException("不存在任务实例,请确认!");
        }

        String operatorName = policyFlowProcessActionDto.getOperator().getOperatorName();
        // 权限检查
        operateAuthorityValidator.checkOperateAuthority(policyFlowProcessActionDto.getFlowTypeEnum(),
                policyFlowProcessActionDto.getNodeOperateTypeEnum(),
                runTask.getProcessInstanceId(), runTask.getId(), flowStatus, operatorName);

        //2.设置审批人
        taskEntity.setAssignee(policyFlowProcessActionDto.getOperator().getOperatorName());
        taskService.saveTask(taskEntity);

        //3.添加驳回意见
        flowableCommentProcessor.addComment(runTask.getId(), policyFlowProcessActionDto.getOperator().getOperatorName(),
                policyFlowProcessActionDto.getProcessInstanceId(), policyFlowProcessActionDto.getNodeOperateTypeEnum(), "退回至:" + policyFlowProcessActionDto.getContent()
                , JSON.toJSONString(policyFlowProcessActionDto));

        //4. todo bug 加签节点的名字都一样，处理提交人节点
        FlowNode distActivity = flowableBmpnModelProcessor.findFlowNodeByActivityId(taskEntity.getProcessDefinitionId(), policyFlowProcessActionDto.getTargetNodeId() + "");
        if (distActivity != null) {
            if ("提交人".equals(distActivity.getName())) {
                ProcessInstance processInstance = runtimeService.createProcessInstanceQuery().processInstanceId(taskEntity.getProcessInstanceId()).singleResult();
                runtimeService.setVariable(policyFlowProcessActionDto.getProcessInstanceId() + "", "initiator",
                        processInstance.getStartUserId());
            }

            // 修改流程状态，取决于退回去的节点
            if (PolicyFlowNodeTypeEnum.APPLY.getName().equals(distActivity.getName())) {
                bizDataProc.updateStatusByProcessInstanceIdAndFlowType(policyFlowProcessActionDto.getFlowTypeEnum().getCode(),
                        taskEntity.getProcessInstanceId(), PolicyFlowStatusEnum.POLICY_APPLY_CREATED.getCode());
            } else if (PolicyFlowNodeTypeEnum.PRE_EXAMINE.getName().equals(distActivity.getName())) {
                bizDataProc.updateStatusByProcessInstanceIdAndFlowType(policyFlowProcessActionDto.getFlowTypeEnum().getCode(), taskEntity.getProcessInstanceId(), PolicyFlowStatusEnum.POLICY_APPLY_CREATED.getCode());
            } else if (PolicyFlowNodeTypeEnum.ADD_SIGN_EXAMINE.getName().equals(distActivity.getName())) {
                bizDataProc.updateStatusByProcessInstanceIdAndFlowType(policyFlowProcessActionDto.getFlowTypeEnum().getCode(), taskEntity.getProcessInstanceId(), PolicyFlowStatusEnum.ADD_SIGN_EXAMINE_ING.getCode());
            } else {
                throw new ServiceRuntimeException("节点类型不合法！name:" + distActivity.getName());
            }
        }
        //5.删除节点
//        this.deleteActivity(policyFlowProcessActionDto.getTargetNodeId() + "", taskEntity.getProcessInstanceId());

        List<String> executionIds = new ArrayList<>();
        //6.判断节点是不是子流程内部的节点
        if (flowableBmpnModelProcessor.checkActivitySubprocessByActivityId(taskEntity.getProcessDefinitionId(),
                policyFlowProcessActionDto.getTargetNodeId() + "")
                && flowableBmpnModelProcessor.checkActivitySubprocessByActivityId(taskEntity.getProcessDefinitionId(),
                taskEntity.getTaskDefinitionKey())) {
            //6.1 子流程内部驳回
            Execution executionTask = runtimeService.createExecutionQuery().executionId(taskEntity.getExecutionId()).singleResult();
            String parentId = executionTask.getParentId();
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(parentId).list();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            this.moveExecutionsToSingleActivityId(executionIds, policyFlowProcessActionDto.getTargetNodeId() + "");
        } else {
            //6.2 普通驳回
            List<Execution> executions = runtimeService.createExecutionQuery().parentId(taskEntity.getProcessInstanceId()).list();
            executions.forEach(execution -> executionIds.add(execution.getId()));
            this.moveExecutionsToSingleActivityId(executionIds, policyFlowProcessActionDto.getTargetNodeId() + "");
        }

        // 通知(知晓消息通知：提交人、所有知会人; 代办消息;  退回节点处理人)
        NodeOperateNoticeInfo nodeOperateNoticeInfo = NodeOperateNoticeInfo.builder().build();
        nodeOperateNoticeInfo.setRelId(bizId);
        nodeOperateNoticeInfo.setNodeOperateTypeEnum(policyFlowProcessActionDto.getNodeOperateTypeEnum());
        nodeOperateNoticeInfo.setProcessInstanceId(policyFlowProcessActionDto.getProcessInstanceId());
        nodeOperateNoticeInfo.setOperateNodeId(runTask.getId());
        nodeOperateNoticeInfo.setTargetNodeId(policyFlowProcessActionDto.getTargetNodeId());
        nodeOperateNoticeInfo.setOperator(policyFlowProcessActionDto.getOperator().getOperatorName());
        noticeable.notice(nodeOperateNoticeInfo);

        // 操作日志
        ModifyType modifyType = NodeOperateTypeEnum.REJECT.equals(policyFlowProcessActionDto.getNodeOperateTypeEnum()) ?
                ModifyType.FLOW_REJECT : ModifyType.FLOW_RETURN;
        operationLogProcessor.addOperationLog(bizId, Module.POLICY_FLOW,
                modifyType, policyFlowProcessActionDto, policyFlowProcessActionDto.getOperator());
        return String.format("退回/驳回成功! 目标节点id:%s, 目标节点名称:%s", runTask.getId(), runTask.getName());
    }

    public String rejectNode(PolicyFlowProcessActionDto policyFlowProcessActionDto) {
        log.info("=====> rejectNode, policyFlowProcessActionDto:{}", JSON.toJSONString(policyFlowProcessActionDto));
        policyFlowProcessActionDto.setNodeOperateTypeEnum(NodeOperateTypeEnum.REJECT);

        // 查询目标节点：提交申请节点
        Activity disActivity = this.findActivityByName(policyFlowProcessActionDto.getProcessInstanceId(),
                PolicyFlowNodeTypeEnum.APPLY.getName());
        policyFlowProcessActionDto.setTargetNodeId(disActivity.getName());
        return returnNode(policyFlowProcessActionDto);
    }

    /**
     * 执行跳转
     */
    protected void moveExecutionsToSingleActivityId(List<String> executionIds, String activityId) {
        log.info("=====> moveExecutionsToSingleActivityId, executionIds:{}, activityId:{}",
                JSON.toJSONString(executionIds), activityId);
        RuntimeService runtimeService = processEngine.getRuntimeService();
        runtimeService.createChangeActivityStateBuilder()
                .moveExecutionsToSingleActivityId(executionIds, activityId)
                .changeState();
    }

    /**
     * 删除跳转的历史节点信息
     *
     * @param disActivityId     跳转的节点id
     * @param processInstanceId 流程实例id
     */
    protected void deleteActivity(String disActivityId, String processInstanceId) {
        String tableName = managementService.getTableName(ActivityInstanceEntity.class);
        String sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and t.ACT_ID_ = #{disActivityId} " +
                " order by t.END_TIME_ ASC";
        List<ActivityInstance> disActivities = runtimeService.createNativeActivityInstanceQuery().sql(sql)
                .parameter("processInstanceId", processInstanceId)
                .parameter("disActivityId", disActivityId).list();
        //删除运行时和历史节点信息
        if (CollectionUtils.isNotEmpty(disActivities)) {
            ActivityInstance activityInstance = disActivities.get(0);
            sql = "select t.* from " + tableName + " t where t.PROC_INST_ID_=#{processInstanceId} and (t.END_TIME_ >= #{endTime} or t.END_TIME_ is null)";
            List<ActivityInstance> datas = runtimeService.createNativeActivityInstanceQuery().sql(sql).parameter("processInstanceId", processInstanceId)
                    .parameter("endTime", activityInstance.getEndTime()).list();
            List<String> runActivityIds = new ArrayList<>();
            if (CollectionUtils.isNotEmpty(datas)) {
                datas.forEach(ai -> runActivityIds.add(ai.getId()));
                runFlowableActinstDao.deleteRunActinstsByIds(runActivityIds);
                hisFlowableActinstDao.deleteHisActinstsByIds(runActivityIds);
            }
        }
    }

    protected Activity findActivityByName(String processDefinitionId, String name) {
        Activity activity = null;
        BpmnModel bpmnModel = flowableBmpnModelProcessor.getBpmnModelByProcessDefId(processDefinitionId);
        Process process = bpmnModel.getMainProcess();
        Collection<FlowElement> list = process.getFlowElements();
        for (FlowElement f : list) {
            if (StringUtils.isNotBlank(name)) {
                if (name.equals(f.getName())) {
                    activity = (Activity) f;
                    break;
                }
            }
        }
        return activity;
    }
}
