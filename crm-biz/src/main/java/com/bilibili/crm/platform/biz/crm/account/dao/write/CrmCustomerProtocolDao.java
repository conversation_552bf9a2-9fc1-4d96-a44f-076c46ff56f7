package com.bilibili.crm.platform.biz.crm.account.dao.write;

import com.bilibili.crm.platform.biz.po.CrmCustomerProtocolPo;
import com.bilibili.crm.platform.biz.po.CrmCustomerProtocolPoExample;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.ibatis.annotations.Param;

public interface CrmCustomerProtocolDao {
    long countByExample(CrmCustomerProtocolPoExample example);

    int deleteByExample(CrmCustomerProtocolPoExample example);

    int deleteByPrimaryKey(Long id);

    int insertUpdate(CrmCustomerProtocolPo record);

    int insertBatch(List<CrmCustomerProtocolPo> records);

    int insertUpdateBatch(List<CrmCustomerProtocolPo> records);

    int insert(CrmCustomerProtocolPo record);

    int insertUpdateSelective(CrmCustomerProtocolPo record);

    int insertSelective(CrmCustomerProtocolPo record);

    List<CrmCustomerProtocolPo> selectByExample(CrmCustomerProtocolPoExample example);

    CrmCustomerProtocolPo selectByPrimaryKey(Long id);

    int updateByExampleSelective(@Param("record") CrmCustomerProtocolPo record, @Param("example") CrmCustomerProtocolPoExample example);

    int updateByExample(@Param("record") CrmCustomerProtocolPo record, @Param("example") CrmCustomerProtocolPoExample example);

    int updateByPrimaryKeySelective(CrmCustomerProtocolPo record);

    int updateByPrimaryKey(CrmCustomerProtocolPo record);
}