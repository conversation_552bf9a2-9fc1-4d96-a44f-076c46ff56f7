package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.dataprepare.service.CrmKpiCkPrepareService;
import com.bilibili.crm.platform.api.contract.service.ContractBillPeriodService;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.service.contract.ContractQueryServiceHelper;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;

@Slf4j
@Component
@JobHandler("BillPeriodNewBuildJob")
public class BillPeriodNewBuildJob extends IJobHandler {

    @Autowired
    private RedisLock redisLock;

    @Resource
    private CrmKpiCkPrepareService crmKpiCkPrepareService;

    @Resource
    private ContractQueryServiceHelper contractQueryServiceHelper;

    @Resource
    private ContractBillPeriodService contractBillPeriodService;


    private static final String redisBillJobKey = "BillPeriodNewBuildJob";

    private static final String dataCode = "BillPeriodNewBuildJob";

    private static final Long BATCH_OPERATE_NUMBER = 500L;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Timestamp timestamp =  Utils.getToday();;
        // 最后解锁下，下面可能发生并发，
        redisLock.getLock(redisBillJobKey + timestamp, 5000);

        try {
            // 校验是否可以执行
            if (!checkJobStatusAndUpdate(timestamp)) {
                return ReturnT.SUCCESS;
            }
            // 查询要处理的合同数
            Long count = contractQueryServiceHelper.queryContractTotalCount();
            if (!Utils.isPositive(count)) {
                log.info("BillPeriodNewBuildJob no contract time={}", timestamp);
                return ReturnT.SUCCESS;
            }

            // 分页进行执行操作
            Long page = (count / BATCH_OPERATE_NUMBER) + 1L;
            contractBillPeriodService.refreshBillPeriodByNewRule(page, BATCH_OPERATE_NUMBER.intValue());
            crmKpiCkPrepareService.insertOrUpdatePrepareDataInfoByStatus(timestamp, dataCode, 1);
            return ReturnT.SUCCESS;
        } catch (Exception e) {
            log.error("BillPeriodNewBuildJob exec fail", e);
            crmKpiCkPrepareService.insertOrUpdatePrepareDataInfoByStatus(timestamp, dataCode, 0);
            return ReturnT.FAIL;
        } finally {
            redisLock.releaseLock(redisBillJobKey + timestamp);
        }
    }


    private Boolean checkJobStatusAndUpdate(Timestamp timestamp) {
        // 判断是否在执行中
        if (crmKpiCkPrepareService.checkPrepareDataInfoByStatus(timestamp, dataCode, 3)) {
            log.info("该日期已经有任务在执行中 {}", timestamp);
            return false;
        }

        // 没有在执行中，修改为执行中
        crmKpiCkPrepareService.insertOrUpdatePrepareDataInfoByStatus(timestamp, dataCode, 3);
        return true;
    }
}
