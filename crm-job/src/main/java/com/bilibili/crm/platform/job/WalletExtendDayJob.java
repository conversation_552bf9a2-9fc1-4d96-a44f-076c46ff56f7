package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.service.consume.AdxConsumeService;
import com.bilibili.crm.platform.biz.service.consume.WalletConsumeService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2021/4/27 1:53 下午
 */
@Component
@JobHandler("WalletExtendDayJob")
public class WalletExtendDayJob extends AbstractBaseJobHandler {
    @Autowired
    private AdxConsumeService adxConsumeService;
    @Autowired
    private WalletConsumeService walletConsumeService;

    @Override
    protected boolean biz(String param) {
        int dayOffset = 2;
        if (StringUtils.isNumeric(param)) {
            dayOffset = Integer.parseInt(param);
        }
        AlarmHelper.log("DayOffset", dayOffset);
        Timestamp begin = Utils.getSomeDayBeforeToday(dayOffset);
        Timestamp end = Utils.getToday();
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            //adx数据刷入
            adxConsumeService.flushAll2ES(begin, end);
            //wallet数据刷入
            walletConsumeService.flushConsume2ESFromNew(begin, end);
            // 效果实结数据刷入
            walletConsumeService.flushClose2ES(begin, end);
            // 合伙人数据刷入
            walletConsumeService.flushCluePassES(begin, end);
        });
        return true;
    }

}
