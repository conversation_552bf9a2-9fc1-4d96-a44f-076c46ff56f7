package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.writer.WxSellGoodGmvWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-03-05 15:59:25
 * @description:
 **/

@Component
@JobHandler("WxSellGoodGmvJob")
@Slf4j
public class WxSellGoodGmvJob extends IJobHandler {

    @Resource
    private WxSellGoodGmvWriter wxSellGoodGmvWriter;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    @Autowired
    private WxAppDataCheckService dataCheckService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Timestamp begin = new Timestamp(System.currentTimeMillis());
        Timestamp end = new Timestamp(System.currentTimeMillis());
        if (StringUtils.hasText(s) && s.contains("~")) {
            String[] strings = s.split("~");
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }
        List<Timestamp> dayList = Utils.getEachDays(begin, end);

        if (!StringUtils.hasText(s)) {
            //检查当天状态位
            Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
            if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SELL_GOODS_GMV)) {
                Cat.logEvent("WxAppCacheSellGoodGmvJob_DATA_HAD_DOWN", day.toString());
                return ReturnT.SUCCESS;
            }
        }
        dayList.forEach(day -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                try {
                    //检查 淘宝 ck数据是否ready
                    if (!dataCheckService.mobileDataCheckByState(incomeTimeUtil.getYesterdayBegin(day), dataCheckService.generateSellGoodGmvTbCkKey(incomeTimeUtil.getYesterdayBegin(day)))) {
                        return;
                    }
                    //检查 京东 ck数据是否ready
                    if (!dataCheckService.mobileDataCheckByState(incomeTimeUtil.getYesterdayBegin(day), dataCheckService.generateSellGoodGmvJdCkKey(incomeTimeUtil.getYesterdayBegin(day)))) {
                        return;
                    }
                    log.info("WxAppCacheSellGoodGmvJobStart:{}", incomeTimeUtil.getYesterdayBegin(day));
                    wxSellGoodGmvWriter.buildGmvOverView(day);
                    wxSellGoodGmvWriter.buildGoodCategoryOverViewAndContrast(day);
                    wxSellGoodGmvWriter.buildTopShop(day);
                    wxSellGoodGmvWriter.buildTopGood(day);
                    log.info("WxAppCacheSellGoodGmvJobEnd:{}", incomeTimeUtil.getYesterdayBegin(day));
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(day), WxModuleType.SELL_GOODS_GMV);
                } catch (Exception e) {
                    Cat.logEvent("Exception_WxSellGoodGmvJob", e.getMessage());
                    throw new RuntimeException(e);
                }
            });
        });

        return ReturnT.SUCCESS;
    }
}