package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityTeamItemDTO;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.app.biz.service.writer.SellGoodWriteHelper;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.sale.WxAppSaleCacheWriterV2;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income_sale.manager.WxAppSaleHelper;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@JobHandler("WxAppSaleCacheV2Job")
@Slf4j
public class WxAppSaleCacheV2Job extends IJobHandler {
    @Autowired
    private WxAppSaleCacheWriterV2 wxAppSaleCacheWriterV2;
    @Autowired
    private WxAppSaleHelper wxAppSaleHelper;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private ISaleGroupService iSaleGroupService;

    @Resource
    private SellGoodWriteHelper sellGoodWriteHelper;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (!StringUtils.hasText(args)) {
                //检查当天状态位
                Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
                if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SALE_DASHBOARD)) {
                    Cat.logEvent("WxAppCacheSaleDashboard_DATA_HAD_DOWN", day.toString());
                    return ReturnT.SUCCESS;
                }
            }

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            if (StringUtils.hasText(args) && args.contains("-")) {
                String[] strings = args.split("-");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
                wxAppSaleCacheWriterV2.testForSale(begin, end, strings[2], Integer.valueOf(strings[3]));
                return ReturnT.SUCCESS;
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);
            Collections.reverse(dayList);
            Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
            // 对历史季度最后一天回刷
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                dayList.add(timestamp);
            }
            Map<Integer, SaleGroupDto> groupDtoMap = iSaleGroupService.getAll().stream().collect(Collectors.toMap(SaleGroupDto::getId, Function.identity()));
            List<Integer> sellGoodAgentIds = sellGoodWriteHelper.querySellGoodAgent();

            dayList.forEach(d -> {
                Integer isForQuarterEnd = IsValid.FALSE.getCode();
                if ((Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                    if (d.compareTo(timestamp) == 0) {
                        isForQuarterEnd = IsValid.TRUE.getCode();
                    }
                }
                try {
                    List<BsiOpportunityTeamItemDTO> teamItemDTOS = wxAppSaleCacheWriterV2.buildAllBsiFromCk(d);
                    Integer finalIsForQuarterEnd = isForQuarterEnd;
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                        //缓存sale_manager
                        wxAppSaleCacheWriterV2.cacheRequest(d, WxAppSaleHelper.SALE_MANAGER, teamItemDTOS, groupDtoMap, sellGoodAgentIds, finalIsForQuarterEnd);

                        //销售来源- 品效销售
                        List<String> userBySaleType = wxAppSaleHelper.getNormalSaleEmail();
                        if (CollectionUtils.isEmpty(userBySaleType)) {
                            return;
                        }
                        userBySaleType = userBySaleType.stream().filter(a -> !a.equals("/")).collect(Collectors.toList());
                        AtomicBoolean success = new AtomicBoolean(true);
                        //缓存所有销售
                        userBySaleType.forEach(user -> {
                            Boolean isSuccess = true;
                            try {
                                wxAppSaleCacheWriterV2.cacheRequest(d, user, teamItemDTOS, groupDtoMap, sellGoodAgentIds, finalIsForQuarterEnd);
                                log.info("WxAppSaleCacheJob username {} cache had finished, day:{} ", user, d);
                            } catch (Exception e) {
                                log.error("WxAppSaleCacheJob_FAILED:{}", user, e);
                                success.set(false);
                            } finally {
                                // 重试一次
                                if (!isSuccess) {
                                    wxAppSaleCacheWriterV2.cacheRequest(d, user, teamItemDTOS, groupDtoMap, sellGoodAgentIds, finalIsForQuarterEnd);
                                }
                            }
                        });

                        wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(d), WxModuleType.SALE_DASHBOARD);
                        if (!success.get()) {
                            throw new RuntimeException("fail ");
                        }
                    });
                    Cat.logEvent("WxAppSaleCacheJob_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    log.error("WxAppSaleCacheJob_FAILED", e);
                    Cat.logEvent("WxAppSaleCacheJob_FAILED", d.toString() + ":failed");
                    log.error("WxAppSaleCacheJob_FAILED", e);
                    throw new RuntimeException(e);
                }
            });

        } catch (Exception e) {
            log.error("WxAppSaleCacheJob_FAILED", e);
            Cat.logEvent("Exception_WxAppSaleCacheJob", e.getMessage());
            throw e;
        }
        log.info("WxAppSaleCacheJob success args {}", args);

        return ReturnT.SUCCESS;
    }
}
