package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.fly.IFlyReportWriteService;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;
import org.springframework.util.Assert;

import java.sql.Timestamp;
import java.util.Objects;

/**
 * @author: brady
 * @time: 2021/8/9 3:27 下午
 */
@Component
@JobHandler("FlyExtendJob")
@Slf4j
public class FlyExtendJob extends IJobHandler {
    @Autowired
    private IFlyReportWriteService flyReportWriteService;
    @Autowired
    private CrmCacheManager crmCacheManager;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        //每天12点执行 依赖朝朝数据
        try {
            Timestamp begin = Utils.getYesteday();
            String logStr = CrmUtils.formatDate(begin, CrmUtils.YYYYMMDD);
            Timestamp end = begin;
            if (StringUtils.hasText(args) && args.contains("~")) {
                //20200714~20200714
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }
            Object cache1 = crmCacheManager.getValue("FlyExtendJob_writeFlyOrder2Es_Flag" + logStr);
            Object cache2 = crmCacheManager.getValue("FlyExtendJob_batchFlushWithholdMoney_Flag" + logStr);
            if (StringUtils.isEmpty(args)) {
                //手动强制执行不走这里面
                if (Objects.nonNull(cache1) && Objects.nonNull(cache2)) {
                    log.info("FlyExtendJob_Have_Cache_Success");
                    return ReturnT.SUCCESS;
                }
            }
            Timestamp finalBegin = begin;
            Timestamp finalEnd = end;
            Assert.isTrue(finalBegin.after(new Timestamp(1717171199000L)), "20240601之前不准重刷有问题找产品 拾玖 谨言 研发 阿钢 朝朝 辰星");
            //刷入订单数据
            flyReportWriteService.writeFlyOrder2Es(finalBegin, finalEnd);
            //刷入托管资金池数据
            flyReportWriteService.batchFlushWithholdMoney(finalBegin, finalEnd);
        } catch (Exception e) {
            log.error("FlyExtendJob_error=", e);
            throw e;
        }
        log.info("FlyExtendJob success args {}", args);
        return ReturnT.SUCCESS;
    }

}
