package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.service.consume.IncomeToDBService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/5/29
 **/
@Component
@JobHandler("IncomeToDBJob")
@Slf4j
public class IncomeToDBJob extends IJobHandler {

    @Autowired
    private IncomeToDBService incomeToDBService;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;


    @Override
    public ReturnT<String> execute(String args) throws Exception {


        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {

            Timestamp yesterday = Utils.getYesteday();
            incomeToDBService.buildRTB(Utils.getBeginOfDay(yesterday));
            incomeToDBService.buildBrand(Utils.getBeginOfDay(yesterday));
            //
            Timestamp quarterFirstDate = incomeTimeUtil.getQuarterFirstDate(yesterday);
            List<Timestamp> eachDays = Utils.getEachDays(quarterFirstDate, yesterday);
            eachDays.forEach(day -> {
                incomeToDBService.buildUp(day);
            });

        });

        return ReturnT.SUCCESS;
    }
}
