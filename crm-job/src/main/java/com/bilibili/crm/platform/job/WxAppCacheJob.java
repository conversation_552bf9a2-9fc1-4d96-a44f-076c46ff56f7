package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.app.cache.WxAppCacheWriter;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.dianping.cat.Cat;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/4/15
 * 企业微信大盘日报缓存job
 **/
@Component
@JobHandler("WxAppCacheJob")
@Slf4j
public class WxAppCacheJob extends AbstractBaseJobHandler {

    @Autowired
    private WxAppCacheWriter wxAppCacheWriter;

    @Autowired
    private CrmCacheManager crmCacheManager;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    /**
     * @param args exp：args:20200714
     * @throws Exception
     */
    @Override
    protected boolean biz(String args) {

        try {
            Timestamp date = Utils.getToday();
            if (StringUtils.hasText(args)) {
                date = StringDateParser.stringToTimestamp(args);
            }
            Timestamp finalDate = date;

            AlarmHelper.log("RunWxAppCacheJob", CrmUtils.formatDate(finalDate, "yyyy-MM-dd"));

            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                // 缓存计算数据
                wxAppCacheWriter.cacheCalculateData(finalDate);
                // 缓存请求
                wxAppCacheWriter.cacheRequest(finalDate);
                Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
                // 对历史季度最后一天回刷 只回刷已完成
                if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                    wxAppCacheWriter.cacheHasDoneRequest(timestamp, IsValid.TRUE.getCode());
                }
                log.info("WxAppCacheJob finished date: {}", finalDate.toString());
                crmCacheManager.setValueWithTime("WxAppCacheJob_" + CrmUtils.formatDate(finalDate), 1, TimeUnit.DAYS, 1);
            });
            Cat.logEvent("WxAppCacheJob_SUCCESS", date.toString() + ":normal");
        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppCacheJob", e.getMessage());
            AlarmHelper.alarmEx("DailyReportPartFailed", e, this.getJobName());
            throw e;
        }

        return true;
    }

}
