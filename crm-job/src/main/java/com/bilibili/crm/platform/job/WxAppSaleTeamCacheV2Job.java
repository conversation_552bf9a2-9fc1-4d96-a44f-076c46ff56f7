package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiEstimateDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityTeamItemDTO;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.app.biz.service.writer.SellGoodWriteHelper;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.sale.WxAppSaleCacheWriterV2;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income_sale.manager.WxAppSaleHelper;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicBoolean;
import java.util.function.Function;
import java.util.stream.Collectors;


@Component
@JobHandler("WxAppSaleTeamCacheV2Job")
@Slf4j
public class WxAppSaleTeamCacheV2Job extends IJobHandler {
    @Autowired
    private WxAppSaleCacheWriterV2 wxAppSaleCacheWriterV2;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private SellGoodWriteHelper sellGoodWriteHelper;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            if (StringUtils.hasText(args) && args.contains("-")) {
                String[] strings = args.split("-");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
                wxAppSaleCacheWriterV2.testForTeam(begin, end, Integer.valueOf(strings[2]), Integer.valueOf(strings[3]));
                return ReturnT.SUCCESS;
            }
            List<Timestamp> dayList = Utils.getEachDays(begin, end);
            Collections.reverse(dayList);
            Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
            // 对历史季度最后一天回刷
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                dayList.add(timestamp);
            }
            dayList.forEach(d -> {
                Integer isForQuarterEnd = IsValid.FALSE.getCode();
                if ((Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                    if(d.compareTo(timestamp) == 0){
                        isForQuarterEnd = IsValid.TRUE.getCode();
                    }
                }
                try {
                    Integer finalIsForQuarterEnd = isForQuarterEnd;
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {

                        List<Integer> noLeaderGroupIds = wxAppSaleCacheWriterV2.getNoLeaderGroupIds();
                        if(CollectionUtils.isEmpty(noLeaderGroupIds)){
                            return;
                        }
                        List<BsiOpportunityTeamItemDTO> teamItemDTOS = wxAppSaleCacheWriterV2.buildAllBsiFromCk(d);
                        List<Integer> sellGoodAgentIds = sellGoodWriteHelper.querySellGoodAgent();

                        AtomicBoolean success = new AtomicBoolean(true);
                        //缓存所有无组长团队 以及多个组长团队
                        noLeaderGroupIds.forEach(groupId -> {
                            try {
                                wxAppSaleCacheWriterV2.cacheTeamRequest(d, groupId, teamItemDTOS, sellGoodAgentIds, finalIsForQuarterEnd);
                                log.info("WxAppSaleCacheJob username {} cache had finished ", groupId);
                            } catch (Exception e) {
                                log.error("error " + groupId, e);
                                success.set(false);
                            }
                        });

                        wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(d), WxModuleType.SALE_DASHBOARD_TEAM);
                        if (!success.get()) {
                            throw new RuntimeException("fail ");
                        }
                    });
                    Cat.logEvent("WxAppSaleTeamCacheV2Job_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    log.error("WxAppSaleCacheJob_FAILED", e);
                    Cat.logEvent("WxAppSaleTeamCacheV2Job_FAILED", d.toString() + ":failed");
                    throw new RuntimeException(e);
                }
            });

        } catch (Exception e) {
            log.error("Exception_WxAppSaleTeamCacheV2Job", e);
            Cat.logEvent("Exception_WxAppSaleTeamCacheV2Job", e.getMessage());
            throw e;
        }
        log.info("WxAppSaleTeamCacheV2Job success args {}", args);

        return ReturnT.SUCCESS;
    }
}
