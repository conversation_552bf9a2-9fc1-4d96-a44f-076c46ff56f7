package com.bilibili.crm.platform.job.download;

import com.bilibili.crm.platform.api.effect.service.IRtbConsumeService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-03-12 14:35:18
 * @description:
 **/

@Component
@JobHandler("downLoadCenterCheckJob")
@Slf4j
public class DownLoadCenterCheckJob extends IJobHandler {

    @Resource
    private IRtbConsumeService rtbConsumeService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            rtbConsumeService.changeFailStatus();

        });
        return ReturnT.SUCCESS;
    }
}
