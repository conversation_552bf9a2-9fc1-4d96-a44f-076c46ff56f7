package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.statusmachine.order.OrderStatusMachine;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/11/8
 * 根据起止时间同步订单状态
 **/
@Component
@JobHandler("OrderSyncStatusInTimeJob")
@Slf4j
public class OrderSyncStatusInTimeJob extends IJobHandler {

    @Autowired
    private OrderStatusMachine statusMachine;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        statusMachine.syncStatusInTime();
        return ReturnT.SUCCESS;
    }
}
