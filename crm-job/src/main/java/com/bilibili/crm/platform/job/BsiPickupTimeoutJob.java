package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.bsi_pickup.IBsiPickupService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/6/15 18:05
 * 花火商机判定超时job
 */
@Component
@JobHandler("BsiPickupTimeoutJob")
@Slf4j
public class BsiPickupTimeoutJob extends IJobHandler {

    @Autowired
    private IBsiPickupService bsiPickupService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        bsiPickupService.handleTimeout();
        return ReturnT.SUCCESS;
    }
}
