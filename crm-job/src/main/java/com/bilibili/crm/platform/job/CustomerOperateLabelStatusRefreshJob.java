package com.bilibili.crm.platform.job;

import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.biz.customer_operate_label.bo.QueryCustomerOperateLabelParam;
import com.bilibili.crm.biz.customer_operate_label.dto.CustomerOperateLabelDto;
import com.bilibili.crm.biz.customer_operate_label.enums.CustomerOperateLabelStatusEnum;
import com.bilibili.crm.biz.customer_operate_label.service.CustomerOperateLabelService;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/8/23
 */
@Component
@JobHandler("CustomerOperateLabelStatusRefreshJob")
@Slf4j
public class CustomerOperateLabelStatusRefreshJob extends IJobHandler {

    private static final int PAGE_SIZE = 1000;

    @Resource
    private CustomerOperateLabelService customerOperateLabelService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("CustomerOperateLabelStatusRefreshJob start");
        StopWatch started = StopWatch.createStarted();
        handlePending();
        handleEffecting();
        log.info("CustomerOperateLabelStatusRefreshJob end, cost = {}", started.getTime(TimeUnit.MILLISECONDS));
        return ReturnT.SUCCESS;
    }

    private void handlePending() {
        QueryCustomerOperateLabelParam param = new QueryCustomerOperateLabelParam();
        param.setStatus(Lists.newArrayList(CustomerOperateLabelStatusEnum.EFFECT_PENDING.getCode()));
        param.setLeServiceBeginTime(LocalDateTime.now());
        pageHandle(param, CustomerOperateLabelStatusEnum.EFFECT_ING);
    }

    private void handleEffecting() {
        QueryCustomerOperateLabelParam param = new QueryCustomerOperateLabelParam();
        param.setStatus(Lists.newArrayList(CustomerOperateLabelStatusEnum.EFFECT_ING.getCode()));
        param.setLtServiceEndTime(LocalDateTime.now());
        pageHandle(param, CustomerOperateLabelStatusEnum.EXPIRED);
    }

    private void pageHandle(QueryCustomerOperateLabelParam param, CustomerOperateLabelStatusEnum status) {
        param.setPageSize(PAGE_SIZE);
        int page = 1;
        int pageCount;
        do {
            param.setPage(page);
            Pagination<List<CustomerOperateLabelDto>> pendingInfos = customerOperateLabelService.pageQueryCustomerOperateLabels(param);
            List<CustomerOperateLabelDto> data = pendingInfos.getData();
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            List<Long> ids = data.stream().map(CustomerOperateLabelDto::getId).collect(Collectors.toList());
            ids.forEach(id -> customerOperateLabelService.updateStatus(id, status));
            pageCount = pendingInfos.getPage();
            page++;
        } while (pageCount >= page);
    }
}
