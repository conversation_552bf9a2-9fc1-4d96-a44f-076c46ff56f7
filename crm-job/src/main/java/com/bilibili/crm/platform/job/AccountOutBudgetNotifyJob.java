package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.wallet.service.IBudgetNotifyService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2021/4/8 9:21 下午
 */
@Component
@JobHandler("AccountOutBudgetNotifyJob")
@Slf4j
public class AccountOutBudgetNotifyJob extends IJobHandler {
    @Autowired
    private IBudgetNotifyService budgetNotifyService;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        //前日 (T,T+2) 对T+2数据进行返货
        Timestamp date = incomeTimeUtil.getBeforeYesterday(Utils.getToday());
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> budgetNotifyService.sendEmail(date));
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> budgetNotifyService.robotNotify(date));
        return ReturnT.SUCCESS;
    }
}