package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.wallet.service.IBudgetOutExtraService;
import com.bilibili.crm.platform.api.wallet.service.IUnitInvalidConsumeService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @description:
 * @author: mort
 * @date
 */
@Component
@JobHandler("unitInvalidConsumeJob")
@Slf4j
public class UnitInvalidConsumeJob extends IJobHandler {
    @Autowired
    private IUnitInvalidConsumeService unitInvalidConsumeService;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Timestamp date = incomeTimeUtil.getYesterdayBegin(Utils.getToday());

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> unitInvalidConsumeService.compensate(date, null));
        return ReturnT.SUCCESS;
    }
}
