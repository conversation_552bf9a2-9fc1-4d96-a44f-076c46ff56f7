package com.bilibili.crm.platform.job.customer;

import com.bilibili.crm.platform.api.customer.service.ICustomerActionService;
import com.bilibili.crm.platform.api.enums.CustomerSeaLabelEnum;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaService;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerDao;
import com.bilibili.crm.platform.biz.crm.account.dao.read.ExtCustomerDao;
import com.bilibili.crm.platform.biz.po.CustomerPo;
import com.bilibili.crm.platform.biz.po.CustomerPoExample;
import com.bilibili.crm.platform.common.DbId;
import com.bilibili.crm.platform.core.IDPaginationTaskProcessor;
import com.bilibili.crm.platform.core.LoopContext;
import com.bilibili.crm.platform.core.Parameter;
import com.bilibili.crm.platform.core.TaskProcessorConf;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.compress.utils.Lists;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 客户表的公私海标签T+1更新定时任务
 *
 * <AUTHOR>
 * date 2023/10/31 14:39.
 * Contact: <EMAIL>.
 */
@Slf4j
@Component
@JobHandler("customerSeaLabelChangeJob")
public class CustomerSeaLabelChangeJob extends IJobHandler {
    @Resource
    private ICustomerActionService customerActionService;
    @Autowired
    private ExtCustomerDao extCustomerDao;
    @Resource
    private ISaleSeaService saleSeaService;
    @Autowired
    private CustomerDao customerDao;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Parameter<Param> parameter = Parameter.getParameter(param, Param.class);
        TaskProcessorConf conf = TaskProcessorConf.newBuilder()
                .withIsSkipException(false)
                .withUseThreadPool(false)
                .withTaskName("customerSeaLabelChangeJob")
                .build();
        ReturnT<String> result = new IDPaginationTaskProcessor<Parameter<Param>, CustomerPo>(conf) {

            @Override
            public DbId getMinAndMaxId(Parameter<Param> parameter) {
                CustomerPoExample example = new CustomerPoExample();
                CustomerPoExample.Criteria criteria = example.createCriteria()
                        .andIsDeletedEqualTo(0)
                        .andIsInnerEqualTo(0)
                        .andCustomerCategoryEqualTo(1);
                if (Objects.nonNull(parameter.getParamObj()) && CollectionUtils.isNotEmpty(parameter.getParamObj().getCustomerIds())) {
                    criteria.andIdIn(parameter.getParamObj().getCustomerIds());
                }
                return extCustomerDao.minMaxIdsByExample(example);
            }

            @Override
            public List<CustomerPo> fetchData(LoopContext loopContext, long startId, long endId) {
                CustomerPoExample example = new CustomerPoExample();
                CustomerPoExample.Criteria criteria = example.createCriteria()
                        .andIsDeletedEqualTo(0)
                        .andIsInnerEqualTo(0)
                        .andCustomerCategoryEqualTo(1)
                        .andIdGreaterThanOrEqualTo((int) startId)
                        .andIdLessThan((int) endId);
                if (Objects.nonNull(parameter.getParamObj()) && CollectionUtils.isNotEmpty(parameter.getParamObj().getCustomerIds())) {
                    criteria.andIdIn(parameter.getParamObj().getCustomerIds());
                }
                List<CustomerPo> customerPos = customerDao.selectByExample(example);
                if (CollectionUtils.isEmpty(customerPos)) {
                    return Lists.newArrayList();
                }

                List<Integer> customerIds = customerPos.stream().map(CustomerPo::getId).collect(Collectors.toList());
                changeCustomerSeaLabel(customerIds);

                return customerPos;
            }

            @Override
            public void processData(LoopContext loopContext, CustomerPo customerPo) throws Exception {
                // ignore
            }
        }.process(parameter);

        return result;
    }

    public void changeCustomerSeaLabel(List<Integer> customerIds) {
        Map<Integer, CustomerSeaLabelEnum> isValidMap = saleSeaService.judgeCustomerSeaLabelByCustomerIds(customerIds);
        List<Integer> havaSaleCustomerIds = isValidMap.entrySet()
                .stream()
                .filter(entry -> CustomerSeaLabelEnum.PRIVATE_SEA.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());
        List<Integer> notHavaSaleCustomerIds = isValidMap.entrySet()
                .stream()
                .filter(entry -> CustomerSeaLabelEnum.OPEN_SEA.equals(entry.getValue()))
                .map(Map.Entry::getKey)
                .collect(Collectors.toList());

        if (!CollectionUtils.isEmpty(havaSaleCustomerIds)) {
            customerActionService.updateCustomerSeaLabelByIds(havaSaleCustomerIds, CustomerSeaLabelEnum.PRIVATE_SEA.getCode());
        }
        if (!CollectionUtils.isEmpty(notHavaSaleCustomerIds)) {
            customerActionService.updateCustomerSeaLabelByIds(notHavaSaleCustomerIds, CustomerSeaLabelEnum.OPEN_SEA.getCode());
        }
        log.info("处理公私海标签关系成功,私海客户数量:{}, 公海客户数量:{}.", havaSaleCustomerIds.size(), notHavaSaleCustomerIds.size());
    }

    @Data
    static class Param {

        private List<Integer> customerIds;
    }
}

