package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.account.service.IQueryAccountService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * 如果 mid 的蓝v 属性变更，删除账号绑定的原来的蓝 v mid 信息
 */
@Component
@JobHandler("AccountMidBizMappingJob")
@Slf4j
public class AccountMidBizMappingJob extends IJobHandler {

    @Autowired
    private IQueryAccountService queryAccountService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("=====>>>>>>>>>>>>>>>AccountMidBizMappingJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> queryAccountService.deleteOldMidRecordIfNotNow());
        log.info("=====>>>>>>>>>>>>>>>AccountMidBizMappingJob start executing...");
        return ReturnT.SUCCESS;
    }
}
