package com.bilibili.crm.platform.job.customer;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.sea.QueryCustomerSeaDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.enums.CustomerSeaLabelEnum;
import com.bilibili.crm.platform.api.sale_sea.ISaleSeaService;
import com.bilibili.crm.platform.api.statusmachine.customer.enums.sea.CustomerSeaStateEnum;
import com.bilibili.crm.platform.biz.crm.account.dao.read.ExtCustomerDao;
import com.bilibili.crm.platform.biz.po.CustomerSeaPo;
import com.bilibili.crm.platform.biz.repo.customer.CustomerSeaRepo;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.biz.util.MDCDecoratorUtil;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.bilibili.crm.platform.utils.ThreadPoolUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StopWatch;

import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.atomic.AtomicInteger;
import java.util.stream.Collectors;

/**
 * 客户公海 JOB
 *
 * <AUTHOR>
 * @date 2023/4/4 16:43
 */
@Slf4j
@Component
@JobHandler("customerSeaJob")
public class CustomerSeaJob extends AbstractBaseJobHandler {

    @Autowired
    private ICustomerQueryService customerQueryService;
    @Resource
    private CustomerSeaRepo customerSeaRepo;
    @Resource
    private ISaleSeaService saleSeaService;
    @Autowired
    private ExtCustomerDao extCustomerDao;

    /**
     * 判断客户是否需要流入、流出公海
     */
    public void handleCustomerSea() {
        // 判断公海外的客户是否需要流入
        handleCustomerOutsideSea();
        // 判断公海内的客户是否需要流出
        handleCustomerInsideSea();
    }

    /**
     * 判断公海内的客户是否需要流出
     */
    public void handleCustomerInsideSea() {
        long startTime = System.currentTimeMillis();
        log.info(">>> 开始处理公海内的客户");
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("fetch QueryCustomerSeaDto");
        QueryCustomerSeaDto query = QueryCustomerSeaDto.builder()
                .seaState(CustomerSeaStateEnum.VALID.getCode())
                .build();
        List<CustomerSeaPo> pos = customerSeaRepo.queryByParam(query, false);
        stopWatch.stop();
        if (CollectionUtils.isEmpty(pos)) {
            // 客户公海内没有客户
            log.info(">>> 客户公海中没有客户");
            return;
        }
        // 多线程执行
        stopWatch.start("filter customerIds");
        List<Long> toBeInvalidIds = ThreadPoolUtils.asyncExecute(4, pos,
                MDCDecoratorUtil.function(subPos -> {
                    // 判断这批客户是否要流出公海
                    List<Integer> customerIds = CrmUtils.convertDistinct(subPos, CustomerSeaPo::getCustomerId);
                    Map<Integer, CustomerSeaLabelEnum> isValidMap = saleSeaService.judgeCustomerSeaLabelByCustomerIds(customerIds);
                    // 留下私海
                    List<Integer> customerFilterIds = isValidMap.entrySet()
                            .stream()
                            .filter(entry -> CustomerSeaLabelEnum.PRIVATE_SEA.equals(entry.getValue()))
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toList());
                    return subPos.stream().filter(e -> customerFilterIds.contains(e.getCustomerId())).map(CustomerSeaPo::getId).collect(Collectors.toList());
                }));
        stopWatch.stop();
        stopWatch.start("customers outflows");
        // 将不符合的客户流出公海
        if (!CollectionUtils.isEmpty(toBeInvalidIds)) {
            log.info("toBeFlowOutCustomerIds_is, 流出公海的客户IDS: {}", JSON.toJSONString(pos.stream().filter(e -> toBeInvalidIds.contains(e.getId())).map(CustomerSeaPo::getCustomerId).collect(Collectors.toList())));
        }
        int count = customerSeaRepo.outflows(toBeInvalidIds);
        stopWatch.stop();
        log.info("{}", stopWatch.prettyPrint());
        log.info(">>> 本次共有 {} 名客户从公海中流出, 总耗时: {}ms", count, System.currentTimeMillis() - startTime);
    }

    /**
     * 判断公海外的客户是否需要流入
     */
    public void handleCustomerOutsideSea() {
        log.info(">>> handleCustomerOutsideSea start");
        long startTime = System.currentTimeMillis();
        StopWatch stopWatch = new StopWatch();
        stopWatch.start("fetch customerIds");
        // 查询外部机构客户
        List<Integer> customerIds = extCustomerDao.getAllOrgOutCustomerWithAgent();
        stopWatch.stop();
        log.info(">>> handleCustomerOutsideSea start: {}", customerIds.size());
        stopWatch.start("filter customerIds");
        // 多线程执行
        List<Integer> toBeFlowInCustomerIds = ThreadPoolUtils.asyncExecute(4, customerIds,
                MDCDecoratorUtil.function(subIds -> {
                    Map<Integer, CustomerSeaLabelEnum> isValidMap = saleSeaService.judgeCustomerSeaLabelByCustomerIds(subIds);
                    // 留下公海
                    return isValidMap.entrySet()
                            .stream()
                            .filter(entry -> CustomerSeaLabelEnum.OPEN_SEA.equals(entry.getValue()))
                            .map(Map.Entry::getKey)
                            .collect(Collectors.toList());
                }));
        stopWatch.stop();
        stopWatch.start("customers inflows");
        // 查询公海中所有的有效客户，包括不可见的
        QueryCustomerSeaDto query = QueryCustomerSeaDto.builder()
                .seaStates(Lists.newArrayList(CustomerSeaStateEnum.VALID.getCode(), CustomerSeaStateEnum.INVISIBLE.getCode()))
                .build();
        List<CustomerSeaPo> pos = customerSeaRepo.queryByParam(query, false);
        Map<Integer, CustomerSeaPo> customerIdsInSea = CrmUtils.toMapIdentity(pos, CustomerSeaPo::getCustomerId);
        // 过滤已经在公海中的客户
        toBeFlowInCustomerIds = CrmUtils.filter(toBeFlowInCustomerIds, id -> !customerIdsInSea.containsKey(id));
        AtomicInteger count = new AtomicInteger();
        if (CollectionUtils.isNotEmpty(toBeFlowInCustomerIds)) {
            log.info("toBeFlowInCustomerIds_is, 流入公海的客户IDS: {}", JSON.toJSONString(toBeFlowInCustomerIds));
            ThreadPoolUtils.asyncExecuteVoid(20, toBeFlowInCustomerIds, MDCDecoratorUtil.consumer(subIds -> {
                int batchSize = 2000;
                List<List<Integer>> customerIdsSubList = Lists.partition(subIds, batchSize);
                customerIdsSubList.forEach(subCustomerIds -> {
                    List<CustomerBaseDto> toBeFlowInCustomers = customerQueryService.getCustomerBaseDtosByIds(subCustomerIds);
                    count.addAndGet(customerSeaRepo.inflows(toBeFlowInCustomers));
                });
            }));
        }
        stopWatch.stop();
        log.info("{}", stopWatch.prettyPrint());
        log.info(">>> 本次共有 {} 名客户流入公海中, 总耗时: {}ms", count, System.currentTimeMillis() - startTime);
    }

    @Override
    protected boolean biz(String payload) {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            log.info(">>> 客户公海定时任务开始 at {}", CrmUtils.formatDate(Utils.getNow()));
            // 处理客户流入、流出公海
            handleCustomerSea();
            log.info(">>> 客户公海定时任务结束 at {}", CrmUtils.formatDate(Utils.getNow()));
        });
        return true;
    }
}
