package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.service.finance.notify.NotifySaleLeaderService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("NotifySalePaymentConfirmationLeaderJob")
@Slf4j
public class NotifySalePaymentConfirmationLeaderJob extends IJobHandler {

    @Autowired
    private NotifySaleLeaderService notifySaleLeaderService;

    @Autowired
    private RedisLock redisLock;

    private static final String NotifySalePaymentConfirmationLeaderJobKey = "NotifySalePaymentConfirmationLeaderJobKey";

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        // 最后解锁下，下面可能发生并发，
        redisLock.getLock(NotifySalePaymentConfirmationLeaderJobKey, 5000);
        try {
            log.info("NotifySalePaymentConfirmationLeaderJob execute start");
            // 防止短时间发送多次
            notifySaleLeaderService.notifySaleLeader();
        } catch (Exception e) {
            log.error("NotifySalePaymentConfirmationLeaderJobKey", e);
        } finally {
            redisLock.releaseLock(NotifySalePaymentConfirmationLeaderJobKey);
        }

        // 去重复操作
        return ReturnT.SUCCESS;
    }
}
