package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.service.customer.ext.ICustomerExtService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 企查查客户信息更新job
 */
@Component
@JobHandler("CrmCustomerMsgExtJob")
@Slf4j
public class CrmCustomerMsgExtJob extends IJobHandler {

    @Autowired
    private ICustomerExtService customerExtService;

    /**
     * @param args exp：args:20200714
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>CrmCustomerMsgExtJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            customerExtService.loadQichachaDataAndInsert(Utils.getYesteday());
        });
        log.info("=====>>>>>>>>>>>>>>>CrmCustomerMsgExtJob end executing...");
        return ReturnT.SUCCESS;
    }
}
