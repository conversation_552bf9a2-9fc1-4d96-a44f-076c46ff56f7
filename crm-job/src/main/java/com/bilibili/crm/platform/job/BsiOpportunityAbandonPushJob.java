package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.platform.api.oa.dto.OaCoreUserInfo;
import com.bilibili.crm.platform.api.oa.dto.OaUserInfo;
import com.bilibili.crm.platform.api.oa.service.IOaUserInfoService;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.dto.SimpleSaleDto;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.biz.common.FollowStage;
import com.bilibili.crm.platform.biz.dao.BsiOpportunityDao;
import com.bilibili.crm.platform.biz.dao.BsiOpportunitySaleMappingDao;
import com.bilibili.crm.platform.biz.exception.wx.WxAppException;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPo;
import com.bilibili.crm.platform.biz.po.BsiOpportunityPoExample;
import com.bilibili.crm.platform.biz.po.BsiOpportunitySaleMappingPo;
import com.bilibili.crm.platform.biz.po.BsiOpportunitySaleMappingPoExample;
import com.bilibili.crm.platform.biz.service.policy.component.user.configable.IConfigUserQuerier;
import com.bilibili.crm.platform.biz.util.wx.WxMsgUtils;
import com.bilibili.crm.platform.biz.util.wx.dto.EnterpriseWeChatAgentType;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.SaleGroupStatus;
import com.bilibili.rbac.api.dto.UserBaseDto;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import edu.emory.mathcs.backport.java.util.Collections;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.Comparator;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description
 * @date 10/23/23
 **/
@Component
@JobHandler("bsiOpportunityAbandonPushJob")
@Slf4j
public class BsiOpportunityAbandonPushJob extends IJobHandler {

    @Autowired
    private BsiOpportunityDao bsiOpportunityDao;

    @Resource
    private ISaleService iSaleService;

    @Resource
    private ISaleGroupService iSaleGroupService;

    @Resource
    private WxMsgUtils wxMsgUtils;

    @Autowired
    private IOaUserInfoService oaUserInfoService;

    @Resource
    private IConfigUserQuerier iConfigUserQuerier;

    @Resource
    private BsiOpportunitySaleMappingDao bsiOpportunitySaleMappingDao;

    //线上693
    @Value("${bsi.opp.sale.manager.push.roleid:479}")
    private Integer sale_manger_role;

    private String oppBsiAbandonWxUrl = "http://cm.bilibili.com/ldad/opportunity/main.html#/?follow_stages=0";

    @Value("${bsi.opp.sale.manager.push.switch:0}")
    private Integer pushOn;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        Map<Integer, SaleDto> saleDtoMap = iSaleService.getAllSaleMapWithCache();
        List<SaleGroupDto> saleGroupDtos = iSaleGroupService.getAll();

        List<BsiOpportunityPo> allAbandon = new ArrayList<>();
        Integer tmpId = 0;
        while (true) {
            BsiOpportunityPoExample example = new BsiOpportunityPoExample();
            example.createCriteria()
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andFollowStageEqualTo(FollowStage.ABANDON_ORDER.getCode())
                    .andAbandonTypeEqualTo(0)
                    .andExpectLaunchEndDateGreaterThanOrEqualTo(new Timestamp(1696089600000L))  // 预计投放时间 在 2023-10-01之后的
                    .andIdGreaterThan(tmpId);
            example.setOrderByClause("id asc");
            example.setLimit(500);
            List<BsiOpportunityPo> bsiOpportunityPos = bsiOpportunityDao.selectByExample(example);
            if (CollectionUtils.isEmpty(bsiOpportunityPos)) {
                break;
            }
            allAbandon.addAll(bsiOpportunityPos);
            tmpId = bsiOpportunityPos.get(bsiOpportunityPos.size() - 1).getId();
        }

        if (CollectionUtils.isEmpty(allAbandon)) {
            log.info("empty abandon bsiOpportunity");
            return ReturnT.SUCCESS;
        }

        Map<String, BsiOpportunityPo> saleBsiOppAbandonMap = allAbandon.stream().collect(Collectors.toMap(r -> r.getSalesmanId() + "_" + r.getId(), Function.identity()));

        List<Integer> abandonBsiOppIds = allAbandon.stream().map(BsiOpportunityPo::getId).distinct().collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(abandonBsiOppIds)) {
            BsiOpportunitySaleMappingPoExample example = new BsiOpportunitySaleMappingPoExample();
            example.createCriteria().andBsiOpportunityIdIn(abandonBsiOppIds).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            List<BsiOpportunitySaleMappingPo> bsiOpportunitySaleMappingPos = bsiOpportunitySaleMappingDao.selectByExample(example);
            List<BsiOpportunityPo> saleBsiOppMappingPos = bsiOpportunitySaleMappingPos.stream().map(r -> BsiOpportunityPo.builder()
                    .id(r.getBsiOpportunityId())
                    .salesmanId(r.getSaleId()).build()).collect(Collectors.toList());
            Map<String, BsiOpportunityPo> saleBsiOppMappingPosMap = saleBsiOppMappingPos.stream()
                    .collect(Collectors.toMap(r -> r.getSalesmanId() + "_" + r.getId(), Function.identity(), (a, b)-> a));
            for (Map.Entry<String, BsiOpportunityPo> bsiOpportunityPoEntry : saleBsiOppMappingPosMap.entrySet()) {
                saleBsiOppAbandonMap.putIfAbsent(bsiOpportunityPoEntry.getKey(), bsiOpportunityPoEntry.getValue());
            }
        }

        Map<Integer, List<BsiOpportunityPo>> saleBsiOppAbandonGroup = new ArrayList<>(saleBsiOppAbandonMap.values()).stream().collect(Collectors.groupingBy(BsiOpportunityPo::getSalesmanId));
        //销售推送
        salePush(saleDtoMap, saleBsiOppAbandonGroup);
        //销售组长推送
        saleLeaderPush(saleGroupDtos, saleBsiOppAbandonGroup);
        //销管推送
        saleManagerPush(allAbandon, saleBsiOppAbandonGroup);

        return ReturnT.SUCCESS;
    }

    private void saleManagerPush(List<BsiOpportunityPo> allAbandon, Map<Integer, List<BsiOpportunityPo>> saleBsiOppAbandonMap) {
        //销管推送  销售管理组 693
        List<UserBaseDto> userBaseDtos = iConfigUserQuerier.queryByRoleId(sale_manger_role);
        //要推送的销售小组
        String saleManagerTitle = String.format("营销中心有%s条弃单商机未填写弃单原因%s", allAbandon.size(), "</br>");
        StringBuilder saleManagerContext = new StringBuilder("");
        // 直客组（46）  渠道组（85）
        List<SaleGroupDto> saleManagerNeedPushGroup = new ArrayList<>();
        List<SaleGroupDto> directNextSalesGroup = iSaleGroupService.getNextSalesGroup(46);
        List<SaleGroupDto> channelNextSalesGroup = iSaleGroupService.getNextSalesGroup(85);
        saleManagerNeedPushGroup.addAll(directNextSalesGroup);
        saleManagerNeedPushGroup.addAll(channelNextSalesGroup);
        for (SaleGroupDto saleGroupDto : saleManagerNeedPushGroup) {

            Integer count = 0;
            List<SaleDto> sales = iSaleService.getSalesByGroupId(saleGroupDto.getId());
            List<Integer> saleIds = sales.stream().map(SaleDto::getId).distinct().collect(Collectors.toList());
            for (Integer saleId : saleIds) {
                count += saleBsiOppAbandonMap.getOrDefault(saleId, Collections.emptyList()).size();
            }

            if (!Utils.isPositive(count)) {
                continue;
            }

            saleManagerContext.append(saleGroupDto.getName() + ": " + count + " 条 <br>");
        }
        saleManagerContext.append(String.format("<a href=\"%s\">请push销售及时填写弃单原因</a>", oppBsiAbandonWxUrl));
        List<String> userNames = userBaseDtos.stream().map(UserBaseDto::getUsername).distinct().collect(Collectors.toList());
        List<OaCoreUserInfo> oaUserInfo = oaUserInfoService.getOaCoreUserInfos(userNames);
        List<String> workCodes = oaUserInfo.stream().map(OaCoreUserInfo::getWorkCode).distinct().collect(Collectors.toList());
        sendCrmWxMsg(workCodes, saleManagerTitle + saleManagerContext);
    }

    private void saleLeaderPush(List<SaleGroupDto> saleGroupDtos, Map<Integer, List<BsiOpportunityPo>> saleBsiOppAbandonMap) {
        List<Integer> groupIds = saleGroupDtos.stream().map(SaleGroupDto::getId).distinct().collect(Collectors.toList());
        Map<Integer, List<SimpleSaleDto>> groupIdLeaderMap = saleGroupMappingServiceImpl.queryLeaders(groupIds);
        for (SaleGroupDto saleGroupDto : saleGroupDtos) {
            List<SimpleSaleDto> leaders = groupIdLeaderMap.get(saleGroupDto.getId());
            if (!SaleGroupStatus.VALID.getCode().equals(saleGroupDto.getStatus()) || CollectionUtils.isEmpty(leaders)) {
                continue;
            }
            List<SaleAbandonInfo> saleAbandonInfos = new ArrayList<>();
            List<SaleDto> saleDtos = iSaleService.getSalesByGroupId(saleGroupDto.getId());
            for (SaleDto saleDto : saleDtos) {
                if (!IsValid.TRUE.getCode().equals(saleDto.getStatus())) {
                    continue;
                }
                int size = saleBsiOppAbandonMap.getOrDefault(saleDto.getId(), new ArrayList<>()).size();

                SaleAbandonInfo saleAbandonInfo = new SaleAbandonInfo();
                saleAbandonInfo.setSaleDto(saleDto);
                saleAbandonInfo.setAbandonCount(size);
                saleAbandonInfos.add(saleAbandonInfo);
            }

            saleAbandonInfos = saleAbandonInfos.stream().sorted(Comparator.comparing(SaleAbandonInfo::getAbandonCount).reversed()).collect(Collectors.toList());
            log.info("saleGroupDto {} , saleAbandonInfos {}", saleGroupDto, saleAbandonInfos);
            Integer groupAbandonCount = saleAbandonInfos.stream().map(SaleAbandonInfo::getAbandonCount).reduce(Integer::sum).orElse(0);
            if (!Utils.isPositive(groupAbandonCount)) {
                continue;
            }

            String saleLeaderTitle = String.format("您的团队有%s条弃单商机未填写弃单原因%s", groupAbandonCount, "</br>");
            List<SaleAbandonInfo> subList = saleAbandonInfos.subList(0, 2);
            StringBuilder saleGroupContext = new StringBuilder();
            for (SaleAbandonInfo abandonInfo : subList) {
                saleGroupContext.append(String.format("%s, %s 条%s", abandonInfo.getSaleDto().getEmail(), abandonInfo.getAbandonCount(), "</br>"));
            }
            saleGroupContext.append(String.format("<a href=\"%s\">请push销售及时填写弃单原因</a>", oppBsiAbandonWxUrl));

            for (SimpleSaleDto leader : leaders) {
                // 根据域账号查询员工信息
                List<OaUserInfo> oaUserInfo = oaUserInfoService.getOaUserInfo(leader.getEmail());
                if (CollectionUtils.isEmpty(oaUserInfo)) {
                    continue;
                }
                sendCrmWxMsg(Lists.newArrayList(oaUserInfo.get(0).getWorkCode()), saleLeaderTitle + saleGroupContext);
            }
        }
    }

    private void salePush(Map<Integer, SaleDto> saleDtoMap, Map<Integer, List<BsiOpportunityPo>> saleBsiOppAbandonMap) {
        for (Map.Entry<Integer, SaleDto> saleDtoEntry : saleDtoMap.entrySet()) {
            SaleDto saleDto = saleDtoEntry.getValue();
            if (!IsValid.TRUE.getCode().equals(saleDto.getStatus())) {
                continue;
            }

            List<BsiOpportunityPo> bsiOpportunityPos = saleBsiOppAbandonMap.get(saleDto.getId());
            if (CollectionUtils.isEmpty(bsiOpportunityPos)) {
                continue;
            }
            String saleTitle = String.format("您有%s条弃单商机未填写弃单原因%s", bsiOpportunityPos.size(), "</br>");
            String saleContext = String.format("<a href=\"%s\">请点击正文，跳转至商机列表进行信息补充</a>", oppBsiAbandonWxUrl);

            // 根据域账号查询员工信息
            List<OaUserInfo> oaUserInfo = oaUserInfoService.getOaUserInfo(saleDto.getEmail());
            if (CollectionUtils.isEmpty(oaUserInfo)) {
                continue;
            }
//            sendCrmWxMsg(Lists.newArrayList(oaUserInfo.get(0).getWorkCode()), saleTitle, saleContext, oppBsiAbandonWxUrl);
            sendCrmWxMsg(Lists.newArrayList(oaUserInfo.get(0).getWorkCode()), saleTitle + saleContext);
        }
    }


    private void sendCrmWxMsg(List<String> userWorkCodeList, String title, String content, String url) {
        log.info(">>> 发送企微消息, content: {}", content);

        try {
            wxMsgUtils.sendTextCard(EnterpriseWeChatAgentType.CRM_DATA_AGENT.getCode(), userWorkCodeList, title, content, url);
        } catch (WxAppException e) {
            log.error(">>> 发送商机弃单消息异常, userList: {}, content: {}", JSON.toJSON(userWorkCodeList), content, e);
        }
    }

    private void sendCrmWxMsg(List<String> userWorkCodeList, String content) {
        log.info(">>> 发送企微消息, userWorkCodeList{}, content: {}", userWorkCodeList, content);

        try {
            if (Utils.isPositive(pushOn)) {
                wxMsgUtils.sendTextMessage(EnterpriseWeChatAgentType.CRM_DATA_AGENT.getCode(), userWorkCodeList, content);
            }
        } catch (WxAppException e) {
            log.error(">>> 发送线索分配企微通知异常, userList: {}, content: {}", JSON.toJSON(userWorkCodeList), content, e);
        }
    }


    private static class SaleAbandonInfo {
        private SaleDto saleDto;

        private Integer abandonCount;

        public SaleDto getSaleDto() {
            return saleDto;
        }

        public void setSaleDto(SaleDto saleDto) {
            this.saleDto = saleDto;
        }

        public Integer getAbandonCount() {
            return abandonCount;
        }

        public void setAbandonCount(Integer abandonCount) {
            this.abandonCount = abandonCount;
        }
    }


}
