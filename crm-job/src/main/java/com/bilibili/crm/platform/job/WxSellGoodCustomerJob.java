package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.writer.WxSellGoodCustomerWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-02-07 21:36:02
 * @description:
 **/

@Component
@JobHandler("WxSellGoodCustomerJob")
@Slf4j
public class WxSellGoodCustomerJob extends IJobHandler {

    @Resource
    private WxSellGoodCustomerWriter wxSellGoodCustomerWriter;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    @Autowired
    private WxAppDataCheckService dataCheckService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Timestamp begin = new Timestamp(System.currentTimeMillis());
        Timestamp end = new Timestamp(System.currentTimeMillis());
        if (StringUtils.hasText(s) && s.contains("~")) {
            String[] strings = s.split("~");
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }
        List<Timestamp> dayList = Utils.getEachDays(begin, end);

        if (!StringUtils.hasText(s)) {
            //检查当天状态位
            Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
            if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SELL_GOODS_CUSTOMER)) {
                Cat.logEvent("WxAppCacheSellGoodsCustomerJob_DATA_HAD_DOWN", day.toString());
                return ReturnT.SUCCESS;
            }
        }
        dayList.forEach(day -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                try {
                    //检查ck数据是否ready
                    if (!dataCheckService.mobileDataCheckByState(incomeTimeUtil.getYesterdayBegin(day), dataCheckService.generateSellGoodFlyCkKey(incomeTimeUtil.getYesterdayBegin(day)))) {
                        return;
                    }
                    // 检查大盘模块是否完成
                    if (!wxAppCacheManager.isModulePaperStateReadyV2(Utils.getSomeDayAgo(day, 1), WxModuleType.SELL_GOODS_DASHBOARD)) {
                        return;
                    }
                    log.info("WxAppCacheSellGoodsCustomerStart:{}", incomeTimeUtil.getYesterdayBegin(day));
                    long startTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildIncomeRatioByCategory_start:{} ms",startTime);
                    wxSellGoodCustomerWriter.buildIncomeRatioByCategory(day);
                    long endTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildIncomeRatioByCategory_end:{} ms",endTime);
                    startTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildIncomeRatioByCategory_start:{} ms",startTime);
                    wxSellGoodCustomerWriter.buildIncomeRatioByCategory(day);
                    endTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildIncomeRatioByCategory_end:{} ms",endTime);
                    startTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildContrastByCategory_start:{} ms",startTime);
                    wxSellGoodCustomerWriter.buildContrastByCategory(day);
                    endTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildContrastByCategory_end:{} ms",endTime);
                    startTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildIncomeOverviewByCategory_start:{} ms",startTime);
                    wxSellGoodCustomerWriter.buildIncomeOverviewByCategory(day);
                    endTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildIncomeOverviewByCategory_end:{} ms",endTime);
                    startTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildProductOverviewByCategory_start:{} ms",startTime);
                    wxSellGoodCustomerWriter.buildProductOverviewByCategory(day);
                    endTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildProductOverviewByCategory_end:{} ms",endTime);
                    startTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildCustomerTop_start:{} ms",startTime);
                    wxSellGoodCustomerWriter.buildCustomerTop(day);
                    endTime = System.currentTimeMillis();
                    log.info("WxSellGoodCustomerJob,buildCustomerTop_end:{} ms",endTime);
                    log.info("WxAppCacheSellGoodsCustomerEnd:{}", incomeTimeUtil.getYesterdayBegin(day));
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(day), WxModuleType.SELL_GOODS_CUSTOMER);
                } catch (Exception e) {
                    Cat.logEvent("Exception_WxSalesGoodCustomerJob", e.getMessage());
                    throw new RuntimeException(e);
                }
            });
        });

        return ReturnT.SUCCESS;
    }
}
