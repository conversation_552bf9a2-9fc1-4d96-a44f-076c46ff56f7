package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunitySnapshotService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2020/12/3 9:41 下午
 */
@Component
@JobHandler("BsiOpportunitySnapshotJob")
@Slf4j
public class BsiOpportunitySnapshotJob  extends IJobHandler {
    @Autowired
    private IBsiOpportunitySnapshotService snapshotService;

    /**
     * 商机快照 每天生成昨天的快照
     * @param args
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {

        Timestamp date = Utils.getYesteday();
        if (StringUtils.hasText(args) ) {
            date = StringDateParser.stringToTimestamp(args);
        }
        Timestamp finalDate = date;
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> snapshotService.saveSnapshot(finalDate));

        return ReturnT.SUCCESS;
    }
}
