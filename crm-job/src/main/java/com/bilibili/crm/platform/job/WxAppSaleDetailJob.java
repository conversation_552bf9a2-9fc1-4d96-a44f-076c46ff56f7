package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.sale.WxAppSaleDetailWriter;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.dianping.cat.Cat;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-04-24 10:46:43
 * @description:
 **/
@Component
@JobHandler("WxAppSaleDetailJob")
@Slf4j
public class WxAppSaleDetailJob extends AbstractBaseJobHandler {

    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Autowired
    private WxAppCacheManager wxAppCacheManager;

    @Resource
    private WxAppSaleDetailWriter wxAppSaleDetailWriter;

    @Override
    protected boolean biz(String args) {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (!StringUtils.hasText(args)) {
                //检查当天状态位
                Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
                if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SALE_DETAIL_DIRECT)) {
                    Cat.logEvent("WxAppSaleDirectJob_DATA_HAD_DOWN", day.toString());
                    return true;
                }
            }

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);
            Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
            // 对历史季度最后一天回刷
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                dayList.add(timestamp);
            }

            dayList.forEach(d -> {
                try {
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                        wxAppSaleDetailWriter.cacheRequest(d);
                    });
                    Cat.logEvent("WxAppSaleDirectJob_SUCCESS", d.toString() + ":normal");
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(d), WxModuleType.SALE_DETAIL_DIRECT);
                } catch (Exception e) {
                    Cat.logEvent("WxAppSaleDirectJob_FAILED", d.toString() + ":failed");
                    AlarmHelper.log("WxAppSaleCacheJob_FAILED", e, CrmUtils.formatDate(d));
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppSaleDirectJob", e.getMessage());
            AlarmHelper.alarmEx("DailyReportPartFailed", e, this.getJobName());
            throw e;
        }
        log.info("WxAppSaleDirectJob success args {}", args);

        return true;
    }

}
