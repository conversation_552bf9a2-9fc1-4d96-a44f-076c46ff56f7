package com.bilibili.crm.platform.job.coupon;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.platform.api.coupon.ICouponService;
import com.bilibili.crm.platform.api.coupon.enums.CouponStatus;
import com.bilibili.crm.platform.api.coupon.enums.CouponType;
import com.bilibili.crm.platform.biz.crm.wallet.dao.write.CrmAccCouponWalletDao;
import com.bilibili.crm.platform.biz.po.CrmAccCouponWalletPo;
import com.bilibili.crm.platform.biz.po.CrmAccCouponWalletPoExample;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @Description
 * @date 2023/4/25
 * 生效已使用-> 已失效已使用
 **/
@Component
@JobHandler("CouponEndJob")
@Slf4j
public class CouponEndJob extends IJobHandler {

    @Autowired
    private ICouponService iCouponService;
    @Autowired
    private CrmAccCouponWalletDao crmAccCouponWalletDao;

    @Value("${coupon.batch.size:10000}")
    private Long couponBatchSize;
    //private final static Long BATCH_SIZE = 10L;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
//        activeUsedToEnd();
//        unActiveUsedToEnd();
//        activeUsedToStaleUsed();
        //临时慢sql解决方案
        activeUsedToEndTmp();
        unActiveUsedToEndTmp();
        activeUsedToStaleUsedTmp();

        return ReturnT.SUCCESS;
    }

    private void activeUsedToEnd() {
        Long tmpId = 0L;
        while (true) {
            CrmAccCouponWalletPoExample example = new CrmAccCouponWalletPoExample();
            CrmAccCouponWalletPoExample.Criteria criteria = example.createCriteria();
            criteria.andIdGreaterThanOrEqualTo(tmpId);
            criteria.andBizStatusEqualTo(CouponStatus.ACTIVE_USED.getId());
            criteria.andEndTimeLessThan(new Timestamp(System.currentTimeMillis()));
            criteria.andCouponTypeEqualTo(CouponType.COUPON.getId());
            criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            example.setLimit(2000);
            example.setOrderByClause(" id asc ");

            List<CrmAccCouponWalletPo> crmAccCouponWalletPos = crmAccCouponWalletDao.selectByExample(example);
            if (CollectionUtils.isEmpty(crmAccCouponWalletPos)) {
                break;
            }
            tmpId = crmAccCouponWalletPos.get(crmAccCouponWalletPos.size() - 1).getId();

            //
            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPos) {
                iCouponService.endCoupon(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId());
                log.info("endCoupon {}", couponWalletPo);
            }
        }
    }

    private void unActiveUsedToEnd() {
        Long tmpId = 0L;
        while (true) {
            CrmAccCouponWalletPoExample example = new CrmAccCouponWalletPoExample();
            CrmAccCouponWalletPoExample.Criteria criteria = example.createCriteria();
            criteria.andIdGreaterThanOrEqualTo(tmpId);
            criteria.andBizStatusIn(Lists.newArrayList(CouponStatus.INIT.getId(), CouponStatus.ACTIVE_UNUSED.getId()));
            criteria.andEndTimeLessThan(new Timestamp(System.currentTimeMillis()));
            criteria.andCouponTypeIn(Lists.newArrayList(CouponType.COUPON.getId(), CouponType.PRICE_BREAK.getId(), CouponType.DISCOUNT.getId()));
            criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
            example.setLimit(2000);
            example.setOrderByClause(" id asc ");

            List<CrmAccCouponWalletPo> crmAccCouponWalletPos = crmAccCouponWalletDao.selectByExample(example);
            if (CollectionUtils.isEmpty(crmAccCouponWalletPos)) {
                break;
            }
            tmpId = crmAccCouponWalletPos.get(crmAccCouponWalletPos.size() - 1).getId();

            //
            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPos) {
                iCouponService.updateCouponStatus(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId(), CouponStatus.STALE_UNUSED.getId());
                log.info("unActiveUsedToEnd {}", couponWalletPo);
            }
        }
    }

    public void activeUsedToEndTmp() {
        Long tmpId = 0L;
        Timestamp endDate = new Timestamp(System.currentTimeMillis());
        Long maxId = getMaxId();

        while (tmpId <= maxId) {
            long beginTime = System.currentTimeMillis();
            CrmAccCouponWalletPoExample example = new CrmAccCouponWalletPoExample();
            CrmAccCouponWalletPoExample.Criteria criteria = example.createCriteria();
            criteria.andIdGreaterThanOrEqualTo(tmpId);
            criteria.andIdLessThan(tmpId + couponBatchSize);
            criteria.andBizStatusEqualTo(CouponStatus.ACTIVE_USED.getId());
            criteria.andEndTimeLessThan(endDate);
            criteria.andCouponTypeIn(Lists.newArrayList(CouponType.COUPON.getId()));
            criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            List<CrmAccCouponWalletPo> crmAccCouponWalletPos = crmAccCouponWalletDao.selectByExample(example);

            long endTime = System.currentTimeMillis();
            tmpId = tmpId + couponBatchSize;
            log.info("activeUsedToEndTmp endId {} cost {}", tmpId, endTime - beginTime);

            if (CollectionUtils.isEmpty(crmAccCouponWalletPos)) {
                continue;
            }
            //
            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPos) {
                iCouponService.endCoupon(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId());
                log.info("endCoupon {}", couponWalletPo);
            }
        }
    }

    public Long getMaxId() {
        CrmAccCouponWalletPoExample selectExample = new CrmAccCouponWalletPoExample();
        selectExample.setLimit(1);
        selectExample.setOrderByClause("id desc");

        List<CrmAccCouponWalletPo> pos = crmAccCouponWalletDao.selectByExample(selectExample);
        CrmAccCouponWalletPo crmAccCouponWalletPo = pos.get(0);
        log.info("CouponEndJob  getMaxId  {}", crmAccCouponWalletPo.getId());
        return crmAccCouponWalletPo.getId();
    }

    public void unActiveUsedToEndTmp() {
        Long tmpId = 0L;
        Timestamp endDate = new Timestamp(System.currentTimeMillis());
        Long maxId = getMaxId();
        while (tmpId <= maxId) {
            long beginTime = System.currentTimeMillis();
            CrmAccCouponWalletPoExample example = new CrmAccCouponWalletPoExample();
            CrmAccCouponWalletPoExample.Criteria criteria = example.createCriteria();
            criteria.andIdGreaterThanOrEqualTo(tmpId);
            criteria.andIdLessThan(tmpId + couponBatchSize);
            criteria.andBizStatusIn(Lists.newArrayList(CouponStatus.INIT.getId(), CouponStatus.ACTIVE_UNUSED.getId()));
            criteria.andEndTimeLessThan(endDate);
            criteria.andCouponTypeIn(Lists.newArrayList(CouponType.COUPON.getId(), CouponType.PRICE_BREAK.getId(), CouponType.DISCOUNT.getId()));
            criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            List<CrmAccCouponWalletPo> crmAccCouponWalletPos = crmAccCouponWalletDao.selectByExample(example);

            tmpId = tmpId + couponBatchSize;
            log.info("unActiveUsedToEndTmp endId {}", tmpId);
            long endTime = System.currentTimeMillis();
            log.info("unActiveUsedToEndTmp endId {} cost {}", tmpId, endTime - beginTime);

            if (CollectionUtils.isEmpty(crmAccCouponWalletPos)) {
                continue;
            }

            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPos) {
                iCouponService.updateCouponStatus(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId(), CouponStatus.STALE_UNUSED.getId());
                log.info("unActiveUsedToEnd {}", couponWalletPo);
            }
        }
    }



    public void activeUsedToEndTest() {
        Long tmpId = 0L;
        Timestamp endDate = new Timestamp(System.currentTimeMillis());
        Long maxId = 50000L;

        while (tmpId <= maxId) {
            long beginTime = System.currentTimeMillis();
            CrmAccCouponWalletPoExample example = new CrmAccCouponWalletPoExample();
            CrmAccCouponWalletPoExample.Criteria criteria = example.createCriteria();
            criteria.andIdGreaterThanOrEqualTo(tmpId);
            criteria.andIdLessThan(tmpId + couponBatchSize);
            criteria.andBizStatusEqualTo(CouponStatus.ACTIVE_USED.getId());
            criteria.andEndTimeLessThan(endDate);
            criteria.andCouponTypeEqualTo(CouponType.COUPON.getId());
            criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());

            List<CrmAccCouponWalletPo> crmAccCouponWalletPos = crmAccCouponWalletDao.selectByExample(example);

            long endTime = System.currentTimeMillis();
            tmpId = tmpId + couponBatchSize;
            log.info("activeUsedToEndTmp endId {} cost {}", tmpId, endTime - beginTime);

            if (CollectionUtils.isEmpty(crmAccCouponWalletPos)) {
                continue;
            }
            //
            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPos) {
                iCouponService.endCoupon(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId());
                log.info("endCoupon {}", couponWalletPo);
            }
        }
    }

    /**
     * 生效中已使用 在金额消耗完毕后还未到失效日期就转为 已失效已使用
     */
    private void activeUsedToStaleUsed() {
        Long tmpId = 0L;
        while (true) {
            CrmAccCouponWalletPoExample selectExample = new CrmAccCouponWalletPoExample();
            selectExample.setLimit(2000);
            selectExample.setOrderByClause(" id ASC ");
            selectExample.createCriteria()
                    .andIdGreaterThanOrEqualTo(tmpId)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCouponTypeEqualTo(CouponType.COUPON.getId())
                    .andBizStatusEqualTo(CouponStatus.ACTIVE_USED.getId())
                    .andEndTimeGreaterThan(new Timestamp(System.currentTimeMillis()))//过期时间大于当前时间表示未到过期时间
                    .andCouponValueBalanceLessThanOrEqualTo(0L);
            List<CrmAccCouponWalletPo> crmAccCouponWalletPoList = crmAccCouponWalletDao.selectByExample(selectExample);
            if (CollectionUtils.isEmpty(crmAccCouponWalletPoList)) {
                log.info("Amount_consumed_in_the_effective_used_quantity=0;tmpId={}", tmpId);
                break;
            }
            tmpId = crmAccCouponWalletPoList.get(crmAccCouponWalletPoList.size() - 1).getId();
            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPoList) {
                log.info("activeUsedToStaleUsed:{}", couponWalletPo);
                iCouponService.updateCouponStatus(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId(), CouponStatus.STALE_USED.getId());
            }
        }
    }

    /**
     * 生效中已使用 在金额消耗完毕后还未到失效日期就转为 已失效已使用
     */
    public void activeUsedToStaleUsedTmp() {
        Long tmpId = 0L;
        Timestamp endDate = new Timestamp(System.currentTimeMillis());
        Long maxId = getMaxId();

        while (tmpId <= maxId) {
            long beginTime = System.currentTimeMillis();

            CrmAccCouponWalletPoExample selectExample = new CrmAccCouponWalletPoExample();

            selectExample.createCriteria()
                    .andIdGreaterThanOrEqualTo(tmpId)
                    .andIdLessThan(tmpId + couponBatchSize)
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCouponTypeIn(Lists.newArrayList(CouponType.COUPON.getId()))
                    .andBizStatusEqualTo(CouponStatus.ACTIVE_USED.getId())
                    .andEndTimeGreaterThan(endDate)//过期时间大于当前时间表示未到过期时间
                    .andCouponValueBalanceLessThanOrEqualTo(0L);

            List<CrmAccCouponWalletPo> crmAccCouponWalletPoList = crmAccCouponWalletDao.selectByExample(selectExample);

            long endTime = System.currentTimeMillis();
            tmpId = tmpId + couponBatchSize;
            log.info("activeUsedToStaleUsedTmp endId {} cost {}", tmpId, endTime - beginTime);

            if (CollectionUtils.isEmpty(crmAccCouponWalletPoList)) {
                log.info("Amount_consumed_in_the_effective_used_quantity=0;tmpId={}", tmpId);
                continue;
            }
            for (CrmAccCouponWalletPo couponWalletPo : crmAccCouponWalletPoList) {
                log.info("activeUsedToStaleUsed:{}", couponWalletPo);
                iCouponService.updateCouponStatus(Operator.SYSTEM, couponWalletPo.getId(), couponWalletPo.getAccountId(), CouponStatus.STALE_USED.getId());
            }
        }
    }
}
