package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.trading.flow.service.ITradingFlowDayService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/7/17 11:48
 * 交易流水初始化job
 */
@Component
@JobHandler("TradingFlowInitJob")
@Slf4j
@Deprecated
public class TradingFlowInitJob extends IJobHandler {

    @Autowired
    private ITradingFlowDayService tradingFlowDayService;

    @Override
    public ReturnT<String> execute(String str) throws Exception {

        tradingFlowDayService.init(StringUtils.isEmpty(str) ? Utils.getToday() : Utils.getTimestamp(str));
        return ReturnT.SUCCESS;
    }
}
