package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.app.cache.sale.WxAppSaleHourWriter;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-06-06 13:30:14
 * @description:
 **/

@Component
@JobHandler("WxAppSaleHourDeleteJob")
@Slf4j
public class WxAppSaleHourDeleteJob extends IJobHandler {

    @Resource
    private WxAppSaleHourWriter wxAppSaleHourWriter;


    @Override
    public ReturnT<String> execute(String args) throws Exception {
        log.info("WxAppSaleHourDeleteJob start args {}", args);
        try {
            Timestamp timestamp = new Timestamp(System.currentTimeMillis());
            wxAppSaleHourWriter.cacheDataDelete(timestamp);
        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppSaleHourDeleteJob", e.getMessage());
            throw e;
        }
        log.info("WxAppSaleHourDeleteJob success args {}", args);
        return ReturnT.SUCCESS;
    }
}
