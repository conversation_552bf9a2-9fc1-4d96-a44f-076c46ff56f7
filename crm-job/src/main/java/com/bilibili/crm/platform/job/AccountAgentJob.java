package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.agent.service.IAgentService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * Created by fanwen<PERSON> on 2017/6/14.
 */
@Component
@JobHandler("AccountAgentJob")
@Slf4j
public class AccountAgentJob extends IJobHandler {

    @Autowired
    private IAgentService agentService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("=====>>>>>>>>>>>>>>>AccountAgentJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> agentService.batchSaveAccountAgent());
        log.info("=====>>>>>>>>>>>>>>>AccountAgentJob end executing.");
        return ReturnT.SUCCESS;
    }
}
