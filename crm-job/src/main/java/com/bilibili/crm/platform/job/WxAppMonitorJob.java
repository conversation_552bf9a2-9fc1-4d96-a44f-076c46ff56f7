package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.api.monitor.IWxAppMonitor;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/4/24
 * 企业微信日报监控
 **/
@Component
@JobHandler("WxAppMonitorJob")
@Slf4j
public class WxAppMonitorJob extends IJobHandler {

    @Autowired
    private IWxAppMonitor monitor;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            //状态位
            monitor.sendMonitorMail();
            //部门数据gap监控
            try {
                monitor.incomeGapWithCache(Utils.getToday());
            } catch (WxAppException e) {
                throw new RuntimeException(e);
            }
        });
        return ReturnT.SUCCESS;
    }
}
