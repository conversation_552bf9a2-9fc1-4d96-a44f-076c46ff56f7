package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.service.clue.GameClueService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("FetchGameClueDataJob")
@Slf4j
public class FetchGameClueDataJob extends IJobHandler {

    @Autowired
    private GameClueService gameClueService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> gameClueService.fetch());
        return ReturnT.SUCCESS;
    }
}
