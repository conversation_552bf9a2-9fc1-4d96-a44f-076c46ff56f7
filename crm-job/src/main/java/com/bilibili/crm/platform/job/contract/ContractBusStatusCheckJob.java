package com.bilibili.crm.platform.job.contract;

import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.contract.dto.QueryContractParam;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.statusmachine.contract.IContractStatusSync;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractAuditStatus;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.ContractType;
import com.bilibili.crm.platform.core.Parameter;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.Objects;

/**
 * 须在{@link com.bilibili.crm.platform.job.ContractSyncStatusInTimeJob}之后执行
 *
 * <AUTHOR>
 * @Description
 * @date 2022/8/15
 **/
@Component
@JobHandler("contractBusStatusCheckJob")
@Slf4j
public class ContractBusStatusCheckJob extends IJobHandler {

    @Resource
    private IContractQueryService iContractQueryService;
    @Resource
    private IContractStatusSync iContractStatusSync;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Parameter<Param> parameter = Parameter.getParameter(param, Param.class);
        List<Integer> contractIds = Lists.newArrayList();
        if (Objects.nonNull(parameter.getParamObj()) && CollectionUtils.isNotEmpty(parameter.getParamObj().getContractIds())) {
            contractIds.addAll(parameter.getParamObj().getContractIds());
        }

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {

            List<ContractDto> contractDtos = iContractQueryService.queryContractList(QueryContractParam.builder()
                    // TODO 为兼容线上流程，代码全量发布后可删除【RECEIVABLE】状态的判断
                    .busStatus(Lists.newArrayList(ContractBusStatus.EXECUTED.getCode(), ContractBusStatus.RECEIVABLE.getCode()))
                    .auditStatus(Lists.newArrayList(ContractAuditStatus.SUCCESS.getCode()))
                    .contractTypes(Lists.newArrayList(ContractType.OUTER_MARKET.getCode(), ContractType.GENERAL.getCode()))
                    .contractIds(contractIds)
                    .build());
            for (ContractDto contractDto : contractDtos) {
                iContractStatusSync.deductComplete(contractDto);
            }
        });
        return ReturnT.SUCCESS;
    }

    @Data
    static class Param {
        private List<Integer> contractIds;
    }
}
