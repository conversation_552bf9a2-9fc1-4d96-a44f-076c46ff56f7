package com.bilibili.crm.platform.job.contract;

import com.bilibili.crm.platform.biz.service.contract.component.ContractBiiPeriodAlertComponent;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-06-10 21:06:05
 * @description:
 **/
@Deprecated
@Component
@JobHandler("ContractPayAlertJob")
@Slf4j
public class ContractBillPeriodPayAlertJob extends IJobHandler {

    @Resource
    private ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {
                contractBiiPeriodAlertComponent.alertSale(new Timestamp(System.currentTimeMillis()));
            } catch (Exception e) {
                log.error("contractPayAlertJob error", e);
            }
        });
        return ReturnT.SUCCESS;
    }
}
