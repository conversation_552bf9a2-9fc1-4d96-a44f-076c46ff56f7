package com.bilibili.crm.platform.job.bsi;

import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-02-21 21:14:18
 * @description:
 **/

@Component
@JobHandler("BsiQuarterAmountRefreshJob")
@Slf4j
public class BsiQuarterAmountRefreshJob extends IJobHandler {

    @Resource
    private IBsiOpportunityService bsiOpportunityService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>BsiQuarterAmountRefreshJob.start.executing...");
        try {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> bsiOpportunityService.updateAllAmount());
        } catch (Exception e) {
            log.error("BsiQuarterAmountRefreshJob.error", e);
        }
        log.info("=====>>>>>>>>>>>>>>>BsiQuarterAmountRefreshJob.end.executing.");
        return ReturnT.SUCCESS;
    }
}
