package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.config.ISystemConfigService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2018/7/31 17:12
 */
@Component
@JobHandler("MailNoticeJob")
@Slf4j
public class MailNoticeJob extends IJobHandler {

    @Autowired
    private ISystemConfigService systemConfigService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("=====>>>>>>>>>>>>>>>MailNoticeJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> systemConfigService.sendNoticeMail());
        log.info("=====>>>>>>>>>>>>>>>MailNoticeJob end executing.");
        return ReturnT.SUCCESS;
    }
}
