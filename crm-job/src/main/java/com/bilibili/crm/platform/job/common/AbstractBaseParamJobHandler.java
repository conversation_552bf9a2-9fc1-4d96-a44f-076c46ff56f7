package com.bilibili.crm.platform.job.common;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.lock.RedisLock;
import com.bilibili.crm.platform.biz.config.OTTraceId;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.log.XxlJobLogger;
import io.opentelemetry.api.OpenTelemetry;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.redisson.api.RLock;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;

import java.util.function.Supplier;

@Slf4j
public abstract class AbstractBaseParamJobHandler<T> extends IJobHandler {

    @SuppressWarnings("all")
    @Autowired
    private OpenTelemetry openTelemetry;

    @Autowired
    private PaladinConfig paladinConfig;

    @Autowired
    private CrmCacheManager crmCacheManager;

    @Autowired
    private RedisLock redisLock;

    private final Class<T> cls;

    private final Supplier<T> defaultParam;

    private final ThreadLocal<String> traceIdLocal = new ThreadLocal<>();

    @Data
    public static class Empty {
        private String payload;
    }

    public AbstractBaseParamJobHandler(Class<T> cls, Supplier<T> defaultParam) {
        this.cls = cls;
        this.defaultParam = defaultParam;
    }

    @Override
    public ReturnT<String> execute(String param) {
        String jobName = getJobName();
        boolean success = false;
        // 使span生效

        JSONObject msg = new JSONObject();
        try {
            msg.putAll(MDC.getCopyOfContextMap());
        } catch (Exception e) {
            msg.put("MdcMiss", e.getMessage());
        }
        OTTraceId otTraceId = new OTTraceId(openTelemetry);
        OTTraceId.Ctx ctx = otTraceId.getCtx("JOB", this.getJobName());
        try {
//            ctx.initReqTraceIdAndReqUri();
            traceIdLocal.set(ctx.getTraceId());
            msg.put("log.trace_id", ctx.getTraceId());
            AlarmHelper.log("Job开始执行", jobName, param);
            XxlJobLogger.log("Job开始执行 {}", param);
            XxlJobLogger.log(msg.toJSONString());
            if (this instanceof AbstractBaseJobHandler) {
                Empty p = new Empty();
                p.setPayload(param);
                AlarmHelper.log("解析到的JOB参数", jobName, p);
                XxlJobLogger.log("解析到的JOB参数 {}", JSONObject.toJSONString(p));
                success = this.biz((T) p);
            } else {
                T paramCls = StringUtils.isBlank(param) ? defaultParam() : JSONObject.parseObject(param, cls);
                AlarmHelper.log("解析到的JOB参数", jobName, paramCls);
                XxlJobLogger.log("解析到的JOB参数 {}", JSONObject.toJSONString(paramCls));
                success = biz(paramCls);
            }
        } catch (Exception e) {
            AlarmHelper.alarmEx("JobErr", e, jobName);
        } finally {
            traceIdLocal.remove();
            // Job结束时必须要调用span的end方法！！！
            AlarmHelper.log("job执行结束", jobName);
            otTraceId.close(ctx);
        }
        return new ReturnT<>(success ? 200 : 500, msg.toJSONString());
    }

    protected String getJobName() {
        return this.getClass().getSimpleName();
    }

    protected abstract boolean biz(T param);

    protected T defaultParam() {
        return defaultParam.get();
    }

    public String getTraceId() {
        return traceIdLocal.get();
    }
}
