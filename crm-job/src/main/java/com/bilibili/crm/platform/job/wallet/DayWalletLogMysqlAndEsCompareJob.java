package com.bilibili.crm.platform.job.wallet;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.http.utils.OkHttpUtils;
import com.bilibili.crm.biz.wallet.config.AccountWalletSalesDayNewEsConfig;
import com.bilibili.crm.biz.wallet.config.WalletLogEsConfig;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.OlapConfig;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.SafeStopWatch;
import com.bilibili.crm.platform.api.finance.enums.YesOrNoEnum;
import com.bilibili.crm.platform.biz.crm.wallet.dao.write.AccAccountWalletLogSplitDao;
import com.bilibili.crm.platform.biz.service.wallet.AccAccountWalletLogSplitService;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.bilibili.crm.platform.core.Parameter;
import com.bilibili.mall.kraken.boot.autoconfigure.config.DynamicValue;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.temporal.ChronoUnit;
import java.util.Collections;
import java.util.Objects;
import java.util.Optional;

/**
 * wallet log数据比对任务
 *
 * <AUTHOR>
 * date 2024/12/26 17:14.
 * Contact: <EMAIL>.
 */
@Component
@JobHandler("dayWalletLogMysqlAndEsCompareJob")
@Slf4j
public class DayWalletLogMysqlAndEsCompareJob extends IJobHandler {
    @Autowired
    private AccAccountWalletLogSplitService accAccountWalletLogSplitService;
    @Autowired
    private AccAccountWalletLogSplitDao accAccountWalletLogSplitDao;
    @Autowired
    private WalletLogEsConfig walletLogEsConfig;
    @Autowired
    private AccountWalletSalesDayNewEsConfig accountWalletSalesDayNewEsConfig;
    @Autowired
    private PaladinConfig paladinConfig;
    @Autowired
    private WxRobotService wxRobotService;


    @DynamicValue
    @Value("${compare.qw.notice:0}")
    private Integer flag;

    //企业微信机器人地址 crm钱包日志机器人
    private static final String QW_ROBOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e35bd6d2-986c-48d3-b8f2-bba654221472";
    //private static final String QW_ROBOT_URL = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=********-7d27-483c-a4b4-eea41f8ff815";

    //后门调用的时候，转义一下
    //com.bilibili.crm.platform.job.wallet.DayWalletLogMysqlAndEsCompareJob#execute("{\"params\":{\"date\":\"2025-04-03\"}}")
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Parameter<Param> parameter = Parameter.getParameter(param, Param.class);

        //有参数，比对指定日期当天数据
        if (Objects.nonNull(parameter.getParamObj())) {
            String dateString = parameter.getParamObj().getDate();
            Timestamp date = Utils.getTimestamp(dateString);
            compareDay(date, true);
            return ReturnT.SUCCESS;
        }
        // 0点时刻 ，比对昨日数据
        //非0点时刻，比对所在天截止到前一个小时的数据 比对wallet_log mysql es
        int hour = Utils.getNow().toLocalDateTime().getHour();
        if (hour == 0) {
            Timestamp yesterday = Utils.getYesteday();
            compareDay(yesterday, false);
            return ReturnT.SUCCESS;
        } else {
            Timestamp today = Utils.getToday();
            sendDiffByHour(today, hour);
            return ReturnT.SUCCESS;
        }
    }

    /**
     * 天比对
     *
     * @param time
     * @param haveParam 手动执行标志
     */
    public void compareDay(Timestamp time, boolean haveParam) {
        long mysqlSum = getSumCashByMysql(time);
        long esLogCtimeSum = getSumCashByLogCtimeEs(time);
        long esLogDateSum = getSumCashByLogDateEs(time);
        long esSaleDateSum = getSumCashBySaleDateEs(time);
        long diffWalletLogSum = Math.abs(mysqlSum - esLogCtimeSum); //wallet log mysql和es的差值
        long diffLaunchSum = Math.abs(esLogDateSum - esSaleDateSum); // wallet_log 和sale_day的差值

        log.info("compareDay day {} ,mysqlSum {}, esLogCtimeSum {}, esLogDateSum {}, esSaleDateSum {}, diffWalletLogSum {}, diffLaunchSum {}",
                time, mysqlSum, esLogCtimeSum, esLogDateSum, esSaleDateSum, diffWalletLogSum, diffLaunchSum);

        if (diffWalletLogSum > 100L || diffLaunchSum > 600000L || haveParam) {
            log.info("mysql_es_wallet_log_data_have_error,diffCtime={},diffDate={}", diffWalletLogSum, diffLaunchSum);
            String text = String.format("钱包日志表消耗现金对账,ctime时间=%s,mysql计算总消耗=%s分,logEs计算总消耗=%s分,diff=%s分(原则上除es同步延迟ctime的维度不能出现diff).\n" +
                            " date时间=%s,logEs计算总消耗=%s分,saleByDayEs计算总消耗=%s分,diff=%s分(由于延迟扣费和es执行延迟date的维度暂定阈值6000元)",
                    Utils.getTimestamp2String(time), mysqlSum, esLogCtimeSum, diffWalletLogSum,
                    Utils.getTimestamp2String(time), esLogDateSum, esSaleDateSum, diffLaunchSum);
            log.info(text);
            WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                    .msgtype(MsgType.TEXT.getType())
                    .text(new Text(text))
                    .build();
            wxRobotService.sendWeChatWork(wxRobotMsg, QW_ROBOT_URL);
        }
        if (diffWalletLogSum > 100L || haveParam) {
            sendDiffByHour(time, 24);
        }
    }

    public void sendDiffByHour(Timestamp time, Integer hour) {
        Timestamp beginCtime = Utils.getBeginOfDay(time);
        String tableName = accAccountWalletLogSplitService.buildTableName(time);
        StringBuffer text = new StringBuffer("钱包日志表和ES消耗现金小时对账,ctime时间=").append(Utils.getTimestamp2String(time)).append(".\n");
        StringBuffer text2 = new StringBuffer("钱包日志表和ES消耗现金小时对账小时区别详情,ctime时间=").append(Utils.getTimestamp2String(time)).append(".\n");

        boolean isSend = false;
        for (int i = 1; i <= hour; i++) {
            Timestamp begin = Utils.getSomeHourAfter(beginCtime, i - 1);
            Timestamp end = Utils.getSomeHourAfter(beginCtime, i);
            SafeStopWatch watch = new SafeStopWatch("sendDiffByHour" + i);
            watch.pushTask("sendDiffByHourWithDb" + i);
            Long mysqlSum = Optional.ofNullable(getSumCashByMysqlWithHour(begin, end, tableName)).orElse(0L);
            watch.pushTask("sendDiffByHourWithEs" + i);
            Long mysqlEs = Optional.ofNullable(getSumCashByLogCtimeEsWithHour(begin, end)).orElse(0L);
            watch.log();

            Long diff = Math.abs(mysqlSum - mysqlEs);
            log.info("sendDiffByHour day {} ,hour {}, db {}, es {}, diff {}", time, i, mysqlSum, mysqlEs, diff);
            text.append("小时=").append(Utils.getTimestamp2StringBySecond(begin)).append(",mysql计算总消耗=").append(mysqlSum).append(" es计算总消耗=").append(mysqlEs).append(",diff=").append(diff).append(".\n");
            if (diff > 0 || 0 == mysqlSum || 0 == mysqlEs) {
                Long minId = accAccountWalletLogSplitDao.getMinIdByCTime(begin, tableName);
                Long maxId = accAccountWalletLogSplitDao.getMinIdByCTime(end, tableName) - 1;
                text2.append("小时=").append(Utils.getTimestamp2StringBySecond(begin)).append(",mysql的ID最小值=").append(minId)
                        .append(" mysql的ID最大值=").append(maxId).append(",diff=").append(diff).append(".\n");
                isSend = true;
            }
        }
        text2.append("根据上面的最大最小id去es同步平台重新同步(每天凌晨，数据任务集中处理,最好需要在2点之前完成，相关job有:AccountWalletSaleDay2ESJob,业绩宽表,数仓数据..)," +
                "地址: https://cloud.bilibili.co/elasticsearch/datadetails?&datasource=MySQL&appname=acc-account-wallet-log&version=v2&appid=280&index_version_id=635&index_conf_id=1406&flag=true#sh/sh001/prod");
        log.info(text.toString());
        log.info(text2.toString());

        if (isSend || YesOrNoEnum.YES.getCode().equals(flag)) {
            WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                    .msgtype(MsgType.TEXT.getType())
                    .text(new Text(text.toString()))
                    .build();
            wxRobotService.sendWeChatWork(wxRobotMsg, QW_ROBOT_URL);

            WxRobotMsg wxRobotMsg2 = WxRobotMsg.builder()
                    .msgtype(MsgType.TEXT.getType())
                    .text(new Text(text2.toString()))
                    .build();
            wxRobotService.sendWeChatWork(wxRobotMsg2, QW_ROBOT_URL);
        }
    }

    public Long getSumCashByMysql(Timestamp ctime) {
        String tableName = accAccountWalletLogSplitService.buildTableName(ctime);
        Long resultSum = 0L;
        Timestamp beginCtime = Utils.getBeginOfDay(ctime);
        for (int i = 1; i <= 24; i++) {
            resultSum += getSumCashByMysqlWithHour(Utils.getSomeHourAfter(beginCtime, i - 1), Utils.getSomeHourAfter(beginCtime, i), tableName);
        }
        log.info("getSumCashByMysql={}", resultSum);
        return resultSum;
    }

    public Long getSumCashByMysqlWithHour(Timestamp beginTime, Timestamp endTIme, String tableName) {
        return accAccountWalletLogSplitDao.getSumCashByCTime(beginTime, endTIme, tableName);
    }

    public Long getSumCashByLogCtimeEs(Timestamp ctime) {
        String indexes = walletLogEsConfig.getIndexes(Collections.singletonList(ctime));
        String token = walletLogEsConfig.getToken();
        String url = String.format(paladinConfig.getString(HotKey.olapEsHttpUrl), indexes, OlapConfig.COMMAND_SEARCH);
        String res = OkHttpUtils.bodyPost(url)
                .header(OlapConfig.AUTH_TOKEN_KEY, token)
                .header(OlapConfig.OLAP_SERVER_TIME_OUT_KEY, OlapConfig.OLAP_SERVER_TIME_OUT_VALUE)
                .json(getByDslWithLogCtime(ctime)).callForString();
        return JSONObject.parseObject(res).getJSONObject("aggregations").getJSONObject("total_cash").getLong("value");
    }

    public Long getSumCashByLogCtimeEsWithHour(Timestamp beginTime, Timestamp endTime) {
        String indexes = walletLogEsConfig.getIndexes(Collections.singletonList(beginTime));
        String token = walletLogEsConfig.getToken();
        String url = String.format(paladinConfig.getString(HotKey.olapEsHttpUrl), indexes, OlapConfig.COMMAND_SEARCH);
        String res = OkHttpUtils.bodyPost(url)
                .header(OlapConfig.AUTH_TOKEN_KEY, token)
                .header(OlapConfig.OLAP_SERVER_TIME_OUT_KEY, OlapConfig.OLAP_SERVER_TIME_OUT_VALUE)
                .json(getByDslWithLogCtimeWithHour(beginTime, endTime)).callForString();
        return JSONObject.parseObject(res).getJSONObject("aggregations").getJSONObject("total_cash").getLong("value");
    }

    public Long getSumCashByLogDateEs(Timestamp date) {
        String indexes = walletLogEsConfig.getIndexes(Collections.singletonList(date));
        String token = walletLogEsConfig.getToken();
        String url = String.format(paladinConfig.getString(HotKey.olapEsHttpUrl), indexes, OlapConfig.COMMAND_SEARCH);
        String res = OkHttpUtils.bodyPost(url)
                .header(OlapConfig.AUTH_TOKEN_KEY, token)
                .header(OlapConfig.OLAP_SERVER_TIME_OUT_KEY, OlapConfig.OLAP_SERVER_TIME_OUT_VALUE)
                .json(getByDslWithLogDate(date)).callForString();
        return JSONObject.parseObject(res).getJSONObject("aggregations").getJSONObject("total_cash").getLong("value");
    }

    public Long getSumCashBySaleDateEs(Timestamp date) {
        String indexes = accountWalletSalesDayNewEsConfig.getIndexes(Collections.singletonList(date));
        String token = accountWalletSalesDayNewEsConfig.getToken();
        String url = String.format(paladinConfig.getString(HotKey.olapEsHttpUrl), indexes, OlapConfig.COMMAND_SEARCH);
        String res = OkHttpUtils.bodyPost(url)
                .header(OlapConfig.AUTH_TOKEN_KEY, token)
                .header(OlapConfig.OLAP_SERVER_TIME_OUT_KEY, OlapConfig.OLAP_SERVER_TIME_OUT_VALUE)
                .json(getByDslWithSaleDate(date)).callForString();
        return JSONObject.parseObject(res).getJSONObject("aggregations").getJSONObject("total_cash").getLong("value");
    }

    public String getByDslWithLogCtime(Timestamp ctime) {
        return "{\"query\": {\"bool\": {\"must\": [{\"range\": {\"ctime\": {\"gte\": \"" + Utils.getTimestamp2StringBySecond(Utils.getBeginOfDay(ctime)) + "\", \"lte\": \"" + Utils.getTimestamp2StringBySecond(Utils.getEndOfDay(ctime)) + "\"}}}, {\"term\": {\"operation_type\": 0}}]}}, \"aggs\": {\"total_cash\": {\"sum\": {\"field\": \"cash\"}}}, \"size\": 0}";
    }

    public String getByDslWithLogCtimeWithHour(Timestamp beginTime, Timestamp endTime) {
        return "{\"query\": {\"bool\": {\"must\": [{\"range\": {\"ctime\": {\"gte\": \"" + Utils.getTimestamp2StringBySecond(beginTime) + "\", \"lt\": \"" + Utils.getTimestamp2StringBySecond(endTime) + "\"}}}, {\"term\": {\"operation_type\": 0}}]}}, \"aggs\": {\"total_cash\": {\"sum\": {\"field\": \"cash\"}}}, \"size\": 0}";
    }

    public String getByDslWithLogDate(Timestamp date) {
        return "{\"query\": {\"bool\": {\"must\": [{\"term\": {\"date\": \"" + Utils.getTimestamp2String(date) + "\"}}, {\"range\": {\"ctime\": {\"lte\": \"" + Utils.getTimestamp2StringBySecond(getPreviousHalfHour(Utils.getNow())) + "\"}}}, {\"term\": {\"operation_type\": 0}}]}}, \"aggs\": {\"total_cash\": {\"sum\": {\"field\": \"cash\"}}}, \"size\": 0}";
    }

    public String getByDslWithSaleDate(Timestamp date) {
        return "{\"query\": {\"bool\": {\"must\": [{\"term\": {\"timestamp\": \"" + Utils.getTimestamp2String(date) + "\"}}]}}, \"aggs\": {\"total_cash\": {\"sum\": {\"field\": \"cashConsume\"}}}, \"size\": 0}\n";
    }

    public static Timestamp getPreviousHalfHour(Timestamp time) {
        LocalDateTime localDateTime = time.toLocalDateTime();
        int minutes = localDateTime.getMinute();
        int minutesToSubtract = minutes % 30;
        LocalDateTime roundedTime = localDateTime.minusMinutes(minutesToSubtract).truncatedTo(ChronoUnit.MINUTES);
        return Timestamp.valueOf(roundedTime);
    }


    /**
     * 任务入参
     * eg, {"params":{"date":"2025-04-03"}}
     */
    @Data
    static class Param {
        private String date;
    }
}
