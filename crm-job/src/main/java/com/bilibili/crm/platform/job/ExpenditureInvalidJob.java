package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.expenditure.service.IExpenditureAuditService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @time 2019/10/28 19:54
 */
@Component
@JobHandler("ExpenditureInvalidJob")
@Slf4j
public class ExpenditureInvalidJob extends IJobHandler {

    @Autowired
    private IExpenditureAuditService expenditureAuditService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> expenditureAuditService.expenditureInvalidSchedule());
        return ReturnT.SUCCESS;
    }
}
