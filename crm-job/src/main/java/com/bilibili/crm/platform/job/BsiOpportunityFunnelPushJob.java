package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityIntentProductService;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.api.bsiopportunity.dto.*;
import com.bilibili.crm.platform.api.finance.enums.SaleTypeEnum;
import com.bilibili.crm.platform.api.oa.dto.OaCoreUserInfo;
import com.bilibili.crm.platform.api.oa.service.IOaUserInfoService;
import com.bilibili.crm.platform.api.sale.dto.QuerySaleDto;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.api.white.IWxWriteListService;
import com.bilibili.crm.platform.api.white.dto.QueryWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteType;
import com.bilibili.crm.platform.app.api.client.IWxAppClient;
import com.bilibili.crm.platform.app.api.client.dto.response.WxAppMessageResponse;
import com.bilibili.crm.platform.app.api.client.dto.response.WxAppUserInfo;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.config.WxAppCacheConfig;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.common.FollowStage;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.service.income.util.IncomeUtil;
import com.bilibili.crm.platform.biz.service.income_sale.manager.WxAppSaleHelper;
import com.bilibili.crm.platform.biz.service.policy.component.user.configable.IConfigUserQuerier;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.wx.dto.EnterpriseWeChatAgentType;
import com.bilibili.crm.platform.common.IsAble;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.rbac.api.dto.UserBaseDto;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.math.BigDecimal;
import java.sql.Timestamp;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * <AUTHOR>
 * @Description
 * @date 11/1/23
 **/
@Component
@JobHandler("bsiOpportunityFunnelPushJob")
@Slf4j
public class BsiOpportunityFunnelPushJob extends IJobHandler {

    @Autowired
    private IWxWriteListService wxWriteListService;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private IWxAppClient wxAppClient;
    @Autowired
    private IBsiOpportunityService bsiOpportunityService;
    @Autowired
    private IBsiOpportunityIntentProductService bsiOpportunityIntentProductService;
    @Autowired
    private WxAppSaleHelper wxAppSaleHelper;
    @Autowired
    private ISaleService saleService;
    @Autowired
    private ISaleGroupService iSaleGroupService;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private IncomeUtil incomeUtil;
    @Autowired
    private IConfigUserQuerier configUserQuerier;
    @Autowired
    private IOaUserInfoService oaUserInfoService;

    @Value("${bsi.opportunity.funnel.push.job:0}")
    private Integer PUSH_ABLE;
    @Value("${bsi.opportunity.funnel.push.team.id:761}")
    private Integer PUSH_TEAM;

    /**
     * @param s
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {

        Timestamp day = Utils.getYesteday();
        if (!StringUtils.hasText(s) && pushCondition(day)) {
            Cat.logEvent("WxAppPushJob_DATA_HAD_DOWN", day.toString());
            return ReturnT.SUCCESS;
        }

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {

                //权限组 ID761 推送
                List<UserBaseDto> userBaseDtos = configUserQuerier.queryByRoleId(PUSH_TEAM);
                List<String> userNames = userBaseDtos.stream().map(UserBaseDto::getUsername).distinct().collect(Collectors.toList());
                List<OaCoreUserInfo> oaUserInfo = oaUserInfoService.getOaCoreUserInfos(userNames);
                List<String> userList = oaUserInfo.stream().map(OaCoreUserInfo::getWorkCode).distinct().collect(Collectors.toList());

                PageResult<BsiOpportunityDto> pageBsiAllRecords = bsiOpportunityService.queryBsiOpportunityListForFunnel(BsiOpportunityQueryDto.builder()
                        .expectLaunchMinDate(Utils.getBeginOfDay(incomeTimeUtil.getQuarterFirstDate(day)))
                        .expectLaunchMaxDate(Utils.getEndOfDay(incomeTimeUtil.getQuarterEndDate(day)))
                        .build());
                List<BsiOpportunityDto> bsiAllRecords = pageBsiAllRecords.getRecords();

                BsiOppPushInfo bsiOppPushInfo = buildBsiOppPushInfo(bsiAllRecords);
                log.info("userList {} bsiOppPushInfo {}", userList, bsiOppPushInfo);
                if (Utils.isPositive(PUSH_ABLE)) {
                    dailySummaryPush(userList, bsiOppPushInfo.getCount(), bsiOppPushInfo.getTotalExpectLaunchAmount());
                }

            } catch (WxAppException e) {
                throw new RuntimeException(e);
            }
        });

        //销售推送
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {
                List<WxAppWhiteDto> wxAppWhiteDtos = wxWriteListService.queryWhite(QueryWxAppWhiteDto.builder()
                        .whiteType(WxAppWhiteType.SALE.getCode())
                        .isPush(IsValid.TRUE.getCode())
                        .ctimeEnd(Utils.getEndOfDay(day))
                        .whiteStatus(0)
                        .build());
                //正常销售
                List<String> normalSale = wxAppSaleHelper.getNormalSaleEmail();

                //过滤推送人员，管理员推，销售状态正常推
                List<WxAppWhiteDto> pushList = wxAppWhiteDtos.stream().filter(item -> {
                    if (IsValid.TRUE.getCode().equals(item.getIsManager())) {
                        return true;
                    }
                    if (normalSale.contains(item.getUserName())) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                //消息推送
                for (WxAppWhiteDto wxAppWhiteDto : pushList) {

                    BsiOpportunityQueryDto dto = BsiOpportunityQueryDto.builder()
                            .expectLaunchMinDate(Utils.getBeginOfDay(incomeTimeUtil.getQuarterFirstDate(day)))
                            .expectLaunchMaxDate(Utils.getEndOfDay(incomeTimeUtil.getQuarterEndDate(day)))
                            .build();

                    //判断用户是否是销售角色
                    List<SaleDto> sales = saleService.getListByQueryDto(QuerySaleDto.builder().emails(Lists.newArrayList(wxAppWhiteDto.getUserName())).status(IsAble.NORMAL.getCode()).build());
                    if (org.apache.commons.collections.CollectionUtils.isNotEmpty(sales)) {
                        SaleDto thisSale = sales.get(0);
                        dto.setSaleOperator(wxAppWhiteDto.getUserName());
                        List<SaleDto> childSales = iSaleGroupService.getGroupMembersNew(thisSale, thisSale.getLevel());
                        List<Integer> directSales = Stream.concat(childSales.stream().map(SaleDto::getId).collect(Collectors.toList()).stream(),
                                Lists.newArrayList(thisSale.getId()).stream()).distinct().collect(Collectors.toList());
                        if (SaleTypeEnum.directSaleTypes.contains(thisSale.getType())) {//直客销售 商机归属直客销售是自己及下属的商机
                            dto.setDirectSales(directSales);
                        } else if (SaleTypeEnum.channelSaleTypes.contains(thisSale.getType())) {//渠道销售 商机归属渠道销售是自己及下属的商机
                            dto.setChannelSales(directSales);
                        } else {
                            log.info("销售类型不合适(非1234).");
                            continue;
                        }
                    }

                    PageResult<BsiOpportunityDto> pageBsiAllRecords = bsiOpportunityService.queryBsiOpportunityListForFunnel(dto);
                    List<BsiOpportunityDto> bsiAllRecords = pageBsiAllRecords.getRecords();
                    BsiOppPushInfo bsiOppPushInfo = buildBsiOppPushInfo(bsiAllRecords);

                    log.info("userList {} bsiOppPushInfo {}", wxAppWhiteDto.getUserName(), bsiOppPushInfo);
                    if (Utils.isPositive(PUSH_ABLE)) {
                        dailySummaryPush(Lists.newArrayList(wxAppWhiteDto.getUserName()), bsiOppPushInfo.getCount(), bsiOppPushInfo.getTotalExpectLaunchAmount());
                    }

                }
            } catch (Exception e) {
                throw new RuntimeException(e);
            }
        });
        //设置日报推送状态位
        wxAppCacheManager.setWxAbilityStatus(day, WxAppCacheConfig.WX_APP_BIS_OPPORTUNITY_PUSH_JOB_STATUS);

        return ReturnT.SUCCESS;
    }

    private BsiOppPushInfo buildBsiOppPushInfo(List<BsiOpportunityDto> bsiAllRecords) {
        BigDecimal totalExpectLaunchAmount = BigDecimal.ZERO;
        //本季度和其他季度商机预计投放金额-有效商机（10%-90%阶段的商机）的全部一级意向产品本季度或者其他季度预估金额之和
        List<BsiOpportunityDto> filterEffectiveBsiDtoList = bsiAllRecords.stream()
                .filter(e -> FollowStage.getTenAndNinetyStatus().contains(e.getFollowStage()))
                .collect(Collectors.toList());
        if (!CollectionUtils.isEmpty(filterEffectiveBsiDtoList)) {
            List<BsiOpportunityIntentProductMappingDto> intentProductMappingDtoList = bsiOpportunityIntentProductService.getBsiIntentProductMapping(
                    BsiIntentProductQueryDto.builder().bsiOpportunityIds(filterEffectiveBsiDtoList.stream().map(BsiOpportunityDto::getId).distinct().collect(Collectors.toList())).build());
            if (!CollectionUtils.isEmpty(intentProductMappingDtoList)) {
                Map<Integer, BsiOpportunityDto> filterEffectiveBsiDtoMap = filterEffectiveBsiDtoList.stream().collect(Collectors.toMap(BsiOpportunityDto::getId, Function.identity()));

                Set<Integer> bsiIdList = filterEffectiveBsiDtoMap.keySet();
                intentProductMappingDtoList = intentProductMappingDtoList.stream().filter(e -> bsiIdList.contains(e.getBsiOpportunityId())).collect(Collectors.toList());
                List<BsiOpportunityIntentProductDto> filterBsiOpportunityIntentProductDtoList = intentProductMappingDtoList.stream()
                        .map(item -> bsiOpportunityService.calculateQuarterAmount(filterEffectiveBsiDtoMap.get(item.getBsiOpportunityId()), BsiOpportunityIntentProductDto.builder()
                                .totalAmount(item.getTotalAmount())
                                .quarterAmount(item.getQuarterAmount())
                                .otherQuarterAmount(item.getOtherQuarterAmount())
                                .entryTime(item.getCtime())
                                .isQuarterUnpack(item.getUnpackQuarter())
                                .build())).collect(Collectors.toList());

                totalExpectLaunchAmount = Utils.fromFenToYuan(filterBsiOpportunityIntentProductDtoList.stream()
                        .map(BsiOpportunityIntentProductDto::getQuarterAmount)
                        .filter(Objects::nonNull)
                        .reduce(Long::sum).orElse(0L));

            }
        }

        Long count = bsiAllRecords.stream()
                .filter(e -> FollowStage.getTenAndNinetyStatus().contains(e.getFollowStage())).count();

        BsiOppPushInfo bsiOppPushInfo = new BsiOppPushInfo();
        bsiOppPushInfo.setCount(count);
        bsiOppPushInfo.setTotalExpectLaunchAmount(incomeUtil.divide(totalExpectLaunchAmount, new BigDecimal(10000), 2));
        return bsiOppPushInfo;
    }

    private void dailySummaryPush(List<String> userList, Long count, BigDecimal totalExpectLaunchAmount) throws WxAppException {

        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        userList.forEach(userId -> {
            try {
                WxAppUserInfo userInfo = wxAppClient.getUserInfo(userId);

                String alias = userInfo.getAlias();
                String title = "商机漏斗看板";
                String welcome = "<div class=\"normal\">" + alias + " 您好</div>";
                String content = "<div class=\"highlight\">跟进中的有效商机" + count + "条，当前季度预估成单金额" + totalExpectLaunchAmount + "万，点击可查看更多维度详情。</div>";
                String description = welcome + content;

                String url = "https://cm.bilibili.com/ldad/opportunity/main.html#/filter";

                WxAppMessageResponse response = wxAppClient.sendTextCard(EnterpriseWeChatAgentType.CRM_DATA_AGENT.getCode(), Lists.newArrayList(userId), title, description, url);

                log.info("WxPush user {} ,response {}", alias, JSON.toJSONString(response));

                String invalidUserList = response.getInvalidUser();
                if (!Strings.isNullOrEmpty(invalidUserList)) {
                    log.info("WxPush invalid user {} ,response {}", alias, JSON.toJSONString(response));
                    Cat.logEvent("WX_APP_PUSH_FAIL", "dailyPush", "failed", invalidUserList);
                }

                try {
                    Thread.sleep(500L);
                } catch (InterruptedException e) {
                    log.info("WxPush InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }

            } catch (WxAppException e) {
                Cat.logError("WxAppPaperServiceImp.dailyPush error userId is" + userId, e);
            }
        });


    }


    private boolean pushCondition(Timestamp day) {
        Integer status = wxAppCacheManager.getWxAbilityStatus(day, WxAppCacheConfig.WX_APP_BIS_OPPORTUNITY_PUSH_JOB_STATUS);

        if (IsValid.TRUE.getCode().equals(status)) {
            return true;
        }
        return false;
    }


    private static class BsiOppPushInfo {

        private Long count;
        //万元
        private BigDecimal totalExpectLaunchAmount;

        public Long getCount() {
            return count;
        }

        public void setCount(Long count) {
            this.count = count;
        }

        public BigDecimal getTotalExpectLaunchAmount() {
            return totalExpectLaunchAmount;
        }

        public void setTotalExpectLaunchAmount(BigDecimal totalExpectLaunchAmount) {
            this.totalExpectLaunchAmount = totalExpectLaunchAmount;
        }

        @Override
        public String toString() {
            return "BsiOppPushInfo{" +
                    "count=" + count +
                    ", totalExpectLaunchAmount=" + totalExpectLaunchAmount +
                    '}';
        }
    }
}
