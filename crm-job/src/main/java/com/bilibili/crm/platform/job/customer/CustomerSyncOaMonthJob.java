package com.bilibili.crm.platform.job.customer;

import com.bilibili.crm.platform.api.customer.dto.CustomerInfoDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.oa.service.IOaService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.time.*;
import java.time.format.DateTimeFormatter;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 *
 */
@Slf4j
@Component
@JobHandler("customerSyncOaMonthJob")
public class CustomerSyncOaMonthJob extends IJobHandler {
    private static final String ALL = "all";
    private static final int DEFAULT_START_CUSTOMER_ID = 0;
    private static final int LIMIT_SIZE = 1000;
    private static final DateTimeFormatter DATETIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    @Autowired
    private ICustomerQueryService customerQueryService;
    @Autowired
    private IOaService oaService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        ZonedDateTime now = ZonedDateTime.now(Clock.systemDefaultZone());

        if (ALL.equals(s)) {
            log.info("start to sync all valid external companies to oa，start date time: {}", now);
            syncAllCustomers();
            log.info("finish to sync all valid external companies to oa，end date time: {}", ZonedDateTime.now(Clock.systemDefaultZone()));
        } else if (s.startsWith("datetime/")) {
            // datetime/2024-06-10 00:00:00        time zone: Asia/Shanghai
            ZonedDateTime specifiedDateTime = LocalDateTime.parse(s.split("/")[1], DATETIME_FORMATTER).atZone(ZoneId.of("Asia/Shanghai"));
            log.info("start to sync the added valid external companies of the last month to oa，specified date time: {}, start date time: {}", specifiedDateTime, now);
            syncAddedCustomers(specifiedDateTime);
            log.info("finish to sync the added valid external companies of the last month to oa，specified date time: {}, end date time: {}", specifiedDateTime,
                    ZonedDateTime.now(Clock.systemDefaultZone()));
        } else if (s.startsWith("customerIds:")) {
            // customerIds:111,222
            List<Integer> customerIds = Arrays.stream(s.split(":")[1].split(",")).distinct().map(Integer::parseInt).collect(Collectors.toList());
            syncSpecifiedCustomers(customerIds);
        } else {
            log.info("start to sync the added valid external companies of the last month to oa，start date time: {}", now);
            syncAddedCustomers(now);
            log.info("finish to sync the added valid external companies of the last month to oa，end date time: {}", ZonedDateTime.now(Clock.systemDefaultZone()));
        }

        return ReturnT.SUCCESS;
    }

    private void syncAllCustomers() throws InterruptedException {
        int theStartCustomerId = DEFAULT_START_CUSTOMER_ID;
        List<CustomerInfoDto> customers = customerQueryService.getValidExternalCompanyCustomersByIdAndLimitSize(theStartCustomerId, LIMIT_SIZE);

        while (CollectionUtils.isNotEmpty(customers)) {
            for (CustomerInfoDto customerInfoDto : customers) {
                oaService.syncCustomerInfo(customerInfoDto);
                Thread.sleep(100L);
            }

            theStartCustomerId = customers.get(customers.size() - 1).getId() + 1;
            customers = customerQueryService.getValidExternalCompanyCustomersByIdAndLimitSize(theStartCustomerId, LIMIT_SIZE);
            Thread.sleep(100L);
        }

    }

    private void syncAddedCustomers(ZonedDateTime currentDateTime) throws InterruptedException {
        List<CustomerInfoDto> customers = customerQueryService.getAddedValidExternalCompanyCustomersOfLastMonth(currentDateTime);
        for (CustomerInfoDto customer : customers) {
            oaService.syncCustomerInfo(customer);
            Thread.sleep(100L);
        }
    }

    private void syncSpecifiedCustomers(List<Integer> customerIds) {
        List<CustomerInfoDto> customers = customerQueryService.getCustomerInfoByIds(customerIds);
        customers.forEach(customer -> oaService.syncCustomerInfo(customer));
    }
}