package com.bilibili.crm.platform.job.investment;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.investment.WxAppInvestmentWriter;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.dianping.cat.Cat;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-11-29 19:38:03
 * @description:
 **/

@Component
@JobHandler("WxAppInvestmentDataJob")
@Slf4j
public class WxAppInvestmentDataJob extends AbstractBaseJobHandler {

    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Autowired
    private WxAppCacheManager wxAppCacheManager;

    @Resource
    private WxAppInvestmentWriter wxAppInvestmentWriter;

    @Override
    protected boolean biz(String args) {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (!StringUtils.hasText(args)) {
                //检查当天状态位
                Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
                if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.INVESTMENT)) {
                    Cat.logEvent("WxAppInvestmentDataJob_DATA_HAD_DOWN", day.toString());
                    return true;
                }
            }

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);

            dayList.forEach(d -> {
                try {
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                        wxAppInvestmentWriter.cacheRequest(Utils.getEndOfDay(incomeTimeUtil.getYesterdayBegin(d)));
                    });
                    Cat.logEvent("WxAppInvestmentDataJob_SUCCESS", d.toString() + ":normal");
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(d), WxModuleType.INVESTMENT);
                } catch (Exception e) {
                    Cat.logEvent("WxAppInvestmentDataJob_FAILED", d.toString() + ":failed");
                    throw new RuntimeException(e);
                }
            });
        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppInvestmentDataJob", e.getMessage());
            throw e;
        }
        log.info("WxAppInvestmentDataJob success args {}", args);

        return true;
    }

}
