package com.bilibili.crm.platform.job.es_migrate.sycpb_new_es;

import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.migrate.ESWalletConsumeExtendNewPo;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.ESWalletConsumeExtendPo;
import com.bilibili.crm.platform.job.es_migrate.AbstractEsDataSyncJob;
import com.fasterxml.jackson.core.JsonProcessingException;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("WalletConsumeDayExtendDataSyncJob")
public class WalletConsumeDayExtendDataSyncJob extends AbstractEsDataSyncJob<ESWalletConsumeExtendPo> {

    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsMigrateMessageSender<ESWalletConsumeExtendNewPo> eSWalletConsumeExtendNewPoMessageSender;

    @Override
    protected String getIndexName() {
        return "wallet_consume_day_extend";
    }

    @Override
    protected Class<ESWalletConsumeExtendPo> getPoClass() {
        return ESWalletConsumeExtendPo.class;
    }

    @Override
    protected ElasticsearchTemplate getElasticsearchTemplate() {
        return elasticsearchTemplate;
    }

    @Override
    protected void processData(List<ESWalletConsumeExtendPo> content, String index) {
        List<ESWalletConsumeExtendNewPo> newContent = convertToNewPo(content);
        eSWalletConsumeExtendNewPoMessageSender.sendBatch(newContent, index, EsMigrateCommon.JOB);
    }

    private List<ESWalletConsumeExtendNewPo> convertToNewPo(List<ESWalletConsumeExtendPo> content) {
        return content.stream()
                .map(this::convertSinglePo)
                .collect(Collectors.toList());
    }

    private ESWalletConsumeExtendNewPo convertSinglePo(ESWalletConsumeExtendPo po) {
        ESWalletConsumeExtendNewPo newPo = new ESWalletConsumeExtendNewPo();
        BeanUtils.copyProperties(po, newPo);
        return newPo;
    }

    // 后门
    public void localTest() {
        this.syncData();
    }
    public void localTest(Timestamp begin, Timestamp end) {
        this.syncData(begin, end);
    }

}
