package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.budget.IBudgetConsumeNotifyService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

@Slf4j
@Component
@JobHandler("BudgetConsumeNotifyJob")
public class BudgetConsumeNotifyJob extends IJobHandler {

    @Autowired
    private IBudgetConsumeNotifyService budgetConsumeNotifyService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        log.info("BudgetConsumeNotifyJob start");

        Timestamp hour = Utils.getLastHourTimestamp();
        boolean needNotify = true;

        if (!StringUtils.isEmpty(param)) {
            String[] strings = param.split(",");
            hour = StringDateParser.stringToTimestamp(strings[0]);
            needNotify = Boolean.parseBoolean(strings[1]);
        }

        budgetConsumeNotifyService.processNotify(hour, needNotify);

        log.info("BudgetConsumeNotifyJob end");
        return ReturnT.SUCCESS;
    }
}
