package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.finance.service.IAccountBalanceService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * Created by fanwenbin on 2017/6/14.
 */
@Component
@JobHandler("AccountAgentBalanceJob")
@Slf4j
public class AccountAgentBalanceJob extends IJobHandler {

    @Autowired
    private IAccountBalanceService accountBalanceService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("=====>>>>>>>>>>>>>>>AccountAgentBalanceJob.start.executing...");

        try {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> accountBalanceService.saveAccountAgentBalance());



        } catch (Exception e) {
            log.error("AccountAgentBalanceJob.saveAccountAgentBalance.error", e);
        }

        log.info("=====>>>>>>>>>>>>>>>AccountAgentBalanceJob.end.executing.");
        return ReturnT.SUCCESS;
    }
}
