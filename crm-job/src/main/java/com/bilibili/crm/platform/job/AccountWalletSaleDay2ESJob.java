package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.stat.IPerformanceAdService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Calendar;

@Component
@JobHandler("AccountWalletSaleDay2ESJob")
@Slf4j
public class AccountWalletSaleDay2ESJob extends IJobHandler {

    @Autowired
    private IPerformanceAdService performanceAdService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {

            log.info("AccountWalletSaleDay2ESJob start to insert date to es");
            Long start = System.currentTimeMillis();
            Integer count = 0;
            Timestamp today = Utils.getBeginOfDay(Utils.getToday());
            Timestamp yesterday = Utils.getBeginOfDay(Utils.getSomeDayAgo(today, 1));
            Timestamp beforeYesterday = Utils.getBeginOfDay(Utils.getSomeDayAgo(today, 2));

            Calendar c = Calendar.getInstance();
            if (c.get(Calendar.HOUR_OF_DAY) == 6){
                count = performanceAdService.flushAccountWalletLog2ES(beforeYesterday);
                log.info("success to insert data in date {}, and total insert count {}, and time cost {} ms", beforeYesterday, count, (System.currentTimeMillis()-start));
            }
            start = System.currentTimeMillis();
            if (c.get(Calendar.HOUR_OF_DAY) == 2){
                count = performanceAdService.flushAccountWalletLog2ES(yesterday);
                log.info("success to insert data in date {}, and total insert count {}, and time cost {} ms", yesterday, count, (System.currentTimeMillis()-start));
            }
            start = System.currentTimeMillis();
            if (c.get(Calendar.HOUR_OF_DAY)%6 == 0){
                count = performanceAdService.flushAccountWalletLog2ES(yesterday);
                log.info("success to insert data in date {}, and total insert count {}, and time cost {} ms", yesterday, count, (System.currentTimeMillis()-start));
            }
            start = System.currentTimeMillis();
            count = performanceAdService.flushAccountWalletLog2ES(today);
            log.info("success to insert data in date {}, and total insert count {}, and time cost {} ms", today, count, (System.currentTimeMillis()-start));
        });
        return ReturnT.SUCCESS;
    }
}
