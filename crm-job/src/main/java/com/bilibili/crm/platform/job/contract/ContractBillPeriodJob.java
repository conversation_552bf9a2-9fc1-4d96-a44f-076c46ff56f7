package com.bilibili.crm.platform.job.contract;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.contract.service.ContractBillPeriodService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-05-28 22:22:52
 * @description:
 **/

@Component
@JobHandler("ContractBillPeriodJob")
@Slf4j
public class ContractBillPeriodJob extends IJobHandler {

    @Resource
    private ContractBillPeriodService contractBillPeriodService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        Timestamp begin = Utils.getToday();
        Timestamp end = Utils.getEndOfDay(Utils.getToday());
        if (StringUtils.hasText(args) && args.contains("~")) {
            String[] strings = args.split("~");
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }
        List<Timestamp> dayList = Utils.getEachDays(begin, end);

        dayList.forEach(day -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                contractBillPeriodService.buildBillPeriodByTime(day);
            });
        });

        return ReturnT.SUCCESS;
    }

}
