package com.bilibili.crm.platform.job.account.label;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.adp.mail.api.dto.MailMessage;
import com.bilibili.adp.mail.api.service.IMailService;
import com.bilibili.crm.platform.biz.crm.account.dao.write.AccAccountLabelMappingDao;
import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPo;
import com.bilibili.crm.platform.biz.po.AccAccountLabelMappingPoExample;
import com.bilibili.crm.platform.common.account.label.AccountLabelMappingType;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.<PERSON><PERSON><PERSON><PERSON>andler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Component
@JobHandler("SpecialLabelBingAccountNoticeJob")
@Slf4j
public class SpecialLabelBingAccountNoticeJob extends IJobHandler {

    @Value("#{'${need.notice.label.id:1,2,3}'.split(',')}")
    private List<Integer> needNoticeLabelId;

    @Value("${special.label.bind.account.gap.day:1}")
    private Integer specialLabelBindAccountNoticeGapDay;

    @Value("#{'${special.label.bind.notice.mail:<EMAIL>,<EMAIL>}'.split(',')}")
    private List<String> mailList;

    @Autowired
    private AccAccountLabelMappingDao accAccountLabelMappingDao;
    @Autowired
    private IMailService mailService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        if(CollectionUtils.isEmpty(needNoticeLabelId)){
            return ReturnT.SUCCESS;
        }

        if(!Utils.isPositive(specialLabelBindAccountNoticeGapDay)){
            return ReturnT.SUCCESS;
        }

        if(CollectionUtils.isEmpty(mailList)){
            return ReturnT.SUCCESS;
        }

        Timestamp ctimeBeginTime = Utils.getBeginOfDay(Utils.getSomeDayBeforeToday(specialLabelBindAccountNoticeGapDay));
        Timestamp ctimeEndTime = Utils.getEndOfDay(Utils.getSomeDayBeforeToday(specialLabelBindAccountNoticeGapDay));
        AccAccountLabelMappingPoExample example = new AccAccountLabelMappingPoExample();
        example.or().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andLabelIdIn(needNoticeLabelId)
                .andMappingTypeEqualTo(AccountLabelMappingType.MANUAL.getCode())
                .andCtimeGreaterThanOrEqualTo(ctimeBeginTime)
                .andCtimeLessThanOrEqualTo(ctimeEndTime);
        List<AccAccountLabelMappingPo> pos = accAccountLabelMappingDao.selectByExample(example);

        if(!CollectionUtils.isEmpty(pos)){
            String bindDay = Utils.getTimestamp2String(Utils.getSomeDayBeforeToday(specialLabelBindAccountNoticeGapDay));
            List<String> accountIds = pos.stream().map(po -> po.getAccountId().toString()).collect(Collectors.toList());
            String accountIdsStr = StringUtils.join(accountIds, ",");

            //xx年xx月xx日 为账号xxxxx增加251标签，距今已30天，请知晓。
            String title = "特殊标签被绑通知";
            String content = bindDay + "<br><br>" + "为账号" + accountIdsStr + "增加251标签，距今已30天，请知晓。";
            mailList.forEach(to -> {
                MailMessage mailMessage = new MailMessage();
                mailMessage.setSubject(title);
                mailMessage.setText(content);
                mailMessage.setUseHtml(true);
                mailMessage.setHasFile(false);
                mailMessage.setTos(Lists.newArrayList(to));
                try {
                    mailService.send(mailMessage);
                } catch (ServiceException e) {
                    log.error("mailService.send.notice.error", e);
                }
            });

        }
        return ReturnT.SUCCESS;
    }
}
