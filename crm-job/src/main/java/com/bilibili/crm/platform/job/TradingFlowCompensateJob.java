package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.trading.flow.service.ITradingFlowDayService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2020/10/13 18:43
 * 交易流水补偿JOB
 * 由于引擎计费存在一定的延迟
 * 每日24点之前的交易，会延迟到凌晨之后写入
 * 故设置这个补偿JOB，在T+1凌晨之后补刷这部分数据
 */
@Component
@JobHandler("TradingFlowCompensateJob")
@Slf4j
@Deprecated
public class TradingFlowCompensateJob extends IJobHandler {

    @Autowired
    private ITradingFlowDayService tradingFlowDayService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        //回刷三天
        Timestamp begin = Utils.getSomeDayBeforeToday(3);
        Timestamp end = Utils.getYesteday();
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            List<Timestamp> eachDays = Utils.getEachDays(begin, end);
            eachDays.forEach(day -> {
                tradingFlowDayService.syncWalletLog(day, null);
            });
        });

        return ReturnT.SUCCESS;
    }
}
