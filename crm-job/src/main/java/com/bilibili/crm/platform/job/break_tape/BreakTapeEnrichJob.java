package com.bilibili.crm.platform.job.break_tape;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.break_tape.IBreakTapeService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-07-22 19:45:07
 * @description:
 **/

@Slf4j
@Component
@JobHandler("BreakTapeEnrichJob")
public class BreakTapeEnrichJob extends IJobHandler {

    @Resource
    private IBreakTapeService breakTapeService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            Timestamp end = new Timestamp(System.currentTimeMillis());
            Timestamp start = Utils.getSomeMinuteAgo(end, 2);
            breakTapeService.enRichBreakTapeReceipt(start, end);
        });
        return ReturnT.SUCCESS;
    }


}
