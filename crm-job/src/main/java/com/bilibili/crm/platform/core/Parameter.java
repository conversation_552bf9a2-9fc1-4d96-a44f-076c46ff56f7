package com.bilibili.crm.platform.core;

import com.bilibili.adp.common.util.GsonUtils;
import com.google.gson.reflect.TypeToken;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

import java.util.Map;

/**
 * 任务参数格式：{"pageSize":100,"startId":0,"endId":1000,"concurrentNum":10,"rateLimiter":100,"sharding":false,"params":{"key1":"value1","key2":"value2"}}
 */
@Data
public class Parameter<T> {

    private Integer pageSize;

    private Long startId;

    private Long endId;

    private Integer concurrentNum;

    private Integer rateLimiter;

    private Boolean sharding;

    private Map<String, Object> params;

    private T paramObj;

    public static <T> Parameter<T> defaultParameter() {
        return new Parameter<>();
    }

    public static <T> Parameter<T> getParameter(String paramStr, Class<T> clazz) {
        if (StringUtils.isEmpty(paramStr)) {
            return defaultParameter();
        }

        Parameter<T> parameter = GsonUtils.getGson().fromJson(paramStr, new TypeToken<Parameter<T>>() {
        }.getType());

        T params = GsonUtils.getGson().fromJson(GsonUtils.getGson().toJson(parameter.getParams()), clazz);
        parameter.setParamObj(params);

        return parameter;
    }
}
