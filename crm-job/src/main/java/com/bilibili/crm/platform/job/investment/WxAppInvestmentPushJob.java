package com.bilibili.crm.platform.job.investment;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.project.InvestmentService;
import com.bilibili.crm.platform.api.white.IWxWriteListService;
import com.bilibili.crm.platform.api.white.dto.QueryWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteType;
import com.bilibili.crm.platform.app.api.service.IWxAppPaperService;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.config.WxAppCacheConfig;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-11-29 17:50:50
 * @description:
 **/

@Component
@JobHandler("WxAppInvestmentPushJob")
@Slf4j
public class WxAppInvestmentPushJob extends IJobHandler {

    @Autowired
    private IWxAppPaperService wxAppPaperService;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private InvestmentService investmentService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Timestamp day = Utils.getYesteday();
        if (!StringUtils.hasText(s) && pushCondition(day)) {
            Cat.logEvent("WxAppInvestmentPushJob_DATA_HAD_DOWN", day.toString());
            return ReturnT.SUCCESS;
        }

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {
                List<String> userList = investmentService.queryWhiteUserId();
                wxAppPaperService.dailyInvestmentPush(userList);
                //设置日报推送状态位
                wxAppCacheManager.setWxAbilityStatus(day, WxAppCacheConfig.WX_APP_INVESTMENT_PUSH_JOB_STATUS);

            } catch (WxAppException e) {
                throw new RuntimeException(e);
            }
        });
        return ReturnT.SUCCESS;
    }

    private boolean pushCondition(Timestamp day) {
        Integer status = wxAppCacheManager.getWxAbilityStatus(day, WxAppCacheConfig.WX_APP_INVESTMENT_PUSH_JOB_STATUS);

        if (IsValid.TRUE.getCode().equals(status)) {
            return true;
        }
        return false;
    }
}
