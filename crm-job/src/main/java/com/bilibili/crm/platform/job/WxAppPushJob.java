package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.dataprepare.service.CrmKpiCkPrepareService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.app.api.service.IWxAppPaperService;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.biz.addata.dao.write.CrmAchievementDayItemDao;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.po.addata.CrmAchievementDayItemPo;
import com.bilibili.crm.platform.biz.po.addata.CrmAchievementDayItemPoExample;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.sale.SaleKpiTypeEnum;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2020/4/2
 * 企业微信日报推送
 **/
@Component
@JobHandler("WxAppPushJob")
@Slf4j
public class WxAppPushJob extends AbstractBaseJobHandler {

    @Autowired
    private IWxAppPaperService wxAppPaperService;
    @Autowired
    private CrmCacheManager crmCacheManager;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Resource
    private CrmAchievementDayItemDao crmAchievementDayItemDao;
    @Autowired
    private WxRobotService wxRobotService;
    @Autowired
    private CrmKpiCkPrepareService crmKpiCkPrepareService;

    @Override
    protected boolean biz(String s) {
        try {
            Timestamp logDate = Utils.getYesteday();
            String logDateStr = CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD);
            Object cache1 = crmCacheManager.getValue("wg03_info_WxAppPushJob_Flag_Sale" + logDateStr);
            Object cache2 = crmCacheManager.getValue("wg03_info_WxAppPushJob_Flag_Admin" + logDateStr);
            if (Objects.nonNull(cache1) && Objects.nonNull(cache2)) {
                log.info("WxAppPushJob_Have_Cache_Success");
                return true;
            }
            Object cache3 = crmCacheManager.getValue("wg03_info_AchievementEstimateSaleSummaryJob_Flag_" + logDateStr);
            if (Objects.nonNull(cache3) && Objects.equals("2", cache3.toString()) && Objects.isNull(cache1)) {
                crmCacheManager.setValueWithTime("wg03_info_WxAppPushJob_Flag_Sale" + logDateStr, "1", TimeUnit.DAYS, 3);
            }
            if (wxAppCacheManager.isPaperStateAllReadyV2(logDate) && Objects.nonNull(cache3) && Objects.equals("2", cache3.toString()) && Objects.isNull(cache2)) {
                crmCacheManager.setValueWithTime("wg03_info_WxAppPushJob_Flag_Admin" + logDateStr, "1", TimeUnit.DAYS, 3);
            }
            if (wxAppCacheManager.isPaperStateAllReadyV2(logDate) && Objects.nonNull(cache3) && Objects.equals("2", cache3.toString()) && Objects.isNull(cache2)) {
                AlarmHelper.log("dailyPush", logDateStr);
                wxAppPaperService.dailyPush(null, logDateStr);
            }
            if (Objects.nonNull(cache3) && Objects.equals("2", cache3.toString()) && Objects.isNull(cache1)) {
                AlarmHelper.log("dailySalePush", logDateStr);
                crmKpiCkPrepareService.insertPrepareDataInfoToDB(logDate,"CRM_DAILY_JOB");
                wxAppPaperService.dailySalePush(null, logDate);
            }
            Object checkCache1 = crmCacheManager.getValue("wg03_info_WxAppPushJob_checkData_Flag_1" + logDateStr);
            if (Objects.isNull(checkCache1)) {
                checkData(logDate);
            }
        } catch (Throwable t) {
            AlarmHelper.alarmEx("DailyReportPartFailed", t, this.getJobName());
        }
        return true;
    }

    public void checkData(Timestamp logDate) {
        Timestamp beginQuarterTime = CrmUtils.getQuarterBeginDate(logDate);
        if (!Objects.equals(CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD), CrmUtils.formatDate(beginQuarterTime, CrmUtils.YYYYMMDD))) {
            CrmAchievementDayItemPoExample poExample1 = new CrmAchievementDayItemPoExample();
            CrmAchievementDayItemPoExample poExample2 = new CrmAchievementDayItemPoExample();
            poExample1.createCriteria().andAggTimeEqualTo(Utils.getBeginOfDay(logDate))
                    .andDataEnvEqualTo("prd")
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andBizTypeEqualTo(SaleKpiTypeEnum.BILIBILI.getBizId());
            poExample2.createCriteria().andAggTimeEqualTo(Utils.getBeginOfDay(Utils.getSomeDayAgo(logDate, 1)))
                    .andDataEnvEqualTo("prd")
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andBizTypeEqualTo(SaleKpiTypeEnum.BILIBILI.getBizId());
            List<CrmAchievementDayItemPo> poList1 = crmAchievementDayItemDao.selectByExample(poExample1);
            List<CrmAchievementDayItemPo> poList2 = crmAchievementDayItemDao.selectByExample(poExample2);
            if (!CollectionUtils.isEmpty(poList1) && !CollectionUtils.isEmpty(poList2)) {
                CrmAchievementDayItemPo po1 = poList1.get(0);
                CrmAchievementDayItemPo po2 = poList2.get(0);
                if (po1.getBrandCompletedTaskAmount().compareTo(po2.getBrandCompletedTaskAmount()) <= 0
                        || po1.getEffectCompletedTaskAmount().compareTo(po2.getEffectCompletedTaskAmount()) <= 0
                        || po1.getPickupCompletedTaskAmount().compareTo(po2.getPickupCompletedTaskAmount()) <= 0) {
                    StringBuilder text = new StringBuilder();
                    text.append("CRM大盘日报品效花季度已完成数据感觉有问题，比上一天少了:");
                    text.append("昨天品牌季度已完成=").append(po1.getBrandCompletedTaskAmount()).append("分;");
                    text.append("前天品牌季度已完成=").append(po2.getBrandCompletedTaskAmount()).append("分;");
                    text.append("昨天效果季度已完成=").append(po1.getEffectCompletedTaskAmount()).append("分;");
                    text.append("前天效果季度已完成=").append(po2.getEffectCompletedTaskAmount()).append("分;");
                    text.append("昨天花火季度已完成=").append(po1.getPickupCompletedTaskAmount()).append("分;");
                    text.append("前天花火季度已完成=").append(po2.getPickupCompletedTaskAmount()).append("分;");
                    WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                            .msgtype(MsgType.TEXT.getType())
                            .text(new Text(text.toString()))
                            .build();
                    wxRobotService.sendWeChatWork(wxRobotMsg, "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=080d5444-224a-4539-bc32-e63e7cea2ef2");
                    crmCacheManager.setValueWithTime("wg03_info_WxAppPushJob_checkData_Flag_1" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD), "1", TimeUnit.DAYS, 3);
                }
            }
        }
    }

}
