package com.bilibili.crm.platform.job.break_tape;

import com.alibaba.fastjson.JSON;
import com.bilibili.crm.platform.api.break_tape.IBreakTapeService;
import com.bilibili.crm.platform.api.break_tape.dto.TaskExecRequest;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.site.lookup.util.StringUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * <AUTHOR>
 * @date 2024-05-20 15:44
 */
@Slf4j
@Component
@JobHandler("RefreshAccountBudgetAndBalanceArriveJob")
public class RefreshAccountBudgetAndBalanceArriveJob extends IJobHandler {

    @Autowired
    private IBreakTapeService breakTapeService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            TaskExecRequest taskExecRequest = JSON.parseObject(s, TaskExecRequest.class);
            log.info("taskExecRequest = {}", JSON.toJSONString(taskExecRequest));
            breakTapeService.refreshBreakRapeAccountHour(taskExecRequest);
        });
        return ReturnT.SUCCESS;
    }
}
