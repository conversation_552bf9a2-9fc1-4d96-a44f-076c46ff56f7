package com.bilibili.crm.platform.job.contract;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.LogEventService;
import com.bilibili.crm.platform.api.achievement.service.IAchieveFromForecastBillService;
import com.bilibili.crm.platform.api.contract.dto.RebateRadixParamDto;
import com.bilibili.crm.platform.api.contract.service.IRebateRadixService;
import com.bilibili.crm.platform.api.income.dto.ContractAdIncome;
import com.bilibili.crm.platform.api.order.dto.OrderBaseDto;
import com.bilibili.crm.platform.biz.mq.message.RebateRadixNormalMessage;
import com.bilibili.crm.platform.job.common.AbstractBaseParamJobHandler;
import com.bilibili.crm.platform.job.contract.param.RebateRadixNormalParam;
import com.bilibili.crm.platform.utils.TimeUtils;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("RebateRadixNormalJob")
public class RebateRadixNormalJob extends AbstractBaseParamJobHandler<RebateRadixNormalParam> {

    @Autowired
    private LogEventService logEventService;

    @Autowired
    private IRebateRadixService iRebateRadixService;

    @Autowired
    private IAchieveFromForecastBillService iAchieveFromForecastBillService;

    public RebateRadixNormalJob() {
        super(RebateRadixNormalParam.class, RebateRadixNormalParam::new);
    }

    @Override
    protected boolean biz(RebateRadixNormalParam param) {
        long msgTime = System.currentTimeMillis();
        AlarmHelper.log("RebateRadixNormal", getTraceId(), msgTime);
        //根据条件查询订单
        RebateRadixParamDto paramDto = new RebateRadixParamDto();

        paramDto.setContractIds(param.getContractIds());
        paramDto.setExcludeAccountIds(param.getExcludeAccountIds());
        List<Integer> foreCastContractIds = iRebateRadixService.getForeCastContractIds(paramDto);
        if (StringUtils.isNotBlank(param.getOrderBeginDate())) {
            paramDto.setBeginTime(Utils.getTimestamp(param.getOrderBeginDate()));
        }
        AlarmHelper.log("QueryOrders", paramDto);
        List<OrderBaseDto> forecastList = iRebateRadixService.getForecastOrder(paramDto, foreCastContractIds);
        forecastList = forecastList.stream()
                .filter(order -> CollectionUtils.isEmpty(param.getOrderIds()) || param.getOrderIds().contains(order.getId()))
                .collect(Collectors.toList());
        List<ContractAdIncome> normalContractAdIncome = iAchieveFromForecastBillService.checkoutWithRetry(forecastList);
        AlarmHelper.log("StartMq", normalContractAdIncome.size(), foreCastContractIds);
        normalContractAdIncome.stream()
                .map(income -> {
                    RebateRadixNormalMessage msg = new RebateRadixNormalMessage();
                    msg.setContractId(income.getContractId().longValue());
                    msg.setOrderId(income.getOrderId().longValue());
                    msg.setAmount(0L);
                    msg.setRebateDate(TimeUtils.getTimeString(income.getDate()));
                    msg.setTraceId(getTraceId());
                    msg.setMsgTime(msgTime);
                    msg.setAmountDouble(income.getAmount().doubleValue());
                    return msg;
                }).forEach(logEventService::putLancer);

        return true;
    }

}
