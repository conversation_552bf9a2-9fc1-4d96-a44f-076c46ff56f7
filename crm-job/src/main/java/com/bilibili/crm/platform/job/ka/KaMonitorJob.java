package com.bilibili.crm.platform.job.ka;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.platform.api.ka_belonging.enums.KaBelongingConfigLevelEnum;
import com.bilibili.crm.platform.biz.cache.CrmCacheKaMonitorKey;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.dao.CrmSaleDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleGroupDao;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.po.addata.CrmGroupProductKaLabelPo;
import com.bilibili.crm.platform.biz.repo.AccCompanyGroupRepo;
import com.bilibili.crm.platform.biz.repo.kamonitor.CompanyGroupKaMonitorRepo;
import com.bilibili.crm.platform.biz.repo.kamonitor.GroupKaMonitorRetentionDto;
import com.bilibili.crm.platform.biz.repo.kamonitor.GroupKaMonitorTransitionDto;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.*;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

@Component
@JobHandler("KaMonitorJob")
@Slf4j
public class KaMonitorJob extends IJobHandler {

    @Autowired
    private CompanyGroupKaMonitorRepo companyGroupKaMonitorRepo;

    @Autowired
    private AccCompanyGroupRepo accCompanyGroupRepo;

    @Autowired
    private CrmCacheManager crmCacheManager;

    @Autowired
    private CrmSaleGroupDao saleGroupDao;

    @Autowired
    private CrmSaleDao saleDao;

    @Value("${crm.kamonitor.salegroup:95,电商医疗食饮;15,网教金;75,数码家电;55,美妆鞋服;52,游戏;60,汽车;64,电商;90,医疗;96,医疗;83,网服;101,教育;84,金融;77,手机;78,PC大家电;100,家居家装;76,小家电;103,美妆;104,鞋服;52,游戏;60,汽车;all,all}")
    private String saleGroupConfig;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>KaMonitorJob.start.executing...");
        try {
            Calendar calendar = Calendar.getInstance(); // 获取当前日期
            calendar.add(Calendar.DAY_OF_MONTH, -1); // 将日期减1
            Date previousDate = calendar.getTime();
            SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
            String currentDate = sdf.format(previousDate);
            this.executeSaleAndCompanyJob(currentDate);
            this.executeWeek(currentDate);
        } catch (Exception e) {
            log.error("KaMonitorJob.saveAccountAgentBalance.error", e);
        }

        log.info("=====>>>>>>>>>>>>>>>KaMonitorJob.end.executing.");
        return ReturnT.SUCCESS;
    }

    public void executeSaleAndCompanyJob(String date) {
        log.info("executeSaleAndCompanyJob start ={}", date);
        List<CrmGroupProductKaLabelPo> allCompanyGroupList = companyGroupKaMonitorRepo.getGroupProductKaLabelByDate(date, 3, new ArrayList<>());
        if (CollectionUtils.isEmpty(allCompanyGroupList)) {
            log.info("executeSaleAndCompanyJob data no ready");
            return;
        }
        List<CrmGroupProductKaLabelPo> companyGroups = companyGroupKaMonitorRepo.uniqueCompanyGroup(allCompanyGroupList);
        log.info("executeSaleAndCompanyJob step 1");

        List<Long> groupIds = companyGroups.stream().map(CrmGroupProductKaLabelPo::getGroupId).distinct().collect(Collectors.toList());
        // todo 获取集团表中的集团信息
        Map<Integer, AccCompanyGroupPo> categoryAndCompanyGroupMap = companyGroupKaMonitorRepo.getGroupCategoryByGroupIds(groupIds);
        Map<Integer, CrmUnitedIndustryPo> categoryMap = companyGroupKaMonitorRepo.getVerticalCategory();

        log.info("executeSaleAndCompanyJob step 2");

        Map<Integer, List<Long>> saleGroupAndCompanyGroupMap = new HashMap<>();
        Map<Integer, List<Long>> saleAndCompanyGroupMap = new HashMap<>();
        try {
            companyGroups.forEach(x -> {
                ArrayList<Integer> saleIds = new ArrayList<>();
                // 获取集团的ID
                if (!x.getDirectSaleIds().equals("") && !x.getDirectSaleIds().equals("''")) {
                    String[] saleIdStrs = x.getDirectSaleIds().replace(" ", "").split(",");
                    for (String saleId : saleIdStrs) {
                        saleIds.add(Integer.valueOf(saleId));
                    }
                }

                for (Integer saleId : saleIds) {
                    List<Long> groupIdList = saleAndCompanyGroupMap.getOrDefault(saleId, new ArrayList<>());
                    groupIdList.add(x.getGroupId());
                    saleAndCompanyGroupMap.put(saleId, groupIdList);
                }
                Set<Integer> saleGroupIDs = new HashSet<>();
                if (categoryAndCompanyGroupMap.containsKey(x.getGroupId().intValue())) {
                    Integer unitedThirdIndustryId = x.getGUnitedThirdIndustryId().intValue();
                    // 是否为垂直行业
                    if (categoryMap.containsKey(unitedThirdIndustryId) && categoryMap.get(unitedThirdIndustryId).getIsVertical() == 1) {
                        saleGroupIDs = new HashSet<>(companyGroupKaMonitorRepo.getSaleIdByCategoryId(x.getGUnitedFirstIndustryId().intValue(),
                                x.getGUnitedSecondIndustryId().intValue(), x.getGUnitedThirdIndustryId().intValue()));

                        if (CollectionUtils.isEmpty(saleGroupIDs)) {
                            saleGroupIDs.add(Integer.MAX_VALUE);
                        }
                    } else { // 非垂直行业 或者无行业标签
                        if (CollectionUtils.isEmpty(saleIds)) {
                            saleGroupIDs.add(Integer.MAX_VALUE);
                        } else {
                            CrmSalePoExample example = new CrmSalePoExample();
                            example.createCriteria().andIdIn(saleIds)
                                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode());

                            List<CrmSalePo> salePos = saleDao.selectByExample(example);
                            saleGroupMappingServiceImpl.fillSalePoLevel(salePos);
                            List<Integer> saleGroupIdList = salePos.stream().map(CrmSalePo::getGroupId).collect(Collectors.toList());
                            if (CollectionUtils.isEmpty(saleGroupIdList)) {
                                saleGroupIDs.add(Integer.MAX_VALUE);
                            } else {
                                saleGroupIDs.addAll(saleGroupIdList);
                                List<Integer> needQuerySaleGroupIdList = saleGroupIdList;
                                while (!CollectionUtils.isEmpty(needQuerySaleGroupIdList)) {
                                    CrmSaleGroupPoExample saleGroupPoExample = new CrmSaleGroupPoExample();
                                    log.info("select sale group by group id {}", needQuerySaleGroupIdList);
                                    saleGroupPoExample.createCriteria().andIdIn(needQuerySaleGroupIdList)
                                            .andIsDeletedEqualTo(IsDeleted.VALID.getCode());
                                    List<CrmSaleGroupPo> saleGroupPos = saleGroupDao.selectByExample(saleGroupPoExample);
                                    saleGroupIDs.addAll(saleGroupPos.stream().map(CrmSaleGroupPo::getId).collect(Collectors.toList()));
                                    needQuerySaleGroupIdList = saleGroupPos.stream().map(CrmSaleGroupPo::getParentId).filter(parentId -> parentId != 0)
                                            .collect(Collectors.toList());
                                }
                            }
                        }
                    }
                }

                log.info("executeSaleAndCompanyJob step 3 group_id ={}", x.getGroupId());

                if (CollectionUtils.isEmpty(saleGroupIDs)) {
                    log.info("ka monitor job saleGroupId empty {}", x.getGroupId());
                    saleGroupIDs.add(Integer.MAX_VALUE);
                }

                saleGroupIDs.forEach(saleGroupId -> {
                    List<Long> groupIdList = saleGroupAndCompanyGroupMap.getOrDefault(saleGroupId, new ArrayList<>());
                    groupIdList.add(x.getGroupId());
                    saleGroupAndCompanyGroupMap.put(saleGroupId, groupIdList);
                    log.info("ka monitor job saleGroupId {}", saleGroupId);
                    log.info("ka monitor job groupIdList {}", JSON.toJSONString(groupIdList));
                });
            });
        } catch (Exception e) {
            log.error("executeSaleAndCompanyJob error", e);
        }
        log.info("executeSaleAndCompanyJob step 4");
        String year = date.substring(0, 4);
        saleGroupAndCompanyGroupMap.forEach((saleGroupId, groupIdList) -> crmCacheManager.set(year + CrmCacheKaMonitorKey.KA_MONITOR_SALEGROUPID_GROUPID, String.valueOf(saleGroupId), JSON.toJSONString(groupIdList)));
        saleAndCompanyGroupMap.forEach((saleId, groupIdList) -> {
            String key = year + CrmCacheKaMonitorKey.KA_MONITOR_SALEID_GROUPID + saleId;
            crmCacheManager.setKv(key, JSON.toJSONString(groupIdList), 1000L, TimeUnit.DAYS);
            log.info("saleId={} groupIdList={}", saleId, JSON.toJSONString(groupIdList));
        });
        crmCacheManager.setKv(year + CrmCacheKaMonitorKey.KA_MONITOR_ALL_SALE_GROUP_ID, JSON.toJSONString(saleGroupAndCompanyGroupMap.keySet()), 1000L, TimeUnit.DAYS);
    }

    public void executeWeek(String date) throws ParseException {

        SimpleDateFormat sdf = new SimpleDateFormat("yyyyMMdd");
        Date servenDate = sdf.parse(date);
        Calendar calendar = Calendar.getInstance(); // 获取当前日期
        calendar.setTime(servenDate);
        int dayOfWeek = calendar.get(Calendar.DAY_OF_WEEK);
        if (dayOfWeek == Calendar.THURSDAY) {
            int weekOfYear = calendar.get(Calendar.WEEK_OF_YEAR);
            Map<String, List<Float>> retention1000WMap;
            Map<String, List<Float>> retention300WMap;
            Map<String, List<Float>> transition300WMap;
            Map<String, List<Float>> transition100WMap;

            String year = date.substring(0, 4);
            if (year.equals("2024")) {
                year = "";
            }
            retention1000WMap = initRateLineMap(year + CrmCacheKaMonitorKey.KA_RETENTION_1000_RATE_LINE, weekOfYear);
            retention300WMap = initRateLineMap(year + CrmCacheKaMonitorKey.KA_RETENTION_300_RATE_LINE, weekOfYear);
            transition300WMap = initRateLineMap( year + CrmCacheKaMonitorKey.KA_TRANSITION_300_RATE_LINE, weekOfYear);
            transition100WMap = initRateLineMap(year + CrmCacheKaMonitorKey.KA_TRANSITION_100_RATE_LINE, weekOfYear);

            // 留存率
            List<GroupKaMonitorRetentionDto> retentionDtos = companyGroupKaMonitorRepo.kaMonitorForRetention(Operator.SYSTEM, date);
            for (GroupKaMonitorRetentionDto dto : retentionDtos) {

                float retention = (float) CrmUtils.formatDouble(dto.getRetentionRate(), 4);
                if (Objects.equals(dto.getKaType(), KaBelongingConfigLevelEnum.KA.getCode()) && retention1000WMap.containsKey(dto.getKey())) {
                    if (weekOfYear - 1 == retention1000WMap.get(dto.getKey()).size()) {
                        retention1000WMap.get(dto.getKey()).add(retention);
                    } else {
                        retention1000WMap.get(dto.getKey()).set(weekOfYear - 1, retention);
                    }
                }

                if (Objects.equals(dto.getKaType(), KaBelongingConfigLevelEnum.LUMBAR_TAIL_300W.getCode()) && retention300WMap.containsKey(dto.getKey())) {
                    if (weekOfYear - 1 == retention300WMap.get(dto.getKey()).size()) {
                        retention300WMap.get(dto.getKey()).add(retention);
                    } else {
                        retention300WMap.get(dto.getKey()).set(weekOfYear - 1, retention);
                    }
                }
            }
            // 跃迁率
            List<GroupKaMonitorTransitionDto> transitionDtos = companyGroupKaMonitorRepo.kaMonitorForTransition(Operator.SYSTEM, date);
            for (GroupKaMonitorTransitionDto dto : transitionDtos) {
                if (!transition300WMap.containsKey(dto.getKey())) {
                    continue;
                }
                float transition300W = (float) CrmUtils.dividedByDouble((double) dto.getKa5ToKa2Num(), dto.getKa5BeginNum(), 4);
                if (weekOfYear - 1 == transition300WMap.get(dto.getKey()).size()) {
                    transition300WMap.get(dto.getKey()).add(transition300W);
                } else {
                    transition300WMap.get(dto.getKey()).set(weekOfYear - 1, transition300W);
                }

                float transition100W = (float) CrmUtils.dividedByDouble((double) (dto.getKa3ToKa5Num() + dto.getKa3ToKa2Num()) , dto.getKa3BeginNum(), 4);
                if (weekOfYear - 1 == transition100WMap.get(dto.getKey()).size()) {
                    transition100WMap.get(dto.getKey()).add(transition100W);
                } else {
                    transition100WMap.get(dto.getKey()).set(weekOfYear - 1, transition100W);
                }
            }

            // 跃迁率汇总
            int allKa5Num = transitionDtos.stream().mapToInt(GroupKaMonitorTransitionDto::getKa5BeginNum).sum();
            int allKa5TransitionNum = transitionDtos.stream().mapToInt(GroupKaMonitorTransitionDto::getKa5ToKa2Num).sum();
            float allTransition300W = (float) CrmUtils.dividedByDouble((double) allKa5TransitionNum , allKa5Num, 4);
            if (weekOfYear - 1 == transition300WMap.get("all").size()) {
                transition300WMap.get("all").add(allTransition300W);
            } else {
                transition300WMap.get("all").set(weekOfYear - 1, allTransition300W);
            }
            int allKa3Num = transitionDtos.stream().mapToInt(GroupKaMonitorTransitionDto::getKa3BeginNum).sum();
            int allKa3ToKa5Num = transitionDtos.stream().mapToInt(GroupKaMonitorTransitionDto::getKa3ToKa5Num).sum();
            int allKa3ToKa2Num = transitionDtos.stream().mapToInt(GroupKaMonitorTransitionDto::getKa3ToKa2Num).sum();
            float allTransition100W = (float) CrmUtils.dividedByDouble((double) (allKa3ToKa5Num + allKa3ToKa2Num) , allKa3Num, 4);
            if (weekOfYear - 1 == transition100WMap.get("all").size()) {
                transition100WMap.get("all").add(allTransition100W);
            } else {
                transition100WMap.get("all").set(weekOfYear - 1, allTransition100W);
            }

            crmCacheManager.setKv(year + CrmCacheKaMonitorKey.KA_RETENTION_1000_RATE_LINE, JSON.toJSONString(retention1000WMap), 1000L, TimeUnit.DAYS);
            crmCacheManager.setKv(year + CrmCacheKaMonitorKey.KA_RETENTION_300_RATE_LINE, JSON.toJSONString(retention300WMap), 1000L, TimeUnit.DAYS);
            crmCacheManager.setKv(year + CrmCacheKaMonitorKey.KA_TRANSITION_300_RATE_LINE, JSON.toJSONString(transition300WMap), 1000L, TimeUnit.DAYS);
            crmCacheManager.setKv(year + CrmCacheKaMonitorKey.KA_TRANSITION_100_RATE_LINE, JSON.toJSONString(transition100WMap), 1000L, TimeUnit.DAYS);


            log.info("retention1000WList ={}", JSON.toJSONString(retention1000WMap));
            log.info("retention300WList ={}", JSON.toJSONString(retention300WMap));
            log.info("transition300WList ={}", JSON.toJSONString(transition300WMap));
            log.info("transition100WList ={}", JSON.toJSONString(transition100WMap));
        }
    }

    private Map<String, List<Float>> initRateLineMap(String redisKey, Integer weekOfYear) {
        Map<String, List<Float>> retentionMap;
        Object result = crmCacheManager.getValue(redisKey);
        if (Objects.isNull(result)) {
            retentionMap = new HashMap<>();
            log.info("saleGroupConfig = {}", saleGroupConfig);
            String[] linesArray = saleGroupConfig.split(";");
            for (String line : linesArray) {
                String[] lineArray = line.split(",");
                if (lineArray.length != 2) {
                    continue;
                }
                retentionMap.put(lineArray[0], new ArrayList<>(Collections.nCopies(weekOfYear, 0.0f)));
            }
        } else {
            retentionMap = JSON.parseObject(result.toString(), Map.class);
        }
        return retentionMap;
    }
}
