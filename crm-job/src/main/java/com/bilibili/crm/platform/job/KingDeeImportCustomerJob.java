package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.kingdee.service.IBiliKingDeeDataImportService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * @author: mort
 * @time: 2022/09/05 2:28 下午
 */
@Component
@JobHandler("KingDeeImportCustomerJob")
@Slf4j
public class KingDeeImportCustomerJob extends IJobHandler {

    @Autowired
    private IBiliKingDeeDataImportService kingDeeDataImportService;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    /**
     * @param args exp：args:202112
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {

        try {
            //默认上个月, 同步上个月的数据
            Timestamp date = incomeTimeUtil.getSomeMonthAgoBeginTime(1);
            if (StringUtils.hasText(args)) {
                date = StringDateParser.stringToTimestamp(args);
            }
            Timestamp finalDate = date;

            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                pickSupplierImport(finalDate);
                log.info("KingDeeImportCustomerJob finished date: {}", finalDate.toString());
            });
            Cat.logEvent("KingDeeImportCustomerJob_SUCCESS", date.toString() + ":normal");
        } catch (Exception e) {
            Cat.logEvent("Exception_KingDeeImportCustomerJob", e.getMessage());
            throw e;
        }
        return ReturnT.SUCCESS;
    }


    /**
     * [{"taxRegisterCode":"91310000MA1G8B8AXD2","name":"上海哔哩哔哩科技有限公司2"}]
     */
    public void pickSupplierImport(Timestamp date) {

        Timestamp begin = Utils.getBeginOfDay(Utils.getBeginDayOfMonth(date));
        Timestamp end = Utils.getEndOfDay(Utils.getEndDayOfMonth(date));

        Timestamp beginOfDay = Utils.getBeginOfDay(begin);
        Timestamp endOfDay = Utils.getEndOfDay(end);
        kingDeeDataImportService.customerImport(beginOfDay, endOfDay, null);
    }

}
