package com.bilibili.crm.platform.job;

import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.app.cache.sale.WxAppSaleHourWriter;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.dianping.cat.Cat;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-06-06 10:16:05
 * @description:
 **/

@Component
@JobHandler("WxAppSaleHourJob")
@Slf4j
public class WxAppSaleHourJob extends AbstractBaseJobHandler {

    @Resource
    private WxAppSaleHourWriter wxAppSaleHourWriter;

    @Override
    protected boolean biz(String args) {
        log.info("WxAppSaleHourJob start args {}", args);
        try {
            Timestamp time = new Timestamp(System.currentTimeMillis());
            if (StringUtils.isNotBlank(args)) {
                time = CrmUtils.parseTimestamp(args, CrmUtils.YYYYMMDD_WITHOUT_SPLIT);
                if (time == null) {
                    AlarmHelper.log("WrongArgs", args);
                    return false;
                }
                time = CrmUtils.getLastTimeOfDay(time);
            }
            wxAppSaleHourWriter.cacheRequest(time);
        } catch (Exception e) {
            AlarmHelper.alarmEx("DailyReportPartFailed", e, this.getJobName());
            Cat.logEvent("Exception_WxAppSaleHourJob", e.getMessage());
            throw e;
        }
        log.info("WxAppSaleHourJob success args {}", args);
        return true;
    }

}
