package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("WxAppDataCheckJob")
@Slf4j
public class WxAppDataCheckJob extends IJobHandler {
    @Autowired
    private WxAppDataCheckService dataCheckService;

    /**
     * 1. 检查各个模块数据是否准备好
     * 2. 检查数仓数据(es:dss_ad_overview)是否准备好
     * 3. 检查依赖report_day_stock 是否准备好
     * 4. 检查账单数据是否准备好
     * 5. 检查消耗数据数据(es:wallet_log)是否准备好
     * 6. 检查消耗数据数据(es:ad_stat_source_day)是否准备好
     * @param args
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) ->  dataCheckService.dataCheck());
        return ReturnT.SUCCESS;
    }

}
