package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.service.order.PushOrderDeductFeeService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("PushOrderDeductFeeJob")
@Slf4j
public class PushOrderDeductFeeJob extends IJobHandler {

    @Autowired
    private PushOrderDeductFeeService pushOrderDeductFeeService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        pushOrderDeductFeeService.deductFee();
        return ReturnT.SUCCESS;
    }
}
