package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.account.service.IAccountIndustryTagService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2018/12/19
 **/
@Component
@JobHandler("AccountTagNoticeJob")
@Slf4j
public class AccountTagNoticeJob extends IJobHandler {

    @Autowired
    private IAccountIndustryTagService accountIndustryTagService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("=====>>>>>>>>>>>>>>>AccountTagNoticeJob.start.executing...");

        try {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> accountIndustryTagService.sendNewAccountWithoutBindingMail());
        } catch (Exception e) {
            log.error("=====>>>>>>>>>>>>>>>AccountTagNoticeJob.execute.error", e);
        }

        log.info("=====>>>>>>>>>>>>>>>AccountTagNoticeJob.end.executing.");
        return ReturnT.SUCCESS;
    }
}
