package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("BsiOpportunityInvalidJob")
@Slf4j
public class BsiOpportunityInvalidJob extends IJobHandler {
    @Autowired
    private IBsiOpportunityService bsiOpportunityService;

    /**
     * 商单失效通知以及商单超期（60天）失效
     * @param args
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> bsiOpportunityService.bsiOpportunityInvalid(null));
        return ReturnT.SUCCESS;
    }
}
