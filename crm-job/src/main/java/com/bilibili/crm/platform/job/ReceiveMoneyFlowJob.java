package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.bangumi.databus.service.DatabusPoolSub;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.api.finance.dto.automation.MainStationReceiveMoneyFlowInfo;
import com.bilibili.crm.platform.api.finance.enums.CrmReceiveFlowTaskStatusEnum;
import com.bilibili.crm.platform.biz.config.OTTraceId;
import com.bilibili.crm.platform.biz.repo.finance.CrmReceiveFlowTaskRecordRepo;
import com.bilibili.crm.platform.biz.service.finance.automation.component.ReceiveMoneyFlowReceiver;
import com.bilibili.crm.platform.biz.service.finance.automation.component.parser.FlowParser;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Event;
import com.dianping.cat.message.Transaction;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.mybatis.spring.MyBatisSystemException;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.dao.DuplicateKeyException;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

/**
 * 收款流水接收 job
 *
 * <AUTHOR>
 * @date 2021/2/4 12:59 下午
 */
@Component
@JobHandler("receiveMoneyFlowJob")
@Slf4j
public class ReceiveMoneyFlowJob extends IJobHandler {

    @Autowired
    private ThreadPoolTaskExecutor executor;
    @Resource(name = "receiveMoneyFlowDataBusPoolSub")
    private DatabusPoolSub receiveMoneyFlowDataBusPoolSub;
    @Autowired
    private ReceiveMoneyFlowReceiver receiveMoneyFlowReceiveProcessor;
    @Autowired
    private FlowParser flowParser;
    @Autowired
    private CrmReceiveFlowTaskRecordRepo crmReceiveFlowTaskRecordRepo;
    @Autowired
    private OTTraceId otTraceId;

    @Value("${dataBus.switch.receiveMoneyFlow:1}")
    private Integer dataBusSwitch;

    private final static List<Future<Integer>> futureList = new ArrayList<>();

    @Scheduled(cron = "0 1/1 * * * ? ")   // 每1 min 执行一次
    public void execute() {
        OTTraceId.Ctx ctx = otTraceId.getCtx(this.getClass().getSimpleName(), this.getClass().getSimpleName()).initReqTraceIdAndReqUri();
        try {
            this.execute("");
        } catch (Exception e) {
            e.printStackTrace();
        } finally {
            otTraceId.close(ctx);
        }
    }

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        // 如果 data bus 关闭，不生成
        if (IsValid.FALSE.getCode().equals(dataBusSwitch)) {
            return ReturnT.SUCCESS;
        }
        log.info("=====>>>>>>>>>>>>>>> ReceiveMoneyFlowJob.start ...");
        Transaction transaction = Cat.newTransaction("JOB", "ReceiveMoneyFlowJob");
        transaction.addData("futureList.size", futureList.size());
        synchronized (futureList) {
            // init
            if (CollectionUtils.isEmpty(futureList)) {
                try {
                    futureList.add(addTask());
                    log.info("add task success");
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (Exception e) {
                    log.error("submit task error", e);
                    transaction.setStatus(e);
                } finally {
                    transaction.complete();
                }
            } else {
                try {
                    Future<Integer> future = futureList.get(0);
                    if (future == null || future.get(1, TimeUnit.MILLISECONDS) == 0) {
                        futureList.set(0, addTask());
                        log.info("add task success");
                        transaction.addData("databus health", "add again!");
                    }
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (TimeoutException e) {
                    transaction.addData("databus health", "running");
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (Exception e) {
                    log.error("submit task error");
                    transaction.setStatus(e);
                } finally {
                    transaction.complete();
                }
            }
        }

        log.info("=====>>>>>>>>>>>>>>> ReceiveMoneyFlowJob.end ...");
        return ReturnT.SUCCESS;
    }

    /**
     * 添加一个任务到线程池中
     *
     * @return
     */
    private Future<Integer> addTask() {
        return executor.submit(() -> {
            try {
                receiveMoneyFlowDataBusPoolSub.sub(this::handle);
            } catch (Exception e) {
                log.error("execute.FinalDraftFlow.databus.error", e);
                return 0;
            }
            return 1;
        });
    }


    /**
     * 处理报文
     *
     * @param encryptJsonObject data bus 接收到的报文 格式：{
     *                          "billId": 1111111,
     *                          "statementId": "1212121",
     *                          "bank": "CMB",
     *                          "transAmount": 382410,
     *                          "customerAccountMaskNo": "************",
     *                          "customerName": "济南文化传播有限公司",
     *                          "trxDate": "2021-01-27 00:20:50",
     *                          "accountMaskNo": "************",
     *                          "subAcountNo": "*****************",
     *                          "accountName": "幻电",
     *                          "userMemo": "打赏",
     *                          "partnerId": 10005,
     *                          "signType": "MD5",
     *                          "sign": "e46dcfef32229f6988517b42bf1fcab2"
     *                          }
     */
    public void handle(JSONObject encryptJsonObject) {
        log.info("=====> handle msg, msg:{}", JSON.toJSONString(encryptJsonObject));
        MainStationReceiveMoneyFlowInfo decryptFlowDto = flowParser.parseMsg(encryptJsonObject);

        // mq json 传消费操作
        consume(encryptJsonObject, decryptFlowDto, t -> {
            receiveMoneyFlowReceiveProcessor.onReceivedFlowMsg(decryptFlowDto);
        });
    }

    /**
     * mq json 传消费操作
     *
     * @param encryptJsonObject 加密的 json
     * @param decryptFlowDto    解密的 dto
     */
    private void consume(JSONObject encryptJsonObject, MainStationReceiveMoneyFlowInfo decryptFlowDto, Consumer<JSONObject> business) {
        Transaction transaction = Cat.newTransaction("收款流水DATABUS消费", "receiveMoneyFlowReceiverConsumer");

        Integer taskId = 0;
        try {
            taskId = crmReceiveFlowTaskRecordRepo.createFlowTaskRecord(decryptFlowDto.getBillId(),
                    decryptFlowDto.getStatementId(), encryptJsonObject.toString(), JSON.toJSONString(decryptFlowDto));
            Cat.logEvent("consume", "插入任务record", Event.SUCCESS, String.format("taskId=%s&billId=%s", taskId, decryptFlowDto.getBillId()));
        } catch (DuplicateKeyException | MyBatisSystemException e) {
            AlarmHelper.alarmEx("=====> consume[bill id repeat, discard message], billId:", e, decryptFlowDto.getBillId(), decryptFlowDto.getStatementId());
            return;
        } catch (Exception e) {
            AlarmHelper.alarmEx("=====> consume[create flow record task fail], billId:", e, decryptFlowDto.getBillId(), decryptFlowDto.getStatementId());
        }

        try {
            transaction.addData("MSG", encryptJsonObject.toJSONString());
            log.info("=====> consume receiveMoneyFlowJob MSG {}", encryptJsonObject.toJSONString());
            // 落下报文 待处理
            // 处理中
            Cat.logEvent("consume", "更新任务recordStatus=1处理中", Event.SUCCESS, String.format("taskId=%s&billId=%s",
                    taskId, decryptFlowDto.getBillId()));

            crmReceiveFlowTaskRecordRepo.updateFlowTaskRecordStatus(taskId,
                    CrmReceiveFlowTaskStatusEnum.PROCESSING.getCode(), "processing");
            // 业务处理
            business.accept(encryptJsonObject);

            // 修改报文为处理完成
            crmReceiveFlowTaskRecordRepo.updateFlowTaskRecordStatus(taskId,
                    CrmReceiveFlowTaskStatusEnum.COMPLETE_AND_SUCCESS.getCode(), "ok");
            transaction.setStatus(Transaction.SUCCESS);
        } catch (Exception e) {
            Cat.logEvent("consume", "流水生成失败", Event.SUCCESS, String.format("taskId=%s&billId=%s",
                    taskId, decryptFlowDto.getBillId()));
            AlarmHelper.alarmEx("ConsumeGenFailed", e, taskId, decryptFlowDto.getBillId());
            if (transaction != null) {
                transaction.setStatus(e);
            }
            // 修改报文为处理失败
            crmReceiveFlowTaskRecordRepo.updateFlowTaskRecordStatus(taskId,
                    CrmReceiveFlowTaskStatusEnum.FAILED.getCode(), "流水生成失败");
            crmReceiveFlowTaskRecordRepo.updateFlowTaskRecordRetryCount(taskId);
            Cat.logEvent("consume", "更新任务recordStatus=-1失败", Event.SUCCESS, String.format("taskId=%s&billId=%s",
                    taskId, decryptFlowDto.getBillId()));

            throw e;
        } finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
    }
}
