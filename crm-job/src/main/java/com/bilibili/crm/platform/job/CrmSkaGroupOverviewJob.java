package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityContractService;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityIntentProductService;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiContractQueryDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiIntentProductQueryDto;
import com.bilibili.crm.platform.api.bsiopportunity.dto.BsiOpportunityIntentProductMappingDto;
import com.bilibili.crm.platform.api.company.dto.GroupSignBoxConfigDTO;
import com.bilibili.crm.platform.api.company.service.ICorporationGroupService;
import com.bilibili.crm.platform.api.enums.group.CompanyGroupTypeEnum;
import com.bilibili.crm.platform.api.follow_manage.enums.CustomerBizType;
import com.bilibili.crm.platform.api.ka_belonging.dto.KaBelongIngDTO;
import com.bilibili.crm.platform.api.ka_belonging.dto.KaBelongIngQueryDTO;
import com.bilibili.crm.platform.api.ka_belonging.enums.KaBelongingConfigLevelEnum;
import com.bilibili.crm.platform.api.ka_belonging.enums.KaBelongingMappingTypeEnum;
import com.bilibili.crm.platform.api.ka_belonging.service.IKaBelongIngService;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.service.ISaleService;
import com.bilibili.crm.platform.biz.clickhouse.param.AdsCrmCommerceKpiAmountDataADQueryParam;
import com.bilibili.crm.platform.biz.common.FollowStage;
import com.bilibili.crm.platform.biz.crm.account.dao.write.CustomerDao;
import com.bilibili.crm.platform.biz.dao.*;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.po.clickhouse.AdsCrmCommerceKpiAmountDataADPO;
import com.bilibili.crm.platform.biz.repo.AccCompanyGroupRepo;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.IsInnerEnum;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * crmSka看板监控的Job
 *
 * <AUTHOR>
 * date 2024/2/26 15:27.
 * Contact: <EMAIL>.
 */
@Component
@JobHandler("CrmSkaGroupOverviewJob")
@Slf4j
public class CrmSkaGroupOverviewJob extends IJobHandler {
    @Autowired
    private CrmSkaDashboardItemDao crmSkaDashboardItemDao;
    @Autowired
    private IKaBelongIngService kaBelongIngService;
    @Autowired
    private AccCompanyGroupRepo accCompanyGroupRepo;
    @Autowired
    private ISaleService saleService;
    @Autowired
    private CrmGroupOrganizationDao crmGroupOrganizationDao;
    @Autowired
    private BsiOpportunityDao bsiOpportunityDao;
    @Autowired
    private IBsiOpportunityContractService bsiOpportunityContractService;
    @Autowired
    private IBsiOpportunityIntentProductService bsiOpportunityIntentProductService;
    @Autowired
    private CustomerDao customerDao;
    @Autowired
    private CrmFollowManageRecordDao crmFollowManageRecordDao;
    @Autowired
    private CrmFollowManageCustomerDao crmFollowManageCustomerDao;
    @Autowired
    private CrmContactorDao crmContactorDao;
    @Autowired
    private ICorporationGroupService corporationGroupService;
    @Autowired
    private CrmSkaTargetMoneyConfigDao crmSkaTargetMoneyConfigDao;
    @Value("${crm.portal.env:prd}")
    private String env;
    @Value("${ska.tag.sale.group.str:{}}")
    private String tagAndSaleGroupIdStr;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        //参数 20240101_20240331
        Timestamp yesterday = Utils.getYesteday();
        if (StringUtils.isBlank(param)) {
            param = CrmUtils.formatDateCanNull(CrmUtils.getQuarterBeginDate(yesterday), CrmUtils.YYYYMMDD_WITHOUT_SPLIT) + "_" + CrmUtils.formatDateCanNull(yesterday, CrmUtils.YYYYMMDD_WITHOUT_SPLIT);
        }
        try {
            if (StringUtils.isNotBlank(param)) {
                CrmSkaDashboardItemPoExample example = new CrmSkaDashboardItemPoExample();
                example.createCriteria().andQueryDateEqualTo(param).andDataEnvEqualTo(env);
                crmSkaDashboardItemDao.deleteByExample(example);
                buildSkaDashBoard(CrmUtils.parseTimestamp(param.split("_")[0], CrmUtils.YYYYMMDD_WITHOUT_SPLIT), CrmUtils.parseTimestamp(param.split("_")[1], CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
            }
        } catch (Exception e) {
            log.error("buildSkaDashBoard_fail,!!!");
            log.error("buildSkaDashBoard_e,e=", e);
            return ReturnT.FAIL;
        }
        return ReturnT.SUCCESS;
    }


    public Map<String, Integer> getTagAndsaleGroupMap() {
        return JSON.parseObject(tagAndSaleGroupIdStr, new TypeReference<Map<String, Integer>>() {
        });
    }

    public List<KaBelongIngDTO> buildKaBelongIngDTOList(Timestamp dateTime) {
        List<KaBelongIngDTO> skaList = new ArrayList<>();

        List<KaBelongIngDTO> groupSkaList = kaBelongIngService.queryKaBelongByMappingIdAndTypeSource(KaBelongIngQueryDTO.builder()
                .beginTime(dateTime).endTime(dateTime).mappingType(KaBelongingMappingTypeEnum.GROUP.getCode()).configLevel(KaBelongingConfigLevelEnum.SKA.getCode()).build());
        List<KaBelongIngDTO> productSkaList = kaBelongIngService.queryKaBelongByMappingIdAndTypeSource(KaBelongIngQueryDTO.builder()
                .beginTime(dateTime).endTime(dateTime).mappingType(KaBelongingMappingTypeEnum.PRODUCT.getCode()).configLevel(KaBelongingConfigLevelEnum.SKA.getCode()).build());

        Map<Integer, KaBelongIngDTO> groupSkaMap = groupSkaList.stream().collect(Collectors.toMap(KaBelongIngDTO::getMappingId, Function.identity(), (o, n) -> n));
        List<Integer> kpiAllGroupIdList = groupSkaList.stream().map(KaBelongIngDTO::getMappingId).collect(Collectors.toList());
        List<AccCompanyGroupPo> allGroups = accCompanyGroupRepo.queryListByIds(kpiAllGroupIdList);

        List<Integer> parentGroupIdList = allGroups.stream().filter(e -> Objects.equals(e.getGroupType(), CompanyGroupTypeEnum.PARENT_GROUP.getCode())).map(AccCompanyGroupPo::getId).collect(Collectors.toList());
        groupSkaList = groupSkaList.stream().filter(e -> !parentGroupIdList.contains(e.getMappingId())).collect(Collectors.toList());
        Map<Integer, List<Integer>> sonGroupIdMap = accCompanyGroupRepo.getSonGroupIdMapByParentIdList(parentGroupIdList);
        List<Integer> sonGroupIdList = sonGroupIdMap.values().stream().flatMap(List::stream).collect(Collectors.toList());
        groupSkaList = groupSkaList.stream().filter(e -> !sonGroupIdList.contains(e.getMappingId())).collect(Collectors.toList());

        sonGroupIdMap.keySet().forEach(parentGroupId -> {
            List<Integer> sonGroupIds = sonGroupIdMap.get(parentGroupId);
            sonGroupIds.forEach(sonGroupId -> {
                KaBelongIngDTO dto = new KaBelongIngDTO();
                dto.setMappingType(KaBelongingMappingTypeEnum.GROUP);
                dto.setConfigLevel(KaBelongingConfigLevelEnum.SKA);
                dto.setSkaSubTag(groupSkaMap.get(parentGroupId).getSkaSubTag());
                dto.setMappingId(sonGroupId);
                skaList.add(dto);
            });
        });
        skaList.addAll(groupSkaList);
        skaList.addAll(productSkaList);
        return skaList;
    }

    public void buildSkaDashBoard(Timestamp dateBegin, Timestamp dateEnd) {
        List<KaBelongIngDTO> skaList = buildKaBelongIngDTOList(dateEnd);
        List<Integer> kpiAllGroupIdList = skaList.stream().filter(e -> Objects.equals(e.getMappingType(), KaBelongingMappingTypeEnum.GROUP)).map(KaBelongIngDTO::getMappingId).collect(Collectors.toList());
        List<AccCompanyGroupPo> allGroups = accCompanyGroupRepo.queryListByIds(kpiAllGroupIdList);
        Map<Integer, AccCompanyGroupPo> allGroupMap = allGroups.stream().collect(Collectors.toMap(AccCompanyGroupPo::getId, Function.identity()));
        //单集团
        List<KaBelongIngDTO> skaGroupByGroupList = skaList.stream().filter(e -> Objects.equals(e.getMappingType(), KaBelongingMappingTypeEnum.GROUP)).filter(e -> !Objects.equals(e.getSkaSubTag(), "品牌拆分")).collect(Collectors.toList());
        List<Integer> skaGroupByGroupIdList = skaGroupByGroupList.stream().map(KaBelongIngDTO::getMappingId).collect(Collectors.toList());
        Map<Integer, String> groupIdAndTagMap = getSkaGroupIdAndTagMap(skaGroupByGroupList, allGroupMap);
        //集团加品牌
        Map<Integer, List<Integer>> skaGroupAndProductMap = getGroupIdAndProductIdMap(skaList);
        Map<Integer, String> productIdAndTagMap = skaList.stream().filter(e -> Objects.equals(e.getMappingType(), KaBelongingMappingTypeEnum.PRODUCT)).collect(Collectors.toMap(KaBelongIngDTO::getMappingId, KaBelongIngDTO::getSkaSubTag, (a, b) -> b));
        List<AdsCrmCommerceKpiAmountDataADPO> kpiAllList = getAllKpiListBySkaList(skaGroupByGroupIdList, skaGroupAndProductMap, groupIdAndTagMap, productIdAndTagMap, dateBegin, dateEnd, Utils.getYesteday());
        if (CollectionUtils.isEmpty(kpiAllList)) {
            log.info("getAllKpiListBySkaList_is_null,dateBegin={},dateEnd={},今日业绩宽表kpi数据还没有准备好，请稍后手动执行一下线上定时任务 CrmSkaGroupOverviewJob 生成SKA看板数据。", dateBegin, dateEnd);
            return;
        }
        LocalDateTime localDateTimeBegin = dateBegin.toLocalDateTime();
        LocalDateTime localDateTimeEnd = dateEnd.toLocalDateTime();
        LocalDateTime oneYearAgoQuarterBegin = localDateTimeBegin.minusYears(1);
        LocalDateTime oneYearAgoQuarterEnd = localDateTimeEnd.minusYears(1);
        List<AdsCrmCommerceKpiAmountDataADPO> kpiBeforeAllList = getAllKpiListBySkaList(skaGroupByGroupIdList, skaGroupAndProductMap, groupIdAndTagMap, productIdAndTagMap, Timestamp.valueOf(oneYearAgoQuarterBegin), Timestamp.valueOf(oneYearAgoQuarterEnd), Utils.getYesteday());
        buildEmptyKpiData(kpiAllList, skaGroupByGroupIdList, groupIdAndTagMap, dateBegin, dateEnd);
        fillOtherBrandInfo(kpiAllList);
        Map<Integer, List<Integer>> groupSaleMap = accCompanyGroupRepo.getSaleIdMapByGroupIdList(kpiAllGroupIdList, dateEnd);
        Map<String, Integer> tagAndsaleGroupMap = getTagAndsaleGroupMap();
        Map<Integer, SaleDto> allSaleMap = saleService.getAllSaleMapWithCache();
        //是否更新架构
        Map<Integer, Integer> groupProfileMap = getGroupProfileFlagMap(kpiAllGroupIdList);
        //集团联系人
        List<CustomerPo> customerPoList = getCustomerPoListByGroupIds(kpiAllGroupIdList);
        List<CrmContactorPo> crmContactorPos = getCrmContactorPoListByCustomerIds(customerPoList);
        Map<Integer, List<CrmContactorPo>> crmContactorPosByOrgMap = getCrmContactorPoListByGroupIds(kpiAllGroupIdList);
        Map<Integer, Integer> groupContactHaveMap = getGroupHaveContactMap(kpiAllGroupIdList, customerPoList, crmContactorPos, crmContactorPosByOrgMap);
        Map<Integer, Integer> groupContactFlagMap = getGroupAllContactorRepeatMap(kpiAllGroupIdList, customerPoList, crmContactorPos, crmContactorPosByOrgMap);
        //本季度的商机数
        Map<Integer, Integer> groupBsiCountMap = getGroupQuarterBsiCountMap(kpiAllGroupIdList, skaGroupByGroupIdList, skaGroupAndProductMap);
        //本季度的跟进记录
        Map<Integer, Integer> groupRecordMap = getGroupQuarterRecordMap(kpiAllGroupIdList);
        //签框进展和签框金额和签框完成金额
        Map<Integer, GroupSignBoxConfigDTO> groupSignProcessMap = getGroupSignProcessMap(kpiAllGroupIdList, dateBegin);
        //所有目标
        Map<String, CrmSkaTargetMoneyConfigPo> allTargetMoneyMap = getAllTargetMoney();
        List<CrmSkaDashboardItemPo> dataList = new ArrayList<>();
        kpiAllGroupIdList.forEach(groupId -> dataList.addAll(buildGroupSKAOverviewItemPO(kpiAllList, kpiBeforeAllList, String.valueOf(groupId), allGroupMap, tagAndsaleGroupMap, groupSaleMap, allSaleMap, groupProfileMap, groupContactHaveMap, groupContactFlagMap, groupBsiCountMap, groupRecordMap, groupSignProcessMap, allTargetMoneyMap)));
        if (CollectionUtils.isNotEmpty(dataList)) {
            dataList.forEach(d -> crmSkaDashboardItemDao.insertSelective(d));
        }
        log.info("buildSkaDashBoard,success_is_count=" + dataList.size());
    }

    public void buildEmptyKpiData(List<AdsCrmCommerceKpiAmountDataADPO> kpiList,
                                  List<Integer> skaGroupByGroupIdList,
                                  Map<Integer, String> groupIdAndTagMap,
                                  Timestamp dateBegin, Timestamp dateEnd) {
        String queryDate = CrmUtils.formatDate(dateBegin, CrmUtils.YYYYMMDD_WITHOUT_SPLIT) + "_" + CrmUtils.formatDate(dateEnd, CrmUtils.YYYYMMDD_WITHOUT_SPLIT);
        List<Integer> haveGroupIds = kpiList.stream().map(AdsCrmCommerceKpiAmountDataADPO::getGroupId).map(Integer::valueOf).distinct().collect(Collectors.toList());
        List<Integer> notGroupIds = skaGroupByGroupIdList.stream().filter(e -> !haveGroupIds.contains(e)).distinct().collect(Collectors.toList());
        notGroupIds.forEach(t -> {
            AdsCrmCommerceKpiAmountDataADPO adPo = new AdsCrmCommerceKpiAmountDataADPO();
            adPo.setGroupId(t.toString());
            adPo.setSkaSubTag(groupIdAndTagMap.get(t));
            adPo.setSkaTagSource("子标签来源集团,集团id=" + t);
            adPo.setQueryDate(queryDate);
            adPo.setKpiAmount(0.0);
            kpiList.add(adPo);
        });
    }


    public Map<Integer, String> getSkaGroupIdAndTagMap(List<KaBelongIngDTO> skaGroupByGroupList, Map<Integer, AccCompanyGroupPo> allGroupMap) {
        Map<Integer, String> groupIdAndTagMap = skaGroupByGroupList.stream().collect(Collectors.toMap(KaBelongIngDTO::getMappingId, KaBelongIngDTO::getSkaSubTag, (a, b) -> b));
        groupIdAndTagMap.keySet().forEach(key -> {
            String value = groupIdAndTagMap.getOrDefault(key, "");
            if (StringUtils.isBlank(value)) {
                String defaultValue = allGroupMap.get(key).getName();
                groupIdAndTagMap.put(key, defaultValue);
            }
        });
        return groupIdAndTagMap;
    }

    public Map<Integer, List<Integer>> getGroupIdAndProductIdMap(List<KaBelongIngDTO> skaList) {
        //集团加品牌
        List<Integer> skaGroupByProductIdList = skaList.stream().filter(e -> Objects.equals(e.getMappingType(), KaBelongingMappingTypeEnum.GROUP)).filter(e -> Objects.equals(e.getSkaSubTag(), "品牌拆分")).map(KaBelongIngDTO::getMappingId).collect(Collectors.toList());
        List<KaBelongIngDTO> skaProductList = skaList.stream().filter(e -> Objects.equals(e.getMappingType(), KaBelongingMappingTypeEnum.PRODUCT)).filter(e -> StringUtils.isNotBlank(e.getSkaSubTag())).collect(Collectors.toList());
        Map<Integer, List<Integer>> groupIdAndproductIdMap = accCompanyGroupRepo.getProductIdMapByGroupIdList(skaGroupByProductIdList);
        List<Integer> haveSkaProductIds = skaProductList.stream().map(KaBelongIngDTO::getMappingId).collect(Collectors.toList());
        return groupIdAndproductIdMap.entrySet().stream()
                .map(entry -> new AbstractMap.SimpleEntry<>(entry.getKey(),
                        entry.getValue().stream().filter(haveSkaProductIds::contains).collect(Collectors.toList())))
                .filter(entry -> !entry.getValue().isEmpty())
                .collect(Collectors.toMap(AbstractMap.SimpleEntry::getKey, AbstractMap.SimpleEntry::getValue));
    }

    public List<AdsCrmCommerceKpiAmountDataADPO> getAllKpiListBySkaList(List<Integer> skaGroupByGroupIdList,
                                                                        Map<Integer, List<Integer>> skaGroupAndProductMap,
                                                                        Map<Integer, String> groupIdAndTagMap,
                                                                        Map<Integer, String> productIdAndTagMap,
                                                                        Timestamp dateBegin, Timestamp dateEnd,
                                                                        Timestamp logDate) {
        AdsCrmCommerceKpiAmountDataADQueryParam queryParam = new AdsCrmCommerceKpiAmountDataADQueryParam();
        queryParam.setIsInner(String.valueOf(IsInnerEnum.OUTER.getCode()));
        queryParam.setLogDate(CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD));
        queryParam.setBusAccountDateBegin(CrmUtils.formatDate(dateBegin, CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
        queryParam.setBusAccountDateEnd(CrmUtils.formatDate(dateEnd, CrmUtils.YYYYMMDD_WITHOUT_SPLIT));
        queryParam.setAmountTypeList(Lists.newArrayList("已计收", "已完成", "已交付"));
        List<AdsCrmCommerceKpiAmountDataADPO> kpiGroupList = new ArrayList<>();
        List<AdsCrmCommerceKpiAmountDataADPO> kpiProductList = new ArrayList<>();
        String queryDate = CrmUtils.formatDate(dateBegin, CrmUtils.YYYYMMDD_WITHOUT_SPLIT) + "_" + CrmUtils.formatDate(dateEnd, CrmUtils.YYYYMMDD_WITHOUT_SPLIT);
        //单集团
        if (CollectionUtils.isNotEmpty(skaGroupByGroupIdList)) {
            queryParam.setGroupIdList(skaGroupByGroupIdList);
            kpiGroupList = accCompanyGroupRepo.queryCrmSKAGroupKpiAmountDataList(queryParam);
            //构建tag
            kpiGroupList.forEach(u -> {
                u.setSkaSubTag(groupIdAndTagMap.get(Integer.valueOf(u.getGroupId())));
                u.setSkaTagSource("子标签来源集团,集团id=" + u.getGroupId());
                u.setQueryDate(queryDate);
            });
        }
        //集团加品牌
        if (Objects.nonNull(skaGroupAndProductMap) && skaGroupAndProductMap.size() > 0) {
            queryParam.setGroupIdList(new ArrayList<>(skaGroupAndProductMap.keySet()));
            queryParam.setProductIdList(skaGroupAndProductMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
            kpiProductList = accCompanyGroupRepo.queryCrmSKAGroupKpiAmountDataList(queryParam);
            kpiProductList.forEach(u -> {
                u.setSkaSubTag(productIdAndTagMap.get(Integer.valueOf(u.getProductId())));
                u.setSkaTagSource("子标签来源品牌,品牌id=" + u.getProductId());
                u.setQueryDate(queryDate);
            });
        }
        List<AdsCrmCommerceKpiAmountDataADPO> kpiAllList = new ArrayList<>();
        kpiAllList.addAll(kpiGroupList);
        kpiAllList.addAll(kpiProductList);
        return kpiAllList;
    }

    public void fillOtherBrandInfo(List<AdsCrmCommerceKpiAmountDataADPO> poList) {
        poList.forEach(e -> {
            if (Objects.equals(e.getProductFirstCategoryType(), "其他") && AdsCrmCommerceKpiAmountDataADPO.brandOtherListSource.contains(e.getBillSource())) {
                e.setProductFirstCategoryType("品牌");
            }
        });
    }

    public void fillSkaSubTagByProduct(List<AdsCrmCommerceKpiAmountDataADPO> poList, List<KaBelongIngDTO> skaList, Map<Integer, AccCompanyGroupPo> allGroupMap) {
        StringBuffer infoStr = new StringBuffer();
        Map<Integer, String> skaSubTagProductMap = skaList.stream().collect(Collectors.toMap(KaBelongIngDTO::getMappingId, KaBelongIngDTO::getSkaSubTag, (o, n) -> n));
        poList.forEach(e -> {
            String skaSubTag = skaSubTagProductMap.get(Integer.valueOf(e.getProductId()));
            String skaTagSource = "0_" + e.getProductId();
            if (StringUtils.isBlank(skaSubTag)) {
                String groupName = allGroupMap.getOrDefault(Integer.valueOf(e.getGroupId()), AccCompanyGroupPo.builder().name("").build()).getName();
                if (StringUtils.isBlank(groupName)) {
                    infoStr.append(e.getAmountId()).append(",");
                }
            } else {
                e.setSkaSubTag(skaSubTag);
                e.setSkaTagSource(skaTagSource);
            }
        });
        if (StringUtils.isNotBlank(infoStr.toString())) {
            log.info("ska_overview,fillSkaSubTag_not_groupInfo,amountIds={}", infoStr);
        }
    }


    public Map<Integer, Integer> getGroupProfileFlagMap(List<Integer> kpiAllGroupIdList) {
        if (CollectionUtils.isEmpty(kpiAllGroupIdList)) {
            return new HashMap<>();
        }
        CrmGroupOrganizationPoExample example = new CrmGroupOrganizationPoExample();
        example.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andSuperiorOrgIdEqualTo(0L).andGroupIdIn(kpiAllGroupIdList);
        List<CrmGroupOrganizationPo> poList = crmGroupOrganizationDao.selectByExample(example);
        List<Integer> groupIds = poList.stream().map(CrmGroupOrganizationPo::getGroupId).distinct().collect(Collectors.toList());
        Map<Integer, Integer> result = new HashMap<>();
        kpiAllGroupIdList.forEach(e -> {
            if (groupIds.contains(e)) {
                result.put(e, 1);
            } else {
                result.put(e, 0);
            }
        });
        return result;
    }

    public Map<Integer, Integer> getGroupQuarterBsiCountMap(List<Integer> kpiAllGroupIdList, List<Integer> skaGroupByGroupIdList, Map<Integer, List<Integer>> skaGroupAndProductMap) {
        if (CollectionUtils.isEmpty(kpiAllGroupIdList)) {
            return new HashMap<>();
        }
        if (CollectionUtils.isEmpty(skaGroupByGroupIdList) && skaGroupAndProductMap.isEmpty()) {
            return new HashMap<>();
        }
        Timestamp quarterBegin = CrmUtils.getQuarterBeginDate(Utils.getYesteday());
        Timestamp quarterEnd = Utils.getEndOfDay(CrmUtils.getQuarterEndDate(Utils.getYesteday()));
        BsiOpportunityPoExample example1 = new BsiOpportunityPoExample();
        if (!CollectionUtils.isEmpty(skaGroupByGroupIdList)) {
            example1.or().andFollowStageIn(FollowStage.getTenAndNinetyStatus())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCtimeGreaterThanOrEqualTo(quarterBegin)
                    .andCtimeLessThanOrEqualTo(quarterEnd)
                    .andGroupIdIn(skaGroupByGroupIdList);
        }
        if (Objects.nonNull(skaGroupAndProductMap) && skaGroupAndProductMap.size() > 0) {
            example1.or().andFollowStageIn(FollowStage.getTenAndNinetyStatus())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCtimeGreaterThanOrEqualTo(quarterBegin)
                    .andCtimeLessThanOrEqualTo(quarterEnd)
                    .andGroupIdIn(new ArrayList<>(skaGroupAndProductMap.keySet()))
                    .andProductIdIn(skaGroupAndProductMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }
        List<BsiOpportunityPo> poList1 = bsiOpportunityDao.selectByExample(example1);

        BsiOpportunityPoExample example2 = new BsiOpportunityPoExample();
        if (!CollectionUtils.isEmpty(skaGroupByGroupIdList)) {
            example2.or().andFollowStageEqualTo(FollowStage.COMPLETE_ORDER.getCode())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCtimeGreaterThanOrEqualTo(quarterBegin)
                    .andCtimeLessThanOrEqualTo(quarterEnd)
                    .andGroupIdIn(skaGroupByGroupIdList);
        }
        if (Objects.nonNull(skaGroupAndProductMap) && skaGroupAndProductMap.size() > 0) {
            example2.or().andFollowStageEqualTo(FollowStage.COMPLETE_ORDER.getCode())
                    .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                    .andCtimeGreaterThanOrEqualTo(quarterBegin)
                    .andCtimeLessThanOrEqualTo(quarterEnd)
                    .andGroupIdIn(new ArrayList<>(skaGroupAndProductMap.keySet()))
                    .andProductIdIn(skaGroupAndProductMap.values().stream().flatMap(List::stream).collect(Collectors.toList()));
        }
        List<BsiOpportunityPo> poList2 = bsiOpportunityDao.selectByExample(example2);

        if (CollectionUtils.isNotEmpty(poList2)) {
            Map<Integer, List<Integer>> haveContractBisMap = bsiOpportunityContractService.getBsiContractMap(BsiContractQueryDto.builder().bsiOpportunityIds(poList2.stream().map(BsiOpportunityPo::getId).collect(Collectors.toList())).build());
            poList2 = poList2.stream().filter(e -> !haveContractBisMap.containsKey(e.getId())).collect(Collectors.toList());
        }

        List<Integer> bsiIds = poList1.stream().map(BsiOpportunityPo::getId).collect(Collectors.toList());
        bsiIds.addAll(poList2.stream().map(BsiOpportunityPo::getId).collect(Collectors.toList()));

        //品牌商机意向产品
        List<BsiOpportunityIntentProductMappingDto> bsiProductList = bsiOpportunityIntentProductService.getBsiIntentProductMapping(BsiIntentProductQueryDto.builder()
                .bsiOpportunityIds(bsiIds).build());
        bsiProductList = bsiProductList.stream().filter(e -> {
            long sum = 0L;
            if (Objects.nonNull(e.getQuarterAmount())) {
                sum += e.getQuarterAmount();
            }
            if (Objects.nonNull(e.getOtherQuarterAmount())) {
                sum += e.getOtherQuarterAmount();
            }
            if (Objects.nonNull(e.getTotalAmount())) {
                sum += e.getTotalAmount();
            }
            if (sum > 100) {
                return true;
            }
            return false;
        }).collect(Collectors.toList());

        List<Integer> filterBsiIds = bsiProductList.stream().map(BsiOpportunityIntentProductMappingDto::getBsiOpportunityId).distinct().collect(Collectors.toList());
        poList1.addAll(poList2);
        poList1 = poList1.stream().filter(e -> filterBsiIds.contains(e.getId())).collect(Collectors.toList());
        log.info("getGroupQuarterBsiCountMap,所有的商机ids=" + JSON.toJSONString(poList1.stream().map(BsiOpportunityPo::getId).collect(Collectors.toList())));
        List<Integer> customerIds = poList1.stream().map(BsiOpportunityPo::getCustomerId).filter(Utils::isPositive).distinct().collect(Collectors.toList());
        Map<Integer, Integer> customerIdGroupIdMap = getCustomerIdGroupMap(customerIds);
        Map<Integer, Integer> result = new HashMap<>();
        kpiAllGroupIdList.forEach(e -> result.put(e, 0));
        poList1.forEach(e -> {
            Integer customerGroupId = customerIdGroupIdMap.get(e.getCustomerId());
            if (result.containsKey(customerGroupId)) {
                log.info("result_商机id={},属于客户id={},属于集团id={}", e.getId(), e.getCustomerId(), customerGroupId);
                result.put(customerGroupId, result.get(customerGroupId) + 1);
            }
        });
        return result;
    }

    /**
     * 含有冻结的
     */
    public Map<Integer, Integer> getCustomerIdGroupMap(List<Integer> customerIds) {
        Map<Integer, Integer> customerIdGroupIdMap = new HashMap<>();
        if (CollectionUtils.isNotEmpty(customerIds)) {
            CustomerPoExample exampleCustomer = new CustomerPoExample();
            exampleCustomer.createCriteria().andIdIn(customerIds);
            List<CustomerPo> customerPoList = customerDao.selectByExample(exampleCustomer);
            customerIdGroupIdMap = customerPoList.stream().collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getGroupId));
        }
        return customerIdGroupIdMap;
    }

    public List<CustomerPo> getCustomerPoListByGroupIds(List<Integer> kpiAllGroupIdList) {
        if (CollectionUtils.isEmpty(kpiAllGroupIdList)) {
            return Collections.emptyList();
        }
        CustomerPoExample poExample = new CustomerPoExample();
        poExample.createCriteria().andGroupIdIn(kpiAllGroupIdList).andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return customerDao.selectByExample(poExample);
    }

    public List<CrmContactorPo> getCrmContactorPoListByCustomerIds(List<CustomerPo> customerIds) {
        if (CollectionUtils.isEmpty(customerIds)) {
            return Collections.emptyList();
        }
        CrmContactorPoExample crmContactorPoExample = new CrmContactorPoExample();
        crmContactorPoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andCustomerIdIn(customerIds.stream().map(CustomerPo::getId).collect(Collectors.toList()))
                .andIsEnableEqualTo(IsDeleted.VALID.getCode());
        return crmContactorDao.selectByExample(crmContactorPoExample);
    }

    //没有customerId的联系人
    public Map<Integer, List<CrmContactorPo>> getCrmContactorPoListByGroupIds(List<Integer> kpiAllGroupIdList) {
        if (CollectionUtils.isEmpty(kpiAllGroupIdList)) {
            return new HashMap<>();
        }
        CrmGroupOrganizationPoExample exampleOrg = new CrmGroupOrganizationPoExample();
        exampleOrg.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andGroupIdIn(kpiAllGroupIdList);
        List<CrmGroupOrganizationPo> orgPoList = crmGroupOrganizationDao.selectByExample(exampleOrg);
        Map<Integer, List<Long>> groupIdAndOrgIdMap = orgPoList.stream().collect(Collectors.groupingBy(CrmGroupOrganizationPo::getGroupId,
                Collectors.mapping(CrmGroupOrganizationPo::getId, Collectors.toList())));

        CrmContactorPoExample crmContactorPoExample = new CrmContactorPoExample();
        crmContactorPoExample.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andOrgIdIn(orgPoList.stream().map(CrmGroupOrganizationPo::getId).collect(Collectors.toList()))
                .andCustomerIdEqualTo(0)
                .andIsEnableEqualTo(IsDeleted.VALID.getCode());
        List<CrmContactorPo> crmContactorPoList = crmContactorDao.selectByExample(crmContactorPoExample);
        Map<Integer, List<CrmContactorPo>> result = new HashMap<>();
        for (Map.Entry<Integer, List<Long>> m : groupIdAndOrgIdMap.entrySet()) {
            List<CrmContactorPo> filterPoList = crmContactorPoList.stream().filter(e -> m.getValue().contains(e.getOrgId())).collect(Collectors.toList());
            if (CollectionUtils.isNotEmpty(filterPoList)) {
                result.put(m.getKey(), filterPoList);
            }
        }
        return result;
    }

    public Map<Integer, Integer> getGroupHaveContactMap(List<Integer> kpiAllGroupIdList, List<CustomerPo> customerPoList, List<CrmContactorPo> crmContactorPos, Map<Integer, List<CrmContactorPo>> crmContactorPosByOrgMap) {
        Map<Integer, Integer> result = new HashMap<>();
        kpiAllGroupIdList.forEach(e -> result.put(e, 0));
        crmContactorPosByOrgMap.keySet().forEach(e -> result.put(e, 1));
        if (CollectionUtils.isEmpty(kpiAllGroupIdList) || CollectionUtils.isEmpty(customerPoList) || CollectionUtils.isEmpty(crmContactorPos)) {
            return result;
        }
        List<Integer> haveContactorCustomerIds = crmContactorPos.stream().map(CrmContactorPo::getCustomerId).distinct().collect(Collectors.toList());
        Map<Integer, Integer> customerIdGroupIdMap = customerPoList.stream().collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getGroupId));
        haveContactorCustomerIds.forEach(e -> {
            Integer customerGroupId = customerIdGroupIdMap.get(e);
            if (result.containsKey(customerGroupId)) {
                result.put(customerGroupId, 1);
            }
        });
        return result;
    }

    public Map<Integer, Integer> getGroupAllContactorRepeatMap(List<Integer> kpiAllGroupIdList, List<CustomerPo> customerPoList, List<CrmContactorPo> crmContactorPos, Map<Integer, List<CrmContactorPo>> crmContactorPosByOrgMap) {
        Map<Integer, Integer> result = new HashMap<>();
        kpiAllGroupIdList.forEach(e -> result.put(e, 0));
        if (CollectionUtils.isEmpty(kpiAllGroupIdList) || CollectionUtils.isEmpty(customerPoList) || (CollectionUtils.isEmpty(crmContactorPos) && crmContactorPosByOrgMap.isEmpty())) {
            return result;
        }
        //手机号与集团id列表的Map
        Map<String, List<Integer>> phoneNumberToGroupIdList = new HashMap<>();
        //集团id与手机号列表的Map
        Map<Integer, List<String>> groupIdToPhoneNumberList = new HashMap<>();
        Map<Integer, Integer> customerIdToGroupId = customerPoList.stream().collect(Collectors.toMap(CustomerPo::getId, CustomerPo::getGroupId));
        for (CrmContactorPo contactorPo : crmContactorPos) {
            Integer customerId = contactorPo.getCustomerId();
            String phoneNumber = contactorPo.getContactorPhone();
            Integer groupId = customerIdToGroupId.get(customerId);
            if (Utils.isPositive(groupId) && Utils.isPositive(customerId) && StringUtils.isNotBlank(phoneNumber) && !phoneNumberToGroupIdList.computeIfAbsent(phoneNumber, k -> new ArrayList<>()).contains(groupId)) {
                phoneNumberToGroupIdList.get(phoneNumber).add(groupId);
            }
            if (Utils.isPositive(groupId) && Utils.isPositive(customerId) && StringUtils.isNotBlank(phoneNumber) && !groupIdToPhoneNumberList.computeIfAbsent(groupId, k -> new ArrayList<>()).contains(phoneNumber)) {
                groupIdToPhoneNumberList.get(groupId).add(phoneNumber);
            }
        }
        for (Map.Entry<Integer, List<CrmContactorPo>> m : crmContactorPosByOrgMap.entrySet()) {
            Integer groupId = m.getKey();
            m.getValue().forEach(p -> {
                String phoneNumber = p.getContactorPhone();
                if (StringUtils.isNotBlank(phoneNumber) && !phoneNumberToGroupIdList.computeIfAbsent(phoneNumber, k -> new ArrayList<>()).contains(groupId)) {
                    phoneNumberToGroupIdList.get(phoneNumber).add(groupId);
                }
                if (StringUtils.isNotBlank(phoneNumber) && !groupIdToPhoneNumberList.computeIfAbsent(groupId, k -> new ArrayList<>()).contains(phoneNumber)) {
                    groupIdToPhoneNumberList.get(groupId).add(phoneNumber);
                }
            });
        }
        kpiAllGroupIdList.forEach(groupId -> {
            List<String> phoneNumberList = groupIdToPhoneNumberList.get(groupId);
            if (CollectionUtils.isEmpty(phoneNumberList)) {
                return;
            }
            int repeat = 0;
            List<String> filterPhoneNumberList = phoneNumberList.stream().filter(phone -> phoneNumberToGroupIdList.getOrDefault(phone, Collections.emptyList()).size() > 1).collect(Collectors.toList());
            if (filterPhoneNumberList.size() == phoneNumberList.size()) {
                repeat = 1;
                log.info("getGroupAllContactorRepeatMap_repeat_info,集团id={},重复的电话号码={},每个电话依次属于集团id={}", groupId, JSON.toJSONString(phoneNumberList),
                        JSON.toJSONString(phoneNumberList.stream().map(phone -> phoneNumberToGroupIdList.getOrDefault(phone, Collections.emptyList())).collect(Collectors.toList())));
            }
            result.put(groupId, repeat);
        });
        return result;
    }

    public Map<Integer, Integer> getGroupQuarterRecordMap(List<Integer> kpiAllGroupIdList) {
        Map<Integer, Integer> result = new HashMap<>();
        kpiAllGroupIdList.forEach(e -> result.put(e, 0));
        Timestamp quarterBegin = CrmUtils.getQuarterBeginDate(Utils.getYesteday());
        Timestamp quarterEnd = CrmUtils.getQuarterEndDate(Utils.getYesteday());
        CrmFollowManageRecordPoExample recordPoExample = new CrmFollowManageRecordPoExample();
        recordPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andFollowTimeGreaterThanOrEqualTo(quarterBegin)
                .andFollowTimeLessThanOrEqualTo(quarterEnd);
        List<CrmFollowManageRecordPo> recordList = crmFollowManageRecordDao.selectByExample(recordPoExample);
        if (CollectionUtils.isEmpty(recordList)) {
            return result;
        }
        Map<Long, List<CrmFollowManageRecordPo>> recordMap = recordList.stream().collect(Collectors.groupingBy(CrmFollowManageRecordPo::getCustomerRecordId));
        CrmFollowManageCustomerPoExample customerPoExample = new CrmFollowManageCustomerPoExample();
        customerPoExample.createCriteria().andIdIn(new ArrayList<>(recordMap.keySet())).andBizTypeEqualTo(CustomerBizType.CUSTOMER.getCode());
        List<CrmFollowManageCustomerPo> customerPoList = crmFollowManageCustomerDao.selectByExample(customerPoExample);
        Map<Long, Long> customerPoMap = customerPoList.stream().collect(Collectors.toMap(CrmFollowManageCustomerPo::getId, CrmFollowManageCustomerPo::getCustomerId, (o1, o2) -> o1));
        Map<Integer, Integer> customerIdGroupIdMap = getCustomerIdGroupMap(customerPoMap.values().stream().map(Long::intValue).collect(Collectors.toList()));
        recordList.forEach(e -> {
            Long customerId = customerPoMap.get(e.getCustomerRecordId());
            if (Objects.isNull(customerId)) {
                return;
            }
            Integer customerGroupId = customerIdGroupIdMap.get(customerId.intValue());
            if (Objects.isNull(customerGroupId)) {
                return;
            }
            if (result.containsKey(customerGroupId)) {
                log.info("result_记录id={},属于客户id={},客户记录表主键id={},属于集团id={}", e.getId(), customerId, e.getCustomerRecordId(), customerGroupId);
                result.put(customerGroupId, result.get(customerGroupId) + 1);
            }
        });
        return result;
    }

    public Map<Integer, GroupSignBoxConfigDTO> getGroupSignProcessMap(List<Integer> kpiAllGroupIdList, Timestamp dateBegin) {
        Map<Integer, GroupSignBoxConfigDTO> result = new HashMap<>();
        List<GroupSignBoxConfigDTO> signBoxConfigDTOList = corporationGroupService.getAllGroupSignBoxConfigByType(0);
        Map<Integer, GroupSignBoxConfigDTO> signBoxConfigDTOMap = signBoxConfigDTOList.stream().collect(Collectors.toMap(GroupSignBoxConfigDTO::getSignGroupId, e -> e, (o, n) -> n));
        kpiAllGroupIdList.forEach(e -> result.put(e, signBoxConfigDTOMap.getOrDefault(e, GroupSignBoxConfigDTO.builder().signProcess("-").signMoney(0L).signCompleteMoney(0L).build())));
        return result;
    }

    public Map<String, CrmSkaTargetMoneyConfigPo> getAllTargetMoney() {
        Map<String, CrmSkaTargetMoneyConfigPo> result = new HashMap<>();
        CrmSkaTargetMoneyConfigPoExample targetExample = new CrmSkaTargetMoneyConfigPoExample();
        targetExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        List<CrmSkaTargetMoneyConfigPo> targetPoList = crmSkaTargetMoneyConfigDao.selectByExample(targetExample);
        targetPoList.forEach(e -> {
            if (Objects.equals(e.getSkaTargetType(), 0)) {
                result.put(e.getSkaTargetType() + "_" + e.getSkaGroupId() + "_" + e.getSkaTargetTime(), e);
            } else {
                result.put(e.getSkaTargetType() + "_" + e.getSkaTargetName() + "_" + e.getSkaTargetTime(), e);
            }
        });
        return result;
    }

    public List<CrmSkaDashboardItemPo> buildGroupSKAOverviewItemPO(List<AdsCrmCommerceKpiAmountDataADPO> poList,
                                                                   List<AdsCrmCommerceKpiAmountDataADPO> kpiBeforeAllList,
                                                                   String groupId,
                                                                   Map<Integer, AccCompanyGroupPo> allGroupMap,
                                                                   Map<String, Integer> tagAndsaleGroupMap,
                                                                   Map<Integer, List<Integer>> groupSaleMap,
                                                                   Map<Integer, SaleDto> allSaleMap,
                                                                   Map<Integer, Integer> groupProfileMap,
                                                                   Map<Integer, Integer> groupContactHaveMap,
                                                                   Map<Integer, Integer> groupContactFlagMap,
                                                                   Map<Integer, Integer> groupBsiCountMap,
                                                                   Map<Integer, Integer> groupRecordMap,
                                                                   Map<Integer, GroupSignBoxConfigDTO> groupSignProcessMap,
                                                                   Map<String, CrmSkaTargetMoneyConfigPo> allTargetMoneyMap
    ) {
        List<AdsCrmCommerceKpiAmountDataADPO> filterGroupIdList = poList.stream().filter(e -> Objects.equals(e.getGroupId(), groupId)).collect(Collectors.toList());
        List<AdsCrmCommerceKpiAmountDataADPO> filterBeforeGroupIdList = kpiBeforeAllList.stream().filter(e -> Objects.equals(e.getGroupId(), groupId)).collect(Collectors.toList());
        if (CollectionUtils.isEmpty(filterGroupIdList)) {
            return new ArrayList<>();
        }
        Map<String, List<AdsCrmCommerceKpiAmountDataADPO>> skaSubTagMap = filterGroupIdList.stream().filter(e -> StringUtils.isNotBlank(e.getSkaSubTag())).collect(Collectors.groupingBy(AdsCrmCommerceKpiAmountDataADPO::getSkaSubTag));
        Map<String, List<AdsCrmCommerceKpiAmountDataADPO>> skaBeforeSubTagMap = filterBeforeGroupIdList.stream().filter(e -> StringUtils.isNotBlank(e.getSkaSubTag())).collect(Collectors.groupingBy(AdsCrmCommerceKpiAmountDataADPO::getSkaSubTag));
        List<CrmSkaDashboardItemPo> result = new ArrayList<>();
        Integer groupIdInt = Integer.valueOf(groupId);
        String queryDate = filterGroupIdList.get(0).getQueryDate();
        skaSubTagMap.keySet().forEach(tag -> {
            Long quarterTargetMoney = allTargetMoneyMap.getOrDefault("1_" + tag + "_" + CrmUtils.getQuarterDescByTime(CrmUtils.getString2Timestamp(queryDate.split("_")[1], CrmUtils.YYYYMMDD_WITHOUT_SPLIT), "-"), CrmSkaTargetMoneyConfigPo.builder().skaTargetMoney(0L).build()).getSkaTargetMoney();
            Long yearTargetMoney = allTargetMoneyMap.getOrDefault("1_" + tag + "_" + queryDate.substring(0, 4) + "-Q1", CrmSkaTargetMoneyConfigPo.builder().skaTargetMoney(0L).build()).getSkaTargetMoney() +
                    allTargetMoneyMap.getOrDefault("1_" + tag + "_" + queryDate.substring(0, 4) + "-Q2", CrmSkaTargetMoneyConfigPo.builder().skaTargetMoney(0L).build()).getSkaTargetMoney() +
                    allTargetMoneyMap.getOrDefault("1_" + tag + "_" + queryDate.substring(0, 4) + "-Q3", CrmSkaTargetMoneyConfigPo.builder().skaTargetMoney(0L).build()).getSkaTargetMoney() +
                    allTargetMoneyMap.getOrDefault("1_" + tag + "_" + queryDate.substring(0, 4) + "-Q4", CrmSkaTargetMoneyConfigPo.builder().skaTargetMoney(0L).build()).getSkaTargetMoney();
            List<Integer> saleList = groupSaleMap.getOrDefault(groupIdInt, Collections.emptyList());
            double kpiAmountAll = skaSubTagMap.get(tag).stream().map(AdsCrmCommerceKpiAmountDataADPO::getKpiAmount).reduce(0.0, Double::sum);
            double kpiBeforeAmountAll = skaBeforeSubTagMap.getOrDefault(tag, Collections.emptyList()).stream().map(AdsCrmCommerceKpiAmountDataADPO::getKpiAmount).reduce(0.0, Double::sum);
            double kpiBrandAmountAll = skaSubTagMap.get(tag).stream().filter(e -> Objects.equals(e.getProductFirstCategoryType(), "品牌")).map(AdsCrmCommerceKpiAmountDataADPO::getKpiAmount).reduce(0.0, Double::sum);
            double kpiRtbAmountAll = skaSubTagMap.get(tag).stream().filter(e -> Objects.equals(e.getProductFirstCategoryType(), "效果")).map(AdsCrmCommerceKpiAmountDataADPO::getKpiAmount).reduce(0.0, Double::sum);
            double kpiPickAmountAll = skaSubTagMap.get(tag).stream().filter(e -> Objects.equals(e.getProductFirstCategoryType(), "花火")).map(AdsCrmCommerceKpiAmountDataADPO::getKpiAmount).reduce(0.0, Double::sum);
            String skaSubTagSource = skaSubTagMap.get(tag).get(0).getSkaTagSource();
            saleList.forEach(e -> {
                Integer saleGroupId = tagAndsaleGroupMap.get(tag);
                if (Objects.nonNull(saleGroupId)) {
                    CrmSkaDashboardItemPo po = new CrmSkaDashboardItemPo();
                    GroupSignBoxConfigDTO signBoxConfigDTO = groupSignProcessMap.getOrDefault(groupIdInt, GroupSignBoxConfigDTO.builder().build());
                    po.setSaleName(allSaleMap.getOrDefault(e, SaleDto.builder().name("").build()).getName());
                    po.setSaleId(e);
                    po.setSaleGroupId(saleGroupId);
                    po.setSkaSubTag(tag);
                    po.setSkaGroupId(groupIdInt);
                    po.setSkaGroupName(allGroupMap.getOrDefault(groupIdInt, AccCompanyGroupPo.builder().name("").build()).getName());
                    po.setSkaCompleteMoney((long) (kpiAmountAll * 100.0));
                    po.setSkaBrandCompleteMoney((long) (kpiBrandAmountAll * 100.0));
                    po.setSkaRtbCompleteMoney((long) (kpiRtbAmountAll * 100.0));
                    po.setSkaPickupCompleteMoney((long) (kpiPickAmountAll * 100.0));
                    po.setQueryDate(queryDate);
                    po.setSkaSubTagSource(skaSubTagSource);
                    po.setSkaBeforeCompleteMoney((long) (kpiBeforeAmountAll * 100.0));
                    po.setSkaQuarterTarget(quarterTargetMoney);
                    po.setGroupYearTarget(yearTargetMoney);
                    po.setGroupProfileFlag(groupProfileMap.getOrDefault(groupIdInt, 0));
                    po.setGroupContactsFlag(groupContactHaveMap.getOrDefault(groupIdInt, 0));
                    po.setGroupContactsRepeatFlag(groupContactFlagMap.getOrDefault(groupIdInt, 0));
                    po.setGroupBsiCount(groupBsiCountMap.getOrDefault(groupIdInt, 0));
                    po.setGroupVisitCount(groupRecordMap.getOrDefault(groupIdInt, 0));
                    po.setGroupSignMoney(signBoxConfigDTO.getSignMoney());
                    po.setGroupSignProcess(signBoxConfigDTO.getSignProcess());
                    po.setGroupSignCompleteMoney(signBoxConfigDTO.getSignCompleteMoney());
                    po.setIsDeleted(IsDeleted.VALID.getCode());
                    po.setCtime(Utils.getNow());
                    po.setMtime(Utils.getNow());
                    po.setDataEnv(env);
                    result.add(po);
                }
            });
        });
        return result;
    }
}

