package com.bilibili.crm.platform.job.es_migrate.sycpb_new_es.es2hive;

import com.bilibili.adp.common.bean.PageResult;
import com.bilibili.crm.biz.es_migrate.bean.WalletConsumeDayExtendEsBean;
import com.bilibili.crm.biz.es_migrate.bean.WalletConsumeDayExtendQuery;
import com.bilibili.crm.biz.es_migrate.service.WalletConsumeDayExtendOlapService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.adx.biz.service.EsToHiveInterface;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Slf4j
@Component
@JobHandler("WalletConsumeDayExtendMi2HiveJob")
public class WalletConsumeDayExtendMi2HiveJob extends AbstractBaseJobHandler {

    @Autowired
    private EsToHiveInterface esToHiveInterface;
    @Autowired
    private WalletConsumeDayExtendOlapService walletConsumeDayExtendOlapService;

    @Override
    protected boolean biz(String param) {
        WalletConsumeDayExtendQuery query = new WalletConsumeDayExtendQuery();
        query.setSize(1000);
        int index = 1;
        PageResult<WalletConsumeDayExtendEsBean> res;
        long total = 0;
        do {
            query.setPage(index++);
            res = walletConsumeDayExtendOlapService.pageSearch(query);
            total += esToHiveInterface.putLancer("wallet_consume_day_extend", "all_sync", res.getRecords());
            AlarmHelper.log("SyncRecord", total);
        } while (CollectionUtils.isNotEmpty(res.getRecords()));

        return true;
    }
}
