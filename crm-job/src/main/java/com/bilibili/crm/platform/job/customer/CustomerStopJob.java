package com.bilibili.crm.platform.job.customer;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.service.sale_sea.component.CustomerStopComponent;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-06-19 17:40:04
 * @description:
 **/

@Slf4j
@Component
@JobHandler("CustomerStopJob")
public class CustomerStopJob extends IJobHandler {

    @Resource
    private CustomerStopComponent customerStopComponent;

    @Resource
    private CustomerSeaJob customerSeaJob;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            customerStopComponent.stop(Utils.getNow(), new ArrayList<>(), new ArrayList<>());
            customerSeaJob.handleCustomerSea();
        });
        return ReturnT.SUCCESS;
    }
}
