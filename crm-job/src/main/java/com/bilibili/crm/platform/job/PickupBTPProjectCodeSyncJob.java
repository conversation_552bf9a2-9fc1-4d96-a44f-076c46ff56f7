package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.CollectionWithDefaultPoolHelper;
import com.bilibili.commercialorder.soa.order.dto.OrderInfoForCrmSynDto;
import com.bilibili.commercialorder.soa.order.dto.QueryOrderInfoForCrmReqDto;
import com.bilibili.commercialorder.soa.order.service.ISoaCmOrderService;
import com.bilibili.crm.platform.api.cost_management.dto.BTPProjectDto;
import com.bilibili.crm.platform.api.cost_management.dto.BTPProjectPageRequestDto;
import com.bilibili.crm.platform.api.cost_management.service.IBTPIntegrationService;
import com.bilibili.crm.platform.api.pickup.INewPickupSettleService;
import com.bilibili.crm.platform.api.pickup.dto.NewPickupSettleDto;
import com.bilibili.crm.platform.api.pickup.dto.QueryPickupSettleDto;
import com.bilibili.crm.platform.biz.dao.CrmOrderDao;
import com.bilibili.crm.platform.biz.dao.CrmOrderExtraDao;
import com.bilibili.crm.platform.biz.dao.CrmProjectItemPackageDao;
import com.bilibili.crm.platform.biz.po.*;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

/**
 * 同步花火订单（任务上）绑定的btp project code
 */
@Component
@Slf4j
@JobHandler("pickupBTPProjectCodeSyncJob")
public class PickupBTPProjectCodeSyncJob extends IJobHandler {
    @Autowired
    private CrmOrderDao crmOrderDao;
    @Autowired
    private CrmOrderExtraDao crmOrderExtraDao;
    @Autowired
    private CrmProjectItemPackageDao crmProjectItemPackageDao;
    @Autowired
    private INewPickupSettleService iNewPickupSettleService;
    @Autowired
    private ISoaCmOrderService iSoaCmOrderService;
    @Autowired
    private IBTPIntegrationService ibtpIntegrationService;


    @Override
    public ReturnT<String> execute(String args) throws Exception {
        syncPickupBTPProjectCode(args);
        return ReturnT.SUCCESS;
    }

    /**
     * @param crmOrderIdsString: comma separated ("1,2,3")
     */
    public void syncPickupBTPProjectCode(String crmOrderIdsString) {
        log.info("start to sync pickup BTP project codes to CRM of crmOrderIds = {}", crmOrderIdsString);

        List<Integer> crmOrderIds;
        if (StringUtils.isEmpty(crmOrderIdsString)) {
            // get all pickup crm order
            List<CrmOrderPo> crmOrderPoList = getAllValidPickupCrmOrders();
            crmOrderIds = crmOrderPoList.stream().map(CrmOrderPo::getId).distinct().collect(Collectors.toList());
        } else {
            crmOrderIds = Arrays.stream(crmOrderIdsString.split(",")).map(Integer::parseInt).distinct().collect(Collectors.toList());
        }

        // get all btp project code
        Map<Integer, OrderInfoForCrmSynDto> crmOrderPickupOrderMap = getCrmOrderPickupOrderMap(crmOrderIds);

        Map<Integer, String> crmOrderIdMainSiteProjectIdMap = crmOrderPickupOrderMap.entrySet().stream()
                .filter(entry -> entry.getValue().getTaskInfo() != null && !StringUtils.isEmpty(entry.getValue().getTaskInfo().getProjectId()))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> entry.getValue().getTaskInfo().getProjectId(),
                        (k1, k2) -> k1
                ));

        // 花火使用的是主站的项目id，这里转换成btp的项目编号
        List<String> mainSiteProjectIds = crmOrderIdMainSiteProjectIdMap.values().stream().distinct().collect(Collectors.toList());
        List<BTPProjectDto> btpProjectDtoList = ibtpIntegrationService.getAllBTPProjectsByParam(BTPProjectPageRequestDto.builder().businessCodeList(mainSiteProjectIds).build());
        Map<String, String> mainSiteProjectIdBTPProjectCodeMap = btpProjectDtoList.stream().collect(Collectors.toMap(
                BTPProjectDto::getBusinessCode,
                BTPProjectDto::getProjectCode,
                (k1, k2) -> k1
        ));

        Map<Integer, String> crmOrderIdBTPProjectCodeMap = crmOrderIdMainSiteProjectIdMap.entrySet().stream()
                .filter(entry -> !StringUtils.isEmpty(mainSiteProjectIdBTPProjectCodeMap.get(entry.getValue())))
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> mainSiteProjectIdBTPProjectCodeMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));

        // update all pickup crm order for crm project id
        updatePickupCrmOrderExtraCrmProjectIds(crmOrderIdBTPProjectCodeMap);

        log.info("finished to sync pickup BTP project codes to CRM of crmOrderIds = {}", crmOrderIdsString);
    }

    public List<CrmOrderPo> getAllValidPickupCrmOrders() {
        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria criteria = crmOrderPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andTypeEqualTo(10);  // 花火订单
        return crmOrderDao.selectByExample(crmOrderPoExample);
    }

    public Map<Integer, OrderInfoForCrmSynDto> getCrmOrderPickupOrderMap(List<Integer> crmOrderIds) {
        List<NewPickupSettleDto> newPickupSettleDtoList = iNewPickupSettleService.getPickupSettleList(QueryPickupSettleDto.builder().crmOrderIds(crmOrderIds).build());
        Map<Integer, Integer> crmOrderIdPickupOrderIdMap = newPickupSettleDtoList.stream().collect(Collectors.toMap(
                NewPickupSettleDto::getCrmOrderId,
                NewPickupSettleDto::getPickupId,
                (k1, k2) -> k1
        ));

        List<Integer> pickupOrderIds = crmOrderIdPickupOrderIdMap.values().stream().distinct().collect(Collectors.toList());

        List<OrderInfoForCrmSynDto> orderInfoForCrmSynDtoList = CollectionWithDefaultPoolHelper.callInBatchesAsync(pickupOrderIds, 100, eachPickupOrderIds ->
                iSoaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder()
                        .orderIds(eachPickupOrderIds)
                        .isQueryTask(true)
                        .build())
        );

        Map<Integer, OrderInfoForCrmSynDto> pickupOrderIdMap = orderInfoForCrmSynDtoList.stream().collect(Collectors.toMap(
                OrderInfoForCrmSynDto::getId,
                Function.identity(),
                (k1, k2) -> k1
        ));

        return crmOrderIdPickupOrderIdMap.entrySet().stream()
                .filter(entry -> pickupOrderIdMap.get(entry.getValue()) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> pickupOrderIdMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));
    }

    public void updatePickupCrmOrderExtraCrmProjectIds(Map<Integer, String> crmOrderIdBTPProjectCodeMap) {
        List<Integer> crmOrderIds = new ArrayList<>(crmOrderIdBTPProjectCodeMap.keySet());
        List<String> btpProjectCodes = new ArrayList<>(crmOrderIdBTPProjectCodeMap.values());

        if (CollectionUtils.isEmpty(btpProjectCodes)) {
            return;
        }

        CrmProjectItemPackagePoExample crmProjectItemPackagePoExample = new CrmProjectItemPackagePoExample();
        CrmProjectItemPackagePoExample.Criteria criteria = crmProjectItemPackagePoExample.createCriteria();
        criteria.andBtpProjectCodeIn(btpProjectCodes);
        List<CrmProjectItemPackagePo> crmProjectItemPackagePoList = crmProjectItemPackageDao.selectByExample(crmProjectItemPackagePoExample);

        Map<String, Integer> btpProjectCodeCrmProjectIdMap = crmProjectItemPackagePoList.stream().collect(Collectors.toMap(
                CrmProjectItemPackagePo::getBtpProjectCode,
                CrmProjectItemPackagePo::getId,
                (k1, k2) -> k1
        ));

        List<CrmOrderExtraPo> crmOrderExtraPoList = crmOrderIds.stream().map(
                crmOrderId -> {
                    String btpProjectCode = crmOrderIdBTPProjectCodeMap.get(crmOrderId);
                    Integer crmProjectId = btpProjectCodeCrmProjectIdMap.get(btpProjectCode);
                    return CrmOrderExtraPo.builder()
                            .crmOrderId(crmOrderId)
                            .projectItemId(crmProjectId)
                            .build();
                }
        ).collect(Collectors.toList());

        for (CrmOrderExtraPo crmOrderExtraPo : crmOrderExtraPoList) {
            if (crmOrderExtraPo.getProjectItemId() == null) {
                log.info("crmProjectId not found, skip it: {}", crmOrderExtraPo);
                continue;
            }

            try {
                CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
                CrmOrderExtraPoExample.Criteria crmOrderExtraPoExampleCriteria = crmOrderExtraPoExample.createCriteria();
                crmOrderExtraPoExampleCriteria.andCrmOrderIdEqualTo(crmOrderExtraPo.getCrmOrderId());

                crmOrderExtraDao.updateByExampleSelective(crmOrderExtraPo, crmOrderExtraPoExample);
                log.info("succeeded to sync pickup BTP project codes to CRM of crmOrderId = {}, crmProjectId = {}", crmOrderExtraPo.getCrmOrderId(), crmOrderExtraPo.getProjectItemId());

                Thread.sleep(100L);

            } catch (Exception e) {
                log.error("failed to sync pickup BTP project codes to CRM of crmOrderId = {}, crmProjectId = {}", crmOrderExtraPo.getCrmOrderId(), crmOrderExtraPo.getProjectItemId());
            }
        }

    }
}
