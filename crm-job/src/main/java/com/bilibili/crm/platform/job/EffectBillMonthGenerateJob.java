package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.SafeStopWatch;
import com.bilibili.crm.platform.api.income.exception.IncomeConditionInvalidException;
import com.bilibili.crm.platform.biz.service.effect.component.EffectBillRefreshComponent;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.bilibili.crm.platform.job.common.AbstractBaseParamJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.Data;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.function.Supplier;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2022-12-18 19:09:21
 * @description: 效果应收账单月度生成JOB
 **/

@Slf4j
@Component
@JobHandler("EffectBillMonthGenerateJob")
public class EffectBillMonthGenerateJob extends AbstractBaseParamJobHandler<EffectBillMonthGenerateJob.GenEffBillParam> {

    public EffectBillMonthGenerateJob() {
        super(GenEffBillParam.class, GenEffBillParam::new);
    }

    @Data
    public static class GenEffBillParam {

        private boolean adx = true;
        private boolean dpa = true;
        private boolean effect = true;

        private int monthOffset =  1;
    }

    @Resource
    private EffectBillRefreshComponent effectBillRefreshComponent;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Override
    protected boolean biz(GenEffBillParam param) {
        Timestamp date = incomeTimeUtil.getSomeMonthAgoBeginTime(param.monthOffset);
        Timestamp begin = Utils.getBeginOfDay(Utils.getBeginDayOfMonth(date));
        Timestamp end = Utils.getEndOfDay(Utils.getEndDayOfMonth(date));

        Timestamp beginOfDay = Utils.getBeginOfDay(begin);
        Timestamp endOfDay = Utils.getEndOfDay(end);

        AlarmHelper.log("EffectBillMonthGenerateJobTime", beginOfDay, endOfDay);

        try {
            SafeStopWatch watch = new SafeStopWatch(this.getJobName());
            watch.pushTask("adx");
            long adxCount = param.isAdx() ? effectBillRefreshComponent.refreshAdx(begin, end) : 0L;
            watch.pushTask("dpa");
            long dpaCount = param.isDpa() ? effectBillRefreshComponent.refreshDpa(begin, end) : 0L;
            watch.pushTask("effect");
            long effectPostCount = param.isEffect() ? effectBillRefreshComponent.refreshEffect(begin, end) : 0L;
            watch.log();
            AlarmHelper.log("Finish", adxCount, dpaCount, effectPostCount);
        } catch (IncomeConditionInvalidException e) {
            throw new RuntimeException(e);
        }
        return true;
    }


}
