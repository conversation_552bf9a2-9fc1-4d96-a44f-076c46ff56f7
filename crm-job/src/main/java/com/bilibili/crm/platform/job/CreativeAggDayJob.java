package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.service.consume.CreativeAggService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;

/**
 * @param：args:20200714~20200714
 */
@Component
@JobHandler("creativeAggDayJob")
public class CreativeAggDayJob extends IJobHandler {
    @Resource
    private CreativeAggService creativeAggService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {

        Timestamp begin = Utils.getSomeDayBeforeToday(1);
        Timestamp end = Utils.getYesteday();

        if (StringUtils.hasText(args) && args.contains("~")) {
            String[] strings = args.split("~");
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }

        Timestamp finalBegin = begin;
        Timestamp finalEnd = end;

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            creativeAggService.flushByDay2ES(finalBegin, finalEnd);
        });
        return ReturnT.SUCCESS;
    }
}
