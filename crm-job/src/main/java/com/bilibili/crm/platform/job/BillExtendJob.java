package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.bilibili.bangumi.databus.service.DatabusPoolSub;
import com.bilibili.crm.platform.biz.canal.CanalMessageHandler;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;

/**
 * 消费bill canal 日志JOB
 * @author: brady
 * @time: 2021/3/17 3:58 下午
 */
@Slf4j
@Component
@JobHandler("BillExtendJob")
public class BillExtendJob extends IJobHandler {

    @Autowired
    private ThreadPoolTaskExecutor executor;

    @Resource(name = "billDataBusPoolSub")
    private DatabusPoolSub billDataBusPoolSub;


    @Value("${dataBus.switch.archiveMessage.bill:1}")
    private Integer dataBusSwitch;
    @Autowired
    private CanalMessageHandler canalMessageHandler;

    private final static List<Future<Integer>> futureList = new ArrayList<>();

    //@Override
    public ReturnT<String> execute(String s) throws Exception {

        if (IsValid.FALSE.getCode() == dataBusSwitch) {
            return ReturnT.SUCCESS;
        }
        log.info("BillExtendJob.start");

        Transaction transaction = Cat.newTransaction("JOB", "BillExtendJob");
        transaction.addData("futureList.size", futureList.size());
        synchronized (futureList) {

            if (CollectionUtils.isEmpty(futureList)) {
                try {
                    futureList.add(addTask());
                    log.info("add task success");
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (Exception e) {
                    log.error("submit task error", e);
                    transaction.setStatus(e);
                } finally {
                    transaction.complete();
                }
            } else {
                try {
                    Future<Integer> future = futureList.get(0);
                    if (future == null || future.get(1, TimeUnit.MILLISECONDS) == 0) {
                        futureList.set(0, addTask());
                        log.info("add task success");
                        transaction.addData("databus health", "add again!");
                    }
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (TimeoutException e) {
                    transaction.addData("databus health", "running");
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (Exception e) {
                    log.error("submit task error");
                    transaction.setStatus(e);
                } finally {
                    transaction.complete();
                }
            }
        }

        log.info("BillExtendJob.end");
        return ReturnT.SUCCESS;
    }


    private Future<Integer> addTask() {
        return executor.submit(() -> {
            try {
                billDataBusPoolSub.sub(jsonObject -> batchHandle(jsonObject));
                System.out.println("add task");
            } catch (Exception e) {
                log.error("execute.FinalDraftFlow.databus.error", e);
                return 0;
            }
            return 1;
        });
    }

    private void batchHandle(JSONObject jsonObject)  {
        log.info("receive bill message：{}", JSON.toJSONString(jsonObject));
        canalMessageHandler.handleBinlogMsg(jsonObject);
    }

}

