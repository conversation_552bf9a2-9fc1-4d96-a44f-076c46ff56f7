package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.clue.service.IBsiOptClueStoreService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2022/2/16 9:39 下午
 */
@Component
@JobHandler("BsiClueSyncJob")
@Slf4j
public class BsiClueSyncJob extends IJobHandler {
    @Autowired
    private IBsiOptClueStoreService bsiOptClueStoreService;

    /**
     * exp：args:20220217~20220217
     * @param args
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            //同步一天数据
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }
            //花火留资同步
            bsiOptClueStoreService.syncPickUpRegisterInfo(begin, end);
            //花火留资数据关键字段更新
            bsiOptClueStoreService.syncPickUpRegisterInfoUpdate(begin, end);
        });
        return ReturnT.SUCCESS;
    }
}
