package com.bilibili.crm.platform.job.wallet;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.common.CommonConstant;
import com.bilibili.crm.platform.biz.crm.wallet.dao.write.WalletTxLogDao;
import com.bilibili.crm.platform.biz.crm.wallet.po.WalletTxLogPo;
import com.bilibili.crm.platform.biz.crm.wallet.po.WalletTxLogPoExample;
import com.bilibili.crm.platform.biz.dao.TxLogDao;
import com.bilibili.crm.platform.biz.po.TxLogPo;
import com.bilibili.crm.platform.biz.po.TxLogPoExample;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 8/29/23
 **/
@Component
@JobHandler("txCompareJob")
@Slf4j
public class TxCompareJob extends IJobHandler {

    @Autowired
    private TxLogDao txLogDao;
    @Autowired
    private WalletTxLogDao walletTxLogDao;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {

            List<String> notInWalletTx = new ArrayList<>();
            List<String> notInTx = new ArrayList<>();

            Timestamp begin = Utils.getSomeHourAgo(Utils.getNow(), 3);
            Timestamp end = new Timestamp(System.currentTimeMillis() - 1000);

            if (StringUtils.hasText(param) && param.contains("~")) {
                String[] strings = param.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            Timestamp tmpBegin = new Timestamp(begin.getTime());
            while (true) {
                TxLogPoExample tmpTxExample = new TxLogPoExample();
                tmpTxExample.createCriteria().andCtimeGreaterThan(tmpBegin).andCtimeLessThanOrEqualTo(end)
                        .andBizEqualTo(CommonConstant.CRM_WALLET_TX_BIZ);
                tmpTxExample.setLimit(2000);
                tmpTxExample.setOrderByClause("ctime asc");
                List<TxLogPo> txLogPos = txLogDao.selectByExample(tmpTxExample);

                if (CollectionUtils.isEmpty(txLogPos)) {
                    break;
                }
                tmpBegin = txLogPos.get(txLogPos.size() - 1).getCtime();

                List<String> txSerialNumbers = txLogPos.stream().map(TxLogPo::getSerialNumber).collect(Collectors.toList());

                WalletTxLogPoExample walletTxLogPoExample = new WalletTxLogPoExample();
                walletTxLogPoExample.createCriteria().andSerialNumberIn(txSerialNumbers);
                List<WalletTxLogPo> walletTxLogPos = walletTxLogDao.selectByExample(walletTxLogPoExample);
                if (txLogPos.size() == walletTxLogPos.size()) {
                    continue;
                }

                txLogPos.forEach(txLogPo -> {
                    WalletTxLogPoExample walletTxLogPoExample2 = new WalletTxLogPoExample();
                    walletTxLogPoExample2.createCriteria().andSerialNumberEqualTo(txLogPo.getSerialNumber());
                    List<WalletTxLogPo> walletTxLogPos2 = walletTxLogDao.selectByExample(walletTxLogPoExample2);
                    if (CollectionUtils.isEmpty(walletTxLogPos2)) {
                        notInWalletTx.add(txLogPo.getSerialNumber());
                    }
                });
            }


            tmpBegin = new Timestamp(begin.getTime());
            while (true) {
                WalletTxLogPoExample tmpWalletTxLogPoExample = new WalletTxLogPoExample();
                tmpWalletTxLogPoExample.createCriteria().andCtimeGreaterThan(tmpBegin).andCtimeLessThanOrEqualTo(end)
                        .andBizEqualTo(CommonConstant.CRM_WALLET_TX_BIZ);
                tmpWalletTxLogPoExample.setLimit(2000);
                tmpWalletTxLogPoExample.setOrderByClause("ctime asc");
                List<WalletTxLogPo> walletTxLogPos = walletTxLogDao.selectByExample(tmpWalletTxLogPoExample);

                if (CollectionUtils.isEmpty(walletTxLogPos)) {
                    break;
                }
                tmpBegin = walletTxLogPos.get(walletTxLogPos.size() - 1).getCtime();

                List<String> walletTxSerialNumbers = walletTxLogPos.stream().map(WalletTxLogPo::getSerialNumber).collect(Collectors.toList());

                TxLogPoExample txLogPoExample = new TxLogPoExample();
                txLogPoExample.createCriteria().andSerialNumberIn(walletTxSerialNumbers);
                List<TxLogPo> txLogPos = txLogDao.selectByExample(txLogPoExample);
                if (txLogPos.size() == walletTxLogPos.size()) {
                    continue;
                }

                walletTxLogPos.forEach(walletTxLogPo -> {
                    TxLogPoExample txLogPoExample2 = new TxLogPoExample();
                    txLogPoExample2.createCriteria().andSerialNumberEqualTo(walletTxLogPo.getSerialNumber());
                    List<TxLogPo> txLogPos2 = txLogDao.selectByExample(txLogPoExample2);
                    if (CollectionUtils.isEmpty(txLogPos2)) {
                        notInTx.add(walletTxLogPo.getSerialNumber());
                    }
                });
            }

            Assert.isTrue(CollectionUtils.isEmpty(notInTx) && CollectionUtils.isEmpty(notInWalletTx),
                    "crm-wallet事务对账不一致 notInTx " + JSON.toJSONString(notInTx) + " , notInWalletTx " + JSON.toJSON(notInWalletTx));

        });

        return ReturnT.SUCCESS;
    }
}
