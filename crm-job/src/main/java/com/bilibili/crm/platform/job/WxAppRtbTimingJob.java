package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.cache.WxAppCacheWriter;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;

/**
 * <AUTHOR>
 * @date 2020/4/25
 * 企业微信日报 效果营销-实时收入 缓存job
 **/
@Component
@JobHandler("WxAppRtbTimingJob")
@Slf4j
public class WxAppRtbTimingJob extends IJobHandler {

    @Autowired
    private WxAppCacheWriter wxAppCacheWriter;

    /**
     * @param args exp：args:20200714
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {

        //CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) ->  wxAppCacheWriter.cacheRtbTimingIncome());

        try {
            Timestamp date = Utils.getToday();
            if (StringUtils.hasText(args)) {
                date = StringDateParser.stringToTimestamp(args);
            }
            Timestamp finalDate = date;

            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> wxAppCacheWriter.cacheRtbTimingIncome(finalDate));

        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppRtbTimingJob", e.getMessage());
            throw e;
        }

        return ReturnT.SUCCESS;
    }
}
