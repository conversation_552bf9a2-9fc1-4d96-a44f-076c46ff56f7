package com.bilibili.crm.platform.job.es_migrate.sycpb_new_es.copyes;

import com.alibaba.fastjson.JSONObject;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.api.fly.dto.QueryFlyDto;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.ESWalletConsumeExtendPo;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.migrate.ESWalletConsumeExtendNewPo;
import com.bilibili.crm.platform.biz.service.consume.config.ConsumeExtendConfig;
import com.bilibili.crm.platform.biz.service.fly.FlyConfig;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.es_migrate.AbstractEsDataSyncJob;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.compress.utils.Lists;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.TermsQueryBuilder;
import org.elasticsearch.search.sort.FieldSortBuilder;
import org.elasticsearch.search.sort.SortOrder;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;
import java.util.stream.Collectors;

import static com.bilibili.crm.platform.biz.util.CrmUtils.setParam;
import static org.elasticsearch.index.query.QueryBuilders.*;
import static org.elasticsearch.index.query.QueryBuilders.rangeQuery;

@Slf4j
@Component
@JobHandler("WalletConsumeDayExtendDataSync2PreJob")
public class WalletConsumeDayExtendDataSync2PreJob extends AbstractEsDataSyncJob<ESWalletConsumeExtendPo> {

    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsMigrateMessageSender<ESWalletConsumeExtendNewPo> messageSender;

    private int cnt = 0;

    @Override
    protected String getIndexName() {
        return "wallet_consume_day_extend";
    }

    @Override
    protected Class<ESWalletConsumeExtendPo> getPoClass() {
        return ESWalletConsumeExtendPo.class;
    }

    @Override
    protected ElasticsearchTemplate getElasticsearchTemplate() {
        return elasticsearchTemplate;
    }

    @Override
    protected void processData(List<ESWalletConsumeExtendPo> content, String index) {
        List<ESWalletConsumeExtendNewPo> newContent = convertToNewPo(content);

        cnt += newContent.size();
        messageSender.sendBatch(newContent, ConsumeExtendConfig.WALLET_INDEX, EsMigrateCommon.INCR);
        AlarmHelper.log("processData", cnt);
    }

    private List<ESWalletConsumeExtendNewPo> convertToNewPo(List<ESWalletConsumeExtendPo> content) {
        return content.stream()
                .map(this::convertSinglePo)
                .collect(Collectors.toList());
    }

    private ESWalletConsumeExtendNewPo convertSinglePo(ESWalletConsumeExtendPo po) {
        ESWalletConsumeExtendNewPo newPo = new ESWalletConsumeExtendNewPo();
        BeanUtils.copyProperties(po, newPo);
        return newPo;
    }

    public String query(Integer productType) {
        BoolQueryBuilder queryBuilder = boolQuery();

        setParam(productType, (value) -> queryBuilder.filter(termQuery("productType", value)));

        SearchQuery searchQuery = new NativeSearchQueryBuilder()
                .withIndices(ConsumeExtendConfig.WALLET_INDEX)
                .withQuery(queryBuilder)
                .withPageable(PageRequest.of(1, 10))
                .build();
        List<ESWalletConsumeExtendNewPo> res = elasticsearchTemplate.queryForList(searchQuery, ESWalletConsumeExtendNewPo.class);

        return JSONObject.toJSONString(res);
    }
}
