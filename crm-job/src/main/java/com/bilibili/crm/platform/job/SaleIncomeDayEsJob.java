package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.service.achievement.SaleKpiService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/7/27
 **/
@Component
@JobHandler("saleIncomeDayEsJob")
@Slf4j
public class SaleIncomeDayEsJob extends IJobHandler {

    @Autowired
    private SaleKpiService saleKpiService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            if (StringUtils.hasText(args)) {
                saleKpiService.saleDayIncomeToEs(StringDateParser.stringToTimestamp(args), null);
            } else {
                saleKpiService.saleDayIncomeToEs(Utils.getToday(), null);
            }
        });

        return ReturnT.SUCCESS;
    }
}
