package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.adx.dto.AdxBidderConfigDto;
import com.bilibili.crm.platform.api.adx.dto.AdxBidderDayDto;
import com.bilibili.crm.platform.api.adx.service.IAdxBidderDayService;
import com.bilibili.crm.platform.api.adx.service.IESAdxBidderDayService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.ArrayList;
import java.util.List;
import java.util.Map;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-09-12
 **/
@Component
@JobHandler("AdxBidderDay2ESJob")
@Slf4j
public class AdxBidderDay2ESJob extends IJobHandler {

    @Autowired
    private IESAdxBidderDayService esAdxBidderDayService;
    @Autowired
    private IAdxBidderDayService adxBidderDayService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("start to import all adx bidder data to es");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            List<AdxBidderDayDto> result = new ArrayList<>();
            Map<Integer, AdxBidderConfigDto> configMap = adxBidderDayService.queryMapFromBidderConfig();
            List<AdxBidderDayDto> list = adxBidderDayService.queryBidderByAllDay();
            list.forEach(dto -> {
                if (configMap.containsKey(dto.getBidderId())){
                    AdxBidderConfigDto configDto = configMap.get(dto.getBidderId());
                    dto.setAccountId(configDto.getAccountId());
                    dto.setDspName(configDto.getName());
                    result.add(dto);
                }
            });
            esAdxBidderDayService.batchInsert(result);
        });
        log.info("finished to import adx bidder data");
        return ReturnT.SUCCESS;
    }
}
