package com.bilibili.crm.platform.core;

import com.bilibili.crm.platform.api.exception.CrmAppException;
import com.bilibili.crm.platform.api.exception.code.CrmAppExceptionCode;
import com.bilibili.crm.platform.biz.util.ThreadPoolUtils;
import com.bilibili.crm.platform.biz.util.SpringContextHolder;
import com.bilibili.crm.platform.biz.util.ThreadPoolManager;
import com.bilibili.crm.platform.common.DbId;
import com.bilibili.crm.platform.support.metrics.Metrics;
import com.bilibili.crm.platform.support.metrics.MetricsNames;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.Futures;
import com.google.common.util.concurrent.ListenableFuture;
import com.google.common.util.concurrent.ListeningExecutorService;
import com.xxl.job.core.biz.model.ReturnT;
import io.opentelemetry.api.OpenTelemetry;
import io.opentelemetry.api.trace.Span;
import io.opentelemetry.api.trace.StatusCode;
import io.opentelemetry.api.trace.Tracer;
import io.opentelemetry.context.Scope;
import lombok.Getter;
import org.apache.commons.collections4.CollectionUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.util.Assert;

import java.util.List;
import java.util.Objects;
import java.util.concurrent.CancellationException;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.atomic.AtomicLong;

public abstract class AbstractTaskProcessor<R extends Parameter<?>, T> {

    protected static final int PAGESIZE = 3000;

    /**
     * 默认线程池
     */
    public static final ListeningExecutorService DEFAULT_THREAD_POOL = ThreadPoolManager.JOB_DEFAULT_THREAD_POOL;

    /**
     * Default is 4 times of processors
     */
    protected static final int DEDAULT_MAX_THREADS = ThreadPoolUtils.PROCESSORS * 4;

    /**
     * 日志对象
     */
    protected Logger logger;

    /**
     * 定时任务名称
     */
    protected String taskName;

    /**
     * 线程池
     */
    protected ListeningExecutorService threadPool;

    /**
     * 定时任务相关配置
     */
    protected TaskProcessorConf conf;

    protected final OpenTelemetry openTelemetry;

    @Getter
    public volatile boolean isStop = false;

    public AbstractTaskProcessor(TaskProcessorConf conf) {
        Assert.notNull(conf, "conf is null");
        Assert.notNull(conf.getTaskName(), "taskName is null");
        this.conf = conf;
        logger = conf.getLogger();
        taskName = conf.getTaskName();
        threadPool = conf.getThreadPool();
        openTelemetry = SpringContextHolder.getBean(OpenTelemetry.class);
    }

    public ReturnT<String> process(R parameter) {
        Tracer tracer = openTelemetry.getTracer(this.taskName);
        Span span = tracer.spanBuilder(this.taskName)
                // 可添加自定义键值对
                .setAttribute("taskName", this.taskName)
                .startSpan();
        // 必须要调用 makeCurrent，否则Span并没有被真正激活
        try (Scope ignored = span.makeCurrent()) {
            // 需要被自定义Span包裹的业务代码块
            // 初始化定时任务
            init(parameter);
            // 执行分页循环
            return handleLoop(parameter);
        } catch (Throwable e) {
            logger.error("task_run error", e);
            // 异常处理，标记错误状态后会被强制采样
            span.setStatus(StatusCode.ERROR)
                    .recordException(e);
            errorMonitor(e, "task_run");
            return ReturnT.FAIL;
        } finally {
            // 需要包裹的代码块结束后必须调用 end
            span.end();
        }
    }

    /**
     * 初始化定时任务
     *
     * @param parameter
     */
    protected void init(R parameter) {
        if (logger == null) {
            logger = LoggerFactory.getLogger(taskName);
        }
        if (conf.isUseThreadPool() && threadPool == null) {
            threadPool = DEFAULT_THREAD_POOL;
        }
    }

    /**
     * 执行分页循环
     *
     * @param parameter
     */
    protected abstract ReturnT<String> handleLoop(R parameter) throws InterruptedException;

    /**
     * 多机分片执行时，用于控制是否在当前机器上执行处理该数据
     * <p>
     * 推荐策略：{@code Math.abs(item.getId() % shards) == index}
     *
     * @param loopContext
     * @param shards
     * @param index
     * @param item
     * @return
     */
    public Boolean isProcess(final LoopContext loopContext, int shards, int index, T item) {
        return null;
    }

    protected void batchProcessData(final LoopContext loopContext, AtomicLong sum, final List<T> todoList) throws InterruptedException {
        if (CollectionUtils.isEmpty(todoList)) {
            return;
        }
        logger.info("<{}> 当前待处理的数据量: {}", taskName, todoList.size());
        List<ListenableFuture<Void>> futures = Lists.newArrayList();
        for (T item : todoList) {

            if (Thread.currentThread().isInterrupted()) {
                isStop = true;
                logger.warn("<{}> task forced to stop!", taskName);
                throw new InterruptedException();
            }

            if (conf.isUseThreadPool() && threadPool != null) {
                handleAsyncProcessData(loopContext, item, sum, futures); // 异步处理
            } else {
                handleProcessData(loopContext, item, sum); // 同步处理
            }
        }
        if (conf.isUseThreadPool() && CollectionUtils.isNotEmpty(futures)) {
            Throwable throwable = null;
            final ListenableFuture<?> allFutures = Futures.allAsList(futures);
            try {
                allFutures.get();
            } catch (CancellationException | InterruptedException e) {
                logger.error("task_run error", e);
                throwable = e;
            } catch (ExecutionException ee) {
                logger.error("task_run error", ee);
                throwable = ee.getCause();
            }
            //异常处理，根据配置决定是否抛出异常
            handleException(throwable);
        }
    }

    public void handleProcessData(final LoopContext loopContext, T item, AtomicLong sum) throws InterruptedException {
        String parentTraceId = Span.current().getSpanContext().getTraceId();
        Tracer tracer = openTelemetry.getTracer(this.taskName);
        Span span = tracer.spanBuilder(this.taskName)
                // 若异步任务与主线程不需要关联，需要设置noParent，使该Span成为RootSpan
                .setNoParent()
                // 可以在attribute中把主线程的traceId放入，以便排障、关联
                .setAttribute("pub_trace_id", parentTraceId)
                // 可添加自定义键值对
                .setAttribute("task_name", this.taskName)
                .startSpan();
        // 必须要调用 makeCurrent，否则Span并没有被真正激活
        try (Scope ignoredScope = span.makeCurrent();
             MDC.MDCCloseable ignoredMdc = MDC.putCloseable("pub_trace_id", parentTraceId)) {

            Metrics.one(MetricsNames.XXL_JOB, "data_process", "taskName", taskName.replace(".", "_"));

            // 需要被自定义Span包裹的业务代码块
            logger.info("<{}> 当前待处理的数据 item:{}", taskName, item);

            retryProcessData(loopContext, item);

            sum.getAndIncrement();
        } catch (Throwable t) {
            logger.error("data process error, data:{}", item, t);
            // 异常处理，标记错误状态后会被强制采样
            span.setStatus(StatusCode.ERROR)
                    .recordException(t);
            //异常处理，根据配置决定是否抛出异常
            handleException(t);
        } finally {
            // 需要包裹的代码块结束后必须调用 end
            span.end();
        }
    }

    public void handleAsyncProcessData(final LoopContext loopContext, T item, AtomicLong sum, List<ListenableFuture<Void>> futures) throws InterruptedException {
        String parentTraceId = Span.current().getSpanContext().getTraceId();

        try {

            // 使用线程池并发处理任务
            ListenableFuture<Void> future = threadPool.submit(() -> {

                Tracer tracer = openTelemetry.getTracer(this.taskName);
                Span span = tracer.spanBuilder(this.taskName)
                        // 若异步任务与主线程不需要关联，需要设置noParent，使该Span成为RootSpan
                        .setNoParent()
                        // 可以在attribute中把主线程的traceId放入，以便排障、关联
                        .setAttribute("pub_trace_id", parentTraceId)
                        // 可添加自定义键值对
                        .setAttribute("task_name", this.taskName)
                        .startSpan();
                // 必须要调用 makeCurrent，否则Span并没有被真正激活
                try (Scope ignoredScope = span.makeCurrent();
                     MDC.MDCCloseable ignoredMdc = MDC.putCloseable("pub_trace_id", parentTraceId)) {

                    Metrics.one(MetricsNames.XXL_JOB, "data_process", "taskName", taskName.replace(".", "_"));

                    // 需要被自定义Span包裹的业务代码块
                    logger.info("<{}> 当前待处理的数据 item:{}", taskName, item);

                    if (isStop) {
                        logger.warn("<{}> task forced to stop!", taskName);
                        throw new InterruptedException();
                    }

                    retryProcessData(loopContext, item);

                    sum.getAndIncrement();
                } catch (Throwable t) {
                    logger.error("data process error, data:{}", item, t);
                    // 异常处理，标记错误状态后会被强制采样
                    span.setStatus(StatusCode.ERROR)
                            .recordException(t);
                    //异常处理，根据配置决定是否抛出异常
                    handleException(t);
                } finally {
                    // 需要包裹的代码块结束后必须调用 end
                    span.end();
                }
                return null;
            });
            futures.add(future);
        } catch (Throwable t) {
            logger.error("submit task error", t);
            //异常处理，根据配置决定是否抛出异常
            handleException(t);
        }
    }

    public void errorMonitor(Throwable throwable, String busiPoint) {
        CrmAppExceptionCode exceptionCode = null;
        //监控打点
        if (CrmAppException.class.isAssignableFrom(throwable.getClass())) {
            CrmAppException crmAppException = (CrmAppException) throwable;
            exceptionCode = CrmAppExceptionCode.toEnum(crmAppException.getCode());
        }
        if (Objects.isNull(exceptionCode)) {
            exceptionCode = CrmAppExceptionCode.SYSTEM_ERROR;
        }
        Metrics.oneErr(MetricsNames.XXL_JOB, busiPoint, exceptionCode, "taskName", taskName.replace(".", "_"));
    }

    /**
     * 异常处理，根据配置决定是否抛出异常
     *
     * @param throwable
     */
    private void handleException(Throwable throwable) throws InterruptedException {
        if (throwable == null) {
            return;
        }
        // 中断异常，可能是手动停止定时任务
        if (InterruptedException.class.isAssignableFrom(throwable.getClass())) {
            logger.warn("<{}> Task execution interrupted.", taskName, throwable);
            throw new InterruptedException(throwable.getMessage());
        }
        logger.error("<{}> Task execution throws an exception.", taskName, throwable);
        errorMonitor(throwable, "data_process");
        if (!conf.isSkipException()) {
            Class<?>[] skipExceptions = conf.getSkipExceptions();
            if (skipExceptions == null || skipExceptions.length == 0) {
                throw new RuntimeException(throwable);
            }
            boolean isSkip = false;
            for (Class<?> clz : skipExceptions) {
                if (clz.isAssignableFrom(throwable.getClass())) {
                    isSkip = true;
                    break;
                }
            }
            if (!isSkip) {
                throw new RuntimeException(throwable);
            }
        }
    }

    /**
     * 获取要处理数据的id范围
     *
     * @param parameter
     * @return
     */
    public abstract DbId getMinAndMaxId(R parameter);

    /**
     * 根据id获取数据
     *
     * @param loopContext
     * @param startId
     * @param endId
     * @return
     */
    public abstract List<T> fetchData(final LoopContext loopContext, long startId, long endId);

    public void retryProcessData(final LoopContext loopContext, T t) throws Exception {
        if (conf.isEnableRetry()) {
            int retryCount = 0;
            long delay = 1000; // initial delay in milliseconds
            while (retryCount < conf.getRetryTimes()) {
                try {
                    processData(loopContext, t);
                    return;
                } catch (Exception e) {
                    retryCount++;
                    logger.warn("Retry {}/{} for data: {}", retryCount, conf.getRetryTimes(), t, e);
                    if (retryCount >= conf.getRetryTimes()) {
                        throw e;
                    }
                    try {
                        Thread.sleep(delay);
                    } catch (InterruptedException ie) {
                        Thread.currentThread().interrupt();
                        throw new RuntimeException("Retry interrupted", ie);
                    }
                    delay *= 2; // exponential backoff
                }
            }
        } else {
            processData(loopContext, t);
        }
    }

    /**
     * 处理数据
     *
     * @param loopContext
     * @param t
     */
    public abstract void processData(final LoopContext loopContext, T t) throws Exception;
}
