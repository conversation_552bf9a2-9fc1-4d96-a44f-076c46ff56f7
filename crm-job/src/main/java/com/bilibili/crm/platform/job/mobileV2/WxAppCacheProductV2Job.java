package com.bilibili.crm.platform.job.mobileV2;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.v2.WxAppCacheWriterV2;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.dianping.cat.Cat;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.time.LocalDateTime;
import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

@Component
@JobHandler("WxAppCacheProductV2Job")
@Slf4j
public class WxAppCacheProductV2Job extends AbstractBaseJobHandler {

    @Autowired
    private WxAppCacheWriterV2 wxAppCacheWriterV2;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private CrmCacheManager crmCacheManager;

    /**
     * @param args exp：args:20200714~20200714
     */
    @Override
    protected boolean biz(String args) {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (!StringUtils.hasText(args)) {
                if (!Objects.equals(1, crmCacheManager.getValue("WxAppCacheJob_" + CrmUtils.formatDate(begin)))) {
                    AlarmHelper.log("WxAppCacheProductV2Job", begin, "CacheNotReady", crmCacheManager.getValue("WxAppCacheJob_" + CrmUtils.formatDate(begin)));
                    LocalDateTime now = LocalDateTime.now();
                    if (now.getHour() > 8) {
                        AlarmHelper.alarm("DailyReportPartFailed", "WxAppCacheJobNotReady");
                    }
                    return true;
                }
                //检查当天状态位
                Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
                if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.PRODUCT_V2)) {
                    Cat.logEvent("WxAppCacheProductV2Job_DATA_HAD_DOWN", day.toString());
                    return true;
                }
                if (wxAppCacheManager.isModulePaperExecuting(day, WxModuleType.PRODUCT_V2)) {
                    Cat.logEvent("WxAppCacheProductV2Job_DATA_EXECUTING", day.toString());
                    return true;
                }
            }

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
            List<Timestamp> dayList = Utils.getEachDays(begin, end);
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                dayList.add(timestamp);
            }

            AlarmHelper.log("DayList", dayList.stream().map(CrmUtils::formatDate).collect(Collectors.toList()));
            dayList.forEach(d -> {
                try {
                    AlarmHelper.log("StartCache", CrmUtils.formatDate(d, "yyyy-MM-dd"));
                    Integer isForQuarterEnd = IsValid.FALSE.getCode();
                    if ((Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                        if (d.compareTo(timestamp) == 0) {
                            isForQuarterEnd = IsValid.TRUE.getCode();
                        }
                    }
                    Integer finalIsForQuarterEnd = isForQuarterEnd;
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                        // 缓存计算数据
                        try {
                            // 缓存请求
                            wxAppCacheWriterV2.cacheRequest(d, finalIsForQuarterEnd);
                        } catch (Exception e) {
                            Cat.logEvent("Exception_WxAppCacheProductV2Job", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
                    Cat.logEvent("WxAppCacheProductV2Job_SUCCESS", d.toString() + ":normal");
                    AlarmHelper.log("CacheSuccess", CrmUtils.formatDate(d, "yyyy-MM-dd"));
                } catch (Exception e) {
                    //刷数据记录Cat
                    Cat.logEvent("WxAppCacheProductV2Job_SUCCESS", d.toString() + ":failed");
                    throw new RuntimeException(e);
                }

                try {
                    Thread.sleep(60000L);
                } catch (InterruptedException e) {
                    log.info("WxAppCacheProductV2Job InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }
            });
        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppCacheProductV2Job", e.getMessage());
            AlarmHelper.alarmEx("DailyReportPartFailed", e, this.getJobName());
            throw e;
        }

        AlarmHelper.log("WxAppCacheProductV2Job success args", args);

        return true;
    }

}
