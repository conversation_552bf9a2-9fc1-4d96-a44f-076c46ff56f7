package com.bilibili.crm.platform.job.es_migrate.sycpb_new_es;

import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.migrate.ESBillConsumeExtendNewPo;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.ESBillConsumeExtendPo;
import com.bilibili.crm.platform.job.es_migrate.AbstractEsDataSyncJob;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("BillConsumeDayExtendDataSyncJob")
public class BillConsumeDayExtendDataSyncJob extends AbstractEsDataSyncJob<ESBillConsumeExtendPo> {

    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsMigrateMessageSender<ESBillConsumeExtendNewPo> eSBillConsumeExtendNewPoMessageSender;

    @Override
    protected String getIndexName() {
        return "bill_consume_day_extend";
    }

    @Override
    protected Class<ESBillConsumeExtendPo> getPoClass() {
        return ESBillConsumeExtendPo.class;
    }

    @Override
    protected ElasticsearchTemplate getElasticsearchTemplate() {
        return elasticsearchTemplate;
    }

    @Override
    protected void processData(List<ESBillConsumeExtendPo> content, String index) {
        List<ESBillConsumeExtendNewPo> newContent = convertToNewPo(content);
        eSBillConsumeExtendNewPoMessageSender.sendBatch(newContent, index, EsMigrateCommon.JOB);
    }

    private List<ESBillConsumeExtendNewPo> convertToNewPo(List<ESBillConsumeExtendPo> content) {
        return content.stream()
                .map(this::convertSinglePo)
                .collect(Collectors.toList());
    }

    private ESBillConsumeExtendNewPo convertSinglePo(ESBillConsumeExtendPo po) {
        ESBillConsumeExtendNewPo newPo = new ESBillConsumeExtendNewPo();
        BeanUtils.copyProperties(po, newPo);
        return newPo;
    }

    // 后门
    public void localTest() {
        this.syncData();
    }
    public void localTest(Timestamp begin, Timestamp end) {
        this.syncData(begin, end);
    }

}
