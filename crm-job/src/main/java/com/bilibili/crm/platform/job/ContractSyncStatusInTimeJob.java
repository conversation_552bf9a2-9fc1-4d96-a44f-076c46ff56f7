package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.statusmachine.contract.ContractStatusMachine;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/11/5
 * 根据起止时间同步合同状态
 **/
@Component
@JobHandler("ContractSyncStatusInTimeJob")
@Slf4j
public class ContractSyncStatusInTimeJob extends IJobHandler {

    @Autowired
    private ContractStatusMachine statusMachine;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("ContractSyncStatusInTimeJob start");
        statusMachine.syncStatusInTime();
        log.info("ContractSyncStatusInTimeJob end");
        return ReturnT.SUCCESS;
    }
}
