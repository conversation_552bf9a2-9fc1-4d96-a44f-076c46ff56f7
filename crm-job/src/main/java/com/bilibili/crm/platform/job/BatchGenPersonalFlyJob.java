package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.alibaba.fastjson.TypeReference;
import com.bilibili.adp.common.bean.Operator;
import com.bilibili.bangumi.databus.service.DatabusPoolSub;
import com.bilibili.crm.platform.api.account.dto.CreatePersonalFlyDto;
import com.bilibili.crm.platform.api.account.service.IPersonalFlyAccountService;
import com.bilibili.crm.platform.biz.bean.PersonalFlyHandleBean;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.common.PersonalFlyHandleType;
import com.bilibili.crm.platform.common.PersonnalFlyAccountType;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.scheduling.concurrent.ThreadPoolTaskExecutor;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.function.Consumer;
import java.util.stream.Collectors;

/**
 * 批量插入起飞账号的 Job
 */
@Component
@JobHandler("BatchGenPersonalFlyJob")
@Slf4j
public class BatchGenPersonalFlyJob extends IJobHandler {

    @Autowired
    private ThreadPoolTaskExecutor executor;

    @Resource(name = "growUpWhiteUpDataBusPoolSub")
    private DatabusPoolSub growUpWhiteUpDataBusPoolSub;

    @Autowired
    private IPersonalFlyAccountService personalFlyAccountService;

    @Value("${dataBus.switch.archiveMessage:1}")
    private Integer dataBusSwitch;

    private final static List<Future<Integer>> futureList = new ArrayList<>();

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        if (IsValid.FALSE.getCode() == dataBusSwitch) {
            return ReturnT.SUCCESS;
        }
        log.info("BatchGenPersonalFlyAccountJob.start");

        Transaction transaction = Cat.newTransaction("JOB", "BatchGenPersonalFlyAccountJob");
        transaction.addData("futureList.size", futureList.size());
        synchronized (futureList) {
            // init
            if (CollectionUtils.isEmpty(futureList)) {
                try {
                    futureList.add(addTask());
                    log.info("add task success");
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (Exception e) {
                    log.error("submit task error", e);
                    transaction.setStatus(e);
                } finally {
                    transaction.complete();
                }
            } else {
                try {
                    Future<Integer> future = futureList.get(0);
                    if (future == null || future.get(60, TimeUnit.SECONDS) == 0) {
                        futureList.set(0, addTask());
                        log.info("add task success");
                        transaction.addData("databus health", "add again!");
                    }
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (TimeoutException e) {
                    transaction.addData("databus health", "running");
                    transaction.setStatus(Transaction.SUCCESS);
                } catch (Exception e) {
                    log.error("submit task error");
                    transaction.setStatus(e);
                } finally {
                    transaction.complete();
                }
            }
        }

        log.info("BatchGenPersonalFlyAccountJob.end");
        return ReturnT.SUCCESS;
    }

    private Future<Integer> addTask() {
        return executor.submit(() -> {
            try {
                growUpWhiteUpDataBusPoolSub.sub(this::batchHandle);
            } catch (Exception e) {
                log.error("execute.FinalDraftFlow.databus.error", e);
                return 0;
            }
            return 1;
        });
    }


    /**{"opt":1,"white_up_list":[{"mid":12434,"type":1,"ctime":"2019-09-24 20:21:21"},{"mid":12323,"type":1,"ctime":"2019-09-24 20:21:21"}]}**/
    /**解析databus中的json串信息**/
    /**批量生成个人起飞账号**/
    /**
     * 批量删除个人起飞账号
     **/
    private void batchHandle(JSONObject jsonObject) {

        log.info("收到个人起飞账号消息：{}", JSON.toJSONString(jsonObject));

        List<Long> mids = parseAccountInfo(jsonObject);

        consume(jsonObject, json -> {
            PersonalFlyHandleType handleType = PersonalFlyHandleType.getByCode(jsonObject.getInteger("opt"));
            switch (handleType) {
                case ADD:
                    personalFlyAccountService.createAccountSyn(CreatePersonalFlyDto.builder()
                            .mids(mids)
                            .roles(Lists.newArrayList(PersonnalFlyAccountType.INCENTIVE_BONUS.getCode(), PersonnalFlyAccountType.CASH.getCode()))
                            .build(), Operator.SYSTEM);
                    break;
                case DEL:
                    personalFlyAccountService.disableRoles(mids,
                            Lists.newArrayList(PersonnalFlyAccountType.INCENTIVE_BONUS.getCode()),
                            Operator.SYSTEM);
                    break;
                default:
                    return;
            }
            personalFlyAccountService.pubToPersonalFlyTopic(handleType.getCode(), mids);
        });
    }

    private List<Long> parseAccountInfo(JSONObject jsonObject) {
        if (jsonObject == null || jsonObject.getJSONArray("white_up_list") == null) {
            return Collections.emptyList();
        }

        JSONArray midsJson = jsonObject.getJSONArray("white_up_list");

        String str = JSON.toJSONString(midsJson);
        List<PersonalFlyHandleBean> list = JSON.parseObject(str, new TypeReference<List<PersonalFlyHandleBean>>() {
        });
        log.info("解析个人起飞账号消息结果为：{}", list);
        return list.stream().map(PersonalFlyHandleBean::getMid).collect(Collectors.toList());
    }

    private boolean consume(JSONObject jsonObject, Consumer<JSONObject> business) {
        Boolean result = false;
        Transaction transaction = null;
        try {
            transaction = Cat.newTransaction(CatUtils.DATA_BUS, "flyRecheck");
            transaction.addData("MSG", jsonObject.toJSONString());

            log.info("UpVideoFlyRecheckJob MSG {}", jsonObject.toJSONString());

            business.accept(jsonObject);

            transaction.setStatus(Transaction.SUCCESS);
            result = true;
        } catch (Exception e) {
            log.error("handleFlyRecheck error", e);
            if (transaction != null) {
                transaction.setStatus(e);
            }
            throw e;
        } finally {
            if (transaction != null) {
                transaction.complete();
            }
        }
        return result;
    }
}
