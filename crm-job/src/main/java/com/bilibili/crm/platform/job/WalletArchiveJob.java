package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.biz.crm.wallet.dao.write.AccWalletConsumeRecordDao;
import com.bilibili.crm.platform.biz.po.AccWalletConsumeRecordPo;
import com.bilibili.crm.platform.biz.po.AccWalletConsumeRecordPoExample;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;

/**
 * @author: brady
 * @time: 2024/9/25 11:05
 */
@Component
@JobHandler("WalletArchiveJob")
@Slf4j
public class WalletArchiveJob extends IJobHandler {
//    @Autowired
//    private IAccountWalletLogService accountWalletLogService;

    @Autowired
    private AccWalletConsumeRecordDao accWalletConsumeRecordDao;
    //    @Autowired
//    private AccAccountWalletLogDao walletLogDao;
    @Autowired
    private WxRobotService wxRobotService;

    private static final long WALLET_DATE = 92 * 24 * 60 * 60 * 1000;
    private static final long CONSUME_DATE = 11 * 24 * 60 * 60 * 1000;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

//        AccAccountWalletLogPo logMinIdA = getLogMinId();
//
//        try {
//            Thread.sleep(10000L);
//        } catch (Exception e) {
//
//        }
//        AccAccountWalletLogPo logMinIdB = getLogMinId();
//
//        if (logMinIdA.getId().equals(logMinIdB.getId()) && (Utils.getNow().getTime() - logMinIdB.getCtime().getTime()) > WALLET_DATE) {
//            log.info("WalletArchiveJob AccAccountWalletLogPo delay");
//            //通知
//            notifyWx("3332 accAccount_wallet_log表归档停滞");
//        }

        AccWalletConsumeRecordPo consumeRecordMinIdA = getConsumeRecordMinId();

        try {
            Thread.sleep(30000L);
        } catch (Exception e) {

        }
        AccWalletConsumeRecordPo consumeRecordMinIdB = getConsumeRecordMinId();

        if (consumeRecordMinIdA.getId().equals(consumeRecordMinIdB.getId()) && (Utils.getNow().getTime() - consumeRecordMinIdB.getCtime().getTime()) > CONSUME_DATE) {
            log.info("WalletArchiveJob AccWalletConsumeRecordPo delay");
            notifyWx("acc_wallet_consume_record表归档停滞");
        }
        return ReturnT.SUCCESS;
    }

    public AccWalletConsumeRecordPo getConsumeRecordMinId() {
        AccWalletConsumeRecordPoExample example = new AccWalletConsumeRecordPoExample();
        example.setOrderByClause("id asc");
        example.setLimit(1);
        List<AccWalletConsumeRecordPo> accWalletConsumeRecordPos = accWalletConsumeRecordDao.selectByExample(example);

        return accWalletConsumeRecordPos.get(0);
    }

    public void notifyWx(String content) {

        WxRobotMsg wxRobotMsg = WxRobotMsg.builder().msgtype(MsgType.MARKDOWN.getType()).markdown(new Text(content)).build();
        String robotUrl = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=7680e5ab-c32f-4fa1-b908-d775d18fb28e";
        wxRobotService.sendWeChatWork(wxRobotMsg, robotUrl);
    }

}
