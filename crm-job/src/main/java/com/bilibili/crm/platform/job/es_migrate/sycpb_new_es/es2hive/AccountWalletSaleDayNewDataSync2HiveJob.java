package com.bilibili.crm.platform.job.es_migrate.sycpb_new_es.es2hive;

import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.po.ESAccountWalletSaleDayPo;
import com.bilibili.crm.platform.adx.biz.service.EsToHiveInterface;
import com.bilibili.crm.platform.job.es_migrate.AbstractEsDataSyncJob;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

@Slf4j
@Component
@JobHandler("AccountWalletSaleDayNewDataSync2HiveJob")
public class AccountWalletSaleDayNewDataSync2HiveJob extends AbstractEsDataSyncJob<ESAccountWalletSaleDayPo> {

    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsToHiveInterface esToHiveInterface;

    @Override
    protected String getIndexName() {
        return "account_wallet_sale_day_new";
    }

    @Override
    protected Class<ESAccountWalletSaleDayPo> getPoClass() {
        return ESAccountWalletSaleDayPo.class;
    }

    @Override
    protected ElasticsearchTemplate getElasticsearchTemplate() {
        return elasticsearchTemplate;
    }

    @Override
    protected RangeQueryBuilder customQuery(Timestamp begin, Timestamp end) {
        return null;
    }

    @Override
    protected void processData(List<ESAccountWalletSaleDayPo> content, String index) {
        esToHiveInterface.putLancer(index, EsMigrateCommon.JOB, content);
    }

}
