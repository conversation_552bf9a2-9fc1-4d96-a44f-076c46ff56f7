package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.cache.WxAppAgentCacheWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheLoader;
import com.bilibili.crm.platform.app.cache.WxAppCustomerCacheWriter;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;

@Component
@JobHandler("WxAppCacheAgentJob")
@Slf4j
public class WxAppCacheAgentJob extends IJobHandler {


    @Autowired
    private WxAppAgentCacheWriter wxAppAgentCacheWriter;
    @Autowired
    private WxAppCacheLoader cacheLoader;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    /**
     * @param args exp：args:20200714~20200714
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {

        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (!StringUtils.hasText(args)) {
                //检查当天状态位
                Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
                if (cacheLoader.isModulePaperStateReady(day, WxModuleType.AGENT)) {
                    Cat.logEvent("WxAppCacheAgentJob_DATA_HAD_DOWN", day.toString());
                    return ReturnT.SUCCESS;
                }
            }

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);
            Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
            // 对历史季度最后一天回刷
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                dayList.add(timestamp);
            }

            dayList.forEach(d -> {
                try {
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                        // 缓存计算数据
                        try {
                            wxAppAgentCacheWriter.cacheCalculateData(d);
                        } catch (Exception e) {
                            Cat.logEvent("Exception_WxAppCacheAgentJob", e.getMessage());
                            throw new RuntimeException(e);
                        }
                        // 缓存请求
                        wxAppAgentCacheWriter.cacheRequest(d);

                    });
                    Cat.logEvent("WxAppCacheAgentJob_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    //刷数据记录Cat
                    Cat.logEvent("WxAppCacheAgentJob_SUCCESS", d.toString() + ":failed");
                    throw new RuntimeException(e);
                }

            });

        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppCacheAgentJob", e.getMessage());
            throw e;
        }

        log.info("WxAppCacheAgentJob success args {}", args);

        return ReturnT.SUCCESS;
    }
}
