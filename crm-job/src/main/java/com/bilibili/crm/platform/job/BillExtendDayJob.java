package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.service.consume.BillConsumeService;
import com.bilibili.crm.platform.biz.service.consume.PickupConsumeService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @author: brady
 * @time: 2021/4/27 1:56 下午
 */
@Component
@JobHandler("BillExtendDayJob")
public class BillExtendDayJob extends IJobHandler {

    @Autowired
    private BillConsumeService billConsumeService;


    @Override
    public ReturnT<String> execute(String s) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            //账单未关账数据写入
            billConsumeService.flushDailyExtendBill();
            billConsumeService.flushAdjustBill();
        });
        return ReturnT.SUCCESS;
    }
}
