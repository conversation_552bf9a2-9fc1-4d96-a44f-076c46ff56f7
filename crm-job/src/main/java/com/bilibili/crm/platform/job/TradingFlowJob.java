package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.trading.flow.service.ITradingFlowDayService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/7/16 18:17
 * 交易流水job
 */
@Component
@JobHandler("TradingFlowJob")
@Slf4j
public class TradingFlowJob extends IJobHandler {

    @Autowired
    private ITradingFlowDayService tradingFlowDayService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        tradingFlowDayService.syncWalletLog(Utils.getToday(), null);
        return ReturnT.SUCCESS;
    }
}
