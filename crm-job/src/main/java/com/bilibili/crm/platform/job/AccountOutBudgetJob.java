package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.wallet.service.IBudgetOutExtraService;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @description: 账号超预算红包返还job
 * @author: brady
 * @date: 2021/2/6 11:47 下午
 */
@Component
@JobHandler("AccountOutBudgetJob")
@Slf4j
public class AccountOutBudgetJob extends IJobHandler {
    @Autowired
    private IBudgetOutExtraService budgetOutExtraService;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        //前日 (T,T+2)
        Timestamp date = incomeTimeUtil.getBeforeYesterday(Utils.getToday());
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> budgetOutExtraService.budgetOutExtraRedPacket(date));

        return ReturnT.SUCCESS;
    }
}
