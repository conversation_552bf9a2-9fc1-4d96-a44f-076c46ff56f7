package com.bilibili.crm.platform.job.operation;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.operation.service.OperationConfigJobService;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 *
 * <AUTHOR>
 * date 2024/11/17 17:14.
 * Contact: <EMAIL>.
 */
@Component
@JobHandler("operationRefreshFinalJob")
@Slf4j
public class OperationRefreshFinalJob extends IJobHandler {
    @Autowired
    private CrmCacheManager crmCacheManager;
    @Value("${crm.portal.env:prd}")
    private String env;
    @Autowired
    private OperationConfigJobService operationConfigJobService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        Timestamp timestamp = Utils.getBeginOfDay(Utils.getYesteday());
        Object cache = crmCacheManager.getValue("wg03_info_operationRefreshFinalJob_Flag_" + CrmUtils.formatDate(timestamp, CrmUtils.YYYYMMDD));
        if (Objects.nonNull(cache) && Objects.equals(env, "prd")) {
            log.info("operationRefreshFinalJob_Have_Cache_Success");
            return ReturnT.SUCCESS;
        }
        operationConfigJobService.refreshFinalOperationForJob(null, null, null, null);
        crmCacheManager.setValueWithTime("wg03_info_operationRefreshFinalJob_Flag_" + CrmUtils.formatDate(timestamp, CrmUtils.YYYYMMDD), "1", TimeUnit.DAYS, 1);
        return ReturnT.SUCCESS;
    }


}

