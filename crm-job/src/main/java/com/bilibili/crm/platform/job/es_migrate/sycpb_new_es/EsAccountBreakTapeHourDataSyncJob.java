package com.bilibili.crm.platform.job.es_migrate.sycpb_new_es;

import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.migrate.EsBreakTapeAccountHourNewPo;
import com.bilibili.crm.platform.biz.elasticsearch.new_es.po.EsBreakTapeAccountHourPo;
import com.bilibili.crm.platform.job.es_migrate.AbstractEsDataSyncJob;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Component
@JobHandler("EsAccountBreakTapeHourDataSyncJob")
public class EsAccountBreakTapeHourDataSyncJob extends AbstractEsDataSyncJob<EsBreakTapeAccountHourPo> {

    @Resource(name = "elasticsearchTemplate")
    private ElasticsearchTemplate elasticsearchTemplate;
    @Autowired
    private EsMigrateMessageSender<EsBreakTapeAccountHourNewPo> esBreakTapeAccountHourNewPoMessageSender;

    @Override
    protected String getIndexName() {
        return "es_account_break_tape_hour";
    }

    @Override
    protected Class<EsBreakTapeAccountHourPo> getPoClass() {
        return EsBreakTapeAccountHourPo.class;
    }

    @Override
    protected ElasticsearchTemplate getElasticsearchTemplate() {
        return elasticsearchTemplate;
    }

    @Override
    protected void processData(List<EsBreakTapeAccountHourPo> content, String index) {
        List<EsBreakTapeAccountHourNewPo> newContent = convertToNewPo(content);
        esBreakTapeAccountHourNewPoMessageSender.sendBatch(newContent, index, EsMigrateCommon.JOB);
    }

    private List<EsBreakTapeAccountHourNewPo> convertToNewPo(List<EsBreakTapeAccountHourPo> content) {
        return content.stream()
                .map(this::convertSinglePo)
                .collect(Collectors.toList());
    }

    private EsBreakTapeAccountHourNewPo convertSinglePo(EsBreakTapeAccountHourPo po) {
        EsBreakTapeAccountHourNewPo newPo = new EsBreakTapeAccountHourNewPo();
        BeanUtils.copyProperties(po, newPo);
        return newPo;
    }

    // 后门
    public void localTest() {
        this.syncData();
    }
    public void localTest(Timestamp begin, Timestamp end) {
        this.syncData(begin, end);
    }

}
