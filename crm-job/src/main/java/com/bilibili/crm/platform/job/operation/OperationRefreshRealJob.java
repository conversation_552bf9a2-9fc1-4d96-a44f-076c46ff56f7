package com.bilibili.crm.platform.job.operation;

import com.bilibili.crm.biz.operation.service.OperationConfigJobService;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * <AUTHOR>
 * date 2024/11/17 17:14.
 * Contact: <EMAIL>.
 */
@Component
@JobHandler("operationRefreshRealJob")
@Slf4j
public class OperationRefreshRealJob extends AbstractBaseJobHandler {

    @Autowired
    private OperationConfigJobService operationConfigJobService;

    @Override
    protected boolean biz(String args) {
        operationConfigJobService.refreshRealOperationForJob();
        return true;
    }

}

