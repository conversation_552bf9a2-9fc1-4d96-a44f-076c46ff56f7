package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.cache.WxAppAgentCacheWriter;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;

@Component
@JobHandler("WxAppCacheAgentInitJob")
@Slf4j
public class WxAppCacheAgentInitJob extends IJobHandler {

    @Autowired
    private WxAppAgentCacheWriter wxAppAgentCacheWriter;

    /**
     * @param args exp：exp：args:20200714~20200714
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        //Long s = System.currentTimeMillis();
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);

            dayList.forEach(d -> {
                try {
                    wxAppAgentCacheWriter.cacheCompositionYesterday(d);
                    Cat.logEvent("WxAppCacheAgentInitJob_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    Cat.logEvent("WxAppCacheAgentInitJob_SUCCESS", d.toString() + ":failed");
                }
                try {
                    Thread.sleep(60000L);
                } catch (InterruptedException e) {
                    log.info("WxAppCacheAgentInitJob InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }
            });

        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppCacheAgentInitJob", e.getMessage());
            throw e;
        }
        return ReturnT.SUCCESS;
    }
}


