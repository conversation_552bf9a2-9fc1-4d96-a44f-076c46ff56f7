package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * @description:
 * @author: brady
 * @time: 2020/10/19 3:59 下午
 */
@Component
@JobHandler("BsiOpportunityNotifyJob")
@Slf4j
public class BsiOpportunityNotifyJob extends IJobHandler {
    @Autowired
    private IBsiOpportunityService bsiOpportunityService;

    /**
     * 商单失效通知以 (创建商机53,58天后各提醒一次)
     * @param args
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> bsiOpportunityService.bsiOpportunityNotify());

        return ReturnT.SUCCESS;
    }
}