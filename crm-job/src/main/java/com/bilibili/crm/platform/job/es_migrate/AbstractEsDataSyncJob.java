package com.bilibili.crm.platform.job.es_migrate;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz_common.olap.config.HotKey;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.platform.adx.biz.common.EsMigrateCommon;
import com.bilibili.crm.platform.adx.biz.service.EsMigrateMessageSender;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.fasterxml.jackson.databind.*;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.elasticsearch.action.search.SearchResponse;
import org.elasticsearch.index.query.BoolQueryBuilder;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.RangeQueryBuilder;
import org.elasticsearch.search.SearchHit;
import org.elasticsearch.search.SearchHits;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.data.elasticsearch.core.ElasticsearchTemplate;
import org.springframework.data.elasticsearch.core.ScrolledPage;
import org.springframework.data.elasticsearch.core.SearchResultMapper;
import org.springframework.data.elasticsearch.core.aggregation.AggregatedPage;
import org.springframework.data.elasticsearch.core.aggregation.impl.AggregatedPageImpl;
import org.springframework.data.elasticsearch.core.query.NativeSearchQueryBuilder;
import org.springframework.data.elasticsearch.core.query.SearchQuery;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;
import java.text.SimpleDateFormat;
import java.util.*;

@Slf4j
@Component
public abstract class AbstractEsDataSyncJob<T> extends AbstractBaseJobHandler {

    protected static final ObjectMapper globalObjectMapper = new ObjectMapper();

    @Autowired
    private PaladinConfig paladinConfig;
    @Autowired
    private EsMigrateMessageSender<T> messageSender;

    @Override
    public boolean biz(String dateStr)  {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            // 格式如: 2024-10-01,2024-10-31
            if (StringUtils.isNotEmpty(dateStr)) {
                String[] dates = dateStr.split(",");
                Timestamp begin = Utils.getTimestamp(dates[0]);
                Timestamp end = Utils.getTimestamp(dates[1]);
                syncData(begin, end);
            } else {
                syncData();
            }
        });

        return true;
    }

    /**
     * 获取索引名称
     * @return
     */
    protected abstract String getIndexName();

    /**
     * 获取索引映射的po的类型
     * @return
     */
    protected abstract Class<T> getPoClass();

    protected abstract ElasticsearchTemplate getElasticsearchTemplate();

    public void syncData() {
        doSyncData(null, null, null);
    }

    public void syncData(Timestamp begin, Timestamp end) {
        doSyncData(begin, end, null);
    }


    /**
     * 数据同步
     * @param begin
     * @param end
     */
    public void doSyncData(Timestamp begin, Timestamp end, String indexName) {
        String index = StringUtils.isEmpty(indexName) ? getIndexName() : indexName;
        log.info("{} 开始处理", index);
        BoolQueryBuilder queryBuilder = QueryBuilders.boolQuery();
        RangeQueryBuilder customQueryBuilder = customQuery(begin, end);
        if (customQueryBuilder != null) {
            queryBuilder.must(customQueryBuilder);
        }

        NativeSearchQueryBuilder searchQueryBuilder = new NativeSearchQueryBuilder()
                .withIndices(index)
                .withQuery(queryBuilder)
                .withPageable(PageRequest.of(0, paladinConfig.getIntegerOrDefault(HotKey.esMigratePageSize, 1000)));
        addSort(searchQueryBuilder);
        SearchQuery searchQuery = searchQueryBuilder.build();

        ElasticsearchTemplate elasticsearchTemplate = getElasticsearchTemplate();
        Integer scrollTimeout = paladinConfig.getIntegerOrDefault(HotKey.esMigrateScrollTimeout, 5 * 60 * 1000);
        ScrolledPage<T> scroll = null;
        try {
            scroll = (ScrolledPage<T>) elasticsearchTemplate.startScroll(scrollTimeout, searchQuery, getPoClass());
            log.info("{} scroll查询总命中数：{}", index, scroll.getTotalElements());

            while (scroll.hasContent()) {

                // 处理本次查出的数据
                processData(scroll.getContent(), index);

                // 万一es崩了，打开降级，抢救一下
                if (paladinConfig.switchOn(HotKey.esMigrateScrollDegrade)) {
                    throw new RuntimeException("scroll查询降级");
                }

                // 继续滚动
                scroll = (ScrolledPage<T>) elasticsearchTemplate.continueScroll(scroll.getScrollId(), scrollTimeout, getPoClass());
            }
        } catch (Exception e) {
            log.error("{} 数据同步失败: {}", index, e.getMessage(), e);
        } finally {
            if (scroll != null) {
                elasticsearchTemplate.clearScroll(scroll.getScrollId());
            }
        }

        log.info("{} 处理结束", index);
    }

    protected void processData(List<T> content, String index) {
        messageSender.sendBatch(content, index, EsMigrateCommon.JOB);
    }

    protected RangeQueryBuilder customQuery(Timestamp begin, Timestamp end) {
        return null;
    }

    protected void addSort(NativeSearchQueryBuilder searchSourceBuilder) {
        // 默认不排序
    }

    public void doSyncDataForMultipleIndices(Timestamp begin, Timestamp end) {
        // 获取时间范围内的所有索引名称
        List<String> indexNames = getIndexNamesByDateRange(getIndexName(), begin, end, getIndexFormat());

        // 对每个索引分别调用原来的 doSyncData 方法
        for (String indexName : indexNames) {
            doSyncData(begin, end, indexName);
        }
    }

    protected static List<String> getIndexNamesByDateRange(String baseIndexName, Timestamp begin, Timestamp end, String indexFormat) {
        Calendar calendar = Calendar.getInstance();
        calendar.setTimeInMillis(begin.getTime());

        List<String> indexNames = new ArrayList<>();
        SimpleDateFormat format = new SimpleDateFormat(indexFormat);

        while (! calendar.getTime().after(new Date(end.getTime()))) {
            String indexName = baseIndexName + "_" + format.format(calendar.getTime());
            indexNames.add(indexName);

            calendar.add(Calendar.MONTH, 1);
        }

        return indexNames;
    }

    /**
     * 获取索引名称的时间格式
     * @return 时间格式字符串，如 "yyyy_MM" 或 "yyyyMM"
     */
    protected String getIndexFormat() {
        return"yyyy_MM";
    }

    private final SearchResultMapper searchResultMapper = new SearchResultMapper() {
        private final ObjectMapper objectMapper = customizeObjectMapper();

        @Override
        public <T> AggregatedPage<T> mapResults(SearchResponse response, Class<T> aClass, Pageable pageable) {
            List<T> result = new ArrayList<>();
            SearchHits hits = response.getHits();
            if (hits.getHits().length <= 0) {
                return new AggregatedPageImpl<>(Collections.emptyList(), pageable, hits.getTotalHits(), response.getScrollId());
            }

            for (SearchHit hit : hits) {
                try {
                    T po = objectMapper.readValue(hit.getSourceAsString(), aClass);
                    result.add(po);
                } catch (Exception e) {
                    log.error("Error mapping search hit to object: {}", e.getMessage(), e);
                }
            }

            return new AggregatedPageImpl<>(result, pageable, hits.getTotalHits(), response.getScrollId());
        }
    };

    /**
     * 子类可以覆盖此方法以提供自定义的 ObjectMapper 配置
     */
    protected ObjectMapper customizeObjectMapper() {
        return globalObjectMapper;
    }


}
