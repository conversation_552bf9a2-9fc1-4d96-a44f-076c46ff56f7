package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.writer.WxSellGoodDashboardWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;

/**
 * @author: mort
 * @mail: <EMAIL>
 * @time: 2023-02-06 17:03:07
 * @description:
 **/

@Component
@JobHandler("wxSellGoodsDashboardJob")
@Slf4j
public class WxSellGoodsDashboardJob extends IJobHandler {

    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private WxSellGoodDashboardWriter wxSellGoodDashboardWriter;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;
    @Autowired
    private WxAppDataCheckService dataCheckService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {

        Timestamp begin = Utils.getToday();
        Timestamp end = Utils.getEndOfDay(Utils.getToday());

        if (!StringUtils.hasText(args)) {
            //检查当天状态位
            Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
            if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SELL_GOODS_DASHBOARD)) {
                Cat.logEvent("WxAppCacheSellGoodsJob_DATA_HAD_DOWN", day.toString());
                return ReturnT.SUCCESS;
            }
        }

        if (StringUtils.hasText(args) && args.contains("~")) {
            String[] strings = args.split("~");
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }

        List<Timestamp> dayList = Utils.getEachDays(begin, end);

        dayList.forEach(d -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                try {
                    //检查ck昨日数据是否ready
                    if (!dataCheckService.mobileDataCheckByState(Utils.getSomeDayAgo(d, 1), dataCheckService.generateSellGoodFlyCkKey(Utils.getSomeDayAgo(d, 1)))) {
                        return;
                    }

                    wxSellGoodDashboardWriter.buildData(d);
                    Cat.logEvent("WxAppCacheSellGoodsJob_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    Cat.logEvent("Exception_WxSellGoodsDashboardJob", e.getMessage());
                    Cat.logEvent("WxAppCacheSellGoodsJob_FAIL", d.toString() + ":failed");
                    throw new RuntimeException(e);
                }
            });
        });
        return ReturnT.SUCCESS;
    }
}
