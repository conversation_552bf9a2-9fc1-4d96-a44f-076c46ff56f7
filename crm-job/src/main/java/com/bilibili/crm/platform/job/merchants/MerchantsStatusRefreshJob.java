package com.bilibili.crm.platform.job.merchants;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.biz.customer_operate_label.bo.QueryCustomerOperateLabelParam;
import com.bilibili.crm.biz.customer_operate_label.dto.CustomerOperateLabelDto;
import com.bilibili.crm.biz.customer_operate_label.enums.CustomerOperateLabelStatusEnum;
import com.bilibili.crm.biz.merchants.bo.QueryMerchantsProjectParam;
import com.bilibili.crm.biz.merchants.bo.UpdateMerchantsProjectParam;
import com.bilibili.crm.biz.merchants.config.MerchantsConfig;
import com.bilibili.crm.biz.merchants.dto.MerchantsProjectDto;
import com.bilibili.crm.biz.merchants.enums.MerchantsPricingEnum;
import com.bilibili.crm.biz.merchants.enums.MerchantsRackEnum;
import com.bilibili.crm.biz.merchants.enums.MerchantsStatusEnum;
import com.bilibili.crm.biz.merchants.service.MerchantsService;
import com.bilibili.crm.biz.weixin.service.WxMessageService;
import com.bilibili.crm.platform.api.log.operator.dto.NewLogOperatorDto;
import com.bilibili.crm.platform.api.log.operator.service.ILogOperatorService;
import com.bilibili.crm.platform.biz.util.MDCDecoratorUtil;
import com.bilibili.crm.platform.common.ModifyType;
import com.bilibili.crm.platform.common.Module;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @date 2024/11/17
 */
@Component
@JobHandler("MerchantsStatusRefreshJob")
@Slf4j
public class MerchantsStatusRefreshJob extends IJobHandler {

    private static final int PAGE_SIZE = 10;

    @Resource
    private MerchantsService merchantsService;

    @Resource
    private WxMessageService wxMessageService;

    @Resource
    private MerchantsConfig merchantsConfig;

    @Resource
    private ILogOperatorService iLogOperatorService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("MerchantsStatusRefreshJob start");
        StopWatch started = StopWatch.createStarted();
        handlePending();
        handleEffecting();
        log.info("MerchantsStatusRefreshJob end, cost = {}", started.getTime(TimeUnit.MILLISECONDS));
        return ReturnT.SUCCESS;
    }

    private void handlePending() throws InterruptedException {
        QueryMerchantsProjectParam param = new QueryMerchantsProjectParam();
        param.setMerchantStatus(Lists.newArrayList(MerchantsStatusEnum.APPROVE_ADOPT.getCode()));
        param.setPricingStatus(MerchantsPricingEnum.APPROVE_ADOPT.getCode());

        param.setInvestmentBeginTimeLe(LocalDateTime.now());
        pageHandle(param, MerchantsStatusEnum.EFFECTING, MerchantsRackEnum.ONLINE);
    }

    private void handleEffecting() throws InterruptedException {
        QueryMerchantsProjectParam param = new QueryMerchantsProjectParam();
        param.setMerchantStatus(Lists.newArrayList(MerchantsStatusEnum.EFFECTING.getCode()));

        param.setInvestmentEndTimeLt(LocalDateTime.now());
        pageHandle(param, MerchantsStatusEnum.EXPIRED, MerchantsRackEnum.OFFLINE);
    }

    public void pageHandle(QueryMerchantsProjectParam param, MerchantsStatusEnum status, MerchantsRackEnum rackStatus) throws InterruptedException {
        param.setSize(PAGE_SIZE);
        int page = 1;
        int pageCount;
        do {
            param.setPage(page);
            Pagination<List<MerchantsProjectDto>> pagination = merchantsService.pageQuery(param);
            List<MerchantsProjectDto> data = pagination.getData();
            if (CollectionUtils.isEmpty(data)) {
                return;
            }
            data.forEach(d -> {
                Integer investmentLongTimeFlag = d.getInvestmentLongTimeFlag();
                if (investmentLongTimeFlag == 1 && status == MerchantsStatusEnum.EXPIRED) {
                    return;
                }

                if (null == d.getInvestmentBeginTime()) {
                    return;
                }

                merchantsService.update("", buildParam(d, status, rackStatus));
                if (MerchantsRackEnum.ONLINE == rackStatus) {
                    insertLogCaseRackOnline(d.getId());
                }
                List<String> msgUsers = Lists.newArrayList(d.getCreator());
                msgUsers.addAll(d.getBusinessContacts());
                sendMessage(msgUsers, rackStatus, d);
            });
            pageCount = pagination.getPage();
            page++;
            Thread.sleep(10);
        } while (pageCount >= page);
    }

    private UpdateMerchantsProjectParam buildParam(MerchantsProjectDto dto, MerchantsStatusEnum status, MerchantsRackEnum rackEnum) {
        UpdateMerchantsProjectParam updateParam = new UpdateMerchantsProjectParam();
        updateParam.setFlowId(dto.getFlowId());
        updateParam.setId(dto.getId());
        updateParam.setUpdateType((byte) 2);
        updateParam.setMerchantStatus(status.getCode());
        updateParam.setRackStatus(rackEnum.getCode());
        if (rackEnum == MerchantsRackEnum.ONLINE) {
            updateParam.setHisRackStatus(MerchantsRackEnum.ONLINE.getCode());
        }
        return updateParam;
    }

    private void sendMessage(List<String> users, MerchantsRackEnum merchantsRackEnum, MerchantsProjectDto dto) {
        CompletableFuture.runAsync(MDCDecoratorUtil.runnable(() -> {
            if (CollectionUtils.isEmpty(users)) {
                log.warn("sendMessage users is blank");
                return;
            }
            String content;
            if (merchantsRackEnum == MerchantsRackEnum.OFFLINE) {
                content = String.format(MerchantsConfig.RACK_OFFLINE_APPROVE, dto.getProjectName(), dto.getId(), merchantsConfig.merchantsUrl);
            } else {
                content = String.format(MerchantsConfig.RACK_ONLINE, dto.getProjectName(), dto.getId(), merchantsConfig.merchantsUrl);
            }
            wxMessageService.sendMsg(users, content, null);
        }));
    }

    private void insertLogCaseRackOnline(Integer projectId) {
        Operator operator = new Operator();
        operator.setOperatorName("system-job");
        NewLogOperatorDto operatorDto = new NewLogOperatorDto();
        operatorDto.setModule(Module.MERCHANTS);
        operatorDto.setModifyType(ModifyType.MERCHANTS_RACK_AUTO_ONLINE);
        operatorDto.setObjId(projectId);
        iLogOperatorService.insertLog(operator, operatorDto);
    }
}
