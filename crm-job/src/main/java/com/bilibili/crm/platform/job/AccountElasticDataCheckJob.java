package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.account.bean.AccAccountEsBean;
import com.bilibili.crm.biz.account.bean.AccountQuery;
import com.bilibili.crm.biz.account.olap.AccountOlapService;
import com.bilibili.crm.platform.api.account.dto.BiliUserType;
import com.bilibili.crm.platform.api.exception.code.CrmAppExceptionCode;
//import com.bilibili.crm.platform.biz.dao.AccAccountWalletDao;
import com.bilibili.crm.platform.biz.crm.wallet.dao.write.AccAccountWalletDao;
import com.bilibili.crm.platform.biz.dao.CrmSaleSeaBelongingDao;
import com.bilibili.crm.platform.biz.crm.account.dao.read.AccAccountExtDao;
import com.bilibili.crm.platform.biz.new_platform.dao.CrmAccountAdStatDao;
import com.bilibili.crm.platform.biz.new_platform.po.CrmAccountAdStatPo;
import com.bilibili.crm.platform.biz.new_platform.po.CrmAccountAdStatPoExample;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.crm.account.dao.read.AccAccountReadOnlyDao;
import com.bilibili.crm.platform.common.DbId;
import com.bilibili.crm.platform.common.IsValid;
import com.bilibili.crm.platform.core.*;
import com.bilibili.crm.platform.support.metrics.Metrics;
import com.bilibili.crm.platform.support.metrics.MetricsNames;
import com.google.common.collect.Lists;
import com.google.common.util.concurrent.RateLimiter;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Objects;
import java.util.stream.Collectors;

/**
 * 比较mysql db表数据与es数据的一致性，
 * <p>
 * 注意：支持检测
 * 1、在同时执行检索服务Elasticsearch平台增量 & 全量索引写入时平台无法保证的的数据一致性问题。
 * 2、dts任务消息丢失等非可预见的问题。
 * 原理很简单，只是简单比较数据的mtime。检索服务Elasticsearch平台配置了多个链路，每个链路都需要检查。
 * <p>
 * 支持传参指定mysql db表扫描的id范围，或者通过实现{@link AbstractTaskProcessor#getMinAndMaxId(Parameter)}方法查询当前定时扫描的mysql db表的id范围。
 */
@Slf4j
@Component
@JobHandler("accountElasticDataCheckJob")
public class AccountElasticDataCheckJob extends IJobHandler {

    @Autowired
    private AccAccountReadOnlyDao accAccountReadOnlyDao;
    @Autowired
    private AccAccountExtDao accAccountExtDao;
    @Autowired
    private CrmSaleSeaBelongingDao saleSeaBelongingDao;
    @Autowired
    private CrmAccountAdStatDao accountAdStatDao;
    @Autowired
    private AccAccountWalletDao accAccountWalletDao;
    @Autowired
    private AccountOlapService accountOlapService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Parameter<Void> parameter = Parameter.getParameter(param, Void.class);
        TaskProcessorConf conf = TaskProcessorConf.newBuilder()
                .withIsSkipException(true)
                .withSkipExceptions(Exception.class)
                .withUseThreadPool(true)
                .withIsShards(Objects.isNull(parameter.getSharding()) ? false : parameter.getSharding())
                .withTaskName("accountElasticDataCheckJob")
                .build();
        RateLimiter rateLimiter = RateLimiter.create(Objects.isNull(parameter.getRateLimiter()) ? 30 : parameter.getRateLimiter());
        return new IDPaginationTaskProcessor<Parameter<Void>, AccAccountPo>(conf) {

            @Override
            public DbId getMinAndMaxId(Parameter<Void> parameter) {
                AccAccountPoExample example = new AccAccountPoExample();
                return accAccountExtDao.minMaxIdsByExample(example);
            }

            @Override
            public Boolean isProcess(LoopContext loopContext, int shards, int index, AccAccountPo accAccountPo) {
                if (shards <= 0) {
                    return true;
                }
                return accAccountPo.getAccountId() % shards == index;
            }

            @Override
            public List<AccAccountPo> fetchData(LoopContext loopContext, long startId, long endId) {
                AccAccountPoExample example = new AccAccountPoExample();
                example.createCriteria()
                        .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                        .andAccountIdGreaterThanOrEqualTo((int) startId)
                        .andAccountIdLessThan((int) endId);
                return accAccountReadOnlyDao.selectByExample(example);
            }

            @Override
            public void processData(LoopContext loopContext, AccAccountPo accAccountPo) throws Exception {
                rateLimiter.acquire();

                // 链路1: 账号只关注es字段：mtime

                // 链路2: 客销关系关注es字段：
                // （1）direct_sale_id（is_deleted = 0 AND is_current =1 AND bili_user_type = 2）
                // （2）channel_sale_id（is_deleted = 0 AND is_current =1 AND bili_user_type = 1）
                List<CrmSaleSeaBelongingPo> saleSeaBelongingPos = getSaleSeaBelongingPos(accAccountPo.getAccountId());
                List<Integer> directSaleIds = Lists.newArrayList();
                List<Integer> channelSaleIds = Lists.newArrayList();
                if (CollectionUtils.isNotEmpty(saleSeaBelongingPos)) {
                    directSaleIds = saleSeaBelongingPos.stream()
                            .filter(saleSeaBelongingPo -> BiliUserType.STRAIGHT_MANAGER.getCode().equals(saleSeaBelongingPo.getBiliUserType()))
                            .map(CrmSaleSeaBelongingPo::getSaleId).collect(Collectors.toList());
                    channelSaleIds = saleSeaBelongingPos.stream()
                            .filter(saleSeaBelongingPo -> BiliUserType.CHANNEL_MANAGER.getCode().equals(saleSeaBelongingPo.getBiliUserType()))
                            .map(CrmSaleSeaBelongingPo::getSaleId).collect(Collectors.toList());
                }

                // 链路3: 账号每日花费汇总只关注es字段：today_cost
                Long todayCost = getTodayCost(accAccountPo.getAccountId());

                // 链路4：账号钱包 关注es字段：cash、red_packet、special_red_packet、credit
                AccAccountWalletPo accAccountWalletPo = getAccountWallet(accAccountPo.getAccountId());

                AccountQuery accountQuery = new AccountQuery();
                accountQuery.setAccountId(accAccountPo.getAccountId());
                List<AccAccountEsBean> accAccountEsBeans = accountOlapService.doBatchSearch(accountQuery);
                if (CollectionUtils.isNotEmpty(accAccountEsBeans)) {
                    for (AccAccountEsBean accAccountEsBean : accAccountEsBeans) {
                        // 链路1: 账号只关注es字段：mtime
                        if (!Objects.equals(accAccountPo.getMtime(), accAccountEsBean.getMtime())) {
                            log.warn("accountElasticDataCheckJob check mtime error, accountId={}, mtime db:{}, mtime es:{}",
                                    accAccountPo.getAccountId(), accAccountPo.getMtime(), accAccountEsBean.getMtime());
                            Metrics.oneErr(MetricsNames.XXL_JOB, "mtime", CrmAppExceptionCode.BUSINESS_ERROR, "taskName", "accountElasticDataCheckJob");
                            return;
                        }
                        // 链路2
                        if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(directSaleIds, accAccountEsBean.getDirectSaleId()))) {
                            log.warn("accountElasticDataCheckJob check direct_sale_id error, accountId={}, directSaleIds db:{}, directSaleIds es:{}",
                                    accAccountPo.getAccountId(), directSaleIds, accAccountEsBean.getDirectSaleId());
                            Metrics.oneErr(MetricsNames.XXL_JOB, "direct_sale_id", CrmAppExceptionCode.BUSINESS_ERROR, "taskName", "accountElasticDataCheckJob");
                            return;
                        }
                        if (CollectionUtils.isNotEmpty(CollectionUtils.disjunction(channelSaleIds, accAccountEsBean.getChannelSaleId()))) {
                            log.warn("accountElasticDataCheckJob check channel_sale_id error, accountId={}, channelSaleIds db:{}, channelSaleIds es:{}",
                                    accAccountPo.getAccountId(), channelSaleIds, accAccountEsBean.getChannelSaleId());
                            Metrics.oneErr(MetricsNames.XXL_JOB, "channel_sale_id", CrmAppExceptionCode.BUSINESS_ERROR, "taskName", "accountElasticDataCheckJob");
                            return;
                        }
                        // 链路3
                        if (!Objects.equals(todayCost, accAccountEsBean.getTodayCost())) {
                            log.warn("accountElasticDataCheckJob check today_cost error, accountId={}, todayCost db:{}, todayCost es:{}",
                                    accAccountPo.getAccountId(), todayCost, accAccountEsBean.getTodayCost());
                            Metrics.oneErr(MetricsNames.XXL_JOB, "today_cost", CrmAppExceptionCode.BUSINESS_ERROR, "taskName", "accountElasticDataCheckJob");
                            return;
                        }
                        // 链路4
                        if (Objects.nonNull(accAccountWalletPo) && (
                                !Objects.equals(accAccountWalletPo.getCash(), accAccountEsBean.getCash()) ||
                                        !Objects.equals(accAccountWalletPo.getRedPacket(), accAccountEsBean.getRedPacket()) ||
                                        !Objects.equals(accAccountWalletPo.getSpecialRedPacket(), accAccountEsBean.getSpecialRedPacket()) ||
                                        !Objects.equals(accAccountWalletPo.getCredit(), accAccountEsBean.getCredit())
                                )) {
                            log.warn("accountElasticDataCheckJob check account_wallet error, accountId={}",
                                    accAccountPo.getAccountId());
                            Metrics.oneErr(MetricsNames.XXL_JOB, "account_wallet", CrmAppExceptionCode.BUSINESS_ERROR, "taskName", "accountElasticDataCheckJob");
                            return;
                        }
                    }
                }
            }
        }.process(parameter);
    }

    private List<CrmSaleSeaBelongingPo> getSaleSeaBelongingPos(Integer accountId) {
        CrmSaleSeaBelongingPoExample example = new CrmSaleSeaBelongingPoExample();
        example.createCriteria()
                .andAccountIdEqualTo(accountId)
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andIsCurrentEqualTo(IsValid.TRUE.getCode())
                .andBiliUserTypeIn(Lists.newArrayList(BiliUserType.CHANNEL_MANAGER.getCode(), BiliUserType.STRAIGHT_MANAGER.getCode()));
        return saleSeaBelongingDao.selectByExample(example);
    }

    private Long getTodayCost(Integer accountId) {
        CrmAccountAdStatPoExample example = new CrmAccountAdStatPoExample();
        example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId)
                .andBelongDateEqualTo(Utils.getToday());
        List<CrmAccountAdStatPo> accountAdStatPos = accountAdStatDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accountAdStatPos)) {
            return null;
        }
        return accountAdStatPos.get(0).getTodayCost();
    }

    private AccAccountWalletPo getAccountWallet(Integer accountId) {
        AccAccountWalletPoExample example = new AccAccountWalletPoExample();
        example.createCriteria()
                .andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andAccountIdEqualTo(accountId);
        List<AccAccountWalletPo> accAccountWalletPos = accAccountWalletDao.selectByExample(example);
        if (CollectionUtils.isEmpty(accAccountWalletPos)) {
            return null;
        }
        return accAccountWalletPos.get(0);
    }
}
