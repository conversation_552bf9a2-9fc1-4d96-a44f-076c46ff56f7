package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.bo.PriSeaCustomerItemBo;
import com.bilibili.crm.platform.app.cache.sale_efficiency.SaleEfficiencyBasicService;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.oneservice.OneServiceGrpcService;
import com.bilibili.crm.platform.biz.oneservice.OneServiceQueryObject;
import com.bilibili.crm.platform.biz.oneservice.OneServiceScene;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.bo.SaleEfficiencySummaryTeamRequest;
import com.bilibili.crm.platform.job.common.AbstractBaseParamJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2024-03-10 11:22:50
 * @description:
 **/
@Component
@JobHandler("saleEfficiencySummaryTeamJob")
@Slf4j
public class SaleEfficiencySummaryTeamJob extends AbstractBaseParamJobHandler<SaleEfficiencySummaryTeamRequest> {

    @Resource
    private SaleEfficiencyBasicService saleEfficiencyBasicService;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    public SaleEfficiencySummaryTeamJob() {
        super(SaleEfficiencySummaryTeamRequest.class, SaleEfficiencySummaryTeamRequest::new);
    }

    @Override
    protected boolean biz(SaleEfficiencySummaryTeamRequest request) {
        Timestamp begin = Utils.getYesteday();
        Timestamp end = Utils.getYesteday();
        String priSeaQueryLogDate = CrmUtils.formatDate(begin, CrmUtils.YYYYMMDD_WITHOUT_SPLIT);

        try {
            List<Timestamp> eachDays = Utils.getEachDays(begin, end);
            Timestamp timestamp = incomeTimeUtil.getYesterdayBegin(incomeTimeUtil.getQuarterFirstDate(Utils.getNow()));
            // 对历史季度最后一天回刷
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                eachDays.add(timestamp);
            }

            eachDays.forEach(day -> {
                int isQuarterEnd = 0;
                if (day.compareTo(incomeTimeUtil.getQuarterEndDate(day)) == 0 && Utils.getEachDays(day, Utils.getNow()).size() >= 2) {
                    isQuarterEnd = 1;
                }
                saleEfficiencyBasicService.cacheRequest(
                        Utils.getBeginOfDay(day),
                        request.getSaleGroupIdList(),
                        request.getSaleIdList(),
                        isQuarterEnd, priSeaQueryLogDate);
                wxAppCacheManager.validModuleRequestStatus(Utils.getBeginOfDay(day), WxModuleType.SALE_EFFICIENCY);
                AlarmHelper.log("SaleEfficiencySummaryTeamJob_success", Utils.getBeginOfDay(day).toString());
            });

        } catch (Exception e) {
            throw new RuntimeException(e);
        }

        return true;
    }
}
