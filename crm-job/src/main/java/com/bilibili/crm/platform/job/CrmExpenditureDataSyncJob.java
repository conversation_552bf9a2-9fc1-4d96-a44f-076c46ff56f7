package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.enums.IsDeleted;
import com.bilibili.adp.common.util.CollectionWithDefaultPoolHelper;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.commercialorder.soa.order.dto.OrderInfoForCrmSynDto;
import com.bilibili.commercialorder.soa.order.dto.QueryOrderInfoForCrmReqDto;
import com.bilibili.commercialorder.soa.order.service.ISoaCmOrderService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.service.expenditure.RecordCrmExpenditureToHiveService;
import com.bilibili.crm.platform.api.account.dto.BiliUserBaseInfoDto;
import com.bilibili.crm.platform.api.cost_management.dto.BTPProjectDto;
import com.bilibili.crm.platform.api.cost_management.dto.BTPPurchaseBatchDto;
import com.bilibili.crm.platform.api.cost_management.dto.BTPPurchaseDetailDto;
import com.bilibili.crm.platform.api.cost_management.service.IBTPIntegrationService;
import com.bilibili.crm.platform.biz.dao.*;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmBtpCashAmountDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmBtpVirtualGoldAmountLogDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmExpenditureDao;
import com.bilibili.crm.platform.biz.dao.expenditure.CrmExpenditureTypeDao;
import com.bilibili.crm.platform.biz.mq.message.CrmExpenditureDataMessage;
import com.bilibili.crm.platform.biz.po.*;
import com.bilibili.crm.platform.biz.po.expenditure.*;
import com.bilibili.crm.platform.biz.service.account.bilibiliuser.BilibiliUserQuerier;
import com.bilibili.crm.platform.common.*;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@Slf4j
@JobHandler("crmExpenditureDataSyncJob")
public class CrmExpenditureDataSyncJob extends AbstractBaseJobHandler {
    private static final DateTimeFormatter DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");

    private static final List<Integer> PICKUP_ORDER_COMPLETED_STATUS = Arrays.asList(11, 110);

    private static final List<Integer> PICKUP_ORDER_ABORT_STATUS = Arrays.asList(-2, -13, -12);

    @Autowired
    private IBTPIntegrationService ibtpIntegrationService;
    @Autowired
    private CrmExpenditureDao crmExpenditureDao;
    @Autowired
    private CrmBtpVirtualGoldAmountLogDao crmBtpVirtualGoldAmountLogDao;
    @Autowired
    private CrmOrderDao crmOrderDao;
    @Autowired
    private CrmOrderExtraDao crmOrderExtraDao;
    @Autowired
    private CrmExpenditureTypeDao crmExpenditureTypeDao;
    @Autowired
    private CrmProjectItemPackageDao crmProjectItemPackageDao;
    @Autowired
    private CrmContractDao crmContractDao;
    @Autowired
    private CrmBtpCashAmountDao crmBtpCashAmountDao;
    @Autowired
    private CrmPickupSettleNewDao crmPickupSettleNewDao;
    @Autowired
    private ISoaCmOrderService iSoaCmOrderService;
    @Autowired
    private BilibiliUserQuerier bilibiliUserQuerier;
    @Autowired
    private RecordCrmExpenditureToHiveService recordCrmExpenditureToHiveService;


    @Override
    protected boolean biz(String payload) {
        long msgTime = System.currentTimeMillis();
        AlarmHelper.log("CrmExpenditureData", getTraceId(), msgTime);

        log.info("start sync crm expenditure data to hive");

        // get cash expenditure data
        List<CrmExpenditureDataMessage> crmExpenditureCashMessages = getCrmCashExpenditureMessages();

        // get virtual gold expenditure data
        List<CrmExpenditureDataMessage> crmExpenditureVirtualGoldMessages = getCrmVirtualGoldExpenditureMessages();

        // record
        crmExpenditureCashMessages.forEach(recordCrmExpenditureToHiveService::saveCrmExpenditureDataMessage);

        crmExpenditureVirtualGoldMessages.forEach(recordCrmExpenditureToHiveService::saveCrmExpenditureDataMessage);

        log.info("finished sync crm expenditure data to hive");

        return true;
    }


    public List<CrmExpenditureDataMessage> getCrmCashExpenditureMessages() {
        List<CrmBtpCashAmountPo> crmBtpCashAmountPoList = getCrmBtpCashAmountLogs();
        List<Integer> hasCashCrmExpenditureIds = crmBtpCashAmountPoList.stream().map(CrmBtpCashAmountPo::getExpenditureId).map(Long::intValue).distinct().collect(Collectors.toList());

        List<CrmExpenditurePo> crmExpenditurePoList = getCrmCashExpenditures();
        Map<Integer, CrmExpenditurePo> crmExpenditureMap = crmExpenditurePoList.stream().collect(Collectors.toMap(
                CrmExpenditurePo::getId,
                Function.identity(),
                (k1, k2) -> k1
        ));

        Map<Integer, CrmOrderPo> crmExpenditureIdCrmOrderMap = getCrmOrders(crmExpenditurePoList);
        Map<Integer, CrmProjectItemPackagePo> crmExpenditureIdCrmProjectMap = getCrmProjects(crmExpenditurePoList);
        Map<Integer, CrmContractPo> crmExpenditureIdCrmContractMap = getCrmContracts(crmExpenditurePoList);
        Map<Integer, BTPProjectDto> crmExpenditureIdBTPProjectMap = getCrmExpenditureIdBTPProjectMap(crmExpenditurePoList);
        Map<Integer, BTPPurchaseBatchDto> crmExpenditureIdBTPBatchMap = getCrmExpenditureIdBTPBatchMap(crmExpenditurePoList);
        Map<Integer, OrderInfoForCrmSynDto> crmExpenditureIdPickUpOrderMap = getCrmExpenditureIdPickUpOrderMap(crmExpenditurePoList);
        Map<Integer, String> crmExpenditureTypeIdNameMap = getCrmExpenditureTypeIdNameMap(crmExpenditurePoList);

        List<CrmExpenditureDataMessage> results = new ArrayList<>();

        List<CrmExpenditureDataMessage> hasNotBtpCashMessages = crmExpenditurePoList.stream()
                .filter(crmExpenditurePo -> crmExpenditurePo.getSource() == ExpenditureSourceType.BTP.getCode())
                .filter(crmExpenditurePo -> !hasCashCrmExpenditureIds.contains(crmExpenditurePo.getId()))
                .map(crmExpenditurePo -> {
                            Integer crmExpenditureId = crmExpenditurePo.getId();

                            Integer crmOrderId = crmExpenditurePo.getCrmOrderId();
                            Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                            String btpProjectCode = crmExpenditurePo.getBtpProjectCode();
                            String btpBatchCode = crmExpenditurePo.getBtpBatchCode();
                            String btpDetailCode = crmExpenditurePo.getBtpDetailCode();

                            BTPPurchaseBatchDto btpPurchaseBatchDto = crmExpenditureIdBTPBatchMap.get(crmExpenditureId);
                            CrmOrderPo crmOrderPo = crmExpenditureIdCrmOrderMap.get(crmExpenditureId);
                            CrmProjectItemPackagePo crmProjectItemPackagePo = crmExpenditureIdCrmProjectMap.get(crmExpenditureId);
                            CrmContractPo crmContractPo = crmExpenditureIdCrmContractMap.get(crmExpenditureId);
                            OrderInfoForCrmSynDto orderInfoForCrmSynDto = crmExpenditureIdPickUpOrderMap.get(crmExpenditureId);

                            CrmExpenditureDataMessage crmExpenditureDataMessage = new CrmExpenditureDataMessage();
                            crmExpenditureDataMessage.setBtpDetailCode(btpDetailCode);
                            crmExpenditureDataMessage.setBtpGoodsName(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getBtpPurchaseDetails()
                                    .stream()
                                    .filter(btpPurchaseDetailDto -> btpDetailCode.equals(btpPurchaseDetailDto.getCode()))
                                    .findFirst()
                                    .orElse(new BTPPurchaseDetailDto())
                                    .getGoodsName());
                            crmExpenditureDataMessage.setBtpBatchCode(btpBatchCode);
                            crmExpenditureDataMessage.setBtpBatchStatus(btpPurchaseBatchDto == null ? null : (btpPurchaseBatchDto.getStatus() == null ? null :
                                    Objects.requireNonNull(BTPPurchaseBatchDto.Status.getByCode(btpPurchaseBatchDto.getStatus())).getDescription()));

                            crmExpenditureDataMessage.setBtpApprovedTime(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getApprovedTime());
                            crmExpenditureDataMessage.setBtpCreator(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getCreator());
                            crmExpenditureDataMessage.setBtpProjectCode(btpProjectCode);
                            crmExpenditureDataMessage.setBtpProjectName(Optional.ofNullable(crmExpenditureIdBTPProjectMap.get(crmExpenditureId)).orElse(new BTPProjectDto()).getProjectName());
                            crmExpenditureDataMessage.setCrmExpenditureId(crmExpenditureId.toString());
                            crmExpenditureDataMessage.setCrmOrderId(crmOrderId.toString());
                            crmExpenditureDataMessage.setCrmOrderName(crmOrderPo == null ? null : crmOrderPo.getExplanation());
                            crmExpenditureDataMessage.setCrmOrderType(crmOrderPo == null ? null : (crmOrderPo.getType() == null ? null : CrmOrderType.getByCode(crmOrderPo.getType()).getDesc()));
                            crmExpenditureDataMessage.setCrmProjectId(crmProjectItemPackagePo == null ? null : (crmProjectItemPackagePo.getId() == null ? null : crmProjectItemPackagePo.getId().toString()));
                            crmExpenditureDataMessage.setCrmProjectName(crmProjectItemPackagePo == null ? null : crmProjectItemPackagePo.getName());
                            crmExpenditureDataMessage.setCrmContractNumber(crmContractPo == null ? null : (crmContractPo.getContractNumber() == null ? null : crmContractPo.getContractNumber().toString()));
                            crmExpenditureDataMessage.setCrmContractName(crmContractPo == null ? null : crmContractPo.getName());

                            crmExpenditureDataMessage.setSupplierCode(null);
                            crmExpenditureDataMessage.setSupplierName(null);
                            crmExpenditureDataMessage.setEstimatedTime(crmExpenditurePo.getCtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                            crmExpenditureDataMessage.setAccruedTime(null);
                            crmExpenditureDataMessage.setWrittenOffTime(null);
                            crmExpenditureDataMessage.setEstimatedCost(crmExpenditurePo.getPrice());
                            crmExpenditureDataMessage.setAccruedCost(null);
                            crmExpenditureDataMessage.setWrittenOffCost(null);

                            crmExpenditureDataMessage.setExpenditureSourceName(ExpenditureSourceType.getByCode(crmExpenditurePo.getSource()).getDescription());
                            crmExpenditureDataMessage.setExpenditureTypeDescription(
                                    crmExpenditureTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureTypeIdNameMap.get(crmExpenditurePo.getSecondType())
                            );
                            crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.getByCode(crmExpenditurePo.getExpenditureStatus()).getDescription());
                            crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.getByCode(crmExpenditurePo.getAuditStatus()).getDescription());

                            if (pickUpOrderNo != 0L) {
                                crmExpenditureDataMessage.setPickUpOrderNo(pickUpOrderNo);
                                crmExpenditureDataMessage.setMid(orderInfoForCrmSynDto == null ? null : orderInfoForCrmSynDto.getLongUpperMid());
                                crmExpenditureDataMessage.setUpNickName(getNickName(crmExpenditureDataMessage.getMid()));
                                crmExpenditureDataMessage.setPickUpOrderEstimatedCost(orderInfoForCrmSynDto == null ? null : Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderEstimatedTime(crmExpenditurePo.getCtime().toLocalDateTime().format(DATE_TIME_FORMATTER));

                                if (orderInfoForCrmSynDto != null) {
                                    if (PICKUP_ORDER_COMPLETED_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderAccruedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderWrittenOffCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderAccruedTime(orderInfoForCrmSynDto.getStatusMtime() == null ? null : orderInfoForCrmSynDto.getStatusMtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                                        crmExpenditureDataMessage.setPickUpOrderWrittenOffTime(orderInfoForCrmSynDto.getStatusMtime() == null ? null : orderInfoForCrmSynDto.getStatusMtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                                        crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.EXECUTION_COMPLETED.getDescription());
                                        crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getDescription());
                                    } else if (PICKUP_ORDER_ABORT_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderAccruedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderWrittenOffCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.ABORT.getDescription());
                                        crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.ABORT.getDescription());
                                    } else {
                                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.EXECUTING.getDescription());
                                        crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getDescription());
                                    }
                                }
                            }

                            return crmExpenditureDataMessage;
                        }
                ).collect(Collectors.toList());

        results.addAll(hasNotBtpCashMessages);

        List<CrmExpenditureDataMessage> hasBtpCashMessages = crmBtpCashAmountPoList.stream().map(
                crmBtpCashAmountPo -> {
                    Integer crmExpenditureId = crmBtpCashAmountPo.getExpenditureId().intValue();
                    CrmExpenditurePo crmExpenditurePo = crmExpenditureMap.get(crmExpenditureId);

                    Integer crmOrderId = crmExpenditurePo.getCrmOrderId();
                    Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                    String btpProjectCode = crmExpenditurePo.getBtpProjectCode();
                    String btpBatchCode = crmExpenditurePo.getBtpBatchCode();
                    String btpDetailCode = crmExpenditurePo.getBtpDetailCode();

                    BTPPurchaseBatchDto btpPurchaseBatchDto = crmExpenditureIdBTPBatchMap.get(crmExpenditureId);
                    CrmOrderPo crmOrderPo = crmExpenditureIdCrmOrderMap.get(crmExpenditureId);
                    CrmProjectItemPackagePo crmProjectItemPackagePo = crmExpenditureIdCrmProjectMap.get(crmExpenditureId);
                    CrmContractPo crmContractPo = crmExpenditureIdCrmContractMap.get(crmExpenditureId);
                    OrderInfoForCrmSynDto orderInfoForCrmSynDto = crmExpenditureIdPickUpOrderMap.get(crmExpenditureId);

                    CrmExpenditureDataMessage crmExpenditureDataMessage = new CrmExpenditureDataMessage();
                    crmExpenditureDataMessage.setBtpDetailCode(btpDetailCode);
                    crmExpenditureDataMessage.setBtpGoodsName(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getBtpPurchaseDetails()
                            .stream()
                            .filter(btpPurchaseDetailDto -> btpDetailCode.equals(btpPurchaseDetailDto.getCode()))
                            .findFirst()
                            .orElse(new BTPPurchaseDetailDto())
                            .getGoodsName());
                    crmExpenditureDataMessage.setBtpBatchCode(btpBatchCode);
                    crmExpenditureDataMessage.setBtpBatchStatus(btpPurchaseBatchDto == null ? null : (btpPurchaseBatchDto.getStatus() == null ? null :
                            Objects.requireNonNull(BTPPurchaseBatchDto.Status.getByCode(btpPurchaseBatchDto.getStatus())).getDescription()));

                    crmExpenditureDataMessage.setBtpApprovedTime(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getApprovedTime());
                    crmExpenditureDataMessage.setBtpCreator(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getCreator());
                    crmExpenditureDataMessage.setBtpProjectCode(btpProjectCode);
                    crmExpenditureDataMessage.setBtpProjectName(Optional.ofNullable(crmExpenditureIdBTPProjectMap.get(crmExpenditureId)).orElse(new BTPProjectDto()).getProjectName());
                    crmExpenditureDataMessage.setCrmExpenditureId(crmExpenditureId.toString());
                    crmExpenditureDataMessage.setCrmOrderId(crmOrderId.toString());
                    crmExpenditureDataMessage.setCrmOrderName(crmOrderPo == null ? null : crmOrderPo.getExplanation());
                    crmExpenditureDataMessage.setCrmOrderType(crmOrderPo == null ? null : (crmOrderPo.getType() == null ? null : CrmOrderType.getByCode(crmOrderPo.getType()).getDesc()));
                    crmExpenditureDataMessage.setCrmProjectId(crmProjectItemPackagePo == null ? null : (crmProjectItemPackagePo.getId() == null ? null : crmProjectItemPackagePo.getId().toString()));
                    crmExpenditureDataMessage.setCrmProjectName(crmProjectItemPackagePo == null ? null : crmProjectItemPackagePo.getName());
                    crmExpenditureDataMessage.setCrmContractNumber(crmContractPo == null ? null : (crmContractPo.getContractNumber() == null ? null : crmContractPo.getContractNumber().toString()));
                    crmExpenditureDataMessage.setCrmContractName(crmContractPo == null ? null : crmContractPo.getName());

                    crmExpenditureDataMessage.setSupplierCode(crmBtpCashAmountPo.getSupplierCode());
                    crmExpenditureDataMessage.setSupplierName(crmBtpCashAmountPo.getSupplierName());
                    crmExpenditureDataMessage.setEstimatedTime(crmExpenditurePo.getCtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                    crmExpenditureDataMessage.setAccruedTime(crmBtpCashAmountPo.getAccruedTime() == null ? null : crmBtpCashAmountPo.getAccruedTime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                    crmExpenditureDataMessage.setWrittenOffTime(crmBtpCashAmountPo.getWrittenOffTime() == null ? null : crmBtpCashAmountPo.getWrittenOffTime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                    crmExpenditureDataMessage.setEstimatedCost(crmExpenditurePo.getPrice());
                    crmExpenditureDataMessage.setAccruedCost(crmBtpCashAmountPo.getAccruedAmount());
                    crmExpenditureDataMessage.setWrittenOffCost(crmBtpCashAmountPo.getWrittenOffAmount());

                    crmExpenditureDataMessage.setExpenditureSourceName(ExpenditureSourceType.getByCode(crmExpenditurePo.getSource()).getDescription());
                    crmExpenditureDataMessage.setExpenditureTypeDescription(
                            crmExpenditureTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureTypeIdNameMap.get(crmExpenditurePo.getSecondType())
                    );
                    crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.getByCode(crmExpenditurePo.getExpenditureStatus()).getDescription());
                    crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.getByCode(crmExpenditurePo.getAuditStatus()).getDescription());

                    if (pickUpOrderNo != 0L) {
                        crmExpenditureDataMessage.setPickUpOrderNo(pickUpOrderNo);
                        crmExpenditureDataMessage.setMid(orderInfoForCrmSynDto == null ? null : orderInfoForCrmSynDto.getLongUpperMid());
                        crmExpenditureDataMessage.setUpNickName(getNickName(crmExpenditureDataMessage.getMid()));
                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(orderInfoForCrmSynDto == null ? null : Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                        crmExpenditureDataMessage.setPickUpOrderEstimatedTime(crmExpenditurePo.getCtime().toLocalDateTime().format(DATE_TIME_FORMATTER));

                        if (orderInfoForCrmSynDto != null) {
                            if (PICKUP_ORDER_COMPLETED_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                                crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderAccruedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderWrittenOffCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderAccruedTime(orderInfoForCrmSynDto.getStatusMtime() == null ? null : orderInfoForCrmSynDto.getStatusMtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                                crmExpenditureDataMessage.setPickUpOrderWrittenOffTime(orderInfoForCrmSynDto.getStatusMtime() == null ? null : orderInfoForCrmSynDto.getStatusMtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                                crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.EXECUTION_COMPLETED.getDescription());
                                crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getDescription());
                            } else if (PICKUP_ORDER_ABORT_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                                crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderAccruedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderWrittenOffCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.ABORT.getDescription());
                                crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.ABORT.getDescription());
                            } else {
                                crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.EXECUTING.getDescription());
                                crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getDescription());
                            }
                        }
                    }

                    return crmExpenditureDataMessage;
                }
        ).collect(Collectors.toList());

        results.addAll(hasBtpCashMessages);

        List<CrmExpenditureDataMessage> notBtpMessages = crmExpenditurePoList.stream()
                .filter(crmExpenditurePo -> crmExpenditurePo.getSource() != ExpenditureSourceType.BTP.getCode())
                .map(crmExpenditurePo -> {
                            Integer crmExpenditureId = crmExpenditurePo.getId();

                            Integer crmOrderId = crmExpenditurePo.getCrmOrderId();
                            Long pickUpOrderNo = crmExpenditurePo.getPickUpOrderNo();
                            String btpProjectCode = crmExpenditurePo.getBtpProjectCode();
                            String btpBatchCode = crmExpenditurePo.getBtpBatchCode();
                            String btpDetailCode = crmExpenditurePo.getBtpDetailCode();

                            BTPPurchaseBatchDto btpPurchaseBatchDto = crmExpenditureIdBTPBatchMap.get(crmExpenditureId);
                            CrmOrderPo crmOrderPo = crmExpenditureIdCrmOrderMap.get(crmExpenditureId);
                            CrmProjectItemPackagePo crmProjectItemPackagePo = crmExpenditureIdCrmProjectMap.get(crmExpenditureId);
                            CrmContractPo crmContractPo = crmExpenditureIdCrmContractMap.get(crmExpenditureId);
                            OrderInfoForCrmSynDto orderInfoForCrmSynDto = crmExpenditureIdPickUpOrderMap.get(crmExpenditureId);

                            CrmExpenditureDataMessage crmExpenditureDataMessage = new CrmExpenditureDataMessage();
                            crmExpenditureDataMessage.setBtpDetailCode(btpDetailCode);
                            crmExpenditureDataMessage.setBtpGoodsName(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getBtpPurchaseDetails()
                                    .stream()
                                    .filter(btpPurchaseDetailDto -> btpDetailCode.equals(btpPurchaseDetailDto.getCode()))
                                    .findFirst()
                                    .orElse(new BTPPurchaseDetailDto())
                                    .getGoodsName());
                            crmExpenditureDataMessage.setBtpBatchCode(btpBatchCode);
                            crmExpenditureDataMessage.setBtpBatchStatus(btpPurchaseBatchDto == null ? null : (btpPurchaseBatchDto.getStatus() == null ? null :
                                    Objects.requireNonNull(BTPPurchaseBatchDto.Status.getByCode(btpPurchaseBatchDto.getStatus())).getDescription()));

                            crmExpenditureDataMessage.setBtpApprovedTime(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getApprovedTime());
                            crmExpenditureDataMessage.setBtpCreator(btpPurchaseBatchDto == null ? null : btpPurchaseBatchDto.getCreator());
                            crmExpenditureDataMessage.setBtpProjectCode(btpProjectCode);
                            crmExpenditureDataMessage.setBtpProjectName(Optional.ofNullable(crmExpenditureIdBTPProjectMap.get(crmExpenditureId)).orElse(new BTPProjectDto()).getProjectName());
                            crmExpenditureDataMessage.setBtpCategoryName(crmExpenditurePo.getBtpCategoryName());
                            crmExpenditureDataMessage.setBtpBudgetPurposeName(crmExpenditurePo.getBtpBudgetPurposeName());

                            crmExpenditureDataMessage.setCrmExpenditureId(crmExpenditureId.toString());
                            crmExpenditureDataMessage.setCrmOrderId(crmOrderId.toString());
                            crmExpenditureDataMessage.setCrmOrderName(crmOrderPo == null ? null : crmOrderPo.getExplanation());
                            crmExpenditureDataMessage.setCrmOrderType(crmOrderPo == null ? null : (crmOrderPo.getType() == null ? null : CrmOrderType.getByCode(crmOrderPo.getType()).getDesc()));
                            crmExpenditureDataMessage.setCrmProjectId(crmProjectItemPackagePo == null ? null : (crmProjectItemPackagePo.getId() == null ? null : crmProjectItemPackagePo.getId().toString()));
                            crmExpenditureDataMessage.setCrmProjectName(crmProjectItemPackagePo == null ? null : crmProjectItemPackagePo.getName());
                            crmExpenditureDataMessage.setCrmContractNumber(crmContractPo == null ? null : (crmContractPo.getContractNumber() == null ? null : crmContractPo.getContractNumber().toString()));
                            crmExpenditureDataMessage.setCrmContractName(crmContractPo == null ? null : crmContractPo.getName());

                            crmExpenditureDataMessage.setEstimatedTime(crmExpenditurePo.getCtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                            crmExpenditureDataMessage.setAccruedTime(crmExpenditurePo.getAccruedTime() == null ? null : crmExpenditurePo.getAccruedTime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                            crmExpenditureDataMessage.setWrittenOffTime(crmExpenditurePo.getWrittenOffTime() == null ? null : crmExpenditurePo.getWrittenOffTime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                            crmExpenditureDataMessage.setEstimatedCost(crmExpenditurePo.getPrice());
                            crmExpenditureDataMessage.setAccruedCost(crmExpenditurePo.getAccruedCost() == 0L ? null : crmExpenditurePo.getAccruedCost());
                            crmExpenditureDataMessage.setWrittenOffCost(crmExpenditurePo.getWrittenOffCost() == 0L ? null : crmExpenditurePo.getWrittenOffCost());

                            crmExpenditureDataMessage.setExpenditureSourceName(ExpenditureSourceType.getByCode(crmExpenditurePo.getSource()).getDescription());
                            crmExpenditureDataMessage.setExpenditureTypeDescription(
                                    crmExpenditureTypeIdNameMap.get(crmExpenditurePo.getType()) + "-" + crmExpenditureTypeIdNameMap.get(crmExpenditurePo.getSecondType())
                            );
                            crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.getByCode(crmExpenditurePo.getExpenditureStatus()).getDescription());
                            crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.getByCode(crmExpenditurePo.getAuditStatus()).getDescription());

                            if (pickUpOrderNo != 0L) {
                                crmExpenditureDataMessage.setPickUpOrderNo(pickUpOrderNo);
                                crmExpenditureDataMessage.setMid(orderInfoForCrmSynDto == null ? null : orderInfoForCrmSynDto.getLongUpperMid());
                                crmExpenditureDataMessage.setUpNickName(getNickName(crmExpenditureDataMessage.getMid()));
                                crmExpenditureDataMessage.setPickUpOrderEstimatedCost(orderInfoForCrmSynDto == null ? null : Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                crmExpenditureDataMessage.setPickUpOrderEstimatedTime(crmExpenditurePo.getCtime().toLocalDateTime().format(DATE_TIME_FORMATTER));

                                if (orderInfoForCrmSynDto != null) {
                                    if (PICKUP_ORDER_COMPLETED_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderAccruedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderWrittenOffCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderAccruedTime(orderInfoForCrmSynDto.getStatusMtime() == null ? null : orderInfoForCrmSynDto.getStatusMtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                                        crmExpenditureDataMessage.setPickUpOrderWrittenOffTime(orderInfoForCrmSynDto.getStatusMtime() == null ? null : orderInfoForCrmSynDto.getStatusMtime().toLocalDateTime().format(DATE_TIME_FORMATTER));
                                        crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.EXECUTION_COMPLETED.getDescription());
                                        crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getDescription());
                                    } else if (PICKUP_ORDER_ABORT_STATUS.contains(orderInfoForCrmSynDto.getOrderStatus())) {
                                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderAccruedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setPickUpOrderWrittenOffCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.ABORT.getDescription());
                                        crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.ABORT.getDescription());
                                    } else {
                                        crmExpenditureDataMessage.setPickUpOrderEstimatedCost(Utils.fromYuanToFen(orderInfoForCrmSynDto.getPlatformTotalExpenses()));
                                        crmExpenditureDataMessage.setExpenditureStatus(ExpenditureStatus.EXECUTING.getDescription());
                                        crmExpenditureDataMessage.setExpenditureAuditStatus(NewExpenditureAuditStatus.NONE_AUDIT.getDescription());
                                    }
                                }
                            }

                            return crmExpenditureDataMessage;
                        }
                ).collect(Collectors.toList());

        results.addAll(notBtpMessages);

        return results;
    }

    public List<CrmExpenditureDataMessage> getCrmVirtualGoldExpenditureMessages() {
        List<CrmBtpVirtualGoldAmountLogPo> crmBtpVirtualGoldAmountLogPoList = getCrmVirtualGoldExpenditures();
        if (CollectionUtils.isEmpty(crmBtpVirtualGoldAmountLogPoList)) {
            return Collections.emptyList();
        }

        List<String> btpProjectCodes = crmBtpVirtualGoldAmountLogPoList.stream()
                .map(CrmBtpVirtualGoldAmountLogPo::getBtpProjectCode)
                .filter(btpProjectCode -> !StringUtils.isEmpty(btpProjectCode))
                .distinct()
                .collect(Collectors.toList());

        List<BTPProjectDto> btpProjectDtoList = ibtpIntegrationService.getAllBTPProjectsByCodes(btpProjectCodes);

        if (CollectionUtils.isEmpty(btpProjectDtoList)) {
            return Collections.emptyList();
        }

        Map<String, BTPProjectDto> btpProjectMap = btpProjectDtoList.stream()
                .collect(Collectors.toMap(
                        BTPProjectDto::getProjectCode,
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        Map<String, CrmProjectItemPackagePo> mappedBtpProjectCrmProjectMap = getBTPProjectCodeCrmProjectMap(btpProjectDtoList);
        Set<String> mappedBtpProjectCodes = mappedBtpProjectCrmProjectMap.keySet();

        List<BTPProjectDto> noMappedBtpProjects = btpProjectDtoList.stream().filter(btpProjectDto -> !mappedBtpProjectCodes.contains(btpProjectDto.getProjectCode()))
                .collect(Collectors.toList());


        List<CrmExpenditureDataMessage> results = btpProjectDtoList.stream()
                .filter(btpProjectDto -> btpProjectMap.get(btpProjectDto.getProjectCode()) != null && mappedBtpProjectCrmProjectMap.get(btpProjectDto.getProjectCode()) != null)
                .map(btpProjectDto -> {
                    String btpProjectCode = btpProjectDto.getProjectCode();
                    CrmExpenditureDataMessage crmExpenditureDataMessage = new CrmExpenditureDataMessage();
                    crmExpenditureDataMessage.setBtpProjectCode(btpProjectCode);
                    crmExpenditureDataMessage.setBtpProjectName(btpProjectDto.getProjectName());
                    crmExpenditureDataMessage.setCrmProjectId(mappedBtpProjectCrmProjectMap.get(btpProjectCode).getId().toString());
                    crmExpenditureDataMessage.setCrmProjectName(mappedBtpProjectCrmProjectMap.get(btpProjectCode).getName());
                    return crmExpenditureDataMessage;
                })
                .collect(Collectors.toList());

        results.addAll(noMappedBtpProjects.stream().map(
                btpProjectDto -> {
                    String btpProjectCode = btpProjectDto.getProjectCode();
                    CrmExpenditureDataMessage crmExpenditureDataMessage = new CrmExpenditureDataMessage();
                    crmExpenditureDataMessage.setBtpProjectCode(btpProjectCode);
                    crmExpenditureDataMessage.setBtpProjectName(btpProjectDto.getProjectName());
                    crmExpenditureDataMessage.setExpenditureSourceName("BTP虚拟金回收");
                    return crmExpenditureDataMessage;
                }
        ).collect(Collectors.toList()));

        return results;
    }

    public List<CrmExpenditurePo> getCrmCashExpenditures() {
        CrmExpenditurePoExample crmExpenditurePoExample = new CrmExpenditurePoExample();
        CrmExpenditurePoExample.Criteria criteria = crmExpenditurePoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andSourceNotIn(Arrays.asList(ExpenditureSourceType.LEGACY.getCode(), ExpenditureSourceType.UNKNOWN.getCode()));
        return crmExpenditureDao.selectByExample(crmExpenditurePoExample);
    }

    public List<CrmBtpVirtualGoldAmountLogPo> getCrmVirtualGoldExpenditures() {
        CrmBtpVirtualGoldAmountLogPoExample crmBtpVirtualGoldAmountLogPoExample = new CrmBtpVirtualGoldAmountLogPoExample();
        CrmBtpVirtualGoldAmountLogPoExample.Criteria criteria = crmBtpVirtualGoldAmountLogPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode());
        return crmBtpVirtualGoldAmountLogDao.selectByExample(crmBtpVirtualGoldAmountLogPoExample);
    }

    /**
     * @param crmExpenditurePoList
     * @return key: crm expenditure id
     */
    public Map<Integer, CrmOrderPo> getCrmOrders(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<Integer> crmOrderIds = crmExpenditurePoList.stream().map(CrmExpenditurePo::getCrmOrderId).filter(crmOrderId -> crmOrderId != 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, Integer> crmExpenditureOrderIdMap = crmExpenditurePoList.stream().filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0).collect(Collectors.toMap(
                CrmExpenditurePo::getId,
                CrmExpenditurePo::getCrmOrderId,
                (k1, k2) -> k1
        ));

        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria criteria = crmOrderPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(crmOrderIds);
        Map<Integer, CrmOrderPo> crmOrderMap = crmOrderDao.selectByExample(crmOrderPoExample).stream().collect(Collectors.toMap(
                CrmOrderPo::getId,
                Function.identity(),
                (k1, k2) -> k1
        ));

        return crmExpenditureOrderIdMap.entrySet().stream()
                .filter(entry -> crmOrderMap.get(entry.getValue()) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> crmOrderMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));
    }

    /**
     * @param crmExpenditurePoList
     * @return key: crm expenditure id
     */
    public Map<Integer, CrmOrderExtraPo> getCrmOrderExtras(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<Integer> crmOrderIds = crmExpenditurePoList.stream().map(CrmExpenditurePo::getCrmOrderId).filter(crmOrderId -> crmOrderId != 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, Integer> crmExpenditureOrderIdMap = crmExpenditurePoList.stream().filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0).collect(Collectors.toMap(
                CrmExpenditurePo::getId,
                CrmExpenditurePo::getCrmOrderId,
                (k1, k2) -> k1
        ));

        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria criteria = crmOrderExtraPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCrmOrderIdIn(crmOrderIds);
        Map<Integer, CrmOrderExtraPo> crmOrderExtraMap = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream()
                .collect(Collectors.toMap(
                        CrmOrderExtraPo::getCrmOrderId,
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        return crmExpenditureOrderIdMap.entrySet().stream()
                .filter(entry -> crmOrderExtraMap.get(entry.getValue()) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> crmOrderExtraMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));
    }

    /**
     * @param crmExpenditurePoList
     * @return key: crm expenditure id
     */
    public Map<Integer, CrmProjectItemPackagePo> getCrmProjects(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<Integer> crmOrderIds = crmExpenditurePoList.stream().map(CrmExpenditurePo::getCrmOrderId).filter(crmOrderId -> crmOrderId != 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, Integer> crmExpenditureOrderIdMap = crmExpenditurePoList.stream().filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0).collect(Collectors.toMap(
                CrmExpenditurePo::getId,
                CrmExpenditurePo::getCrmOrderId,
                (k1, k2) -> k1
        ));

        CrmOrderExtraPoExample crmOrderExtraPoExample = new CrmOrderExtraPoExample();
        CrmOrderExtraPoExample.Criteria criteria = crmOrderExtraPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andCrmOrderIdIn(crmOrderIds);

        Map<Integer, Integer> crmOrderProjectIdMap = crmOrderExtraDao.selectByExample(crmOrderExtraPoExample).stream()
                .collect(Collectors.toMap(
                        CrmOrderExtraPo::getCrmOrderId,
                        CrmOrderExtraPo::getProjectItemId,
                        (k1, k2) -> k1
                ));

        List<Integer> crmProjectIds = crmOrderProjectIdMap.values().stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmProjectIds)) {
            return Collections.emptyMap();
        }

        CrmProjectItemPackagePoExample crmProjectItemPackagePoExample = new CrmProjectItemPackagePoExample();
        CrmProjectItemPackagePoExample.Criteria crmProjectItemPackagePoExampleCriteria = crmProjectItemPackagePoExample.createCriteria();
        crmProjectItemPackagePoExampleCriteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(crmProjectIds);
        Map<Integer, CrmProjectItemPackagePo> crmProjectMap = crmProjectItemPackageDao.selectByExample(crmProjectItemPackagePoExample).stream()
                .collect(Collectors.toMap(
                        CrmProjectItemPackagePo::getId,
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        return crmExpenditureOrderIdMap.entrySet().stream()
                .filter(entry -> crmOrderProjectIdMap.get(entry.getValue()) != null)
                .filter(entry -> crmProjectMap.get(crmOrderProjectIdMap.get(entry.getValue())) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> crmProjectMap.get(crmOrderProjectIdMap.get(entry.getValue())),
                        (k1, k2) -> k1
                ));
    }

    /**
     * @param crmExpenditurePoList
     * @return key: crm expenditure id
     */
    public Map<Integer, CrmContractPo> getCrmContracts(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<Integer> crmOrderIds = crmExpenditurePoList.stream().map(CrmExpenditurePo::getCrmOrderId).filter(crmOrderId -> crmOrderId != 0).distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return Collections.emptyMap();
        }

        Map<Integer, Integer> crmExpenditureOrderIdMap = crmExpenditurePoList.stream().filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0).collect(Collectors.toMap(
                CrmExpenditurePo::getId,
                CrmExpenditurePo::getCrmOrderId,
                (k1, k2) -> k1
        ));

        CrmOrderPoExample crmOrderPoExample = new CrmOrderPoExample();
        CrmOrderPoExample.Criteria criteria = crmOrderPoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(crmOrderIds);

        Map<Integer, Integer> crmOrderContractIdMap = crmOrderDao.selectByExample(crmOrderPoExample).stream().collect(Collectors.toMap(
                CrmOrderPo::getId,
                CrmOrderPo::getCrmContractId,
                (k1, k2) -> k1
        ));

        List<Integer> crmContractIds = crmOrderContractIdMap.values().stream().distinct().collect(Collectors.toList());
        if (CollectionUtils.isEmpty(crmContractIds)) {
            return Collections.emptyMap();
        }

        CrmContractPoExample crmContractPoExample = new CrmContractPoExample();
        CrmContractPoExample.Criteria crmContractPoExampleCriteria = crmContractPoExample.createCriteria();
        crmContractPoExampleCriteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andIdIn(crmContractIds);
        Map<Integer, CrmContractPo> crmContractMap = crmContractDao.selectByExample(crmContractPoExample).stream()
                .collect(Collectors.toMap(
                        CrmContractPo::getId,
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        return crmExpenditureOrderIdMap.entrySet().stream()
                .filter(entry -> crmOrderContractIdMap.get(entry.getValue()) != null)
                .filter(entry -> crmContractMap.get(crmOrderContractIdMap.get(entry.getValue())) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> crmContractMap.get(crmOrderContractIdMap.get(entry.getValue())),
                        (k1, k2) -> k1
                ));
    }

    /**
     * @param crmExpenditurePoList
     * @return key: crm expenditure id
     */
    public Map<Integer, BTPProjectDto> getCrmExpenditureIdBTPProjectMap(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<String> btpProjectCodes = crmExpenditurePoList.stream()
                .map(CrmExpenditurePo::getBtpProjectCode)
                .filter(btpProjectCode -> !StringUtils.isEmpty(btpProjectCode))
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(btpProjectCodes)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> crmExpenditureIdBTPProjectCodeMap = crmExpenditurePoList.stream()
                .filter(crmExpenditurePo -> !StringUtils.isEmpty(crmExpenditurePo.getBtpProjectCode()))
                .collect(Collectors.toMap(
                        CrmExpenditurePo::getId,
                        CrmExpenditurePo::getBtpProjectCode,
                        (k1, k2) -> k1
                ));

        List<BTPProjectDto> btpProjectDtoList = ibtpIntegrationService.getAllBTPProjectsByCodes(btpProjectCodes);
        Map<String, BTPProjectDto> btpProjectMap = btpProjectDtoList.stream().collect(Collectors.toMap(
                BTPProjectDto::getProjectCode,
                Function.identity(),
                (k1, k2) -> k1
        ));

        return crmExpenditureIdBTPProjectCodeMap.entrySet().stream()
                .filter(entry -> btpProjectMap.get(entry.getValue()) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> btpProjectMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));
    }

    /**
     * @param btpProjectDtoList
     * @return btp project code
     */
    public Map<String, CrmProjectItemPackagePo> getBTPProjectCodeCrmProjectMap(List<BTPProjectDto> btpProjectDtoList) {
        List<String> btpProjectCodes = btpProjectDtoList.stream()
                .map(BTPProjectDto::getProjectCode)
                .filter(btpProjectCode -> !StringUtils.isEmpty(btpProjectCode))
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(btpProjectCodes)) {
            return Collections.emptyMap();
        }

        final Map<String, BTPProjectDto> btpProjectMap = btpProjectDtoList.stream()
                .filter(btpProjectDto -> !StringUtils.isEmpty(btpProjectDto.getProjectCode()))
                .collect(Collectors.toMap(
                        BTPProjectDto::getProjectCode,
                        Function.identity(),
                        (k1, k2) -> k1
                ));

        CrmProjectItemPackagePoExample crmProjectItemPackagePoExample = new CrmProjectItemPackagePoExample();
        CrmProjectItemPackagePoExample.Criteria criteria = crmProjectItemPackagePoExample.createCriteria();
        criteria.andIsDeletedEqualTo(IsDeleted.VALID.getCode()).andBtpProjectCodeIn(btpProjectCodes);

        return crmProjectItemPackageDao.selectByExample(crmProjectItemPackagePoExample).stream()
                .filter(crmProjectItemPackagePo -> btpProjectMap.get(crmProjectItemPackagePo.getBtpProjectCode()) != null)
                .collect(Collectors.toMap(
                        CrmProjectItemPackagePo::getBtpProjectCode,
                        Function.identity(),
                        (k1, k2) -> k1
                ));
    }

    /**
     * @param crmExpenditurePoList
     * @return key: crm expenditure id
     */
    public Map<Integer, BTPPurchaseBatchDto> getCrmExpenditureIdBTPBatchMap(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<String> btpBatchCodes = crmExpenditurePoList.stream()
                .map(CrmExpenditurePo::getBtpBatchCode)
                .filter(btpBatchCode -> !StringUtils.isEmpty(btpBatchCode))
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(btpBatchCodes)) {
            return Collections.emptyMap();
        }

        Map<Integer, String> crmExpenditureBtpBatchCodeMap = crmExpenditurePoList.stream()
                .filter(crmExpenditurePo -> !StringUtils.isEmpty(crmExpenditurePo.getBtpBatchCode()))
                .collect(Collectors.toMap(
                        CrmExpenditurePo::getId,
                        CrmExpenditurePo::getBtpBatchCode,
                        (k1, k2) -> k1
                ));

        List<BTPPurchaseBatchDto> btpPurchaseBatchDtoList = ibtpIntegrationService.getBTPPurchaseBatchByCodes(btpBatchCodes);
        Map<String, BTPPurchaseBatchDto> btpBatchMap = btpPurchaseBatchDtoList.stream().collect(Collectors.toMap(
                BTPPurchaseBatchDto::getCode,
                Function.identity(),
                (k1, k2) -> k1
        ));

        return crmExpenditureBtpBatchCodeMap.entrySet().stream()
                .filter(entry -> btpBatchMap.get(entry.getValue()) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> btpBatchMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));
    }

    /**
     * 获取crm成本相关的花火订单的信息
     *
     * @return key: crm expenditure id
     */
    public Map<Integer, OrderInfoForCrmSynDto> getCrmExpenditureIdPickUpOrderMap(List<CrmExpenditurePo> crmExpenditurePoList) {
        List<Long> pickUpOrderNos = crmExpenditurePoList.stream()
                .map(CrmExpenditurePo::getPickUpOrderNo)
                .filter(pickUpOrderNo -> pickUpOrderNo != 0L)
                .distinct().collect(Collectors.toList());

        final Map<Long, OrderInfoForCrmSynDto> pickUpOrderMap = new ConcurrentHashMap<>();

        CollectionWithDefaultPoolHelper.callInBatchesAsync(pickUpOrderNos, 100, (eachPickUpOrderNos) -> {
            List<OrderInfoForCrmSynDto> pickUpOrders = iSoaCmOrderService.queryOrderInfosForCrm(QueryOrderInfoForCrmReqDto.builder().orderNos(eachPickUpOrderNos).build());
            pickUpOrderMap.putAll(pickUpOrders.stream().collect(Collectors.toMap(
                    OrderInfoForCrmSynDto::getOrderNo,
                    Function.identity(),
                    (k1, k2) -> k1
            )));

            return Collections.EMPTY_LIST;
        });

        Map<Integer, Long> crmExpenditureIdPickUpOrderNoMap = crmExpenditurePoList.stream().filter(crmExpenditurePo -> crmExpenditurePo.getPickUpOrderNo() != 0L).collect(Collectors.toMap(
                CrmExpenditurePo::getId,
                CrmExpenditurePo::getPickUpOrderNo,
                (k1, k2) -> k1
        ));

        return crmExpenditureIdPickUpOrderNoMap.entrySet().stream()
                .filter(entry -> pickUpOrderMap.get(entry.getValue()) != null)
                .collect(Collectors.toMap(
                        Map.Entry::getKey,
                        entry -> pickUpOrderMap.get(entry.getValue()),
                        (k1, k2) -> k1
                ));
    }

    public Map<Integer, String> getCrmExpenditureTypeIdNameMap(List<CrmExpenditurePo> crmExpenditurePoList) {
        Set<Integer> typeIds = new HashSet<>();
        List<Integer> firstTypeIds = crmExpenditurePoList.stream().map(CrmExpenditurePo::getType).distinct().collect(Collectors.toList());
        List<Integer> secondTypeIds = crmExpenditurePoList.stream().map(CrmExpenditurePo::getSecondType).distinct().collect(Collectors.toList());

        typeIds.addAll(firstTypeIds);
        typeIds.addAll(secondTypeIds);

        CrmExpenditureTypePoExample crmExpenditureTypePoExample = new CrmExpenditureTypePoExample();
        CrmExpenditureTypePoExample.Criteria criteria = crmExpenditureTypePoExample.createCriteria();
        criteria.andIdIn(new ArrayList<>(typeIds));
        List<CrmExpenditureTypePo> crmExpenditureTypePoList = crmExpenditureTypeDao.selectByExample(crmExpenditureTypePoExample);

        return crmExpenditureTypePoList.stream().collect(Collectors.toMap(
                CrmExpenditureTypePo::getId,
                CrmExpenditureTypePo::getName,
                (k1, k2) -> k1
        ));
    }

    public List<CrmBtpCashAmountPo> getCrmBtpCashAmountLogs() {
        CrmBtpCashAmountPoExample crmBtpCashAmountPoExample = new CrmBtpCashAmountPoExample();
        crmBtpCashAmountPoExample.createCriteria().andIsDeletedEqualTo(IsDeleted.VALID.getCode())
                .andExpenditureIdNotEqualTo(0L);
        return crmBtpCashAmountDao.selectByExample(crmBtpCashAmountPoExample);
    }

    public void refreshCrmExpenditureWithPickUpOrderNo() {
        List<CrmExpenditurePo> crmExpenditurePoList = getCrmCashExpenditures();

        List<CrmExpenditurePo> pickUpCrmExpenditurePoList = crmExpenditurePoList.stream()
                .filter(crmExpenditurePo -> crmExpenditurePo.getSource() == ExpenditureSourceType.PICKUP_PLATFORM.getCode())
                .filter(crmExpenditurePo -> crmExpenditurePo.getCrmOrderId() != 0)
                .collect(Collectors.toList());

        List<Integer> crmOrderIds = pickUpCrmExpenditurePoList.stream()
                .map(CrmExpenditurePo::getCrmOrderId)
                .distinct()
                .collect(Collectors.toList());

        if (CollectionUtils.isEmpty(crmOrderIds)) {
            return;
        }

        CrmPickupSettleNewPoExample crmPickupSettleNewPoExample = new CrmPickupSettleNewPoExample();
        crmPickupSettleNewPoExample.createCriteria().andCrmOrderIdIn(crmOrderIds);
        List<CrmPickupSettleNewPo> crmPickupSettleNewPoList = crmPickupSettleNewDao.selectByExample(crmPickupSettleNewPoExample);

        Map<Integer, Long> crmOrderIdPickupOrderNoMap = crmPickupSettleNewPoList.stream().collect(Collectors.toMap(
                CrmPickupSettleNewPo::getCrmOrderId,
                CrmPickupSettleNewPo::getPickupOrderNo,
                (k1, k2) -> k1
        ));

        List<CrmExpenditurePo> toBeUpdatedCrmExpenditurePoList = pickUpCrmExpenditurePoList.stream().map(
                crmExpenditurePo -> {
                    CrmExpenditurePo updatedCrmExpenditurePo = new CrmExpenditurePo();
                    updatedCrmExpenditurePo.setId(crmExpenditurePo.getId());
                    updatedCrmExpenditurePo.setPickUpOrderNo(crmOrderIdPickupOrderNoMap.get(crmExpenditurePo.getCrmOrderId()));
                    return updatedCrmExpenditurePo;
                }
        ).collect(Collectors.toList());

        for (CrmExpenditurePo toBeUpdatedCrmExpenditurePo : toBeUpdatedCrmExpenditurePoList) {
            try {
                crmExpenditureDao.updateByPrimaryKeySelective(toBeUpdatedCrmExpenditurePo);
                log.info("succeed to refresh crm expenditure with pick up order no of: {}", toBeUpdatedCrmExpenditurePo);
                Thread.sleep(200L);
            } catch (Exception e) {
                log.error("failed to refresh crm expenditure with pick up order no of: {}", toBeUpdatedCrmExpenditurePo, e);
            }
        }
    }

    public String getNickName(Long mid) {
        if (mid == null) {
            return null;
        }

        BiliUserBaseInfoDto biliUserBaseInfoDto = bilibiliUserQuerier.getBiliUserBaseInfoByMid(mid);
        if (biliUserBaseInfoDto == null) {
            return null;
        }

        return biliUserBaseInfoDto.getNickName();
    }
}
