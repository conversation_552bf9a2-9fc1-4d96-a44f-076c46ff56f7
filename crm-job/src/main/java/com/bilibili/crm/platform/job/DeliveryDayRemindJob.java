package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.service.settle_account.remind.DeliveryDelayRemindService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2019/8/12
 * 按天触发计收逾期提醒邮件
 **/
@Component
@JobHandler("DeliveryDayRemindJob")
@Slf4j
public class DeliveryDayRemindJob extends IJobHandler {

    @Autowired
    private DeliveryDelayRemindService deliveryDelayRemindService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> deliveryDelayRemindService.dayOverdueRemind());
        return ReturnT.SUCCESS;
    }
}
