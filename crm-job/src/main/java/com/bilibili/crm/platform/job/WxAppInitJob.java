package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.app.cache.WxAppCacheWriter;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2020/4/21
 * 企业微信日报缓存初始化job
 **/
@Component
@JobHandler("WxAppInitJob")
@Slf4j
public class WxAppInitJob extends IJobHandler {

    @Autowired
    private WxAppCacheWriter wxAppCacheWriter;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) ->    wxAppCacheWriter.initCalculateData(30));
        return ReturnT.SUCCESS;
    }
}
