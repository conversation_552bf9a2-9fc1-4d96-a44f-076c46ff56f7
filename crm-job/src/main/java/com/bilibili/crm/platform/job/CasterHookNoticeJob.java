package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.biz.service.caster.CasterHookService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 * <AUTHOR>
 * @date 2021/4/27
 * caster发布回调消息通知
 */
@Component
@JobHandler("CasterHookNoticeJob")
@Slf4j
public class CasterHookNoticeJob extends I<PERSON>obHandler {

    @Autowired
    private CasterHookService casterHookService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        casterHookService.sendMessage();
        return ReturnT.SUCCESS;
    }
}
