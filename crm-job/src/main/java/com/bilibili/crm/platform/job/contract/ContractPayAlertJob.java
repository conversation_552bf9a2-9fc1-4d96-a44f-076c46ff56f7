package com.bilibili.crm.platform.job.contract;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.sales.impl.SaleGroupMappingServiceImpl;
import com.bilibili.crm.platform.api.contract.dto.ContractDto;
import com.bilibili.crm.platform.api.contract.dto.QueryContractParam;
import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.api.finance.enums.YesOrNoEnum;
import com.bilibili.crm.platform.api.sale.dto.SaleDto;
import com.bilibili.crm.platform.api.sale.dto.SimpleSaleDto;
import com.bilibili.crm.platform.api.sale.group.dto.SaleGroupDto;
import com.bilibili.crm.platform.api.sale.group.service.ISaleGroupService;
import com.bilibili.crm.platform.api.statusmachine.contract.enums.ContractBusStatus;
import com.bilibili.crm.platform.biz.common.ContractStatusAlertEnum;
import com.bilibili.crm.platform.biz.service.contract.component.ContractBiiPeriodAlertComponent;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.ContractType;
import com.bilibili.crm.platform.common.IsValid;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

/**
 * <AUTHOR>
 * @Description
 * @date 2022/8/15
 **/
@Deprecated
@Component
@JobHandler("contractPayAlertJob")
@Slf4j
public class ContractPayAlertJob extends IJobHandler {

    @Value("${crm.contract.pay.alert.mjz_email:<EMAIL>}")
    public String MJZ_BILIBILI_COM;
    @Value("${crm.contract.pay.alert.finbp_email:<EMAIL>}")
    public String FINBP_A_BILIBILI_COM;
    @Value("${crm.contract.pay.alert.huikuan_legal_email:<EMAIL>}")
    public String HUIKUAN_LEGAL_BILIBILI_COM;
    public static final String NOT_SEND_USER = "Shelley";
    @Resource
    private IContractQueryService iContractQueryService;
    @Resource
    private ISaleGroupService iSaleGroupService;

    @Resource
    private ContractBiiPeriodAlertComponent contractBiiPeriodAlertComponent;
    @Autowired
    private SaleGroupMappingServiceImpl saleGroupMappingServiceImpl;

    @Override
    public ReturnT<String> execute(String param) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            List<ContractDto> contractDtos = iContractQueryService.queryContractList(QueryContractParam.builder()
                    .busStatus(Lists.newArrayList(ContractBusStatus.EXECUTED.getCode(), ContractBusStatus.RECEIVABLE.getCode()))
                    .contractTypes(Lists.newArrayList(ContractType.OUTER_MARKET.getCode(), ContractType.GENERAL.getCode()))
                    .build());
            contractDtos.forEach(this::handle);
        });
        return ReturnT.SUCCESS;
    }

    /**
     * 处理单个合同的逻辑
     *
     * @param contractDto
     */
    public void handle(ContractDto contractDto) {
        List<SaleDto> saleDtos = contractDto.getSaleDtos();
        if (CollectionUtils.isEmpty(saleDtos)) {
            return;
        }

        if (null == contractDto.getEndTime()) {
            return;
        }

        if (0 == contractDto.getAmount()) {
            // 排除合同打包价为0的合同
            return;
        }

        if (contractDto.getName().contains("内部需求")) {
            // 排除合同名称中有内部需求几个字的合同
            return;
        }

        if (YesOrNoEnum.YES.getCode().equals(contractDto.getIsDeductCompleted())) {
            // 排除合同是否回款完成为是的合同
            return;
        }

        ContractStatusAlertEnum contractStatusAlertEnum = ContractStatusAlertEnum.judge(contractDto);
        if (null == contractStatusAlertEnum) {
            return;
        }

        for (SaleDto saleDto : saleDtos) {

            List<String> wxUserWorkCodeList = new ArrayList<>();
            String wxTitle = "";
            String wxContent = "";

            List<String> mailReceivers = new ArrayList<>();
            String mailSubject = "";
            String mailContent = "";

            List<String> salesGroupPidsEmail = new ArrayList<>();
            if (Integer.valueOf(0).equals(saleDto.getGroupId())) {
                continue;
            }
            List<Integer> salesGroupPidsById = iSaleGroupService.getSalesGroupParentIdsById(saleDto.getGroupId());
            try {
                saleGroupMappingServiceImpl.queryLeaders(salesGroupPidsById)
                    .values().stream().flatMap(List::stream).map(SimpleSaleDto::getEmail)
                        .forEach(salesGroupPidsEmail::add);
            } catch (Exception e) {
                log.error("error ", e);
            }

            switch (contractStatusAlertEnum) {
                case EXECUTED_DONE:
                    wxUserWorkCodeList = Lists.newArrayList(saleDto.getEmail());
                    wxTitle = "【合同执行完成提醒】<br/>";
                    wxContent = String.format("如下合同已执行完成，请尽快发起结算单确认/协议签署以及开票流程，同步催促客户回款，避免影响业绩提成。<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc());

                    mailReceivers = wxUserWorkCodeList.stream().map(r -> r + "@bilibili.com").collect(Collectors.toList());
                    mailSubject = String.format("合同%s（%s）已执行完成", contractDto.getContractNumber(), contractDto.getCustomerName());
                    mailContent = String.format("您有如下合同已执行完成，请尽快发起结算单确认/协议签署以及开票流程，同步催促客户回款，避免影响业绩提成。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s<br/>" +
                                    "查看详情：<a href=\"https://cm-mng.bilibili.co/crm/#/contract/list/show/general/%s\">点击后跳转至对应合同的详情页</a>", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc(), contractDto.getId());
                    break;
                case EXECUTED_THIRTY:
                    wxUserWorkCodeList = Lists.newArrayList(saleDto.getEmail());
                    wxUserWorkCodeList.addAll(saleDto.getLeaderEmails());
                    wxUserWorkCodeList = wxUserWorkCodeList.stream().filter(r -> null != r && !r.equals(NOT_SEND_USER)).collect(Collectors.toList());
                    wxTitle = "【合同完成30天后仍未完成回款提醒】<br/>";
                    wxContent = String.format("如下合同已执行完成30天，仍未完成回款动作，请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免影响业绩提成。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc());

                    mailReceivers = wxUserWorkCodeList.stream().map(r -> r + "@bilibili.com").collect(Collectors.toList());
                    mailSubject = String.format("合同%s（%s）完成30天后仍未完成回款", contractDto.getContractNumber(), contractDto.getCustomerName());
                    mailContent = String.format("您有如下合同已执行完成30天，仍未完成回款动作，请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免影响业绩提成。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s<br/>" +
                                    "查看详情：<a href=\"https://cm-mng.bilibili.co/crm/#/contract/list/show/general/%s\">点击后跳转至对应合同的详情页</a>", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc(), contractDto.getId());
                    break;
                case EXECUTED_SIXTY:
                    wxUserWorkCodeList = Lists.newArrayList(saleDto.getEmail());
                    wxUserWorkCodeList.addAll(salesGroupPidsEmail);
                    wxUserWorkCodeList = wxUserWorkCodeList.stream().filter(r -> null != r && !r.equals(NOT_SEND_USER)).collect(Collectors.toList());
                    wxTitle = "【合同完成60天后仍未完成回款提醒】<br/>";
                    wxContent = String.format("如下合同已执行完成60天，仍未完成回款动作，请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免影响业绩提成。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc());

                    mailReceivers = wxUserWorkCodeList.stream().map(r -> r + "@bilibili.com").collect(Collectors.toList());
                    mailReceivers.add(MJZ_BILIBILI_COM);
                    mailSubject = String.format("合同%s（%s）完成60天后仍未完成回款", contractDto.getContractNumber(), contractDto.getCustomerName());
                    mailContent = String.format("您有如下合同已执行完成60天，仍未完成回款动作，请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免影响业绩提成。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s<br/>" +
                                    "查看详情：<a href=\"https://cm-mng.bilibili.co/crm/#/contract/list/show/general/%s\">点击后跳转至对应合同的详情页</a>", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc(), contractDto.getId());
                    break;
                case EXECUTED_NINETY:
                    wxUserWorkCodeList = Lists.newArrayList(saleDto.getEmail());
                    wxUserWorkCodeList.addAll(salesGroupPidsEmail);
                    wxUserWorkCodeList = wxUserWorkCodeList.stream().filter(r -> null != r && !r.equals(NOT_SEND_USER)).collect(Collectors.toList());
                    wxTitle = "【合同完成90天后仍未完成回款提醒】<br/>";
                    wxContent = String.format("如下合同已执行完成90天，仍未完成回款动作，即将超过账期。<br/>" +
                                    "请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免影响业绩提成。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc());

                    mailReceivers = wxUserWorkCodeList.stream().map(r -> r + "@bilibili.com").collect(Collectors.toList());
                    mailReceivers.addAll(Lists.newArrayList(MJZ_BILIBILI_COM, FINBP_A_BILIBILI_COM));
                    mailSubject = String.format("【重要】合同%s（%s）完成90天后仍未完成回款", contractDto.getContractNumber(), contractDto.getCustomerName());
                    mailContent = String.format("您有如下合同已执行完成90天，仍未完成回款动作，即将超过账期。<br/>" +
                                    "请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免影响业绩提成。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s<br/>" +
                                    "查看详情：<a href=\"https://cm-mng.bilibili.co/crm/#/contract/list/show/general/%s\">点击后跳转至对应合同的详情页</a>", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc(), contractDto.getId());
                    break;
                case EXECUTED_ONE_HUNDRED_AND_EIGHTY:
                    wxUserWorkCodeList = Lists.newArrayList(saleDto.getEmail());
                    wxUserWorkCodeList.addAll(salesGroupPidsEmail);
                    wxUserWorkCodeList = wxUserWorkCodeList.stream().filter(r -> null != r && !r.equals(NOT_SEND_USER)).collect(Collectors.toList());
                    wxTitle = "【合同回款严重超时提醒】<br/>";
                    wxContent = String.format("如下合同已执行完成180天，仍未完成回款动作，已严重超过账期。<br/>" +
                                    "请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免引起相关诉讼，影响提成获取以及客情关系。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc());

                    mailReceivers = wxUserWorkCodeList.stream().map(r -> r + "@bilibili.com").collect(Collectors.toList());
                    mailReceivers.addAll(Lists.newArrayList(MJZ_BILIBILI_COM, FINBP_A_BILIBILI_COM));
                    mailSubject = String.format("【重要】合同%s（%s）回款已严重超时", contractDto.getContractNumber(), contractDto.getCustomerName());
                    mailContent = String.format("您有如下合同已执行完成180天，仍未完成回款动作，已严重超过账期。<br/>" +
                                    "请尽快发起结算单确认/协议签署以及开票流程等，同步催促客户回款，避免引起相关诉讼，影响提成获取以及客情关系。如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s<br/>" +
                                    "查看详情：<a href=\"https://cm-mng.bilibili.co/crm/#/contract/list/show/general/%s\">点击后跳转至对应合同的详情页</a>", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc(), contractDto.getId());
                    break;
                case EXECUTED_TWO_HUNDRED_AND_TEN:
                    wxUserWorkCodeList = Lists.newArrayList(saleDto.getEmail());
                    wxUserWorkCodeList.addAll(salesGroupPidsEmail);
                    wxUserWorkCodeList = wxUserWorkCodeList.stream().filter(r -> null != r && !r.equals(NOT_SEND_USER)).collect(Collectors.toList());
                    wxTitle = "【合同回款超时报警】<br/>";
                    wxContent = String.format("如下合同已执行完成210天，仍未完成回款动作，已严重超过账期。<br/>" +
                                    "请财务、法务同事评估是否需要走诉讼流程，避免相关风险。<br/>" +
                                    "如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc());

                    mailReceivers = wxUserWorkCodeList.stream().map(r -> r + "@bilibili.com").collect(Collectors.toList());
                    mailReceivers.addAll(Lists.newArrayList(MJZ_BILIBILI_COM, FINBP_A_BILIBILI_COM, HUIKUAN_LEGAL_BILIBILI_COM));
                    mailSubject = String.format("【重要】合同%s（%s）回款预警", contractDto.getContractNumber(), contractDto.getCustomerName());
                    mailContent = String.format("您有如下合同已执行完成210天，仍未完成回款动作，已严重超过账期。<br/>" +
                                    "请评估是否需要走发催款函/诉讼流程，避免相关风险。如需财务及法务协助，请及时与财务及法务联系。<br/>" +
                                    "如相关流程都已发起，可忽略该信息，有其他问题可咨询媒介对接人员。合同详情如下：<br/>" +
                                    "客户名称：%s<br/>" +
                                    "代理商名称：%s<br/>" +
                                    "合同号：%s<br/>" +
                                    "合同名称：%s<br/>" +
                                    "项目名称：%s<br/>" +
                                    "合同时间：%s<br/>" +
                                    "合同金额：%s<br/>" +
                                    "责任销售：%s<br/>" +
                                    "合同是否开票完成：%s<br/>" +
                                    "查看详情：<a href=\"https://cm-mng.bilibili.co/crm/#/contract/list/show/general/%s\">点击后跳转至对应合同的详情页</a>", contractDto.getCustomerName(), StringUtils.isEmpty(contractDto.getAgentAccountName()) ? "--" : contractDto.getAgentAccountName(), contractDto.getContractNumber(),
                            contractDto.getName(), contractDto.getProjectName(), Utils.getTimestamp2String(contractDto.getBeginTime()) + "&" + Utils.getTimestamp2String(contractDto.getEndTime()), Utils.fromFenToYuan(contractDto.getAmount()), saleDto.getName(), IsValid.getByCode(contractDto.getIsOaOpenBill()).getDesc(), contractDto.getId());
                    break;
                default:
                    break;
            }

            mailReceivers = mailReceivers.stream().filter(r -> !r.startsWith("未知")).collect(Collectors.toList());
            log.info("wxUserWorkCodeList {}, wxContent {}, mailReceivers {}, mailSubject {}, mailContent {}", wxUserWorkCodeList, wxContent, mailReceivers, mailSubject, mailContent);
            //send qiwe
            contractBiiPeriodAlertComponent.sendQiwe(wxUserWorkCodeList, wxTitle + wxContent);

            //send email
            contractBiiPeriodAlertComponent.sendEmail(mailReceivers, mailSubject, mailContent);

        }
    }


}
