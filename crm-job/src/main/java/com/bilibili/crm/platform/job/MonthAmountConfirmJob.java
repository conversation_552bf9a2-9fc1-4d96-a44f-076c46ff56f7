package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.finance.service.IBrandIncomeConfirmationService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.BrandIncomeConfirmStatus;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import java.sql.Timestamp;
import java.util.Calendar;

/**
 * @description:
 * @author: wangbin01
 * @create: 2018-06-18
 **/
@Component
@JobHandler("MonthAmountConfirmJob")
@Slf4j
public class MonthAmountConfirmJob extends IJobHandler {

    @Autowired
    private IBrandIncomeConfirmationService confirmationService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            Calendar calendar = Calendar.getInstance();
            int day = calendar.get(Calendar.DAY_OF_MONTH);
            int hour = calendar.get(Calendar.HOUR_OF_DAY);
            if (day == 1 && hour == 0) {
                Timestamp timestamp = new Timestamp(calendar.getTimeInMillis());
                confirmationService.initHistory(timestamp, timestamp, BrandIncomeConfirmStatus.NOT_CONFIRM.getCode());

                timestamp = Utils.getSomeDayAgo(timestamp, 1);
                confirmationService.insertOrUpdateStatus(timestamp, BrandIncomeConfirmStatus.UN_CONFIRM.getCode());
            }
        });
        return ReturnT.SUCCESS;
    }
}
