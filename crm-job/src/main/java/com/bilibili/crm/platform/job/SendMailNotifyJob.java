package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.contract.service.IContractQueryService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

@Component
@JobHandler("SendMailNotifyJob")
@Slf4j
public class SendMailNotifyJob extends IJobHandler {

    @Autowired
    private IContractQueryService contractQueryService;

    /**
     * 对符合条件的合同发送邮件提示
     * @param s
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> contractQueryService.queryContractAndSendMail());
        return ReturnT.SUCCESS;
    }
}
