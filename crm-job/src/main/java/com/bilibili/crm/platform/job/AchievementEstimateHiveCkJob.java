package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz_common.olap.config.PaladinConfig;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.common.SafeStopWatch;
import com.bilibili.crm.platform.app.api.service.*;
import com.bilibili.crm.platform.app.api.service.dto.AchievementEstimateHiveCkQueryListByGroupDTO;
import com.bilibili.crm.platform.app.api.service.dto.PrepareDataDto;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.po.addata.CrmAchievementCardPo;
import com.bilibili.crm.platform.biz.service.weixin.MsgType;
import com.bilibili.crm.platform.biz.service.weixin.Text;
import com.bilibili.crm.platform.biz.service.weixin.WxRobotMsg;
import com.bilibili.crm.platform.biz.service.weixin.service.WxRobotService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.bo.AchievementEstimateHiveCkParam;
import com.bilibili.crm.platform.job.common.AbstractBaseParamJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.slf4j.MDC;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;
import java.util.Objects;
import java.util.concurrent.TimeUnit;

/**
 * 仪表盘-业绩监控-业绩季度预估job
 *
 * <AUTHOR>
 * date 2023/8/17 17:14.
 * Contact: <EMAIL>.
 */
@Component
@JobHandler("achievementEstimateHiveCkJob")
@Slf4j
public class AchievementEstimateHiveCkJob extends AbstractBaseParamJobHandler<AchievementEstimateHiveCkParam> {
    @Autowired
    private AchievementEstimateHiveCkService achievementEstimateHiveCkService;
    @Autowired
    private AchievementEstimateHiveCkPrepareService achievementEstimateHiveCkPrepareService;
    @Autowired
    private AchievementEstimateHiveCkNinetyEveryDayService achievementEstimateHiveCkNinetyEveryDayService;
    @Autowired
    private AchievementEstimateHiveCkInsertMysqlService achievementEstimateHiveCkInsertMysqlService;
    @Autowired
    private AchievementEstimateHiveCkInsertCardService achievementEstimateHiveCkInsertCardService;
    @Autowired
    private CrmCacheManager crmCacheManager;
    @Autowired
    private WxRobotService wxRobotService;
    @Value("${crm.portal.env:prd}")
    private String env;
    private static final String SUB_TASK_INSERT_AD_DATA_MYSQL_BY_CK = "SUB_TASK_INSERT_AD_DATA_MYSQL_BY_CK";
    private static final String SUB_TASK_INSERT_NINETY_EVERY_DAY_DATE = "SUB_TASK_INSERT_NINETY_EVERY_DAY_DATE";
    private static final String SUB_TASK_INSERT_CARD_DATA_MYSQL_BY_CK = "SUB_TASK_INSERT_CARD_DATA_MYSQL_BY_CK";
    private static final String TASK_RUNNING = "AchievementEstimateHiveCkJob_Flag_running";
    @Autowired
    private PaladinConfig paladinConfig;

    public AchievementEstimateHiveCkJob() {
        super(AchievementEstimateHiveCkParam.class, AchievementEstimateHiveCkParam::new);
    }

    //"yyyy-MM-dd"
    @Override
    protected boolean biz(AchievementEstimateHiveCkParam param) {
        AlarmHelper.log("env", env);
        Timestamp timestamp;
        if (StringUtils.isEmpty(param.getDate())) {
            timestamp = Utils.getYesteday();
        } else {
            timestamp = CrmUtils.parseTimestamp(param.getDate(), CrmUtils.YYYYMMDD);
        }
        String errMsg = String.format("\n环境:%s\n日期:%s\nreq_trace_id: %s\n", env, CrmUtils.formatDate(timestamp, CrmUtils.YYYYMMDD), MDC.get("req_trace_id"));

        AlarmHelper.log("Parameters", timestamp, param);
        try {
            run(param, timestamp);
        } catch (Throwable e) {
            log.error("wg03_info_AchievementEstimateSaleSummaryJob,have_error=", e);
            AlarmHelper.alarmEx("EstimateSaleSummaryJob", e, param);
            sendWxRobotMsg(e.getMessage() + errMsg);
            return false;
        } finally {
            crmCacheManager.deleteKey(TASK_RUNNING);
        }
        
        return true;
    }

    private void sendWxRobotMsg(String msg) {
        WxRobotMsg wxRobotMsg = WxRobotMsg.builder()
                .msgtype(MsgType.TEXT.getType())
                .text(new Text(msg))
                .build();
        wxRobotService.sendWeChatWork(wxRobotMsg, paladinConfig.getString("achievement.chat.robot"));
    }

    private boolean run(AchievementEstimateHiveCkParam param, Timestamp timestamp) {
        SafeStopWatch watch = new SafeStopWatch("AchievementEstimateSaleSummaryJob");
        String jobRunRedisKey = "wg03_info_AchievementEstimateSaleSummaryJob_Flag_" + CrmUtils.formatDate(timestamp, CrmUtils.YYYYMMDD);
        boolean taskNotFinish = subTaskNotDone(SUB_TASK_INSERT_AD_DATA_MYSQL_BY_CK, timestamp)
                || subTaskNotDone(SUB_TASK_INSERT_NINETY_EVERY_DAY_DATE, timestamp)
                || subTaskNotDone(SUB_TASK_INSERT_CARD_DATA_MYSQL_BY_CK, timestamp);

        boolean res = true;
        // 自动调度，无参数
        if (StringUtils.isEmpty(param.getDate())) {
            // JOB正在执行中，直接忽略
            if (Objects.nonNull(crmCacheManager.getValue(TASK_RUNNING))) {
                log.info("AchievementEstimateSaleSummaryJob_Running");
                return true;
            }
            // 子任务全部执行完成，直接忽略
            if (!taskNotFinish) {
                log.info("AchievementEstimateSaleSummaryJob_Have_Cache_Success");
                return true;
            }
        }
        crmCacheManager.setValueWithTime(TASK_RUNNING, true, TimeUnit.DAYS, 1);
        watch.pushTask("ckTodayDataIsReady");
        if (!achievementEstimateHiveCkService.ckTodayDataIsReady(timestamp)) {
            AlarmHelper.log("CkNotReady");
            return true;
        }

        watch.pushTask("prepareAllFrontData");

        PrepareDataDto prepareDataDto;
        try {
            prepareDataDto = achievementEstimateHiveCkPrepareService.prepareAllFrontData(timestamp, timestamp, 1);
        } catch (Exception e) {
            throw new IllegalStateException("准备数据异常", e);
        }

        if (param.isInsertAdDataMysqlByCk() || subTaskNotDone(SUB_TASK_INSERT_AD_DATA_MYSQL_BY_CK, timestamp)) {
            watch.pushTask(SUB_TASK_INSERT_AD_DATA_MYSQL_BY_CK);
            AlarmHelper.log("dailyReport", timestamp);
            dailyReport(timestamp, jobRunRedisKey, prepareDataDto);
            markTaskSuccess(SUB_TASK_INSERT_AD_DATA_MYSQL_BY_CK, timestamp);
        }

        if (param.isInsertNinetyEveryDayData() || subTaskNotDone(SUB_TASK_INSERT_NINETY_EVERY_DAY_DATE, timestamp)) {
            if (Objects.equals(CrmUtils.formatDate(timestamp, CrmUtils.YYYYMMDD), CrmUtils.formatDate(Utils.getYesteday(), CrmUtils.YYYYMMDD))) {
                watch.pushTask("insertNinetyEveryDayData");
                AlarmHelper.log("insertNinetyEveryDayData", timestamp);
                achievementEstimateHiveCkNinetyEveryDayService.insertNinetyEveryDayData(timestamp, prepareDataDto);
            }
            markTaskSuccess(SUB_TASK_INSERT_NINETY_EVERY_DAY_DATE, timestamp);
        }
        if (param.isInsertCardDataMysqlByCk() || subTaskNotDone(SUB_TASK_INSERT_CARD_DATA_MYSQL_BY_CK, timestamp)) {
            watch.pushTask("insertCardDataMysqlByCk");
            try {
                cardData(timestamp, prepareDataDto);
                markTaskSuccess(SUB_TASK_INSERT_CARD_DATA_MYSQL_BY_CK, timestamp);
            } catch (Exception e) {
                AlarmHelper.log("指标卡数据产出失败", e, timestamp);
                res = false;
            }
        }

        watch.log();

        return res;
    }

    private void dailyReport(Timestamp timestamp, String jobRunRedisKey, PrepareDataDto prepareDataDto) {
        try {
            crmCacheManager.setValueWithTime(jobRunRedisKey, "1", TimeUnit.DAYS, 1);
            achievementEstimateHiveCkInsertMysqlService.insertAdDataMysqlByCk(timestamp, prepareDataDto);
            crmCacheManager.setValueWithTime(jobRunRedisKey, "2", TimeUnit.DAYS, 1);
            sendWxRobotMsg(String.format("日报产出成功\n环境:%s\n日期:%s\nreq_trace_id: %s", env, CrmUtils.formatDate(timestamp, CrmUtils.YYYYMMDD), MDC.get("req_trace_id")));
        } catch (Throwable t) {
            throw new IllegalStateException("日报产出异常", t);
        }
    }

    private void cardData(Timestamp logDate, PrepareDataDto prepareDataDto) {
        List<List<Timestamp>> cardTimeEachList = CrmUtils.getCardEachTimeSlotByDate(logDate);
        List<Timestamp> startList = new ArrayList<>();
        List<Timestamp> endList = new ArrayList<>();
        Long minTime = paladinConfig.getLongOrDefault("card.data.min.date", null);
        cardTimeEachList.forEach(timestamps -> {
            Timestamp analysisBeginDate = timestamps.get(0);
            Timestamp analysisEndDate = timestamps.get(1);
            if (minTime != null) {
                if (analysisBeginDate.getTime() < minTime || analysisEndDate.getTime() < minTime) {
                    return;
                }
            }

            AchievementEstimateHiveCkQueryListByGroupDTO queryListByGroupDTO = new AchievementEstimateHiveCkQueryListByGroupDTO();
            queryListByGroupDTO.setQueryBiliBili(Boolean.TRUE);
            queryListByGroupDTO.setLogDate(logDate);
            queryListByGroupDTO.setAnalysisBeginDate(analysisBeginDate);
            queryListByGroupDTO.setAnalysisEndDate(analysisEndDate);
            queryListByGroupDTO.setSaleAuthData(0);
            List<CrmAchievementCardPo> cardPos = achievementEstimateHiveCkService.queryCrmAchievementCardDtoAllList(queryListByGroupDTO);
            if (CollectionUtils.isEmpty(cardPos) || minTime != null) {
                AlarmHelper.log("CardDate",
                        CrmUtils.formatDate(analysisBeginDate, CrmUtils.YYYYMMDDHHMMSS),
                        CrmUtils.formatDate(analysisEndDate, CrmUtils.YYYYMMDDHHMMSS));
                startList.add(analysisBeginDate);
                endList.add(analysisEndDate);
            }
        });
        if (CollectionUtils.isEmpty(startList) && CollectionUtils.isEmpty(endList)) {
            AlarmHelper.log("CardData", logDate, "没有需要产出的数据");
            return;
        }
        try {
            crmCacheManager.setValueWithTime("wg03_info_insertCardDataMysqlByCk_Flag_" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD), "2", TimeUnit.DAYS, 1);
            AlarmHelper.log("insertCardDataMysqlByCk", logDate, startList, endList);
            achievementEstimateHiveCkInsertCardService.insertCardDataMysqlByCk(logDate, null, startList, endList, null, null, null, null, prepareDataDto);
            crmCacheManager.setValueWithTime("wg03_info_insertCardDataMysqlByCk_Flag_" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD), "1", TimeUnit.DAYS, 1);
        } catch (Throwable t) {
            crmCacheManager.setValueWithTime("wg03_info_insertCardDataMysqlByCk_Flag_" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD), "0", TimeUnit.DAYS, 1);
            throw new IllegalStateException("指标卡数据产出异常", t);
        }
    }

    public boolean subTaskNotDone(String subTask, Timestamp logDate) {
        String key = "AchievementEstimateSaleSummaryJob_Flag_SubTask_" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD) + "_" + subTask;
        Object cache = crmCacheManager.getValue(key);
        boolean res = !Objects.equals("true", String.valueOf(cache));
        AlarmHelper.log("subTaskNotDone", subTask, res);
        return res;
    }

    public void markTaskSuccess(String subTask, Timestamp logDate) {
        String key = "AchievementEstimateSaleSummaryJob_Flag_SubTask_" + CrmUtils.formatDate(logDate, CrmUtils.YYYYMMDD) + "_" + subTask;

        AlarmHelper.log("markTaskSuccess", subTask);
        crmCacheManager.setValueWithTime(key, "true", TimeUnit.DAYS, 1);
    }

}

