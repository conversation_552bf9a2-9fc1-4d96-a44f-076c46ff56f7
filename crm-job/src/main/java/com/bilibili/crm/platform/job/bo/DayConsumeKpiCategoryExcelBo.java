package com.bilibili.crm.platform.job.bo;

import com.bilibili.adp.web.framework.annotations.ExcelResources;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

/**
 * <AUTHOR>
 * @Description
 * @date 11/28/23
 **/
@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class DayConsumeKpiCategoryExcelBo {

    @ExcelResources(title = "行业")
    private String categoryName;

    @ExcelResources(title = "截止日消耗（万）")
    private String quarterConsume;

    @ExcelResources(title = "QTD（万）")
    private String qtd;

    @ExcelResources(title = "非带货预估（万）")
    private String notSellGoodsPredict;

    @ExcelResources(title = "带货预估（万）")
    private String sellGoodsPredict;

    /**
     * 冲刺落点
     */
    @ExcelResources(title = "冲刺落点（万）")
    private String sprint;

    @ExcelResources(title = "前日消耗（万）")
    private String beforeYesterDayConsume;

    @ExcelResources(title = "昨日消耗（万）")
    private String yesterDayConsume;

    @ExcelResources(title = "当日消耗（万）")
    private String dayConsume;

    @ExcelResources(title = "日环比")
    private String dayLinkRatio;

    @ExcelResources(title = "剩余日均TODO（万）")
    private String remainDayConsumeAvgToDo;

    @ExcelResources(title = "当日消耗gap（万）")
    private String dayConsumeAvgGap;

    /**
     * 日耗达成率
     */
//    @ExcelResources(title = "")
    private BigDecimal dayConsumeAvgCompleteRatio;

    private String weekAvg;

    private String sevenDayAgoLinkRatio;

    private String fourteenDayAgoLinkRatio;

}
