package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.writer.WxSellGoodAgentWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-02-06 17:03:07
 * @description:
 **/

@Component
@JobHandler("WxSellGoodAgentJob")
@Slf4j
public class WxSellGoodAgentJob extends IJobHandler {

    @Resource
    WxSellGoodAgentWriter wxSellGoodAgentWriter;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    @Autowired
    private WxAppDataCheckService dataCheckService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {

        Timestamp begin = Utils.getToday();
        Timestamp end = Utils.getEndOfDay(Utils.getToday());
        if (StringUtils.hasText(args) && args.contains("~")) {
            String[] strings = args.split("~");

            // 所以取数Job有两个参数
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }

        // 获取这些间隔的天数
        List<Timestamp> dayList = Utils.getEachDays(begin, end);
        log.info("WxSellGoodAgentJobTime, begin:{},end:{}", begin, end);

        if (!StringUtils.hasText(args)) {
            //检查当天状态位
            Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
            // 幂等
            if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SELL_GOODS_AGENT)) {
                Cat.logEvent("WxAppCacheSellGoodsAgentJob_DATA_HAD_DOWN", day.toString());
                return ReturnT.SUCCESS;
            }
        }

        // 对每一天都构建当天的代理商所有收入、顶部收入、金额
        dayList.forEach(day -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                try {
                    //检查ck昨日数据是否ready
                    if (!dataCheckService.mobileDataCheckByState(Utils.getSomeDayAgo(day, 1), dataCheckService.generateSellGoodFlyCkKey(Utils.getSomeDayAgo(day, 1)))) {
                        return;
                    }

                    // 构建代理商全量数据
                    wxSellGoodAgentWriter.buildAgentAllIncome(day);

                    // 构建代理商top数据
                    wxSellGoodAgentWriter.buildAgentTopIncome(day);

                    // 构建代理商活跃数量数据
                    wxSellGoodAgentWriter.buildAgentAmount(day);

                    // 设置缓存幂等
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(day), WxModuleType.SELL_GOODS_AGENT);
                } catch (Exception e) {
                    Cat.logEvent("Exception_WxSalesGoodAgentJob", e.getMessage());
                    throw new RuntimeException(e);
                }
            });
        });
        return ReturnT.SUCCESS;
    }
}
