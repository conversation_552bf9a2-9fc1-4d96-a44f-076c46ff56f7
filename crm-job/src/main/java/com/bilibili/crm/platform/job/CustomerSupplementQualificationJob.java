package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.customer.dto.CustomerSupplementQuaDto;
import com.bilibili.crm.platform.biz.common.ExportBizUtils;
import com.bilibili.crm.platform.biz.service.customer.CustomerQueryService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.model.CustomerSupplementQuaVo;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import java.sql.Timestamp;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

/**
 * @Description 客户资质补录邮件提醒
 * <AUTHOR>
 * @DATE 2022/8/23 2:59 下午
 **/
@Slf4j
@Component
@JobHandler("CustomerSupplementQualificationJob")
public class CustomerSupplementQualificationJob extends IJobHandler {

    @Autowired
    private CustomerQueryService customerQueryService;
    @Autowired
    private ExportBizUtils exportBizUtils;

    @Value("${supplement.send.to:<EMAIL>}")
    private String supplementSendTo;

    @Value("${supplement.send.ccs:<EMAIL>}")
    private String supplementSendCcs;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>CustomerSupplementQualificationJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), transation -> this.customerSupplementQualificationSendEmail());
        log.info("=====>>>>>>>>>>>>>>>CustomerSupplementQualificationJob end executing.");
        return ReturnT.SUCCESS;
    }

    public void customerSupplementQualificationSendEmail() {
        List<CustomerSupplementQuaDto> customerSupplementQuaDtos = customerQueryService.getCustomerSupplementQuaDtos(null, null);
        Timestamp sevenDayAfter = Utils.getSomeDayAfterToday(7);
        String sevenDayAfterStr = Utils.getTimestamp2String(sevenDayAfter);
        List<CustomerSupplementQuaDto> sendList = customerSupplementQuaDtos.stream().filter(t -> t.getSupplementEndTime().compareTo(sevenDayAfterStr) <= 0).collect(Collectors.toList());
        List<CustomerSupplementQuaVo> sendVos = CrmUtils.copyList(sendList, CustomerSupplementQuaVo.class);
        if (CollectionUtils.isEmpty(sendVos)) {
            return;
        }
        List<String> to = Arrays.asList(supplementSendTo.split(","));
        List<String> css = Arrays.asList(supplementSendCcs.split(","));
        exportBizUtils.asyncExportToEmail(sendVos, "待补充资质的客户列表", to, css);
    }
}
