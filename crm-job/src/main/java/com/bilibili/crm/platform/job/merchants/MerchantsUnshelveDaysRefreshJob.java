package com.bilibili.crm.platform.job.merchants;

import com.bilibili.adp.web.framework.core.Pagination;
import com.bilibili.crm.biz.merchants.bo.QueryMerchantsProjectParam;
import com.bilibili.crm.biz.merchants.bo.QueryMerchantsShelveParam;
import com.bilibili.crm.biz.merchants.dto.MerchantsProjectDto;
import com.bilibili.crm.biz.merchants.enums.MerchantsRackEnum;
import com.bilibili.crm.biz.merchants.enums.MerchantsStatusEnum;
import com.bilibili.crm.biz.merchants.repository.MerchantsShelveRepository;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.time.StopWatch;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.time.Duration;
import java.time.LocalDateTime;
import java.util.List;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2024/12/5
 */
@Component
@JobHandler("MerchantsUnshelveDaysRefreshJob")
@Slf4j
public class MerchantsUnshelveDaysRefreshJob extends IJobHandler {

    private static final int PAGE_SIZE = 50;

    @Resource
    private MerchantsShelveRepository merchantsShelveRepository;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("MerchantsUnshelveDaysRefreshJob start");
        StopWatch started = StopWatch.createStarted();
        pageHandle();
        log.info("MerchantsUnshelveDaysRefreshJob end, cost = {}", started.getTime(TimeUnit.MILLISECONDS));
        return ReturnT.SUCCESS;
    }

    public void pageHandle() throws InterruptedException {
        QueryMerchantsShelveParam param = new QueryMerchantsShelveParam();
        param.setSize(PAGE_SIZE);

        LocalDateTime now = LocalDateTime.now();
        int page = 1;
        int pageCount;
        do {
            param.setPage(page);
            Pagination<List<MerchantsProjectDto>> pagination = merchantsShelveRepository.pageQuery(param);
            List<MerchantsProjectDto> data = pagination.getData();
            if (CollectionUtils.isEmpty(data)) {
                return;
            }


            data.forEach(d -> {
                Integer investmentLongTimeFlag = d.getInvestmentLongTimeFlag();
                LocalDateTime investmentEndTime = d.getInvestmentEndTime();
                if (investmentLongTimeFlag == 1 || investmentEndTime == null) {
                    merchantsShelveRepository.update(d.getId(), Integer.MAX_VALUE);
                    return;
                }
                merchantsShelveRepository.update(d.getId(), (int) Duration.between(now, investmentEndTime).toDays());
            });
            pageCount = pagination.getPage();
            page++;
            Thread.sleep(10);
        } while (pageCount >= page);
    }
}
