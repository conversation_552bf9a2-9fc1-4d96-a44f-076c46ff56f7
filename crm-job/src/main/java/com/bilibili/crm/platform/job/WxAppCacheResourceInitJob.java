package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.cache.WxAppResourceCacheWriter;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;
@Component
@JobHandler("WxAppCacheResourceInitJob")
@Slf4j
public class WxAppCacheResourceInitJob extends IJobHandler {
    @Autowired
    private WxAppResourceCacheWriter wxAppResourceCacheWriter;
    /**
     * @param args exp：args:20200714~20200714
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);

            dayList.forEach(d -> {
                try {
                    wxAppResourceCacheWriter.cacheCompositionYesterday(d);
                    Cat.logEvent("WxAppCacheResourceInitJob_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    Cat.logEvent("WxAppCacheResourceInitJob_SUCCESS", d.toString() + ":failed");
                }
                try {
                    Thread.sleep(60000L);
                } catch (InterruptedException e) {
                    log.info("WxAppCacheResourceInitJob InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }
            });

        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppCacheResourceInitJob", e.getMessage());
            throw e;
        }
        return ReturnT.SUCCESS;
    }
}
