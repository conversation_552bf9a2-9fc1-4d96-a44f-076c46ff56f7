package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.wallet.service.IAccountWalletTradeBalanceQueryService;
import com.bilibili.crm.platform.biz.service.wallet.EsAccountWalletService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @author: brady
 * @time: 2021/12/7 6:00 下午
 */
@Component
@JobHandler("CheckTableJob")
@Slf4j
public class CheckTableJob extends IJobHandler {
    @Autowired
    private IAccountWalletTradeBalanceQueryService accountWalletTradeBalanceQueryService;
    @Autowired
    private EsAccountWalletService esAccountWalletService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {

        log.info("=====>>>>>>>>>>>>>>>CheckTableJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(),
                (t) -> accountWalletTradeBalanceQueryService.checkTableExist());
        log.info("=====>>>>>>>>>>>>>>>CheckTableJob end executing.");

//        CatUtils.newTransaction(CatUtils.JOB, "acc_account_wallet_log_by_month_job_check",
//                (t) -> esAccountWalletService.createIndexByMonth(Utils.getToday()));

        return ReturnT.SUCCESS;
    }
}
