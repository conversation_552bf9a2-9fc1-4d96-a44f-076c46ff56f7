package com.bilibili.crm.platform.job;

import com.alibaba.fastjson.JSON;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.white.IWxWriteListService;
import com.bilibili.crm.platform.api.white.dto.QueryWxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteDto;
import com.bilibili.crm.platform.api.white.dto.WxAppWhiteType;
import com.bilibili.crm.platform.app.api.client.IWxAppClient;
import com.bilibili.crm.platform.app.api.client.dto.response.WxAppMessageResponse;
import com.bilibili.crm.platform.app.api.client.dto.response.WxAppUserInfo;
import com.bilibili.crm.platform.app.cache.config.WxAppCacheConfig;
import com.bilibili.crm.platform.app.cache.sale.WxAppSaleCacheManager;
import com.bilibili.crm.platform.app.exception.WxAppCrmExceptionCode;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.income_sale.manager.WxAppSaleHelper;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.common.IsValid;
import com.dianping.cat.Cat;
import com.google.common.base.Strings;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;
import java.util.stream.Collectors;
import java.util.stream.Stream;

/**
 * @author: brady
 * @time: 2020/12/14 6:54 下午
 */
@Component
@JobHandler("WxAppSalePushJob")
@Slf4j
public class WxAppSalePushJob extends IJobHandler {
    @Autowired
    private WxAppSaleCacheManager wxAppSaleCacheManager;
    @Autowired
    private IWxWriteListService wxWriteListService;
    @Autowired
    private IWxAppClient wxAppClient;
    @Autowired
    private WxAppSaleHelper wxAppSaleHelper;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        Timestamp day = Utils.getYesteday();
        if (!StringUtils.hasText(s) && pushCondition(day)) {
            Cat.logEvent("WxAppSalePushJob_DATA_HAD_DOWN", day.toString());
            return ReturnT.SUCCESS;
        }

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {
                List<WxAppWhiteDto> wxAppWhiteDtos = wxWriteListService.queryWhite(QueryWxAppWhiteDto.builder()
                        .whiteType(WxAppWhiteType.SALE.getCode())
                        .isPush(IsValid.TRUE.getCode())
                        .ctimeEnd(Utils.getEndOfDay(day))
                        .whiteStatus(Integer.valueOf(0))
                        .build());
                //正常销售
                List<String> normalSale = wxAppSaleHelper.getNormalSaleEmail();

                //过滤推送人员，管理员推，销售状态正常推
                List<WxAppWhiteDto> pushList = wxAppWhiteDtos.stream().filter(item -> {
                    if (IsValid.TRUE.getCode().equals(item.getIsManager())) {
                        return true;
                    }
                    if (normalSale.contains(item.getUserName())) {
                        return true;
                    }
                    return false;
                }).collect(Collectors.toList());
                //消息推送
                dailyPush(pushList, day);
                //设置日报推送状态位
                wxAppSaleCacheManager.setWxSaleAbilityStatus(day, WxAppCacheConfig.WX_APP_SALE_PUSH_JOB_STATUS);

            } catch (WxAppException e) {
                throw new RuntimeException(e);
            }
        });
        return ReturnT.SUCCESS;
    }

    private boolean pushCondition(Timestamp day) {
        Integer status = wxAppSaleCacheManager.getWxSaleAbilityStatus(day, WxAppCacheConfig.WX_APP_SALE_PUSH_JOB_STATUS);

        if (IsValid.TRUE.getCode().equals(status)) {
            return true;
        }
        return false;
    }

    private void dailyPush(List<WxAppWhiteDto> userList, Timestamp day) throws WxAppException {
        if (CollectionUtils.isEmpty(userList)) {
            return;
        }

        //判断所有销售数据是否已准备好
        for (WxAppWhiteDto wxAppWhiteDto : userList) {
            if (IsValid.TRUE.getCode().equals(wxAppWhiteDto.getIsManager())) {
                isCacheReady(day, WxAppSaleHelper.SALE_MANAGER);
                continue;
            }
            isCacheReady(day, wxAppWhiteDto.getUserName());
        }

        userList.forEach(item -> {
            try {
                WxAppUserInfo userInfo = wxAppClient.getUserInfo(item.getWorkNum());
                String paperDate = Utils.getTimestamp2String(Utils.getYesteday());

                String alias = userInfo.getAlias();
                String title = "商业中心通知";
                String welcome = "<div class=\"normal\">" + alias + " 您好</div>";
                String content = "<div class=\"highlight\">商业中心手机日报销售版（" + paperDate + "）已生成</div>";
                String description = welcome + content;
//                String url = "http://cm.bilibili.com/ldad/daily-paper/daily.html#/sale" + "?tenant_code=CRM&paper_date=" + Utils.getYesteday().getTime();
                String url = "http://cm.bilibili.com/ldad/daily-paper-new/main.html?mock_login=no&code=&state=#/";

                WxAppMessageResponse response = wxAppClient.sendTextCard(Lists.newArrayList(item.getWorkNum()), title, description, url);
                log.info("WxSalePush user {} ,response {}", alias, JSON.toJSONString(response));

                //List<String> invalidUserList = Stream.of(response.getInvalidUser().split("|")).collect(Collectors.toList());
                String invalidUserList = response.getInvalidUser();
                if (!Strings.isNullOrEmpty(invalidUserList)) {
                    log.info("WxSalePush invalid user {} ,response {}", alias, JSON.toJSONString(response));
                    Cat.logEvent("WX_APP_SALE_PUSH_FAIL", "dailyPush", "failed", invalidUserList);
                }
                try {
                    Thread.sleep(500L);
                } catch (InterruptedException e) {
                    log.info("WxSalePush InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }
            } catch (WxAppException e) {
                log.error("WxSalePush error response {}", e);
                Cat.logError("WxAppSalePushJob.dailyPush userId " + item, e);
            }
        });
    }

    private void isCacheReady(Timestamp day, String username) throws WxAppException {
//        if (!wxAppSaleCacheManager.isSalePaperStateReady(day, username)) {
//            log.info("WxAppSalePushJob push username {} not ready ", username);
//            throw new WxAppException(WxAppCrmExceptionCode.PAPER_STATE_ERROR, username + " sale data not ready");
//        }
        if (!wxAppSaleCacheManager.isSalePaperStateReadyV2(day)) {
            log.info("WxAppSalePushJob push username {} not ready ", username);
            throw new WxAppException(WxAppCrmExceptionCode.PAPER_STATE_ERROR, username + " sale data not ready");
        }
    }

    /**
     * mock 推送消息
     *
     * @param username
     */
    public void mockPush(String username) {
        List<WxAppWhiteDto> wxAppWhiteDtos = wxWriteListService.queryWhite(QueryWxAppWhiteDto.builder()
                .whiteType(WxAppWhiteType.SALE.getCode())
                .isPush(IsValid.TRUE.getCode())
                .ctimeEnd(Utils.getEndOfDay(Utils.getToday()))
                .userName(username)
                .whiteStatus(Integer.valueOf(0))
                .build());
        if (CollectionUtils.isEmpty(wxAppWhiteDtos)) {
            return;
        }

        List<WxAppWhiteDto> pushList = wxAppWhiteDtos;

        try {
            dailyPush(pushList, Utils.getYesteday());
        } catch (WxAppException e) {
            e.printStackTrace();
        }
    }
}
