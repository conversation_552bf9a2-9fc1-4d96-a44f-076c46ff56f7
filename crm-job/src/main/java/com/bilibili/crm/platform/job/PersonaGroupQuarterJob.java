package com.bilibili.crm.platform.job;

import com.bilibili.crm.platform.api.company.service.ICompanyGroupService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;


/**
 * 每季度更新计算一次集团列表行业情况
 *
 * <AUTHOR>
 */
@Component
@JobHandler("PersonaGroupQuarterJob")
@Slf4j
public class PersonaGroupQuarterJob extends IJobHandler {

    @Autowired
    private ICompanyGroupService companyGroupService;

    /**
     * @param args
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>PersonaGroupQuarterJob start executing...");
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {
                companyGroupService.loadQuarterAndUpdateCompanyGroup(null,null);
            } catch (Exception e) {
                log.info("=====>>>>>>>>>>>>>>>PersonaGroupQuarterJob error.", e);
                Cat.logEvent("PersonaGroupQuarterJob", e.getMessage());
            }
        });
        log.info("=====>>>>>>>>>>>>>>>PersonaGroupQuarterJob end executing...");
        return ReturnT.SUCCESS;
    }
}
