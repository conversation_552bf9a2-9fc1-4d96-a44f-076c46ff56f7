/**
 * Inc.
 * Copyright (c) 2004-2023 All Rights Reserved.
 */
package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.writer.WxSalesGoodTransWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxDailyOcpcTargetEnum;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.soa.dto.SalesGoodOcpxTargetTransIncomeCompositionDto;
import com.bilibili.crm.platform.soa.dto.SalesGoodsOcpcTargetQuarterPieDto;
import com.bilibili.crm.platform.soa.dto.SalesGoodsOcpcTargetTransCompositeDto;
import com.bilibili.utils.TimeUtil;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.ArrayList;
import java.util.List;

/**
 * 微信带货日报转化目标数据同步Job。
 *
 * <AUTHOR> Zhao
 * @version $Id: WxSaleGoodsOcpcTargetTransJob.java, v 0.1 2023-03-01 9:37 PM Tony Zhao Exp $$
 */
@Slf4j
@JobHandler("WxSaleGoodsOcpcTargetTransJob")
@Component
public class WxSaleGoodsOcpcTargetTransJob extends IJobHandler {

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    @Autowired
    private WxSalesGoodTransWriter wxSalesGoodTransWriter;

    @Autowired
    private WxAppDataCheckService dataCheckService;

    @Override
    public ReturnT<String> execute(String param) throws Exception {

        Timestamp begin = new Timestamp(System.currentTimeMillis());
        Timestamp end = new Timestamp(System.currentTimeMillis());

        if (StringUtils.hasText(param)) {
            // 有分隔符
            if (param.contains("~")) {
                String[] strings = param.split("~");

                // 所以取数Job有两个参数
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }
        } else {
            // 不带参数，定时任务默认调度到当天

        }

        // 获取这些间隔的天数
        List<Timestamp> dayList = Utils.getEachDays(begin, end);

        if (!StringUtils.hasText(param)) {
            //检查当天状态位
            Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
            // 幂等
            if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SELL_GOODS_OCPC_TRANS)) {
                Cat.logEvent("WxSaleGoodsOcpcTargetTransJob_DATA_HAD_DONE", day.toString());
                return ReturnT.SUCCESS;
            }
        }

        // 对每一天都构建当天的代理商所有收入、顶部收入、金额
        dayList.stream().forEach(day -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                try {
                    // 检查昨天的标志位
                    Timestamp yesterdayBegin = incomeTimeUtil.getYesterdayBegin(day);
                    //检查ck数据是否ready => 校验当天数据是否已经灌到clickhouse里
                    if (!dataCheckService.mobileDataCheckByState(yesterdayBegin, dataCheckService.generateSellGoodFlyCkKey(yesterdayBegin))) {
                        return;
                    }

                    // 每一种转化类型都要
                    List<SalesGoodOcpxTargetTransIncomeCompositionDto> transList = new ArrayList<>();
                    for (WxDailyOcpcTargetEnum type : WxDailyOcpcTargetEnum.values()) {
                        int ocpxTargetType = type.getCode();
                        if (-1 != ocpxTargetType) {
                            // 构建当天下所有ocpc目标的转化数据（指标+折线图）
                            SalesGoodOcpxTargetTransIncomeCompositionDto oneTrans =
                                    wxSalesGoodTransWriter.buildOcpcTargetTransAllIncome(ocpxTargetType, day);
                            transList.add(oneTrans);
                        }
                    }

                    // 顺带生成季度饼图
                    SalesGoodsOcpcTargetQuarterPieDto quarterPie = wxSalesGoodTransWriter.generatePie(transList);

                    // 组合模型
                    SalesGoodsOcpcTargetTransCompositeDto ocpcComposite = new SalesGoodsOcpcTargetTransCompositeDto();
                    ocpcComposite.setDate(TimeUtil.getBeginOfDay(incomeTimeUtil.getYesterdayEnd(day)));
                    ocpcComposite.setTransIncomeCompositionList(transList);
                    ocpcComposite.setQuarterPie(quarterPie);

                    // 一次性保存所有的ocpcTarget收入
                    wxSalesGoodTransWriter.saveTransIncome2DB(ocpcComposite);

                    // 设置缓存幂等
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(day), WxModuleType.SELL_GOODS_OCPC_TRANS);

                } catch (Exception e) {
                    Cat.logEvent("Exception_WxSaleGoodsOcpcTargetTransJob", e.getMessage());
                    throw new RuntimeException(e);
                }
            });
        });

        return ReturnT.SUCCESS;
    }

}