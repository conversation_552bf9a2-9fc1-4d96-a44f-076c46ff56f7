package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.dataprepare.service.CrmKpiCkPrepareService;
import com.bilibili.crm.common.AlarmHelper;
import com.bilibili.crm.platform.biz.cache.CrmCacheManager;
import com.bilibili.crm.platform.biz.service.consume.WalletConsumeService;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.bilibili.crm.platform.job.common.AbstractBaseJobHandler;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Component;
import org.springframework.util.Assert;
import java.sql.Timestamp;
import java.util.Objects;

/**
 * @author: brady
 * @time: 2021/8/9 3:27 下午
 */
@Component
@JobHandler("FlyExtendWalletConsumeJob")
@Slf4j
public class FlyExtendWalletConsumeJob extends AbstractBaseJobHandler {
    @Autowired
    private WalletConsumeService walletConsumeService;
    @Autowired
    private CrmCacheManager crmCacheManager;
    @Value("${crm.portal.env:prd}")
    private String env;
    @Autowired
    private CrmKpiCkPrepareService crmKpiCkPrepareService;

    @Override
    protected boolean biz(String args) {
        //每天12点执行 依赖朝朝数据
        try {
            Timestamp begin = Utils.getYesteday();
            String logStr = CrmUtils.formatDate(begin, CrmUtils.YYYYMMDD);
            Timestamp end = begin;
            if (StringUtils.isNotBlank(args) && args.contains("~")) {
                //20200714~20200714
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }
            Object cache3 = crmCacheManager.getValue("FlyExtendJob_flushPersonFly2Es_Flag" + logStr);
            if (StringUtils.isBlank(args)) {
                //手动强制执行不走这里面
                if (Objects.nonNull(cache3)) {
                    log.info("FlyExtendWalletConsumeJob_Have_Cache_Success");
                    return true;
                }
            }
            Assert.isTrue(begin.after(new Timestamp(1717171199000L)), "20240601之前不准重刷有问题找产品 拾玖 谨言 研发 阿钢 朝朝 辰星");
            //个人起飞业绩看板刷数
            AlarmHelper.log("FlushPersonFly2Es", CrmUtils.formatDate(begin), CrmUtils.formatDate(end));
            walletConsumeService.flushPersonFly2Es(begin, end);
            Object cache11 = crmCacheManager.getValue("FlyExtendJob_writeFlyOrder2Es_Flag" + logStr);
            Object cache22 = crmCacheManager.getValue("FlyExtendJob_batchFlushWithholdMoney_Flag" + logStr);
            Object cache33 = crmCacheManager.getValue("FlyExtendJob_flushPersonFly2Es_Flag" + logStr);
            if (Objects.nonNull(cache11) && Objects.nonNull(cache22) && Objects.nonNull(cache33) && Objects.equals(env, "prd")) {
                crmKpiCkPrepareService.insertPrepareDataInfoToDB(begin, "FlyExtendJob");
            }
        } catch (Exception e) {
            log.error("FlyExtendWalletConsumeJob_error=", e);
            throw e;
        }
        log.info("FlyExtendWalletConsumeJob success args {}", args);
        return true;
    }

}
