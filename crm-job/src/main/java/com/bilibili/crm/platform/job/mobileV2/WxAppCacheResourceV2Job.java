package com.bilibili.crm.platform.job.mobileV2;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.cache.WxAppCacheLoader;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.cache.WxAppResourceCacheWriter;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.app.exception.WxAppException;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.List;

@Component
@JobHandler("WxAppCacheResourceJobV2")
@Slf4j
public class WxAppCacheResourceV2Job extends IJobHandler {

    @Autowired
    private WxAppResourceCacheWriter wxAppResourceCacheWriter;
    @Autowired
    private WxAppCacheLoader cacheLoader;
    @Autowired
    private IncomeTimeUtil incomeTimeUtil;
    @Autowired
    private WxAppCacheManager wxAppCacheManager;

    /**
     * @param args exp：args:20200714~20200714
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String args) throws Exception {
        try {
            Timestamp begin = Utils.getToday();
            Timestamp end = Utils.getEndOfDay(Utils.getToday());

            if (!StringUtils.hasText(args)) {
                //检查当天状态位
                Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
                if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.RESOURCE_V2)) {
                    Cat.logEvent("WxAppCacheResourceV2Job_DATA_HAD_DOWN", day.toString());
                    return ReturnT.SUCCESS;
                }
            }

            if (StringUtils.hasText(args) && args.contains("~")) {
                String[] strings = args.split("~");
                begin = StringDateParser.stringToTimestamp(strings[0]);
                end = StringDateParser.stringToTimestamp(strings[1]);
            }

            List<Timestamp> dayList = Utils.getEachDays(begin, end);

            Timestamp timestamp = incomeTimeUtil.getQuarterFirstDate(Utils.getNow());
            // 对历史季度最后一天回刷
            if ((Utils.getNow().getTime() - timestamp.getTime()) < 6L * 24L * 60L * 60L * 1000L && (Utils.getNow().getTime() - timestamp.getTime()) > 24L * 60L * 60L * 1000L) {
                dayList.add(timestamp);
            }

            dayList.forEach(d -> {
                try {
                    CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                        // 缓存计算数据
                        try {
                            // 缓存请求
                            wxAppResourceCacheWriter.cacheRequestV2(d);
                        } catch (Exception e) {
                            Cat.logEvent("Exception_WxAppCacheResourceV2Job", e.getMessage());
                            throw new RuntimeException(e);
                        }
                    });
                    Cat.logEvent("WxAppCacheResourceV2Job_SUCCESS", d.toString() + ":normal");
                } catch (Exception e) {
                    //刷数据记录Cat
                    Cat.logEvent("WxAppCacheResourceV2Job_SUCCESS", d.toString() + ":failed");
                    throw new RuntimeException(e);
                }

                try {
                    Thread.sleep(60000L);
                } catch (InterruptedException e) {
                    log.info("WxAppCacheResourceV2Job InterruptedException:", e);
                    Thread.currentThread().interrupt();
                }
            });
        } catch (Exception e) {
            Cat.logEvent("Exception_WxAppCacheResourceV2Job", e.getMessage());
            throw e;
        }
        log.info("WxAppCacheResourceV2Job success args {}" ,args );

        return ReturnT.SUCCESS;
    }
}
