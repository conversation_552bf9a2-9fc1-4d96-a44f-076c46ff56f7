package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.statement.IStatementService;
import com.bilibili.crm.platform.api.wallet.service.IXstLaunchIncomeQueryService;
import com.bilibili.crm.platform.soa.dto.statement.CrmStatementDetailBatchSaveDto;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import java.sql.Timestamp;
import java.util.Calendar;
import java.util.List;

/**
 * 花火合伙人月度结算单
 *
 * @author: brady
 * @time: 2022/10/8 2:52 下午
 */
@Slf4j
@Component
@JobHandler("CluePassMonthStatementJob")
public class CluePassMonthStatementJob extends IJobHandler {

    @Autowired
    private IStatementService statementService;

    @Autowired
    private IXstLaunchIncomeQueryService xstLaunchIncomeQueryService;

    /**
     * @param param exp :2022-12
     * @return
     * @throws Exception
     */
    @Override
    public ReturnT<String> execute(String param) throws Exception {
        Timestamp statementMonth = getStatementMonth();

        if (StringUtils.hasText(param)) {
            statementMonth = StringDateParser.stringToTimestamp(param);
        }

        log.info("CluePassMonthStatementJob start month {}", statementMonth);
        //合伙人结算单
        List<CrmStatementDetailBatchSaveDto> statementList = xstLaunchIncomeQueryService.queryXstStatementList(statementMonth);

        statementList.forEach(statement -> {
            statementService.createCluePassStatement(statement);
        });
        log.info("CluePassMonthStatementJob end month {} ,size {}", statementMonth, statementList.size());
        return ReturnT.SUCCESS;
    }


    private Timestamp getStatementMonth() {
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.MONTH, -1);
        calendar.set(Calendar.DAY_OF_MONTH, 1);
        return Utils.getBeginOfDay(Utils.getTimestamp(calendar.getTimeInMillis()));
    }
}
