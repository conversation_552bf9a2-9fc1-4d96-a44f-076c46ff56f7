package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.exception.ServiceException;
import com.bilibili.crm.platform.api.finance.service.IAccountFlowService;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

/**
 *
 * Created by fanwenbin on 2017/6/14.
 */
@Component
@JobHandler("AccountBalanceCheckJob")
@Slf4j
public class AccountBalanceCheckJob extends IJobHandler {

    @Autowired
    private IAccountFlowService accountFlowService;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        log.info("=====>>>>>>>>>>>>>>>AccountBalanceCheckJob.start.executing...");

        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            try {
                accountFlowService.checkAccountBalanceSendMail();
            } catch (ServiceException e) {
                log.error("=====>>>>>>>>>>>>>>>AccountBalanceCheckJob.execute.error", e);
            }
        });

        log.info("=====>>>>>>>>>>>>>>>AccountBalanceCheckJob.end.executing.");
        return ReturnT.SUCCESS;
    }
}
