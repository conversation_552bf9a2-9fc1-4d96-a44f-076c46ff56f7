package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.StringDateParser;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.app.biz.monitor.WxAppDataCheckService;
import com.bilibili.crm.platform.app.biz.service.writer.WxSellGoodResourceWriter;
import com.bilibili.crm.platform.app.cache.WxAppCacheManager;
import com.bilibili.crm.platform.app.common.WxModuleType;
import com.bilibili.crm.platform.biz.service.income.util.IncomeTimeUtil;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.dianping.cat.Cat;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;

/**
 * @author: guowei05
 * @mail: <EMAIL>
 * @time: 2023-03-02 14:56:18
 * @description:
 **/
@Component
@JobHandler("WxSellGoodResourceJob")
@Slf4j
public class WxSellGoodResourceJob extends IJobHandler {

    @Resource
    WxSellGoodResourceWriter wxSellGoodResourceWriter;

    @Resource
    private IncomeTimeUtil incomeTimeUtil;

    @Resource
    private WxAppCacheManager wxAppCacheManager;

    @Autowired
    private WxAppDataCheckService dataCheckService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {

        Timestamp begin = Utils.getToday();
        Timestamp end = Utils.getEndOfDay(Utils.getToday());
        if (StringUtils.hasText(args) && args.contains("~")) {
            String[] strings = args.split("~");
            begin = StringDateParser.stringToTimestamp(strings[0]);
            end = StringDateParser.stringToTimestamp(strings[1]);
        }
        List<Timestamp> dayList = Utils.getEachDays(begin, end);
        log.info("WxSellGoodResourceJobTime, begin:{},end:{}", begin, end);
        if (!StringUtils.hasText(args)) {
            //检查当天状态位
            Timestamp day = incomeTimeUtil.getYesterdayBegin(begin);
            if (wxAppCacheManager.isModulePaperStateReadyV2(day, WxModuleType.SELL_GOODS_RESOURCE)) {
                Cat.logEvent("WxSellGoodResourceJob_DATA_HAD_DOWN", day.toString());
                return ReturnT.SUCCESS;
            }
        }

        dayList.forEach(day -> {
            CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
                try {
                    //检查ck昨日数据是否ready
                    if (!dataCheckService.mobileDataCheckByState(Utils.getSomeDayAgo(day, 1), dataCheckService.generateSellGoodFlyCkKey(Utils.getSomeDayAgo(day, 1)))) {
                        return;
                    }
                    if (!dataCheckService.mobileDataCheckByState(Utils.getSomeDayAgo(day, 1), dataCheckService.generateSellGoodRtbCkKey(Utils.getSomeDayAgo(day, 1)))) {
                        return;
                    }
                    if (!dataCheckService.mobileDataCheckByState(Utils.getSomeDayAgo(day, 1), dataCheckService.generateEffectResourceCkKey(Utils.getSomeDayAgo(day, 1)))) {
                        return;
                    }
                    log.info("WxSellGoodResourceJobStart:{}", incomeTimeUtil.getYesterdayBegin(day));
                    wxSellGoodResourceWriter.buildIncomeOverview(day);
                    wxSellGoodResourceWriter.buildIncomeRatio(day);
                    wxSellGoodResourceWriter.buildContrast(day);
                    log.info("WxSellGoodResourceJobEnd:{}", incomeTimeUtil.getYesterdayBegin(day));
                    wxAppCacheManager.validModuleRequestStatus(incomeTimeUtil.getYesterdayBegin(day), WxModuleType.SELL_GOODS_RESOURCE);
                } catch (Exception e) {
                    Cat.logEvent("Exception_WxSellGoodResourceJob", e.getMessage());
                    throw new RuntimeException(e);
                }
            });
        });
        return ReturnT.SUCCESS;
    }
}
