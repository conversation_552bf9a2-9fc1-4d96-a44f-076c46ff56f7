package com.bilibili.crm.platform.job;

import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.platform.api.pickup.INewPickupSettleService;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;

import java.sql.Timestamp;

/**
 * @Description 对前一天完成的花火订单在crm状态还未流转到完成的再同步一次
 * <AUTHOR>
 * @DATE 2021/10/12 7:44 下午
 **/
@Component
@JobHandler("PickupOrderSyncForLastDayJob")
public class PickupOrderSyncForLastDayJob extends IJobHandler {

    @Autowired
    private INewPickupSettleService newPickupSettleService;

    @Override
    public ReturnT<String> execute(String args) throws Exception {
        newPickupSettleService.syncLastDayPickupSettle();
        Timestamp oneDayBeforeToday = Utils.getSomeDayBeforeToday(1);
        Timestamp beginTime = Utils.getBeginOfDay(oneDayBeforeToday);
        Timestamp endTime = Utils.getBeginOfDay(Utils.getToday());
        newPickupSettleService.syncPickupSettleByTime(beginTime, endTime);
        return ReturnT.SUCCESS;
    }
}
