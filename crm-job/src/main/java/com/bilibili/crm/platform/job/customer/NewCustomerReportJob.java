package com.bilibili.crm.platform.job.customer;

import com.bilibili.adp.common.bean.Operator;
import com.bilibili.adp.common.util.Utils;
import com.bilibili.crm.biz.customer_operate_label.service.CustomerOperateLabelService;
import com.bilibili.crm.platform.api.bsiopportunity.IBsiOpportunityService;
import com.bilibili.crm.platform.api.customer.dto.CustomerBaseDto;
import com.bilibili.crm.platform.api.customer.dto.report.QueryNewCustomerReportDto;
import com.bilibili.crm.platform.api.customer.dto.report.QueryNewCustomerReportFollowRecordDto;
import com.bilibili.crm.platform.api.customer.service.ICustomerQueryService;
import com.bilibili.crm.platform.api.customer.service.report.INewCustomerReportService;
import com.bilibili.crm.platform.api.customer.service.sea.ICustomerSeaClaimService;
import com.bilibili.crm.platform.api.statusmachine.customer.enums.report.NewCustomerReportStateEnum;
import com.bilibili.crm.platform.biz.po.CrmNewCustomerReportPo;
import com.bilibili.crm.platform.biz.repo.customer.NewCustomerReportFollowRecordRepo;
import com.bilibili.crm.platform.biz.repo.customer.NewCustomerReportLogRepo;
import com.bilibili.crm.platform.biz.repo.customer.NewCustomerReportRepo;
import com.bilibili.crm.platform.biz.service.follow_manage.FollowManageService;
import com.bilibili.crm.platform.biz.statusmachine.customer.report.NewCustomerReportStateMachine;
import com.bilibili.crm.platform.biz.util.CatUtils;
import com.bilibili.crm.platform.biz.util.CrmUtils;
import com.google.common.collect.Lists;
import com.xxl.job.core.biz.model.ReturnT;
import com.xxl.job.core.handler.IJobHandler;
import com.xxl.job.core.handler.annotation.JobHandler;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.springframework.stereotype.Component;
import org.springframework.transaction.annotation.Transactional;

import javax.annotation.Resource;
import java.sql.Timestamp;
import java.util.List;
import java.util.Objects;

import static com.bilibili.crm.platform.api.statusmachine.customer.enums.report.NewCustomerReportOperationEnum.REPORT_AUTO_INVALID;
import static com.bilibili.crm.platform.api.statusmachine.customer.enums.report.NewCustomerReportOperationEnum.REPORT_AUTO_INVALID_2;

/**
 * 新客报备定时任务
 *
 * <AUTHOR>
 * @date 2022/11/18 16:42
 */
@Slf4j
@Component
@JobHandler("newCustomerReportJob")
public class NewCustomerReportJob extends IJobHandler {
    @Resource
    private NewCustomerReportRepo newCustomerReportRepo;
    @Resource
    private NewCustomerReportFollowRecordRepo newCustomerReportFollowRecordRepo;
    @Resource
    private NewCustomerReportStateMachine newCustomerReportStateMachine;

    @Resource
    private NewCustomerReportLogRepo newCustomerReportLogRepo;

    @Resource
    private ICustomerQueryService customerQueryService;

    @Resource
    private IBsiOpportunityService bsiOpportunityService;

    @Resource
    private INewCustomerReportService newCustomerReportService;

    @Resource
    private CustomerOperateLabelService customerOperateLabelService;
    @Resource
    private ICustomerSeaClaimService customerSeaClaimService;
    @Resource
    private FollowManageService followManageService;
    private final static Integer NEW_REPORT_INVALID_DAYS_FOR_NO_RECORD = 30;
    private final static Integer NEW_REPORT_INVALID_DAYS_FOR_HAVE_RECORD = 90;

    private final static Operator OPERATOR = Operator.SYSTEM;

    @Override
    public ReturnT<String> execute(String s) throws Exception {
        CatUtils.newTransaction(CatUtils.JOB, getClass().getSimpleName(), (t) -> {
            log.info(">>> 新客报备定时任务开始 at {}", CrmUtils.formatDate(Utils.getNow()));

            // 处理生效中状态的报备
            handleReports();

            log.info(">>> 新客报备定时任务结束 at {}", CrmUtils.formatDate(Utils.getNow()));
        });

        return ReturnT.SUCCESS;
    }

    /**
     * 处理报备
     */
    public void handleReports() {
        QueryNewCustomerReportDto param = QueryNewCustomerReportDto.builder()
                .reportStates(Lists.newArrayList(
                        NewCustomerReportStateEnum.VALID.getCode()
                ))
                .build();
        List<CrmNewCustomerReportPo> pos = newCustomerReportRepo.queryByParam(param, false);
        log.info(">>> 待处理报备: {}", pos.size());
        // 一条条处理
        pos.forEach(this::handleReport);
    }

    @Transactional(value = "jtaTransactionManager", rollbackFor = Exception.class)
    public void handleReport(CrmNewCustomerReportPo po) {
        String customerName = po.getCustomerName();
        List<CustomerBaseDto> customerBaseDtos = customerQueryService.queryCustomerListByName(customerName);
        if (CollectionUtils.isNotEmpty(customerBaseDtos)) {

            //判断客户所开户的行业是否属于该销售所属上级小组的负责行业，（逐级往上找，直至找到有配置行业范围的小组，以该组的行业范围为准）；
            // 如果开户的行业属于行业范围，则流转为已开户，同步绑定客销关系；否则转到销售确认流程
            if (!newCustomerReportService.isSaleConfInCustomerUnitedIndustry(po.getId())) {
                newCustomerReportStateMachine.turnToSaleConfirm(OPERATOR, po.getId());
                return;
            }
            CustomerBaseDto customerBaseDto = customerBaseDtos.get(0);
            // 已开户
            newCustomerReportStateMachine.turnToAccountOpened(OPERATOR, po.getId());
            customerSeaClaimService.noticeOperationForCustomerAdopt(customerBaseDto.getId(), customerBaseDto.getUsername(), po.getSaleId(), customerBaseDto.getIsAgent());
            // 填充商机信息
            bsiOpportunityService.fillNewReportBsiInfo(customerBaseDtos.get(0).getUsername());
            // 更新跟进拜访客户信息
            followManageService.changeCustomerType(po.getId(), customerBaseDtos.get(0).getId());
            return;
        }
        handleInvalidNewReport(po);
    }


    /**
     * 若30天无跟进，报备失效，其他该行业销售可对该客户进行报备（前提是客户目前也没开户进来）；
     * 若报备有跟进，但90天未开户，报备也失效
     */
    public void handleInvalidNewReport(CrmNewCustomerReportPo po) {
        boolean noFollow = noFollowRecords(po.getId(), NEW_REPORT_INVALID_DAYS_FOR_NO_RECORD);
        if (noFollow || po.getValidTime().before(Utils.getSomeDayBeforeToday(NEW_REPORT_INVALID_DAYS_FOR_HAVE_RECORD))) {
            // 流转到已失效
            newCustomerReportStateMachine.turnToInvalid(OPERATOR, po.getId());
            // 操作日志
            newCustomerReportLogRepo.createLog(Operator.SYSTEM, po.getId(), "", Objects.equals(noFollow, true) ? REPORT_AUTO_INVALID : REPORT_AUTO_INVALID_2);
        }
    }

    /**
     * 报备是否有跟进记录
     *
     * @param reportId   报备ID
     * @param daysBefore 天数内是否有报备记录
     * @return boolean
     */
    public boolean noFollowRecords(Long reportId, int daysBefore) {
        Timestamp ctimeStart = Utils.getSomeDayBeforeToday(daysBefore);
        QueryNewCustomerReportFollowRecordDto param = QueryNewCustomerReportFollowRecordDto.builder()
                .reportId(reportId)
                .ctimeStart(ctimeStart)
                .build();

        return CollectionUtils.isEmpty(newCustomerReportFollowRecordRepo.queryByParam(param, false));
    }
}
